inputDatePickerFormat="DD-MMM-YYYY",inputDateParserFormat="DD-MMM-YYYY",pastTransactionDays=365;var weekday=new Array(7);weekday[0]="Sunday",weekday[1]="Monday",weekday[2]="Tuesday",weekday[3]="Wednesday",weekday[4]="Thursday",weekday[5]="Friday",weekday[6]="Saturday";var shortWeekday=new Array(7);shortWeekday[0]="Sun",shortWeekday[1]="Mon",shortWeekday[2]="Tue",shortWeekday[3]="Wed",shortWeekday[4]="Thu",shortWeekday[5]="Fri",shortWeekday[6]="Sat";var time_formats=[[60,"seconds",1],[120,"1 minute ago","1 minute from now"],[3600,"minutes",60],[7200,"1 hour ago","1 hour from now"],[86400,"hours",3600],[172800,"Yesterday","Tomorrow"],[604800,"days",86400],[1209600,"Last week","Next week"],[2419200,"weeks",604800],[4838400,"Last month","Next month"],[29030400,"months",2419200],[58060800,"Last year","Next year"],[290304e4,"years",29030400],[580608e4,"Last century","Next century"],[580608e5,"centuries",290304e4]],month_names=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function getCurrentDateInTimezone(e){var t=new Date,a=t.getTime()+6e4*t.getTimezoneOffset();return new Date(a+36e5*e)}function getCurrentDate(){return getCurrentDateInTimezone("+5.5")}function getDayStart(e){return e.setHours(0),e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),e}function getDayEnd(e){return e.setHours(23),e.setMinutes(59),e.setSeconds(59),e.setMilliseconds(0),e}function formatDateToDDMMYYYY(e){return`${String(e.getDate()).padStart(2,"0")}/${String(e.getMonth()+1).padStart(2,"0")}/${e.getFullYear()}`}function initDateRangePicker(){$('input[name="daterange"]').daterangepicker({opens:"left"}),$('input[name="datetimes"]').daterangepicker({timePicker:!0,opens:"left",startDate:moment().startOf("hour"),endDate:moment().startOf("hour").add(32,"hour"),locale:{format:"M/DD hh:mm A"}}),$('input[name="datesingle"]').daterangepicker({singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("hour").subtract(40,"days"),maxDate:moment().startOf("hour"),locale:{format:"DD/MMM/YYYY"}});var e=moment().subtract(29,"days"),t=moment();function a(e,t){$("#reportrange span").html(e.format("MMMM D, YYYY")+" - "+t.format("MMMM D, YYYY"))}$("#reportrange").daterangepicker({startDate:e,endDate:t,ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]}},a),a(e,t)}function initDateInput(){$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initDateWithYearRange(e){initDateWithYearRange(e,!1)}function initDateWithYearRange(e,t){var a=e.split(":"),n=parseInt(a[0].substr(1)),r=parseInt(a[1].substr(1));$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,defaultDate:null,showDropdowns:!0,minDate:moment().startOf("day").subtract(n,"year"),maxDate:moment().startOf("day").add(r,"year"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),t&&$(".select-date").val(getTodayDateInInputFormat()),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initUpdateDateInId(e,t){$("#"+e).val(t);const a=$("#"+e).data("daterangepicker");if(a&&t){const e=moment(t,inputDatePickerFormat);a.setStartDate(e),a.setEndDate(e)}}function initDateById(e,t,a){var n=t.split(":"),r=parseInt(n[0].substr(1)),o=parseInt(n[1].substr(1));$("#"+e).daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,defaultDate:null,showDropdowns:!0,minDate:moment().startOf("day").subtract(r,"year"),maxDate:moment().startOf("day").add(o,"year"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),a&&$(".select-date").val(getTodayDateInInputFormat()),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initOnChangeDateFeature(){$(".select-date").on("apply.daterangepicker",function(e,t){const a=t.startDate.format(inputDatePickerFormat);$(this).val(a).trigger("change")}),$(".select-date").on("show.daterangepicker",function(e,t){t.setStartDate(t.startDate.clone()),t.setEndDate(t.endDate.clone())})}function initDate(e){$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(e,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initDateWithChange(e,t){$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(e,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},onSelect:function(e){console.log("Selected date: "+e+", Current Selected Value= "+this.value),$(this).change()}},bind_daterangepicker_apply_event).on("change",function(){}),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initDateDuration(e,t){$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:new Date(1e3*e),maxDate:new Date(1e3*t),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initPastDateById(e,t){$("#"+e).daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(t,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function initFutureDate(e){$(".select-date").daterangepicker({autoUpdateInput:!1,autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day"),maxDate:moment().startOf("day").add(e,"days"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"}},bind_daterangepicker_apply_event),initOnChangeDateFeature(),bind_daterangepicker_clear_event()}function bind_daterangepicker_apply_event(e,t){$(this.element[0]).val(e.format(inputDatePickerFormat))}function bind_daterangepicker_clear_event(){$(".select-date").on("cancel.daterangepicker",function(e,t){$(this).val("")})}function getPreviousDate(e){var t=new Date;return t.setDate(t.getDate()-e),t}function getDate(e){var t=moment(e,inputDateParserFormat);return t.isValid()?t.toDate():null}function convertToEpochTime(e,t){if(""==t&&null==t&&null==t)return null;var a=new Date(1e3*e),n=t.split(":"),r=n[0],o=n[1];return a.setHours(r,o,0,0),Math.floor(a.getTime()/1e3)}function convertMinutesToTime(e){if(""!=e&&null!=e&&null!=e){return`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`}return null}function getHourMinuteTime(e){if(""!=e&&null!=e&&null!=e){var t=e.split(":");return{hour:t[0],minute:t[1]}}return{hour:0,minute:0}}function getFormattedDate(e){return null!=e&&e>0?formatDateToString(new Date(1e3*e)):null}function convertEpochTimeToTime(e){return null!=e&&e>0?formatTimeToString(new Date(1e3*e)):null}function convertTimeToMinute(e){return null!=e&&null!=e?60*e.hour+e.minute:0}function formatDateToString(e){return String(e.getDate()).padStart(2,"0")+"-"+month_names[e.getMonth()]+"-"+e.getFullYear()}function formatDateToStringWithYearFirst(e){var t=String(e.getDate()).padStart(2,"0"),a=month_names[e.getMonth()];return e.getFullYear()+"-"+a+"-"+t}function formatTimeToString(e){return formatTimeToStringWithShowSecondsFlag(e,!1)}function formatTimeToStringWithShowSecondsFlag(e,t){var a=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),r=String(e.getSeconds()).padStart(2,"0");return t?a+":"+n+":"+r:a+":"+n}function getFormattedDateInNumber(e){return null!=e&&e>0?(dateVal=new Date(1e3*e),formatDateToString(dateVal)):null}function setFormattedDate(e,t){var a=getFormattedDate(e);if(null==a)return null;var n=moment(a,"DD-MMM-YYYY"),r=$(t).data("daterangepicker");return r.setStartDate(n),r.setEndDate(n),$(t).val(n.format(r.locale.format)),a}function getWeekDay(e){return null!=e&&e>0?(dateVal=new Date(1e3*e),weekday[dateVal.getDay()]):null}function getShortWeekDay(e){return null!=e&&e>0?(dateVal=new Date(1e3*e),shortWeekday[dateVal.getDay()]):null}function getFormattedDateWithTime(e){return null!=e&&e>0?(dateVal=new Date(1e3*e),formatDateToString(dateVal)+" "+formatTimeToString(dateVal)):null}function getFormattedDateWithTimeWithYearFirst(e){return null!=e&&e>0?(dateVal=new Date(1e3*e),formatDateToStringWithYearFirst(dateVal)+" "+formatTimeToString(dateVal)):null}function getFormattedTime(e){return("0"+e).slice(-2)}function getTodayDateInInputFormat(){return formatDateToString(new Date)}function getTodayDateInInputFormatWithYearFirst(){return formatDateToStringWithYearFirst(new Date)}function time_ago(e){var t=new Date(1e3*e);switch(typeof t){case"number":break;case"string":t=+new Date(t);break;case"object":t.constructor===Date&&(t=t.getTime());break;default:t=+new Date}var a=(+new Date-t)/1e3,n="ago",r=1;if(0==a)return"Just now";a<0&&(a=Math.abs(a),n="from now",r=2);for(var o,i=0;o=time_formats[i++];)if(a<o[0])return"string"==typeof o[2]?o[r]:Math.floor(a/o[2])+" "+o[1]+" "+n;return t}function initMultipleDateRange(e,t,a,n){$("#"+e).daterangepicker({autoApply:!0,autoUpdateInput:!1,singleDatePicker:!1,showDropdowns:!0,minDate:moment().startOf("day").subtract(t,"days"),maxDate:moment().startOf("day"),locale:{format:n,separator:a,cancelLabel:"Clear"}}),$("#"+e).on("apply.daterangepicker",function(e,t){$(this).val(t.startDate.format(n)+a+t.endDate.format(n))})}function getFormattedDateRange(e,t){let a=0,n=0;if(null!=e||""!==e){const[r,o]=e.split(t);r&&(a=getDate(r).getTime()/1e3),o&&(n=getDate(o).getTime()/1e3)}return[a,n]}