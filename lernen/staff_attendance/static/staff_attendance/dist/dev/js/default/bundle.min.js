$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerMarkAttendanceMenu(),menuLoader.registerAttendanceReportsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerMarkAttendanceMenu:function(){$("#markAttendanceNav").on("click",function(){attendanceRegister.loadMainScreen(!0)})},registerAttendanceReportsMenu:function(){$("#attendanceReportNav").on("click",function(){attendanceReports.loadReportsMenu()})}};function parseTimeToMinutes(t){if(!t||!t.includes(":"))return null;const[e,a]=t.split(":").map(Number);return 60*e+a}function showError(t){$(t).css("border","1px solid red")}function clearError(t){$(t).css("border","")}var homePage={initHomePage:function(){homePage.displayDashboardContent()},loadHomePage:function(){ajaxClient.get("/staff-attendance/home",function(t){$("#main-content").html(t),homePage.initHomePage()})},displayDashboardContent:function(){}},attendanceRegister={dataCache:{},loadMainScreen:function(){ajaxClient.get("/staff-attendance/register/main-screen",function(t){$("#main-content").html(t),attendanceRegister.initPage()})},initPage:function(){initDate(365),initDateInput()},loadAttendanceSheet:function(){var t=getDate($("#attendance-date").val());null!=t?(attendanceRegister.dataCache.selectedDate=t,ajaxClient.get("/staff-attendance/register/get-sheet/"+t.getTime()/1e3,function(t){$("#staff-attendance-feed-container").html(t),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[3,"asc"]]}),$(".clockpicker").clockpicker({autoclose:!0,default:"now"}),attendanceRegister.onClickDisableClockpicker();var e=readJson("#attendance-register"),a={};if(null!=e&&e.length>0)for(var n=0;n<e.length;n++){var i=e[n];a[i.staff.staffId]=i}attendanceRegister.dataCache.staffAttendanceRegisterMap=a,attendanceRegister.bindFillDefaultStaffTime(),attendanceRegister.registerOnChangeTime(),attendanceRegister.overrideTab()})):showErrorDialogBox("please select date for attendance")},onClickDisableClockpicker:function(){$(".disable-clockpicker-click").on("click",function(){"true"===$("#authorised_to_edit_staff_attendance_time").text().toLowerCase()||$(".clockpicker-popover").hide();var t=new Date,e=t.getHours()+":"+t.getMinutes();$(this).parent().find(".clockpicker-input").val(e)})},bindFillDefaultStaffTime:function(){$("#fill-default-staff-in-time").change(function(){if(this.checked){var t=attendanceRegister.dataCache.staffAttendanceRegisterMap;attendanceRegister.fillTimeForStaffRows("IN",t)}else attendanceRegister.resetTimeForStaffRows("IN")}),$("#fill-default-staff-out-time").change(function(){if(this.checked){var t=attendanceRegister.dataCache.staffAttendanceRegisterMap;attendanceRegister.fillTimeForStaffRows("OUT",t)}else attendanceRegister.resetTimeForStaffRows("OUT")})},resetTimeForStaffRows:function(t){$("tr.staff-attendance-row").each(function(){$(this).find(".clockpicker-input").each(function(){"IN"==t&&$(this).hasClass("in-time")&&$(this).val(""),"OUT"==t&&$(this).hasClass("out-time")&&$(this).val("")})})},fillTimeForStaffRows:function(t,e){$("tr.staff-attendance-row").each(function(){var a=$(this).attr("id");if(a in e){var n=e[a];$(this).find(".clockpicker-input").each(function(){if("IN"==t&&$(this).hasClass("in-time")){var e=n.staff.staffTimingDetails.inTime;if(null==e)$(this).val("");else{var a=attendanceRegister.formatDuration(e);$(this).val(a)}}if("OUT"==t&&$(this).hasClass("out-time")){var i=n.staff.staffTimingDetails.outTime;if(null==i)$(this).val("");else{a=attendanceRegister.formatDuration(i);$(this).val(a)}}})}})},formatDuration:function(t){return t.hour.toString().padStart(2,"0")+":"+t.minute.toString().padStart(2,"0")},submitAttendance:function(){var t=[],e=attendanceRegister.dataCache.selectedDate,a=!1;$("tr.staff-attendance-row").each(function(){var e=$(this),n=e.attr("id"),i=[],r=e.find(".in-times .time-pair-entry"),s=e.find(".out-times .time-pair-entry");r.each(function(t){var e=$(this).find("input"),n=s.eq(t).find("input"),r=e.val(),o=n.val();if(r||o)if(attendanceRegister.validateOutTime(n)||attendanceRegister.validateInTime(e))a=!0;else{var d={};if(r){var c=r.split(":");d.inTime={hour:c[0],minute:c[1],second:"00"}}if(o){var f=o.split(":");d.outTime={hour:f[0],minute:f[1],second:"00"}}i.push(d)}}),i.length>0&&t.push({staffId:n,staffTimeDurationList:i})});var n={attendanceDate:e.getTime()/1e3,staffAttendanceInputList:t};a||ajaxClient.post("/staff-attendance/register/submit-attendance",{staffAttendancePayload:JSON.stringify(n)},function(t){$("#attendance-feed-status-modal-container").html(t),$("#attendance-default-status-modal").modal({backdrop:"static",keyboard:!1}),attendanceRegister.loadAttendanceSheet()})},addAttendanceRow:function(t){const e=$(t).closest("tr"),a=e.find(".in-times"),n=e.find(".out-times"),i=a.find(".time-pair-entry").last(),r=n.find(".time-pair-entry").last(),s=i.find("input"),o=r.find("input");if(!s.val()||!o.val())return s.val()||showError(s),o.val()||showError(o),void showErrorDialogBox("Please fill this attendance row in order to add new attendance row");if(attendanceRegister.validateOutTime(o)||attendanceRegister.validateInTime(s))return;const d=i.clone(),c=r.clone();d.find("input").val(""),c.find("input").val(""),d.find(".add-time-btn").removeClass("add-time-btn btn-success").addClass("remove-time-btn").attr("onclick","attendanceRegister.deleteAttendanceRow(this)").html('<i class="bi bi-x"></i>'),c.find(".add-time-btn").removeClass("add-time-btn btn-success").addClass("remove-time-btn").attr("onclick","attendanceRegister.deleteAttendanceRow(this)").html('<i class="bi bi-x"></i>'),a.append(d),n.append(c),$(".clockpicker").clockpicker({autoclose:!0,default:"now"}),attendanceRegister.registerOnChangeTime()},deleteAttendanceRow:function(t){const e=$(t).closest("tr"),a=e.find(".in-times"),n=e.find(".out-times"),i=a.find(".remove-time-btn").index(t);a.find(".time-pair-entry").eq(i).remove(),n.find(".time-pair-entry").eq(i).remove()},registerOnChangeTime:function(){$(".in-time").on("change",function(){attendanceRegister.validateInTime(this)}),$(".out-time").on("change",function(){attendanceRegister.validateOutTime(this)})},validateOutTime:function(t){const e=$(t),a=e.closest(".time-pair-entry"),n=e.closest("tr").find(".in-times .time-pair-entry").eq(a.index()).find(".in-time"),i=n.val(),r=e.val();if(r&&!i)return showError(e),showError(n),showErrorDialogBox("In time cannot be empty if out time is filled. Note: it is a 24-hour clock system, please fill accordingly (e.g., 2:00 PM should be written as 14:00)."),!0;const s=parseTimeToMinutes(i),o=parseTimeToMinutes(r);return null!==s&&null!==o?o<=s?(showError(e),showError(n),showErrorDialogBox("In time should not be greater or equal to the out time. Note: it is 24 hours clock system, please fill accordingly eg: 02:00 pm should be written as 14:00"),!0):(clearError(e),clearError(n),!1):void 0},validateInTime:function(t){const e=$(t),a=e.closest(".time-pair-entry").index(),n=e.closest("tr"),i=n.find(".out-times .time-pair-entry").eq(a-1).find(".out-time"),r=n.find(".out-times .time-pair-entry").eq(a).find(".out-time"),s=i.val(),o=r.val(),d=e.val(),c=parseTimeToMinutes(d),f=parseTimeToMinutes(s),l=parseTimeToMinutes(o);if(d&&null!==c){if(a>0&&null!==f&&c<f)return showError(e),showError(i),showErrorDialogBox("Current in time should not be earlier than previous out time. Note: it is a 24-hour clock system, e.g., 02:00 PM should be written as 14:00."),!0;if(o&&null!==l&&c>=l)return showError(e),showError(r),showErrorDialogBox("In time should be earlier than its corresponding out time. Note: it is a 24-hour clock system, e.g., 02:00 PM should be written as 14:00."),!0;clearError(e),clearError(i),clearError(r)}return!1},updateStaffAttendanceStatus:function(t){$("#staff-update-attendance-status-modal").modal("show");var e=$(t).closest("tr"),a=e.attr("id"),n=($(t).closest("td"),e.find("td.attendance_register_entry").text()),i=JSON.parse(n);$("#updated-staff-id").text(a);var r="",s="";if(i&&i.staffAttendanceDaySummaryList&&i.staffAttendanceDaySummaryList.length>0){var o=i.staffAttendanceDaySummaryList[0];r=o.staffAttendanceStatus,s=o.remarks}$("#attendance-remarks").val(s),$("#staff-attendance-status").val(r).trigger("change")},updateStaffAttendance:function(){var t=$("#attendance-remarks").val(),e=$("#updated-staff-id").text(),a=$("#staff-attendance-status").val(),n=attendanceRegister.dataCache.selectedDate;""!=a&&null!=a||($("#staff-update-attendance-status-modal").modal("toggle"),showErrorDialogBox("Please fill staff attendance status in order to update"));var i=[{staffId:e,staffAttendanceStatus:a,remarks:t}],r={attendanceDate:n.getTime()/1e3,staffAttendanceStatusInputs:i};$("#staff-update-attendance-status-modal").modal("toggle"),ajaxClient.post("/staff-attendance/register/update-staff-attendance-status",{staffMarkAttendancePayload:JSON.stringify(r)},function(t){$("#attendance-feed-status-modal-container").html(t),$("#attendance-staff-status-modal").modal({backdrop:"static",keyboard:!1}),attendanceRegister.loadAttendanceSheet()})},overrideTab:function(){$(".staff-attendance-row").on("keydown",function(t){if("Tab"===t.key){const e=[];$("tr.staff-attendance-row").each(function(){const t=$(this).find(".in-times .time-pair-entry"),a=$(this).find(".out-times .time-pair-entry"),n=Math.max(t.length,a.length);for(let i=0;i<n;i++){const n=t.eq(i).find(".in-time"),r=a.eq(i).find(".out-time");n.length&&e.push(n[0]),r.length&&e.push(r[0])}});const a=document.activeElement,n=e.indexOf(a);if(-1!==n){t.preventDefault();const a=t.shiftKey?n-1:n+1;a>=0&&a<e.length&&$(e[a]).focus()}}})}},attendanceReports={loadReportsMenu:function(){ajaxClient.get("/staff-attendance/reports",function(t){$("#main-content").html(t),initSelect2("All"),initDateInput(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),attendanceReports.bindGenerateReportEvent(),initSelect2()})},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var t=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(t))){var e=$(t).find("p.report-type").text().trim(),a=-1,n=-1,i="";if(void 0===(i=$(t).find("#staff-attendance-status").val())&&(i=""),$(t).find("input.staff-attendance-start-date").length>0&&""!=$(t).find("input.staff-attendance-start-date").val()&&(a=getDate($(t).find("input.staff-attendance-start-date").val()).getTime()/1e3),$(t).find("input.staff-attendance-end-date").length>0&&""!=$(t).find("input.staff-attendance-end-date").val()&&(n=getDate($(t).find("input.staff-attendance-end-date").val()).getTime()/1e3),-1!=n&&-1!=a&&n<a)showErrorDialogBox("End date cannot be less than start date");else{var r="";$(t).find("select.filter-staff-status").length>0&&(r=$(t).find("select.filter-staff-status").val().join());var s="";if("STAFF_MONTHLY_ATTENDANCE_SUMMARY"===e||"STAFF_ATTENDANCE_DETAILS"===e){$(t).find("select.filter-staff-category").length>0&&(s=$(t).find("select.filter-staff-category").val().join());var o=(n-a)/86400;if(o<0||o>=31)return void alert("Total number of days cannot be greater than 31. Please reduce the range!")}$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/staff-attendance/generate-report/"+e+"?attendance_status="+i+"&startDate="+a+"&endDate="+n+"&staffCategory="+s+"&staffStatusStr="+r,"_blank")}}})},resetPopUp:function(){$(".staff-attendance-start-date").val(""),$(".staff-attendance-end-date").val("")}};