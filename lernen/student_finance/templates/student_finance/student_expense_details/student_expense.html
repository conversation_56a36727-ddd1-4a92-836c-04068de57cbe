{% if student %}
{% load currency_utils %}
{% load json %}
<p id="services-json" style="display:none;"> {{services|jsonstr}}</p>
<p id="student-finance-preferences-json" style="display:none;"> {{student_finance_preferences|jsonstr}}</p>
<p id="student-wallet-preferences-json" style="display:none;"> {{student_wallet_preferences|jsonstr}}</p>


<div class="container-fluid border rounded fee-payment-tab active" id='fee-payment-view-tab'
  style="width:100%; height:100%; background-color:#fff; overflow: hidden;">
  <br>
  <div style="float:left">
    <p style="color:black">Status : <span class="badge badge-success">ENROLLED</span> </p>
  </div>
  <div>
    <table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
      <thead>
      </thead>
      <tbody>
        <tr>
          {% if student.thumbnail %}<td scope="col" rowspan="2"><img src="data:image/jpg;base64, {{student.thumbnail}}">
          </td>{% endif %}
          <th scope="col">Name</th>
          <th scope="col">Adm No.</th>
          <th scope="col">Father Name</th>
          <th scope="col">Class</th>
          <th scope="col">Wallet(Rs)</th>
          <th scope="col" colspan="2"></th>
        </tr>
        <tr>
          <td style="display:none;">
            <p id="fee-assign-view-student-id" style="display:none;"> {{student.studentId}} </p>
          </td>
          <td id="search-student-name"> {{student.studentBasicInfo.name}} {% if is_fee_payment_restricted %}
            <div style="color: red; font-size: smaller;">
              Fee Payment Restricted
            </div>
            {% endif %}
          </td>
          <td id="search-admission-number">{{student.studentBasicInfo.admissionNumber}}</td>
          <td>{{student.studentFamilyInfo.fathersName}}</td>
          <td id="search-student-class">{{student.studentAcademicSessionInfoResponse.standard.displayNameWithSection}}
          </td>
          {% if student.walletAmount < 0 %} <td id="search-student-wallet-amount"><span
              style="color:red"><strong>{{student.walletAmount}}</strong></span></td>
            {% else %}
            <td id="search-student-wallet-amount">{{student.walletAmount}}</td>
            {% endif %}
            {% with student.studentAcademicSessionInfoResponse.academicSession.academicSessionId|stringformat:"s" as sessionId %}
              <td>
                <button style="width:100%;" type="button" class="btn btn-primary" id="collect-fee-button" aria-haspopup="true"
                aria-expanded="false" onclick="manageStudentExpenseDetails.addStudentExpense();">
                  Add Service Expense
              </button>
              </td>
            {% endwith %}
            <td> <button style="width:100%;" type="button" class="btn btn-primary" id="generate-student-finance-popup">Generate Student Expense Report</button></td>
        </tr>

      </tbody>
    </table>
  </div>
  <br />
</div>

<div id="add-expense-action-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title">Add Student Expense</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body">
            <p id="fee-assign-view-student-id" style="display:none;"> {{student.studentId}} </p>
            <div class="row">
              <div class="col-md-12" style="text-align: right;">
                <strong>Available Wallet Amount:</strong>
                <span id="available-wallet-amount-display"
                      style="color: {% if student.walletAmount > 0 %}green{% elif student.walletAmount < 0 %}red{% endif %};">
                  {{ student.walletAmount }}
                </span>
              </div>
            </div>
              <div class="row">
                  <div class="form-group col-md-6">
                      <label>Service Category <span style="color:red">*</span></label>
                      <select id="student-service-category" class="form-control mandatory-field">
                        <option selected=""></option>
                        <option value="GROOMING">Grooming</option>
                        <option value="HOSTEL">Hostel</option>
                        <option value="FOOD">Food</option>
                        <option value="MEDICAL"> Medical</option>
                        <option value="TRANSPORT">Transport</option>
                        <option value="TELEPHONE">Telephone</option>
                        <option value="EXAM">Exam</option>
                        <option value="FINE">Fine</option>
                        <option value="TRIP">Trip</option>
                        <option value="FUNCTION">Function</option>
                        <option value="OTHER">Other</option>
                      </select>                  
                    </div>
                  
                  <div class="form-group col-md-6">
                      <label>Service <span style="color:red">*</span></label>
                      <select class="form-control  mandatory-field" id="service">
                          <option selected value=""></option>
                          
                      </select>
                  </div>
              </div>

              <div class="row">
                <div class="form-group col-md-6">
                  <label>Amount <span style="color:red">*</span></label>
                  <input type="text" class="form-control" id="expense-amount" placeholder="Enter Expense Amount" required>
              </div>
              <div class="form-group col-md-6">
                <label>Date <span style="color:red">*</span></label>
                <input type="text" class="form-control select-date mandatory-field" id="expense-transaction-date"
                    placeholder="Enter Date">
              </div>
              </div>

              <div class="row">
                  <div class="form-group col-md-8">
                      <label>Description</label>
                      <input type="text" class="form-control " id="description" placeholder="Enter Description">
                  </div>
                  <div class="form-group col-md-4">
                      <label>Refrence </label>
                      <input type="text" class="form-control " id="transaction-refrence"
                          placeholder="Enter Refrence">
                  </div>
              </div>
                      
            <div class="row">
              <div class="form-group col-md-6">
                <label>Payment Mode<span style="color:red">*</span></label>
                <select class="form-control mandatory-field" id="payment-mode">
                  <option value="WALLET" selected>Student Wallet</option>
                </select>
              </div>
            </div>
          </div>
          <div class="modal-footer" id="approved-modal-footer" >
              <button type="button" id="close-button" class="btn btn-secondary" data-dismiss="modal" style=" float:right;">Close</button>
              <button type="button" id="transfer-action-button" class="btn btn-primary" style=" float:right;" onclick="addStudentExpenseHandler.confirmAddExpance();">Add Expence</button>
          </div>

      </div>
  </div>
</div>

<div class="modal fade" id="student-expense-report-with-date-range-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md" role="document">
            <div class="modal-content report-field-container">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Student Finance Detail Report</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                   <!--  <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Academic Session*</label>
                            <select class="form-control report-academic-session mandatory-field">
                            {% for academic_year in academic_years %}
                                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                            {% endfor %}
                            </select>
                        </div>
                    </div> -->
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Select Start Date*</label>
                            <input type="text" class="form-control select-date student-expense-start-date mandatory-field" placeholder="Start Date"/>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select End Date*</label>
                            <input type="text" class="form-control select-date student-expense-end-date mandatory-field" placeholder="End Date"/>
                        </div>
                    </div>
                </div>
                <p style="display:none;" class="report-type">STUDENT_FINANCE_DETAILS_REPORT</p>
                <p class="report-type-view-student-id" style="display:none;"> {{student.studentId}} </p>
                <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
                        <button type="button" class="btn btn-primary generate-report">View Report</button>
                </div>
            </div>
        </div>
    </div>

{% endif %}