 {% if user_template_assignment_list %}
      <div class="row">
        <div class="col-md-8">
          <div class="form-check">
           <input type="checkbox" class="form-check-input" id="bulk-staff-select-all-checkbox" onclick="assignHolidayTemplate.selectBulkStaffList(this)">
           <label class="form-check-label"> Select All</label>
           &nbsp;&nbsp;
           <label class="form-check-label">  <span style="color:green;"> <strong> Selected : <span id="bulk-staff-selected-count">0</span> </strong></span> </label>
          </div>
        </div>
        <div class="col-md-4" style="text-align:right;">
          <label class="form-check-label"> <span style="color:blue;"> <strong> Total Staff : {{user_template_assignment_list|length}} </strong></span> </label>
        </div>
      </div>
      <br>
      <div class="table-responsive" style=" height: 300px;  overflow-y: scroll; cursor: pointer;">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th scope="col" >Staff Id</th>
                <th scope="col" class="w-25">Name</th>
                <th scope="col" >Calendar</th>
              </tr>
            </thead>
            <tbody>
                {% for user_template_assignment in user_template_assignment_list%}
                  <tr>
                    <td>
                      <div class="form-check">
                       <input type="checkbox" class="form-check-input bulk-staff-select-checkbox" onclick="assignHolidayTemplate.bulkStaffSelectCheckbox(this)">
                       <label class="form-check-label"> {{user_template_assignment.userData.staffBasicInfo.staffInstituteId}}</label>
                       <p class="bulk-template-assign-staff-id" style="display:none;"> {{user_template_assignment.userData.staffId}} </p>
                     </div>
                    </td>
                    <td > {{user_template_assignment.userData.staffBasicInfo.name}}</td>
                    {% if user_template_assignment.userHolidayTemplateData and user_template_assignment.userHolidayTemplateData.holidayTemplateDurationList %}
                      <td > {{user_template_assignment.userHolidayTemplateData.holidayTemplateDurationList.0.holidayTemplate.templateName}}</td>
                    {% else %}
                      <td > - </td>
                    {% endif %}
                  </tr>
                {% endfor %}
            </tbody>
          </table>
      </div>

      <div style="text-align: center;">
        <!-- <button type="submit" class="btn btn-primary">Save</button> -->
        <br>
        <button style="width:25%;" type="submit" class="btn btn-primary" onclick="assignHolidayTemplate.assignStaffTemplateConfirmModal()">Assign Calendar</button>
      </div>
{% else %}
  <h5> No staff found<h5>
{% endif %}