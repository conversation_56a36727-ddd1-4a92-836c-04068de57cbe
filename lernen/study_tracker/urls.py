from django.contrib import admin
from django.urls import path
from django.urls import include
from study_tracker.view import *

urlpatterns = [

    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),

    # Reports
    path('reports', reports_view, name='reports_view'),
    path('report-session-change/<academic_session_id>', reports_session_change_view, name='reports_session_change_view'),
    path('generate-report/<report_type>', generate_report_view, name='generate_report_view'),
    path('view-report/<report_type>', generate_report_view, name='generate_report_view'),
]