var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;

$(document).ready(function() {
    $('#sidebarCollapseImage').click(function() {
        var isCollapsed = 1;
        if ($("#sidebar.active").length == 0) {
            isCollapsed = 0;
        }
        var contentMargin = "";
        if (isCollapsed == 1) {
            var fullMargin = sidebarFull + 20;
            contentMargin = fullMargin + "px";
        } else {
            var collapsedMargin = sidebarCollapsed + 20;
            contentMargin = collapsedMargin + "px";

        }
        $('#sidebar').toggleClass('active');
        $("#company_name_footer").toggleClass('active');
        $('#content').css('margin-left', contentMargin);

    });

    registerSidebarMenu();
    // google.charts.load("current", {packages:["corechart"]});
    // google.charts.setOnLoadCallback(graphReadyToRender);
    instituteOnboarding.loadHomePage();
    paymentReminder.readPaymentState();
});

function sidebarHoverOutState(current) {
    $(current).find('img.sidebar_icon').attr("style", "");
    $(current).find('img.sidebar_icon_active').attr("style", "display:none");
}

function sidebarHoverOnState(current) {
    $(current).find('img.sidebar_icon').attr("style", "display:none");
    $(current).find('img.sidebar_icon_active').attr("style", "");
}

function sideBarHoverEventCallback() {
    $(".sidebar-menu-item").hover(
        function() {
            sidebarHoverOnState(this);
        },
        function() {
            if ($(this).parent().hasClass("active")) {
                sidebarHoverOnState(this);
            } else {
                sidebarHoverOutState(this);
            }
        });
}

function activateMenuItem() {
    $('.sidebar-menu-item').click(function(e) {
        $('.sidebar-menu li.active').removeClass('active');
        var $parent = $(this).parent();
        $parent.addClass('active');

        sidebarHoverOutState($('.sidebar-menu').find('li').not('.active').find('.sidebar-menu-item'));
        e.preventDefault();
    });
}

function registerSidebarMenu() {
    sideBarHoverEventCallback();
    activateMenuItem();
    registerHomeMenu();
    registerOnboardNewInstituteMenu();
    registerIngestStudentMenu();
    registerInstituteManagementMenu();
}

function registerHomeMenu() {
    $('#homeNav').on('click', function() {
      instituteOnboarding.loadHomePage();
    });
}

function registerOnboardNewInstituteMenu() {
    $('#onboardNewInstituteNav').on('click', function() {
      instituteOnboarding.loadHomePage();
    });
}

function registerIngestStudentMenu() {
    $('#ingestStudentNav').on('click', function() {
      ingestStudent.loadHomePage();
    });
}

function registerInstituteManagementMenu() {
    $('#instituteManagementNav').on('click', function() {
      instituteManagement.loadHomePage();
    });
}

var instituteOnboarding = {

  loadHomePage : function () {
    ajaxClient.get("/institute-onboarding/home", function(data) {
        $("#content").html(data);
    });
  }

}

var ingestStudent = {
  dataCache : {},

  resetDataCache : function () {
      ingestStudent.dataCache = {}
  },

  loadHomePage : function () {
    ajaxClient.get("/institute-onboarding/ingest-student-home", function(data) {
        ingestStudent.resetDataCache();
        $("#content").html(data);
        var allInstitutes = readJson("#all-institute");
        ingestStudent.dataCache.allInstitutes = allInstitutes;
        $("#institute-select-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  updateSelectedInstitute : function () {
      var instituteId = $("#select-institute").find(':selected').val().trim();
      if(instituteId == null || instituteId == ""){
        showErrorDialogBox("Invalid Institute. Please select valid institute");
        return;
      }
      var allInstitutes = ingestStudent.dataCache.allInstitutes;
      var selectedInstitute = null;
      for(var i = 0 ; i < allInstitutes.length; i++){
        var institute = allInstitutes[i];
        if(institute.instituteId == parseInt(instituteId)){
            selectedInstitute = institute;
        }
      }
      if(selectedInstitute == null){
        showErrorDialogBox("Selected institute does not exists. Please select valid institute");
        return;
      }

      ingestStudent.dataCache.selectedInstitute = selectedInstitute
      $("#selected-institute-display").text(selectedInstitute.instituteId + " (" + selectedInstitute.instituteName +")")
      $("#institute-select-modal").modal('toggle');

      ajaxClient.get("/institute-onboarding/institute-academic-session/"+selectedInstitute.instituteId, function(data) {
          $("#institute-academic-sessions-container").html(data);
      });
  },

  loadIngestionConfigs : function () {
    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
    ajaxClient.get("/institute-onboarding/student-ingestion-config/"+instituteId, function(data) {
        $("#student-ingestion-configuration-modal-container").html(data);
        ingestStudent.bindMultiColumnClassCheckBox();
        $("#student-ingestion-configuration-modal").modal({backdrop: 'static', keyboard: false});
    });

  },

  loadIngestionresult : function () {
    $("#student-data-ingestion-result-modal").modal({backdrop: 'static', keyboard: false});
  },

  loadUploadFile : function () {
    $("#upload-student-data-file-modal").modal({backdrop: 'static', keyboard: false});
  },

  bindMultiColumnClassCheckBox : function () {
    $('#multi-column-class').change(function() {
        if(this.checked) {
            $("#multi-class-column-info").attr( "style", "");
        }else{
          $("#multi-class-column-info").attr( "style", "display:none");
        }
    });
    $('#clear-all-fields').on('click', function () {
        $('.column-index').val('');

        $('.table-mandatory-switch').prop('checked', false);
    });
  },

  confirmApplyIngestionConfig : function () {
      var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
      var instituteName = ingestStudent.dataCache.selectedInstitute.instituteName;
      var accepted = confirm("Do you want to update student ingestion configuraton for " + instituteId + " - " + instituteName + " ?");
      if(!accepted){
          return;
      }

      $("#student-ingestion-configuration-modal").modal('toggle');

      var studentIngestionConfigs = ingestStudent.getIngestionConfig();
      ajaxClient.post("/institute-onboarding/update-student-ingestion-config/"+instituteId, {'studentIngestionConfigs':JSON.stringify(studentIngestionConfigs)}, function(data){
        $("#status-modal-container").html(data);
        $("#status-modal").modal('toggle');
      });
  },

  getIngestionConfig : function () {
      var studentDataDelimiter = $("#file-delimiter").val();
      var studentClassColumnFormat = $("#select-class-input-format").find(':selected').val().trim();
      var studentDataHeader = $("#file-header").is(":checked");
      var studentDataMultiColumnClass = $("#multi-column-class").is(":checked");
      var dobFormat = $("#dob-format").val();
      var admissionDateFormat = $("#admission-date-format").val();

      var classColumn = null;
      var sectionColumn = null;
      var streamColumn = null;

      if(studentDataMultiColumnClass){
          var classIndexValue = $("#class-column-index").val().trim();
          if(classIndexValue != ""){
            classColumn = parseInt(classIndexValue);
          }

          var sectionIndexValue = $("#section-column-index").val().trim();
          if(sectionIndexValue != ""){
            sectionColumn = parseInt(sectionIndexValue);
          }

          var streamIndexValue = $("#stream-column-index").val().trim();
          if(streamIndexValue != ""){
            streamColumn = parseInt(streamIndexValue);
          }
      }

      var studentDataFieldList = [];
      $("tr.student-data-field-row").each(function() {
            var studentDataFieldName = $(this).attr("id");
            var mandatory = $(this).find('.toggle-switch:checkbox:checked').length > 0;
            var columnIndexValue = $(this).find('.column-index').val().trim();
            studentDataFieldList.push({'studentDataFieldName': studentDataFieldName, 'index' : columnIndexValue, 'mandatory' : mandatory});
      });

      return {'studentDataFieldList' : studentDataFieldList, 'studentDataDelimiter' : studentDataDelimiter, 'studentDataHeader' : studentDataHeader, 'dobFormat' : dobFormat, 'admissionDateFormat' : admissionDateFormat, 'studentClassColumnField' : {'studentDataMultiColumnClass' : studentDataMultiColumnClass, 'studentClassColumnFormat' : studentClassColumnFormat, 'classColumn' : classColumn, 'sectionColumn' : sectionColumn, 'streamColumn' : streamColumn}}
  },

  uploadStudentData : function () {
    if (($("#student-data-upload-file-input"))[0].files.length <= 0) {
      showErrorDialogBox("Provide valid student data file");
      return;
    }

    $("#upload-student-data-file-modal").modal('toggle');

    var file = ($("#student-data-upload-file-input"))[0].files[0];

    var formData = new FormData();
    if(file != "") {
      formData.append('document', file);
    }

    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;

    ajaxClient.uploadFile("/institute-onboarding/upload-student-data-file/"+instituteId, formData, function(data) {
        $("#status-modal-container").html(data);
        $("#status-modal").modal('toggle');
    });
  },

  loadStudentFileData : function () {
    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
    var fileId = $("#student-file-id").val().trim();
    ingestStudent.dataCache.selectedFileId = fileId
    ajaxClient.get("/institute-onboarding/load-student-file-data/"+instituteId+"?fileId="+fileId, function(data) {
        $("#student-file-data-container").html(data);
    });
  },

  studentIngestionDryRun : function () {
    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
    var fileId = ingestStudent.dataCache.selectedFileId;
    var admissionNumberOnlyCheck = $("#admission-number-check").is(":checked");
    var createUserCheck = $("#create-user-check").is(":checked");
    ingestStudent.dataCache.admissionNumberOnlyCheck = admissionNumberOnlyCheck;
    ingestStudent.dataCache.createUserCheck = createUserCheck;
    var academicSessionId = $("#institute-academic-sessions").find(':selected').val().trim();
    ajaxClient.post("/institute-onboarding/ingest-student-dry-run/"+instituteId+"?fileId="+fileId+"&academicSessionId="+academicSessionId+"&admissionNumberOnlyCheck="+admissionNumberOnlyCheck, {}, function(data) {
        $("#student-ingestion-summary-modal-container").html(data);
        $("#student-ingestion-dry-run-summary-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  confirmExecuteStudentIngestion : function () {
    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
    var instituteName = ingestStudent.dataCache.selectedInstitute.instituteName;
    var admissionNumberOnlyCheck = ingestStudent.dataCache.admissionNumberOnlyCheck;
    var createUserCheck = ingestStudent.dataCache.createUserCheck;
    var accepted = confirm("Do you want to execute student ingestion for " + instituteId + " - " + instituteName + " ?");
    if(!accepted){
        return;
    }
    $("#student-ingestion-dry-run-summary-modal").modal('toggle');

   
    var fileId = ingestStudent.dataCache.selectedFileId;
    var academicSessionId = $("#institute-academic-sessions").find(':selected').val().trim();
    ajaxClient.post("/institute-onboarding/ingest-student-execute/"+instituteId+"?fileId="+fileId+"&academicSessionId="+academicSessionId+"&admissionNumberOnlyCheck="+admissionNumberOnlyCheck+"&createUserCheck="+createUserCheck, {}, function(data) {
        $("#status-modal-container").html(data);
        $("#status-modal").modal('toggle');


    });
  },

  loadStudentIngestionResult : function () {
    var instituteId = ingestStudent.dataCache.selectedInstitute.instituteId;
    var resultId = $("#student-data-ingestion-result-id").val().trim();

    $("#student-data-ingestion-result-modal").modal('toggle');

    ajaxClient.get("/institute-onboarding/ingest-student-result/"+instituteId+"?resultId="+resultId, function(data) {
        $("#student-ingestion-summary-modal-container").html(data);
        $("#student-ingestion-execution-summary-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

}

var instituteManagement = {

  dataCache : {},

  resetDataCache : function () {
      instituteManagement.dataCache = {}
  },

  loadHomePage : function () {
    ajaxClient.get("/institute-onboarding/management/home", function(data) {
        instituteManagement.resetDataCache();
        $("#content").html(data);
        var allInstitutes = readJson("#all-institute");
        instituteManagement.dataCache.allInstitutes = allInstitutes;
        $("#institute-select-modal").modal({backdrop: 'static', keyboard: false});
        instituteManagement.bindSelectFeatureInput();
    });
  },

  // loadPreferenceHomePage : function () {
  //   ajaxClient.get("/institute-onboarding/preferences/home", function(data) {
  //       instituteManagement.resetDataCache();
  //       $("#content").html(data);
  //       var allInstitutes = readJson("#all-institute");
  //       instituteManagement.dataCache.allInstitutes = allInstitutes;
  //       $("#institute-select-modal").modal({backdrop: 'static', keyboard: false});
  //   });
  // },
  bindSelectFeatureInput : function () {
    $('#feature-select').on('change', function() {
       var feature = $(this).find(':selected').val().trim();
       if(feature == "preferences"){
          instituteManagement.loadInstitutePreferences();
       } else if (feature == "recharge-sms"){
          instituteManagement.loadInstituteSMSRechargeScreen();
       } else {
         alert("Feature not supported");
       }
    });
  },

  updateSelectedInstitute : function () {
      var instituteId = $("#select-institute").find(':selected').val().trim();
      if(instituteId == null || instituteId == ""){
        showErrorDialogBox("Invalid Institute. Please select valid institute");
        return;
      }

      var selectedInstitute = null;
      if(instituteId == "GLOBAL"){
          selectedInstitute = {"instituteId" : "GLOBAL", "instituteName" : "GLOBAL"};
      }else{
        var allInstitutes = instituteManagement.dataCache.allInstitutes;
        for(var i = 0 ; i < allInstitutes.length; i++){
          var institute = allInstitutes[i];
          if(institute.instituteId == parseInt(instituteId)){
              selectedInstitute = institute;
          }
        }
        if(selectedInstitute == null){
          showErrorDialogBox("Selected institute does not exists. Please select valid institute");
          return;
        }
      }


      instituteManagement.dataCache.selectedInstitute = selectedInstitute
      $("#selected-institute-display").text(selectedInstitute.instituteId + " (" + selectedInstitute.instituteName +")")
      $("#institute-select-modal").modal('toggle');

  },

  loadInstitutePreferences : function () {
    var instituteId = instituteManagement.dataCache.selectedInstitute.instituteId;
    ajaxClient.get("/institute-onboarding/preferences/data/"+instituteId, function(data) {
        $("#institute-feature-screen").html(data);
        instituteManagement.bindEditPreference();
    });
  },

  bindEditPreference : function () {
    $('.edit-preference').change(function() {
        if(this.checked) {
          $(this).closest('tr').find('.preference-value').prop('disabled', false )
        }else{
          $(this).closest('tr').find('.preference-value').prop('disabled', true)
        }
    });
  },

  updatePreferences : function () {
    var selectedRows = "";
    var updatePaylad = {};
    var row = 1;
    $('tr.feature-preference-entity').each(function() {
      if($(this).find('.edit-preference').is(':checked')){
        var prefEntityId = $(this).closest('tr.feature-preference-entity').attr("id");
        var prefGroupId = $(this).closest('div.feature-preference-group').attr("id");
        var featureId = $(this).closest('div.product-feature').attr("id");

        if(!(featureId in updatePaylad)){
          updatePaylad[featureId] = {};
        }

        if(!(prefGroupId in updatePaylad[featureId])){
          updatePaylad[featureId][prefGroupId] = {};
        }

        var prefEntityName = $(this).closest('tr.feature-preference-entity').find(".feature-preference-entity-name").text();

        var value = null;
        if($(this).closest('tr').find(".preference-value").is("input")){
          value = $(this).closest('tr').find(".preference-value").val().toString();
        }else if($(this).closest('tr').find(".preference-value").is("select")){
          value = $(this).closest('tr').find(".preference-value").find(":selected").val();
        }

        updatePaylad[featureId][prefGroupId][prefEntityId] = value;

        selectedRows += "<tr> <td> "+row+"</td> <td> "+ featureId +" </td> <td> " + prefGroupId + "</td> <td>" + prefEntityId + " </td> <td>" + prefEntityName + " </td> <td> "+ value +"</td> </tr>";
        row +=1;
      }
    });

    instituteManagement.dataCache.preferenceUpdatePayload = updatePaylad;

    $("#preview-table-content").html(selectedRows);
    $("#institute-preference-update-preview-modal").modal('toggle');
  },

  executePreferencesUpdate : function () {
    var instituteId = instituteManagement.dataCache.selectedInstitute.instituteId;
    var preferenceUpdatePayload = instituteManagement.dataCache.preferenceUpdatePayload;
    console.log(preferenceUpdatePayload);
    $("#institute-preference-update-preview-modal").modal('toggle');
    ajaxClient.post("/institute-onboarding/preferences/update/"+instituteId, { "preferenceUpdatePayload" : JSON.stringify(preferenceUpdatePayload) }, function(data) {
        $("#status-modal-container").html(data);
        $("#preference-update-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },


  loadInstituteSMSRechargeScreen : function () {
    var instituteId = instituteManagement.dataCache.selectedInstitute.instituteId;
    ajaxClient.get("/institute-onboarding/sms-recharge/home/"+instituteId, function(data) {
        $("#institute-feature-screen").html(data);
        // instituteManagement.bindEditPreference();
    });
  },

  updateSMSRechargeConfirmModal : function () {
      var smsRechargeCount = $("#sms-recharge-count").val();
      $("#confirm-sms-recharge-count").text(smsRechargeCount);
      $("#sms-recharge-confirm-modal").modal('toggle');
  },

  rechargeSMS : function () {
    $("#sms-recharge-confirm-modal").modal('toggle');
    var smsRechargeCountStr = $("#sms-recharge-count").val();
    var smsRechargeAmountStr = $("#sms-recharge-amount").val();
    var amountStatus = $("#amount-status").find(':selected').val().trim();
    var serviceProvider = $("#service-provider-type").find(':selected').val().trim();
    var smsRechargeDescription = $("#sms-recharge-description").val();

    var smsRechargeCount = 0;
    if(smsRechargeCountStr != ''){
      smsRechargeCount = parseInt(smsRechargeCountStr);
    }

    var smsRechargeAmount = 0;
    if(smsRechargeAmountStr != ''){
      smsRechargeAmount = parseInt(smsRechargeAmountStr);
    }

    if(smsRechargeCount <= 0 || smsRechargeAmount < 0 || amountStatus == "" || serviceProvider == ""){
      alert("Please fill all the required fields");
      return;
    }
    var smsRechargePayload = {'smsRechargeCount' : smsRechargeCount, 'smsRechargeAmount' : smsRechargeAmount, 'amountStatus' : amountStatus, 'serviceProvider' : serviceProvider, 'smsRechargeDescription' : smsRechargeDescription };
    var instituteId = instituteManagement.dataCache.selectedInstitute.instituteId;
    console.log(smsRechargePayload);
    ajaxClient.post("/institute-onboarding/sms-recharge/execute/"+instituteId, { "smsRechargePayload" : JSON.stringify(smsRechargePayload) }, function(data) {
      $("#status-modal-container").html(data);
      $("#sms-recharge-status-modal").modal({backdrop: 'static', keyboard: false});
    });

  },

  viewPreviousRecharges : function () {
    var instituteId = instituteManagement.dataCache.selectedInstitute.instituteId;
    ajaxClient.get("/institute-onboarding/sms-recharge/history/"+instituteId, function(data) {
      $("#previous-recharge-history-container").html(data);
      $("#previous-recharge-history-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

}
