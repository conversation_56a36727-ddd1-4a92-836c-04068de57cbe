{% load json %}
{% load displaytime %}
{% load string_utils %}
 <div class="col-12 col-lg-3 d-flex">
    <div class="card flex-fill w-100 pl-0 pr-0" style="height: 450px; overflow-y: auto; flex: 1; position: relative;">
      <div class="card-header">
        <h5 class="card-title mb-0" id="new-admission-graph-header-text">Student Birthdays</h5>
    </div>
    <!-- Store original birthday data for client-side filtering -->
    <script type="application/json" id="original-birthday-data">
        {
            "todayBirthdays": {{ todayBirthdays|jsonstr|safe }},
            "upcomingBirthdays": {{ upcomingBirthdays|jsonstr|safe }},
            "totalTodayBirthdays": {{ totalTodayBirthdays }},
            "totalUpcomingBirthdays": {{ totalUpcomingBirthdays }}
        }
    </script>
      <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-after-enrollment-structure-tab" data-toggle="tab" href="#nav-after-enrollment-structure" role="tab" aria-controls="nav-home" aria-selected="true">Today (<span id="today-birthdays-count">{{ totalTodayBirthdays }}</span>)</a>
          <a class="nav-item nav-link" id="nav-before-registration-structure-tab" data-toggle="tab" href="#before-registration-structure" role="tab" aria-controls="nav-profile" aria-selected="false">Upcoming (<span id="upcoming-birthdays-count">{{ totalUpcomingBirthdays }}</span>)</a>
        </div>
      </nav>
      <br/>
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-after-enrollment-structure" role="tabpanel" aria-labelledby="nav-after-enrollment-structure-tab">
              <div style="display:block;overflow-y: auto;max-height: 385px;" id="today-birthdays-container">
              {% if todayBirthdays %}
                <div class="d-flex">
                  <div class="w-100">
                    <div class="mb-0">
                      {% for student in todayBirthdays %}
                      <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateCurrentBirthdayCertificate(this);">
                        <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                          <div class="mt-3 ml-0 mr-2 mb-3 row">
                            <div class="col-3" style="padding-right: 0px;">
                              <div class="stat">
                                <div style="text-align: center;">{{ student.studentName|extract_initials }}</div>
                              </div>
                            </div>
                            <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                              <p class="mb-0" style="font-size:14px;">{{student.studentName}} ({{student.admissionNumber}})</p>
                              <p class="logo-color mb-0">{{student.birthdayDate|print_date_with_month_text}}</p>
                            </div>
                            <div class="col-3">
                              <p class="mb-0 pt-3" style="float:right">{{student.className}}</p>
                            </div>
                          </div>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              {% else %}
                <div class="text-center py-4">
                  <p class="text-muted">No birthdays today</p>
                </div>
              {% endif %}
              </div>
          </div>
          <div class="tab-pane fade" id="before-registration-structure" role="tabpanel" aria-labelledby="nav-before-registration-structure-tab">
            <div style="display:block;overflow-y: auto;max-height: 385px;" id="upcoming-birthdays-container">
            {% if upcomingBirthdays %}
              <div class="d-flex">
                <div class="w-100">
                  <div class="mb-0">
                      {% for student in upcomingBirthdays %}
                      <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateCurrentBirthdayCertificate(this);">
                        <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                          <div class="mt-3 ml-0 mr-2 mb-3 row">
                            <div class="col-3" style="padding-right: 0px;">
                              <div class="stat">
                                <div style="text-align: center;">{{ student.studentName|extract_initials }}</div>
                              </div>
                            </div>
                            <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                              <p class="mb-0" style="font-size:14px;">{{student.studentName}} ({{student.admissionNumber}})</p>
                              <p class="logo-color mb-0">{{student.birthdayDate|print_date_with_month_text}}</p>
                            </div>
                            <div class="col-3">
                              <p class="mb-0 pt-3" style="float:right">{{student.className}}</p>
                            </div>
                          </div>
                      </div>
                      {% endfor %}
                  </div>
              </div>
            </div>
            {% else %}
              <div class="text-center py-4">
                <p class="text-muted">No upcoming birthdays</p>
              </div>
            {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>