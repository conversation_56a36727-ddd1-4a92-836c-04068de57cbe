{% load json %}
{% load displaytime %}
<div class="card-body" id="event-details-list">
    <div>
        <table id="datatables-responsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Events</caption>
            <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col" class="w-25">Title</th>
                <th scope="col">Date</th>
                <th scope="col">Category</th>
                <th scope="col" colspan="2">Action</th>
            </tr>
            </thead>
            <tbody>
            {% for event in events %}
            <tr>
                <td style="display:none;">
                    <p class="event-info" style="display:none;">{{ event|jsonstr }}</p>
                </td>
                <th scope="row">{{ forloop.counter }}</th>
                <td>{{ event.title }}</td>
                <td>
                    {{ event.eventDate|print_date }}
                </td>
                <td>{{ event.category }}</td>
                <td style="width:10%;">
                    <button type="button" class="btn btn-outline-warning update-event" onclick="eventManagement.updateEventDetails(this);">
                        Update
                    </button>
                </td>
                <td style="width:10%;">
                    <button type="button" class="btn btn-outline-danger delete-event" id="{{ event.eventId }}" onclick="eventManagement.deleteEventDetails(this);">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>
