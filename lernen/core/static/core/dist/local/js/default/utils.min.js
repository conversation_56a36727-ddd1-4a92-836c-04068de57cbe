var typingTimer,doneTypingInterval=500,baseURL=getBaseURL(),GET_CALL_TIMEOUT=6e6,POST_CALL_TIMEOUT=6e6,FILE_UPLOAD_TIMEOUT=6e6,STANDARD_SMS_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT=153,SMS_UNICODE_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT=67,ONLINE_LECTURE_DISCUSSION="ONLINE_LECTURE_DISCUSSION",HOMEWORK_DISCUSSION="HOMEWORK_DISCUSSION",ADMIN="ADMIN",SYSTEM="SYSTEM",INTERNAL="INTERNAL",STUDENT="STUDENT",ONLINE_LECTURE="ONLINE_LECTURE",HOMEWORK="HOMEWORK",NOTICE="NOTICE",STUDENT_LEAVE_APPLICATION="STUDENT_LEAVE_APPLICATION",STAFF_LEAVE_APPLICATION="STAFF_LEAVE_APPLICATION",PRIMARY_LOGO_COLOR="#43a2ad",STAFF="STAFF",TABULAR="TABULAR",GRAPHICAL="GRAPHICAL",RUPEE_SYMBOL="₹",ACTION_TYPE="action_type";function getBaseURL(){return getRawBaseURL(!0)}function getRawBaseURL(e){var t=window.location,a=t.protocol+"//"+t.host,i=t.pathname.split("/");if(!e)return a;if(i.length>1){var n=i[1];if(null!=n.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i))return a+"/"+n}return a}function sleep(e){return new Promise(t=>setTimeout(t,e))}var themeHandler={loadTheme:function(e){confirm("Updating the theme will reload the entire page. All unsaved work will be lost. Do you want to proceed?")&&ajaxClient.getWithoutInstituteCode("/core/update-theme/"+e,function(e){location.reload()})}},paymentReminder={reminderRequired:!1,readPaymentState:function(){var e=$("#payment-status").text().trim();if(""!=e){var t=JSON.parse(e);return"PAID"==t.status||"RED"!=t.reminder_level?void(paymentReminder.reminderRequired=!1):(paymentReminder.reminderRequired=!0,void paymentReminder.scheduleReminder())}paymentReminder.reminderRequired=!1},scheduleReminder:function(){paymentReminder.reminderRequired&&setInterval(function(){$("#payment-reminder-modal").modal({backdrop:"static",keyboard:!1})},5e3)}},ajaxClient={get:function(e,t,a=!1){displayLoader(a),$.ajax({url:baseURL+e,type:"GET",timeout:GET_CALL_TIMEOUT,success:function(e){hideLoader(a),t(e),feather.replace()},error:function(e,t,i){"timeout"===t?(hideLoader(a),$("#request-timeout-modal").modal({backdrop:"static",keyboard:!1})):"403"==e.status?(hideLoader(a),$("#session-expired-modal").modal({backdrop:"static",keyboard:!1})):"404"==e.status?(hideLoader(a),$("#invalid-request-modal").modal({backdrop:"static",keyboard:!1})):"0"==e.status?(hideLoader(a),$("#request-offline-modal").modal({backdrop:"static",keyboard:!1})):(hideLoader(a),$("#server-not-responding-modal").modal({backdrop:"static",keyboard:!1}))}})},getWithoutInstituteCode:function(e,t){displayLoader(),$.ajax({url:getRawBaseURL(!1)+e,type:"GET",timeout:GET_CALL_TIMEOUT,success:function(e){hideLoader(),t(e),feather.replace()},error:function(e,t,a){"timeout"===t?(hideLoader(),$("#request-timeout-modal").modal({backdrop:"static",keyboard:!1})):"403"==e.status?(hideLoader(),$("#session-expired-modal").modal({backdrop:"static",keyboard:!1})):"404"==e.status?(hideLoader(),$("#invalid-request-modal").modal({backdrop:"static",keyboard:!1})):"0"==e.status?(hideLoader(),$("#request-offline-modal").modal({backdrop:"static",keyboard:!1})):(hideLoader(),$("#server-not-responding-modal").modal({backdrop:"static",keyboard:!1}))}})},post:function(e,t,a){displayLoader(),$.ajax({url:baseURL+e,type:"POST",data:t,timeout:POST_CALL_TIMEOUT,success:function(e){hideLoader(),a(e),feather.replace()},error:function(e,t,a){"timeout"===t?(hideLoader(),$("#request-timeout-modal").modal({backdrop:"static",keyboard:!1})):"403"==e.status?(hideLoader(),$("#session-expired-modal").modal({backdrop:"static",keyboard:!1})):"404"==e.status?(hideLoader(),$("#invalid-request-modal").modal({backdrop:"static",keyboard:!1})):"0"==e.status?(hideLoader(),$("#request-offline-modal").modal({backdrop:"static",keyboard:!1})):(hideLoader(),$("#server-not-responding-modal").modal({backdrop:"static",keyboard:!1}))}})},uploadFile:function(e,t,a){displayLoader(),$.ajax({url:baseURL+e,type:"POST",enctype:"multipart/form-data",data:t,processData:!1,contentType:!1,cache:!1,timeout:FILE_UPLOAD_TIMEOUT,success:function(e){hideLoader(),a(e),feather.replace()},error:function(e,t,a){"timeout"===t?(hideLoader(),$("#request-timeout-modal").modal({backdrop:"static",keyboard:!1})):"403"==e.status?(hideLoader(),$("#session-expired-modal").modal({backdrop:"static",keyboard:!1})):"404"==e.status?(hideLoader(),$("#invalid-request-modal").modal({backdrop:"static",keyboard:!1})):"0"==e.status?(hideLoader(),$("#request-offline-modal").modal({backdrop:"static",keyboard:!1})):(hideLoader(),$("#server-not-responding-modal").modal({backdrop:"static",keyboard:!1}))}})}},commonUtils={bindCardHoverEvent:function(){$(".card-hover").mouseout(function(){$(this).removeClass("card-hover-active")}).mouseover(function(){$(this).addClass("card-hover-active")})},bindReportCardClickEvent:function(){$(".report-card").on("click",function(){$("#"+$(this).attr("id")+"-modal").modal({backdrop:"static",keyboard:!1}),$(".report-select-all-class").length>0&&($(".report-select-all-class").prop("checked",!0),$(".select-report-student-class-menu").attr("style","display:none"))})},bindCardClickEvent:function(e){$(e).on("click",function(){$("#"+$(this).attr("id")+"-modal").modal({backdrop:"static",keyboard:!1}),$(".report-select-all-class").length>0&&($(".report-select-all-class").prop("checked",!0),$(".select-report-student-class-menu").attr("style","display:none"))})}},reportUtils={getReportHeaders:function(e){var t=[];return $(e).find("input.select-report-column").each(function(){var e=$(this).parent().find("p.column-json").text().trim(),a=JSON.parse(e);a.mandatory?t.push(a.key):$(this).prop("checked")&&t.push(a.key)}),t},getReportHeadersCSV:function(e){for(var t="",a=reportUtils.getReportHeaders(e),i=0;i<a.length;i++)t=0==i?a[i]:t+","+a[i];return t},getRequiredStandards:function(e){var t=[];return 0==$(e).find(".select-report-student-class-menu").length?t:($(e).find("input.select-report-class").each(function(){$(this).prop("checked")&&t.push($(this).attr("id"))}),t)},getRequiredStandardsCSV:function(e){for(var t="",a=reportUtils.getRequiredStandards(e),i=0;i<a.length;i++)t=0==i?a[i]:t+","+a[i];return t},bindSelectClassCheckboxEvent:function(){$(".report-select-all-class").change(function(){$(this).is(":checked")?$(".select-report-student-class-menu").attr("style","display:none"):$(".select-report-student-class-menu").attr("style","display:block")})}},academicSessionHandler={bindSessionChangeEvent:function(e){$(".academic-year-option-button").on("click",function(){var t=$(this).parent().find("p.academic-year-info").text().trim(),a=JSON.parse(t);$("#academic-year-display").text(a.displayName),$("#selected-academic-session-json").text(t),ajaxClient.getWithoutInstituteCode("/core/update-session/"+a.academicSessionId,function(e){}),e()})},getSelectedSession:function(){var e=$("#selected-academic-session-json").text().trim();return JSON.parse(e)},getSelectedSessionId:function(){return academicSessionHandler.getSelectedSession().academicSessionId},getSelectedSessionDisplayName:function(){return academicSessionHandler.getSelectedSession().displayName}},liveSearchHandler={bindEvent:function(e,t,a){$(e).on("keyup",function(e){clearTimeout(typingTimer),13!=e.which?typingTimer=setTimeout(function(){a(t)},doneTypingInterval):e.preventDefault()}),$(e).on("keydown",function(){clearTimeout(typingTimer)}),$(e).focus(function(){$(t).html("")}).blur(function(){})}},studentLiveSearchHandler={bindStudentSearchClickEvent:function(e,t){studentLiveSearchHandler.bindStudentSearchClickEventMethod(e,t,!1)},bindStudentSearchClickEventWithDisplayName:function(e,t){studentLiveSearchHandler.bindStudentSearchClickEventMethod(e,t,!0)},bindStudentSearchClickEventMethod:function(e,t,a){$("#live-search-student-results tbody tr").on("click",function(){var i=$(this).attr("id");if($(e).html(""),a){var n=$(this).find("td").first().text().trim();t(i,n)}else t(i)})}},staffLiveSearchHandler={bindStaffSearchClickEvent:function(e,t){staffLiveSearchHandler.bindStaffSearchClickEventMethod(e,t,!1)},bindStaffSearchClickEventWithDisplayName:function(e,t){staffLiveSearchHandler.bindStaffSearchClickEventMethod(e,t,!0)},bindStaffSearchClickEventMethod:function(e,t,a){$("#live-search-staff-results tbody tr").on("click",function(){var i=$(this).attr("id");if($(e).html(""),a){var n=$(this).find("td").first().text().trim();t(i,n)}else t(i)})}},studentDetails={fillStudentViewDetails:function(e){$("#detail-view\\.student-name").text(e.studentBasicInfo.name),$("#detail-view\\.registration-number").text(e.studentBasicInfo.registrationNumber),$("#detail-view\\.admission-number").text(e.studentBasicInfo.admissionNumber),$("#detail-view\\.pen-number").text(e.studentBasicInfo.penNumber),$("#detail-view\\.apaar-id-number").text(e.studentBasicInfo.apaarIdNo),null!=e.studentAcademicSessionInfoResponse?($("#detail-view\\.standard").text(e.studentAcademicSessionInfoResponse.standard.displayName),null!=e.studentAcademicSessionInfoResponse.standard.standardSectionList&&e.studentAcademicSessionInfoResponse.standard.standardSectionList.length>0?$("#detail-view\\.section").text(e.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionName):$("#detail-view\\.section").text("")):$("#detail-view\\.standard").text(""),$("#detail-view\\.gender").text(e.studentBasicInfo.gender),null!=e.studentBasicInfo.dateOfBirth&&e.studentBasicInfo.dateOfBirth>0?$("#detail-view\\.dob").text(getFormattedDate(e.studentBasicInfo.dateOfBirth)):$("#detail-view\\.dob").text(""),$("#detail-view\\.birth-place").text(e.studentBasicInfo.birthPlace),$("#detail-view\\.category").text(e.studentBasicInfo.userCategory),$("#detail-view\\.religion").text(e.studentBasicInfo.religion),$("#detail-view\\.caste").text(e.studentBasicInfo.caste),$("#detail-view\\.rte").text(e.studentBasicInfo.rteDisplay),$("#detail-view\\.mother-tongue").text(e.studentBasicInfo.motherTongue),$("#detail-view\\.area-type").text(e.studentBasicInfo.areaType),$("#detail-view\\.specially-abled").text(e.studentBasicInfo.speciallyAbledDisplay),$("#detail-view\\.bpl").text(e.studentBasicInfo.bplDisplay),$("#detail-view\\.present-address").text(getStudentPresentAddress(e.studentBasicInfo)),$("#detail-view\\.address").text(getStudentPermanentAddress(e.studentBasicInfo)),$("#detail-view\\.aadhar-number").text(e.studentBasicInfo.aadharNumber),$("#detail-view\\.registration-date").text(getFormattedDate(e.studentBasicInfo.registrationDate)),$("#detail-view\\.admission-date").text(getFormattedDate(e.studentBasicInfo.admissionDate)),$("#detail-view\\.primary-contact-number").text(e.studentBasicInfo.primaryContactNumber),$("#detail-view\\.primary-email").text(e.studentBasicInfo.primaryEmail),$("#detail-view\\.whatsapp-number").text(e.studentBasicInfo.whatsappNumber),$("#detail-view\\.is-sponsored").text(e.studentBasicInfo.sponsoredDisplay),$("#detail-view\\.student-new-admission").text(e.newAdmission?"Yes":"No"),$("#detail-view\\.student-hosteller").text(e.studentBasicInfo.hostellerDisplay),$("#detail-view\\.nationality").text(e.studentBasicInfo.nationality),null!=e.studentImage?$("#detail-view\\.student-image").text("Uploaded"):$("#detail-view\\.student-image").text("Not present"),$("#detail-view\\.student-house").text(null==e.studentBasicInfo.instituteHouse?"":e.studentBasicInfo.instituteHouse.houseName),$("#detail-view\\.student-admission-in-class").text(e.studentBasicInfo.admissionInClass),$("#detail-view\\.student-name-as-per-aadhar").text(e.studentBasicInfo.studentNameAsPerAadhar),$("#detail-view\\.child-category-criteria").text(e.studentBasicInfo.childCategoryCriteria),$("#detail-view\\.specially-abled-type").text(e.studentBasicInfo.speciallyAbledType),$("#detail-view\\.student-mother-name").text(e.studentFamilyInfo.mothersName),$("#detail-view\\.student-father-name").text(e.studentFamilyInfo.fathersName),$("#detail-view\\.student-mother-qualification").text(e.studentFamilyInfo.mothersQualification),$("#detail-view\\.student-father-qualification").text(e.studentFamilyInfo.fathersQualification),$("#detail-view\\.mother-contact-number").text(e.studentFamilyInfo.mothersContactNumber),$("#detail-view\\.father-contact-number").text(e.studentFamilyInfo.fathersContactNumber),$("#detail-view\\.mother-occupation").text(e.studentFamilyInfo.mothersOccupation),$("#detail-view\\.mother-annual-income").text(e.studentFamilyInfo.mothersAnnualIncome),$("#detail-view\\.father-occupation").text(e.studentFamilyInfo.fathersOccupation),$("#detail-view\\.father-annual-income").text(e.studentFamilyInfo.fathersAnnualIncome),$("#detail-view\\.mother-aadhar-number").text(e.studentFamilyInfo.mothersAadharNumber),$("#detail-view\\.father-aadhar-number").text(e.studentFamilyInfo.fathersAadharNumber),$("#detail-view\\.mother-pan-card-details").text(e.studentFamilyInfo.mothersPanCardDetails),$("#detail-view\\.father-pan-card-details").text(e.studentFamilyInfo.fathersPanCardDetails),$("#detail-view\\.annual-family-income").text(e.studentFamilyInfo.approxFamilyIncome),null!=e.studentGuardianInfoList&&e.studentGuardianInfoList.length>0?($("#detail-view\\.guardian-name").text(e.studentGuardianInfoList[0].guardianName),$("#detail-view\\.guardian-relation").text(e.studentGuardianInfoList[0].relation),$("#detail-view\\.guardian-age").text(e.studentGuardianInfoList[0].age),$("#detail-view\\.guardian-gender").text(e.studentGuardianInfoList[0].gender),$("#detail-view\\.guardian-occupation").text(e.studentGuardianInfoList[0].occupation),$("#detail-view\\.guardian-email").text(e.studentGuardianInfoList[0].email),$("#detail-view\\.guardian-contact-number").text(e.studentGuardianInfoList[0].contactNumber),$("#detail-view\\.guardian-address").text(getGuardianAddress(e.studentGuardianInfoList[0]))):($("#detail-view\\.guardian-name").text(""),$("#detail-view\\.guardian-relation").text(""),$("#detail-view\\.guardian-age").text(""),$("#detail-view\\.guardian-gender").text(""),$("#detail-view\\.guardian-occupation").text(""),$("#detail-view\\.guardian-email").text(""),$("#detail-view\\.guardian-contact-number").text(""),$("#detail-view\\.guardian-address").text("")),$("#detail-view\\.previous-school-name").text(e.studentPreviousSchoolInfo.schoolName),$("#detail-view\\.medium").text(e.studentPreviousSchoolInfo.medium),$("#detail-view\\.class-passed").text(e.studentPreviousSchoolInfo.classPassed),$("#detail-view\\.year-of-passing").text(e.studentPreviousSchoolInfo.yearOfPassing),$("#detail-view\\.result").text(e.studentPreviousSchoolInfo.result),$("#detail-view\\.percentage-grade").text(e.studentPreviousSchoolInfo.percentage),$("#detail-view\\.student-is-admission-tc-based").text(e.studentPreviousSchoolInfo.admissionTcBasedDisplay),$("#detail-view\\.previous-school-tc-number").text(e.studentPreviousSchoolInfo.tcNumber),$("#detail-view\\.blood-group").text(e.studentMedicalInfo.bloodGroup),$("#detail-view\\.blood-pressure").text(e.studentMedicalInfo.bloodPressure),$("#detail-view\\.pulse").text(e.studentMedicalInfo.pulse),$("#detail-view\\.height").text(e.studentMedicalInfo.height),$("#detail-view\\.weight").text(e.studentMedicalInfo.weight),$("#detail-view\\.date-of-physical-examination").text(getFormattedDate(e.studentMedicalInfo.dateOfPhysicalExamination))},fillStudentPreviewDetails:function(e){$("#preview\\.student-name").text(e.studentBasicInfo.name),$("#preview\\.registration-number").text(e.studentBasicInfo.registrationNumber),$("#preview\\.student-id").text(e.studentId),null!=e.studentAcademicSessionInfoResponse?($("#preview\\.standard").text(e.studentAcademicSessionInfoResponse.standard.displayName),null!=e.studentAcademicSessionInfoResponse.standard.standardSectionList&&e.studentAcademicSessionInfoResponse.standard.standardSectionList.length>0?$("#preview\\.section").text(e.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionName):$("#preview\\.section").text("")):$("#preview\\.standard").text(""),null!=e.studentImage?$("#preview\\.student-image").text("Uploaded"):$("#preview\\.student-image").text("Not present"),$("#preview\\.gender").text(e.studentBasicInfo.gender),null!=e.studentBasicInfo.dateOfBirth&&e.studentBasicInfo.dateOfBirth>0?$("#preview\\.dob").text(getFormattedDate(e.studentBasicInfo.dateOfBirth)):$("#preview\\.dob").text(""),$("#preview\\.birth-place").text(e.studentBasicInfo.birthPlace),$("#preview\\.category").text(e.studentBasicInfo.userCategory),$("#preview\\.religion").text(e.studentBasicInfo.religion),$("#preview\\.caste").text(e.studentBasicInfo.caste),$("#preview\\.rte").text(e.studentBasicInfo.rteDisplay),$("#preview\\.mother-tongue").text(e.studentBasicInfo.motherTongue),$("#preview\\.area-type").text(e.studentBasicInfo.areaType),$("#preview\\.specially-abled").text(e.studentBasicInfo.speciallyAbledDisplay),$("#preview\\.bpl").text(e.studentBasicInfo.bplDisplay),$("#preview\\.present-address").text(getStudentPresentAddress(e.studentBasicInfo)),$("#preview\\.address").text(getStudentPermanentAddress(e.studentBasicInfo)),$("#preview\\.aadhar-number").text(e.studentBasicInfo.aadharNumber),$("#preview\\.registration-date").text(getFormattedDate(e.studentBasicInfo.registrationDate)),$("#preview\\.admission-date").text(getFormattedDate(e.studentBasicInfo.admissionDate)),$("#preview\\.primary-contact-number").text(e.studentBasicInfo.primaryContactNumber),$("#preview\\.primary-email").text(e.studentBasicInfo.primaryEmail),$("#preview\\.whatsapp-number").text(e.studentBasicInfo.whatsappNumber),$("#preview\\.is-sponsored").text(e.studentBasicInfo.sponsoredDisplay),$("#preview\\.student-new-admission").text(e.studentBasicInfo.newAdmissionDisplay),$("#preview\\.nationality").text(e.studentBasicInfo.nationality),$("#preview\\.student-house").text(null==e.studentBasicInfo.instituteHouse?"":e.studentBasicInfo.instituteHouse.houseName),$("#preview\\.student-admission-in-class").text(e.studentBasicInfo.admissionInClass),$("#preview\\.student-name-as-per-aadhar").text(e.studentBasicInfo.studentNameAsPerAadhar),$("#preview\\.child-category-criteria").text(e.studentBasicInfo.childCategoryCriteria),$("#preview\\.specially-abled-type").text(e.studentBasicInfo.speciallyAbledType),$("#preview\\.student-mother-name").text(e.studentFamilyInfo.mothersName),$("#preview\\.student-father-name").text(e.studentFamilyInfo.fathersName),$("#preview\\.student-mother-qualification").text(e.studentFamilyInfo.mothersQualification),$("#preview\\.student-father-qualification").text(e.studentFamilyInfo.fathersQualification),$("#preview\\.mother-contact-number").text(e.studentFamilyInfo.mothersContactNumber),$("#preview\\.father-contact-number").text(e.studentFamilyInfo.fathersContactNumber),$("#preview\\.mother-occupation").text(e.studentFamilyInfo.mothersOccupation),$("#preview\\.mother-annual-income").text(e.studentFamilyInfo.mothersAnnualIncome),$("#preview\\.father-occupation").text(e.studentFamilyInfo.fathersOccupation),$("#preview\\.father-annual-income").text(e.studentFamilyInfo.fathersAnnualIncome),$("#preview\\.mother-aadhar-number").text(e.studentFamilyInfo.mothersAadharNumber),$("#preview\\.father-aadhar-number").text(e.studentFamilyInfo.fathersAadharNumber),$("#preview\\.annual-family-income").text(e.studentFamilyInfo.approxFamilyIncome),null!=e.studentGuardianInfoList&&e.studentGuardianInfoList.length>0?($("#preview\\.guardian-name").text(e.studentGuardianInfoList[0].guardianName),$("#preview\\.guardian-relation").text(e.studentGuardianInfoList[0].relation),$("#preview\\.guardian-age").text(e.studentGuardianInfoList[0].age),$("#preview\\.guardian-gender").text(e.studentGuardianInfoList[0].gender),$("#preview\\.guardian-occupation").text(e.studentGuardianInfoList[0].occupation),$("#preview\\.guardian-email").text(e.studentGuardianInfoList[0].email),$("#preview\\.guardian-contact-number").text(e.studentGuardianInfoList[0].contactNumber),$("#preview\\.guardian-address").text(getGuardianAddress(e.studentGuardianInfoList[0]))):($("#preview\\.guardian-name").text(""),$("#preview\\.guardian-relation").text(""),$("#preview\\.guardian-age").text(""),$("#preview\\.guardian-gender").text(""),$("#preview\\.guardian-occupation").text(""),$("#preview\\.guardian-email").text(""),$("#preview\\.guardian-contact-number").text(""),$("#preview\\.guardian-address").text("")),$("#preview\\.previous-school-name").text(e.studentPreviousSchoolInfo.schoolName),$("#preview\\.medium").text(e.studentPreviousSchoolInfo.medium),$("#preview\\.class-passed").text(e.studentPreviousSchoolInfo.classPassed),$("#preview\\.year-of-passing").text(e.studentPreviousSchoolInfo.yearOfPassing),$("#preview\\.result").text(e.studentPreviousSchoolInfo.result),$("#preview\\.percentage-grade").text(e.studentPreviousSchoolInfo.percentage),$("#preview\\.student-is-admission-tc-based").text(e.studentPreviousSchoolInfo.admissionTcBasedDisplay),$("#preview\\.previous-school-tc-number").text(e.studentPreviousSchoolInfo.tcNumber),$("#preview\\.blood-group").text(e.studentMedicalInfo.bloodGroup),$("#preview\\.blood-pressure").text(e.studentMedicalInfo.bloodPressure),$("#preview\\.pulse").text(e.studentMedicalInfo.pulse),$("#preview\\.height").text(e.studentMedicalInfo.height),$("#preview\\.weight").text(e.studentMedicalInfo.weight),$("#preview\\.date-of-physical-examination").text(getFormattedDate(e.studentMedicalInfo.dateOfPhysicalExamination))}},studentSearchWithoutAcademicSession={search:function(e,t){ajaxClient.get("/admission/student-search-without-session?text="+e,function(e){$(".search-students-without-academic-session-results").html(e),t(e),studentSearchWithoutAcademicSession.registerViewCallback(),studentSearchWithoutAcademicSession.registerPreviewCallback()})},registerViewCallback:function(){$(".view-student-info").on("click",function(){$("#view-student-details-modal").modal("toggle");var e=$(this).parent().parent().find(".student-info-json").text().trim(),t=JSON.parse(e);studentDetails.fillStudentViewDetails(t)})},registerPreviewCallback:function(){$(".preview-student-info").on("click",function(){$("#preview-student-details-modal").modal("toggle");var e=$(this).parent().parent().find(".student-info-json").text().trim(),t=JSON.parse(e);studentDetails.fillStudentPreviewDetails(t)})}};function registerStudentWithoutAcademicSessionSearchCallback(e){$(".search-students-without-academic-session").on("click",function(){var t=$(".search-students-without-academic-session-input").first().val();studentSearchWithoutAcademicSession.search(t,e)}),$(".search-students-without-academic-session-input").on("keyup",function(t){if(13==t.keyCode){var a=$(this).val();studentSearchWithoutAcademicSession.search(a,e)}})}function getStudentPresentAddress(e){var t="";return null!=e.presentAddress&&""!=e.presentAddress&&(t=t+e.presentAddress+", "),null!=e.presentCity&&""!=e.presentCity&&(t=t+e.presentCity+", "),null!=e.presentState&&""!=e.presentState&&(t=t+e.presentState+", "),null!=e.presentPostOffice&&""!=e.presentPostOffice&&(t=t+e.presentPostOffice+", "),null!=e.presentPoliceStation&&""!=e.presentPoliceStation&&(t=t+e.presentPoliceStation+", "),null!=e.presentZipcode&&""!=e.presentZipcode&&(t=t+e.presentZipcode+", "),""!=t&&(t=t.substring(0,t.length-2)),t}function getStudentPermanentAddress(e){var t="";return null!=e.permanentAddress&&""!=e.permanentAddress&&(t=t+e.permanentAddress+", "),null!=e.permanentCity&&""!=e.permanentCity&&(t=t+e.permanentCity+", "),null!=e.permanentState&&""!=e.permanentState&&(t=t+e.permanentState+", "),null!=e.permanentPostOffice&&""!=e.permanentPostOffice&&(t=t+e.permanentPostOffice+", "),null!=e.permanentPoliceStation&&""!=e.permanentPoliceStation&&(t=t+e.permanentPoliceStation+", "),null!=e.permanentZipcode&&""!=e.permanentZipcode&&(t=t+e.permanentZipcode+", "),""!=t&&(t=t.substring(0,t.length-2)),t}function getGuardianAddress(e){var t="";return null!=e.address&&""!=e.address&&(t=t+e.address+", "),null!=e.city&&""!=e.city&&(t=t+e.city+", "),null!=e.state&&""!=e.state&&(t=t+e.state+", "),null!=e.zipcode&&""!=e.zipcode&&(t=t+e.zipcode+", "),""!=t&&(t=t.substring(0,t.length-2)),t}function redirectToMainPage(){window.location.replace(baseURL)}function titleCase(e){if(null==e||null==e)return"";for(var t=e.toLowerCase().split(" "),a=0;a<t.length;a++)t[a]=t[a].charAt(0).toUpperCase()+t[a].substring(1);return t.join(" ")}function displayLoader(e=!1){e||$(".mask").addClass("ajax")}function hideLoader(e=!1){e||$(".mask").removeClass("ajax")}function enforceConstraints(){maxDigitsLengthConstraints(),preventSpecialChars()}function maxDigitsLengthConstraints(){$(".max-length-10").keypress(function(e){e.which;e.which>=48&&e.which<=57||e.preventDefault(),$(this).val().length>=10&&e.preventDefault()}),$(".max-length-6").keypress(function(e){e.which;e.which>=48&&e.which<=57||e.preventDefault(),$(this).val().length>=6&&e.preventDefault()})}var attachmentTileUtils={displayAttachmentTile:function(e,t){var a="";if(null!=e){for(var i=0;i<e.length;i++){var n='<p class="document-details-json" style="display:none;">'+JSON.stringify(e[i])+"</p>";if("pdf"==e[i].fileExtension)var o='<div class="text-left"><img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/Icons/icons8-adobe-acrobat-reader-64.png" style="width:40px"></div>';else if("png"==e[i].fileExtension||"jpg"==e[i].fileExtension)o='<div class="text-left"><img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/Icons/icons8-image-64.png" style="width:40px"></div>';else o='<div class="text-left"><img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/Icons/icons8-microsoft-excel-64.png" style="width:40px"></div>';a=a+'<div class="row row mt-2 ml-2">'+n+o+('<div class="col-sm-7"><p style="margin-bottom:0%;color:#212121;">'+e[i].documentName+"."+e[i].fileExtension+'</p><p style="cursor:pointer;color:#43a2ad;" class="mt-2" onclick="'+t+'">Click here to download</p></div>')+('<div class="col-sm-4 mt-2"><p style="cursor: pointer;float:right;" onclick="'+t+'"><img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/download-icon-20.png" ></p></div>')+'</div><hr style="border: 2px solid #f9f9f9;border-width: 1px;"/>'}return a}return"<p>No attachments!</p>"}};function preventSpecialChars(){$(".no-special-chars").keypress(function(e){e.which>=48&&e.which<=57||e.which>=65&&e.which<=90||e.which>=97&&e.which<=122||8==e.which||95==e.which||45==e.which||9==e.which||32==e.which||e.preventDefault()})}function validateEmail(e){return!e||null!=e.trim().match(/^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{1,5}|[0-9]{1,3})(\]?)$/)}function registerUploadFileCallback(){$(".upload-file-action").on("change",function(){var e=$(this).val().split("\\");e.length<=0?showErrorDialogBox("Invalid file"):$(this).next(".custom-file-label").html(e[e.length-1])})}function round(e,t){var a=Math.pow(10,t);return Math.round(e*a)/a}function previousPageClickingEvent(){$(".previous-page-link").on("click",function(){var e=$(".page-item.active").prev();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),e.trigger("click"),$(".switch-tab").addClass("disabled"),e.removeClass("disabled").addClass("active")})}function nextPageClickingEvent(){$(".next-page-link").on("click",function(){var e=$(".switch-tab.active").next();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),e.trigger("click"),$(".switch-tab").addClass("disabled"),e.removeClass("disabled").addClass("active")})}function readJson(e){var t=$(e).text().trim();return null==t||null==t||"None"===t||""===t?"":JSON.parse(t)}function registerDropDownSelectEvent(){$(".add-label").change(function(){addLabel(this,$(this).find(":selected").val().trim(),$(this).find(":selected").text().trim()),$(this).val("")})}function addLabel(e,t,a){var i=$(e).parent().find("p.label-container"),n=i.html(),o=!1;i.children("span").each(function(e){if($(this).find(".label-text").length>0){$(this).find(".label-text").text().trim();if($(this).find(".label-id").text().trim().toLowerCase()==t.toLowerCase())return o=!0,!1}}),o||""==t||""==a||(n+=getLabelText(t,a),i.html(n),registerLableCloseActions())}function getLabelText(e,t){return' <span class="badge badge-success label-area"> <span style="display:none;" class="label-id">'+e+'</span> <span class="label-text" >'+t+'</span> <button type="button" class="close label-close" data-dismiss="showErrorDialogBox" aria-label="Close"><span aria-hidden="true">&times;</span></button></span> '}function registerLableCloseActions(){$(".label-close").on("click",function(){$(this).parent().remove()})}function getDropdownSelectedAttributes(e){var t=$(e).parent().find("p.label-container"),a=[];return t.children("span").each(function(e){if($(this).find(".label-id").length>0){var t=$(this).find(".label-id").text().trim();a.push(t)}}),a}function validateMandatoryFields(e){clearMandatoryFieldsErrorDisplayInsideElement(e),bindRemoveErrorDisplayEvent();var t=!1;return $(e).find("input.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),t=!0)}),$(e).find("textarea.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),t=!0)}),$(e).find("select.mandatory-field").each(function(){0!=$(this).find(":selected").length&&""!=$(this).find(":selected").val().trim()||($(this).hasClass("selectpicker")?$(this).parent().css("border","1px solid #ff8795"):$(this).css("border","1px solid #ff8795"),t=!0)}),$(e).find("input.fix-length-6").each(function(){""!=$(this).val().trim()&&6!=$(this).val().trim().length&&($(this).css("border","1px solid #ff8795"),t=!0)}),t}function bindRemoveErrorDisplayEvent(){$(".mandatory-field").focus(function(){$(this).css("border",""),$(this).next().hasClass("mandatory-field-text")&&$(this).next().remove()}).blur(function(){}),$(".fix-length-6").focus(function(){$(this).css("border",""),$(this).next().hasClass("fix-length-text")&&$(this).next().remove()}).blur(function(){}),$("select.selectpicker").parent().find("button").on("click",function(){$(this).parent().parent().find(".error").remove(),$(this).parent().css("border","none")})}function clearMandatoryFieldsErrorDisplay(){$(".fix-length-6").css("border",""),$(".fix-length-text").remove(),$(".mandatory-field").css("border",""),$(".mandatory-field-text").remove()}function clearMandatoryFieldsErrorDisplayInsideElement(e){$(e).find(".fix-length-6").css("border",""),$(e).find(".fix-length-text").remove(),$(e).find(".mandatory-field").css("border",""),$(e).find(".mandatory-field-text").remove()}function closeModal(){clearMandatoryFieldsErrorDisplay()}function containsUnicodeCharacter(e){for(var t=0,a=e.length;t<a;t++)if(e.charCodeAt(t)>255)return!0;return!1}var pagination={bindEvents:function(e,t,a,i){$(".page-number-link").on("click",function(){$(".page-item").removeClass("active"),$(this).parent().addClass("active"),e()}),$(".previous-page-link").on("click",function(){var e=parseFloat($(".page-item.active").find(".page-number").text().trim());if(!(e-1<=0)){var a=e-1;$(".page-item").removeClass("active"),$("#page-number-"+a).addClass("active"),t()}}),$(".next-page-link").on("click",function(){var e=parseFloat($(this).parent().find(".total-pages").text().trim()),t=parseFloat($(".page-item.active").find(".page-number").text().trim());if(!(t+1>e)){var i=t+1;$(".page-item").removeClass("active"),$("#page-number-"+i).addClass("active"),a()}}),$("#items-per-page").change(function(){i()})},updatePaginationDetails:function(e,t){var a=$("#sidebar").height()-$("#"+e).position().top-250;$("#"+t).attr("style","height:"+a+"px;  overflow-y: scroll; cursor: pointer;");var i=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(i.itemsPerPage),$(".page-item").removeClass("active");var n=i.offset/i.itemsPerPage+1;$("#page-number-"+n).addClass("active")}},paginationWithContainer={bindEvents:function(e,t,a,i,n){$("#"+e).find(".page-number-link").on("click",function(){$("#"+e).find(".page-item").removeClass("active"),$(this).parent().addClass("active"),t()}),$("#"+e).find(".previous-page-link").on("click",function(){var t=parseFloat($("#"+e).find(".page-item.active").find(".page-number").text().trim());if(!(t-1<=0)){var i=t-1;$("#"+e).find(".page-item").removeClass("active"),$("#"+e).find("#page-number-"+i).addClass("active"),a()}}),$("#"+e).find(".next-page-link").on("click",function(){var t=parseFloat($(this).parent().find(".total-pages").text().trim()),a=parseFloat($("#"+e).find(".page-item.active").find(".page-number").text().trim());if(!(a+1>t)){var n=a+1;$("#"+e).find(".page-item").removeClass("active"),$("#"+e).find("#page-number-"+n).addClass("active"),i()}}),$("#"+e).find("#items-per-page").change(function(){n()})},updatePaginationDetails:function(e,t,a){var i=$("#"+a).find("#sidebar").height()-$("#"+a).find("#"+e).position().top-250;$("#"+a).find("#"+t).attr("style","height:"+i+"px;  overflow-y: scroll; cursor: pointer;");var n=JSON.parse($("#"+a).find("#pagination-info").text().trim());$("#"+a).find("#items-per-page").val(n.itemsPerPage),$("#"+a).find(".page-item").removeClass("active");var o=n.offset/n.itemsPerPage+1;$("#"+a).find("#page-number-"+o).addClass("active")}};function isEmpty(e){return 0===Object.keys(e).length}function getFloatValue(e){return null==e||null==e?0:""==e?0:parseFloat(e)}var channelCreditsHandler={dataCache:{},initDataCache:function(){channelCreditsHandler.dataCache.channelCreditsMap={}},loadChannelCredits:function(e){ajaxClient.get(e,function(e){$("#channel-credits-container").html(e);for(var t=readJson("#channel-credits-list"),a={},i=0;i<t.length;i++){var n=t[i];a[n.counterType]=n}channelCreditsHandler.dataCache.channelCreditsMap=a})},getChannelCredits:function(e){return e in channelCreditsHandler.dataCache.channelCreditsMap?channelCreditsHandler.dataCache.channelCreditsMap[e].count:0}};function permanentAddressUpdate(e,t,a,i,n,o,s,r,d,l,c,u,f){$("#"+e).on("change",function(){$(this).prop("checked")?($("#"+t).val($("#"+r).val()),$("#"+a).val($("#"+d).val()),$("#"+i).find(".country-states").val(null==$("#"+l).find(".country-states").find(":selected").val()?"":$("#"+l).find(".country-states").find(":selected").val().trim()),null!=n&&null!=c&&$("#"+n).val($("#"+c).val()),null!=o&&null!=u&&$("#"+o).val($("#"+u).val()),$("#"+s).val($("#"+f).val()),$(".address-field").find(".country-states").attr("disabled","disabled"),$(".address-field").attr("disabled","disabled")):($("#"+t).val(""),$("#"+a).val(""),$("#"+i).find(".country-states").val(""),$("#"+s).val(""),$("#"+n).val(""),$("#"+o).val(""),$(".address-field").find(".country-states").removeAttr("disabled"),$(".address-field").removeAttr("disabled"))}),$("#"+r).on("change",function(){$("#"+e).prop("checked")&&$("#"+t).val($("#"+r).val())}),$("#"+d).on("change",function(){$("#"+e).prop("checked")&&$("#"+a).val($("#"+d).val())}),$("#"+l).find(".country-states").on("change",function(){$("#"+e).prop("checked")&&$("#"+i).find(".country-states").val(null==$("#"+l).find(".country-states").find(":selected").val()?"":$("#"+l).find(".country-states").find(":selected").val().trim())}),$("#"+f).on("change",function(){$("#"+e).prop("checked")&&$("#"+s).val($("#"+f).val())})}function staffPermanentAddressUpdate(e,t,a,i,n,o,s,r,d,l,c,u,f){$("#"+e).on("change",function(){$(this).prop("checked")?($("#"+t).val($("#"+r).val()),$("#"+a).val($("#"+d).val()),$("#"+i).val($("#"+l).val()),$("#"+n).find(".country-states").val(null==$("#"+c).find(".country-states").find(":selected").val()?"":$("#"+c).find(".country-states").find(":selected").val().trim()),$("#"+o).find(".country").val(null==$("#"+u).find(".country").find(":selected").val()?"":$("#"+u).find(".country").find(":selected").val().trim()),$("#"+s).val($("#"+f).val()),$(".address-field").find(".country-states").attr("disabled","disabled"),$(".address-field").find(".country").attr("disabled","disabled"),$(".address-field").attr("disabled","disabled")):($("#"+t).val(""),$("#"+a).val(""),$("#"+i).val(""),$("#"+n).find(".country-states").val(""),$("#"+o).find(".country").val(""),$("#"+s).val(""),$(".address-field").find(".country-states").removeAttr("disabled"),$(".address-field").find(".country").removeAttr("disabled"),$(".address-field").removeAttr("disabled"))}),$("#"+r).on("change",function(){$("#"+e).prop("checked")&&$("#"+t).val($("#"+r).val())}),$("#"+d).on("change",function(){$("#"+e).prop("checked")&&$("#"+a).val($("#"+d).val())}),$("#"+l).on("change",function(){$("#"+e).prop("checked")&&$("#"+i).val($("#"+l).val())}),$("#"+c).find(".country-states").on("change",function(){$("#"+e).prop("checked")&&$("#"+n).find(".country-states").val(null==$("#"+c).find(".country-states").find(":selected").val()?"":$("#"+c).find(".country-states").find(":selected").val().trim())}),$("#"+u).find(".country").on("change",function(){$("#"+e).prop("checked")&&$("#"+o).find(".country").val(null==$("#"+u).find(".country").find(":selected").val()?"":$("#"+u).find(".country").find(":selected").val().trim())}),$("#"+f).on("change",function(){$("#"+e).prop("checked")&&$("#"+s).val($("#"+f).val())})}function getSmsCredits(e){if(null==e||null==e||""==e)return 0;var t=parseInt(e.length);return containsUnicodeCharacter(e)?t<=70?1:t<=134?2:3+Math.floor((t-134-1)/SMS_UNICODE_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT):t<=160?1:t<=306?2:3+Math.floor((t-306-1)/STANDARD_SMS_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT)}$(document).ready(function(){registerChangePassword(),bellNotification.initNotificationSetup()});var bellNotification={initNotificationSetup:function(){$("#bell-notification-count").text()<=0?$("#noti_Counter").hide():$("#noti_Counter").show(),$(document).click(function(){$("#notifications").hide()}),bellNotification.onClickNotificationButton()},onClickNotificationButton:function(){$("#noti_Button").click(function(){if($("#user-type-for-notification").text()!==STAFF)return $("#notifications").fadeToggle("fast","linear",function(){if($("#notifications").is(":visible")){$(".seeAll").show(),offset=0,limit=10;var e=bellNotification.getBellNotificationListURL();ajaxClient.get(e+offset+"/"+limit,function(e){$("#notifications").html(e),bellNotification.onClickNotification(),bellNotification.markListAsViewed(),bellNotification.markAllNotificationRead(),bellNotification.onClickSeeAllButton()})}}),$("#noti_Counter").fadeOut("slow"),!1})},getBellNotificationListURL:function(){var e=$("#user-type-for-notification").text();return e===ADMIN||e===SYSTEM||e===INTERNAL?"/adminportal/get-bell-notification-list/":e===STUDENT?"/studentportal/get-bell-notification-list/":e===STAFF?"/staffportal/get-bell-notification-list/":void 0},markListAsViewed:function(){var e=bellNotification.getMarkListOpenedURL();ajaxClient.get(e,function(e){})},getMarkListOpenedURL:function(){var e=$("#user-type-for-notification").text();return e===ADMIN||e===SYSTEM||e===INTERNAL?"/adminportal/mark-list-opened":e===STUDENT?"/studentportal/mark-list-opened":e===STAFF?"/staffportal/mark-list-opened/":void 0},markAllNotificationRead:function(){$(".markAllRead").click(function(){var e=bellNotification.getMarkNotificationReadURL();ajaxClient.get(e,function(e){$("#notifications").html(e),bellNotification.onClickNotification(),$("#notifications").show(),$(".seeAll").show()})})},getMarkNotificationReadURL:function(){var e=$("#user-type-for-notification").text();return e===ADMIN||e===SYSTEM||e===INTERNAL?"/adminportal/mark-as-read":e===STUDENT?"/studentportal/mark-as-read":e===STAFF?"/staffportal/mark-as-read/":void 0},onClickSeeAllButton:function(){$(".seeAll").click(function(){offset=0,limit=0;var e=bellNotification.getBellNotificationListURL();ajaxClient.get(e+offset+"/"+limit,function(e){$("#notifications").html(e),$(".seeAll").hide(),bellNotification.onClickNotification(),$("#notifications").show()})})},onClickNotification:function(){$(".notification-bar").click(function(){$("#notifications").hide();var e=$("#user-type-for-notification").text();e===ADMIN||e===SYSTEM||e===INTERNAL?bellNotification.onClickNotificationAdmin(this):e===STUDENT?bellNotification.onClickNotificationStudent(this):e===STAFF&&bellNotification.onClickNotificationStaff(this)})},onClickNotificationAdmin:function(e){notificationDetailsJson=$(e).find(".notification-details-json").text(),notificationDetails=JSON.parse(notificationDetailsJson);var t=notificationDetails.notificationId;null===notificationDetails.clickedOn&&ajaxClient.get("/adminportal/mark-as-read?notification_id="+t,function(e){$("#notifications").html(e)});var a=notificationDetails.entityName,i=$("#institute-unique-code").text(),n="";void 0!==notificationDetails.metaData.threadId&&(n=notificationDetails.metaData.threadId);var o="";if(void 0!==notificationDetails.metaData.conversationId&&(n=notificationDetails.metaData.threadId,o=notificationDetails.metaData.conversationId),a===ONLINE_LECTURE_DISCUSSION){var s=notificationDetails.metaData.lectureId;window.open("/"+i+"/lecture-management/dashboard?action_type=discussion_board&lecture_id="+s+"&thread_id="+n+"&conversation_id="+o,"_self")}else if(a===HOMEWORK_DISCUSSION){var r=notificationDetails.metaData.homeworkId;window.open("/"+i+"/homework-management/dashboard?action_type=discussion_board&homework_id="+r+"&thread_id="+n+"&conversation_id="+o,"_self")}else if(a===NOTICE){var d=notificationDetails.metaData.noticeId;window.open("/"+i+"/noticeboard/dashboard?action_type=notice&notice_id="+d,"_self")}else if(a===STUDENT_LEAVE_APPLICATION){var l=notificationDetails.metaData.transactionId;window.open("/"+i+"/attendance/dashboard?action_type=student_leave_application&transaction_id="+l,"_self")}else if("STUDENT_ADD_COMPLAIN"===a){var c=notificationDetails.metaData.complainId;window.open("/"+i+"/complaint-box/dashboard?action_type=student_add_complain&complain_id="+c,"_self")}else if("STUDENT_ADD_APPOINTMENT"===a)bellNotification.handleStudentAddAppointmentNotification(notificationDetails,i);else if("ADD_VISITOR"===a)bellNotification.handleVisitorAddAppointmentNotification(notificationDetails,i);else if(a===STAFF_LEAVE_APPLICATION){l=notificationDetails.metaData.transactionId;window.open("/"+i+"/leave-management/dashboard?action_type=staff_leave_application&transaction_id="+l,"_self")}},onClickNotificationStaff:function(e){notificationDetailsJson=$(e).find(".notification-details-json").text(),notificationDetails=JSON.parse(notificationDetailsJson);var t=notificationDetails.notificationId;null===notificationDetails.clickedOn&&ajaxClient.get("/staffportal/mark-as-read?notification_id="+t,function(e){$("#notifications").html(e)});var a=notificationDetails.entityName,i=$("#institute-unique-code").text(),n="";void 0!==notificationDetails.metaData.threadId&&(n=notificationDetails.metaData.threadId);var o="";if(void 0!==notificationDetails.metaData.conversationId&&(n=notificationDetails.metaData.threadId,o=notificationDetails.metaData.conversationId),a===ONLINE_LECTURE_DISCUSSION){var s=notificationDetails.metaData.lectureId;window.open("/"+i+"/lecture-management/dashboard?action_type=discussion_board&lecture_id="+s+"&thread_id="+n+"&conversation_id="+o,"_self")}else if(a===HOMEWORK_DISCUSSION){var r=notificationDetails.metaData.homeworkId;window.open("/"+i+"/homework-management/dashboard?action_type=discussion_board&homework_id="+r+"&thread_id="+n+"&conversation_id="+o,"_self")}else if(a===NOTICE){var d=notificationDetails.metaData.noticeId;window.open("/"+i+"/noticeboard/dashboard?action_type=notice&notice_id="+d,"_self")}else if(a===STUDENT_LEAVE_APPLICATION){var l=notificationDetails.metaData.transactionId;window.open("/"+i+"/attendance/dashboard?action_type=student_leave_application&transaction_id="+l,"_self")}else if("STUDENT_ADD_COMPLAIN"===a){var c=notificationDetails.metaData.complainId;window.open("/"+i+"/complaint-box/dashboard?action_type=student_add_complain&complain_id="+c,"_self")}else if("STUDENT_ADD_APPOINTMENT"===a)bellNotification.handleStudentAddAppointmentNotification(notificationDetails,i);else if(a===STAFF_LEAVE_APPLICATION){l=notificationDetails.metaData.transactionId;window.open("/"+i+"/leave-management/dashboard?action_type=staff_leave_application&transaction_id="+l,"_self")}},onClickNotificationStudent:function(e){notificationDetailsJson=$(e).find(".notification-details-json").text(),notificationDetails=JSON.parse(notificationDetailsJson);var t=notificationDetails.notificationId;null===notificationDetails.clickedOn&&ajaxClient.get("/studentportal/mark-as-read?notification_id="+t,function(e){$("#notifications").html(e)});var a=notificationDetails.entityName,i=($("#institute-unique-code").text(),"");void 0!==notificationDetails.metaData.threadId&&(i=notificationDetails.metaData.threadId);var n="";if(void 0!==notificationDetails.metaData.conversationId&&(i=notificationDetails.metaData.threadId,n=notificationDetails.metaData.conversationId),a===ONLINE_LECTURE_DISCUSSION||a===ONLINE_LECTURE){if($(".sidebar-menu").find(".nav-item").each(function(){$(this).removeClass("active"),$(this).find(".sidebar_icon").css("display","inline-block"),$(this).find(".sidebar_icon_active").css("display","none")}),$("#lectureDetailsNav").parent().addClass("active"),$("#lectureDetailsNav").find(".sidebar_icon").css("display","none"),$("#lectureDetailsNav").find(".sidebar_icon_active").css("display","inline-block"),a===ONLINE_LECTURE)lectureDetails.loadLectureDetailsPage();else if(a===ONLINE_LECTURE_DISCUSSION){var o=notificationDetails.metaData.lectureId;lectureDetails.getLectureDetailsPage(o,a,i,n)}}else if(a===HOMEWORK_DISCUSSION||a===HOMEWORK){if($(".sidebar-menu").find(".nav-item").each(function(){$(this).removeClass("active"),$(this).find(".sidebar_icon").css("display","inline-block"),$(this).find(".sidebar_icon_active").css("display","none")}),$("#homeworkDetailsNav").parent().addClass("active"),$("#homeworkDetailsNav").find(".sidebar_icon").css("display","none"),$("#homeworkDetailsNav").find(".sidebar_icon_active").css("display","inline-block"),a===HOMEWORK)homeworkDetails.loadHomeworkDetailsPage();else if(a===HOMEWORK_DISCUSSION){var s=notificationDetails.metaData.homeworkId;homeworkDetails.getHomeworkDetailsPage(s,a,i,n)}}else a===NOTICE&&($(".sidebar-menu").find(".nav-item").each(function(){$(this).removeClass("active"),$(this).find(".sidebar_icon").css("display","inline-block"),$(this).find(".sidebar_icon_active").css("display","none")}),$("#noticesNav").parent().addClass("active"),$("#noticesNav").find(".sidebar_icon").css("display","none"),$("#noticesNav").find(".sidebar_icon_active").css("display","inline-block"),notices.loadNoticesDetailsPage())},handleStudentAddAppointmentNotification:function(e,t){if(e.metaData&&e.metaData.appointmentId){var a=e.metaData.appointmentId;window.open("/"+t+"/parents-appointment/dashboard?action_type=student_add_appointment&appointment_id="+a,"_self")}},handleVisitorAddAppointmentNotification:function(e,t){if(e.metaData&&e.metaData.visitorId){var a=e.metaData.visitorId;window.open("/"+t+"/visitors-desk/dashboard?action_type=visitor_add_appointment&visitor_id="+a,"_self")}}};function removeURLParameter(e,t){var a=e.split("?");if(a.length>=2){for(var i=encodeURIComponent(t)+"=",n=a[1].split(/[&;]/g),o=n.length;o-- >0;)-1!==n[o].lastIndexOf(i,0)&&n.splice(o,1);return e=a[0]+"?"+n.join("&")}return e}function forgotPasswordScreen(){ajaxClient.post("/core/forgot-password",{},function(e){$(".limiter").html(e)})}function sendLinkToEmail(){var e=$("#username").val();""!==e&&"undefined"!==e?ajaxClient.post("/core/forgot-password/"+e,{},function(e){$(".outer-div").html(e)}):showErrorDialogBox("Please enter a username!")}function getCapitalizeText(e){return e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()}function sideBarHoverEventCallback(){$(".sidebar-link").hover(function(){sidebarHoverOnState(this)},function(){$(this).parent().hasClass("active")?sidebarHoverOnState(this):sidebarHoverOutState(this)})}function activateMenuItem(){$(".sidebar-link").click(function(e){$(".sidebar-nav li.active").removeClass("active"),$(this).parent().addClass("active"),sidebarHoverOutState($(".sidebar-nav").find("li").not(".active").find(".sidebar-link")),e.preventDefault()})}function sidebarHoverOutState(e){$(e).find("img.sidebar_icon").attr("style",""),$(e).find("img.sidebar_icon_active").attr("style","display:none")}function sidebarHoverOnState(e){$(e).find("img.sidebar_icon").attr("style","display:none"),$(e).find("img.sidebar_icon_active").attr("style","")}function showErrorDialogBox(e){$("#dialog-box-title").text("Error"),$("#dialog-box-body").text(e),$("#dialog-box-body").attr("style","color:red"),$("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal">Ok</button>'),$("#dialog-box-modal").modal({backdrop:"static",keyboard:!1})}function showWaringDialogBoxWithExistingModalDetails(e,t){$("#dialog-box-title").html('<i class="fas fa-exclamation-triangle"></i>Warning'),$("#dialog-box-body").text(e),$("#dialog-box-body").attr("style","color:red"),$("#modal-footer").html(`\n    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>\n    <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="togglePopup('${t}')">Yes, Proceed</button>`),$("#dialog-box-modal").modal("toggle")}function showErrorDialogBoxWithExistingModalDetails(e,t){togglePopup(t),$("#dialog-box-title").text("Error"),$("#dialog-box-body").text(e),$("#dialog-box-body").attr("style","color:red"),$("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal" onclick="togglePopup(\''+t+"')\">Ok</button>"),$("#dialog-box-modal").modal("toggle")}function togglePopup(e){$(e).modal("toggle"),$(".modal").each(function(){this.style.setProperty("overflow-y","auto","important")})}function showInformationDialogBox(e){$("#dialog-box-title").text("Information"),$("#dialog-box-body").text(e),$("#dialog-box-body").attr("style","color:black"),$("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal">Ok</button>'),$("#dialog-box-modal").modal({backdrop:"static",keyboard:!1})}function initSelect2(e="All",t="select2"){$("."+t).each(function(){$(this).wrap('<div class="position-relative"></div>').select2({placeholder:e,dropdownParent:$(this).parent(),closeOnSelect:!1})})}function initSingleSelect2(e,t="All"){$(e).wrap('<div class="position-relative"></div>').select2({placeholder:t,dropdownParent:$(this).parent(),closeOnSelect:!1})}function selectAllInSelect2(){$(".select2 > option").prop("selected","selected"),initSelect2("All")}function removeAllInSelect2(){$(".select2 > option").prop("selected",""),initSelect2("None")}function registerChangePassword(){$("#changePasswordNav").on("click",function(){changePassword.loadChangePasswordPage()})}var changePassword={loadChangePasswordPage:function(){var e="",t=$("#user-type-for-notification").text();t===ADMIN||t===SYSTEM||t===INTERNAL?e="/adminportal":t===STUDENT?e="/studentportal":t===STAFF&&(e="/staffportal"),ajaxClient.get(e+"/change-password",function(e){$("#main-content").html(e)})},changePassword:function(){var e=$("#old-password").val();if(""!==e){var t=$("#new-password").val();if(""!==t){var a=$("#confirm-new-password").val();if(""!==a){var i={oldPassword:e,newPassword:t};t!=a?showErrorDialogBox("Password don't match!!"):ajaxClient.post("/adminportal/update-password",{changePasswordInfo:JSON.stringify(i)},function(e){$("#change-password\\.status-modal-container").html(e),$("#change-password-status-modal").modal("toggle"),changePassword.loadChangePasswordPage()})}else showErrorDialogBox("Confirm New Password cannot be empty")}else showErrorDialogBox("New Password cannot be empty")}else showErrorDialogBox("Old Password cannot be empty")}},compressFileUtils={compress:async function(e){try{const t=e.size;console.log("Original file size: "+(t/1024).toFixed(2)+"KB");const a=await compressFileUtils.compressFile(e),i=a.size;return console.log("Compressed file size: "+(i/1024).toFixed(2)+"KB"),new File([a],e.name)}catch(e){console.log("Error compressing file:"+e)}return e},compressPDF:async function(e){const t=await PDFLib.PDFDocument.load(await e.arrayBuffer()),a=await t.save({useObjectStreams:!1});return new Blob([a],{type:"application/pdf"})},compressDOC:async function(e){const t=await mammoth.convertToHtml({arrayBuffer:await e.arrayBuffer()});return new Blob([t.value],{type:"text/html"})},compressExcel:function(e){const t=new FileReader;return new Promise((a,i)=>{t.onload=(e=>{const t=new Uint8Array(e.target.result),i=XLSX.read(t,{type:"array"}),n=XLSX.write(i,{bookType:"xlsx",type:"array",compression:!0});a(new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}))}),t.onerror=i,t.readAsArrayBuffer(e)})},compressPPT:function(e){return new Promise((t,a)=>{const i=new FileReader;i.onload=(a=>{const i=new Uint8Array(a.target.result),n=new PptxGenJS;n.load(i);const o=n.save("arraybuffer");t(new Blob([o],{type:e.type}))}),i.onerror=a,i.readAsArrayBuffer(e)})},compressImage:async function(e){const t={maxSizeMB:1,maxWidthOrHeight:1920,useWebWorker:!0};try{return await imageCompression(e,t)}catch(e){console.log(e)}},compressText:function(e){return new Promise(t=>{const a=new FileReader;a.onload=(e=>{const a=new Blob([e.target.result],{type:"text/plain"});t(a)}),a.readAsText(e)})},compressFile:async function(e){const t=e.type;if("application/pdf"===t)return await compressFileUtils.compressPDF(e);if("application/msword"===t||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===t)return await compressFileUtils.compressDOC(e);if("application/vnd.ms-excel"===t||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===t)return await compressFileUtils.compressExcel(e);if("application/vnd.ms-powerpoint"===t||"application/vnd.openxmlformats-officedocument.presentationml.presentation"===t)return await compressFileUtils.compressPPT(e);if(t.startsWith("image/"))return await compressFileUtils.compressImage(e);if("text/plain"===t)return await compressText(e);throw new Error("Unsupported file type")}};function autofillRollNumbers(){var e=parseInt($("#roll-number-start").val());isNaN(e)||e<=0?showErrorDialogBox("Please enter a valid number greater than zero"):$(".roll-number-input").each(function(t){$(this).val(e+t)})}var followup={addFollowUpDetails:function(e,t){var a=getDate($("#latest-followup-date").val());null!=a&&(a=a.getTime()/1e3),initDateWithYearRange("-10:+10",!0),$("#latest-followup-date").val(getFormattedDate(a)),initPastDateById("fee-transaction-follow-up-date",4e3),$("p#enquiry-follow-up-id").text(e),$("p#screen").text(t),$("#contact-person-transaction-name").val(""),$("#transaction-conversation").val(""),$("#next-enquiry-follow-up-date").val(""),$("#add-enquiry-follow-up-modal").modal("toggle")},addFollowupTrail:function(e){if(!validateMandatoryFields($("#add-enquiry-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#entity-transaction-name").val(),i=getDate($("#enquiry-follow-up-date").val());if(null!=i){i=i.getTime()/1e3;var n=$("#contact-person-transaction-name").val(),o=$("select#follow-up-transaction-type").val(),s=$("#transaction-conversation").val(),r=getDate($("#next-enquiry-follow-up-date").val());if(null!=r&&(r=r.getTime()/1e3),""==r&&(r=null),null!=r&&i>r)return $("#add-enquiry-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var d=$("#screen").text(),l=e,c=$("#enquiry-follow-up-id").text(),u={entityId:c,entityName:a,followUpDate:i,followUpMode:"MANUAL",contactPersonName:n,conversation:s,nextFollowUpDate:r,amount:"",followUpType:o,followUpStatus:l};$("#add-enquiry-follow-up-modal").modal("toggle"),ajaxClient.post("/admission/add-followup-trail/"+t,{followUpPayload:JSON.stringify(u)},function(e){$("#enquiry-status-modal-container").html(e),$("#update-enquiry-status-modal").modal({backdrop:"static",keyboard:!1}),"HOME"===d?admissionEnquiry.viewEnquiryDetails(!0,"ACCEPTED"):admissionEnquiry.showDetails(c,!0)})}else showErrorDialogBox("Please fill follow up date.")}},updateFollowupTrail:function(e){if(!validateMandatoryFields($("#update-enquiry-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#update-transaction-entity-name").val(),i=getDate($("#update-enquiry-follow-up-date").val());if(null!=i){i=i.getTime()/1e3;var n=$("#update-transaction-contact-person-name").val(),o=$("select#update-transaction-follow-up-type").val(),s=$("#update-transaction-conversation").val(),r=$("#update-next-enquiry-follow-up-date").val();if(""!=r&&(r=(r=getDate($("#update-next-enquiry-follow-up-date").val())).getTime()/1e3),""==r&&(r=null),null!=r&&i>r)return $("#update-enquiry-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var d=$("#update-transaction-amount").val(),l=e,c=$("#update-enquiry-follow-up-id").text(),u=$("#student-id").text(),f={entityId:u,followUpId:c,entityName:a,followUpDate:i,followUpMode:"MANUAL",contactPersonName:n,conversation:s,nextFollowUpDate:r,amount:d,followUpType:o,followUpStatus:l};$("#update-enquiry-follow-up-modal").modal("toggle"),ajaxClient.post("/admission/update-followup-trail/"+t,{updateFollowUpPayload:JSON.stringify(f)},function(e){$("#enquiry-status-modal-container").html(e),$("#update-enquiry-status-modal").modal({backdrop:"static",keyboard:!1}),admissionEnquiry.showDetails(u,!0)})}else showErrorDialogBox("Please fill follow up date.")}},deleteFollowUpDetails:function(e,t){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Delete Enquiry Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong color="red">Are you sure that you want to Delete this followup trail ?</strong>'),$("#modal-footer").html('<p style="display:none" id="delete-enquiry-follow-up-id"></p><p style="display:none" id="enquiry-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="followup.deleteFollowupTrail()">Yes, Delete</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-enquiry-follow-up-id").text(e),$("p#enquiry-follow-up-id").text(t),$("#enquiry-follow-up-close-modal").modal("toggle")},deleteFollowupTrail:function(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete-enquiry-follow-up-id").text().trim(),a=$("#enquiry-follow-up-id").text().trim();""!=t?($("#enquiry-follow-up-close-modal").modal("toggle"),ajaxClient.post("/admission/delete-followup-trail/"+e+"/"+t,{},function(e){$("#enquiry-status-modal-container").html(e),$("#update-enquiry-status-modal").modal({backdrop:"static",keyboard:!1}),admissionEnquiry.showDetails(a,!0)})):showErrorDialogBox("Invalid Follow Up to cancel")}},graphicalReportGenerator={loadGraphicalData:function(e,t){var a=$(e).find("#graphical-report-data").text();a&&""!==a.trim()&&"none"!==a.trim().toLowerCase()&&JSON.parse(a).chartDetailsList.forEach((a,i)=>{var n=`graphical-data-${i}`;if("PIE_CHART"===a.chartType)graphicalReportGenerator.renderPieChart(a,e,t,n);else{var o="BAR_GRAPH"===a.chartType?"bar":"line";graphicalReportGenerator.renderBarGraph(a,e,t,n,o,a.stacked)}})},renderPieChart:function(e,t,a,i){var n=e.twoDChartDetails,o=Object.keys(n),s=o.map(function(e){return n[e].colorHexCode}),r=o.map(function(e){return n[e].value||0}),d=$(t).find("#"+i);!d.length&&a.jQuery&&(d=a.jQuery("#"+i));var l=d.get(0).getContext("2d");new a.Chart(l,{type:"pie",data:{labels:o,datasets:[{data:r,backgroundColor:s,borderColor:"#ffffff",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0},title:{display:!0,text:e.title}}}})},renderBarGraph:function(e,t,a,i,n,o){var s=e.threeDChartDetails,r=Object.keys(s),d=r[0],l=s[d]?Object.keys(s[d]):[],c={};l.forEach(function(e){c[e]=s[d]&&s[d][e]&&s[d][e].colorHexCode||"#000000"});var u=l.map(function(e){return{label:e,data:r.map(function(t){return s[t][e]?s[t][e].value:0}),backgroundColor:c[e],borderColor:c[e],hoverBackgroundColor:c[e],hoverBorderColor:c[e],barPercentage:.5}}),f=$(t).find("#"+i);!f.length&&a.jQuery&&(f=a.jQuery("#"+i));var p=f.get(0).getContext("2d");new a.Chart(p,{type:n,data:{labels:r,datasets:u},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0},title:{display:!0,text:e.title}},scales:{x:{stacked:o,title:{display:!0,text:e.xAxisLabel},grid:{display:!0,color:"#ccc"}},y:{stacked:o,beginAtZero:!0,title:{display:!0,text:e.yAxisLabel},grid:{display:!0,color:"#eee"}}}}})}};