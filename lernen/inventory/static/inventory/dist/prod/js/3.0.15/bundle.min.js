var sidebarFull=200,sidebarCollapsed=80,baseURL=getBaseURL(),graphLibraryLoaded=!1;$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerStockDetailsMenu(),menuLoader.registerPurchaseOrderMenu(),menuLoader.registerSellItemsMenu(),menuLoader.registerExchangeItemsMenu(),menuLoader.registerIssueItemsMenu(),menuLoader.registerReturnItemsMenu(),menuLoader.registerSaleReturnItemsMenu(),menuLoader.registerSupplierMenu(),menuLoader.registerReportsMenu(),menuLoader.registerTransactionsMenu(),menuLoader.registerProductGroupsMenu(),menuLoader.registerBrandsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerStockDetailsMenu:function(){$("#stockDetailsNav").on("click",function(){inventoryStock.loadStockDetailsMenu()})},registerPurchaseOrderMenu:function(){$("#purchaseOrderNav").on("click",function(){inventoryPurchase.loadNewPurchaseMenu()})},registerSellItemsMenu:function(){$("#sellItemsNav").on("click",function(){inventorySale.loadNewSaleMenu(inventorySale.saleType)})},registerIssueItemsMenu:function(){$("#issueItemsNav").on("click",function(){inventoryIssueProduct.loadIssueProductMenu()})},registerExchangeItemsMenu:function(){$("#exchangeItemsNav").on("click",function(){inventoryExchangeProduct.loadExchangeProductMenu()})},registerReturnItemsMenu:function(){$("#returnItemsNav").on("click",function(){inventorySale.loadNewSaleMenu(inventorySale.returnType)})},registerSaleReturnItemsMenu:function(){$("#saleReturnItemsNav").on("click",function(){inventorySale.loadNewSaleMenu(inventorySale.saleReturnType)})},registerSupplierMenu:function(){$("#supplierDetailsNav").on("click",function(){inventorySupplier.loadSupplierMenu()})},registerReportsMenu:function(){$("#reportsNav").on("click",function(){inventoryReports.loadReportsMenu()})},registerTransactionsMenu:function(){$("#transactionsNav").on("click",function(){inventoryTransactions.loadTransactionMenu()})},registerProductGroupsMenu:function(){$("#productGroupNav").on("click",function(){inventoryProductGroup.loadProductGroupsMenu()})},registerBrandsMenu:function(){$("#brandNav").on("click",function(){inventoryBrand.loadBrandsMenu()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/inventory/home",function(t){$("#main-content").html(t)})},loadHomePageForSession:function(){ajaxClient.get("/inventory/session-home",function(t){$("#store-dashboard-session-content").html(t)})},refreshHomePage:function(){homePage.loadHomePageForSession()},displayDashboardContent:function(){var t=readJson("#home-page-transaction-stats"),e=[],a=[];for(day in t.perDaySaleAmount){var o=t.perDaySaleAmount[day];e.push(getShortWeekDay(day)),a.push(o)}homePage.renderSalesAmountChart(e,a);var n=[],r=[];for(day in t.perDayPurchaseAmount){o=t.perDayPurchaseAmount[day];n.push(getShortWeekDay(day)),r.push(o)}homePage.renderPurchaseAmountChart(n,r);var i=[],l=[];for(day in t.perDaySaleQuanitity){o=t.perDaySaleQuanitity[day];i.push(getShortWeekDay(day)),l.push(o)}homePage.renderSalesQuantityChart(i,l);var c=[],d=[];for(day in t.perDayPurchaseQuanitity){o=t.perDayPurchaseQuanitity[day];c.push(getShortWeekDay(day)),d.push(o)}homePage.renderPurchaseQuantityChart(c,d)},renderSalesAmountChart:function(t,e){new Chart($("#chartjs-sales-amount-distribution"),{type:"line",data:{labels:t,datasets:[{label:"Sales Amount",fill:!0,backgroundColor:"transparent",borderColor:window.theme.primary,data:e}]},options:{maintainAspectRatio:!1,legend:{display:!1},tooltips:{intersect:!1},hover:{intersect:!0},plugins:{filler:{propagate:!1}},scales:{xAxes:[{reverse:!0,gridLines:{color:"rgba(0,0,0,0.05)"}}],yAxes:[{ticks:{stepSize:500},display:!0,borderDash:[5,5],gridLines:{color:"rgba(0,0,0,0)",fontColor:"#fff"}}]}}})},renderPurchaseAmountChart:function(t,e){new Chart($("#chartjs-purchase-amount-distribution"),{type:"line",data:{labels:t,datasets:[{label:"Purchase Amount",fill:!0,backgroundColor:"transparent",borderColor:window.theme.warning,data:e}]},options:{maintainAspectRatio:!1,legend:{display:!1},tooltips:{intersect:!1},hover:{intersect:!0},plugins:{filler:{propagate:!1}},scales:{xAxes:[{reverse:!0,gridLines:{color:"rgba(0,0,0,0.05)"}}],yAxes:[{ticks:{stepSize:500},display:!0,borderDash:[5,5],gridLines:{color:"rgba(0,0,0,0)",fontColor:"#fff"}}]}}})},renderSalesQuantityChart:function(t,e){new Chart($("#chartjs-sales-quantity-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Sales Quantity",backgroundColor:window.theme.primary,borderColor:window.theme.primary,hoverBackgroundColor:window.theme.primary,hoverBorderColor:window.theme.primary,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderPurchaseQuantityChart:function(t,e){new Chart($("#chartjs-purchase-quantity-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Purchase Quantity",backgroundColor:window.theme.primary,borderColor:window.theme.primary,hoverBackgroundColor:window.theme.primary,hoverBorderColor:window.theme.primary,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeeCollectionPieChart:function(t,e){new Chart($("#chartjs-fee-collection-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,"#108F2B","#C08143 ","#DCDF69","#719E90","#A8B9DF ","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"],borderWidth:5,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cutoutPercentage:70,legend:{display:!1}}})}},inventoryStock={dataCache:{},loadStockDetailsMenu:function(){ajaxClient.get("/inventory/stock-home",function(t){$("#main-content").html(t),inventoryStock.bindAddInitialProductCheckbox("#add-stock-form"),initDate(365),inventoryStock.bindStockSearchCallback(),inventoryStock.registerInputEnterEvent(),registerLableCloseActions(),registerDropDownSelectEvent(),$("#add-stock-modal").on("shown.bs.modal",function(){function t(t){let e=$(t).val();if(!/^(\d+(\.\d{0,2})?)?$/.test(e)){alert("Please enter a valid price with no more than 2 decimal places.");let a=e.match(/^(\d+(\.\d{0,2})?)/);$(t).val(a?a[0]:"")}}$(".product-batch-selling-price").off("change").on("change",function(){t(this)}),$(".product-batch-purchase-price").off("change").on("change",function(){t(this)})})})},expandBatchDetails:function(t){$("#"+t).collapse("toggle")},bindAddInitialProductCheckbox:function(t){$(t).find(".add-initial-batch").click(function(){$(this).is(":checked")?$(this).closest(".product-container").find(".product-batch-details").attr("style","display:block"):$(this).closest(".product-container").find(".product-batch-details").attr("style","display:none")})},bindDeleteProductCheckbox:function(t){$(t).find(".delete-product").click(function(){$(this).closest(".product-container").remove(),inventoryStock.computeTotalProductsForASB(t)})},computeTotalProductsForASB:function(t){$("#total-product-count").val($(t).find(".product-container").length)},addProducts:function(){var t=[];$("#products").children(".product-container").each(function(){var e=$(this).find(".product-name").val(),a=$(this).find(".product-category").find(":selected").val().trim(),o=$(this).find(".product-brand").find(":selected").val().trim(),n=$(this).find(".product-description").val(),r=inventoryStock.getProductInitialBatchPayloadList(this);t.push({productMetadataPayload:{name:e,categoryId:a,brandId:o,description:n},productBatchPayloadList:r})}),$("#add-stock-modal").modal("toggle"),inventoryStock.addStock(t)},updateProduct:function(){var t=inventoryStock.dataCache.selectedSkuId,e=$("#update-product-name").val(),a=$("#update-product-category").find(":selected").val().trim(),o=$("#update-product-brand").find(":selected").val().trim(),n=$("#update-product-description").val(),r=[];$("#update-product-batches").children(".update-product-batch-container").each(function(){var t=$(this).attr("id");null!=t&&""!=t.trim()&&null!=t||(t=null);var e=$(this).find(".update-product-batch-name").val(),a=$(this).find(".update-product-batch-selling-price").val(),o=$(this).find(".update-product-batch-discount").val(),n=$(this).find(".update-discount-in-percent").is(":checked"),i=$(this).find(".update-product-batch-initial-quantity").val(),l=$(this).find(".update-product-batch-purchase-price").val(),c=$(this).find(".update-product-batch-mrp").val(),d=$(this).find(".update-product-batch-description").val(),s=$(this).find(".update-active-batch").is(":checked"),u=!1;$(this).find(".update-default-batch").is(":checked")&&(u=!0),$(this).find("input[name=radioName]:checked").val(),r.push({batchId:t,batchName:e,description:d,initialQuantity:i,sellingPrice:a,discount:o,discountInPercent:n,purchasePrice:l,mrp:c,defaultBatch:u,active:s})});var i={skuId:t,productMetadataPayload:{name:e,categoryId:a,brandId:o,description:n},productBatchUpdatePayloadList:r};$("#update-product-modal").modal("toggle"),ajaxClient.post("/inventory/update-product",{updatePayload:JSON.stringify(i)},function(t){$("#stock-status-modal-container").html(t),$("#stock-status-modal").modal("toggle"),inventoryStock.searchStock(!1)})},getProductInitialBatchPayloadList:function(t){return $(t).find(".add-initial-batch").is(":checked")?[{batchName:$(t).find(".product-batch-name").val(),description:$(t).find(".product-batch-description").val(),initialQuantity:$(t).find(".product-batch-initial-quantity").val(),sellingPrice:$(t).find(".product-batch-selling-price").val(),discount:$(t).find(".product-batch-discount").val(),discountInPercent:$(t).find(".discount-in-percent").is(":checked"),purchasePrice:$(t).find(".product-batch-purchase-price").val(),mrp:$(t).find(".product-batch-mrp").val()}]:[]},addASBProducts:function(t,e){var a=[];$("#asb-products").children(".product-container").each(function(){var o=$(this).find(".product-name").val(),n=$(this).find(".product-description").val(),r=inventoryStock.getProductInitialBatchPayloadList(this);a.push({productMetadataPayload:{name:o,categoryId:t,brandId:e,description:n},productBatchPayloadList:r})}),$("#advance-stock-products-modal").modal("toggle"),inventoryStock.addStock(a)},addStock:function(t){var e={productPayloadList:t};ajaxClient.post("/inventory/add-stock",{newProductRequest:JSON.stringify(e)},function(t){$("#stock-status-modal-container").html(t),$("#stock-status-modal").modal("toggle"),inventoryStock.searchStock(!0)})},bindStockSearchCallback:function(){$("#search-stock").on("click",function(){inventoryStock.searchStock(!0)}),$("#stock-search-text").on("keyup",function(t){13==t.keyCode&&inventoryStock.searchStock(!0)})},searchStock:function(t){searchText=$("#stock-search-text").val();var e=$(".page-item.active").find(".page-number").text().trim();t&&(e=1);var a=$("#items-per-page").val();null!=e&&""!=e||(e=1);var o=(e-1)*a;ajaxClient.get("/inventory/stock-search?text="+searchText+"&itemsPerPage="+a+"&offset="+o,function(t){$("#stock-search-result").html(t);var a=$("#sidebar").height()-$("#stock-search-result").position().top-300;$("#stock-search-result-fixed-height-list-wrapper").attr("style","height:"+a+"px;  overflow-y: scroll; cursor: pointer;");var o=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(o.itemsPerPage),$(".page-item").removeClass("active");o.offset,o.itemsPerPage;$("#page-number-"+e).addClass("active"),inventoryStock.initPagination();var n=readJson("#product-data-json"),r={};for(i=0;i<n.length;i++){var l=n[i];r[l.productMetadata.skuId]=l}inventoryStock.dataCache.productsMap=r})},initPagination:function(){pagination.bindEvents(function(){inventoryStock.searchStock(!1)},function(){inventoryStock.searchStock(!1)},function(){inventoryStock.searchStock(!1)},function(){inventoryStock.searchStock(!0)})},registerInputEnterEvent:function(){$(".add-label-input").on("keyup",function(t){13==t.keyCode&&inventoryStock.addInputLables(this)}),$(".add-label-input").focus(function(){}).blur(function(){inventoryStock.addInputLables(this)})},addInputLables:function(t){var e=$(t).val();if(""!=e){var a=e.trim().split(",");$(t).val("");var o=$(t).parent().find("p"),n=o.html(),r=[];a.forEach(function(t){var e=!1;o.children("span").each(function(a){if($(this).find(".label-text").length>0&&$(this).find(".label-text").text().trim().toLowerCase()==t.trim().toLowerCase())return void(e=!0)}),e||r.push(t)}),r.length>0&&(r.forEach(function(t){""!=t.trim()&&(n=n+' <span class="badge badge-info label-area" style="padding: .3em .4em .4em; margin: 0 .1em; font-size: 14px;" "> <span class="label-text">'+t.trim()+'</span> <button type="button" class="close label-close" ><span aria-hidden="true">&times;</span></button></span> ')}),o.html(n),registerLableCloseActions())}},previewStock:function(){var t=$("#asb-product-name").val(),e=$("#asb-product-category").find(":selected"),a=e.val().trim(),o=e.text().trim(),n=$("#asb-product-brand").find(":selected"),r=n.val().trim(),i=n.text().trim(),l=inventoryStock.getSelectedAttributes("#asb-product-sizes"),c=inventoryStock.getSelectedAttributes("#asb-product-colors"),d=getDropdownSelectedAttributes("#asb-product-genders"),s=$("#asb-product-description").val();$("#advance-stock-builder-modal").modal("toggle");var u={name:t,sizes:l,colors:c,genders:d,categoryId:a,categoryText:o,brandId:r,brandText:i,description:s};ajaxClient.post("/inventory/product/variations",{productVariationsRequest:JSON.stringify(u)},function(t){$("#advance-stock-builder-modal-container").html(t),inventoryStock.bindAddInitialProductCheckbox("#add-advance-stock-form"),inventoryStock.bindDeleteProductCheckbox("#add-advance-stock-form"),$("#advance-stock-products-modal").modal({backdrop:"static",keyboard:!1})})},backToASBBuilder:function(){$("#advance-stock-products-modal").modal("toggle"),$("#advance-stock-builder-modal").modal("toggle")},getSelectedAttributes:function(t){var e=$(t).parent().find("p"),a=[];return e.children("span").each(function(t){if($(this).find(".label-text").length>0){var e=$(this).find(".label-text").text().trim();a.push(e)}}),a},editProduct:function(t){var e=inventoryStock.dataCache.productsMap;if(t in e){var a=e[t];inventoryStock.dataCache.selectedSkuId=t,$("#update-product-name").val(a.productMetadata.name),$("#update-product-category").val(a.productMetadata.categoryId),$("#update-product-brand").val(a.productMetadata.brandId),$("#update-product-description").val(a.productMetadata.description),$("#update-product-batches").html(inventoryStock.buildProductBatches(a)),$("#update-product-modal").modal({backdrop:"static",keyboard:!1})}},deleteProductPreview:function(t){t in inventoryStock.dataCache.productsMap&&(inventoryStock.dataCache.deleteSelectedSkuId=t,$("#delete-product-modal").modal({backdrop:"static",keyboard:!1}))},deleteProduct:function(){$("#delete-product-modal").modal("toggle");var t=inventoryStock.dataCache.deleteSelectedSkuId;ajaxClient.post("/inventory/delete-product/"+t,{},function(t){$("#stock-status-modal-container").html(t),$("#stock-status-modal").modal("toggle"),inventoryStock.searchStock(!0)})},addBatch:function(){$("#update-product-batches").append(inventoryStock.buildProductBatchRow({batchId:"",batchName:"",description:"",discount:"",discountInPercent:!1,initialQuantity:"",sellingPrice:"",totalQuantity:""}))},removeBatch:function(t){$(t).closest(".update-product-batch-container").remove()},buildProductBatches:function(t){var e=t.productBatchDataList;if(null==e||0==e.length)return"";var a=[];for(i=0;i<e.length;i++){var o=e[i],n=inventoryStock.buildProductBatchRow(o);a.push(n)}return a},buildProductBatchRow:function(t){var e=t.batchId,a=t.batchName,o=t.description,n=t.discount,r=t.discountInPercent,i=t.initialQuantity,l=t.sellingPrice,c=t.purchasePrice,d=t.mrp,s=t.totalQuantity,u=t.inUse,p=t.defaultBatch,v="";r&&(v="checked");var h="";t.active&&(h="checked"),null==o&&(o="");var m="Batch has no transactions yet",f="#18A558",y='<div class="form-group col-md-1"> <button type="button" class="close remove-product-batch" aria-label="Close" onclick="inventoryStock.removeBatch(this)"> <span aria-hidden="true"> &times; </span> </button> </div>';u&&(m="Batch has been used in transactions",f="#B8390E",y="");var b="";p&&(b="checked");var g="batch-"+e;return'<div class="card update-product-batch-container" id="'+e+'"> <div class="card-body"> <div class="update-product-batch-details"> <div class="form-row"> <div class="form-group col-md-3"> <div class="form-check"> <input class="form-check-input update-default-batch" type="radio" name="defaultBatchRadio" id="'+g+'" value="'+g+'" '+b+'> <label class="form-check-label" for="'+g+'"> Default Batch </label> </div> </div> <div class="form-group col-md-3"> <div class="form-check"> <input type="checkbox" class="form-check-input update-active-batch" '+h+'> <label class="form-check-label"> <strong>Active Batch</strong> </label> </div> </div> <div class="form-group col-md-5"> <p> <span style="color:'+f+';font-weight:bold;" >'+m+"</span></p> </div> "+y+' </div> <div class="form-row"> <div class="form-group col-md-3"> <label>Batch Name*</label> <input type="text" class="form-control update-product-batch-name mandatory-field" value="'+a+'"> </div> <div class="form-group col-md-3"> <label>Available Quantity</label> <input type="number" class="form-control update-product-batch-available-quantity" placeholder="0" value="'+s+'" readonly> </div> <div class="form-group col-md-2"> <label>Selling Price</label> <input type="number" class="form-control update-product-batch-selling-price mandatory-field" value="'+l+'"> </div> <div class="form-group col-md-2"> <label>Discount</label> <input type="number" class="form-control update-product-batch-discount" placeholder="0" value="'+n+'"> </div> <div class="form-group col-md-2"> <div class="form-check" style="margin-top:20%;"> <input type="checkbox" class="form-check-input update-discount-in-percent" '+v+'> <label class="form-check-label"> <strong>Discount in % </strong> </label> </div> </div> </div> <div class="form-row"> <div class="form-group col-md-2"> <label>Initial Quantity</label> <input type="number" class="form-control update-product-batch-initial-quantity" placeholder="Quanity" value="'+i+'"> </div> <div class="form-group col-md-2"> <label>Purchase Price</label> <input type="number" class="form-control update-product-batch-purchase-price" value="'+c+'"> </div> <div class="form-group col-md-2"> <label>MRP</label> <input type="number" class="form-control update-product-batch-mrp" value="'+d+'"> </div> <div class="form-group col-md-6"> <label>Batch Description</label> <textarea class="form-control update-product-batch-description" rows="1" placeholder="Batch Description">'+o+"</textarea> </div> </div> </div> </div> </div>"}},inventoryPurchase={dataCache:{},loadNewPurchaseMenu:function(){ajaxClient.get("/inventory/new-purchase-home",function(t){$("#main-content").html(t),inventoryPurchase.init()})},loadPurchaseOrderForm:function(){ajaxClient.get("/inventory/new-purchase-form",function(t){$("#purchase-order-body").html(t),inventoryPurchase.init()})},init:function(){var t=readJson("#category-data"),e=readJson("#brand-data");inventoryPurchase.dataCache={categories:t,brands:e},initDate(pastTransactionDays),liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId,inventoryUtils.liveSearchProductResultElementId,inventoryPurchase.donePurchaseProductTyping),inventoryPurchase.bindPaymentStatusSelect()},donePurchaseProductTyping:function(t){var e=$(inventoryUtils.liveSearchProductElementId).val();ajaxClient.get("/inventory/live-stock-search?text="+e+"&addNewProduct=True",function(a){$(t).html(a),inventoryUtils.bindClickEventOnSearchProduct(e,inventoryPurchase.dataCache,inventoryPurchase.getBatchSelectColumn,inventoryPurchase.getRowColumns,!0,inventoryPurchase.getRowDetails,inventoryPurchase.bindPurchaseNewRowEvent,!0,inventoryPurchase.onDelete)})},getBatchSelectColumn:function(t){return'<div class="input-group"> <select class="custom-select product-batch-select">'+t+'</select> <div class="input-group-append"> <button class="btn btn-outline-secondary add-product-batch" type="button">+</button> </div> </div>'},onDelete:function(){inventoryPurchase.computeNetPurchaseAmount()},bindPurchaseNewRowEvent:function(){inventoryPurchase.bindCostComputeEvent()},bindPaymentStatusSelect:function(){},getRowColumns:function(t,e,a,o,n,r,i){var l='<td scope="row" style="width:15%;"> '+a+" </td>";t&&(l='<td scope="row" style="width:15%;"> <input type="text" class="form-control form-control-sm new-product-name" placeholder="Product name" value="'+a+'"> </td>');var c=0;return null!=e&&null==(c=e.productBatch.purchasePrice)&&(c=0),[l,'<td style="width:15%;">'+o+"</td>",'<td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity" '+n+"> </td> ",' <td><input type="number" class="form-control form-control-sm purchase-price mandatory-table-field positive-number" placeholder="0" value="'+c+'" '+n+"></td> ",' <td><input type="number" class="form-control form-control-sm purchase-item-amount" placeholder="0" readonly></td> ','<td><input type="number" class="form-control form-control-sm purchase-discount percentage-range" placeholder="Discount" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm purchase-product-tax percentage-range" placeholder="Tax" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm purchase-item-total" placeholder="0" readonly></td>']},getCategoryCol:function(){for(var t=inventoryPurchase.dataCache.categories,e='<option value="">select</option>',a=0;a<t.length;a++){var o=t[a];e+='<option value="'+o.categoryId+'">'+o.categoryName+"</option>"}return'<td><select class="form-control product-category mandatory-field">'+e+"</select></td>"},getBrandCol:function(){for(var t=inventoryPurchase.dataCache.brands,e='<option value="">select</option>',a=0;a<t.length;a++){var o=t[a];e+='<option value="'+o.brandId+'">'+o.brandName+"</option>"}return'<td><select class="form-control product-brand mandatory-field">'+e+"</select></td>"},getRowDetails:function(t,e){var a="",o="",n="",r="",i="",l="",c="";if(t){c="show",n=inventoryPurchase.getCategoryCol(),r=inventoryPurchase.getBrandCol(),a='<th scope="col">Category*</th>',o='<th scope="col">Brand</th>';i='<th scope="col">Product Description</th>',l='<td><textarea class="form-control product-description" rows="1" placeholder="Product Description" ></textarea></td>'}var d="readonly",s="disabled";(t||null==e)&&(d="",s="");var u="",p="",v="",h="",m="";return null!=e&&(u=e.productBatch.mrp,p=e.productBatch.sellingPrice,v=e.productBatch.discount,e.productBatch.discountInPercent&&(h="checked"),null==(m=e.productBatch.description)&&(m="")),'<tr class="product-batch-details-row"> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-batch-details-container '+c+' "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> '+a+" "+o+" "+i+' <th scope="col">MRP</th> <th scope="col">Selling Price</th> <th scope="col">Discount</th> <th scope="col"></th> <th scope="col">Batch Description</th> </tr> </thead> <tbody> <tr> '+n+" "+r+" "+l+' <td><input type="number" class="form-control form-control-sm product-batch-mrp"  placeholder="0" value="'+u+'" '+d+'></td> <td><input type="number" class="form-control form-control-sm product-batch-selling-price"  placeholder="0" value="'+p+'" '+d+'></td> <td><input type="number" class="form-control form-control-sm product-batch-discount"  placeholder="0" value="'+v+'" '+d+'></td><td><div class="form-group"> <div class="form-check" style="margin-top:8%;"> <input type="checkbox" class="form-check-input discount-in-percent" '+h+" "+s+' > <label class="form-check-label"> <strong>Disc. in % </strong> </label> </div> </div></td> <td><textarea class="form-control batch-description" rows="1" placeholder="Batch Description" '+d+">"+m+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>"},bindCostComputeEvent:function(){$(".product-quantity").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent())}),$(".purchase-price").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent())}),$(".purchase-discount").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent())}),$(".purchase-product-tax").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent())}),$("#additional-cost-input").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent().parent())}),$("#additional-discount-input").focus(function(){}).blur(function(){inventoryPurchase.computeTotal($(this).parent().parent().parent())})},computeTotal:function(t){var e=$(t).find(".product-quantity").first().val(),a=0;e&&(a=parseFloat(e));var o=$(t).find(".purchase-price").first().val(),n=0;o&&(n=parseFloat(o));var r=parseFloat($(t).first().find(".purchase-discount").first().val()),i=0;r&&(i=parseFloat(r));var l=parseFloat($(t).first().find(".purchase-product-tax").first().val()),c=0;l&&(c=parseFloat(l));var d=a*n,s=d+d*c/100-(d+d*c/100)*i/100;$(t).find(".purchase-item-amount").first().val(d.toFixed(2)),$(t).find(".purchase-item-total").first().val(s.toFixed(2)),inventoryPurchase.computeNetPurchaseAmount()},computeNetPurchaseAmount:function(){var t=0,e=0,a=0;$("tr.selected-product-row").each(function(){var o=$(this).find("input.product-quantity").val();""==o&&(o=0);var n=$(this).find("input.purchase-price").val();""==n&&(n=0);var r=$(this).find("input.purchase-discount").val();""==r&&(r=0);var i=$(this).find("input.purchase-product-tax").val();""!=i&&void 0!==i||(i=0);var l=parseFloat(n)*parseFloat(o),c=l*parseFloat(r)/100,d=(l-c)*parseFloat(i)/100;t+=c,e+=d,a+=l});var o=$("#additional-cost-input").val(),n=0;""!=o&&(n=parseFloat(o));var r=$("#additional-discount-input").val(),i=0;return""!=r&&(i=parseFloat(r)),$("#sub-total").text(a.toFixed(2)),$("#total-discount").text(t.toFixed(2)),$("#total-tax").text(e.toFixed(2)),$("#additional-cost").text(n.toFixed(2)),$("#additional-discount").text(i.toFixed(2)),$("#net-total").text((a+n-t-i+e).toFixed(2)),a+n-t-i+e},addNewPurchase:function(){$("#new-purchase-confirmation-modal").modal("toggle");var t=inventoryPurchase.collectPurchasedProducts();if(0!=t.length){var e=$("#purchase-supplier option:selected").val(),a=$("#purchase-payment-mode option:selected").val(),o=$("#purchase-reference").val(),n=$("#purchase-description").val(),r=getDate($("#purchase-date").val()),i=$("#additional-cost-input").val(),l=0;""!=i&&(l=parseFloat(i));var c=$("#additional-discount-input").val(),d=0;""!=c&&(d=parseFloat(c));var s=inventoryPurchase.computeNetPurchaseAmount();if(s<0)showErrorDialogBox("Net Payable amount can't be less than 0");else newPurchasePayload={supplierId:e,transactionMode:a,paymentAmount:s,reference:o,transactionDate:r.getTime(),description:n,additionalCost:l,additionalDiscount:d,purchasedProductBatchList:t},ajaxClient.post("/inventory/purchase-transaction",{newPurchasePayload:JSON.stringify(newPurchasePayload)},function(t){$("#purchase-status-modal-container").html(t),$("#purchase-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Please select atleast one product for purchase!")},collectPurchasedProducts:function(){return productBatchList=[],$("tr.selected-product-row").each(function(){if(!$(this).hasClass(inventoryUtils.batchSelectRowClass)){var t=inventoryUtils.getClassWithPrefix(this,inventoryUtils.rowPrefix);if(null!=t){var e=$(this).next("tr.product-batch-details-row"),a=t.split(inventoryUtils.delimiter),o=a[1].trim(),n=null,r=null,i=null,l=null,c=null;""==o&&(o=null,n=$(this).find("input.new-product-name").val().trim(),r=e.find(".product-category").find(":selected").val().trim(),i=e.find(".product-brand").find(":selected").val().trim(),l=e.find(".product-description").val().trim(),c=e.find(".batch-description").val().trim());var d=a[2],s=null,u=null;""==d.trim()?u=$(this).find("input.purchase-product-batch-name").val().trim():s=d;var p=parseFloat($(this).find("input.product-quantity").val()),v=parseFloat($(this).find("input.purchase-price").val()),h=0;""!=$(this).find("input.purchase-discount").val()&&(h=parseFloat($(this).find("input.purchase-discount").val()));var m=0;""!=$(this).find("input.purchase-product-tax").val()&&(m=parseFloat($(this).find("input.purchase-product-tax").val()));$(this).find("input.purchase-item-total").val();var f=p*v,y=f*h/100,b=(f-y)*m/100,g=e.find(".product-batch-selling-price").val().trim();g=""==g?null:parseFloat(g);var P=e.find(".product-batch-discount").val().trim();P=""==P?null:parseFloat(P);var S=e.find(".discount-in-percent").is(":checked"),w=e.find(".product-batch-mrp").val().trim();w=""==w?null:parseFloat(w),productBatchList.push({skuId:o,productName:n,categoryId:r,brandId:i,batchId:s,batchName:u,quantity:p,totalPrice:f,totalDiscount:y,totalTax:b,sellingPrice:g,saleDiscount:P,saleDiscountInPercent:S,mrp:w,purchasePrice:v,productDescription:l,productBatchDescription:c})}}}),productBatchList}},inventorySale={dataCache:{},saleType:"SALE",returnType:"RETURN",saleReturnType:"SALES_RETURN",loadNewSaleMenu:function(t){ajaxClient.get("/inventory/new-sale-home/"+t,function(e){$("#main-content").html(e);var a=readJson("#inventory-preferences-json"),o=readJson("#student-wallet-preferences-json");inventorySale.dataCache={transactionType:t,inventoryPreferences:a,studentWalletPreferences:o},t==inventorySale.returnType&&(inventorySale.dataCache.customerType="SELLER"),inventorySale.init()})},loadNewSaleForm:function(){var t=inventorySale.dataCache.transactionType;ajaxClient.get("/inventory/new-sale-form/"+t,function(e){$("#sales-order-body").html(e),inventorySale.dataCache={transactionType:inventorySale.dataCache.transactionType,inventoryPreferences:inventorySale.dataCache.inventoryPreferences,studentWalletPreferences:inventorySale.dataCache.studentWalletPreferences},t==inventorySale.returnType&&(inventorySale.dataCache.customerType="SELLER"),inventorySale.init()})},init:function(){initDate(pastTransactionDays),liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId,inventoryUtils.liveSearchProductResultElementId,inventorySale.doneSaleProductTyping),liveSearchHandler.bindEvent("#sales\\.search-product-group","#sales\\.search-product-result",inventorySale.doneProductGroupSaleSearchTyping),inventorySale.bindStudentSaleEvents(),inventorySale.showProductGroupOption(),$("#additional-cost-input").focus(function(){}).blur(function(){inventorySale.computeNetSaleAmount()}),$("#additional-discount-input").focus(function(){}).blur(function(){inventorySale.computeNetSaleAmount()}),initUpdateDateInId("sale-date",$("#sale-global-date").val()),$("#sale-global-date").focus(function(){}).blur(function(){const t=$(this).val();initUpdateDateInId("sale-date",t)})},doneProductGroupSaleSearchTyping:function(t){var e=$("#sales\\.search-product-group").val();inventorySale.searchProductGroupEvent(e,t,inventorySale.bindClickEventOnSaleGroupProductSearch)},searchProductGroupEvent:function(t,e,a){ajaxClient.get("/inventory/live-product-group-search?text="+t,function(t){$(e).html(t),a()})},bindClickEventOnSaleGroupProductSearch:function(){$("#live-search-product-group-results tbody tr th").on("click",function(){var t=$(this).parent().find("td.product-group-json").text().trim(),e=JSON.parse(t),a=e.productGroupBasicInfo.groupId,o=e.productGroupBasicInfo.groupName,n=e.availableQuantity,r=e.totalSellingPrice,i=e.discountedTotalSellingPrice,l=e.productGroupBasicInfo.discount;if(0==$("#"+a+".sale-product-group-item").length){$("#sales\\.search-group-row").before(' <tr class="sale-product-group-item" id="'+a+'"> <td class="sale-product-group-name" scope="row" style = "cursor: pointer;"> '+o+' <p class="product-group-json-row" style="display: none;"> </p> </td> <td style="display:none"><input type="number" class="form-control form-control-sm group-sale-available-quantity" placeholder="Available Quanity"></td><td><input type="number" class="form-control form-control-sm group-sale-quantity mandatory-table-field positive-number unavailable-field-input" id="inputEmail4" placeholder="Quanity"> <p class="unavailable-field" style="display: none;"> <span style="color:#e65f76; ">Unavailable</span></p> </td><td><button type="button" class="btn btn-primary btn-sm get-product-group-batch-details">+Details</button></td><td><span class="badge badge-success" style="width:55%">Yes</span></td> <td><input type="number" class="form-control form-control-sm group-sale-price mandatory-table-field positive-number" placeholder="0" readonly > </td> <td><input type="number" class="form-control form-control-sm group-sale-discounted-price mandatory-table-field positive-number" placeholder="0" readonly > </td> <td><input type="number" class="form-control form-control-sm group-sale-amount" placeholder="0" readonly></td> <td><input type="number" class="form-control form-control-sm group-sale-discount percentage-range" placeholder="Discount"></td> <td><input type="number" class="form-control form-control-sm group-sale-total" placeholder="0" readonly></td> <td> <button type="button" class="close delete-sale-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr> ');for(var c=e.productDataQuantityList||[],d="",s=0;s<c.length;s++){var u=c[s],p=u.productGroupBatchDataQuantities||[];if(p.length>0)for(var v=0;v<p.length;v++){var h=p[v];d+=inventoryUtils.getProductRow(!0,h,u.productMetadata.skuId,u.productMetadata.name,h.productBatchData.batchId,h.productBatchData.batchName,!0,inventorySale.getRowColumnsGroup,!0,inventorySale.getRowDetailsGroup,h.quantity,"selected-product-group-row")}}$("#"+a).after('<tr class="product-group-batch-details-row" id="'+a+'"_product> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-group-batch-details-container "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> <th scope="col" class="w-25">Product Name*</th> <th scope="col">Batch*</th> <th scope="col"></th> <th scope="col">Quantity*</th> <th scope="col">Price*</th> <th scope="col">Amount</th> <th scope="col">Discount(%)</th> <th scope="col">Tax(%)</th> <th scope="col">Total (INR)</th> </tr> </thead> <tbody class='+a+"_table>"+d+"</tbody> </table> </div> </div> </td> </tr>");n=parseFloat(n),r=parseFloat(r),i=parseFloat(i);$("#"+a).find(".group-sale-available-quantity").first().val(n),$("#"+a).find(".group-sale-quantity").first().val(1),$("#"+a).find(".group-sale-price").first().val(r),$("#"+a).find(".group-sale-discounted-price").first().val(i),$("#"+a).find(".group-sale-discount").first().val(l),$("#"+a).find(".group-sale-tax").first().val(0),$("#"+a).find("p.product-group-json-row").text(t),$("#sales\\.search-product-group").val(""),$("#sales\\.search-product-result").html(""),inventorySale.deleteSalesEntry(),inventorySale.bindGroupSalesCostComputeEvent(),inventoryUtils.checkProductTransactionAvailability($("#"+a),".group-sale-quantity",".group-sale-available-quantity"),inventorySale.computeGroupSaleProductTotal($("#"+a)),inventoryUtils.bindRemoveErrorDisplayEvent(),inventoryUtils.productGroupInformation("sale-product-group-name"),inventorySale.expandProductGroupDetails(),inventorySale.bindSaleNewRowEvent("group"),inventorySale.deleteProductEntry(),inventoryUtils.bindExpandBatchButton()}else showErrorDialogBox("Product Group : "+o+" already selected. Please add quantity in that. ")})},deleteProductEntry:function(){$(".delete-product-batch-group-row").off("click").on("click",function(){var t=$(this).parent().parent(),e=inventoryUtils.getClassWithPrefix(t,inventoryUtils.rowPrefix);if(null!=e){var a=$(this).parent().parent().parent().attr("class"),o=e.split(inventoryUtils.delimiter)[1],n=(e.split(inventoryUtils.delimiter)[2],a.split("_")[0]);console.log("entered to delete"),t.next("tr.product-batch-details-row").remove(),t.remove(),inventorySale.computeNetSaleAmount($("#"+n),n),0==$(".selected-product-group-row."+o).length&&$(".selected-product-group-row."+o).remove(),0===$("tbody."+a+" tr").length&&($("tr.sale-product-group-item#"+n).remove(),$("tr.product-group-batch-details-row#"+n).remove()),inventoryUtils.resetAddNewProductBatchButtonState(o)}})},expandProductGroupDetails:function(){$(".get-product-group-batch-details").off("click").on("click",function(){$(this).closest("tr").next("tr").find(".product-group-batch-details-container").collapse("toggle")})},getRowColumnsGroup:function(t,e,a,o,n,r,i){var l=0,c=0;if(inventorySale.dataCache.transactionType==inventorySale.returnType)l=null!=e&&null!=e.productBatchData.purchasePrice?e.productBatch.purchasePrice:0,c=0;else if(null!=e){l=e.productBatchData.sellingPrice;var d=e.discount;null!=d&&(c=d)}return['<td scope="row"> '+a+" </td>",'<td style="width:15%;">'+o+"</td>",'<td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity" value="'+i+'" '+n+"> </td> ",' <td><input type="number" class="form-control form-control-sm sale-price mandatory-table-field positive-number" placeholder="0" value="'+l+'" readonly></td> ',' <td><input type="number" class="form-control form-control-sm sale-item-amount" placeholder="0" value="'+l*i+'" readonly></td> ','<td><input type="number" class="form-control form-control-sm sale-discount percentage-range" placeholder="Discount" value="'+c+'" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm purchase-product-tax percentage-range" placeholder="Tax" readonly> </td> ','<td><input type="number" class="form-control form-control-sm sale-item-total" placeholder="0" value="'+inventorySale.computeProductGroupTotal(i,l,c,0)+'" readonly></td>']},getRowDetailsGroup:function(t,e){var a="",o="",n="",r="";return null!=e&&(a=e.productBatchData.mrp,o=e.productBatchData.purchasePrice,n=e.productBatchData.sellingPrice,null==(r=e.productBatchData.description)&&(r="")),'<tr class="product-batch-details-row"> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-batch-details-container "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> <th scope="col">MRP</th> <th scope="col">Purchase Price</th> <th scope="col">Selling Price</th> <th scope="col">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type="number" class="form-control form-control-sm product-batch-mrp"  placeholder="0" value="'+a+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-purchase-price"  placeholder="0" value="'+o+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-sale-price"  placeholder="0" value="'+n+'" readonly></td> <td><textarea class="form-control batch-description" rows="1" placeholder="Batch Description" readonly >'+r+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>"},computeProductGroupTotal:function(t,e,a,o){var n=t*e;return n+n*o/100-(n+n*o/100)*a/100},deleteSalesEntry:function(){$(".delete-sale-row").click(function(){var t=$(this).closest("tr.sale-product-group-item");if(t.length){var e=t.attr("id");t.remove(),$("tr.product-group-batch-details-row#"+e).remove(),inventorySale.computeNetSaleAmount()}})},bindGroupSalesCostComputeEvent:function(){$(".group-sale-quantity").focus(function(){}).blur(function(){inventoryUtils.checkProductTransactionAvailability($(this).parent().parent(),".group-sale-quantity",".group-sale-available-quantity"),inventorySale.computeGroupSaleProductTotal($(this).parent().parent())}),$(".group-sale-discount").focus(function(){}).blur(function(){inventorySale.computeGroupSaleProductTotal($(this).parent().parent())}),$(".group-sale-tax").focus(function(){}).blur(function(){inventorySale.computeGroupSaleProductTotal($(this).parent().parent())})},computeGroupSaleProductTotal:function(t){var e=$(t).find(".group-sale-quantity").first().val(),a=0;e&&(a=parseFloat(e));var o=$(t).find(".group-sale-price").first().val(),n=0;o&&(n=parseFloat(o));var r=$(t).find(".group-sale-discounted-price").first().val(),i=0;r&&(i=parseFloat(r));var l=parseFloat($(t).first().find(".group-sale-discount").first().val()),c=0;l&&(c=parseFloat(l));var d=parseFloat($(t).first().find(".group-sale-tax").first().val()),s=0;d&&(s=parseFloat(d));var u=i*a,p=u+n*a*s/100-u*c/100;$(t).find(".group-sale-amount").first().val(u.toFixed(2)),$(t).find(".group-sale-total").first().val(p.toFixed(2)),inventorySale.computeNetSaleAmount()},doneSaleProductTyping:function(t){var e=$(inventoryUtils.liveSearchProductElementId).val();ajaxClient.get("/inventory/live-stock-search?text="+e,function(a){$(t).html(a),inventoryUtils.bindClickEventOnSearchProduct(e,inventorySale.dataCache,inventorySale.getBatchSelectColumn,inventorySale.getRowColumns,!0,inventorySale.getRowDetails,inventorySale.bindSaleNewRowEvent,!1,inventorySale.onDelete)})},getBatchSelectColumn:function(t){return'<select class="custom-select product-batch-select">'+t+"</select>"},onDelete:function(){inventorySale.computeNetSaleAmount()},bindSaleNewRowEvent:function(t=null){inventorySale.bindCostComputeEvent(t)},getRowColumns:function(t,e,a,o,n,r,i){var l=0,c=0;if(inventorySale.dataCache.transactionType==inventorySale.returnType)l=null!=e&&null!=e.productBatch.purchasePrice?e.productBatch.purchasePrice:0,c=0;else if(null!=e){l=e.productBatch.sellingPrice;var d=e.productBatch.discount,s=e.productBatch.discountInPercent;null!=d&&(s?c=d:null!=l&&l>0&&(c=100*d/l))}return['<td scope="row"> '+a+" </td>",'<td style="width:15%;">'+o+"</td>",'<td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity" '+n+"> </td> ",' <td><input type="number" class="form-control form-control-sm sale-price mandatory-table-field positive-number" placeholder="0" value="'+l+'" '+n+"></td> ",' <td><input type="number" class="form-control form-control-sm sale-item-amount" placeholder="0" readonly></td> ','<td><input type="number" class="form-control form-control-sm sale-discount percentage-range" placeholder="Discount" value="'+c+'" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm purchase-product-tax percentage-range" placeholder="Tax" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm sale-item-total" placeholder="0" readonly></td>']},getRowDetails:function(t,e){var a="",o="",n="",r="";return null!=e&&(a=e.productBatch.mrp,o=e.productBatch.purchasePrice,n=e.productBatch.sellingPrice,null==(r=e.productBatch.description)&&(r="")),'<tr class="product-batch-details-row"> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-batch-details-container "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> <th scope="col">MRP</th> <th scope="col">Purchase Price</th> <th scope="col">Selling Price</th> <th scope="col">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type="number" class="form-control form-control-sm product-batch-mrp"  placeholder="0" value="'+a+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-purchase-price"  placeholder="0" value="'+o+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-sale-price"  placeholder="0" value="'+n+'" readonly></td> <td><textarea class="form-control batch-description" rows="1" placeholder="Batch Description" readonly >'+r+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>"},bindCostComputeEvent:function(t){$(".product-quantity").focus(function(){}).blur(function(){var e=null,a=null;null!=t&&(a=$(this).parent().parent().parent().attr("class").split("_")[0],e=$("#"+a));inventorySale.computeTotal($(this).parent().parent(),e,a)}),$(".sale-price").focus(function(){}).blur(function(){var e=null;if(null!=t){var a=$(this).parent().parent().parent().attr("class").split("_")[0];e=$("#"+a)}inventorySale.computeTotal($(this).parent().parent(),e,a)}),$(".sale-discount").focus(function(){}).blur(function(){var e=null;if(null!=t){var a=$(this).parent().parent().parent().attr("class").split("_")[0];e=$("#"+a)}inventorySale.computeTotal($(this).parent().parent(),e,a)}),$(".purchase-product-tax").focus(function(){}).blur(function(){var e=null;if(null!=t){var a=$(this).parent().parent().parent().attr("class").split("_")[0];e=$("#"+a)}inventorySale.computeTotal($(this).parent().parent(),e,a)})},computeTotal:function(t,e=null,a=null){var o=$(t).find(".product-quantity").first().val(),n=0;o&&(n=parseFloat(o));var r=$(t).find(".sale-price").first().val(),i=0;r&&(i=parseFloat(r));var l=parseFloat($(t).first().find(".sale-discount").first().val()),c=0;l&&(c=parseFloat(l));var d=parseFloat($(t).first().find(".purchase-product-tax").first().val()),s=0;d&&(s=parseFloat(d));var u=n*i,p=u+u*s/100-(u+u*s/100)*c/100;$(t).find(".sale-item-amount").first().val(u.toFixed(2)),$(t).find(".sale-item-total").first().val(p.toFixed(2)),inventorySale.computeNetSaleAmount(e,a)},computeNetSaleAmount:function(t=null,e=null){var a=0,o=0,n=0;if($("tr.selected-product-row").each(function(){var t=$(this).find("input.product-quantity").val();""==t&&(t=0);var e=$(this).find("input.sale-price").val();""==e&&(e=0);var r=$(this).find("input.sale-discount").val();""==r&&(r=0);var i=$(this).find("input.purchase-product-tax").val();""!=i&&void 0!==i||(i=0);var l=parseFloat(e)*parseFloat(t),c=l*parseFloat(r)/100,d=(l-c)*parseFloat(i)/100;a+=c,o+=d,n+=l}),null!=t){var r=0,i=0;$("tr.selected-product-group-row").each(function(){if(e===$(this).parent().attr("class").split("_")[0]){console.log("entered");var t=$(this).find("input.product-quantity").val();""==t&&(t=0);var a=$(this).find("input.sale-price").val();""==a&&(a=0);var o=$(this).find("input.sale-discount").val();""==o&&(o=0);var n=$(this).find("input.purchase-product-tax").val();""!=n&&void 0!==n||(n=0);var l=parseFloat(a)*parseFloat(t),c=l*parseFloat(o)/100,d=(l-c)*parseFloat(n)/100;r+=c,d,i+=l}});var l=i-r;return $(t).find(".group-sale-price").first().val(i),$(t).find(".group-sale-discounted-price").first().val(l),void inventorySale.computeGroupSaleProductTotal(t)}!inventorySale.isReturnTransaction()&&$("#sell-product-group-check").is(":checked")&&$("tr.sale-product-group-item").each(function(){var t=$(this).find("input.group-sale-quantity").val();""==t&&(t="0");var e=$(this).find("input.group-sale-price").val(),r=$(this).find("input.group-sale-discounted-price").val(),i=$(this).find("input.group-sale-discount").val();""==i&&(i="0");var l=parseFloat(e)*parseFloat(t),c=parseFloat(r)*parseFloat(t),d=l-c+c*parseFloat(i)/100,s=l*parseFloat("0")/100;a+=d,o+=s,n+=l});var c=$("#additional-cost-input").val(),d=0;""!=c&&(d=parseFloat(c));var s=$("#additional-discount-input").val(),u=0;""!=s&&(u=parseFloat(s));var p=0;if($("#net-returned-product-amount").length>0){var v=$("#net-returned-product-amount").text();""!=v&&(p=parseFloat(v))}return $("#sub-total").text(n.toFixed(2)),$("#total-discount").text(a.toFixed(2)),$("#total-tax").text(o.toFixed(2)),$("#additional-cost").text(d.toFixed(2)),$("#additional-discount").text(u.toFixed(2)),$("#net-total").text((n+d-a-u+o-p).toFixed(2)),n+d-a-u+o-p},showProductGroupOption:function(t){$(".show-product-group-check").change(function(){$(this).is(":checked")?$(".product-group-transation-area").attr("style","display:block"):$(".product-group-transation-area").attr("style","display:none")})},userTypeSelectionAction:function(t,e=!1){inventorySale.dataCache.customerType=t,$(".customer-name-input").attr("style","display:none"),$(".sale-confirm-modal-content").attr("style","display:none"),$("#sale-payment-mode").val(""),"STUDENT"==inventorySale.getInventoryUserType()?(e||$("#inventory-student-search-modal").modal("toggle"),$("#student-name-input-div").attr("style","display:block"),inventorySale.dataCache.inventoryPreferences.allowWalletUsage&&$(".sale-confirm-modal-content#sale-confirm-modal-student-wallet-usage-section").attr("style","display:block")):(inventorySale.dataCache.selectedStudent=null,$("#customer-name-input-div").attr("style","display:block"),$("#student-wallet-amount-display-div").attr("style","display:none"),$("#student-wallet-amount-display").html(""),$(".sale-confirm-modal-content#sale-confirm-modal-without-wallet-usage").attr("style","display:block"))},bindStudentSaleEvents:function(){var t="#inventory-student-search-result";$("select#customer-type").off("change").on("change",function(){inventorySale.userTypeSelectionAction($(this).val())}),$("#edit-sutdent-entry").off("click").on("click",function(){"STUDENT"==inventorySale.getInventoryUserType()&&$("#inventory-student-search-modal").modal("toggle")}),$("#confirm-modal-use-wallet-amount-check").off("click").on("click",function(){if(null!=inventorySale.dataCache.selectedStudent){var t=getFloatValue($("#net-total").text());if($(this).is(":checked")){$("#confirm-modal-used-wallet-amount").parent().attr("style","display:block");var e=!1;inventorySale.dataCache.inventoryPreferences.allowWalletBasedCredit&&null!=inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit&&($("#confirm-modal-credit-amount").parent().attr("style","display:block"),$("#credit-amount-label").text("Credit Amount ("+inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit+")"),e=!0);var a=inventorySale.dataCache.selectedStudent.walletAmount<0?0:inventorySale.dataCache.selectedStudent.walletAmount;t<a?($("#confirm-modal-used-wallet-amount").val(t),$("#confirm-modal-paid-amount").val(0),$("#confirm-modal-paid-amount").prop("readonly",!0),$("#confirm-modal-credit-amount").val(0),$("#confirm-modal-credit-amount").prop("readonly",!0)):($("#confirm-modal-used-wallet-amount").val(a),e?($("#confirm-modal-paid-amount").val(0),$("#confirm-modal-paid-amount").prop("readonly",!1),$("#confirm-modal-credit-amount").val(t-a),$("#confirm-modal-credit-amount").prop("readonly",!1)):($("#confirm-modal-paid-amount").val(t-a),$("#confirm-modal-paid-amount").prop("readonly",!0),$("#confirm-modal-credit-amount").val(0),$("#confirm-modal-credit-amount").prop("readonly",!0)))}else $("#confirm-modal-used-wallet-amount").parent().attr("style","display:none"),$("#confirm-modal-credit-amount").parent().attr("style","display:none"),$("#confirm-modal-paid-amount").val(t),$("#confirm-modal-paid-amount").prop("readonly",!0)}}),$("#inventory-student-search-text").on("keyup",function(e){13==e.keyCode&&inventorySale.doneStudentSearchTyping(t)}),liveSearchHandler.bindEvent("#inventory-student-search-text",t,inventorySale.doneStudentSearchTyping)},doneStudentSearchTyping:function(t){var e=$("#inventory-student-search-text").val().trim();ajaxClient.get("/inventory/student-live-search?searchText="+e+"&statusList=ENROLLED",function(e){$(t).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(t,inventorySale.inventoryStudentSearchClickAction)})},getInventoryUserType:function(){var t=inventorySale.dataCache.customerType;return null==t||""==t.trim()?"OTHER":t},inventoryStudentSearchClickAction:function(t,e=!1){inventorySale.dataCache.selectedStudentId=t;var a=inventorySale.dataCache.inventoryPreferences.allowWalletUsage;ajaxClient.get("/inventory/latest-session-student/"+t+"?includeWalletAmount="+a,function(t){$("#selected-student-data").html(t);var o=readJson("#student-data-entry-json");inventorySale.dataCache.selectedStudent=o;var n=!1;o.studentTaggedDetailsList&&o.studentTaggedDetailsList.length>0&&o.studentTaggedDetailsList.forEach(t=>{"INVENTORY_SALE_RESTRICTED"===t.taggedActions&&(n=!0)});var r=o.studentBasicInfo.name+" ("+o.studentBasicInfo.admissionNumber+")";e||$("#inventory-student-search-modal").modal("toggle"),$("#edit-sutdent-entry-name").val(r),n?($('button[data-target="#new-sale-confirm-modal"]').attr("disabled",!0),$("#inventory-sale-tag").text("Inventory Sale Restricted")):($('button[data-target="#new-sale-confirm-modal"]').attr("disabled",!1),$("#inventory-sale-tag").text("")),a&&($("#student-wallet-amount-display-div").attr("style","display:block"),$("#student-wallet-amount-display").html(o.walletAmount),$("#confirm-modal-wallet-amount").text(o.walletAmount))})},updateConfirmModal:function(){var t=inventorySale.getInventoryUserType(),e=getFloatValue($("#net-total").text());$("#confirm-modal-total-sale-payble-amount").text(e),inventorySale.saleOrderConfirmModalResetState(),$("#confirm-modal-paid-amount").val(e),"STUDENT"==t&&inventorySale.dataCache.inventoryPreferences.allowWalletUsage?($("#sale-confirm-modal-student-wallet-usage-section").attr("style","display:block;"),$("#sale-confirm-modal-without-wallet-usage").attr("style","display:none;"),e<=0?($("#with-wallet-return-amount-container").attr("style","display:block;"),$("#with-wallet-collect-amount-container").attr("style","display:none;")):($("#with-wallet-return-amount-container").attr("style","display:none;"),$("#with-wallet-collect-amount-container").attr("style","display:block;"))):($("#sale-confirm-modal-student-wallet-usage-section").attr("style","display:none;"),$("#sale-confirm-modal-without-wallet-usage").attr("style","display:block;"),e<=0?($("#without-wallet-return-payment-mode-selection").attr("style","display:block;"),$("#without-wallet-collect-payment-mode-selection").attr("style","display:none;")):($("#without-wallet-return-payment-mode-selection").attr("style","display:none;"),$("#without-wallet-collect-payment-mode-selection").attr("style","display:block;")))},saleOrderConfirmModalResetState:function(){var t=getFloatValue($("#net-total").text());$("#confirm-modal-use-wallet-amount-check").prop("checked",!1),$("#confirm-modal-used-wallet-amount").parent().attr("style","display:none"),$("#confirm-modal-credit-amount").parent().attr("style","display:none"),$("#confirm-modal-paid-amount").val(t),$("#confirm-modal-paid-amount").prop("readonly",!0)},toggleNewSaleConfirmModal:function(){$("#new-sale-confirm-modal").modal("toggle")},isReturnTransaction:function(){return inventorySale.dataCache.transactionType==inventorySale.returnType},addNewSale:function(){$("#new-sale-confirm-modal").modal("toggle");var t=inventorySale.collectSelectedProducts(),e=[];if(0!=t.length||0!=e.length){var a,o=inventorySale.getInventoryUserType(),n=null;if(inventorySale.isReturnTransaction()){if(null==(n=$("#return-supplier option:selected").val())||""==n.trim())return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Select supplier")}else if(null==(n="STUDENT"==o?inventorySale.dataCache.selectedStudentId:$("#sale-customer-name").val())||""==n.trim())return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Invalid Customer");if((a=inventorySale.computeNetSaleAmount())<0)return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Net Payable amount can't be less than 0");if((a=inventorySale.computeNetSaleAmount())<0)return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Net payable amount can't be less than 0");var r=inventorySale.getNewSalePayload(n,t,e,a);console.log(r),!r.useWallet||null!=r.paidAmount&&0!=r.paidAmount||(r.transactionMode="WALLET"),void 0!==r.transactionMode&&null!==r.transactionMode&&""!==r.transactionMode?ajaxClient.post("/inventory/sale-transaction",{newSalePayload:JSON.stringify(r)},function(t){$("#sale-status-modal-container").html(t),$("#sale-status-modal").modal({backdrop:"static",keyboard:!1})}):alert("Please select a payment mode!")}else showErrorDialogBox("Please select atleast one product or product group for sale!")},getNewSalePayload:function(t,e,a,o){var n=inventorySale.getInventoryUserType(),r=null;r="SALE"==inventorySale.dataCache.transactionType?"STUDENT"==n&&inventorySale.dataCache.inventoryPreferences.allowWalletUsage?o<=0?$("#with-wallet-return-payment-mode-selection").find(".sale-payment-mode option:selected").val():$("#with-wallet-collect-payment-mode-selection").find(".sale-payment-mode option:selected").val():o<=0?$("#without-wallet-return-payment-mode-selection").find(".sale-payment-mode option:selected").val():$("#without-wallet-collect-payment-mode-selection").find(".sale-payment-mode option:selected").val():$("#sale-payment-mode option:selected").val();var i=$("#sale-customer-email").val(),l=$("#sale-description").val(),c=getDate($("#sale-date").val()),d=$("#sale-reference-number").val(),s=$("#additional-cost-input").val(),u=0;""!=s&&(u=parseFloat(s));var p=$("#additional-discount-input").val(),v=0;""!=p&&(v=parseFloat(p));var h=!1,m=0,f=0,y=getFloatValue($("#net-total").text());if("STUDENT"==n&&inventorySale.dataCache.inventoryPreferences.allowWalletUsage&&$("#confirm-modal-use-wallet-amount-check").is(":checked")&&(h=!0,m=getFloatValue($("#confirm-modal-used-wallet-amount").val()),y=getFloatValue($("#confirm-modal-paid-amount").val()),inventorySale.dataCache.inventoryPreferences.allowWalletBasedCredit&&null!=inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit&&(f=getFloatValue($("#confirm-modal-credit-amount").val()))>0)){if(inventorySale.dataCache.selectedStudent.walletAmount-m>0)return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Use available wallet amount instead of giving credit amount");if(inventorySale.dataCache.selectedStudent.walletAmount-m-f<-inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit)return inventorySale.toggleNewSaleConfirmModal(),void showErrorDialogBox("Cannot give more credit then allowed max limit")}return{inventoryUserType:n,inventoryTransactionType:inventorySale.dataCache.transactionType,transactionMode:r,purchasedBy:t,reference:d,email:i,transactionDate:c.getTime(),description:l,additionalCost:u,additionalDiscount:v,useWallet:h,usedWalletAmount:m,walletCreditAmount:f,paidAmount:y,saleProductPayloadList:e,saleProductGroupPayloadList:a}},collectProductGroupSalesOrderFormValues:function(){return productGroups=[],inventorySale.isReturnTransaction()||!$("#sell-product-group-check").is(":checked")?productGroups:($("tr.sale-product-group-item").each(function(){var t=$(this).attr("id"),e=$(this).find("input.group-sale-quantity").val(),a=$(this).find("input.group-sale-price").val(),o=$(this).find("input.group-sale-discounted-price").val(),n=$(this).find("input.group-sale-discount").val(),r={},i=parseFloat(o)*parseFloat(e),l=parseFloat(a)*parseFloat(e),c=i*parseFloat(n)/100,d=l*parseFloat("0")/100;r.groupId=t,r.quantity=e,r.totalPrice=i,r.totalDiscount=c,r.totalTax=d,productGroups.push(r)}),productGroups)},collectSelectedProducts:function(){return productBatchList=[],$("tr.selected-product-row").each(function(){if(!$(this).hasClass(inventoryUtils.batchSelectRowClass)){var t=inventoryUtils.getClassWithPrefix(this,inventoryUtils.rowPrefix);if(null!=t){var e=t.split(inventoryUtils.delimiter),a=e[1].trim();if(""!=a){var o=e[2].trim();if(""!=o){var n=parseFloat($(this).find("input.product-quantity").val()),r=parseFloat($(this).find("input.sale-price").val()),i=0;""!=$(this).find("input.sale-discount").val()&&(i=parseFloat($(this).find("input.sale-discount").val()));var l=0;""!=$(this).find("input.purchase-product-tax").val()&&(l=parseFloat($(this).find("input.purchase-product-tax").val()));$(this).find("input.sale-item-total").val();var c=n*r,d=c*i/100,s=(c-d)*l/100;productBatchList.push({skuId:a,batchId:o,quantity:n,totalPrice:c,totalDiscount:d,totalTax:s})}}}}}),$("tr.selected-product-group-row").each(function(){if(!$(this).hasClass(inventoryUtils.batchSelectRowClass)){var t=$(this).parent().attr("class").split("_")[0],e=inventoryUtils.getClassWithPrefix(this,inventoryUtils.rowPrefix);if(null!=e){var a=e.split(inventoryUtils.delimiter),o=a[1].trim();if(""!=o){var n=a[2].trim();if(""!=n){var r=parseFloat($(this).find("input.product-quantity").val()),i=parseFloat($(this).find("input.sale-price").val()),l=0;""!=$(this).find("input.sale-discount").val()&&(l=parseFloat($(this).find("input.sale-discount").val()));var c=0;""!=$(this).find("input.purchase-product-tax").val()&&(c=parseFloat($(this).find("input.purchase-product-tax").val()));$(this).find("input.sale-item-total").val();var d=r*i,s=d*l/100,u=(d-s)*c/100;""!=$("#"+t).find(".group-sale-discount").val()&&(s+=$("#"+t).find(".group-sale-discount").val()/100*(d-s)),productBatchList.push({skuId:o,batchId:n,quantity:r,totalPrice:d,totalDiscount:s,totalTax:u})}}}}}),productBatchList}},inventoryIssueProduct={dataCache:{},loadIssueProductMenu:function(){ajaxClient.get("/inventory/issue-product-home",function(t){$("#main-content").html(t),inventoryIssueProduct.init()})},loadIssueProductForm:function(){ajaxClient.get("/inventory/issue-product-form",function(t){$("#issue-order-body").html(t),inventoryIssueProduct.init()})},init:function(){inventoryIssueProduct.dataCache={},initDate(pastTransactionDays),liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId,inventoryUtils.liveSearchProductResultElementId,inventoryIssueProduct.doneIssueProductTyping)},doneIssueProductTyping:function(t){var e=$(inventoryUtils.liveSearchProductElementId).val();ajaxClient.get("/inventory/live-stock-search?text="+e,function(a){$(t).html(a),inventoryUtils.bindClickEventOnSearchProduct(e,inventoryIssueProduct.dataCache,inventoryIssueProduct.getBatchSelectColumn,inventoryIssueProduct.getRowColumns,!0,inventoryIssueProduct.getRowDetails,inventoryIssueProduct.bindIssueProductNewRowEvent,!1,inventoryIssueProduct.onDelete)})},getBatchSelectColumn:function(t){return'<select class="custom-select product-batch-select">'+t+"</select>"},onDelete:function(){inventoryIssueProduct.computeIssuedProducts()},bindIssueProductNewRowEvent:function(){inventoryIssueProduct.bindUnitComputeEvent()},getRowColumns:function(t,e,a,o,n,r,i){return['<td scope="row"> '+a+" </td>",'<td style="width:15%;">'+o+"</td>",'<td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity" '+n+"> </td> "]},getRowDetails:function(t,e){var a="",o="",n="";return null!=e&&(a=e.productBatch.mrp,o=e.productBatch.purchasePrice,null==(n=e.productBatch.description)&&(n="")),'<tr class="product-batch-details-row"> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-batch-details-container "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> <th scope="col">MRP</th> <th scope="col">Purchase Price</th> <th scope="col">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type="number" class="form-control form-control-sm product-batch-mrp"  placeholder="0" value="'+a+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-purchase-price"  placeholder="0" value="'+o+'" readonly></td> <td><textarea class="form-control batch-description" rows="1" placeholder="Batch Description" readonly >'+n+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>"},bindUnitComputeEvent:function(){$(".product-quantity").focus(function(){}).blur(function(){inventoryIssueProduct.computeIssuedProducts()})},computeIssuedProducts:function(){var t=0;return $("tr.selected-product-row").each(function(){var e=$(this).find("input.product-quantity").val(),a=0;""!=e&&(a=parseFloat(e)),t+=a}),$("#total-units").text(t),t},toggleConfirmModal:function(){$("#issue-confirmation-modal").modal("toggle")},issueProducts:function(){$("#issue-confirmation-modal").modal("toggle");var t=inventoryIssueProduct.collectSelectedProducts();if(0!=t.length){var e=$("#issue-person-name").val();if(null==e||""==e.trim())return inventoryIssueProduct.toggleConfirmModal(),void showErrorDialogBox("Invalid person to issue products");if(inventoryIssueProduct.computeIssuedProducts()<0)return inventoryIssueProduct.toggleConfirmModal(),void showErrorDialogBox("Total issued products can't be less than 0");var a=$("#issue-person-email").val(),o=$("#issue-description").val(),n=getDate($("#issue-date").val()),r={issuedTo:e,reference:$("#issue-reference-number").val(),email:a,transactionDate:n.getTime(),description:o,issueProductPayloadList:t};ajaxClient.post("/inventory/issue-transaction",{issueProductsPayload:JSON.stringify(r)},function(t){$("#issue-status-model-container").html(t),$("#issue-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Please select atleast one product to issue!")},collectSelectedProducts:function(){return productBatchList=[],$("tr.selected-product-row").each(function(){if(!$(this).hasClass(inventoryUtils.batchSelectRowClass)){var t=inventoryUtils.getClassWithPrefix(this,inventoryUtils.rowPrefix);if(null!=t){var e=t.split(inventoryUtils.delimiter),a=e[1].trim();if(""!=a){var o=e[2].trim();if(""!=o){var n=parseFloat($(this).find("input.product-quantity").val());productBatchList.push({skuId:a,batchId:o,quantity:n})}}}}}),productBatchList}},inventoryExchangeProduct={dataCache:{},loadExchangeProductMenu:function(){ajaxClient.get("/inventory/exchange-product-home",function(t){$("#main-content").html(t),inventoryExchangeProduct.init()})},init:function(){inventoryExchangeProduct.dataCache={},initDate(pastTransactionDays),inventoryExchangeProduct.bindInvoiceSearchEvents()},bindInvoiceSearchEvents:function(){$("#exchange-invoice-id").on("keypress",function(t){13==t.which&&inventoryExchangeProduct.loadExchangeInvoiceDetails()})},loadExchangeInvoiceForm:function(){ajaxClient.get("/inventory/exchange-invoice-form",function(t){$("#exchange-invoice-body-container").html(t),inventoryExchangeProduct.init()})},loadExchangeInvoiceDetails:function(){var t=$("#exchange-invoice-id").val();ajaxClient.get("/inventory/exchange-invoice-screen?invoiceId="+t,function(t){if($("#exchange-invoice-screen").html(t),0!=$("#invoice-data-json").length){inventoryExchangeProduct.init();var e=readJson("#invoice-data-json");inventoryExchangeProduct.dataCache.invoiceData=e,inventoryExchangeProduct.bindInvoiceProductCheckbox();var a=readJson("#inventory-preferences-json"),o=readJson("#student-wallet-preferences-json");inventorySale.dataCache={transactionType:"SALE",inventoryPreferences:a,studentWalletPreferences:o},inventorySale.init(),"STUDENT"==e.inventoryUserType&&(inventorySale.userTypeSelectionAction("STUDENT",!0),inventorySale.inventoryStudentSearchClickAction(e.studentLite.studentId,!0))}})},bindInvoiceProductCheckbox:function(){$(".exchange-product-select-checkbox").change(function(){$(this).is(":checked")?($(this).closest("tr").find(".exchange-product-select-qty").prop("disabled",!1),$(this).closest("tr").find(".exchange-product-select-qty").val(1)):($(this).closest("tr").find(".exchange-product-select-qty").prop("disabled",!0),$(this).closest("tr").find(".exchange-product-select-qty").val(0)),inventoryExchangeProduct.computeAndUpdateNetReturnedProductAmount()}),$(".exchange-product-select-qty").focus(function(){}).blur(function(){$(this).val()>$(this).closest("tr").find("product-net-qty").val()&&$(this).val(1),inventoryExchangeProduct.computeAndUpdateNetReturnedProductAmount()})},computeAndUpdateNetReturnedProductAmount:function(){var t=0;$(".exchange-product-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).closest("tr").find(".exchange-product-select-qty").val(),a=$(this).closest("tr").find(".product-net-qty").val(),o=$(this).closest("tr").find(".product-net-amount").val();t+=e==a?o:o*e/a}}),$("#net-returned-product-amount").text(t.toFixed(2)),inventorySale.computeNetSaleAmount()},getReturnProducPayload:function(){var t=[];return $(".exchange-product-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).closest("tr").find(".exchange-product-id").text().trim(),a=$(this).closest("tr").find(".exchange-product-batch-id").text().trim(),o=$(this).closest("tr").find(".exchange-product-select-qty").val();$(this).closest("tr").find(".product-net-qty").val(),$(this).closest("tr").find(".product-net-amount").val();t.push({skuId:e,batchId:a,selectedQty:o})}}),t},addExchangeTransaction:function(){$("#new-sale-confirm-modal").modal("toggle");var t=inventorySale.computeNetSaleAmount(),e=inventoryExchangeProduct.getReturnProducPayload();if(0!=e.length){var a=inventorySale.collectSelectedProducts(),o=[];if(inventorySale.isReturnTransaction()||(o=inventorySale.collectProductGroupSalesOrderFormValues()),0!=a.length||0!=o.length){var n=inventorySale.getNewSalePayload(null,a,o,t);if(void 0!==n.transactionMode&&null!==n.transactionMode&&""!==n.transactionMode){var r={returnOrderPayload:{returnTransactionId:inventoryExchangeProduct.dataCache.invoiceData.transactionId,returnedProductList:e},newTradePayload:n};ajaxClient.post("/inventory/exchange-transaction",{exchangePayload:JSON.stringify(r)},function(t){$("#exchange-status-model-container").html(t),$("#sale-status-modal").modal({backdrop:"static",keyboard:!1})})}else alert("Please select a payment mode!")}else showErrorDialogBox("Please select atleast one product or product group for sale!")}else showErrorDialogBox("Select atleast one product to return.")}},inventoryUtils={selectedProductsKey:"selectedProducts",selectedProductBatchesKey:"selectedProductBatches",batchSelectRowClass:"batch-select-row",rowPrefix:"rowproduct",delimiter:"_",liveSearchProductElementId:"#live-search-product",liveSearchProductResultElementId:"#live-search-product-result",liveSearchRowElementId:"#live-search-row",deleteProductBatchRowClass:"delete-product-batch-row",deleteProductBatchSelectRowClass:"delete-product-batch-select-row",addNewProductBatchButtonClass:"add-new-batch-button",getClassWithPrefix:function(t,e){var a=$(t).attr("class").split(/\s+/),o=null;return $.each(a,function(t,a){a.startsWith(e)&&(o=a)}),o},getSkuIdFromRow:function(t){var e=inventoryUtils.getClassWithPrefix(t,inventoryUtils.rowPrefix);if(null==e)return null;var a=e.split(inventoryUtils.delimiter)[1].trim();return""==a?null:a},removeErrorOnClick:function(){$(".unavailable-field-input").on("click",function(){$(this).css("border",""),$(this).parent().find("p.unavailable-field").attr("style","display:none")})},searchIndividualProductEvent:function(t,e,a){ajaxClient.get("/inventory/live-stock-search?text="+t,function(t){$(e).html(t),a()})},searchProductGroupEvent:function(t,e,a){ajaxClient.get("/inventory/live-product-group-search?text="+t,function(t){$(e).html(t),a()})},showProductGroupOption:function(t){$(".show-product-group-check").change(function(){t(),$(this).is(":checked")?$(".product-group-transation-area").attr("style","display:block"):$(".product-group-transation-area").attr("style","display:none")})},checkProductTransactionAvailability:function(t,e,a){var o=$(t).find(e).first().val(),n=0;o&&(n=parseFloat(o));var r=parseFloat($(t).first().find(a).first().val()),i=0;r&&(i=parseFloat(r));var l=$(t).find(".badge");l.removeClass(),l.addClass("badge"),i<n?(l.addClass("badge-danger"),l.text("No")):(l.addClass("badge-success"),l.text(i))},checkIfCurrentDateSelected:function(t){return!(getDayStart(getCurrentDate())>getDate($(t).parent().parent().parent().parent().find(".select-date").val()))},productGroupInformation:function(t){$("."+t).click(function(){inventoryProductGroup.showProductGroupListModel($(this).find("p.product-group-json-row"))})},validations:function(t){inventoryUtils.clearValidationErrorDisplay(),inventoryUtils.bindRemoveErrorDisplayEvent(),inventoryUtils.removeErrorOnClick();var e=!1;return $(t).find("input.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),e=!0)}),$(t).find("select.mandatory-field").each(function(){0!=$(this).find(":selected").length&&""!=$(this).find(":selected").val().trim()||($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),e=!0)}),$(t).find("input.mandatory-table-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-table-field-text"> <span style="color:#e65f76;">Mandatory</span></p>'),e=!0)}),$(t).find("input.percentage-range").each(function(){($(this).val()<0||$(this).val()>100)&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-text"> <span style="color:#e65f76;">Invalid</span></p>'),e=!0)}),$(t).find("input.positive-number").each(function(){$(this).val()<0&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-text"> <span style="color:#e65f76;">Invalid</span></p>'),e=!0)}),$(t).find("input.validate-email").each(function(){validateEmail($(this).val())||($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-email-text"> <span style="color:#e65f76;">Invalid Email</span></p>'),e=!0)}),e},clearValidationErrorDisplay:function(){$(".percentage-range").css("border",""),$(".positive-number").css("border",""),$(".invalid-input-text").remove(),$(".mandatory-field").css("border",""),$(".mandatory-field-text").remove(),$(".mandatory-table-field").css("border",""),$(".mandatory-table-field-text").remove(),$(".validate-email").css("border",""),$(".invalid-input-email-text").remove()},closeModal:function(){inventoryUtils.clearValidationErrorDisplay()},bindRemoveErrorDisplayEvent:function(){$(".percentage-range").focus(function(){$(this).css("border",""),$(this).next().hasClass("invalid-input-text")&&$(this).next().remove()}).blur(function(){}),$(".mandatory-field").focus(function(){$(this).css("border",""),$(this).next().hasClass("mandatory-field-text")&&$(this).next().remove()}).blur(function(){}),$(".mandatory-table-field").focus(function(){$(this).css("border",""),$(this).next().hasClass("mandatory-table-field-text")&&$(this).next().remove()}).blur(function(){}),$(".positive-number").focus(function(){$(this).css("border",""),$(this).next().hasClass("invalid-input-text")&&$(this).next().remove()}).blur(function(){}),$(".validate-email").focus(function(){$(this).css("border",""),$(this).next().hasClass("invalid-input-email-text")&&$(this).next().remove()}).blur(function(){})},bindClickEventOnSearchProduct:function(t,e,a,o,n,r,i,l,c){$("#live-search-stock-results tbody tr th").off("click").on("click",function(){if(!$(this).hasClass("skip-click")){if($(this).hasClass("add-new-product")){var d=inventoryUtils.getNewBatchInput();return $(inventoryUtils.liveSearchRowElementId).before(inventoryUtils.getProductRow(!0,null,"",t,"",d,!0,o,n,r)),$(inventoryUtils.liveSearchProductResultElementId).html(""),$(inventoryUtils.liveSearchProductElementId).val(""),void inventoryUtils.bindNewRowEvent(e,c,i)}inventoryUtils.selectedProductsKey in e||(e[inventoryUtils.selectedProductsKey]={}),inventoryUtils.selectedProductBatchesKey in e||(e[inventoryUtils.selectedProductBatchesKey]={});var s=$(this).parent().find("td.stock-json").text().trim(),u=JSON.parse(s),p=u.productMetadata.skuId,v=u.productMetadata.name,h=e[inventoryUtils.selectedProductsKey];for(productIdEntry in h){if(h[productIdEntry].productMetadata.skuId==p)return void showErrorDialogBox("Product :  "+v+" already selected. Please add quantity in that.")}if(l||null!=u.productBatchDataList&&0!=u.productBatchDataList.length){h[p]=u;var m=e[inventoryUtils.selectedProductBatchesKey],f='<option value="">select</option>',y=null;if(null!=u.productBatchDataList&&u.productBatchDataList.length>0){var b=0,g="selected";for(b=0;b<u.productBatchDataList.length;b++){var P=u.productBatchDataList[b],S=P.batchId;f+='<option value="'+S+'" '+g+">"+P.batchName+"</option>",m[S]={productMetadata:u.productMetadata,productBatch:P},0==b&&(y=S),g=""}}var w=a(f);$(inventoryUtils.liveSearchRowElementId).before(inventoryUtils.getProductRow(!1,null,p,v,"",w,!1,o,n,r)),$(inventoryUtils.liveSearchProductResultElementId).html(""),$(inventoryUtils.liveSearchProductElementId).val(""),inventoryUtils.bindProductBatchSelect(e,o,n,r,i,c,l);var x=inventoryUtils.getBatchSelectRowClassName(p);null!=y?inventoryUtils.productBatchSelectAction(e,x,y,o,n,r,i,c,l):null==y&&l&&inventoryUtils.purchaseAddProductBatchButtonAction(p,x,e,o,n,r,c,i),l&&inventoryUtils.bindPurchaseAddProductBatchButton(e,o,n,r,c,i)}else showErrorDialogBox("Product :  "+v+" has no active batch available. Please add a active batch before using it.")}})},bindProductBatchSelect:function(t,e,a,o,n,r,i){$("select.product-batch-select").on("change",function(){var l=this.value;inventoryUtils.productBatchSelectAction(t,this,l,e,a,o,n,r,i)})},getAddAnotherBatchTextElement:function(t){return t?'<a class="'+inventoryUtils.addNewProductBatchButtonClass+'" style="display:block;">Add Other Batch</a>':'<a class="'+inventoryUtils.addNewProductBatchButtonClass+'" style="display:none;">Add Other Batch</a>'},productBatchSelectAction:function(t,e,a,o,n,r,i,l,c){if(null!=a&&null!=a&&""!=a.trim()){var d=t[inventoryUtils.selectedProductBatchesKey][a],s=t[inventoryUtils.selectedProductsKey][d.productMetadata.skuId],u=d.productBatch.totalQuantity,p="",v="\n"+inventoryUtils.getAddAnotherBatchTextElement(!0);!c&&inventoryUtils.getSelectedBatchCount(d.productMetadata.skuId)+1>=s.productBatchDataList.length&&(v="\n"+inventoryUtils.getAddAnotherBatchTextElement(!1)),p=u>0?"<h5>"+d.productBatch.batchName+' &nbsp;<span class="badge badge-success">Available : '+d.productBatch.totalQuantity+"</span></h5>":"<h5>"+d.productBatch.batchName+' &nbsp;<span class="badge badge-danger">Not Available</span></h5>',p+=v,$(e).closest("tr").before(inventoryUtils.getProductRow(!1,d,d.productMetadata.skuId,d.productMetadata.name,d.productBatch.batchId,p,!0,o,n,r)),$("option:selected",e).remove(),inventoryUtils.bindNewRowEvent(t,l,i),inventoryUtils.bindDeleteProductBatchSelectEntry(),inventoryUtils.hideBatchSelectRow(d.productMetadata.skuId)}},getSelectedBatchCount:function(t){return $(".selected-product-row."+t).length},hideBatchSelectRow:function(t){var e=inventoryUtils.getBatchSelectRowClassName(t);$(e).css("display","none")},showBatchSelectRow:function(t){var e=inventoryUtils.getBatchSelectRowClassName(t);$(e).css("display","")},bindNewRowEvent:function(t,e,a){inventoryUtils.deleteProductEntry(t,e),inventoryUtils.bindExpandBatchButton(),inventoryUtils.bindShowAddNewBatchButton(),a()},bindExpandBatchButton:function(){$(".add-product-batch-details").off("click").on("click",function(){$(this).closest("tr").next("tr").find(".product-batch-details-container").collapse("toggle")})},bindShowAddNewBatchButton:function(){$("."+inventoryUtils.addNewProductBatchButtonClass).off("click").on("click",function(){var t=$(this).closest("tr"),e=inventoryUtils.getSkuIdFromRow(t);null!=e&&(inventoryUtils.showBatchSelectRow(e),inventoryUtils.hideAllAddNewProductBatchButtons(e))})},hideAllAddNewProductBatchButtons:function(t){$("."+t).find("."+inventoryUtils.addNewProductBatchButtonClass).css("display","none")},showAddNewProductBatchButtonOnLastBatch:function(t){var e=inventoryUtils.getBatchSelectRowClassName(t);$(e).parent().find(".selected-product-row."+t).last().find("."+inventoryUtils.addNewProductBatchButtonClass).css("display","block")},deleteProductEntry:function(t,e){$("."+inventoryUtils.deleteProductBatchRowClass).off("click").on("click",function(){var a="."+inventoryUtils.batchSelectRowClass,o=$(this).parent().parent(),n=inventoryUtils.getClassWithPrefix(o,inventoryUtils.rowPrefix);if(null!=n){var r=n.split(inventoryUtils.delimiter)[1],i=n.split(inventoryUtils.delimiter)[2];if(null!=i&&null!=i&&""!=i.trim()){var l=t[inventoryUtils.selectedProductBatchesKey][i];(0==o.next(a).length?o.nextUntil(a).last().next():o.next(a)).find("select.product-batch-select").append($("<option></option>").attr("value",i).text(l.productBatch.batchName))}o.next("tr.product-batch-details-row").remove(),o.remove(),0==$(".selected-product-row."+r).length&&($(".selected-product-row."+r).remove(),delete t[inventoryUtils.selectedProductsKey][r]),inventoryUtils.resetAddNewProductBatchButtonState(r),e()}})},bindDeleteProductBatchSelectEntry:function(){$("."+inventoryUtils.deleteProductBatchSelectRowClass).off("click").on("click",function(){var t=$(this).parent().parent(),e=inventoryUtils.getSkuIdFromRow(t);inventoryUtils.resetAddNewProductBatchButtonState(e)})},resetAddNewProductBatchButtonState:function(t){inventoryUtils.hideBatchSelectRow(t),inventoryUtils.hideAllAddNewProductBatchButtons(t),inventoryUtils.showAddNewProductBatchButtonOnLastBatch(t)},bindPurchaseAddProductBatchButton:function(t,e,a,o,n,r){$(".add-product-batch").off("click").on("click",function(){var i=$(this).parent().parent().parent().parent(),l=inventoryUtils.getClassWithPrefix(i,inventoryUtils.rowPrefix);if(null!=l){var c=l.split(inventoryUtils.delimiter)[1];null!=c&&null!=c&&""!=c.trim()&&inventoryUtils.purchaseAddProductBatchButtonAction(c,this,t,e,a,o,n,r)}})},purchaseAddProductBatchButtonAction:function(t,e,a,o,n,r,i,l){var c=a[inventoryUtils.selectedProductsKey][t],d=inventoryUtils.getNewBatchInput();d+="\n"+inventoryUtils.getAddAnotherBatchTextElement(!0),$(e).closest("tr").before(inventoryUtils.getProductRow(!1,null,c.productMetadata.skuId,c.productMetadata.name,"",d,!0,o,n,r)),inventoryUtils.bindNewRowEvent(a,i,l),inventoryUtils.hideBatchSelectRow(t)},getNewBatchInput:function(){return'<input type="text" class="form-control form-control-sm purchase-product-batch-name mandatory-table-field" placeholder="New batch name">'},getRowClassName:function(t,e){return inventoryUtils.rowPrefix+inventoryUtils.delimiter+t+inventoryUtils.delimiter+e},getBatchSelectRowClassName:function(t){return"."+inventoryUtils.batchSelectRowClass+"."+inventoryUtils.getRowClassName(t,"")},getProductRow:function(t,e,a,o,n,r,i,l,c,d,s=0,u="selected-product-row"){var p="",v="",h='<button type="button" class="close '+inventoryUtils.deleteProductBatchRowClass+' " aria-label="Close"> <span aria-hidden="true">&times;</span> </button>';"selected-product-group-row"===u&&(h='<button type="button" class="close delete-product-batch-group-row aria-label="Close"> <span aria-hidden="true">&times;</span> </button>');var m="",f="",y=u;i||(p="readonly",v="disabled",h='<button type="button" class="close '+inventoryUtils.deleteProductBatchSelectRowClass+' " aria-label="Close"> <span aria-hidden="true">&times;</span> </button>',"selected-product-group-row"===u&&(h='<button type="button" class="close delete-product-batch-group-row aria-label="Close"> <span aria-hidden="true">&times;</span> </button>'),m=inventoryUtils.batchSelectRowClass,f='style="display:none;"',y="");for(var b=inventoryUtils.getRowClassName(a,n),g=l(t,e,o,r,p,v,s),$='<tr class=" '+a+" "+y+" "+m+" "+b+' " '+f+" >",P=0;P<g.length;P++)2==P&&($+='<td><button type="button" class="btn btn-primary btn-sm add-product-batch-details" '+v+">+Details</button></td>"),$+=g[P];($+="<td> "+h+"</td> </tr>",c&&i)&&($+=d(t,e));return $}},inventoryBrand={loadBrandsMenu:function(){ajaxClient.get("/inventory/brands",function(t){$("#main-content").html(t),inventoryBrand.registerAddBrandCallback(),inventoryBrand.registerBrandSearchCallback(),inventoryBrand.fillDeleteBrandModel(),inventoryBrand.fillUpdateBrandModel()})},registerBrandSearchCallback:function(){$("#search-brand").off("click").on("click",function(){inventoryBrand.searchBrands()}),$("#brand-search-text").on("keyup",function(t){13==t.keyCode&&inventoryBrand.searchBrands()})},searchBrands:function(){var t=$("#brand-search-text").val();$("#brand-search-result").html(""),ajaxClient.get("/inventory/search-brands?text="+t,function(t){$("#brand-search-result").html(t),inventoryBrand.fillDeleteBrandModel(),inventoryBrand.fillUpdateBrandModel()})},registerAddBrandCallback:function(){$("#add-brand").off("click").on("click",function(){var t=$("#brand-name").val();$("#addBrandModel").modal("toggle");var e={brandName:t};ajaxClient.post("/inventory/add-brand",{brandData:JSON.stringify(e)},function(t){inventoryBrand.searchBrands(),inventoryBrand.clearAddBrandForm(),$("#brand-status-modal-container").html(t),$("#brand-status-modal").modal("toggle")})})},fillUpdateBrandModel:function(){$(".update-brand").off("click").on("click",function(){inventoryBrand.clearUpdateBrandForm();var t=$(this).parent().parent().find("td.brand-info").text().trim(),e=JSON.parse(t);$("#update-brand-name").val(e.brandName),$("#update-brand-id").val(e.brandId),$("#update-brand-modal").modal("toggle")})},updateBrand:function(){var t=$("#update-brand-name").val(),e=$("#update-brand-id").val();$("#update-brand-modal").modal("toggle");var a={brandId:e,brandName:t};ajaxClient.post("/inventory/update-brand",{brandData:JSON.stringify(a)},function(t){inventoryBrand.searchBrands(),$("#brand-status-modal-container").html(t),$("#brand-status-modal").modal("toggle")})},fillDeleteBrandModel:function(){$(".delete-brand").off("click").on("click",function(){var t=$(this).parent().parent().find("td.brand-info").text().trim(),e=JSON.parse(t);$("#delete-brand-id").text(e.brandId),$("#brand-delete-confirmation-modal-text").text("Are you sure to delete brand "+e.brandName),$("#brand-delete-confirmation-modal").modal("toggle")})},deleteBrand:function(){$("#brand-delete-confirmation-modal").modal("toggle");var t=$("#delete-brand-id").text();ajaxClient.post("/inventory/delete-brand",{brandId:t},function(t){$("#brand-brand-status-modal-container").html(t),$("#brand-status-modal").modal("toggle"),inventoryBrand.searchBrands()})},clearAddBrandForm:function(){$("#addBrandModel").find("input").val(""),$("#addBrandModel").find("select").val(""),$("#addBrandModel").find("span.label-area").remove()},clearUpdateBrandForm:function(){$("#update-brand-modal").find("input").val(""),$("#update-brand-modal").find("select").val(""),$("#update-brand-modal").find("span.label-area").remove()}},inventorySupplier={loadSupplierMenu:function(){ajaxClient.get("/inventory/supplier",function(t){$("#main-content").html(t),inventorySupplier.registerSupplierSearchCallback(),inventorySupplier.registerAddSupplierCallback(),inventorySupplier.registerUpdateSupplierCallback(),inventoryUtils.removeErrorOnClick(),inventoryUtils.bindRemoveErrorDisplayEvent(),inventorySupplier.bindUpdateSupplierCallback(),inventorySupplier.bindViewSupplierCallback()})},registerSupplierSearchCallback:function(){$("#searchSuppliers").off("click").on("click",function(){inventorySupplier.searchSuppliers()}),$("#searchSuppliersInput").on("keyup",function(t){13==t.keyCode&&inventorySupplier.searchSuppliers()})},searchSuppliers:function(){searchText=$("#searchSuppliersInput").val(),$("#searchSuppliersResult").html(""),ajaxClient.get("/inventory/supplier-search?text="+searchText,function(t){$("#searchSuppliersResult").html(t),inventorySupplier.bindUpdateSupplierCallback(),inventorySupplier.bindViewSupplierCallback()})},registerAddSupplierCallback:function(){$("#add-supplier").off("click").on("click",function(){if(inventorySupplier.validateAddSupplierForm()){var t=$("#supplier-name").val(),e=$("#supplier-first-name").val(),a=$("#supplier-last-name").val(),o=$("#supplier-address").val(),n=$("#supplier-city").val(),r=$("#supplier-state option:selected").val(),i=$("#supplier-zipcode").val(),l=$("#supplier-email").val(),c=$("#supplier-primary-mobile").val(),d=$("#supplier-secondary-mobile").val(),s=$("#supplier-landline").val();$("#searchSuppliersResult").html(""),$("#addSupplier").modal("toggle");var u={supplierName:t,contactName:e+" "+a,address:o,city:n,state:r,zipcode:i,email:l,primaryPhoneNumber:c,secondPhoneNumber:d,landlineNumber:s};ajaxClient.post("/inventory/add-supplier",{supplierPayload:JSON.stringify(u)},function(t){inventorySupplier.searchSuppliers(),inventorySupplier.clearAddSupplierForm(),$("#supplier-status-modal-container").html(t),$("#supplier-status-model").modal("toggle")})}})},clearAddSupplierForm:function(){$("#addSupplier").find("input").val(""),$("#addSupplier").find("select").val("")},bindUpdateSupplierCallback:function(){$(".update-supplier-button").off("click").on("click",function(){var t=$(this).parent().parent().find("p.supplier-info-json").text().trim(),e=JSON.parse(t);$("#supplier-update-supplierId").val(e.supplierId),$("#supplier-update-contactName").val(e.contactName),$("#supplier-update-supplierName").val(e.supplierName),$("#supplier-update-address").val(e.address),$("#supplier-update-city").val(e.city),$("#supplier-update-state").val(e.state),$("#supplier-update-zipcode").val(e.zipcode),$("#supplier-update-email").val(e.email),$("#supplier-update-primaryPhoneNumber").val(e.primaryPhoneNumber),$("#supplier-update-secondPhoneNumber").val(e.secondPhoneNumber),$("#supplier-update-landline").val(e.landlineNumber)})},bindViewSupplierCallback:function(){$(".view-supplier-button").off("click").on("click",function(){var t=$(this).parent().parent().find("p.supplier-info-json").text().trim(),e=JSON.parse(t);$("#supplier-view-supplierId").val(e.supplierId),$("#supplier-view-contactName").val(e.contactName),$("#supplier-view-supplierName").val(e.supplierName),$("#supplier-view-address").val(e.address),$("#supplier-view-city").val(e.city),$("#supplier-view-state").val(e.state),$("#supplier-view-zipcode").val(e.zipcode),$("#supplier-view-email").val(e.email),$("#supplier-view-primaryPhoneNumber").val(e.primaryPhoneNumber),$("#supplier-view-secondPhoneNumber").val(e.secondPhoneNumber),$("#supplier-view-landline").val(e.landlineNumber)})},registerUpdateSupplierCallback:function(){$("#update-supplier").off("click").on("click",function(){var t=$("#supplier-update-supplierId").val(),e=$("#supplier-update-contactName").val(),a=$("#supplier-update-supplierName").val(),o=$("#supplier-update-address").val(),n=$("#supplier-update-city").val(),r=$("#supplier-update-state").val(),i=$("#supplier-update-zipcode").val(),l=$("#supplier-update-email").val(),c=$("#supplier-update-primaryPhoneNumber").val(),d=$("#supplier-update-secondPhoneNumber").val(),s=$("#supplier-update-landline").val();$("#supplier-update-supplier-modal").modal("toggle");var u={supplierId:t,contactName:e,supplierName:a,address:o,city:n,state:r,zipcode:i,email:l,primaryPhoneNumber:c,secondPhoneNumber:d,landlineNumber:s};ajaxClient.post("/inventory/update-supplier",{supplierPayload:JSON.stringify(u)},function(t){inventorySupplier.searchSuppliers(),$("#supplier-status-modal-container").html(t),$("#supplier-status-modal").modal("toggle")})})},validateAddSupplierForm:function(){$(".mandatory-field").attr("style","display:none"),$(".mandatory-field-input").css("border",""),$(".validate-email").css("border",""),$(".invalid-email").attr("style","display:none");var t=!1,e=$("#supplier-name");""==e.val()&&(e.css("border","1px solid #ff8795"),$("#mandatoryFieldCompanyName").attr("style","display:block"),t=!0);var a=$("#supplier-first-name");""==a.val()&&(a.css("border","1px solid #ff8795"),$("#mandatoryFieldContactName").attr("style","display:block"),t=!0);var o=$("#supplier-address");""==o.val()&&(o.css("border","1px solid #ff8795"),$("#mandatoryFieldAddress").attr("style","display:block"),t=!0);var n=$("#supplier-city");""==n.val()&&(n.css("border","1px solid #ff8795"),$("#mandatoryFieldCity").attr("style","display:block"),t=!0);var r=$("#supplier-state");""==r.val()&&(r.css("border","1px solid #ff8795"),$("#mandatoryFieldState").attr("style","display:block"),t=!0);var i=$("#supplier-email");return validateEmail(i.val())||(i.css("border","1px solid #ff8795"),$("#invalidFieldEmail").attr("style","display:block"),t=!0),!t},removeErrorInitially:function(){$(".mandatory-field").attr("style","display:none"),$(".mandatory-field-input").css("border",""),$(".validate-email").css("border",""),$(".invalid-email").attr("style","display:none")}},inventoryTransactions={dataCache:{},loadTransactionMenu:function(){ajaxClient.get("/inventory/transactions",function(t){$("#main-content").html(t),inventoryTransactions.bindtransactionGenerationOptions(),inventoryTransactions.dataCache={}})},loadTransactionBody:function(){ajaxClient.get("/inventory/transactions-body",function(t){$("#transations-body").html(t),inventoryTransactions.bindtransactionGenerationOptions(),inventoryTransactions.dataCache={}})},getTransactionDetails:function(t){ajaxClient.get("/inventory/transactions-details/"+t,function(t){$("#transaction-summary-modal-container").html(t),$("#transaction-summary-modal").modal("toggle")})},generateInvoice:function(t){window.open(baseURL+"/inventory/pdf-invoice/"+t,"_blank")},deleteTransactionPreview:function(t){inventoryTransactions.dataCache.deleteSelectedTransactionId=t,$("#delete-transaction-screen-modal").modal("toggle")},deleteTransaction:function(){$("#delete-transaction-screen-modal").modal("toggle");var t=inventoryTransactions.dataCache.deleteSelectedTransactionId;ajaxClient.post("/inventory/delete-transaction/"+t,{},function(t){$("#transaction-status-modal-container").html(t),$("#transaction-status-modal").modal("toggle"),inventoryTransactions.loadTransactionBody()})},bindtransactionGenerationOptions:function(){$("#active-transaction").on("click",function(){$(".inventory-transaction").attr("style","display:none;"),$("#active-transations-body").attr("style","display:block;"),inventoryTransactions.loadTransactionMenu()}),$("#cancelled-transaction").on("click",function(){$(".inventory-transaction").attr("style","display:none;"),$("#cancelled-transactions-body").attr("style","display:block;")})}},inventoryReports={pastReportAllowedDays:365,TRANSACTION:"TRANSACTION",USER_LEVEL_DAY_WISE_REPORT:"USER_LEVEL_DAY_WISE_REPORT",loadReportsMenu:function(){ajaxClient.get("/inventory/reports",function(t){$("#main-content").html(t),initDate(inventoryReports.pastReportAllowedDays),inventoryReports.generateReport()})},generateReport:function(){$(".generate-report").on("click",function(){var t=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(t))){var e=$(this).parent().parent().find("p").text(),a=-1,o=-1;if($(this).parent().parent().parent().parent().modal("toggle"),"STOCK_DETAILS_REPORT"!=e){var n=$(this).parent().parent().find("input.report-start-date").val(),r=$(this).parent().parent().find("input.report-end-date").val(),i=getDate(n),l=getDate(r);if(a=getDayStart(i),o=getDayEnd(l),a.getTime()>=o.getTime())return void showErrorDialogBox("Report start date cannot be greater than report end date. Please enter valid range.");a=a.getTime()/1e3,o=o.getTime()/1e3}window.open(baseURL+"/inventory/generate-report/"+e+"?start="+a+"&end="+o,"_blank")}})}},inventoryProductGroup={dataCache:{},loadProductGroupsMenu:function(){ajaxClient.get("/inventory/product-groups",function(t){$("#main-content").html(t),inventoryProductGroup.init(),inventoryProductGroup.searchProductGroup(),inventoryProductGroup.registerProductGroupSearchCallback(),liveSearchHandler.bindEvent("#groups\\.search-product","#groups\\.add\\.search-result",inventoryProductGroup.doneAddGroupProductTyping),liveSearchHandler.bindEvent("#groups\\.update\\.search-product","#groups\\.update\\.search-result",inventoryProductGroup.doneUpdateGroupProductTyping),inventoryUtils.bindRemoveErrorDisplayEvent(),inventoryProductGroup.registerAddProductGroupCallback(),inventoryProductGroup.registerUpdateProductGroupCallback(),inventoryProductGroup.registerDeleteProductGroupCallback()})},init:function(){liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId,inventoryUtils.liveSearchProductResultElementId,inventoryProductGroup.doneProductTyping)},doneProductTyping:function(t){var e=$(inventoryUtils.liveSearchProductElementId).val();ajaxClient.get("/inventory/live-stock-search?text="+e,function(a){$(t).html(a),inventoryUtils.bindClickEventOnSearchProduct(e,inventoryProductGroup.dataCache,inventoryProductGroup.getBatchSelectColumn,inventoryProductGroup.getRowColumns,!0,inventoryProductGroup.getRowDetails,inventoryProductGroup.bindSaleNewRowEvent,!1,inventoryProductGroup.onDelete)})},getBatchSelectColumn:function(t){return'<select class="custom-select product-batch-select">'+t+"</select>"},getRowColumns:function(t,e,a,o,n,r,i){var l=0,c=0;if(null!=e){l=e.productBatch.sellingPrice;var d=e.productBatch.discount,s=e.productBatch.discountInPercent;null!=d&&(s?c=d:null!=l&&l>0&&(c=100*d/l))}return['<td scope="row"> '+a+" </td>",'<td style="width:15%;">'+o+"</td>",'<td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity" '+n+"> </td> ",' <td><input type="number" class="form-control form-control-sm sale-price mandatory-table-field positive-number" placeholder="0" value="'+l+'" readonly></td> ',' <td><input type="number" class="form-control form-control-sm sale-item-amount" placeholder="0" readonly></td> ','<td><input type="number" class="form-control form-control-sm sale-discount percentage-range" placeholder="Discount" value="'+c+'" '+n+"> </td> ",'<td><input type="number" class="form-control form-control-sm sale-item-total" placeholder="0" readonly></td>']},getRowDetails:function(t,e){var a="",o="",n="",r="";return null!=e&&(a=e.productBatch.mrp,o=e.productBatch.purchasePrice,n=e.productBatch.sellingPrice,null==(r=e.productBatch.description)&&(r="")),'<tr class="product-batch-details-row"> <td colspan="12" style="padding: 0 !important;"> <div class="collapse product-batch-details-container "> <div class="card card-body"> <table id="datatables-reponsive" class="table table-borderless datatables-reponsive-table text-center"> <thead> <tr> <th scope="col">MRP</th> <th scope="col">Purchase Price</th> <th scope="col">Selling Price</th> <th scope="col">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type="number" class="form-control form-control-sm product-batch-mrp"  placeholder="0" value="'+a+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-purchase-price"  placeholder="0" value="'+o+'" readonly></td> <td><input type="number" class="form-control form-control-sm product-batch-sale-price"  placeholder="0" value="'+n+'" readonly></td> <td><textarea class="form-control batch-description" rows="1" placeholder="Batch Description" readonly >'+r+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>"},bindSaleNewRowEvent:function(){inventoryProductGroup.bindCostComputeEvent()},onDelete:function(){},bindViewProductsGroup:function(){$(".view-product-group").on("click",function(){inventoryProductGroup.showProductGroupListModel($(this).parent().parent().find("p.product-group-info"))})},bindCostComputeEvent:function(){$(".product-quantity").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent())}),$(".sale-price").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent())}),$(".sale-discount").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent())}),$(".purchase-product-tax").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent())}),$("#additional-cost-input").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent().parent())}),$("#additional-discount-input").focus(function(){}).blur(function(){inventorySale.computeTotal($(this).parent().parent().parent())})},computeTotal:function(t){var e=$(t).find(".product-quantity").first().val(),a=0;e&&(a=parseFloat(e));var o=$(t).find(".sale-price").first().val(),n=0;o&&(n=parseFloat(o));var r=parseFloat($(t).first().find(".sale-discount").first().val()),i=0;r&&(i=parseFloat(r));var l=parseFloat($(t).first().find(".purchase-product-tax").first().val()),c=0;l&&(c=parseFloat(l));var d=a*n,s=d+d*c/100-(d+d*c/100)*i/100;$(t).find(".sale-item-amount").first().val(d.toFixed(2)),$(t).find(".sale-item-total").first().val(s.toFixed(2)),inventorySale.computeNetSaleAmount()},collectSelectedProducts:function(){return productBatchList=[],$("tr.selected-product-row").each(function(){if(!$(this).hasClass(inventoryUtils.batchSelectRowClass)){var t=inventoryUtils.getClassWithPrefix(this,inventoryUtils.rowPrefix);if(null!=t){var e=t.split(inventoryUtils.delimiter),a=e[1].trim();if(""!=a){var o=e[2].trim();if(""!=o){var n=parseFloat($(this).find("input.product-quantity").val()),r=0;""!=$(this).find("input.sale-discount").val()&&(r=parseFloat($(this).find("input.sale-discount").val())),productBatchList.push({skuId:a,batchId:o,quantity:n,discount:r})}}}}}),productBatchList},showProductGroupListModel:function(t){for(var e=t.text().trim(),a=JSON.parse(e),o=titleCase(a.productGroupBasicInfo.groupName),n="",r=1,i=0;i<a.productDataQuantityList.length;i++){var l=a.productDataQuantityList[i].quantity,c=a.productDataQuantityList[i],d=c.productMetadata.name,s=c.category.categoryName,u="-";null!=c.brand&&(u=c.brand.brandName);for(var p=0;p<c.productGroupBatchDataQuantities.length;p++){var v=c.productGroupBatchDataQuantities[p],h=v.productBatchData.batchName,m=(l=v.quantity,v.discount);n+="<tr> <th>"+r+"</th> <td> "+d+"</td> <td>"+h+"</td> <td>"+s+"</td> <td> "+u+"</td> <td>"+l+"</td> <td> "+v.productBatchData.sellingPrice+"</td>  <td> "+m+"</td>  <td> "+v.availableGroupCount+"</td> </tr>";r=r+1}}$("#groups\\.product-group-name").html(o),$("#groups\\.product-list-tbody").html(n),$("#groups\\.product_list_model").modal("toggle")},bindUpdateProductsGroup:function(){$(".update-product-group").on("click",function(){$("#groups\\.update\\.search_row").prevAll("tr").remove(),$(".group-screen").hide(),$("#groups\\.update_screen").show();for(var t=$(this).parent().parent().find("p.product-group-info").text().trim(),e=JSON.parse(t),a=e.productGroupBasicInfo.groupId,o=titleCase(e.productGroupBasicInfo.groupName),n=e.productGroupBasicInfo.discount,r=e.productGroupBasicInfo.description,i=0;i<e.productDataQuantityList.length;i++){var l=e.productDataQuantityList[i],c=l.productGroupBatchDataQuantities&&l.productGroupBatchDataQuantities.length>0?l.productGroupBatchDataQuantities[0]:null;if(c){var d=c.productBatchData&&c.productBatchData.batchId?c.productBatchData.batchId:"",s=void 0!==c.quantity?c.quantity:0,u=void 0!==c.discount?c.discount:0,p=c.productBatchData&&c.productBatchData.sellingPrice?c.productBatchData.sellingPrice:0,v=titleCase(l.productMetadata.name),h=l.productMetadata.skuId,m=(l.brand&&titleCase(l.brand.brandName),titleCase(l.category.categoryName),c.productBatchData.batchName||"Unknown Batch"),f=c.productBatchData.totalQuantity||0,y="";y=f>0?"<span class='badge badge-success'>Available: "+f+"</span>":"<span class='badge badge-danger'>Not Available</span>";var b={productMetadata:l.productMetadata,productBatch:c.productBatchData},g=' <tr class="update-grouped-product '+d+'" id="'+h+'"> <td class="update-product-name" scope="row"> '+v+" </td><td>"+m+" "+y+'</td><td> <button type="button" class="btn btn-primary btn-sm add-product-batch-details">+Details</button></td> <td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quantity"> </td> <td> <input type="number" class="form-control form-control-sm product-price mandatory-table-field positive-number" placeholder="0" readonly></td> <td> <input type="number" class="form-control form-control-sm product-discount percentage-range" placeholder="0"> </td> <td><input type="number" class="form-control form-control-sm product-amount" placeholder="0" readonly></td> <td> <button type="button" class="close delete-group-item" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'+inventoryProductGroup.getRowDetails(!0,b);$("#groups\\.update\\.search_row").before(g),$("#"+h+".update-grouped-product").find(".product-price").first().val(p),$("#"+h+".update-grouped-product").find(".product-quantity").first().val(s),$("#"+h+".update-grouped-product").find(".product-discount").first().val(u),inventoryProductGroup.computeProductTotal($("#"+h+".update-grouped-product"))}}inventoryProductGroup.deleteProductItemEntry(),inventoryProductGroup.bindGroupProductCostComputeEvent(),inventoryUtils.removeErrorOnClick(),inventoryUtils.bindExpandBatchButton(),$("#groups\\.group-id").text(a),$("#groups\\.update\\.group-name").val(o),$("#groups\\.update\\.discount").val(n),$("#groups\\.update\\.description").val(r)})},bindDeleteProductsGroup:function(){$(".delete-product-group").on("click",function(){var t=$(this).parent().parent().find("p.product-group-info").text().trim(),e=JSON.parse(t),a=e.productGroupBasicInfo.groupId,o=titleCase(e.productGroupBasicInfo.groupName);$("#groups\\.delete\\.product-group-id").html(a),$("#groups\\.delete\\.product-group-name").html(o),$("#groups\\.delete\\.product-group-model").modal("toggle")})},registerProductGroupSearchCallback:function(){$("#groups\\.search-group").on("click",function(){inventoryProductGroup.searchProductGroup()}),$("#groups\\.search-text").on("keyup",function(t){13==t.keyCode&&inventoryProductGroup.searchProductGroup()})},searchProductGroup:function(){var t=$("#groups\\.search-text").val();$("#groups\\.search-result").html(""),ajaxClient.get("/inventory/search-product-groups?text="+t,function(t){$("#groups\\.search-result").html(t),inventoryProductGroup.bindViewProductsGroup(),inventoryProductGroup.bindUpdateProductsGroup(),inventoryProductGroup.bindDeleteProductsGroup()})},addNewGroup:function(){$(".group-screen").attr("style","display:none"),$("#groups\\.add_screen").attr("style","display:block"),$("#live-search-row").prevAll("tr").remove(),inventoryUtils.clearValidationErrorDisplay(),inventoryProductGroup.dataCache[inventoryUtils.selectedProductsKey]={},inventoryProductGroup.clearFormFields()},returnToMainScreen:function(){$(".group-screen").attr("style","display:none"),$("#groups\\.default_screen").attr("style","display:block"),inventoryUtils.clearValidationErrorDisplay(),inventoryProductGroup.dataCache[inventoryUtils.selectedProductsKey]={},inventoryProductGroup.clearFormFields()},clearFormFields:function(){$("#groups\\.group-name").val(""),$("#groups\\.discount").val(""),$("#groups\\.description").val(""),$(".product-batch-details-row").remove()},doneAddGroupProductTyping:function(t){var e=$("#groups\\.search-product").val();ajaxClient.get("/inventory/live-stock-search?text="+e,function(e){$(t).html(e),inventoryProductGroup.bindClickEventOnAddProductSearchItem()})},doneUpdateGroupProductTyping:function(t){var e=$("#groups\\.update\\.search-product").val();ajaxClient.get("/inventory/live-stock-search?text="+e,function(e){$(t).html(e),inventoryProductGroup.bindClickEventOnUpdateProductSearchItem()})},bindClickEventOnAddProductSearchItem:function(){$("#live_search_stock_results tbody tr th").on("click",function(){var t=$(this).parent().find("td.stock-json").text().trim(),e=JSON.parse(t),a=e.skuId,o=e.productName,n=parseFloat(e.sellingPrice),r=parseFloat(e.discount);0==$(".grouped-product-item#"+a).length?($("#groups\\.add\\.search_row").before(' <tr class="grouped-product-item" id="'+a+'"> <td class="purchase_product_name" scope="row"> '+o+' </td> <td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quanity"> </td> <td> <input type="number" class="form-control form-control-sm product-price mandatory-table-field positive-number" placeholder="0" readonly></td> <td> <input type="number" class="form-control form-control-sm product-discount mandatory-field percentage-range" placeholder="0"> </td> <td><input type="number" class="form-control form-control-sm product-amount" placeholder="0" readonly></td> <td> <button type="button" class="close delete-group-item " aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr> '),$("#"+a+".grouped-product-item").find(".product-price").first().val(n),$("#"+a+".grouped-product-item").find(".product-discount").first().val(r),$("#groups\\.add\\.search-result").html(""),$("#groups\\.search-product").val(""),inventoryProductGroup.deleteProductItemEntry(),inventoryProductGroup.bindGroupProductCostComputeEvent(),inventoryProductGroup.removeErrorOnClick()):showErrorDialogBox("Product :  "+o+" already selected. Please add quantity in that. ")})},bindClickEventOnUpdateProductSearchItem:function(){$("#live-search-stock-results tbody tr th").on("click",function(){var t=$(this).parent().find("td.stock-json").text().trim(),e=JSON.parse(t),a=e.productMetadata.skuId,o=e.productMetadata.name,n=e.productBatchDataList&&e.productBatchDataList.length>0?e.productBatchDataList[0]:null;if(n){var r=n.batchId,i=n.sellingPrice,l=n.discount,c=n.discountInPercent;null!=l&&null!=i&&i>0&&!c&&(l=100*l/i);var d=n.batchName||"",s=n.totalQuantity||0,u="";u=s>0?"<span class='badge badge-success'>Available: "+s+"</span>":"<span class='badge badge-danger'>Not Available</span>";var p={productMetadata:e.productMetadata,productBatch:n},v=inventoryProductGroup.getRowDetails(!0,p);if(0==$("#"+a+".update-grouped-product").length){var h='  <tr class="update-grouped-product '+r+'" id="'+a+'">   <td class="update-product-name" scope="row"> '+o+" </td>   <td>"+d+" "+u+'  </td>  <td>   <button type="button" class="btn btn-primary btn-sm add-product-batch-details">+Details</button>  </td>   <td> <input type="number" class="form-control form-control-sm product-quantity mandatory-table-field positive-number" placeholder="Quantity"> </td>   <td> <input type="number" class="form-control form-control-sm product-price mandatory-table-field positive-number" placeholder="0" readonly></td>   <td> <input type="number" class="form-control form-control-sm product-discount percentage-range" placeholder="0"> </td>   <td> <input type="number" class="form-control form-control-sm product-amount" placeholder="0" readonly></td>   <td> <button type="button" class="close delete-group-item" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'+v;$("#groups\\.update\\.search_row").before(h),$("#"+a+".update-grouped-product").find(".product-price").first().val(i),$("#"+a+".update-grouped-product").find(".product-discount").first().val(l),$("#groups\\.update\\.search-result").html(""),$("#groups\\.update\\.search-product").val(""),inventoryProductGroup.deleteProductItemEntry(),inventoryProductGroup.bindGroupProductCostComputeEvent(),inventoryUtils.bindExpandBatchButton()}else showErrorDialogBox("Product : "+o+" already selected. Please add quantity in that.")}else showErrorDialogBox("No batch data available for product "+o)})},deleteProductItemEntry:function(){$(".delete-group-item").click(function(){$(this).parent().parent().remove()})},bindGroupProductCostComputeEvent:function(){$(".product-quantity").focus(function(){}).blur(function(){inventoryProductGroup.computeProductTotal($(this).parent().parent())}),$(".product-discount").focus(function(){}).blur(function(){inventoryProductGroup.computeProductTotal($(this).parent().parent())})},computeProductTotal:function(t){var e=$(t).find(".product-quantity").first().val(),a=0;e&&(a=parseInt(e));var o=$(t).find(".product-discount").first().val(),n=0;o&&(n=parseInt(o));var r=$(t).find(".product-price").first().val(),i=0;r&&(i=parseFloat(r));var l=a*i*(100-n)/100;$(t).find(".product-amount").first().val(l.toFixed(2))},collectProductListValues:function(t){return products=[],$(t).each(function(){var t=$(this).attr("id"),e=$(this).find("input.product-quantity").val(),a=$(this).find("input.product-discount").val(),o=$(this).attr("class").split(/\s+/).filter(function(t){return"update-grouped-product"!==t})[0],n={};n.skuId=t,n.batchId=o,n.quantity=e,n.discount=a,products.push(n)}),products},registerAddProductGroupCallback:function(){$("#groups\\.add-confirm").on("click",function(){if($("#groups-add-confirm-modal").modal("toggle"),products=inventoryProductGroup.collectSelectedProducts(),products.length>0){var t=$("#groups\\.group-name").val(),e=$("#groups\\.discount").val(),a=$("#groups\\.description").val();productGroup={productGroupBasicInfo:{groupName:t,discount:e,description:a},productGroupItems:products},ajaxClient.post("/inventory/add-product-group",{productGroup:JSON.stringify(productGroup)},function(t){$("#groups\\.status-model-container").html(t),$("#groups\\.status_model").modal("toggle"),inventoryProductGroup.returnToMainScreen(),inventoryProductGroup.searchProductGroup()})}else alert("Please add atleast one product in group.")})},registerUpdateProductGroupCallback:function(){$("#groups\\.update-confirm").on("click",function(){if($("#groups-update-confirm-modal").modal("toggle"),products=inventoryProductGroup.collectProductListValues("tr.update-grouped-product"),products.length>0){var t=inventoryUtils.validations($(".tr.update-grouped-product"));if(!inventoryUtils.validations($("#update\\.product-group"))&&!t){var e=$("#groups\\.group-id").text().trim(),a=$("#groups\\.update\\.group-name").val(),o=$("#groups\\.update\\.discount").val(),n=$("#groups\\.update\\.description").val();productGroup={productGroupBasicInfo:{groupId:e,groupName:a,discount:o,description:n},productGroupItems:products},ajaxClient.post("/inventory/update-product-group",{productGroup:JSON.stringify(productGroup)},function(t){$("#groups\\.status-model-container").html(t),$("#groups\\.status_model").modal("toggle"),inventoryProductGroup.returnToMainScreen(),inventoryProductGroup.searchProductGroup()})}}})},registerDeleteProductGroupCallback:function(){$("#groups\\.delete-confirm").on("click",function(){$("#groups\\.delete\\.product-group-model").modal("toggle");var t=$("#groups\\.delete\\.product-group-id").text().trim();ajaxClient.post("/inventory/delete-product-group/"+t,{},function(t){$("#groups\\.status-model-container").html(t),$("#groups\\.status_model").modal("toggle"),inventoryProductGroup.returnToMainScreen(),inventoryProductGroup.searchProductGroup()})})}};