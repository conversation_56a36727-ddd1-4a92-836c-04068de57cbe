from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.utils.tracking_events import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from .controller.holiday_calendar_manager import *
from math import *
from datetime import date
from core.controller.utils.date_utils import *
from collections import OrderedDict


module_name = 'HOLIDAY_CALENDAR'

def authorized_user_session(request):
	return authorized_module(request,module_name)

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "HOLIDAY_CALENDAR_LOADED"})
		user_login_view = get_user_login_view(request)
		user_type = user_login_view['user']['userType']
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		tutorial_videos = get_tutorial_video_details(module_name, institute_unique_code, user_type)
		return render(request, 'holiday_calendar/layouts/default-calendar-layout.html',{'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data': payment_status_data, 'view_only_access': view_only_access, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'tutorial_videos': tutorial_videos})

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'holiday_calendar/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'holiday_calendar/layouts/dashboard-session-content.html',{'user': user_login_view['user']})


def manage_holiday_home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'holiday_calendar/holidays/home.html',{'academic_years':academic_years, 'current_session':current_session})


def session_holidays_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		session_holidays = get_session_holidays(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'holiday_calendar/holidays/session_holidays.html',{'session_holidays':session_holidays})

@csrf_exempt
def new_holidays_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		holiday_payload = json.loads(request.POST['payload'])
		response_data = add_new_holidays(user_login_view, institute_unique_code, holiday_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})


@csrf_exempt
def update_holidays_view(request, institute_unique_code, holiday_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		holiday_payload = json.loads(request.POST['payload'])
		response_data = update_holidays(user_login_view, institute_unique_code, holiday_id, holiday_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})


@csrf_exempt
def delete_holidays_view(request, institute_unique_code, academic_session_id, holiday_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_holiday(user_login_view, institute_unique_code, academic_session_id, holiday_id)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})


def holiday_template_home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		session_holiday_templates = get_session_holiday_templates(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'holiday_calendar/templates/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'session_holiday_templates'  : session_holiday_templates})

def holiday_template_form_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		session_holidays_group = group_holidays(get_session_holidays(user_login_view, institute_unique_code, academic_session_id))
		return render(request, 'holiday_calendar/templates/new_template_modal.html', {'holidays_group' : session_holidays_group , 'new_template' : True, 'template_id' : 'new_template', 'update_allowed' : True})


def group_holidays(session_holidays, selected_holidays = {}):
	recurring_holidays = []
	static_grouped_holidays = []
	static_holidays = OrderedDict()
	if session_holidays is None:
		return {'recurring_holidays' : [], 'static_grouped_holidays' : []}

	for holiday in session_holidays:
		if holiday['holidayType'] == 'RECURRING':
			recurring_holidays.append({'holiday' : holiday, 'selected' : holiday['holidayId'] in selected_holidays})
		else:
			start_datetime = get_default_datetime(holiday['start'])
			holiday_start_month = start_datetime.month
			if holiday_start_month in static_holidays:
				month_data = static_holidays[holiday_start_month]
				month_data['holidays'].append({'holiday' : holiday, 'selected' : holiday['holidayId'] in selected_holidays})
				static_holidays[holiday_start_month] = month_data
			else:
				title = start_datetime.strftime("%B %Y")
				static_holidays[holiday_start_month] = {'title' : title, 'holidays' : [{'holiday' : holiday, 'selected' : holiday['holidayId'] in selected_holidays}]}

	if static_holidays:
		total_months = len(static_holidays)
		month_holiday_list = list(static_holidays.values())
		static_grouped_holidays = [month_holiday_list[i:i + total_months] for i in range(0, len(month_holiday_list), total_months)]

	return {'recurring_holidays' : recurring_holidays, 'static_grouped_holidays' : static_grouped_holidays}



def holiday_template_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		session_holiday_templates = get_session_holiday_templates(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'holiday_calendar/templates/session_templates_list.html', {'session_holiday_templates'  : session_holiday_templates})



@csrf_exempt
def add_new_holiday_template_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		holiday_template_payload = json.loads(request.POST['holidayTemplatePayload'])
		response_data = add_new_holiday_template(user_login_view, institute_unique_code, holiday_template_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data" : response_data})



def get_holiday_map(holidays):
	holiday_map = {}
	for holiday in holidays:
		holiday_map[holiday['holidayId']] = holiday
	return holiday_map

def get_holiday_template_view(request, institute_unique_code, template_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		holiday_template_details = get_holiday_template_details(user_login_view, institute_unique_code, template_id)
		template_holidays_group = None
		if not holiday_template_details is None:
			holidays = holiday_template_details['holidays']
			holiday_map = get_holiday_map(holidays)
			template_holidays_group = group_holidays(holidays, holiday_map)
		return render(request, 'holiday_calendar/templates/view_template_modal.html', {'holiday_template_details'  : holiday_template_details, 'holidays_group' : template_holidays_group , 'new_template' : False, 'template_id' : template_id, 'update_allowed' : False})



def holiday_template_update_form_view(request, institute_unique_code, template_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		holiday_template_details = get_holiday_template_details(user_login_view, institute_unique_code, template_id)
		template_holidays_group = None
		if not holiday_template_details is None:
			session_holidays = get_session_holidays(user_login_view, institute_unique_code, holiday_template_details['holidayTemplate']['academicSessionId'])
			holiday_map = get_holiday_map(holiday_template_details['holidays'])
			template_holidays_group = group_holidays(session_holidays, holiday_map)
		return render(request, 'holiday_calendar/templates/update_template_modal.html', {'holiday_template_details'  : holiday_template_details, 'holidays_group' : template_holidays_group , 'new_template' : False, 'template_id' : template_id, 'update_allowed' : True})

@csrf_exempt
def update_holiday_template_view(request, institute_unique_code, template_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		holiday_template_payload = json.loads(request.POST['holidayTemplatePayload'])
		response_data = update_holiday_template(user_login_view, institute_unique_code, template_id, holiday_template_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data" : response_data})

@csrf_exempt
def delete_holiday_template_view(request, institute_unique_code, template_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_holiday_template(user_login_view, institute_unique_code, template_id)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})




def template_assign_home_page_view(request, institute_unique_code, user_type):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		session_holiday_templates = get_session_holiday_templates(user_login_view, institute_unique_code, current_session['academicSessionId'], user_type)
		user_template_assignment_list = get_session_holiday_template_assignment(user_login_view, institute_unique_code, current_session['academicSessionId'], user_type)
		return render(request, 'holiday_calendar/assignment/main_screen.html',{'user_type' : user_type, 'academic_years':academic_years, 'current_session':current_session, 'standards' : standards, 'session_holiday_templates'  : session_holiday_templates, 'user_template_assignment_list' : user_template_assignment_list})


def template_assign_session_view(request, institute_unique_code, academic_session_id, user_type):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		session_holiday_templates = get_session_holiday_templates(user_login_view, institute_unique_code, academic_session_id, user_type)
		user_template_assignment_list = get_session_holiday_template_assignment(user_login_view, institute_unique_code, academic_session_id, user_type)
		if user_type == "STUDENT":
			return render(request, 'holiday_calendar/assignment/student_session_screen.html', {'user_type' : user_type, 'standards' : standards, 'session_holiday_templates'  : session_holiday_templates, 'user_template_assignment_list' : user_template_assignment_list})
		elif user_type == "STAFF":
			return render(request, 'holiday_calendar/assignment/staff_session_screen.html', {'user_type' : user_type, 'standards' : standards, 'session_holiday_templates'  : session_holiday_templates, 'user_template_assignment_list' : user_template_assignment_list})

		return render(request, 'holiday_calendar/assignment/student_session_screen.html', {'user_type' : user_type, 'standards' : standards, 'session_holiday_templates'  : session_holiday_templates, 'user_template_assignment_list' : user_template_assignment_list})


def template_user_search_view(request, institute_unique_code, academic_session_id, user_type):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		required_standards = request.GET.get("requiredStandards","")
		template_not_assigned_users_only = request.GET.get("templateNotAssignedUsersOnly", False)
		user_template_assignment_list = get_session_holiday_template_assignment(user_login_view, institute_unique_code, academic_session_id, user_type, template_not_assigned_users_only, required_standards)
		if user_type == "STUDENT":
			return render(request, 'holiday_calendar/assignment/student_template_list.html', {'user_type' : user_type, 'user_template_assignment_list' : user_template_assignment_list})
		elif user_type == "STAFF":
			return render(request, 'holiday_calendar/assignment/staff_template_list.html', {'user_type' : user_type, 'user_template_assignment_list' : user_template_assignment_list})

		return render(request, 'holiday_calendar/assignment/student_template_list.html', {'user_type' : user_type, 'user_template_assignment_list' : user_template_assignment_list})



@csrf_exempt
def user_template_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		holiday_template_assign_payload = json.loads(request.POST['holidayTemplateAssignPayload'])
		response_data = update_user_holiday_template_assignment(user_login_view, institute_unique_code, holiday_template_assign_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data" : response_data})


def event_management_main_screen_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		events = get_event_details(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'holiday_calendar/event_management/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'events' : events})

@csrf_exempt
def add_event_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		event_details_payload = json.loads(request.POST['event_details'])
		response_data = add_event_details(user_login_view, institute_unique_code, event_details_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})

@csrf_exempt
def update_event_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		event_details_payload = json.loads(request.POST['event_details'])
		response_data = update_event_details(user_login_view, institute_unique_code, event_details_payload)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})
		
@csrf_exempt
def delete_event_view(request, institute_unique_code, academic_session_id, event_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_event(user_login_view, institute_unique_code, academic_session_id, event_id)
		return render(request, 'holiday_calendar/default_status_modal.html',{"data":response_data})

def event_session_change_screen_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		events = get_event_details(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'holiday_calendar/event_management/event_list.html',{'events' : events})

