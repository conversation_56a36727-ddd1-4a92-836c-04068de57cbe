import time
import json
from core.controller.utils.cache_provider import *
from core.controller.utils.restclient import *
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

cache_provider = get_cache_provider()
restclientWithoutUser = getrestclientWithoutUser()
LESSON_PLAN_CACHE_NAMESPACE = "lesson_plan_preferences"

def get_course_staff_lesson_plan_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    if standard_id == None and section_id == None:
        response = restclient.get(user_login_view, "/2.0/lesson/course-details?institute_id="+str(institute_id)+"&academic_session_id="+ str(academic_session_id) + "&user_id=" + str(user['uuid']))
        return response.get_data()
    elif section_id == 'null' or section_id == None : 
        response = restclient.get(user_login_view, "/2.0/lesson/course-details?institute_id="+str(institute_id)+"&academic_session_id="+ str(academic_session_id) + "&standard_id=" + standard_id + "&user_id=" + str(user['uuid']))
        return response.get_data()
    response = restclient.get(user_login_view, "/2.0/lesson/course-details?institute_id="+str(institute_id)+"&academic_session_id="+ str(academic_session_id) + "&standard_id=" + standard_id + "&section_id=" + section_id + "&user_id=" + str(user['uuid']))
    return response.get_data()

def get_lesson_plan_structure(user_login_view, institute_unique_code, lesson_plan_id, lesson_plan_status):
    response = restclient.get(user_login_view, "/2.0/lesson/plan-details?lesson_plan_id="+lesson_plan_id+"&lesson_plan_status="+ lesson_plan_status)
    return response.get_data()

def save_lesson_plan_details(user_login_view, institute_unique_code, academic_session_id, course_id, finalPayload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']

    response = restclient.post(user_login_view, "/2.0/lesson/plan-classification-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid']+"&course_id="+course_id, finalPayload)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Lesson Plan Data Saved Successfully!!!"}

def save_staff_response_details(user_login_view, institute_unique_code, academic_session_id, course_id, finalPayload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']

    response = restclient.post(user_login_view, "/2.0/lesson/staff-response-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid']+"&course_id="+course_id, finalPayload)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Staff response Saved Successfully!!!"}

def lesson_plan_details_clone(user_login_view, institute_unique_code, academicSessionId, finalPayload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']

    response = restclient.post(user_login_view, "/2.0/lesson/clone-lesson-plan-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academicSessionId)+"&user_id="+user['uuid'], finalPayload)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Cloned Lesson Plans Successfully!!!"}

def delete_lesson_plan_details_all(user_login_view, institute_unique_code, academicSessionId, lessonPlanId):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/lesson/plan-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academicSessionId)+"&user_id="+user['uuid']+"&lesson_plan_id="+lessonPlanId)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Lesson Plan Deleted Successfully!!!"}
