{% load json %}
<div class="row mb-2 mb-xl-3">
    {% include 'core/utils/v2/academic_session_display.html'%}
    <div class="col-auto ml-auto text-right mt-n1">
      <button type="button" class="btn btn-primary" data-toggle="modal" onclick="configureTemplate.addNewTemplateForm()">New Calendar</button>
      {% include 'core/utils/v2/academic_session_dropdown.html'%}
    </div>
</div>

<div class="container-fluid p-0 card">
  <br />
  <div id="session-holiday-template-list" class="card-body">
    {% include 'holiday_calendar/templates/session_templates_list.html' %}
  </div>
</div>


<div id="delete-holiday-template-modal" class="modal fade" id="holiday-default-status-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Delete Calendar</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        </div>
        <div class="modal-body m-3">
            <p id="delete-holiday-template-id" style="display:none"></p>
            <p> <strong> Are you sure to remove this Calendar? </strong></p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button id="delete-holiday-template" type="button" class="btn btn-primary" onclick="configureTemplate.submitDeleteHolidayTemplate()">Delete Calendar</button>
        </div>
    </div>
  </div>
</div>


<div id="holiday-template-add-modal-container"></div>
<div id="holiday-template-view-modal-container"></div>
<div id="holiday-template-update-modal-container"></div>
<div id="holiday-template-status-modal-container"></div>
