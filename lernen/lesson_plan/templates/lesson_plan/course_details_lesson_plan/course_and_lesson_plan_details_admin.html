{% load json %}
<div class="card-body" id="course-details-div">
    {% include 'lesson_plan/course_details_lesson_plan/clone_lesson_plan.html' %}
    <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
        <caption>List of Courses</caption>
        <thead>
            <tr style="text-align: center;">
                <th scope="col">S.No</th>
                <th scope="col">Course name</th>
                <th scope="col">Course type</th>
                <th scope="col">Staff Name</th>
                <th scope="col">Lesson Plan Created</th>
                <th scope="col" colspan="2">Action</th>
            </tr>
        </thead>
        <tbody>
        {% if course_staff_details_list %}
            {% for course_staff_details in course_staff_details_list %}
                    <tr style="text-align: center;">
                        <td>
                            <input type="checkbox" class="form-check-input lesson-plan-course-clone" onclick="lessonPlanPage.lessonPlanSelectCheckbox(this)" {% if not lesson_plan_admin_access %} disabled{% endif %}>
                            <label class="form-check-label"> {{ forloop.counter }}</label>
                            <p class="course-staff-details-info-json" style="display:none;"> {{course_staff_details|jsonstr}} </p>
                        </td>
                        <td>
                            {{course_staff_details.courseName}}
                        </td>
                        <td>
                            {{course_staff_details.courseType}}
                        </td>
                        <td>
                            {% if course_staff_details.staffId %}
                                {{course_staff_details.staffName}}({{course_staff_details.staffInstituteId}})
                            {% else %}
                                - 
                            {% endif %}
                        </td>
                        <td>
                            <p class="lesson-plan-id" style="display:none;">{{course_staff_details.lessonPlanId}}</p>
                            {% if course_staff_details.lessonPlanId %}
                                Yes
                            {% else %}
                                No 
                            {% endif %}
                        </td>

                        {# Actions Section #}
                        <td style="width:10%;">
                            <button type="button" class="btn btn-outline-warning dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                Details
                            </button>
                            <div class="dropdown-menu user-actions-options dropdown-menu-right">
                                <a class="dropdown-item lesson-plan-details">
                                    Lesson Plan
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item staff-response-details  
                                {% if not lesson_plan_staff_response or not course_staff_details.lessonPlanId %} disabled{% endif %}"
                                {% if not lesson_plan_staff_response or not course_staff_details.lessonPlanId %} style="pointer-events: none; color: #737D85;"{% endif %}>
                                    Staff Response
                                </a>
                            </div>                            
                        </td>
                        <td style="width:10%;">
                            <button type="button" class="btn btn-outline-danger delete-lesson-plan" aria-haspopup="true" aria-expanded="false"
                                {% if not course_staff_details.lessonPlanId %} disabled {% endif %}>
                                Delete
                            </button>
                        </td>
                    </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="8">No Course are available for the selected Class.</td>
            </tr>
        {% endif %}
        </tbody>
    </table>
</div>