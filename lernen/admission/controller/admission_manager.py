import time
import json
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

def add_new_student(user_login_view, institute_unique_code, add_student_payload, document_name, document):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    add_student_payload['studentPayload']['instituteId'] = institute_id
    # will be taken from UI if student is getting admitted but not new admission
    # add_student_payload['studentPayload']['newAdmission'] = True
    if document is None :
        response = restclient.upload_file(user_login_view, "/2.0/student/add-student/" +"?institute_id="+str(institute_id) + "&user_id="+user['uuid'], {'add_student_payload' : json.dumps(add_student_payload)})
    else:
        response = restclient.upload_file(user_login_view, "/2.0/student/add-student/" +"?institute_id="+str(institute_id) + "&user_id="+user['uuid'], {'add_student_payload' : json.dumps(add_student_payload), 'file' : document, 'documentName' : document_name})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Student Added Successfully!!!" , 'student' : response.get_data()}

def add_json_value(student):
    student['json_value'] = json.dumps(student)
    return student


# def _get_search_id(student):
#     search_id = ""
#     if(student['studentBasicInfo']['name'] is not None):
#         search_id += str(student['studentBasicInfo']['name']) + "|"
#     if(student['studentBasicInfo']['admissionNumber'] is not None):
#         search_id += str(student['studentBasicInfo']['admissionNumber'])+ "|"
#     if(student['studentBasicInfo']['gender'] is not None):
#         search_id += str(student['studentBasicInfo']['gender'])+ "|"
#     return search_id.lower()

def update_student(user_login_view, institute_unique_code, update_student_payload, academic_session_id, document_name, document):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    update_student_payload['instituteId'] = institute_id
    if document is None :
        response = restclient.upload_file(user_login_view, "/2.0/student/update-student/"+str(academic_session_id)+"?institute_id="+str(institute_id) + "&user_id="+user['uuid'], {'update_student_payload' : json.dumps(update_student_payload)})
    else:
        response = restclient.upload_file(user_login_view, "/2.0/student/update-student/"+str(academic_session_id)+"?institute_id="+str(institute_id) + "&user_id="+user['uuid'], {'update_student_payload' : json.dumps(update_student_payload), 'file' : document, 'documentName' : document_name})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Student Updated Successfully!!!" }


def admit_student(user_login_view, institute_unique_code, enroll_student_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    entityDiscountStructurePayload = enroll_student_payload['entityDiscountStructurePayload']
    if entityDiscountStructurePayload:
        for entityFeeAssignmentPayload in entityDiscountStructurePayload['entityFeeAssignmentPayloads']:
            if entityFeeAssignmentPayload['feeEntity'] == "INSTITUTE":
                entityFeeAssignmentPayload['entityId'] = institute_id
    transportAssignmentPayload = enroll_student_payload['transportAssignmentPayload']
    if transportAssignmentPayload:
        transportAssignmentPayload['instituteId'] = institute_id
    response = restclient.put(user_login_view, "/2.0/student/admit-student?institute_id="+str(institute_id) + "&user_id="+user['uuid'], enroll_student_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Student Enrolled Successfully!!!" }

def get_student_tc_pdf(user_login_view, institute_unique_code, student_id, student_tc_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file_with_payload_error_reason(user_login_view, "/2.0/student/"+student_id+"/pdf-tc?institute_id="+str(institute_id), student_tc_payload)

def generate_student_tc_pdf(user_login_view, institute_unique_code, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/student/"+student_id+"/generate-pdf-tc?institute_id="+str(institute_id))

def get_admission_form_pdf(user_login_view, institute_unique_code, student_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/student/"+student_id+"/admission-form/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))

def get_board_registration_form_pdf(user_login_view, institute_unique_code, student_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/student/"+student_id+"/board-registration-form/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))

def get_birthday_certificate(user_login_view, institute_unique_code, academic_session_id,student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/student/"+student_id+"/birthday-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))

def get_study_certificate(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-study-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_bonafide_certificate(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-bonafide-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_tuition_fees_certificate(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-tuition-fees-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_character_certificate(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-character-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_promotion_certificate(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-promotion-certificate?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_books_receipt(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/student/"+str(student_id)+"/pdf-books-receipt?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def get_static_admission_form_pdf(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/student/static/admission-form/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))

def get_verification_restrict_enrollment(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/enrollment/verfiy-details/" + str(student_id) + "?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "No Fees Pending, can proceed to Enroll Student!"}

def get_enrollment_assign_payload(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/enrollment/assign-details/" + str(student_id) + "?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))
    return response.get_data()

def get_sibling_list(user_login_view, institute_unique_code, text):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/siblings/" + str(institute_id)+"?search_text="+text)
    return response.get_data()

def add_siblings(user_login_view, institute_unique_code, is_student_level_action, student_sibling_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_sibling_payload['instituteId'] = institute_id
    response = restclient.post(user_login_view, "/2.0/student/siblings?institute_id="+str(institute_id) + "&user_id="+user['uuid'] + "&is_student_level_action="+str(is_student_level_action), student_sibling_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sibling Details Added Successfully!!!" }

def update_siblings(user_login_view, institute_unique_code, is_student_level_action, student_sibling_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_sibling_payload['instituteId'] = institute_id
    response = restclient.put(user_login_view, "/2.0/student/siblings-details?institute_id="+str(institute_id) + "&user_id="+user['uuid'] + "&is_student_level_action="+str(is_student_level_action), student_sibling_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sibling Details Updated Successfully!!!" }

def delete_sibling_details(user_login_view, institute_unique_code, sibling_group_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/student/sibling-group/"+str(sibling_group_id) + "?user_id="+user['uuid'] + "&institute_id="+str(institute_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sibling Details Deleted Successfully!!!"}

def get_student_tc_details(user_login_view, institute_unique_code, student_id, reset):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/"+student_id+"/tc-details?institute_id="+str(institute_id)+"&reset="+str(reset))
    return response.get_data()

def get_admission_home_stats(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/student-admission-stats/" + str(academic_session_id) +"?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()

def get_student_sibling_details(user_login_view, institute_unique_code, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/student-sibling-details/" + str(student_id) + "?institute_id="+str(institute_id))
    return response.get_data()

def get_student_details(user_login_view, institute_unique_code, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/student-details-without-session/" + str(student_id))
    return response.get_data()

def get_auto_suggest_house_details(user_login_view, institute_unique_code, academic_session_id, auto_suggest_house_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/student/auto-suggest-house?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id), auto_suggest_house_payload)
    return response.get_data()

def get_sibling_list_with_session(user_login_view, institute_unique_code, text, academic_session_id, status):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/siblings-with-session/" + str(institute_id)+"?search_text="+text + "&academic_session_id="+str(academic_session_id) + "&status="+str(status))
    return response.get_data()

def get_student_sibling_details_with_session(user_login_view, institute_unique_code, student_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/student-sibling-details-with-session/" + str(student_id) + "?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id))
    return response.get_data()

def get_session_enquiry_details(user_login_view, institute_unique_code, academic_session_id, status, offset, limit, filter_date):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']

    response = restclient.get(user_login_view, "/2.0/enquiry/enquiry-details-with-pagination?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid']+"&enquiry_status_str="+str(status)+"&limit="+str(limit)+"&offset="+str(offset)+"&follow_up_date="+str(filter_date))
    if not response.is_success():
        return None
    return _create_enquiry_details_pagination_response(response.get_data())


def _create_enquiry_details_pagination_response(enquiry_details_with_pagination):
    enquiry_details = []
    result = enquiry_details_with_pagination.get('result') or []

    for enquiry_detail in result:
        enquiry_details.append(enquiry_detail)
    return {'enquiry_detail' : enquiry_details_with_pagination['result'], 'enquiry_details' : enquiry_details, 'paginationInfo' : enquiry_details_with_pagination['paginationInfo']}


def update_enquiry_status(user_login_view, institute_unique_code, enquiryDetailsPayload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/enquiry/enquiry-status?institute_id="+str(institute_id)+"&user_id="+user['uuid'], enquiryDetailsPayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Enquiry Status Updated Successfully!!!"}

def get_enquiry_detail_with_enquiry_id(user_login_view, institute_unique_code, academic_session_id, enquiry_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/enquiry/enquiry-detail/"+str(enquiry_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid']).get_data()







