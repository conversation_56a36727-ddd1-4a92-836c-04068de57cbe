from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from .controller.fee_config_manager import *
from .controller.discount_manager import *
from .controller.fee_assignment_manager import *
from .controller.fee_payment_manager import *
from core.controller.user.authentication import *
from core.controller.utils.tracking_events import *
from core.controller.utils.template_manager import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.reports_manager import *
from core.controller.utils.authorised_actions import *
from datetime import date
from math import *
from core.controller.utils.authorised_actions import *
from institute_management.controller.institute_management_manager import *
import time
import datetime
from datetime import date
from datetime import datetime

MODULE_NAME = 'FEES'

def authorized_user_session(request):
	return authorized_module(request,'FEES')

def fees_landing_page_view(request):
    return render(request, 'fees/index.html',{})

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "FEE_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		user_type = user_login_view['user']['userType']
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		tutorial_videos = get_tutorial_video_details(MODULE_NAME, institute_unique_code, user_type)
		return render(request, 'fees/layouts/default-fees-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes' : app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data': payment_status_data, 'view_only_access': view_only_access, 'bell_notification_count' : bell_notification_count, 'web_ui_preferences' : web_ui_preferences, 'tutorial_videos': tutorial_videos })

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		class_payment_stats = get_class_payment_stats(user_login_view, institute_unique_code, current_session['academicSessionId'])
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_HOME_STATS)
		selected_date_time = date.today()
		current_date = selected_date_time.strftime("%d-%b-%Y")
		return render(request, 'fees/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session,'class_payment_stats':class_payment_stats, 'web_ui_preferences' : web_ui_preferences, 'view_fees_stats' : view_fees_stats, 'show_today_stats' : True, 'date' : current_date})


def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		class_payment_stats = get_class_payment_stats(user_login_view, institute_unique_code, academic_session_id)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_HOME_STATS)
		return render(request, 'fees/layouts/dashboard-session-content.html',{'class_payment_stats':class_payment_stats, 'user': user_login_view['user'], 'web_ui_preferences' : web_ui_preferences, 'view_fees_stats' : view_fees_stats, 'show_today_stats' : True})


def date_wise_class_payment_stats_view(request, institute_unique_code, academic_session_id, selected_date):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		class_payment_stats = get_date_wise_class_payment_stats(user_login_view, institute_unique_code, academic_session_id, selected_date)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_HOME_STATS)
		return render(request, 'fees/layouts/dashboard-session-content.html',{'class_payment_stats':class_payment_stats, 'user': user_login_view['user'], 'web_ui_preferences' : web_ui_preferences, 'view_fees_stats' : view_fees_stats, 'show_today_stats' : False})


def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		if(status == 'undefined'):
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""))
		else:
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), "", request.GET.get("status",""))
		return render(request, 'core/student/live_search_student_list.html',{"students":students})

def fee_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)

		session_fees = get_all_fee_configurations(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'fees/fee_config/fee_details.html',{'academic_years':academic_years,'session_fees' : session_fees,'current_session':current_session})


def fee_config_list_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		session_fees = get_all_fee_configurations(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_config/fee_list.html',{'session_fees' : session_fees})

@csrf_exempt
def add_regular_fee_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		regular_fees = {"sessionId":request.POST["sessionId"],"duration":request.POST["duration"],"fineApplicable":request.POST["fineApplicable"], "transferToWallet":request.POST["transferToWallet"]}
		response_data = add_regular_fee_config(user_login_view, institute_unique_code, regular_fees)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_regular_fees_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_regular_fee_config(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def add_special_fee_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		special_fee_data = json.loads(request.POST['specialFeeData'])
		response_data = add_special_fee(user_login_view, institute_unique_code, special_fee_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_special_fees_view(request, institute_unique_code, fee_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_special_fee_config(user_login_view, institute_unique_code, fee_id)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def add_one_time_fee_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		onetime_fee_data = json.loads(request.POST['oneTimeFeeData'])
		response_data = add_one_time_fee(user_login_view, institute_unique_code, onetime_fee_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_one_time_fees_view(request, institute_unique_code, fee_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_one_time_fee_config(user_login_view, institute_unique_code, fee_id)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

def fee_head_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_categories = get_fee_categories(user_login_view, institute_unique_code)
		fee_heads = get_fee_heads(user_login_view, institute_unique_code)
		return render(request, 'fees/fee_config/fee_head_details.html',{'fee_categories':fee_categories,'fee_heads':fee_heads})

@csrf_exempt
def add_fee_head_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_head_data = json.loads(request.POST['feeHeadData'])
		response_data = add_fee_head(user_login_view, institute_unique_code, fee_head_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})


def fee_head_list_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_heads = get_fee_heads(user_login_view, institute_unique_code)
		return render(request, 'fees/fee_config/fee_head_list.html',{'fee_heads' : fee_heads})


def discount_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_heads = get_fee_heads(user_login_view, institute_unique_code)
		discounts = get_discounts(user_login_view, institute_unique_code)
		return render(request, 'fees/discount/discount_details.html',{'fee_heads' : fee_heads, 'discounts' : discounts, 'feeHeadsJson' : json.dumps(fee_heads)})

@csrf_exempt
def add_discount_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		discount_info = json.loads(request.POST['discountInfo'])
		response_data = add_discount(user_login_view, institute_unique_code, discount_info)
		return render(request, 'fees/discount/status_modal.html',{"data":response_data})

def get_discount_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		discounts = get_discounts(user_login_view, institute_unique_code)
		return render(request, 'fees/discount/discount_list.html',{'discounts' : discounts})


########################## Fees Assignment ##############################

def fee_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# fee_heads = get_fee_heads()
		session_authorized_fee_assignments = get_authorized_fee_assignments_with_grid(user_login_view, institute_unique_code, current_session['academicSessionId'])
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'fees/fee_assignment/main_screen.html',{'academic_years':academic_years,'current_session':current_session,'standards':standards, 'session_authorized_fee_assignments':session_authorized_fee_assignments})

def class_fee_assignment_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		# fee_heads = get_fee_heads()
		session_authorized_fee_assignments = get_authorized_fee_assignments_with_grid(user_login_view, institute_unique_code, session_id)
		standards = get_standards(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_assignment/class_assignment.html',{'standards':standards, 'session_authorized_fee_assignments':session_authorized_fee_assignments})

def student_fee_assignment_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		# fee_heads = get_fee_heads()
		session_authorized_fee_assignments = get_authorized_fee_assignments_with_grid(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_assignment/student_assignment.html',{'session_authorized_fee_assignments' : session_authorized_fee_assignments})

def bulk_student_fee_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_heads = get_fee_heads(user_login_view, institute_unique_code)
		standards = get_standards_without_section(user_login_view, institute_unique_code)
		return render(request, 'fees/fee_assignment/bulk_student_assignment.html',{'fee_heads' : fee_heads, 'standards':standards})

@csrf_exempt
def get_bulk_fee_assignment_status_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		studentIds = json.loads(request.POST['studentIds'])
		bulkAssignmentFeesStatus = get_bulk_fee_assignment_status(user_login_view, institute_unique_code, studentIds,session_id)
		return render(request, 'fees/fee_assignment/bulk_student_fee_head_assignment.html',{'bulkAssignmentFeesStatus' : bulkAssignmentFeesStatus})

def get_class_students_view(request, institute_unique_code, session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		class_students = get_class_students(user_login_view, institute_unique_code, session_id, standard_id)
		return render(request, 'fees/fee_assignment/class_student_list.html',{'class_students' : class_students})

def get_entity_fee_assignment_view(request, institute_unique_code, session_id, entity, entity_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student = None
		if(entity == "STUDENT"):
			student = get_student(user_login_view, institute_unique_code, session_id,entity_id)
			if student is None:
				return render(request, 'fees/fee_assignment/assigned_fee_list.html',{})
			else:
				entity_id = student['studentId']
		# session_authorized_fee_assignments = get_authorized_fee_assignments_with_grid(session_id)
		session_assigned_fees = get_fee_assignment(user_login_view, institute_unique_code, session_id,entity,entity_id)
		# session_unassigned_authorized_fee_assignments = get_unassigned_authorized_fee_assignments(session_authorized_fee_assignments['authorized_fee_assignments'],session_assigned_fees['all_assigned_fees'])
		# session_fees = get_all_fee_configurations(session_id)
		# session_unassigned_fees = get_unassigned_fees(session_fees['all_fees'],session_assigned_fees['all_assigned_fees'] )
		return render(request, 'fees/fee_assignment/assigned_fee_list.html',{'session_assigned_fees' : session_assigned_fees, 'student' : student})

def get_entity_fee_assignment_v2_view(request, institute_unique_code, session_id, entity, entity_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student = None
		if(entity == "STUDENT"):
			student = get_student(user_login_view, institute_unique_code, session_id,entity_id)
			if student is None:
				return render(request, 'fees/fee_assignment/assigned_fee_list.html',{})
			else:
				entity_id = student['studentId']

		session_assigned_fees = get_fee_assignment_v2(user_login_view, institute_unique_code, session_id,entity,entity_id, 'FEES')

		return render(request, 'fees/fee_assignment/assigned_fee_list_v2.html',{'session_assigned_fees' : session_assigned_fees, 'student' : student})


@csrf_exempt
def add_fee_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_assignment_info = json.loads(request.POST['feeAssignmentInfo'])
		response_data = add_fee_assignment(user_login_view, institute_unique_code, fee_assignment_info)
		return render(request, 'fees/fee_assignment/status_modal.html',{"data":response_data})

def generate_fee_chart_view(request,institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_session_id = request.GET.get("academic_session_id", 0)
		student_Ids = request.GET.get("student_Ids","")
		student_per_page = request.GET.get("student_per_page", 0)
		due_amount = request.GET.get("due_amount", False)
		due_amount_till_today = request.GET.get("due_amount_till_today", False)
		collected_amount = request.GET.get("collected_amount", False)
		disounted_amount = request.GET.get("disounted_amount", False)
		document = None
		document = get_fee_charts(user_login_view, academic_session_id, institute_unique_code, student_Ids, student_per_page, due_amount, collected_amount, disounted_amount, due_amount_till_today)
		if(document['success']):
			response = HttpResponse(document['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+document['file_name']
			return response
		return HttpResponse("<strong>"+document['error_reason']+"</strong>")


########################## Discount Assignment ##############################

def discount_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		discount_structures = get_discount_structures(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'fees/discount_assignment/main_screen.html',{'discount_structures' : discount_structures, 'academic_years':academic_years,'current_session':current_session, 'student' : None})


def get_assigned_discount_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_discount_assignment = get_student_discount_assignment(user_login_view, institute_unique_code, session_id,student_id)
		if student_discount_assignment is None or student_discount_assignment['studentLite'] is None:
			return render(request, 'fees/discount_assignment/student_discount_view.html',{'student' : None, 'instant_discount_assigned' : False})
		instant_discount_assigned = is_instant_discount_assigned(student_discount_assignment)
		discount_structures = get_discount_structures(user_login_view, institute_unique_code, session_id)
		student_unassigned_discounts = get_unassigned_discounts(discount_structures, student_discount_assignment['feeDiscountAssignmentDetailsList'])
		fee_data = get_fee_data(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/discount_assignment/student_discount_view.html',{'student_assigned_discount_structures' : student_discount_assignment['feeDiscountAssignmentDetailsList'], 'student_unassigned_discount_structures' : student_unassigned_discounts, 'student' : student_discount_assignment['studentLite'], 'instant_discount_assigned' : instant_discount_assigned, 'fee_data' : fee_data})

def is_instant_discount_assigned(student_discount_assignment):
	for student_assigned_discount_structure in student_discount_assignment['feeDiscountAssignmentDetailsList']:
		if student_assigned_discount_structure['feeDiscountMetadata']['discountStructureType'] == "SYSTEM":
			return True
	return False


def get_student_fee_discount_list_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_fee_discount_assignment_metadata_list = get_student_fee_discount_list(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/discount_assignment/get_student_discount_details.html',{'student_fee_discount_assignment_metadata_list' : student_fee_discount_assignment_metadata_list})


def get_student_fee_discount_list_by_search_text_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_fee_discount_assignment_metadata_list = get_student_fee_discount_list_by_search_text(user_login_view, institute_unique_code, session_id, request.GET.get("searchText",""), request.GET.get("discountIds",""))
		return render(request, 'fees/discount_assignment/get_student_discount_details.html',{'student_fee_discount_assignment_metadata_list' : student_fee_discount_assignment_metadata_list})

def get_student_fee_discount_assignment_details_view(request, institute_unique_code, student_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_assigned_discounts = get_student_assigned_discounts(user_login_view, institute_unique_code, academic_session_id, student_id)
		return render(request, 'fees/discount_assignment/student_assigned_discount_modal.html',{'student_assigned_discounts' : student_assigned_discounts})



@csrf_exempt
def add_discount_assignment_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		discount_assignment_info = json.loads(request.POST['discountAssignmentInfo'])
		response_data = add_discount_assignment(user_login_view, institute_unique_code, academic_session_id, discount_assignment_info)
		return render(request, 'fees/discount_assignment/status_modal.html',{"data":response_data})


################################## New Student Structure Assignmet ################

def student_discount_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		discounts = get_discounts(user_login_view, institute_unique_code)

		return render(request, 'fees/student_discount_assignment/main_screen.html',{'discounts' : discounts, 'academic_years':academic_years,'current_session':current_session, 'student' : None})


########################################### Payments #################################################

def fee_payment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'fees/payment/main_screen.html',{'academic_years':academic_years,'current_session':current_session})


def get_student_payment_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		authorised_to_add_instant_discount = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, INSTANT_DISCOUNT_ASSIGNMENT)
		authorised_to_update_fine_assigned_amount = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_FINE_ASSIGNED_AMOUNT)
		authorised_to_collect_siblings_fees = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, COLLECT_SIBLING_FEES)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id, True, True)
		fee_followup_details = get_fee_followup_details_by_student_id(user_login_view,institute_unique_code,session_id,student_id)
		status = get_status_of_followup_details(fee_followup_details)
		
		total_due_amount_till_today = 0
		total_due_amount = 0
		
		if student is None:
			return render(request, 'fees/payment/student_payment.html',{'student' : None, 'authorised_to_add_instant_discount' : authorised_to_add_instant_discount, 'authorised_to_update_fine_assigned_amount' : authorised_to_update_fine_assigned_amount, 'authorised_to_collect_siblings_fees' : authorised_to_collect_siblings_fees, 'session_id' : session_id, 'total_due_amount_till_today' : total_due_amount_till_today, 'total_due_amount' : total_due_amount})

		sibling_due_fee_details = remove_current_student(get_sibling_due_fee_details(user_login_view, institute_unique_code, session_id, student_id), student_id)

		current_date = date.today().strftime("%d-%b-%Y")
		student_id = student['studentId']
		student_assigned_discounts = get_student_assigned_discounts(user_login_view, institute_unique_code, session_id, student_id)
		fee_payment_details = get_fee_payment_details(user_login_view, institute_unique_code, session_id,student_id)
		if fee_payment_details is None :
			return render(request, 'fees/payment/student_payment.html',{'student':student, 'institute_metadata_preferences' : institute_metadata_preferences, 'current_date' : current_date, 'authorised_to_add_instant_discount' : authorised_to_add_instant_discount, 'fee_payment_details' : fee_payment_details, 'full_paid_fee_list' : [], 'partially_paid_fee_list' : [], 'unpaid_fee_list' : [], 'due_fee_list' : [], 'student_assigned_discounts' : student_assigned_discounts, 'sibling_due_fee_details' : sibling_due_fee_details, 'authorised_to_update_fine_assigned_amount' : authorised_to_update_fine_assigned_amount, 'authorised_to_collect_siblings_fees' : authorised_to_collect_siblings_fees, 'session_id' : session_id, 'total_due_amount_till_today' : total_due_amount_till_today, 'total_due_amount' : total_due_amount})
		
		transactions_data = get_fee_payment_transactions(user_login_view, institute_unique_code, student_id)
		due_in_other_sessions = False
		for other_session in fee_payment_details['other_sessions']:
			if len(other_session['due_fee_list']) > 0:
				due_in_other_sessions = True
		restrict_session_fees_payment = fetch_restrict_session_fees_payment(user_login_view, institute_unique_code, session_id, student_id)
		is_fee_payment_restricted = is_fee_payment_action_restricted(student, "FEE_PAYMENT_RESTRICTED")
		bank_accounts = get_institute_bank_account_details(user_login_view, institute_unique_code)

		total_due_amount = fee_payment_details['other_sessions_due_amount'] + fee_payment_details['selected_session']['total_due_amount']
		total_due_amount_till_today = fee_payment_details['other_sessions_due_amount_till_today'] + fee_payment_details['selected_session']['total_due_amount_till_today']
		
		return render(request, 'fees/payment/student_payment.html',{'student':student, 'institute_metadata_preferences' : institute_metadata_preferences, 'current_date' : current_date, 'authorised_to_add_instant_discount' : authorised_to_add_instant_discount, 'active_transactions' : transactions_data['active_transactions'], 'cancelled_transactions': transactions_data['cancelled_transactions'],  'fee_payment_details' : fee_payment_details, 'due_in_other_sessions': due_in_other_sessions, 'student_assigned_discounts' : student_assigned_discounts, 'sibling_due_fee_details' : sibling_due_fee_details, 'fee_followup_details':fee_followup_details,'status':status, 'authorised_to_update_fine_assigned_amount' : authorised_to_update_fine_assigned_amount, 'authorised_to_collect_siblings_fees' : authorised_to_collect_siblings_fees, 'session_id' : session_id, 'restrict_session_fees_payment' : restrict_session_fees_payment, 'is_fee_payment_restricted' :  is_fee_payment_restricted, 'bank_accounts' : bank_accounts, 'total_due_amount_till_today' : total_due_amount_till_today, 'total_due_amount' : total_due_amount})

def remove_current_student(sibling_due_fee_details, student_id):
	if not sibling_due_fee_details:
		return sibling_due_fee_details
	final_sibling_due_fee_details = []
	for sibling_due_fee_detail in sibling_due_fee_details:
		if sibling_due_fee_detail['studentId'] != student_id:
			final_sibling_due_fee_details.append(sibling_due_fee_detail)
	return final_sibling_due_fee_details

@csrf_exempt
def collect_fee_payment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_payment_payload = json.loads(request.POST['feePaymentPayload'])
		response_data = collect_fee_payment(user_login_view, institute_unique_code, fee_payment_payload)
		return render(request, 'fees/payment/collect_fee_payment_status_modal.html',{"response":response_data, 'multiple' : False})

@csrf_exempt
def add_fee_followup_view(request,institute_unique_code,session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		follow_up_payload = json.loads(request.POST['followUpPayload'])
		response_data = add_fee_followup(user_login_view, institute_unique_code, session_id,follow_up_payload)
		return render(request, 'fees/follow_up_details/status_modal.html',{"data":response_data})

@csrf_exempt
def add_fee_reminder_followup_view(request,institute_unique_code,session_id,entity_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		follow_up_payload = json.loads(request.POST['followUpPayload'])
		response_data = add_fee_followup(user_login_view, institute_unique_code, session_id,follow_up_payload)
		return render(request, 'fees/reminders/reminder_followup_status_modal.html',{"data":response_data,"student_id":entity_id})

@csrf_exempt
def update_fee_followup_view(request,institute_unique_code,session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_follow_up_payload = json.loads(request.POST['updateFollowUpPayload'])
		response_data = update_fee_followup(user_login_view, institute_unique_code, session_id,update_follow_up_payload)
		return render(request, 'fees/follow_up_details/status_modal.html',{"data":response_data})

@csrf_exempt
def update_reminder_followup_view(request,institute_unique_code,session_id,entity_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_follow_up_payload = json.loads(request.POST['updateFollowUpPayload'])
		response_data = update_fee_followup(user_login_view, institute_unique_code, session_id,update_follow_up_payload)
		return render(request, 'fees/reminders/reminder_followup_status_modal.html',{"data":response_data,"student_id":entity_id})

def payment_transaction_details_view(request, institute_unique_code, transaction_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_transaction_details = get_fee_payment_transaction_details(user_login_view, institute_unique_code, transaction_id)
		total_paid_amount = 0
		total_discount_amount = 0
		for fee_id_data in student_transaction_details['feePaymentTransactionDetails']['feeIdFeeHeadTransactionDetails']:
			for fee_head_amount in fee_id_data['feeHeadTransactionAmountsDetails']:
				total_paid_amount += fee_head_amount['paidAmount']
				total_discount_amount += fee_head_amount['instantDiscount']
		return render(request, 'fees/transactions/fee_payment_transaction_details_modal.html',{"total_paid_amount" : total_paid_amount, "total_discount_amount" : total_discount_amount, "transaction_details":student_transaction_details['feePaymentTransactionDetails'], 'student' : student_transaction_details['student']})

def payment_invoice_view(request, institute_unique_code, transaction_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		invoice = get_fee_payment_invoice(user_login_view, institute_unique_code, transaction_id)
		if invoice is None:
			return HttpResponse("<strong>Invalid Payment Information.</strong>")
		fees_totals = []
		total_paid_amount = 0
		for fee_id_data in invoice['feePaymentTransactionDetails']['feeIdFeeHeadTransactionDetails']:
			fee_paid_amount = 0
			for fee_head_amount in fee_id_data['feeHeadTransactionAmountsDetails']:
				fee_paid_amount += fee_head_amount['paidAmount']
			total_paid_amount += fee_paid_amount
			fees_totals.append({'fees_data' : fee_id_data, 'fee_paid_amount' : fee_paid_amount})
		return render(request, 'fees/payment/invoice.html',{"institute": get_institute_details(user_login_view, institute_unique_codes), "fees_totals" : fees_totals, "invoice" : invoice,'total_paid_amount':total_paid_amount})


def payment_invoice_summary_view(request, institute_unique_code, transaction_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		invoice_summary = get_fee_payment_invoice_summary(user_login_view, institute_unique_code, transaction_id)
		if invoice_summary is None:
			return HttpResponse("<strong>Invalid Transaction Information.</strong>")
		return render(request, 'fees/payment/main_invoice.html',{"institute": get_institute_details(user_login_view, institute_unique_code), "invoice_summary" : invoice_summary})

def payment_invoice_pdf_summary_view(request, institute_unique_code, transaction_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		pdf_invoice = get_fee_payment_pdf_invoice_summary(user_login_view, institute_unique_code, transaction_id)
		if(pdf_invoice is not None):
			response = HttpResponse(pdf_invoice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+pdf_invoice['file_name']
			return response
		return HttpResponse("<strong>Invalid Transaction Information.</strong>")

def bulk_invoice_pdf_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_session_id = request.GET.get("academic_session_id", 0)
		start_date = request.GET.get("start_date", -1)
		end_date = request.GET.get("end_date", -1)
		fee_transaction_status = request.GET.get("fee_transaction_status", "")
		request_data = {"academic_session_id":academic_session_id,"start_date":start_date, "end_date" : end_date, "fee_transaction_status" : fee_transaction_status }
		# report = get_report(user_login_view, institute_unique_code, report_request)
		bulk_pdf_invoice = get_bulk_pdf_invoice_summary(user_login_view, institute_unique_code, request_data)
		if(bulk_pdf_invoice is not None):
			response = HttpResponse(bulk_pdf_invoice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+bulk_pdf_invoice['file_name']
			return response
		return HttpResponse("<strong>Invalid Information.</strong>")

@csrf_exempt
def cancel_payment_transaction_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		fee_cancelled_payload = json.loads(request.POST['feeCancelledPayload'])
		response_data = cancel_fee_payment(user_login_view, institute_unique_code, fee_cancelled_payload)
		return render(request, 'fees/payment/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_fee_followup_view(request,institute_unique_code,academic_session_id,follow_up_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_fee_followup(user_login_view, institute_unique_code, academic_session_id,follow_up_id)
		return render(request, 'fees/follow_up_details/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_fee_reminder_followup_view(request,institute_unique_code,academic_session_id,follow_up_id,entity_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_fee_followup(user_login_view, institute_unique_code, academic_session_id,follow_up_id)
		return render(request, 'fees/reminders/reminder_followup_status_modal.html',{"data":response_data,"student_id":entity_id})


######################Updates#####################

@csrf_exempt
def close_fee_followup_view(request,institute_unique_code,academic_session_id,is_student,status,follow_up_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = close_fee_followup(user_login_view, institute_unique_code, academic_session_id,is_student,status,follow_up_id)
		return render(request, 'fees/follow_up_details/status_modal.html',{"data":response_data})

@csrf_exempt
def	close_fee_remainder_followup_view(request,institute_unique_code,academic_session_id,is_student,status,follow_up_id,entity_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = close_fee_followup(user_login_view, institute_unique_code, academic_session_id,is_student,status,follow_up_id)
		return render(request, 'fees/reminders/reminder_followup_status_modal.html',{"data":response_data,"student_id":entity_id})


@csrf_exempt
def update_fee_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_fee_config_data = json.loads(request.POST['updateFeeConfigData'])
		response_data = update_regular_one_time_fee_config(user_login_view, institute_unique_code, update_fee_config_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def update_special_fee_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_special_fee_config_data = json.loads(request.POST['updateSpecialFeeConfigData'])
		response_data = update_special_fee_config(user_login_view, institute_unique_code, update_special_fee_config_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def update_fee_head_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_fee_head_data = json.loads(request.POST['updateFeeHeadData'])
		response_data = update_fee_head(user_login_view, institute_unique_code, update_fee_head_data)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_fee_head_configuration_view(request, institute_unique_code, fee_head_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_fee_head(user_login_view, institute_unique_code, fee_head_id)
		return render(request, 'fees/fee_config/fee_config_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_discount_configuration_view(request, institute_unique_code, discount_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_discount(user_login_view, institute_unique_code, discount_id)
		return render(request, 'fees/discount/status_modal.html',{"data":response_data})

@csrf_exempt
def update_discount_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_discount_data = json.loads(request.POST['discountInfo'])
		response_data = update_discount(user_login_view, institute_unique_code, update_discount_data)
		return render(request, 'fees/discount/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_discount_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		delete_discount_assignment_data = json.loads(request.POST['deleteDiscountAssignmentInfo'])
		response_data = delete_discount_assignment(user_login_view, institute_unique_code, delete_discount_assignment_data)
		return render(request, 'fees/discount_assignment/status_modal.html',{"data":response_data})

@csrf_exempt
def update_fee_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_fees_assignment_data = json.loads(request.POST['updateFeeAssignmentInfo'])
		response_data = update_fees_assignment(user_login_view, institute_unique_code, update_fees_assignment_data)
		return render(request, 'fees/fee_assignment/status_modal.html',{"data":response_data})


@csrf_exempt
def update_fee_assignment_amounts_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_fees_assignment_amounts_data = json.loads(request.POST['payload'])
		response_data = update_fees_assignment_amounts(user_login_view, institute_unique_code, academic_session_id, update_fees_assignment_amounts_data)
		return render(request, 'fees/fee_assignment/status_modal.html',{"data":response_data})



@csrf_exempt
def delete_fee_assignment_view(request, institute_unique_code, entity_name, entity_id, fee_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_fees_assignment(user_login_view, institute_unique_code, entity_name,entity_id, fee_id)
		return render(request, 'fees/fee_assignment/status_modal.html',{"data":response_data})

@csrf_exempt
def get_all_payment_transactions_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)

		return render(request, 'fees/transactions/main_screen.html',{'academic_years':academic_years, 'current_session':current_session})

def get_fee_followup_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'fees/follow_up_details/main_screen.html',{'academic_years':academic_years, 'current_session':current_session})




def get_all_payment_transactions_view(request, institute_unique_code, status, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		all_payment_transactions_data_with_pagination_info = get_session_fee_payment_transactions(user_login_view, institute_unique_code,current_session['academicSessionId'], status, offset, limit)
		pagination_info = all_payment_transactions_data_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)
		return render(request, 'fees/transactions/main_screen_container.html',{'pagination' : True, 'academic_years':academic_years, 'current_session':current_session, 'active_transactions' : all_payment_transactions_data_with_pagination_info['active_transactions'], 'cancelled_transactions': all_payment_transactions_data_with_pagination_info['cancelled_transactions'], "pagination_details":pagination_details})

@csrf_exempt
def get_session_payment_transactions_view(request, institute_unique_code, academic_session_id, status, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		text = request.GET.get("text","")
		all_payment_transactions_data_with_pagination_info = get_session_fee_payment_transactions(user_login_view, institute_unique_code, academic_session_id, status, offset, limit, text)
		pagination_info = all_payment_transactions_data_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)

		return render(request, 'fees/transactions/main_screen_container.html',{'pagination' : True, 'active_transactions' : all_payment_transactions_data_with_pagination_info['active_transactions'], 'cancelled_transactions': all_payment_transactions_data_with_pagination_info['cancelled_transactions'], "pagination_details" : pagination_details})

def get_student_fee_details_view(request,institute_unique_code,academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_fee_follow_up_details = get_student_fee_details(user_login_view,institute_unique_code,academic_session_id)
		return render(request, 'fees/follow_up_details/main_screen_container.html',{'pagination':True,'active_follow_up' : student_fee_follow_up_details['active_follow_up'],'inactive_follow_up' : student_fee_follow_up_details['inactive_follow_up']})


def get_student_followup_details_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_followup_details = get_fee_followup_details_by_student_id(user_login_view,institute_unique_code,academic_session_id,student_id)
		status = get_status_of_followup_details(fee_followup_details)
		return render(request,'fees/follow_up_details/follow_up_screen_container.html',{'fee_followup_details':fee_followup_details,'studentId':student_id,'status':status})

def get_student_followup_remainder_details_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		fee_followup_details = get_fee_followup_details_by_student_id(user_login_view,institute_unique_code,academic_session_id,student_id)
		status = get_status_of_followup_details(fee_followup_details)
		return render(request,'fees/reminders/reminder_screen_container.html',{'fee_followup_details':fee_followup_details,'studentId':student_id,'status':status})

def get_status_of_followup_details(fee_followup_details):
	status = "true"
	if fee_followup_details != None:
		for fee_followup_detail in fee_followup_details:
			if fee_followup_detail['followUpStatus'] == "INACTIVE":
				status = "true"
			else:
				status = "false"
				break
	return status

def reminder_view(request, institute_unique_code):
     if authorized_user_session(request) and request.is_ajax():
        return render(request, 'fees/payment/reminder.html',{})


def reports_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		headers = get_report_headers(user_login_view, institute_unique_code, MODULE_NAME)
		report_header_columns = get_report_headers_for_display(headers)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		all_session_fees = get_all_session_fee_configurations(user_login_view, institute_unique_code)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		fee_head_list = get_fee_heads(user_login_view, institute_unique_code)
		return render(request, 'fees/reports/main_screen.html',{'academic_years':academic_years, 'report_header_columns' : report_header_columns, 'all_session_fees' : all_session_fees, 'standards': standards, 'fee_head_list' : fee_head_list, 'current_session' : current_session})

def get_session_standards_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'fees/reports/standard_section.html',{'standards':standards})

def generate_report_view(request, institute_unique_code, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		transactionType = request.GET.get("transactionType")
		transaction_mode = request.GET.get("transactionMode")
		report_request = {"report_type":report_type,"academic_session_id":request.GET.get("academic_session_id",0),"required_headers":request.GET.get("requiredHeaders",""), "multiple_academic_session_ids" : request.GET.get("requiredMultiSession",""), "start_date":request.GET.get("reportStartDate",-1), "end_date" : request.GET.get("reportEndDate",-1), "fee_due_date" : request.GET.get("feeDueDate",-1), 'fee_ids' : request.GET.get("fee_ids",""), 'required_standards' : request.GET.get("requiredStandards",""), 'studentStatus' :  request.GET.get("studentStatus",""), "download_format" : downloadFormat, "fee_head_ids" : request.GET.get("fee_head_ids",""), "fee_payment_transaction_status" : transactionType, "fee_report_data_type" : request.GET.get("feeReportDataType"), "transaction_mode" : transaction_mode, "hide_student_with_zero_fees_assignment" : request.GET.get("hideStudentWithZeroFeesAssignment"), "report_data_visibility" : request.GET.get("reportDataVisibility"), "show_student_with_zero_wallet_balance": request.GET.get("showStudentWithZeroWalletBalance")}
		if download_report :
			report = get_report(user_login_view, institute_unique_code, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_report_data(user_login_view, institute_unique_code, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})



def assigned_discounts_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'fees/discount_assignment/assigned_discount_details.html',{'academic_years':academic_years, 'current_session':current_session})


def due_fees_reminder_screen_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'fees/reminders/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'standards': standards})

def load_bulk_student_screen_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'fees/reminders/bulk_students_screen.html',{'standards': standards})


def due_fees_students_view(request, institute_unique_code, academic_session_id, due_date, include_fine):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		required_standards = request.GET.get("requiredStandards","")
		include_past_sessions = request.GET.get("includePastSessions", False)
		due_fees_students = get_due_fees_students(user_login_view, institute_unique_code, academic_session_id,due_date, include_fine, required_standards, include_past_sessions)
		return render(request, 'fees/reminders/student_list.html',{'due_fees_students':due_fees_students, 'include_fine' : include_fine == "true"})

def bulk_reminder_template_selection_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		audio_templates = get_audio_voice_call_templates(user_login_view, institute_unique_code, True, "DUE_FEE")
		push_notification_preferences = get_push_notification_preferences(user_login_view, institute_unique_code)
		template_id = push_notification_preferences['dueFeeReminderPushNotificationTemplateId']
		template_details = get_templates_by_id(user_login_view, institute_unique_code, template_id)
		return render(request, 'fees/reminders/bulk_reminder_template_selection_modal.html',{'audio_templates':audio_templates, 'template_details' : template_details})


@csrf_exempt
def send_due_payment_reminders_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		send_reminder_payload = json.loads(request.POST['sendRemindersPayload'])
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		send_reminder_payload['academicSessionId'] = current_session['academicSessionId']
		response_data = send_due_payment_reminders(user_login_view, institute_unique_code, send_reminder_payload)
		return render(request, 'fees/reminders/status_modal.html',{"data":response_data})

def notification_history_homepage_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)

		return render(request, 'fees/reminders/notification_history_homepage.html',{'academic_years':academic_years, 'current_session':current_session})

def notification_history_view(request, institute_unique_code, academic_session_id, delivery_mode, notificationHistoryTab, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)

		if(delivery_mode == 'APP'):
			mobile_notification_history = get_mobile_notification_history(user_login_view, institute_unique_code, offset, limit)
			pagination_details = None
			mobile_notifications = []
			if mobile_notification_history:
				pagination_details = get_pagination_details(mobile_notification_history['paginationInfo'])
				mobile_notifications = mobile_notification_history['result']
			return render(request, 'fees/reminders/mobile_notification_history.html',{'mobile_notifications' : mobile_notifications, 'pagination_details' : pagination_details})

		batch_notifications = []
		individual_notifications = []
		pagination_details = None
		if notificationHistoryTab == "BATCH":
			batch_notification_history = get_batch_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit)
			pagination_details = get_pagination_details(batch_notification_history['paginationInfo'])
			batch_notifications = batch_notification_history['result']
		else:
			individual_notification_history = get_individual_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit)
			pagination_details = get_pagination_details(individual_notification_history['paginationInfo'])
			individual_notifications = individual_notification_history['result']

		if(delivery_mode == 'SMS'):
			return render(request, 'fees/reminders/sms_notification_history.html',{'batch_notifications': batch_notifications, 'individual_notifications' : individual_notifications, 'pagination_details' : pagination_details})
		elif (delivery_mode == 'CALL'):
			return render(request, 'fees/reminders/voice_notification_history.html',{'batch_notifications': batch_notifications, 'individual_notifications' : individual_notifications, 'pagination_details' : pagination_details})


def batch_notification_details_view(request, institute_unique_code, batch_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		batch_notification_details = get_batch_notification_details(user_login_view, institute_unique_code, "STUDENT", batch_id)
		return render(request, 'fees/reminders/batch_notifications_view_modal.html',{'batch_notification_details':batch_notification_details})


########## Demand notice ##################

@csrf_exempt
def submit_demand_notice_students_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		demand_notice_payload = json.loads(request.POST['demandNoticePayload'])
		demand_notice_request_id = store_demand_notice_students(demand_notice_payload)
		return HttpResponse(demand_notice_request_id)

def generate_demand_notice_view(request, institute_unique_code, demand_notice_request_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		pdf_demand_notice = get_pdf_demand_notice(user_login_view, institute_unique_code, demand_notice_request_id)
		if(pdf_demand_notice is not None):
			response = HttpResponse(pdf_demand_notice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+pdf_demand_notice['file_name']
			return response
		return HttpResponse("<strong>Invalid information submitted.</strong>")


#### Fee Structure

def fee_structure_config_without_session_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		session_id = current_session['academicSessionId']

		standards = get_standards(user_login_view, institute_unique_code, session_id)
		session_authorized_fee_assignments = get_authorized_fee_assignments(user_login_view, institute_unique_code, session_id)
		fee_structures = get_fee_structure_configurations(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_structure/main_screen.html',{'academic_years':academic_years,'fee_structures' : fee_structures,'current_session':current_session, 'session_authorized_fee_assignments' : session_authorized_fee_assignments, 'standards' : standards})


def fee_structure_config_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		session_authorized_fee_assignments = get_authorized_fee_assignments(user_login_view, institute_unique_code, session_id)
		standards = get_standards(user_login_view, institute_unique_code, session_id)
		fee_structures = get_fee_structure_configurations(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/fee_structure/fee_structure_list.html',{'fee_structures' : fee_structures,'session_authorized_fee_assignments' : session_authorized_fee_assignments, 'standards' : standards})


@csrf_exempt
def add_fee_structure_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		add_structure_json = json.loads(request.POST['feeAssignmentStructurePayload'])
		response_data = add_fee_structure(user_login_view, institute_unique_code, add_structure_json, session_id)
		return render(request, 'fees/fee_structure/fee_structure_status_modal.html',{"data":response_data})


@csrf_exempt
def delete_fee_structure_view(request, institute_unique_code, structure_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_fee_structure(user_login_view, institute_unique_code, structure_id, academic_session_id)
		return render(request, 'fees/fee_structure/fee_structure_status_modal.html',{"data":response_data})

@csrf_exempt
def update_fee_structure_view(request, institute_unique_code, structure_id, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_structure_json = json.loads(request.POST['feeAssignmentStructurePayload'])
		response_data = update_fee_structure(user_login_view, institute_unique_code, structure_id, update_structure_json, session_id)
		return render(request, 'fees/fee_structure/fee_structure_status_modal.html',{"data":response_data})


# Discount Structure

def discount_structure_without_session_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		session_id = current_session['academicSessionId']
		standards = get_standards(user_login_view, institute_unique_code, session_id)
		fee_data = get_fee_data(user_login_view, institute_unique_code, session_id)
		discount_structures = get_discount_structures(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/discount_structure/main_screen.html',{'academic_years':academic_years, 'discount_structures' : discount_structures, 'fee_data' : fee_data, 'current_session':current_session, 'standards' : standards})

def discount_structure_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, session_id)
		fee_data = get_fee_data(user_login_view, institute_unique_code, session_id)
		discount_structures = get_discount_structures(user_login_view, institute_unique_code, session_id)
		return render(request, 'fees/discount_structure/discount_structure_list.html',{'discount_structures' : discount_structures, 'fee_data' : fee_data, 'standards' : standards})

@csrf_exempt
def add_discount_structure_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		add_structure_json = json.loads(request.POST['discountAssignmentStructurePayload'])
		response_data = add_discount_structure(user_login_view, institute_unique_code, add_structure_json, session_id)
		return render(request, 'fees/discount_structure/discount_structure_status_modal.html',{"data":response_data})

@csrf_exempt
def add_student_discount_structure_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		add_structure_json = json.loads(request.POST['discountAssignmentStructurePayload'])
		response_data = add_student_discount_structure(user_login_view, institute_unique_code, add_structure_json, session_id, student_id)
		return render(request, 'fees/discount_structure/discount_structure_status_modal.html',{"data":response_data})

@csrf_exempt
def update_discount_structure_view(request, institute_unique_code, structure_id, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		update_structure_json = json.loads(request.POST['discountAssignmentStructurePayload'])
		response_data = update_discount_structure(user_login_view, institute_unique_code, structure_id, update_structure_json, session_id)
		return render(request, 'fees/discount_structure/discount_structure_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_discount_structure_view(request, institute_unique_code, structure_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_discount_structure(user_login_view, institute_unique_code, structure_id, academic_session_id)
		return render(request, 'fees/discount_structure/discount_structure_status_modal.html',{"data":response_data})


@csrf_exempt
def recharge_student_wallet_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		wallet_recharge_payload = json.loads(request.POST['walletRechargePayload'])
		response_data = recharge_student_wallet(user_login_view, institute_unique_code, wallet_recharge_payload)
		return render(request, 'fees/payment/wallet_recharge_status_modal.html',{"data":response_data})


def wallet_transactions_view(request, institute_unique_code, user_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		wallet_transactions = get_wallet_transactions(user_login_view, institute_unique_code, user_id)
		return render(request, 'fees/payment/user_wallet_transactions_modal.html',{'wallet_transactions' : wallet_transactions})


def get_channel_credits_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		channel_credits_list = get_counters(user_login_view, institute_unique_code, "SMS_COUNTER,AUDIO_VOICE_CALL_COUNTER")
		return render(request, 'core/utils/channel_credits_header_row_view.html',{'channel_credits_list' : channel_credits_list})

def get_sms_preferences_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		sms_preferences = load_sms_preferences(user_login_view, institute_unique_code)
		return render(request, 'core/utils/sms_count.html',{'sms_counter' : '', 'sms_preferences' : sms_preferences})

def fee_structure_change_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'fees/fee_assignment/fee_structure.html',{'academic_years':academic_years,'current_session':current_session})

def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		if(status == 'undefined'):
			students = lite_v2_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""))
		else:
			students = lite_v2_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), request.GET.get("status",""))
		return render(request, 'core/student/live_search_student_list.html',{"students":students, "search_version" : "v2"})

def get_student_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id)
		eligibility = check_fee_structure_change_eligibility(user_login_view, institute_unique_code, session_id, student_id)
		return render(request, 'fees/fee_assignment/student_details.html',{'student' : student, 'eligibility' : eligibility})

def get_fee_structure_configuration(request, institute_unique_code, academic_session_id, student_id):
	user_login_view = get_user_login_view(request)
	student = get_student(user_login_view, institute_unique_code, academic_session_id, student_id)
	standard_id = student['studentAcademicSessionInfoResponse']['standard']['standardId']
	fee_structure = get_fee_structure_meta_data(user_login_view, institute_unique_code, standard_id, academic_session_id, "")
	return render(request, 'fees/fee_assignment/fee_structure_change_configuration.html',{'fee_structure' : fee_structure})

@csrf_exempt
def fee_structure_change_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		uuids = json.loads(request.POST['uuids'])
		response_data = fee_structure_change(user_login_view, institute_unique_code, academic_session_id, student_id, uuids)
		return render(request, 'fees/fee_assignment/status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})

def get_follow_up_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id, True)
		fee_followup_details = get_fee_followup_details_by_student_id(user_login_view,institute_unique_code,session_id,student_id)
		status = get_status_of_followup_details(fee_followup_details)
		return render(request, 'fees/payment/follow_up_detail.html',{'student':student, 'fee_followup_details':fee_followup_details,'status':status})

def get_sibling_due_fee_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)

		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		authorised_to_add_instant_discount = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, INSTANT_DISCOUNT_ASSIGNMENT)
		authorised_to_update_fine_assigned_amount = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_FINE_ASSIGNED_AMOUNT)
		authorised_to_collect_siblings_fees = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, COLLECT_SIBLING_FEES)

		sibling_due_fee_details = get_sibling_due_fee_details(user_login_view, institute_unique_code, session_id, student_id)

		return render(request, 'fees/payment/sibling_due_fee_details.html', {'institute_metadata_preferences' : institute_metadata_preferences, 'authorised_to_add_instant_discount' : authorised_to_add_instant_discount, 'authorised_to_update_fine_assigned_amount' : authorised_to_update_fine_assigned_amount, 'authorised_to_collect_siblings_fees' : authorised_to_collect_siblings_fees, 'student_id' : student_id, 'sibling_due_fee_details' : sibling_due_fee_details})

@csrf_exempt
def collect_sibling_fees_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		fee_payment_payload_lite_list = json.loads(request.POST['feePaymentPayloadLiteList'])
		response_data = collect_sibling_fees(user_login_view, institute_unique_code, academic_session_id, fee_payment_payload_lite_list)
		transaction_str = None
		if response_data['success']:
			delimeter = ""
			transaction_str = ""
			for response in response_data['fee_payment_response']:
				if response['transactionId']:
					transaction_str += (delimeter + response['transactionId'])
					delimeter = ","
		return render(request, 'fees/payment/collect_fee_payment_status_modal.html',{"response":response_data, 'multiple' : True, 'transaction_str' : transaction_str})

def payment_bulk_invoice_pdf_summary_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		transaction_ids_str = request.GET.get("transaction_ids_str","")
		pdf_invoice = get_fee_payment_pdf_bulk_invoice_summary(user_login_view, institute_unique_code, transaction_ids_str, academic_session_id)
		if(pdf_invoice is not None):
			response = HttpResponse(pdf_invoice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+pdf_invoice['file_name']
			return response
		return HttpResponse("<strong>Invalid Transaction Information.</strong>")

def is_fee_payment_action_restricted(student, tagged_action):
    if student.get('studentTaggedDetailsList'):
        for tag_detail in student['studentTaggedDetailsList']:
            if tag_detail.get('taggedActions') == tagged_action:
                return True
    return False

def wallet_invoice_pdf_view(request, institute_unique_code, transaction_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		wallet_invoice = get_wallet_pdf_invoice(user_login_view, institute_unique_code, transaction_id)
		if(wallet_invoice['success']):
			response = HttpResponse(wallet_invoice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+wallet_invoice['file_name']
			return response
		return HttpResponse("<strong>"+wallet_invoice['error_reason']+"</strong>")

def fee_challan_pdf_view(request, institute_unique_code, session_id, student_id):
    if authorized_user_session(request):
        user_login_view = get_user_login_view(request)
        fee_challan = get_student_fee_challan(user_login_view, institute_unique_code, session_id, student_id)
        if fee_challan['success']:
            response = HttpResponse(fee_challan['content'], content_type="application/pdf")
            response['Content-Disposition'] = 'filename=' + fee_challan['file_name']
            return response
        return HttpResponse("<strong>" + fee_challan['error_reason'] + "</strong>")

@csrf_exempt
def update_wallet_transaction_amount_view(request, institute_unique_code):
    if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
        user_login_view = get_user_login_view(request)
        update_wallet_transaction_payload = json.loads(request.POST['updateWalletTransactionPayload'])
        response_data = update_wallet_transaction_amount(user_login_view, institute_unique_code, update_wallet_transaction_payload)
        return render(request, 'fees/payment/update_wallet_transaction_status_modal.html', {"data": response_data})

@csrf_exempt
def delete_wallet_transaction_view(request, institute_unique_code):
    if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
        user_login_view = get_user_login_view(request)
        delete_wallet_transaction_payload = json.loads(request.POST['deleteWalletTransactionPayload'])
        response_data = delete_wallet_transaction(user_login_view, institute_unique_code, delete_wallet_transaction_payload)
        return render(request, 'fees/payment/update_wallet_transaction_status_modal.html', {"data": response_data})