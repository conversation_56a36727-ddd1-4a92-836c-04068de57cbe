{% load displaytime %}
{% load json %}
<div class="container-fluid p-0 card">
    <p class="ml-auto mb-0 mt-3 mr-4">
        <a href="../admission-enquiry-form/" target="_blank" class="text-info" style="text-decoration: underline;">Admission Enquiry Form</a>
    </p>
    {%if status == 'ACCEPTED'%}
    <div class="form-row align-items-center ml-2">
    <div class="col-sm-3 my-1">
        <input type="text" class="form-control select-date" id="latest-followup-date"  placeholder="Enter Followup Date ...">
    </div>
    <div class="col-sm-2 my-1">
        <button class="btn btn-success" style="width:100%" onclick="admissionEnquiry.viewEnquiryDetails(true,'ACCEPTED')">Load Enquiry</button>
    </div>
    </div>
    {% endif %}
    <br/>
    <div class="card-body">
        <div class="row">
        </div>
<nav>
    <div class="nav nav-tabs" id="nav-tab" role="tablist">
        <a class="nav-item nav-link active" id="nav-submitted-enquiry-tab" data-toggle="tab" href="#submitted-enquiry-content" role="tab" aria-controls="nav-profile" aria-selected="true" onclick="admissionEnquiry.viewEnquiryDetails(true,'SUBMITTED')">Submitted</a>
        <a class="nav-item nav-link" id="nav-accepted-enquiry-tab" data-toggle="tab" href="#accepted-enquiry-content" role="tab" aria-controls="nav-profile" aria-selected="false" onclick="admissionEnquiry.viewEnquiryDetails(true,'ACCEPTED')">Accepted</a>
        <a class="nav-item nav-link" id="nav-rejected-enquiry-tab" data-toggle="tab" href="#rejected-enquiry-content" role="tab" aria-controls="nav-profile" aria-selected="false" onclick="admissionEnquiry.viewEnquiryDetails(true,'REJECTED')">Rejected</a>
        <a class="nav-item nav-link" id="nav-closed-enquiry-tab" data-toggle="tab" href="#closed-enquiry-content" role="tab" aria-controls="nav-profile" aria-selected="false" onclick="admissionEnquiry.viewEnquiryDetails(true,'CLOSED')">Closed</a>
    </div>
</nav>

<div class="tab-content" id="nav-tabContent">
    <div class="tab-pane fade show active" id="submitted-enquiry-content" role="tabpanel" aria-labelledby="nav-submitted-enquiry-tab">
        <br>
        <div>
            <table class="table table-bordered table-striped datatables-reponsive datatables-reponsive-table submitted-datatables-reponsive" style="min-width:100%;">
                <caption>List of Enquiries</caption>
                <thead>
                <tr>
                    <th style="display:none;"></th>
                    <th scope="col">#</th>
                    <th scope="col" >Tracking ID</th>
                    <th scope="col" >Parent Name</th>
                    <th scope="col" >Child Name</th>
                    <th scope="col" >Contact No.</th>
                    <th scope="col" >Date</th>
                    <th class="no-sort" scope="col">Action</th>
                    <th class="no-sort" > </th>
                    <th class="no-sort" > </th>

                </tr>
                </thead>
                <tbody>
                {% for enquiry in enquiry_details %}
                <tr>
                    <td style="display: none">
                        <p class="enquiry-details-info" style="display:none;"> {{enquiry|jsonstr}} </p>
                    </td>
                    <td scope="row">{{ forloop.counter }}</td>
                    <td>{{enquiry.trackingId}}</td>
                    <td>{{enquiry.guardianName }}</td>
                    <td>{{enquiry.childName }}</td>
                    <td>{{enquiry.guardianContactInfo }}</td>
                    <td>{{enquiry.enquiryDate|print_date}}</td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-info view-enquiry-details" aria-haspopup="true" aria-expanded="false" >
                            View
                        </button>
                    </td>

                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'REJECTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success " aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('ACCEPTED', this);">
                            Accept
                        </button>
                    </td>

                    {% endif %}
                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'ACCEPTED' %}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-danger" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('REJECTED', this);">
                            Reject
                        </button>
                    </td>

                    {% endif %}
                    {%if enquiry.enquiryStatus == 'ACCEPTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.closeEnquiry(this);">
                            Close
                        </button>
                    </td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-secondary" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                           Details
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-warning" aria-haspopup="true" aria-expanded="false"  onclick="followup.addFollowUpDetails('{{enquiry.enquiryId}}', 'HOME')">
                             + Followup
                        </button>
                    </td>
                    {% endif %}

                    {%if enquiry.enquiryStatus == 'CLOSED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                            Details
                        </button>
                    </td>
                    {% endif %}


                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="tab-pane" id="accepted-enquiry-content" role="tabpanel" aria-labelledby="nav-accepted-enquiry-tab">
        <br>
        <div>
            <table class="table table-bordered table-striped datatables-reponsive datatables-reponsive-table accepted-datatables-reponsive w-auto" style="min-width:100%;">
                <caption>List of Enquiries</caption>
                <thead>
                <tr>
                    <th style="display:none;"></th>
                    <th scope="col">#</th>
                    <th scope="col" >Tracking ID</th>
                    <th scope="col" >Parent Name</th>
                    <th scope="col" >Child Name</th>
                    <th scope="col" >Contact No.</th>
                    <th scope="col" >Date</th>
                    <th scope="col" >Followup Date</th>
                    <th scope="col" class="no-sort">Action</th>
                    <th class="no-sort" > </th>
                    <th class="no-sort" > </th>
                    <th class="no-sort" > </th>
                    <th class="no-sort" > </th>

                </tr>
                </thead>
                <tbody>
                {% for enquiry in enquiry_details %}
                <tr>
                    <td style="display: none">
                        <p class="enquiry-details-info" style="display:none;"> {{enquiry|jsonstr}} </p>
                    </td>
                    <td scope="row">{{ forloop.counter }}</td>
                    <td>{{enquiry.trackingId}}</td>
                    <td>{{enquiry.guardianName }}</td>
                    <td>{{enquiry.childName }}</td>
                    <td>{{enquiry.guardianContactInfo }}</td>
                    <td>{{enquiry.enquiryDate|print_date}}</td>
                    <td>
                        {% if enquiry.latestFollowupDate %}
                        {{ enquiry.latestFollowupDate|print_date }}
                        {% else %}
                        -
                        {% endif %}
                    </td>

                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-info view-enquiry-details" aria-haspopup="true" aria-expanded="false" >
                            View
                        </button>
                    </td>

                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'REJECTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success " aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('ACCEPTED', this);">
                            Accept
                        </button>
                    </td>

                    {% endif %}
                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'ACCEPTED' %}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-danger" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('REJECTED', this);">
                            Reject
                        </button>
                    </td>

                    {% endif %}
                    {%if enquiry.enquiryStatus == 'ACCEPTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.closeEnquiry(this);">
                            Close
                        </button>
                    </td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-secondary" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                           Details
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-warning" aria-haspopup="true" aria-expanded="false"  onclick="followup.addFollowUpDetails('{{enquiry.enquiryId}}', 'HOME')">
                             + Followup
                        </button>
                    </td>
                    {% endif %}

                    {%if enquiry.enquiryStatus == 'CLOSED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                            Details
                        </button>
                    </td>
                    {% endif %}


                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="tab-pane" id="rejected-enquiry-content" role="tabpanel" aria-labelledby="nav-rejected-enquiry-tab">
        <br>
        <div>
            <table class="table table-bordered table-striped datatables-reponsive datatables-reponsive-table rejected-datatables-reponsive" style="min-width:100%;">
                <caption>List of Enquiries</caption>
                <thead>
                <tr>
                    <th style="display:none;"></th>
                    <th scope="col">#</th>
                    <th scope="col" >Tracking ID</th>
                    <th scope="col" >Parent Name</th>
                    <th scope="col" >Child Name</th>
                    <th scope="col" >Contact No.</th>
                    <th scope="col" >Date</th>
                    <th scope="col" class="no-sort">Action</th>
                    <th style="display:none;" class="no-sort" > </th>

                </tr>
                </thead>
                <tbody>
                {% for enquiry in enquiry_details %}
                <tr>
                    <td style="display: none">
                        <p class="enquiry-details-info" style="display:none;"> {{enquiry|jsonstr}} </p>
                    </td>
                    <td scope="row">{{ forloop.counter }}</td>
                    <td>{{enquiry.trackingId}}</td>
                    <td>{{enquiry.guardianName }}</td>
                    <td>{{enquiry.childName }}</td>
                    <td>{{enquiry.guardianContactInfo }}</td>
                    <td>{{enquiry.enquiryDate|print_date}}</td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-info view-enquiry-details" aria-haspopup="true" aria-expanded="false" >
                            View
                        </button>
                    </td>

                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'REJECTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success " aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('ACCEPTED', this);">
                            Accept
                        </button>
                    </td>

                    {% endif %}
                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'ACCEPTED' %}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-danger" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('REJECTED', this);">
                            Reject
                        </button>
                    </td>

                    {% endif %}
                    {%if enquiry.enquiryStatus == 'ACCEPTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.closeEnquiry(this);">
                            Close
                        </button>
                    </td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-secondary" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                           Details
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-warning" aria-haspopup="true" aria-expanded="false"  onclick="followup.addFollowUpDetails('{{enquiry.enquiryId}}', 'HOME')">
                             + Followup
                        </button>
                    </td>
                    {% endif %}

                    {%if enquiry.enquiryStatus == 'CLOSED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                            Details
                        </button>
                    </td>
                    {% endif %}


                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="tab-pane" id="closed-enquiry-content" role="tabpanel" aria-labelledby="nav-closed-enquiry-tab">
        <br>
        <div>
            <table class="table table-bordered table-striped datatables-reponsive datatables-reponsive-table closed-datatables-reponsive" style="min-width:100%;">
                <caption>List of Enquiries</caption>
                <thead>
                <tr>
                    <th style="display:none;"></th>
                    <th scope="col">#</th>
                    <th scope="col" >Tracking ID</th>
                    <th scope="col" >Parent Name</th>
                    <th scope="col" >Child Name</th>
                    <th scope="col" >Contact No.</th>
                    <th scope="col" >Date</th>
                    <th scope="col" class="no-sort">Action</th>
                    <th style="display:none;" class="no-sort" > </th>
                </tr>
                </thead>
                <tbody>
                {% for enquiry in enquiry_details %}
                <tr>
                    <td style="display: none">
                        <p class="enquiry-details-info" style="display:none;"> {{enquiry|jsonstr}} </p>
                    </td>
                    <td scope="row">{{ forloop.counter }}</td>
                    <td>{{enquiry.trackingId}}</td>
                    <td>{{enquiry.guardianName }}</td>
                    <td>{{enquiry.childName }}</td>
                    <td>{{enquiry.guardianContactInfo }}</td>
                    <td>{{enquiry.enquiryDate|print_date}}</td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-info view-enquiry-details" aria-haspopup="true" aria-expanded="false" >
                            View
                        </button>
                    </td>

                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'REJECTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success " aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('ACCEPTED', this);">
                            Accept
                        </button>
                    </td>

                    {% endif %}
                    {% if enquiry.enquiryStatus == 'SUBMITTED'  or enquiry.enquiryStatus == 'ACCEPTED' %}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-danger" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.showEnquiry('REJECTED', this);">
                            Reject
                        </button>
                    </td>

                    {% endif %}
                    {%if enquiry.enquiryStatus == 'ACCEPTED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false" onclick="admissionEnquiry.closeEnquiry(this);">
                            Close
                        </button>
                    </td>
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-secondary" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                           Details
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-warning" aria-haspopup="true" aria-expanded="false"  onclick="followup.addFollowUpDetails('{{enquiry.enquiryId}}', 'HOME')">
                             + Followup
                        </button>
                    </td>
                    {% endif %}

                    {%if enquiry.enquiryStatus == 'CLOSED'%}
                    <td style="width:5%;" >
                        <button id={{enquiry.enquiryId}} type="button" class="btn btn-sm btn-outline-success" aria-haspopup="true" aria-expanded="false"  onclick="admissionEnquiry.showDetails('{{enquiry.enquiryId}}')">
                            Details
                        </button>
                    </td>
                    {% endif %}


                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

</div>
    </div>
</div>