from django.contrib import admin
from django.urls import path
from django.urls import include
from organisation_portal.views import *

urlpatterns = [
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('home/v2', home_page_view_v2, name='home_page_view_v2'),
    path('home-date-session-change/<selected_date>/<selected_academic_session_id>', home_date_session_change_page_view, name='home_date_session_change_page_view'),

    path('change-password', change_password_view, name='change_password_view'),
    path('update-password', update_password_view, name='update_password_view'),

    path('generate-birthday-certificate/<institute_id>/<academic_session_id>/<student_id>', generate_birthday_certificate_view, name='generate_birthday_certificate_view'),
    path('generate-current-birthday-certificate/<institute_id>/<student_id>', generate_current_birthday_certificate_view, name='generate_current_birthday_certificate_view'),

    path('fees-reports', fees_reports_view, name='fees_reports_view'),
    path('fees-generate-report/<report_type>', fees_generate_report_view, name='fees_generate_report_view'),

    path('stats/v2/student-attendance', student_attendance_stats, name='student_attendance_stats'),
    path('stats/v2/staff-attendance', staff_attendance_stats, name='staff_attendance_stats'),
    path('stats/v2/student-admission', student_admission_stats, name='student_admission_stats'),
    path('stats/v2/fee-collection', fee_collection_stats, name='fee_collection_stats'),
    path('stats/v2/active-student-transport', active_student_transport_stats, name='active_student_transport_stats'),

    
]
