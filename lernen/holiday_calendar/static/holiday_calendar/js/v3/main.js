var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;

$(document).ready(function() {
  menuLoader.registerSidebarMenu();
  homePage.initHomePage();
  paymentReminder.readPaymentState();
});

var menuLoader = {

    registerSidebarMenu : function () {
        sideBarHoverEventCallback();
        activateMenuItem();
        menuLoader.registerHomeMenu();
        menuLoader.registerHolidayConfigurationMenu();
        menuLoader.registerConfigureTemplateMenu();
        menuLoader.registerAssignTemplateMenu();
        menuLoader.registerEventManagementMenu();
    },

    registerHomeMenu : function () {
        $('#homeNav').on('click', function() {
            homePage.loadHomePage();
        });
    },

    registerHolidayConfigurationMenu : function () {
        $('#configureHolidayNav').on('click', function() {
            holidayConfig.registerHolidayConfigurationMenu();
        });
    },

    registerConfigureTemplateMenu : function () {
        $('#configureTemplateNav').on('click', function() {
            configureTemplate.registerConfigureTemplateMenu();
        });
    },

    registerAssignTemplateMenu : function () {
        $('#assignHolidaysNav').on('click', function() {
            assignHolidayTemplate.loadHomePage();
        });
    },

    registerEventManagementMenu : function () {
        $('#eventManagementNav').on('click', function() {
            eventManagement.loadEventManagementMainPage();
        });
    },
    
};

var homePage =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        // homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/holiday-calendar/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/holiday-calendar/session-home/"+academicSessionId, function(data) {
          // $("#examination-dashboard-session-content").html(data);
          // homePage.displayDashboardContent();
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

};

var holidayConfig = {

  registerHolidayConfigurationMenu : function () {
    ajaxClient.get("/holiday-calendar/manage-holiday/home", function(data) {
        $("#main-content").html(data);
        holidayConfig.bindHolidayTypeSelection();
        holidayConfig.bindHolidayFrequencySelection();
        initSelect2();
        initDateWithYearRange("-5:+5");
    });
  },

  loadSessionHolidays : function () {
    var academicSessionId = $(".holiday-academic-session option:selected").val();
    ajaxClient.get("/holiday-calendar/manage-holiday/"+academicSessionId+"/holidays", function(data) {
        $("#session-holidays-container").html(data);
    });
  },

  bindHolidayTypeSelection : function () {
    $("#holiday-types").change(function() {
      var selectedHolidayType = $(this).find(':selected').val().trim();
      if(selectedHolidayType == 'STATIC'){
          $("#recurring-holiday-input").attr("style", "display:none");
      }else{
          $("#recurring-holiday-input").attr("style", "display:block");
      }
    });
  },

  bindHolidayFrequencySelection : function () {
    $('#holiday-frequency-types').change(function() {
        var selectedHolidayFrequency = $(this).find(':selected').val().trim();
        if(selectedHolidayFrequency == 'WEEK'){
            $("#weeks-of-month-container").attr("style", "display:none");
        }else{
            $("#weeks-of-month-container").attr("style", "display:block");
        }
    });
  },

  resetHolidayModal : function () {
    $("#add-new-holiday-modal").find(".modal-title").text("Add New Holiday");
    $("#add-new-holiday-modal").find(".add-holiday").attr("style", "display:block");
    $("#add-new-holiday-modal").find(".update-holiday").attr("style", "display:none");

    $("#holiday-name").val("");
    $("#holiday-description").val("");
    $('#holiday-types').prop('selectedIndex',0);
    $("#holiday-start-date").val("");
    $("#holiday-end-date").val("");

    $('#holiday-frequency-types').prop('selectedIndex',0);
    $('#holiday-weekdays').prop('selectedIndex',0);
    $("#weeks-of-month").val([]).change();

    $("#weeks-of-month-container").attr("style", "display:none");
    $("#recurring-holiday-input").attr("style", "display:none");
  },

  populateUpdateHolidayModal : function (element) {
    holidayConfig.resetHolidayModal();
    $("#add-new-holiday-modal").find(".modal-title").text("Update Holiday");
    $("#add-new-holiday-modal").find(".add-holiday").attr("style", "display:none");
    $("#add-new-holiday-modal").find(".update-holiday").attr("style", "display:block");

    var holidayEntryJson = $(element).parent().parent().find(".session-holiday-entry").text().trim();
    var holidayEntry = JSON.parse(holidayEntryJson);


    $("#holiday-id").val(holidayEntry.holidayId);
    $("#holiday-name").val(holidayEntry.name);
    $("#holiday-description").val(holidayEntry.description);
    $('#holiday-types').val(holidayEntry.holidayType)
    // $("#holiday-start-date.select-date").datepicker("setDate", new Date(holidayEntry.start * 1000));
    // $("#holiday-end-date.select-date").datepicker("setDate", new Date(holidayEntry.end * 1000));
    // TODO : This does not open the calendar on same date. Just sets the value
    $("#holiday-start-date").val(getFormattedDate(holidayEntry.start));
    $("#holiday-end-date").val(getFormattedDate(holidayEntry.end));

    if(holidayEntry.holidayType == 'RECURRING'){
      $("#recurring-holiday-input").attr("style", "display:block");
      $('#holiday-frequency-types').val(holidayEntry.recurringPattern.recurringFrequency);
      $('#holiday-weekdays').val(holidayEntry.recurringPattern.dayOfWeek);

      if(holidayEntry.recurringPattern.recurringFrequency == 'MONTH'){
          $("#weeks-of-month-container").attr("style", "display:block");
        $("#weeks-of-month").val(holidayEntry.recurringPattern.weekOfMonth).change();
      }
    }
    $("#add-new-holiday-modal").modal({backdrop: 'static', keyboard: false});
  },


  addNewHoliday : function() {
    var invalid = validateMandatoryFields($("#add-new-holiday-modal"));
    if(invalid){
      showErrorDialogBox("Please fill mandatory fields.");
      return;
    }


    var holidayName = $("#holiday-name").val();
    var holidayDescription = $("#holiday-description").val();
    var holidayType = $("#holiday-types option:selected").val();
    var startDate = getDate($('#holiday-start-date').val()).getTime()/1000;
    var endDate = getDate($('#holiday-end-date').val()).getTime()/1000;

    var recurringPattern = null;
    if(holidayType == 'RECURRING'){
      var recurringFrequency = $("#holiday-frequency-types option:selected").val();
      var dayOfWeek = $("#holiday-weekdays option:selected").val();
      var weekOfMonth = []
      if(recurringFrequency == 'MONTH'){
        weekOfMonth =  $("#weeks-of-month").val();
      }
      recurringPattern = {"recurringFrequency" : recurringFrequency, "dayOfWeek" : dayOfWeek, "weekOfMonth" : weekOfMonth}
    }

    $("#add-new-holiday-modal").modal('toggle');

    var payload = {'holidayType': holidayType, 'start' : startDate, 'end' : endDate, 'name' : holidayName, 'description' : holidayDescription, 'recurringPattern' : recurringPattern};

    ajaxClient.post("/holiday-calendar/manage-holiday/new" ,{'payload':JSON.stringify(payload)}, function(data){
      $("#holiday-default-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      holidayConfig.loadSessionHolidays();
    });

  },

  updateHoliday : function() {
    var invalid = validateMandatoryFields($("#add-new-holiday-modal"));
    if(invalid){
      showErrorDialogBox("Please fill mandatory fields.");
      return;
    }

    var holidayId = $("#holiday-id").val();
    var holidayName = $("#holiday-name").val();
    var holidayDescription = $("#holiday-description").val();
    var holidayType = $("#holiday-types option:selected").val();
    var startDate = getDate($('#holiday-start-date').val()).getTime()/1000;
    var endDate = getDate($('#holiday-end-date').val()).getTime()/1000;

    var recurringPattern = null;
    if(holidayType == 'RECURRING'){
      var recurringFrequency = $("#holiday-frequency-types option:selected").val();
      var dayOfWeek = $("#holiday-weekdays option:selected").val();
      var weekOfMonth = []
      if(recurringFrequency == 'MONTH'){
        weekOfMonth =  $("#weeks-of-month").val();
      }
      recurringPattern = {"recurringFrequency" : recurringFrequency, "dayOfWeek" : dayOfWeek, "weekOfMonth" : weekOfMonth}
    }

    $("#add-new-holiday-modal").modal('toggle');

    var payload = {'holidayType': holidayType, 'start' : startDate, 'end' : endDate, 'name' : holidayName, 'description' : holidayDescription, 'recurringPattern' : recurringPattern};

    ajaxClient.post("/holiday-calendar/manage-holiday/update/"+holidayId ,{'payload':JSON.stringify(payload)}, function(data){
      $("#holiday-default-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      holidayConfig.loadSessionHolidays();
    });

  },

  confirmDeleteHoliday : function (element) {
    $("#delete-holiday-modal").modal('toggle');
    var holidayEntryJson = $(element).parent().parent().find(".session-holiday-entry").text().trim();
    var holidayEntry = JSON.parse(holidayEntryJson);
    var academicSessionId = $('.holiday-academic-session').val();
    $("#delete-holiday-id").val(holidayEntry.holidayId);
    $("#delete-academic-session-id").val(academicSessionId);

  },

  deleteHoliday : function(holidayId) {
    $("#delete-holiday-modal").modal('toggle');
    var holidayId = $("#delete-holiday-id").val();
    var academicSessionId = $("#delete-academic-session-id").val();

    ajaxClient.post("/holiday-calendar/manage-holiday/delete/"+academicSessionId+"/"+holidayId ,{}, function(data){
      $("#holiday-default-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      holidayConfig.loadSessionHolidays();
    });

  },

};

var configureTemplate = {
  dataCache : {},

  registerConfigureTemplateMenu : function () {
    ajaxClient.get("/holiday-calendar/configure-template/home", function(data) {
        $("#main-content").html(data);
        configureTemplate.initDataCache();
        academicSessionHandler.bindSessionChangeEvent(configureTemplate.loadHomePageForSession);
    });
  },

  initDataCache : function () {
    configureTemplate.dataCache = {};
  },

  addNewTemplateForm : function() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/holiday-calendar/configure-template/form/"+academicSessionId, function(data) {
        $("#holiday-template-add-modal-container").html(data);
        $("#add-holiday-template-modal").modal('toggle');
    });
  },

  loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/holiday-calendar/configure-template/home/<USER>/" + academicSessionId, function(data) {
        $("#session-holiday-template-list").html(data);
        configureTemplate.initDataCache();
    });
  },

  submitNewHolidayTemplate : function() {
    var templateName = $("#new-holiday-template-name").val().trim();
    var templateType = $("#new-holiday-template-type").find(':selected').val().trim();
    var description = $("#new-holiday-template-description").val().trim();

    var selectedHolidays = configureTemplate.getTemplateSelectedHolidays("add-holiday-template-modal");
    if(selectedHolidays.length == 0){
      alert("Please select atleast one holiday");
      return;
    }

    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    holidayTemplatePayload = {'academicSessionId' : academicSessionId, 'templateName' : templateName, 'templateType' : templateType, 'description' : description, 'holidays' : selectedHolidays}
    $("#add-holiday-template-modal").modal('toggle');
    ajaxClient.post("/holiday-calendar/configure-template/add" ,{'holidayTemplatePayload':JSON.stringify(holidayTemplatePayload)}, function(data){
      $("#holiday-template-status-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      configureTemplate.loadHomePageForSession();
    });

  },

  getTemplateSelectedHolidays : function (holiday_template_modal_id) {
    var selectedHolidays = [];
    $("#"+holiday_template_modal_id).find("input.template-holiday-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var holidayId = $(this).attr("id").split(":")[1];
        selectedHolidays.push(holidayId);
    });
    return selectedHolidays;
  },

  viewHolidayTemplate : function(thisContext) {
      var templateId = $(thisContext).parent().parent().attr("id");
      ajaxClient.get("/holiday-calendar/configure-template/view/"+templateId, function(data) {
          $("#holiday-template-view-modal-container").html(data);
          $("#view-holiday-template-modal").modal('toggle');
      });
  },

  showUpdateHolidayTemplate : function(thisContext) {
      var templateId = $(thisContext).parent().parent().attr("id");
      ajaxClient.get("/holiday-calendar/configure-template/update-form/"+templateId, function(data) {
          $("#holiday-template-update-modal-container").html(data);
          $("#update-holiday-template-modal").modal('toggle');
      });
  },

  submitUpdateHolidayTemplate : function(templateId) {
    var templateName = $("#update-holiday-template-name").val().trim();
    var templateType = $("#update-holiday-template-type").find(':selected').val().trim();
    var description = $("#update-holiday-template-description").val().trim();

    var selectedHolidays = configureTemplate.getTemplateSelectedHolidays("update-holiday-template-modal");
    if(selectedHolidays.length == 0){
      alert("Please select atleast one holiday");
      return;
    }

    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    holidayTemplatePayload = {'academicSessionId' : academicSessionId, 'templateName' : templateName, 'templateType' : templateType, 'description' : description, 'holidays' : selectedHolidays}
    $("#update-holiday-template-modal").modal('toggle');
    ajaxClient.post("/holiday-calendar/configure-template/update/"+templateId ,{'holidayTemplatePayload':JSON.stringify(holidayTemplatePayload)}, function(data){
      $("#holiday-template-status-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      configureTemplate.loadHomePageForSession();
    });

  },

  confirmDeleteHolidayTemplate : function(thisContext) {
      var templateId = $(thisContext).parent().parent().attr("id");
      $("#delete-holiday-template-id").text(templateId);
      $("#delete-holiday-template-modal").modal('toggle');
  },

  submitDeleteHolidayTemplate : function() {
      var templateId = $("#delete-holiday-template-id").text();
      $("#delete-holiday-template-modal").modal('toggle');
      ajaxClient.get("/holiday-calendar/configure-template/delete/"+templateId, function(data) {
         $("#holiday-template-status-modal-container").html(data);
         $("#holiday-default-status-modal").modal('toggle');
         configureTemplate.loadHomePageForSession();
      });
  },

};

var assignHolidayTemplate = {
  dataCache : {},
  studentUserType : "STUDENT",
  staffUserType : "STAFF",
  defaultUserType : "STUDENT",

  loadHomePage : function () {
    ajaxClient.get("/holiday-calendar/assign-template/home/"+assignHolidayTemplate.defaultUserType, function(data) {
        $("#main-content").html(data);
        assignHolidayTemplate.initDataCache();
        initSelect2();
        academicSessionHandler.bindSessionChangeEvent(assignHolidayTemplate.loadHomePageForSession);
        assignHolidayTemplate.registerUserTypeOptionsTabs();
    });
  },

  initDataCache : function () {
    assignHolidayTemplate.dataCache = {"userType" : assignHolidayTemplate.defaultUserType};
  },

  loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var userType = assignHolidayTemplate.dataCache.userType;
      ajaxClient.get("/holiday-calendar/assign-template/home/<USER>/" + academicSessionId +"/"+userType, function(data) {
          $("#user-list-screen").html(data);
          initSelect2();
      });
  },

  registerUserTypeOptionsTabs : function() {
    $("#select-student-user-type").off('click').on('click', function () {
        assignHolidayTemplate.dataCache['userType'] = assignHolidayTemplate.studentUserType;
        assignHolidayTemplate.loadHomePageForSession();
    });

    $("#select-staff-user-type").off('click').on('click', function () {
        assignHolidayTemplate.dataCache['userType'] = assignHolidayTemplate.staffUserType;
        assignHolidayTemplate.loadHomePageForSession();
    });
  },

  searchStudents : function() {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var userType = assignHolidayTemplate.dataCache.userType;
      var requiredStandards = $(".select-template-class").val();
      
      var templateNotAssignedStudentOnly = false;
      if($('#template-not-assigned-checkbox').is(":checked")){
          templateNotAssignedStudentOnly = true;
      }
      if(requiredStandards != undefined && requiredStandards != null) {
        requiredStandards = requiredStandards.join(",");
      } else {
        requiredStandards = "";
      }

      ajaxClient.get("/holiday-calendar/assign-template/user-search/" + academicSessionId +"/"+userType+"?requiredStandards="+requiredStandards+"&templateNotAssignedUsersOnly="+templateNotAssignedStudentOnly, function(data) {
          $("#student-template-list").html(data);
      });
  },

  selectBulkStudentList : function (selectAllCheckbox) {
      if(selectAllCheckbox.checked){
        $(".bulk-student-select-checkbox").prop('checked', true);
        var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
        $("#bulk-students-selected-count").html(selectedStudentCount);
        assignHolidayTemplate.dataCache.selectedStudentCount = selectedStudentCount;
      }
      else{
          $(".bulk-student-select-checkbox").prop('checked', false);
          $("#bulk-students-selected-count").html(0);
          assignHolidayTemplate.dataCache.selectedStudentCount = 0;
      }
  },

  bulkStudentSelectCheckbox : function(studentSelectCheckbox) {
      var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
      $("#bulk-students-selected-count").html(selectedStudentCount);
      assignHolidayTemplate.dataCache.selectedStudentCount = selectedStudentCount;
  },

  getSelectedBulkStudentIds :function () {
    var studentIds = [];
    $("input.bulk-student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-template-assign-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    return studentIds;
  },

  assignStudentTemplateConfirmModal : function() {
    var selectedStudentCount = assignHolidayTemplate.dataCache.selectedStudentCount;
    if(selectedStudentCount == undefined || selectedStudentCount == null || selectedStudentCount == 0){
      alert("Please select atleast one student to assign template");
      return;
    }

    $("#bulk-student-template-assignment-confirmation-modal-text").html("Do you want to set the below calendar to "+ selectedStudentCount +" students?");
    $("#bulk-student-template-assignment-confirmation-modal").modal('toggle');
  },

  assignStudentTemplate : function() {
    $("#bulk-student-template-assignment-confirmation-modal").modal('toggle');
    var selectedTemplateId = $("#assignment-holiday-template").find(':selected').val().trim();
    if(selectedTemplateId == ""){
      selectedTemplateId = null;
    }
    var selectedStudentIds = assignHolidayTemplate.getSelectedBulkStudentIds();
    var userType = assignHolidayTemplate.dataCache.userType;
    var academicSessionId = academicSessionHandler.getSelectedSessionId();


    holidayTemplateAssignPayload = {'academicSessionId' : academicSessionId, 'userType' : userType, 'templateId' : selectedTemplateId, 'userList' : selectedStudentIds}
    $("#add-holiday-template-modal").modal('toggle');
    ajaxClient.post("/holiday-calendar/assign-template/update" ,{'holidayTemplateAssignPayload' : JSON.stringify(holidayTemplateAssignPayload)}, function(data){
      $("#holiday-template-assignment-status-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      assignHolidayTemplate.searchStudents();
    });

  },

  searchStaff : function() {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var userType = assignHolidayTemplate.dataCache.userType;
      // var requiredStandards = $(".select-template-class").val();
      
      var templateNotAssignedStaffOnly = false;
      if($('#template-not-assigned-checkbox').is(":checked")){
          templateNotAssignedStaffOnly = true;
      }
      // if(requiredStandards != undefined && requiredStandards != null) {
      //   requiredStandards = requiredStandards.join(",");
      // } else {
      //   requiredStandards = "";
      // }

      ajaxClient.get("/holiday-calendar/assign-template/user-search/" + academicSessionId +"/"+userType+"?templateNotAssignedUsersOnly="+templateNotAssignedStaffOnly, function(data) {
          $("#staff-template-list").html(data);
      });
  },

  selectBulkStaffList : function (selectAllCheckbox) {
      if(selectAllCheckbox.checked){
        $(".bulk-staff-select-checkbox").prop('checked', true);
        var selectedStaffCount = $('input.bulk-staff-select-checkbox:checkbox:checked').length;
        $("#bulk-staff-selected-count").html(selectedStaffCount);
        assignHolidayTemplate.dataCache.selectedStaffCount = selectedStaffCount;
      }
      else{
          $(".bulk-staff-select-checkbox").prop('checked', false);
          $("#bulk-staff-selected-count").html(0);
          assignHolidayTemplate.dataCache.selectedStaffCount = 0;
      }
  },

  bulkStaffSelectCheckbox : function(staffSelectCheckbox) {
      var selectedStaffCount = $('input.bulk-staff-select-checkbox:checkbox:checked').length;
      $("#bulk-staff-selected-count").html(selectedStaffCount);
      assignHolidayTemplate.dataCache.selectedStaffCount = selectedStaffCount;
  },

  getSelectedBulkStaffIds :function () {
    var staffIds = [];
    $("input.bulk-staff-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var staffId = $(this).parent().find('p.bulk-template-assign-staff-id').first().text().trim();
        staffIds.push(staffId);
    });
    return staffIds;
  },

  assignStaffTemplateConfirmModal : function() {
    var selectedStaffCount = assignHolidayTemplate.dataCache.selectedStaffCount;
    if(selectedStaffCount == undefined || selectedStaffCount == null || selectedStaffCount == 0){
      alert("Please select atleast one staff to assign template");
      return;
    }

    $("#bulk-staff-template-assignment-confirmation-modal-text").html("Do you want to set the below calendar to "+ selectedStaffCount +" staffs?");
    $("#bulk-staff-template-assignment-confirmation-modal").modal('toggle');
  },

  assignStaffTemplate : function() {
    $("#bulk-staff-template-assignment-confirmation-modal").modal('toggle');
    var selectedTemplateId = $("#assignment-holiday-template").find(':selected').val().trim();
    if(selectedTemplateId == ""){
      selectedTemplateId = null;
    }
    var selectedStaffIds = assignHolidayTemplate.getSelectedBulkStaffIds();
    var userType = assignHolidayTemplate.dataCache.userType;
    var academicSessionId = academicSessionHandler.getSelectedSessionId();


    holidayTemplateAssignPayload = {'academicSessionId' : academicSessionId, 'userType' : userType, 'templateId' : selectedTemplateId, 'userList' : selectedStaffIds}
    ajaxClient.post("/holiday-calendar/assign-template/update" ,{'holidayTemplateAssignPayload' : JSON.stringify(holidayTemplateAssignPayload)}, function(data){
      $("#holiday-template-assignment-status-modal-container").html(data);
      $("#holiday-default-status-modal").modal('toggle');
      assignHolidayTemplate.searchStaff();
    });

  },



};

 var eventManagement = {
   dataCache : {},

    loadEventManagementMainPage : function() {
         ajaxClient.get("/holiday-calendar/event-management-main-screen", function(data) {
             $("#main-content").html(data);
             academicSessionHandler.bindSessionChangeEvent(eventManagement.changeSession)
             initDateWithYearRange("-5:+5");
         });
    },

    changeSession : function() {
         var sessionId = academicSessionHandler.getSelectedSessionId();
         ajaxClient.get("/holiday-calendar/event-session-change-screen/"+sessionId, function(data) {
                $("#event-list").html(data);
         });
    },

    addNewEvent: function () {
        var invalid = validateMandatoryFields($("#add-event-modal"));
        if (invalid) {
            return;
        }

        const eventDetails = {
            academicSessionId: academicSessionHandler.getSelectedSessionId(),
            eventId: null,
            title: $("#event-name").val().trim(),
            description: $("#description").val().trim(),
            eventDate: getDate($("#event-date").val()).getTime() / 1000,
            category: $("#event-category").val(),
            metaData: "",
            documents: "",
            createdBy: null
        };
        $("#add-event-modal").modal('toggle');
        ajaxClient.post("/holiday-calendar/add-event", { event_details: JSON.stringify(eventDetails) }, function (data) {
            $("#event-status-modal-container").html(data);
            $("#holiday-default-status-modal").modal({ backdrop: 'static', keyboard: false });
            eventManagement.changeSession();
        });
    },

    updateEventDetails: function(button) {
        const row = $(button).closest("tr");
        const eventJson = row.find(".event-info").text().trim();
        const event = JSON.parse(eventJson);
        $("#update-event-name").val(event.title || "");
        $("#update-description").val(event.description || "");
        $("#update-event-category").val(event.category || "");
        $("#update-event-date").val(getFormattedDate(event.eventDate));
        $("#update-event-id").text(event.eventId || "");

        $("#update-event-modal").modal("show");
    },

    updateEvent: function () {
        var invalid = validateMandatoryFields($("#update-event-modal"));
        if (invalid) {
            return;
        }

        const eventDetails = {
            academicSessionId: academicSessionHandler.getSelectedSessionId(),
            eventId: $("#update-event-id").text().trim(),
            title: $("#update-event-name").val().trim(),
            description: $("#update-description").val().trim(),
            eventDate: getDate($("#update-event-date").val()).getTime() / 1000,
            category: $("#update-event-category").val(),
            metaData: "",
            documents: "",
            createdBy: null
        };

        $("#update-event-modal").modal('toggle');
        ajaxClient.post("/holiday-calendar/update-event", { event_details: JSON.stringify(eventDetails) }, function (data) {
            $("#event-status-modal-container").html(data);
            $("#holiday-default-status-modal").modal({ backdrop: 'static', keyboard: false });
            eventManagement.changeSession();
        });
    },

    deleteEventDetails: function(button) {
        const eventId = $(button).attr("id");
        $("#delete-event-id").text(eventId);
        $("#delete-event-confirm-modal").modal("show");
    },

    deleteEvent: function() {
       academicSessionId = academicSessionHandler.getSelectedSessionId(),
       $("#delete-event-confirm-modal").modal('toggle');
       var eventId = $("#delete-event-id").text().trim();
       ajaxClient.post("/holiday-calendar/delete-event/"+ academicSessionId +"/" +eventId,{}, function(data){
          $("#event-status-modal-container").html(data);
          $("#holiday-default-status-modal").modal({ backdrop: 'static', keyboard: false });
          eventManagement.changeSession();
       });

    },

    resetFields : function() {
         $("#add-event-modal").find('input').val("");
         $("#add-event-modal").find('select').val("");
         $("#add-event-modal").find('textarea').val("");
    },

 };
