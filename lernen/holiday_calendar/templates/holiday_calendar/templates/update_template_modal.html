<div id="update-holiday-template-modal" class="modal fade bd-example-modal-xl" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
     <div class="modal-dialog modal-xl">
         <div class="modal-content">
             <div class="modal-header">
                 <h5 class="modal-title" > Update Calendar </h5>
                 <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                     <span aria-hidden="true"> &times; </span>
                 </button>
             </div>
             <div class="modal-body">
               <div class="form-row">
                 <div class="form-group col-md-4">
                   <label>Calendar Name*</label>
                   <input type="text" class="form-control mandatory-field" id="update-holiday-template-name" placeholder="Calendar Name..." value="{{holiday_template_details.holidayTemplate.templateName}}">
                 </div>
                 <div class="form-group col-md-4">
                   <label>Calendar Type *</label>
                   <select id="update-holiday-template-type" class="form-control mandatory-field">
                        <option value="GENERAL"  {% if holiday_template_details.holidayTemplate.templateType == 'GENERAL' %} selected {% endif %} >General</option>
                        <option value="STAFF" {% if holiday_template_details.holidayTemplate.templateType == 'STAFF' %} selected {% endif %}>Staff</option>
                        <option value="STUDENT" {% if holiday_template_details.holidayTemplate.templateType == 'STUDENT' %} selected {% endif %}>Student</option>
                   </select>
                 </div>
                <div class="form-group col-md-4">
                  <label>Description</label>
                  <textarea class="form-control" id="update-holiday-template-description" rows="1" placeholder="Description"  value="{{holiday_template_details.holidayTemplate.description}}"></textarea>
                </div>
               </div>
               <br />
               <div>
                    {% include 'holiday_calendar/templates/template_holiday_list.html' %}
               </div>
             </div>
             <div class="modal-footer">
                 <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="">Cancel</button>
                 <button id="submit-holiday-template" type="button" class="btn btn-primary" onclick="configureTemplate.submitUpdateHolidayTemplate('{{holiday_template_details.holidayTemplate.templateId}}')">Update Calendar</button>
             </div>
         </div>
     </div>
 </div>
