$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()}),$("#addLessonPlanNav").on("click",function(){lessonPlanPage.loadMainPage()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/lesson-plan/home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/lesson-plan/session-home/"+e,function(e){$("#lesson-plan-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()}},lessonPlanPage={dataCache:{},loadMainPage:function(){ajaxClient.get("/lesson-plan/lesson-plan-view-page",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(lessonPlanPage.changeSession),lessonPlanPage.bindClassChangeEvent(),lessonPlanPage.dataCache.adminAccess=$("#admin-access").text().trim(),lessonPlanPage.dataCache.staffAccess=$("#staff-access").text().trim(),lessonPlanPage.dataCache.staffAccess=$("#staff-response-access").text().trim(),lessonPlanPage.dataCache.selectedLessonPlanCount=0,lessonPlanPage.loadActionForStaffAndLessonPlan("")})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/lesson-plan/standards/"+e,function(e){"True"==lessonPlanPage.dataCache.adminAccess?($("#standard-with-section-options-wrapper").html(e),lessonPlanPage.bindClassChangeEvent(),$("#lesson-plan-main-container").html('<br><br><br><br> <br> <br> <br><br><br><br> <h5 style="text-align:center;color:#223b5c;"> No Courses Lesson Plan is Avalible!</h5><br><br><br><br> <br> <br> <br><br><br><br>')):($("#lesson-plan-main-container").html(e),lessonPlanPage.loadActionForStaffAndLessonPlan(""))})},bindClassChangeEvent:function(){$("#lesson-plan-standard-section-select").change(function(){lessonPlanPage.loadCourseLessonPlanStructure($(this).find(":selected").val().trim())})},loadCourseLessonPlanStructure:function(e){if(""!=e){var n=e.split(":"),a=n[0];if(""!=a){var t=null;2==n.length&&(t=n[1]);var s=academicSessionHandler.getSelectedSessionId();lessonPlanPage.dataCache.classMarksStructures=[],lessonPlanPage.dataCache.selectedStandardSectionMapId=e,lessonPlanPage.dataCache.selectedStandard=a,lessonPlanPage.dataCache.selectedSection=t,ajaxClient.get("/lesson-plan/course-lesson-plan-structure/"+a+"/"+t+"/"+s,function(e){$("#lesson-plan-main-container").html(e),null==t&&($(".lesson-plan-clone-div").hide(),$(".lesson-plan-course-clone").hide()),lessonPlanPage.loadActionForStaffAndLessonPlan()})}}},loadActionForStaffAndLessonPlan:function(){$(".lesson-plan-details").on("click",function(){var e=$(this).parent().parent().parent().find(".course-staff-details-info-json").text().trim(),n=JSON.parse(e),a=n.courseId,t=n.courseName,s=n.standardRowDetails.sectionId,o=n.standardRowDetails.standardId,l="",d=academicSessionHandler.getSelectedSessionId();l=s?n.standardRowDetails.displayNameWithSection:n.standardRowDetails.displayName;var c=$(this).parent().parent().parent().find(".lesson-plan-id").text().trim();lessonPlanPage.getLessonPlanDetails(c,"ACTIVE",t,l,!0,a,o,s,d,"")}),$(".staff-response-details").on("click",function(){var e=$(this).parent().parent().parent().find(".course-staff-details-info-json").text().trim(),n=JSON.parse(e),a=n.courseId,t=n.courseName,s="",o=n.staffId,l=n.standardRowDetails.sectionId,d=n.standardRowDetails.standardId,c=academicSessionHandler.getSelectedSessionId();s=n.standardRowDetails.sectionId?n.standardRowDetails.displayNameWithSection:n.standardRowDetails.displayName;var i=$(this).parent().parent().parent().find(".lesson-plan-id").text().trim();lessonPlanPage.getLessonPlanDetails(i,"ACTIVE",t,s,!1,a,d,l,c,o)}),$("#lesson-plan-course-clone-button").on("click",function(){initSelect2("Select Section ....","clone-lesson-section"),$(".select2-search__field").attr("style","width:100%");var e=lessonPlanPage.getSelectedBulkLessonPlanIds(),n=academicSessionHandler.getSelectedSessionId();null==lessonPlanPage.dataCache.selectedLessonPlanCount&&(lessonPlanPage.dataCache.selectedLessonPlanCount=0);var a=$("#lesson-plan-standard-section-select").find(":selected").val().trim();if(""!=a){var t=a.split(":"),s=t[0];if(""!=s){var o=null;2==t.length&&(o=t[1]),lessonPlanPage.updatePopupStandardSections(s,o),$("#clone-confirm-popup-text").html("Total Number Of Lesson Plans Cloned: "+lessonPlanPage.dataCache.selectedLessonPlanCount+" <span class='tooltip-star' title='Only Created lesson plans are considered for cloning'>*</span>"),$("#clone-conformation-data-popup").modal("toggle"),lessonPlanPage.getCloneConformationDialogBox(n,e,s)}}}),$(".delete-lesson-plan").on("click",function(){var e=$(this).parent().parent().find("p.lesson-plan-id").text().trim(),n=academicSessionHandler.getSelectedSessionId();null==e||""==e&&"None"==e||"undefined"==e?showErrorDialogBox("Lesson Plan Is Not Created So Not Able To Delete"):($("#lesson-plan-delete-warning-popup").modal("toggle"),lessonPlanPage.deleteLessonPlanDetails(n,e))})},deleteLessonPlanDetails:function(e,n){$("#lesson-plan-delete-confirm-button").on("click",function(){$("#lesson-plan-delete-warning-popup").modal("toggle"),ajaxClient.post("/lesson-plan/lesson-plan-delete-details/"+e+"/"+n,{},function(e){$("#lesson-plan-status-modal-container").html(e),$("#lesson-plan-status-modal").modal("toggle"),$("#lesson-plan-status-modal").on("hidden.bs.modal",function(){if("True"==lessonPlanPage.dataCache.adminAccess){var e=lessonPlanPage.dataCache.selectedStandardSectionMapId;lessonPlanPage.loadCourseLessonPlanStructure(e)}else lessonPlanPage.loadMainPage()})})})},getCloneConformationDialogBox:function(e,n,a){$("#lesson-plan-confirm-button").on("click",function(){if(0==lessonPlanPage.dataCache.selectedLessonPlanCount)return $("#clone-conformation-data-popup").modal("toggle"),void showErrorDialogBox("Please select lesson Plan first to Clone");var t=$("#add-clone-lesson-plan-popup-class-section").val()||[];if(t.length<=0)return $("#clone-conformation-data-popup").modal("toggle"),showErrorDialogBox("Please select a section. Cloning is permitted only between sections within the same standard."),void $("#dialog-box-modal").on("hidden.bs.modal",function(){$("#clone-conformation-data-popup").modal("toggle")});var s={lessonPlanIds:n,standardId:a,sectionIds:t};lessonPlanPage.cloneLessonPlanDetails(e,s),$("#clone-conformation-data-popup").modal("toggle"),$("#clone-conformation-popup").modal("toggle")})},cloneLessonPlanDetails:function(e,n){$("#clone-conformation-popup-confirm-button").on("click",function(){$("#clone-conformation-popup").modal("toggle"),ajaxClient.post("/lesson-plan/lesson-plan-clone-details/"+e,JSON.stringify(n),function(e){$("#lesson-plan-status-modal-container").html(e),$("#lesson-plan-status-modal").modal("toggle"),$("#lesson-plan-status-modal").on("hidden.bs.modal",function(){if("True"==lessonPlanPage.dataCache.adminAccess){var e=lessonPlanPage.dataCache.selectedStandardSectionMapId;lessonPlanPage.loadCourseLessonPlanStructure(e)}else lessonPlanPage.loadMainPage()})})})},getLessonPlanDetails:function(e,n,a,t,s,o,l,d,c,i){ajaxClient.get("/lesson-plan/lesson-plan-structure/"+e+"/"+n,function(n){$("#lesson-plan-staff-response-detail-container").html(n),$("#lesson-plan-course-name").text(a),$("#lesson-plan-standard-name").text(t),$("#lesson-plan-academic-session-div").css("display","none"),$("#lesson-plan-main-container").css("display","none"),$("#lesson-plan-form-navbar").css("display","block"),$("#lesson-plan-staff-response-detail-container").css("display","block");const r="None"!=$(".chapter-details-list").text()?$(".chapter-details-list").text().trim():"[]",p=JSON.parse(r);lessonPlanAndStaffResponseUI.renderLessonPlan(p,0,s),initDateWithYearRange("-1:+1"),s?lessonPlanPage.registerSaveLessonPlanData(e,o,l,d,c):(lessonPlanPage.registerSaveStaffResponse(e,o,l,d,c,i),$(".add-chapter-plan").hide())})},back:function(){$("#lesson-plan-academic-session-div").css("display","block"),$("#lesson-plan-main-container").css("display","block"),$("#lesson-plan-form-navbar").css("display","none"),$("#lesson-plan-staff-response-detail-container").css("display","none")},registerSaveStaffResponse:function(e,n,a,t,s,o){$("#add-lesson-plan-details").on("click",function(){var l=lessonPlanPage.collectStaffResponsePayload(e,o);0!=l&&(l.length<=0?showErrorDialogBox("Please add atleast one Staff Response"):($("#lesson-plan-alert-popup").modal("toggle"),$("#lesson-plan-save-details-popup-text").text("Do you want to save Staff Response details?"),$("#lesson-plan-save-confirm-button").on("click",function(){lessonPlanPage.saveStaffResponseData(n,l,a,t,s)})))})},saveStaffResponseData:function(e,n,a,t,s){$("#lesson-plan-alert-popup").modal("toggle"),ajaxClient.post("/lesson-plan/staff-response-details/"+s+"?course_id="+e,JSON.stringify(n),function(e){$("#lesson-plan-status-modal-container").html(e),$("#lesson-plan-status-modal").modal("toggle"),$("#lesson-plan-status-modal").on("hidden.bs.modal",function(){if(lessonPlanPage.back(),"True"==lessonPlanPage.dataCache.adminAccess){var e=a+":";t&&(e+=t),lessonPlanPage.loadCourseLessonPlanStructure(e)}else lessonPlanPage.loadMainPage()})})},registerSaveLessonPlanData:function(e,n,a,t,s){$("#add-lesson-plan-details").on("click",function(){var o=lessonPlanPage.collectChapterTopicPayload();if(0!=o)if(o.length<=0)showErrorDialogBox("Please add atleast one Chapter");else{var l=lessonPlanPage.collectLessonPlanPayload(e,n,a,t,o);l.length<=0?showErrorDialogBox("Please add atleast one Chapter"):($("#lesson-plan-alert-popup").modal("toggle"),$("#lesson-plan-save-confirm-button").on("click",function(){lessonPlanPage.saveLessonPlanData(n,l,a,t,s)}))}})},saveLessonPlanData:function(e,n,a,t,s){$("#lesson-plan-alert-popup").modal("toggle"),ajaxClient.post("/lesson-plan/lesson-plan-details/"+s+"?course_id="+e,JSON.stringify(n),function(e){$("#lesson-plan-status-modal-container").html(e),$("#lesson-plan-status-modal").modal("toggle"),$("#lesson-plan-status-modal").on("hidden.bs.modal",function(){if(lessonPlanPage.back(),"True"==lessonPlanPage.dataCache.adminAccess){var e=a+":";t&&(e+=t),lessonPlanPage.loadCourseLessonPlanStructure(e)}else lessonPlanPage.loadMainPage()})})},collectLessonPlanPayload:function(e,n,a,t,s){const o=[],l=[];null!=t&&l.push(t),null!=e&&""!=e&&"None"!=e&&"undefined"!=e||(e=null);const d={lessonPlanId:e,title:"",description:"",standardId:a,sectionIds:l,courseId:n,lessonPlanStatus:"ACTIVE"};return o.push({lessonPlanMetaDataPayload:d,lessonPlanChapterPayloadList:s}),o},collectChapterTopicPayload:function(){const e=[];let n=!1;return $("#chapter-container .card.card-border").each(function(){if(n)return!1;const a=$(this),t=a.find(".chapter-details tbody tr");let s=t.find(".chapter-id").text(),o=null;s&&""!==s.trim()&&(o=s.trim());const l=t.find(".chapter-title").val();let d="";l&&""!==l.trim()&&(d=l.trim());const c=t.find(".chapter-description").val();let i="";c&&""!==c.trim()&&(i=c.trim());let r=getDate(t.find(".chapter-duedate").val());if(validateMandatoryFields(t))return showErrorDialogBox("Chapter Name Must be given"),n=!0,!1;null!=r&&(r=r.getTime()/1e3);let p={};"undefined"!=o&&"None"!=o||(o=null),""!==d&&(p={chapterId:o,title:d,description:i,dueDate:r,chapterTopicPayloadList:[]}),a.find(".topic-details tbody tr").each(function(){if(n)return!1;const e=$(this),a=e.find(".topic-id").val();let t=null;a&&""!==a.trim()&&(t=a.trim());const s=e.find(".topic-name").val();let o="";s&&""!==s.trim()&&(o=s.trim());const l=e.find(".topic-description").val();let c="";l&&""!==l.trim()&&(c=l.trim());let i=getDate(e.find(".topic-duedate").val());if(validateMandatoryFields(e))return showErrorDialogBox("Topic Name Must be given"),n=!0,!1;null!=i&&(i=i.getTime()/1e3),"None"!=t&&"undefined"!=t||(t=null),""!==d&&""!==o&&p.chapterTopicPayloadList.push({topicId:t,title:o,description:c,dueDate:i})}),""!==d&&e.push(p)}),!n&&e},collectStaffResponsePayload:function(e,n){const a=[];let t=!1;return $("#chapter-container .card.card-border").each(function(){if(t)return!1;const s=$(this),o=s.find(".chapter-details tbody tr").find(".chapter-id").text();let l=null;o&&""!==o.trim()&&(l=o.trim()),s.find(".topic-details tbody tr").each(function(){if(t)return!1;const s=$(this);let o=s.find(".topic-id").val()||null;const d=s.find(".topic-remarks").val();let c="";d&&""!==d.trim()&&(c=d.trim());const i=s.find(".topic-status").val();let r="";i&&""!==i.trim()&&(r=i.trim());let p=getDate(s.find(".topic-startdate").val()),u=getDate(s.find(".topic-compeletedate").val());if(validateMandatoryFields(s))return showErrorDialogBox("Topic Status Must be given"),t=!0,!1;null!=p&&(p=p.getTime()/1e3),null!=u&&(u=u.getTime()/1e3);let f={};"None"!=o&&"undefined"!=o&&null!=o&&"undefined"!=l&&"None"!=l&&null!=l&&(f={lessonPlanId:e,staffId:n,chapterId:l,topicId:o,startedOn:p,completedOn:u,lessonPlanStatus:r,remarks:c},a.push(f))})}),!t&&a},selectLessonPlanList:function(e){if(e.checked){$(".lesson-plan-course-clone").prop("checked",!0);var n=$("input.lesson-plan-course-clone:checkbox:checked").length;$("#lesson-plan-course-clone-count").html(n),lessonPlanPage.dataCache.selectedLessonPlanCount=n}else $(".lesson-plan-course-clone").prop("checked",!1),$("#lesson-plan-course-clone-count").html(0),lessonPlanPage.dataCache.selectedLessonPlanCount=0},lessonPlanSelectCheckbox:function(e){var n=$("input.lesson-plan-course-clone:checkbox:checked").length;$("#lesson-plan-course-clone-count").html(n),lessonPlanPage.dataCache.selectedLessonPlanCount=n},getSelectedBulkLessonPlanIds:function(){var e=[];return $("input.lesson-plan-course-clone").each(function(){if($(this).is(":checked")){var n=$(this).parent().parent().find("p.lesson-plan-id").text().trim();null!=n&&""!=n&&"None"!=n&&"undefined"!=n&&e.push(n)}}),lessonPlanPage.dataCache.selectedLessonPlanCount=e.length,e},updatePopupStandardSections:function(e,n){for(var a=readJson(".standards-json"),t={},s=0;s<a.length;s++){t[(l=a[s]).standardId]=l}var o="";if(e in t){var l=t[e];for(s=0;s<l.standardSectionList.length;s++)l.standardSectionList[s].sectionId!=n&&(o+='<option value="'+l.standardSectionList[s].sectionId+'">'+l.standardSectionList[s].sectionName+"</option>");$(".clone-lesson-section").html(o)}else $(".clone-lesson-section").html(o)}},lessonPlanAndStaffResponseUI={globalChapterCounter:0,createTextarea:function(e="",n="",a=""){const t=document.createElement("textarea");return t.className=`form-control ${a} ${n}`,t.value=e,t},createInput:function(e="",n="",a="",t=""){const s=document.createElement("input");return s.className=`form-control select-date ${t} ${a}`,s.type="text",s.placeholder=n,s.value=e,s},createSelect:function(e=[],n="",a="",t=""){const s=document.createElement("select");return s.className=`form-control ${t} ${a}`,e.forEach(e=>{const a=document.createElement("option");a.value=e.value,a.textContent=e.label,e.value===n&&(a.selected=!0),s.appendChild(a)}),s},createTrashIcon:function(e){const n=document.createElement("i");return n.className="fas fa-trash text-danger",n.style.cursor="pointer",n.onclick=e,n},createTopicRow:function(e={},n=!0){const a=document.createElement("tr");a.style.textAlign="center";const t=document.createElement("input");if(t.type="hidden",t.className="topic-id",t.value=e.topicId||"",a.appendChild(t),n)return a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTrashIcon(()=>a.remove()))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(e.title||"","topic-name","mandatory-field"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(e.description||"","topic-description"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(e.dueDate)||"","Enter Due Date ...","topic-duedate"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(e.staffStartedOn)||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(e.staffCompletedOn)||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(e.staffTopicStatus||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(e.remarks||"-"))),a;return a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(e.title||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(e.description||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(e.dueDate)||"-"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(e.staffStartedOn)||"","Enter Started Date ...","topic-startdate"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(e.staffCompletedOn)||"","Enter Complete Date ...","topic-compeletedate"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createSelect([{value:"NOT_STARTED",label:"NOT STARTED"},{value:"IN_PROGRESS",label:"IN PROGRESS"},{value:"COMPLETED",label:"COMPLETED"}],e.staffTopicStatus||"NOT_STARTED","topic-status","mandatory-field"))),a.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(e.remarks||"","topic-remarks"))),a},createCell:function(e){const n=document.createElement("td");return e instanceof Node?n.appendChild(e):n.innerText=e,n},createTopicSection:function(e=[],n=!0){const a=document.createElement("table");a.className="table table-bordered table-hover topic-details",a.style.minWidth="1500px";const t=a.createTHead();t.innerHTML=n?'\n      <tr style="text-align: center;">\n        <th>Action</th>\n        <th>Name*</th>\n        <th>Description</th>\n        <th>DueDate</th>\n        <th>Started On</th>\n        <th>Completed On</th>\n        <th>Status</th>\n        <th>Remarks</th>\n      </tr>':'\n      <tr style="text-align: center;">\n        <th>Name*</th>\n        <th>Description</th>\n        <th>DueDate</th>\n        <th>Started On</th>\n        <th>Completed On</th>\n        <th>Status</th>\n        <th>Remarks</th>\n      </tr>';const s=document.createElement("tbody");e.forEach(e=>{s.appendChild(lessonPlanAndStaffResponseUI.createTopicRow(e,n))}),a.appendChild(s);const o=document.createElement("div");if(o.className="card-body",o.style.overflowX="auto",o.appendChild(a),n){const e=document.createElement("a");e.textContent="Add More Topic",e.style="text-decoration: none; color: #1F9BCF; cursor: pointer;",e.onclick=(()=>{const e=lessonPlanAndStaffResponseUI.createTopicRow();s.appendChild(e),initDateWithYearRange("-1:+1")}),o.appendChild(e)}return o},createChapterBox:function(e={},n=[],a=!0){let t=lessonPlanAndStaffResponseUI.globalChapterCounter;const s=`chapter-${t++}`,o=document.createElement("div");o.className="card card-border",o.id=s;const l=document.createElement("div");l.className="card-header",l.innerHTML=`\n      <div class="row mb-2 mb-xl-3">\n        <div class="col-auto mr-auto text-left mt-n1">\n          <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-${s}">\n            <h5 style="padding-bottom:0px;color:#43a2ad;">Chapter ${t}</h5>\n          </button>\n        </div>\n      </div>`;const d=lessonPlanAndStaffResponseUI.createTrashIcon(()=>{o.remove(),0===document.querySelectorAll("#chapter-container .card.card-border").length&&($("#add-lesson-Plan").css("display","none"),$(".no-chapter").show())});if(a){const e=document.createElement("div");e.className="col-auto ml-auto text-right mt-n1",e.appendChild(d),l.querySelector(".row").appendChild(e)}const c=document.createElement("div");c.className="collapse show",c.id=`collapse-${s}`;const i=`<td style="display: none;" class="chapter-id">${e.chapterId}</td>`;let r="";r=a?`\n        <tr style="text-align: center;">\n          ${i}\n          <td><textarea class="form-control mandatory-field chapter-title">${e.title||""}</textarea></td>\n          <td><textarea class="form-control chapter-description">${e.description||""}</textarea></td>\n          <td><input type="text" class="form-control select-date chapter-duedate" placeholder="Enter Due Date ..." value="${getFormattedDate(e.dueDate)||""}"/></td>\n        </tr>`:`\n        <tr style="text-align: center;">\n          ${i}\n          <td>${e.title||"-"}</td>\n          <td>${e.description||"-"}</td>\n          <td>${getFormattedDate(e.dueDate)||"-"}</td>\n        </tr>`,c.innerHTML=`\n    <div class="card-body">\n      <table class="table table-bordered table-hover chapter-details">\n        \n      <thead>\n        <tr style="text-align: center;">\n          <th>Chapter Name*</th>\n          <th>Chapter Description</th>\n          <th>Chapter DueDate</th>\n        </tr>\n      </thead>\n        <tbody>${r}</tbody>\n      </table>\n    </div>`;const p=document.createElement("div");p.className="card card-border mt-3";const u=document.createElement("div");u.className="card-header",u.innerHTML=`\n      <h5 class="mb-0">\n        <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-topic-${s}" aria-expanded="true">\n          <h5 style="padding-bottom:0px;color:#43a2ad;">Topic</h5>\n        </button>\n      </h5>\n    `;const f=document.createElement("div");return f.className="collapse show",f.id=`collapse-topic-${s}`,f.appendChild(lessonPlanAndStaffResponseUI.createTopicSection(n,a)),p.appendChild(u),p.appendChild(f),c.querySelector(".card-body").appendChild(p),o.appendChild(l),o.appendChild(c),lessonPlanAndStaffResponseUI.globalChapterCounter=t,o},renderLessonPlan:function(e=[],n=0,a=!0){const t=document.getElementById("chapter-details-div"),s=document.createElement("div");s.id="chapter-container",lessonPlanAndStaffResponseUI.globalChapterCounter=n,t.querySelectorAll("#chapter-container").forEach(e=>e.remove()),0!==e.length?(e.forEach(e=>{s.appendChild(lessonPlanAndStaffResponseUI.createChapterBox(e,e.chapterTopicDetails||[],a))}),$(".no-chapter").hide(),$("#add-lesson-Plan").css("display","flex")):($(".no-chapter").show(),$("#add-lesson-Plan").css("display","none")),t.appendChild(s)},addNewChapter:function(){$("#chapter-container").append(lessonPlanAndStaffResponseUI.createChapterBox()),initDateWithYearRange("-1:+1"),0!==document.querySelectorAll("#chapter-container .card.card-border").length&&($(".no-chapter").hide(),$("#add-lesson-Plan").css("display","flex"))}};