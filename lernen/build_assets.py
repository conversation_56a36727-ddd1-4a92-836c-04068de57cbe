#! /usr/bin/python3
import os
import sys
import subprocess
import json

MIN_JS_SUFFIX = ".min.js"
DIST_FOLDER = "dist"
STATIC = "static"
SLASH = "/"

def execute_gulp(env):
	cmd = "gulp --env=" + env
	print("Running cmd : " + cmd)
	subprocess.call(cmd,shell=True)
	print("Done executing gulp")


def get_updated_js_paths():
	proc = subprocess.Popen(['git','status', '-u'],stdout=subprocess.PIPE)
	js_paths = []
	while True:
	  line = proc.stdout.readline().decode("utf-8") 
	  print(line)
	  if MIN_JS_SUFFIX in line and DIST_FOLDER in line:
	  	js_paths.append(line.split("\n")[0])
	  if not line:
	    break
	  #the real code does filtering here
	return js_paths
  # print "test:", line.rstrip()

def get_files_under_folder(folder_name):
	proc = subprocess.Popen(['ls','-lrt', folder_name],stdout=subprocess.PIPE)
	files = []
	while True:
	  line = proc.stdout.readline()
	  tokens = line.split("\n")[0].split(" ")
	  file_name = tokens[len(tokens) - 1]
	  files.append(file_name)
	  if not line:
	    break
	  #the real code does filtering here
	return files

def get_html_asset_version_path():
	my_path = os.path.abspath(os.path.dirname(__file__))
	path = os.path.join(my_path, 'lernen/asset_version.json')
	return path

def prepare_html_update_cmds(js_paths, bucket_name, aws_profile, env):
	html_update_cmds = set()
	js_mv_cmds = set()
	js_s3_upload_cmds = set()
	modules_version_update = {}
	for js_path in js_paths:
		print(js_path)
		static_path = js_path.split(STATIC)[1]
		tokens = static_path.split(SLASH)
		module_name = tokens[1]
		file_name = tokens[len(tokens) - 1]
		existing_version = tokens[len(tokens) - 2]
		version_tokens = existing_version.split(".")
		new_version = None
		if(len(version_tokens) == 3):
			existing_minor_version = int(version_tokens[2])
			new_minor_version = existing_minor_version + 1
			new_version = version_tokens[0]+"."+version_tokens[1]+"."+str(new_minor_version)

		modules_version_update[module_name] = {'existing_version' : existing_version, 'new_version' : new_version}
	
	f = open(get_html_asset_version_path())
	html_version_data = json.load(f)
	print(html_version_data)
	f.close()

	for module_name in modules_version_update:
		modules_versions = modules_version_update[module_name]
		existing_version = modules_versions['existing_version']
		new_version = modules_versions['new_version']

		if new_version is None:
			print("----- Could not get new version for " + module_name)
			continue

		rel_path_prefix = "{module_name}/dist/{env}/js".format(module_name = module_name, env = env)
		mv_path_format = "{module_name}/static/{rel_path_prefix}/{version}/"
		src_mv_path = mv_path_format.format(module_name = module_name, rel_path_prefix = rel_path_prefix, version = existing_version)
		dest_mv_path = mv_path_format.format(module_name = module_name, rel_path_prefix = rel_path_prefix, version = new_version)

		js_mv_cmds.add("mv "+src_mv_path+" " + dest_mv_path)
		js_s3_upload_cmd_format = "aws s3 cp {local_path} s3://{bucket_name}/static/{rel_path_prefix}/{version}/ --recursive --profile {profile}".format(local_path = dest_mv_path, bucket_name = bucket_name, rel_path_prefix = rel_path_prefix, version = new_version, profile = aws_profile)
		js_s3_upload_cmds.add(js_s3_upload_cmd_format)

		html_version_data[env]['js'][module_name] = new_version

		
		# files = get_files_under_folder(src_mv_path)
		# print(files)
		# for file in files:
		# 	if ".min.js" not in file:
		# 		continue
		# 	js_path_format = "{rel_path_prefix}/{version}/{file_name}"
		# 	src_path_str = js_path_format.format(rel_path_prefix = rel_path_prefix, version = ".*", file_name = file)
		# 	dest_path_str = js_path_format.format(rel_path_prefix = rel_path_prefix, version = version, file_name = file)
		# 	cmd = "perl -i -pe's:"+src_path_str+":"+dest_path_str+":g' `grep -ril  --include \\*.html " +rel_path_prefix+ " *`"
		# 	html_update_cmds.add(cmd)

	print(html_version_data)
	return (js_mv_cmds, js_s3_upload_cmds, html_version_data)

def execute_cmds(cmds):
	for cmd in cmds:
		print("\n\nExecuting cmd : " + cmd)
		subprocess.call(cmd,shell=True)
		print("Done execution\n")


def upload_assets_to_s3(env, js_s3_upload_cmds):
	if(env != "prod"):
		print("\n\n-----------------Not updating assets to s3 for env : " + env + "-----------------\n\n")
		return 

	print("\n\n----------------- Uploading assets to s3 for env : " + env + "-----------------\n\n")
	execute_cmds(js_s3_upload_cmds)

def update_s3_assets_version(env, final_asset_version):
	if(env != "prod"):
		print("\n\n-----------------Not updating assets to s3 for env : " + env + "-----------------\n\n")
		return 

	print("\n\n----------------- Updating assets version for html : " + env + "-----------------\n\n")
	f = open(get_html_asset_version_path(), 'w')
	f.write(json.dumps(final_asset_version))
	f.close()

def execute():
	env = input("Env : ")
	print("\n\n----------------- Executing assets update for env : " + env +" -----------------\n\n")
	execute_gulp(env)
	if(env != "prod"):
		print("Done updating assets. Not updating any versions")
		return

	print("Updating asset versions for env = " + env)
	print("\n\n----------------- Getting updated js paths ... -----------------\n\n")
	js_paths = get_updated_js_paths()
	print(js_paths)
	print("\n")
	if(len(js_paths) == 0):
		print("\nNo js paths are updated. Skipping further process\n")
		return

	print("\n\n----------------- Preparing update cmds ... -----------------\n\n")
	cmds_set = prepare_html_update_cmds(js_paths, "lernen-fe-assets-v5", "admin_2025", env)
	print(cmds_set)
	print("\n\n----------------- Executing folder version update -----------------\n\n")
	execute_cmds(cmds_set[0])
	upload_assets_to_s3(env, cmds_set[1])
	update_s3_assets_version(env, cmds_set[2])

execute()

