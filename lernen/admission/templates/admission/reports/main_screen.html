{% load json %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>REPORTS</h3>
    </div>
</div>
<br>

<div>
    <div class="row">

      <div class="col-sm-4">
        <div id="student-status-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup(this);">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Details Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get detailed report of students statuses</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-enrollment-by-age-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Enrollment By Age</strong></h4>
            <p class="card-body" style="color:#6c757d">Get student enrollment distribution by class, age and gender</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="class-category-distribution-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Category Wise Student Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get detailed report of students distributed in various categories</p>
          </div>
        </div>
      </div>

    </div>
    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="class-religion-distribution-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Religion Wise Student Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get detailed report of students distributed in various religions</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-mobile-track-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Mobile App Track Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get track of student's mobile app activities</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="class-gender-distribution-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Gender Wise Student Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get detailed report of students distributed in various gender</p>
          </div>
        </div>
      </div>

    </div>
  <br/>
    <div class="row">
      <div class="col-sm-4">
        <div id="student-house-summary-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>House Summary Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get summary report of number of students present in each house</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-document-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Document Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get documents details of students </p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-sibling-details-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Siblings Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get detailed report of siblings.</p>
          </div>
        </div>
      </div>

    </div>
    <br/>
    <div class="row">
      <div class="col-sm-4">
        <div id="student-count-report" class="card-hover bg-light report-card" onclick="admissionReports.resetPopup();">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Count Report (Section Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Get a detailed report of the student count by section.</p>
          </div>
        </div>
      </div>
    </br>
    </div>

</div>

    <div class="modal fade bd-example-modal-xl" id="student-status-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle">All Student Details Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            {% include 'core/reports/filter_page.html' %}
            <div class="form-row">

              <div class="form-group col-md-3">
                <label>Final Student Status</label>
                <select class="form-control final-student-status select2" multiple data-toggle="select2">
                  <option value="ENROLLED">Enrolled</option>
                  <option value="RELIEVED">Relieved</option>
                  <option value="ENROLMENT_PENDING">Enrollment Pending</option>
                  <option value="NSO">NSO</option>
                </select>
              </div>
              <div class="form-group col-md-3">
                <label>Sort By</label>
                <select  class="form-control student-sort-by-filter">
                  <option value="">Select</option>
                  <option value="STUDENT_NAME">Student Name</option>
                  <option value="ROLL_NUMBER">Roll Number</option>
                  <option value="ADMISSION_NUMBER">Admission Number</option>
                  <option value="ADMISSION_DATE">Admission Date</option>
                </select>

              </div>
              <div class="form-group col-md-3">
                <label for="date-range-filter">Select Date Range</label>
                <input type="text" class="form-control " id="date-range-filter"  placeholder="Select Date Range" />
              </div>
              <div class="form-group col-md-2" style="padding-top:35px;">
                 <div class="form-check">
                  <input type="checkbox" class="form-check-input new-admission">
                  <label class="form-check-label" for="select-report-student-class"> New Admission </label>
                </div>
              </div>
              <!-- <div class="form-group col-md-3">
                <label>Download As*</label>
                 <select class="form-control download-format mandatory-field">
                     <option value="EXCEL" selected>Excel</option>
                 </select>
              </div> -->
           </div>
            {% include 'core/reports/header_list_for_admission.html' with  header_columns=report_header_columns.STUDENT_DETAILS_REPORT %}
          </div>
          <p style="display:none;" class="report-type">STUDENT_DETAILS_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal" id="student-enrollment-by-age-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle"> Student Enrollment By Age </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">STUDENT_ENROLLMENT_BY_AGE</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>


    <div class="modal fade bd-example-modal" id="class-category-distribution-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle"> Class Category Distribution Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">CAST_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal" id="class-religion-distribution-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle"> Class Religion Distribution Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">RELIGION_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal-xl" id="student-mobile-track-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle"> Student Mobile App Tracking Details Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="form-group col-md-3 class-attendance-type-div">
                <label>Classes</label>
                <select class="form-control reports-student-class select2" multiple data-toggle="select2">
                  {% for standard in standards %}
                    {% if standard.standardSectionList %}
                        {% for section in standard.standardSectionList %}
                          {% with section.sectionId|stringformat:"s" as section_id %}
                            {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                              <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                            {% endwith %}
                          {% endwith %}
                        {% endfor%}
                    {% else %}
                      <option value={{standard.standardId}}>{{standard.displayName}}</option>
                    {% endif %}
                  {% endfor%}
                </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">MOBILE_APP_TRACK</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal" id="class-gender-distribution-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle"> Class Gender Distribution Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">GENDER_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal" id="student-house-summary-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle">House Summary Report </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="form-group col-md-6">
                <label>Download As*</label>
                 <select class="form-control download-format mandatory-field">
                     <option value="EXCEL" selected>Excel</option>
                     <option value="PDF">Pdf</option>
                 </select>
              </div>
            </div>
          </div>
          <p style="display:none;" class="report-type">HOUSE_SUMMARY_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal fade bd-example-modal" id="student-document-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content report-field-container">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLongTitle">Student Document Details </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Academic Session*</label>
                <select class="form-control report-academic-session mandatory-field">
                  {% for academic_year in academic_years %}
                    <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="form-group col-md-6 class-attendance-type-div">
                <label>Classes</label>
                <select class="form-control reports-student-class select2" multiple data-toggle="select2">
                  {% for standard in standards %}
                    {% if standard.standardSectionList %}
                        {% for section in standard.standardSectionList %}
                          {% with section.sectionId|stringformat:"s" as section_id %}
                            {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                              <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                            {% endwith %}
                          {% endwith %}
                        {% endfor%}
                    {% else %}
                      <option value={{standard.standardId}}>{{standard.displayName}}</option>
                    {% endif %}
                  {% endfor%}
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Student Status</label>
                <select class="form-control student-status select2" multiple data-toggle="select2">
                  <option value="ENROLLED">Enrolled</option>
                  <option value="RELIEVED">Relieved</option>
                  <option value="ENROLMENT_PENDING">Enrollment Pending</option>
                  <option value="NSO">NSO</option>
                </select>
              </div>

              <div class="form-group col-md-6">
                <label>Document Types</label>
                <select class="form-control document-types select2" multiple data-toggle="select2">
                  <option value="TRANSFER_CERTIFICATE">Transfer Certificate</option>
                  <option value="AADHAR_CARD">Aadhar Card</option>
                  <option value="STUDENT_PAN_CARD">Pan Card</option>
                  <option value="STUDENT_PROFILE_IMAGE">Student Profile Image</option>
                  <option value="FATHER_PROFILE_IMAGE">Father Profile Image</option>
                  <option value="MOTHER_PROFILE_IMAGE">Mother Profile Image</option>
                  <option value="RATION_CARD">Ration Card</option>
                  <option value="BIRTH_CERTIFICATE">Birth Certificate</option>
                  <option value="PREVIOUS_SCHOOL_STUDY_CERTIFICATE">Previous School Study Certificate</option>
                  <option value="BIRTH_CERTIFICATE_AND_AFFIDAVIT">Birth Certificate & Affidavit</option>
                </select>
              </div>
            </div>

          </div>
          <p style="display:none;" class="report-type">STUDENT_DOCUMENT_REPORT</p>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
          </div>
        </div>
      </div>
    </div>



 <div class="modal fade bd-example-modal-md" id="student-sibling-details-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Sibling Details Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-6">
            <label>Classes</label>
            <select class="form-control reports-student-class select2" multiple data-toggle="select2">
              {% for standard in standards %}
                {% if standard.standardSectionList %}
                    {% for section in standard.standardSectionList %}
                      {% with section.sectionId|stringformat:"s" as section_id %}
                        {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                          <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                        {% endwith %}
                      {% endwith %}
                    {% endfor%}
                {% else %}
                  <option value={{standard.standardId}}>{{standard.displayName}}</option>
                {% endif %}
              {% endfor%}
            </select>
          </div>
          <div class="form-group col-md-6">
              <label>Student Status</label>
              <select class="form-control student-status select2" multiple data-toggle="select2">
                <option value="ENROLLED">Enrolled</option>
                <option value="RELIEVED">Relieved</option>
                <option value="ENROLMENT_PENDING">Enrollment Pending</option>
                <option value="NSO">NSO</option>
              </select>
          </div>
        </div>
      </div>
      <p style="display:none;" class="report-type">SIBLING_DETAILS_REPORT</p>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
        <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade bd-example-modal-md" id="student-count-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Student Count Report (Section Wise)</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          {% include 'core/reports/standard_dropdown.html' %}
          {% include 'core/reports/student_status_dropdown.html' %}
        </div>
      </div>
      <p style="display:none;" class="report-type">SECTION_WISE_STUDENT_COUNT_REPORT</p>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
        <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>
