<div class="modal" id="student-ingestion-configuration-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="title">Configure Student Ingestion</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
          <div class="row">
              <div class="col-md-3">
                  <label> File Delimiter </label>
                  <input type="text" class="form-control" id="file-delimiter" placeholder="File Deliiter" {% if student_ingestion_configurations.studentDataDelimiter %} value="{{student_ingestion_configurations.studentDataDelimiter}}" {% endif %}>
              </div>
              <div class="col-md-3">
                  <label> Class Column Format </label>
                  <select id="select-class-input-format" class="form-control mandatory-field">
                      <option value="CLASS_NAME" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentClassColumnFormat == 'CLASS_NAME' %} selected {%endif%}> CLASS_NAME</option>
                      <option value="CLASS_STREAM_SECTION" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentClassColumnFormat == 'CLASS_STREAM_SECTION' %} selected {%endif%}> CLASS_STREAM_SECTION</option>
                      <option value="CLASS_SECTION_STREAM" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentClassColumnFormat == 'CLASS_SECTION_STREAM' %} selected {%endif%}> CLASS_SECTION_STREAM </option>
                      <option value="CLASS_SECTION" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentClassColumnFormat == 'CLASS_SECTION' %} selected {%endif%}> CLASS_SECTION </option>
                      <option value="CLASS_STREAM" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentClassColumnFormat == 'CLASS_STREAM' %} selected {%endif%}> CLASS_STREAM </option>
                  </select>
              </div>
              <div class="col-md-3" style="margin-top:2em;">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="file-header" {% if student_ingestion_configurations.studentDataHeader %} checked {%endif%}>
                    <label class="form-check-label" for="file-header-present">
                      Header Line
                    </label>
                  </div>
              </div>

              <div class="col-md-3" style="margin-top:2em;">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="multi-column-class" {% if student_ingestion_configurations.studentClassColumnField and student_ingestion_configurations.studentClassColumnField.studentDataMultiColumnClass == True %} checked {%endif%}>
                    <label class="form-check-label" for="file-header-present">
                      Multi Column Class
                    </label>
                  </div>
              </div>
          </div>
          <br />
          <div class="row" id="multi-class-column-info" {% if student_ingestion_configurations.studentClassColumnField is Nonr or student_ingestion_configurations.studentClassColumnField.studentDataMultiColumnClass == False %} style="display:none" {%endif%}>
              <div class="col-md-4">
                  <label> Class Column Index </label>
                  <input type="number" class="form-control" id="class-column-index" placeholder="Class Column" {% if student_ingestion_configurations.studentClassColumnField %} value="{{student_ingestion_configurations.studentClassColumnField.classColumn}}" {%endif%}>
              </div>
              <div class="col-md-4">
                  <label> Section Column Index </label>
                  <input type="number" class="form-control" id="section-column-index" placeholder="Section Column" {% if student_ingestion_configurations.studentClassColumnField %} value="{{student_ingestion_configurations.studentClassColumnField.sectionColumn}}" {%endif%}>
              </div>
              <div class="col-md-4">
                <label> Stream Column Index </label>
                  <input type="number" class="form-control" id="stream-column-index" placeholder="Stream Column" {% if student_ingestion_configurations.studentClassColumnField %} value="{{student_ingestion_configurations.studentClassColumnField.streamColumn}}" {%endif%}>
              </div>
          </div>
          <br/>
          <div class="row">
              <div class="col-md-3">
                  <label> DOB Format </label>
                  <input type="text" class="form-control" id="dob-format" placeholder="DOB Format" {% if student_ingestion_configurations.dobFormat %} value="{{student_ingestion_configurations.dobFormat}}" {% endif %}>
              </div>
              <div class="col-md-3">
                  <label> Admission Date Format </label>
                  <input type="text" class="form-control" id="admission-date-format" placeholder="Admission Date Format" {% if student_ingestion_configurations.dobFormat %} value="{{student_ingestion_configurations.admissionDateFormat}}" {% endif %}>
              </div>
          </div>
          </br>
          </br>
          <div class="row">
            <div class="col-md-12 d-flex justify-content-end">
              <a class="btn btn-link text-danger p-0" style="text-decoration: underline;" id="clear-all-fields">Clear All</a>
            </div>
          </div>
          <div id="student-data-field-container">
            <table class="table table-hover">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col" class="w-50">Field Name</th>
                    <th scope="col">Column Index</th>
                    <th scope="col">Mandatory</th>
                  </tr>
                </thead>
                <tbody>
                  {% for studentDataField in student_ingestion_configurations.studentDataFieldList %}
                    <tr id={{studentDataField.studentDataFieldName}} class="student-data-field-row">
                      <th scope="row">{{forloop.counter}}</th>
                      <td>{{studentDataField.studentDataFieldName}}</td>
                      <td> <input type="number" class="form-control column-index" {% if studentDataField.index is not None %} value="{{studentDataField.index}}" {% endif %}  /> </td>
                      <td><label class="switch"> <input type="checkbox" class="primary toggle-switch table-mandatory-switch" {% if studentDataField.mandatory %} checked {% endif %}> <span class="slider round"></span> </label> </td>
                    </tr>
                  {% endfor %}
                </tbody>
            </table>
          </div>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-dismiss="modal" onclick="">Close</button>
          <button type="button" class="btn btn-outline-danger" onclick="ingestStudent.confirmApplyIngestionConfig()">Apply Configuration</button>
      </div>
    </div>
  </div>
</div>
