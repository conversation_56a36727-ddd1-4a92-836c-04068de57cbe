from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.client.institute_payment_manager import get_payment_status_data
from core.controller.user.notification_manager import get_bell_notification_count
from core.controller.utils.tracking_events import track_event
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from core.controller.user.authentication import *
from core.controller.utils.tracking_events import *
from core.controller.utils.template_manager import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.reports_manager import *
from core.controller.utils.authorised_actions import *
from datetime import date
from math import *
from core.controller.utils.authorised_actions import *

from lesson_plan.controller.lesson_plan_manager import *
from user_management.controller.user_management_manager import user_details


MODULE_NAME = 'LESSON_PLAN'

def authorized_user_session(request):
	return authorized_module(request, MODULE_NAME)

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "LESSON_PLAN_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		return render(request, 'lesson_plan/layouts/default-lesson-plan-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' :  payment_status_data, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access})
	
def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'lesson_plan/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'lesson_plan/layouts/dashboard-session-content.html',{'user': user_login_view['user']})

def lesson_plan_main_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		lesson_plan_admin_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_ADMIN_ACCESS)
		lesson_plan_staff_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_STAFF_ACCESS)
		lesson_plan_staff_response = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, STAFF_RESPONSE_ACCESS)
		if lesson_plan_admin_access :
			standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
			return render(request, 'lesson_plan/course_details_lesson_plan/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards, 'lesson_plan_admin_access': lesson_plan_admin_access, 'lesson_plan_staff_access': lesson_plan_staff_access, 'lesson_plan_staff_response' : lesson_plan_staff_response})
		course_staff_details_list = get_course_staff_lesson_plan_details(user_login_view, institute_unique_code, current_session['academicSessionId'], None, None)
		return render(request, 'lesson_plan/course_details_lesson_plan/main_screen_staff.html', {"academic_years":academic_years,'current_session':current_session, 'lesson_plan_admin_access': lesson_plan_admin_access, 'lesson_plan_staff_access': lesson_plan_staff_access, 'lesson_plan_staff_response' : lesson_plan_staff_response, 'course_staff_details_list': course_staff_details_list})

def get_session_standards_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		lesson_plan_admin_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_ADMIN_ACCESS)
		lesson_plan_staff_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_STAFF_ACCESS)
		lesson_plan_staff_response = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, STAFF_RESPONSE_ACCESS)
		if lesson_plan_admin_access :
			standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
			return render(request, 'lesson_plan/course_details_lesson_plan/standard_with_section_options.html',{'standards':standards, 'lesson_plan_admin_access': lesson_plan_admin_access, 'lesson_plan_staff_access': lesson_plan_staff_access, 'lesson_plan_staff_response' : lesson_plan_staff_response})
		course_staff_details_list = get_course_staff_lesson_plan_details(user_login_view, institute_unique_code, academic_session_id, None, None)
		return render(request, 'lesson_plan/course_details_lesson_plan/course_and_lesson_plan_details_staff.html', {'lesson_plan_admin_access': lesson_plan_admin_access, 'lesson_plan_staff_access': lesson_plan_staff_access, 'lesson_plan_staff_response' : lesson_plan_staff_response, 'course_staff_details_list': course_staff_details_list})

def course_lesson_plan_view(request, institute_unique_code, standard_id, section_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		lesson_plan_admin_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_ADMIN_ACCESS)
		lesson_plan_staff_access = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, LESSON_PLAN_STAFF_ACCESS)
		lesson_plan_staff_response = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, STAFF_RESPONSE_ACCESS)
		course_staff_details_list = get_course_staff_lesson_plan_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id)
		return render(request, 'lesson_plan/course_details_lesson_plan/course_and_lesson_plan_details_admin.html', {'course_staff_details_list': course_staff_details_list, 'lesson_plan_admin_access': lesson_plan_admin_access, 'lesson_plan_staff_access': lesson_plan_staff_access, 'lesson_plan_staff_response' : lesson_plan_staff_response})

def lesson_plan_structure(request, institute_unique_code, lesson_plan_id, lesson_plan_status):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		lessonPlanChapterDetailList = get_lesson_plan_structure(user_login_view, institute_unique_code, lesson_plan_id, lesson_plan_status)
		return render(request, 'lesson_plan/lesson_plan_details/main_screen.html', {'lesson_plan_chapter_detail_list': lessonPlanChapterDetailList})

@csrf_exempt
def save_lesson_plan_data(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		course_id = request.GET.get('course_id',"")
		lesson_plan_data_details = json.loads(request.body)

		response_data = save_lesson_plan_details(user_login_view, institute_unique_code, academic_session_id, course_id, lesson_plan_data_details)
		return render(request, 'lesson_plan/status_modal.html',{"data":response_data})

@csrf_exempt
def save_staff_lesson_plan_response(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		course_id = request.GET.get('course_id',"")
		lesson_plan_staff_response = json.loads(request.body)

		response_data = save_staff_response_details(user_login_view, institute_unique_code, academic_session_id, course_id, lesson_plan_staff_response)
		return render(request, 'lesson_plan/status_modal.html',{"data":response_data})

@csrf_exempt
def clone_lesson_plan_details(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		lesson_plan_staff_response = json.loads(request.body)

		response_data = lesson_plan_details_clone(user_login_view, institute_unique_code, academic_session_id, lesson_plan_staff_response)
		return render(request, 'lesson_plan/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_lesson_plan_details(request, institute_unique_code, academic_session_id, lesson_plan_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_lesson_plan_details_all(user_login_view, institute_unique_code, academic_session_id, lesson_plan_id)
		return render(request, 'lesson_plan/status_modal.html',{"data":response_data})