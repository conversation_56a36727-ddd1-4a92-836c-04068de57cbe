"""This module handles the authentication of users."""

from core.controller.utils.restclient import *
from core.controller.utils.restclient import *
from django.shortcuts import redirect
from django.http import Http404
from django.core.exceptions import PermissionDenied
from core.controller.utils.utility import *
from core.controller.user.institute import *

user_portal_mapping = {'STUDENT' : 'studentportal', 'ADMIN' : 'adminportal' , 'STAFF' : 'adminportal', 'INTERNAL' : 'adminportal'}
restclient = getrestclient()
restclientWithoutUser = getrestclientWithoutUser()
login_page = '/core/login'
default_theme = "default"

def authenticate(username, password):
	return restclientWithoutUser.post_without_user("/2.0/users/authenticate?is_web=true",{'userName':username, 'password' : password}, True)

def get_user_permissions(user_id):
	return restclientWithoutUser.get_without_user("/2.0/user-permission/"+str(user_id)).get_data()

def authorized_user(request):
	session = request.session
	if not session.has_key('username') or not session.has_key('user'):
		return False
	# return redirect(login_page)
	raise PermissionDenied
	# raise Http403("Not authorized to access this module")

def multiple_branch_user(user_login_view):
	user = user_login_view['user']
	user_type = user['userType']
	return user_login_view['organisation']['organisationId'] is not None and user['instituteScope'] is not None and (user_type == 'ADMIN' or user_type == 'STAFF' or user_type == 'INTERNAL')

def get_user_portal_path(user_type, institute_unique_code):
	return "/"+institute_unique_code+"/"+user_portal_mapping[user_type] + "/dashboard"

def authorized_module(request, module):
	if _has_module_access(request,module):
		return True
	# return redirect(login_page)
	raise PermissionDenied
	# raise Http403("Not authorized to access this module")

def _has_module_access(request, module):
	session = request.session
	if not session.has_key('user_login_view'):
		return False

	user_login_view = get_user_login_view(request)
	authorized_modules = user_login_view['user']['authorizedModules']
	if authorized_modules is None or len(authorized_modules) == 0:
		return False

	moduleIdList = []
	for modules in authorized_modules:
		if modules is not None :
			moduleIdList.append(modules['moduleId'])

	if module in moduleIdList:
		return True

	return False

def is_user_view_only(request):
	session = request.session
	if not session.has_key('user_login_view'):
		return False

	user_login_view = get_user_login_view(request)
	# Check if 'user' key exists and is valid
	user = user_login_view.get('user')
	if not user:
		return False

	# Safely check for 'viewOnlyAccess' key in 'user'
	return user.get('viewOnlyAccess', False)

def authorized_organisation_portal(request):
	session = request.session
	if not session.has_key('user_login_view'):
		raise PermissionDenied

	user_login_view = get_user_login_view(request)
	user_type = user_login_view['user']['userType']
	if user_login_view['organisation']['organisationId'] is not None and (user_type == 'ADMIN' or  user_type == 'STAFF' or 'INTERNAL'):
		return True
	raise PermissionDenied

def authorized_user_portal(request, portal_user_type):
	session = request.session
	if not session.has_key('user_login_view'):
		raise PermissionDenied

	user_login_view = get_user_login_view(request)
	user_type = user_login_view['user']['userType']

	user_type_arr = portal_user_type.split(",")
	if user_type is None or not user_type in user_type_arr:
		raise PermissionDenied

	return True
	# raise Http403("Not authorized to access this page")


def change_password(user_login_view, change_password_data):
	institute_id = user_login_view['user']['instituteId']
	user_name = user_login_view['user']['userName']
	uuid = user_login_view['user']['uuid']
	update_password_payload = {'userId' : uuid, 'userName' : user_name, 'oldPassword' : str(change_password_data['oldPassword']), 'newPassword' : str(change_password_data['newPassword'])}
	response = restclient.post(user_login_view, "/2.0/users/change-password/institute/"+str(institute_id), update_password_payload)
	if not response.is_success():
		return {'success' : False , 'message' : response.get_error_message()}
	return {'success' : True , 'message' : "Changed Password Successfully!!!"}


def get_app_attributes(user_login_view, institute_unique_code):
	user_type = user_login_view['user']['userType']
	user_portal_dashboard_path = get_user_portal_path(user_type, institute_unique_code)
	organisation_dashboard_path = None
	multi_branch_user = False
	if multiple_branch_user(user_login_view) :
		multi_branch_user = True
		organisation_dashboard_path = "/organisation-portal/dashboard"

	return {'user_portal_dashboard_path' : user_portal_dashboard_path, 'multiple_branch_user' : multi_branch_user, 'organisation_dashboard_path' : organisation_dashboard_path}

def send_forget_password_email(username, delivery_mode):
	response = restclientWithoutUser.post_without_user("/2.0/users/forgot-password-send-email?username="+str(username) + "&delivery_mode=" + str(delivery_mode), {})
	if not response.is_success():
		return {'success' : False , 'message' : response.get_error_message()}
	return {'success' : True , 'message' : "Reset password link sent successfully to the registered email", 'data' : response.get_data()}

def verify_link(user_id, token_id):
	return restclientWithoutUser.get_without_user("/2.0/users/verify-link/user/" + str(user_id) + "/token/" + str(token_id),{}).get_data()

def reset_password(reset_password_payload):
	response = restclientWithoutUser.post_without_user("/2.0/users/reset-password", reset_password_payload)
	if not response.is_success():
		return {'success' : False , 'message' : response.get_error_message()}
	return {'success' : True , 'message' : "Password Reset Successfully!!!"}

def get_user_by_user_name(username):
	return restclientWithoutUser.get_without_user("/2.0/users/username?username="+str(username), {}).get_data()
