from django.contrib import admin
from django.urls import path
from django.urls import include
from student_finance import views
from student_finance.views import *

urlpatterns = [

    # Student Finance Home Page
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),
    path('student-finance-details', student_finance_details_view, name='student_finance_details_view'),
    path('student-live-search/<academic_session_id>', student_live_search_view, name='student_live_search_view'),
    path('student-expense-details/<session_id>/<student_id>', get_student_expence_details_view, name='get_student_expence_details_view'),
    path('add-student-expense-transaction', add_student_expence_transaction_view, name='add_student_expence_transaction_view'),
    path('generate-report/<report_type>', generate_report_view, name='generate_report_view'),
]
