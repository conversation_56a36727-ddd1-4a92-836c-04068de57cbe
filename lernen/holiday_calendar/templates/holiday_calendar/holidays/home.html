{% load json %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto ml-auto text-right mt-n1">
      <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#add-new-holiday-modal" data-keyboard="false" data-toggle="modal" data-backdrop="static" onclick="holidayConfig.resetHolidayModal()">Add New Holiday</button>
      <!-- <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#" onclick="">Import Holidays</button> -->
    </div>
</div>

<div class="container-fluid p-0">

  <div class="card">
    <br/>
  <div class="card-body">
      <div class="form-row align-items-center">
          <div class="col-sm-3 my-1">
          <label>Academic Session*</label>
          <select class="form-control holiday-academic-session mandatory-field">
            {% for academic_year in academic_years %}
              <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-sm-3 my-1" style="padding-top:1.8rem;">
          <button class="btn btn-primary" onclick="holidayConfig.loadSessionHolidays()" style="width:80%">Load Holidays</button>
        </div>
      </div>
      <div id="session-holidays-container">
        <br><br><br><br>
        <br>
        <br>
        <br><br><br><br>
        <h5 style="text-align:center;color:#223b5c;"> To view holidays, select particular session and load!</h5>
        <br><br><br><br>
        <br>
        <br>
        <br><br><br><br>
      </div>
  </div>
  </div>

</div>

<div id="add-new-holiday-modal-container">
  <div id="add-new-holiday-modal" class="modal fade bd-example-modal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
       <div class="modal-dialog modal-md">
           <div class="modal-content">
               <div class="modal-header">
                   <h5 class="modal-title"> Add New Holiday </h5>
                   <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                       <span aria-hidden="true"> &times; </span>
                   </button>
               </div>
               <div class="modal-body">
                 <p style="display:none" id="holiday-id"></p>
                 <div class="form-row">
                   <div class="form-group col-md-6">
                     <label>Holiday Name*</label>
                     <input type="text" class="form-control mandatory-field" id="holiday-name" placeholder="Holiday Name..." >
                   </div>
                   <div class="form-group col-md-6">
                     <label >Holiday Type *</label>
                     <select id="holiday-types" class="form-control mandatory-field">
                          <option value="STATIC">Fixed Dates</option>
                          <option value="RECURRING">Recurring</option>
                     </select>
                   </div>
                 </div>
                 <div class="form-row">
                   <div class="form-group col-md-6">
                     <label>Holiday Start Date*</label>
                     <input type="text" class="form-control select-date mandatory-field" id="holiday-start-date" placeholder="Holiday Start Date" >
                   </div>
                   <div class="form-group col-md-6">
                     <label>Holiday End Date*</label>
                     <input type="text" class="form-control select-date mandatory-field" id="holiday-end-date" placeholder="Holiday End Date" >
                   </div>
                 </div>
                 <br/>
                 <div id="recurring-holiday-input" style="display:none;">
                   <div class="form-row">
                     <div class="form-group col-md-4">
                       <label>Recurring Frequency*</label>
                       <select id="holiday-frequency-types" class="form-control">
                            <option value="WEEK">Weekly</option>
                            <option value="MONTH">Monthly</option>
                       </select>
                     </div>
                     <div class="form-group col-md-4">
                       <label>Week Day*</label>
                       <select id="holiday-weekdays" class="form-control">
                            <option value="1">Monday</option>
                            <option value="2">Tuesday</option>
                            <option value="3">Wednesday</option>
                            <option value="4">Thursday</option>
                            <option value="5">configureHolidayNav</option>
                            <option value="6">Saturday</option>
                            <option value="7">Sunday</option>
                       </select>
                     </div>
                     <div id="weeks-of-month-container" class="form-group col-md-4" style="display:none;">
                       <label>Week of Month*</label>
                       <select class="form-control mr-sm-4 select2" multiple data-toggle="select2" id="weeks-of-month" multiple data-style="bg-white border" title="Week of Month*">
                         <option value="">-choose-</option>
                         <option value="1">1</option>
                         <option value="2">2</option>
                         <option value="3">3</option>
                         <option value="4">4</option>
                         <option value="5">5</option>
                       </select>
                     </div>
                     </div>
                   </div>
                   <div class="form-group">
                     <label>Description</label>
                     <textarea class="form-control" id="holiday-description" rows="2" placeholder="Description"></textarea>
                   </div>
                 </div>
               <div class="modal-footer">
                   <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="closeModal()">Cancel</button>
                   <button type="button" class="btn btn-primary add-holiday" onclick="holidayConfig.addNewHoliday()">Add New Holiday</button>
                   <button type="button" class="btn btn-primary update-holiday" onclick="holidayConfig.updateHoliday()" style="display:none">Update Holiday</button>
               </div>
           </div>
       </div>
   </div>
</div>

<div class="modal fade" id="delete-holiday-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
  		<div class="modal-header">
  			<h5 class="modal-title">Delete Holiday</h5>
  			<button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
  		</div>
  		<div class="modal-body m-3">
        <p style="display:none" id="delete-holiday-id"></p>
        <p style="display:none" id="delete-academic-session-id"></p>

  			<p class="mb-0">
        Are you sure to delete this holiday?
        </p>
  		</div>
  		<div class="modal-footer">
  			<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-danger" onclick="holidayConfig.deleteHoliday()">Delete Holiday</button>
  		</div>
  	</div>
  </div>
</div>

<div id="import-holiday-modal-container"></div>
<div id="holiday-default-modal-container"></div>
