"""
Django settings for lernen project.

Generated by 'django-admin startproject' using Django 2.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""

import os

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = 'j0=mywk*jz$*%3w6k^*st5u6nm!b@g1-kql_j_bsl9-u1d)hbs'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = []

ROOT_URLCONF = 'lernen.urls'

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'website',
    'newwebsite',
    'home',
    'core',
    'store',
    'fees',
    'admission',
    'staffportal',
    'adminportal',
    'transport',
    'courses',
    'examination',
    'attendance',
    'staffmanagement',
    'income_expense',
    'user_management',
    'salary_management',
    'lecturemanagement',
    'homeworkmanagement',
    'noticeboard',
    'studentportal',
    'student_registration',
    'organisation_portal',
    'instituteonboarding',
    'reset_password',
    'studentmanagement',
    'auditlogs',
    'communication',
    'staff_attendance',
    'timetable_management',
    'inventory',
    'holiday_calendar',
    'frontdesk',
    'mobileapp_management',
    'diary',
    'institute_management',
    'leave_management',
    'library_management',
    'complaint_box',
    'staff_diary',
    'parents_appointment',
    'visitors_desk',
    'visitors_form',
    'enquiry_form',
    'hostel_management',
    'online_assessment',
    'lesson_plan',
    'student_finance',
    'study_tracker'

]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': ["templates"],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'lernen.wsgi.application'


# Database
# https://docs.djangoproject.com/en/2.0/ref/settings/#databases

# 'default': {
#     'ENGINE': 'django.db.backends.sqlite3',
#     'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
# }
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'lernen',
#         'USER': 'root',
#         'PASSWORD': 'root',
#         'HOST': 'localhost',   # Or an IP Address that your DB is hosted on
#         'PORT': '3306'
#     }
# }


CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': '/var/tmp/django_cache',
    }
}



# Password validation
# https://docs.djangoproject.com/en/2.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'

# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True

ROOT_URLCONF='lernen.urls'

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/

# STATIC_URL = 'https://assetsv5.embrate.com/static/'
# # STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
# # This is done bcz settings is folder now instead of file for multiple envs. Ow above one works
# STATIC_ROOT = os.path.join(BASE_DIR, '../static/')

# For local run
STATIC_URL = '/static/'
# STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
# This is done bcz settings is folder now instead of file for multiple envs. Ow above one works
STATIC_ROOT = os.path.join(BASE_DIR, '../static/')


try:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'HOST': os.environ['DJANGO_APP_DB_HOST'],   # Or an IP Address that your DB is hosted on
            'PORT': os.environ['DJANGO_APP_DB_PORT'],
            'NAME': os.environ['DJANGO_APP_DB_NAME'],
            'USER': os.environ['DJANGO_APP_DB_USER'],
            'PASSWORD': os.environ['DJANGO_APP_DB_PASSWORD']
        }
    }
except Exception as e:
    print("unable to load db configs from env vars")
