from time import time
from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from core.controller.user.notification_manager import *
from core.controller.utils.tracking_events import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.utility import *
from core.controller.utils.template_manager import *
from core.controller.utils.authorised_actions import *
from admission.controller.admission_manager import *
from math import *
import math
import time

def authorized_admin_user_session(request):
	return authorized_user_portal(request, 'ADMIN,STAFF,INTERNAL')

def get_current_time():
	return math.trunc(time.time())

def dashboard_view(request, institute_unique_code):
	if authorized_admin_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "ADMIN_PORTAL_LOADED"})
		user_login_view = get_user_login_view(request)
		user = user_login_view['user']

		institute = get_institute_details(user_login_view, institute_unique_code)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		quick_actions_details = get_quick_actions_details(user_login_view, institute_unique_code)
		display_modules = None
		if user['authorizedModules'] is not None:
			display_modules = get_display_modules(user['authorizedModules'], user_login_view, institute_unique_code)

		user_type = user_login_view['user']['userType']
		tutorial_videos = get_tutorial_video_details('ADMIN_PORTAL', institute_unique_code, user_type)
		return render(request, 'adminportal/layouts/default-admin-portal-layout.html',{'dashboard_theme' : get_user_theme(request), 'display_modules' : display_modules, 'institute_unique_code' : institute_unique_code, 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': institute, 'institute_metadata_preferences' : institute_metadata_preferences, 'payment_status_data': payment_status_data, 'view_only_access': view_only_access, 'bell_notification_count' : bell_notification_count, 'web_ui_preferences' : web_ui_preferences, 'quick_actions_details' : quick_actions_details, 'tutorial_videos': tutorial_videos})

def student_transport_info_view(request, institute_unique_code, academic_session_id):
	if authorized_admin_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		student_transport_info_list = get_student_transport_info_list(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'adminportal/layouts/student-transport-detail.html',{'student_transport_info_list' : student_transport_info_list})

def home_page_view(request, institute_unique_code):
	if authorized_admin_user_session(request):
		user_login_view = get_user_login_view(request)
		user = user_login_view['user']
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		quick_actions_details = get_quick_actions_details(user_login_view, institute_unique_code)
		view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_DASHBOARD_STATS)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		institute_stats = get_institute_stats(user_login_view, institute_unique_code, current_session['academicSessionId'], get_current_time())
		standards = institute_stats['standardList']
		class_payment_stats = institute_stats['sessionFeesStats']
		date_wise_class_payment_stats = institute_stats['dateWiseFeesStats']
		admission_home_stats = institute_stats['studentAdmissionStats']
		attendance_stats = institute_stats['dateWiseAttendanceStats']
		invoice_details = institute_stats['invoicesDetails']
		today_birthday_details = institute_stats['todayBirthdayList']
		upcoming_birthday_details = institute_stats['upcomingBirthdayList']
		staff_stats = institute_stats['staffStats']
		transport_stats = institute_stats['transportStats']
		total_students = 0
		present_percentage = 0
		if transport_stats is not None:
			total_students = transport_stats['totalEnrolledStudentCount']
		if total_students != 0:
			present_percentage = (attendance_stats['totalPresentAttendance'] / total_students) * 100
		relieved_students = institute_stats['relievedStudentsCount']
		return render(request, 'adminportal/layouts/dashboard-content.html',{'institute_unique_code' : institute_unique_code, 'user': user_login_view['user'], 'standards':standards, 'class_payment_stats':class_payment_stats, 'date_wise_class_payment_stats' : date_wise_class_payment_stats, 'web_ui_preferences' : web_ui_preferences,
		'view_fees_stats' : view_fees_stats, 'academic_years':academic_years, 'current_session':current_session, 'admission_home_stats' : admission_home_stats, 'quick_actions_details' : quick_actions_details, 'invoice_details' : invoice_details, 'attendance_stats' : attendance_stats, 'today_birthday_details' : today_birthday_details,
		'upcoming_birthday_details' : upcoming_birthday_details, 'staff_stats' : staff_stats, 'transport_stats' : transport_stats, 'present_percentage' : present_percentage, 'relieved_students' : relieved_students})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_admin_user_session(request) :
		user_login_view = get_user_login_view(request)
		user = user_login_view['user']

		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		quick_actions_details = get_quick_actions_details(user_login_view, institute_unique_code)
		view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_DASHBOARD_STATS)

		institute_stats = get_institute_stats(user_login_view, institute_unique_code, academic_session_id, get_current_time())
		standards = institute_stats['standardList']
		class_payment_stats = institute_stats['sessionFeesStats']
		date_wise_class_payment_stats = institute_stats['dateWiseFeesStats']
		admission_home_stats = institute_stats['studentAdmissionStats']
		attendance_stats = institute_stats['dateWiseAttendanceStats']
		invoice_details = institute_stats['invoicesDetails']
		today_birthday_details = institute_stats['todayBirthdayList']
		upcoming_birthday_details = institute_stats['upcomingBirthdayList']
		staff_stats = institute_stats['staffStats']
		transport_stats = institute_stats['transportStats']
		total_students = 0
		present_percentage = 0
		if transport_stats is not None:
			total_students = transport_stats['totalEnrolledStudentCount']
		if total_students != 0:
			present_percentage = (attendance_stats['totalPresentAttendance'] / total_students) * 100
		relieved_students = institute_stats['relievedStudentsCount']

		return render(request, 'adminportal/layouts/dashboard-session-content.html',{'institute_unique_code' : institute_unique_code, 'user': user_login_view['user'], 'standards':standards, 'class_payment_stats':class_payment_stats, 'date_wise_class_payment_stats' : date_wise_class_payment_stats, 'web_ui_preferences' : web_ui_preferences,
		'view_fees_stats' : view_fees_stats, 'admission_home_stats' : admission_home_stats, 'quick_actions_details' : quick_actions_details, 'invoice_details' : invoice_details, 'attendance_stats' : attendance_stats, 'today_birthday_details' : today_birthday_details,
		'upcoming_birthday_details' : upcoming_birthday_details, 'staff_stats' : staff_stats, 'transport_stats' : transport_stats, 'present_percentage' : present_percentage, 'relieved_students' : relieved_students})

def change_password_view(request, institute_unique_code):
    if authorized_admin_user_session(request):
        user = get_user_login_view(request)['user']
        return render(request, 'adminportal/change_password.html',{'user':user})

@csrf_exempt
def update_password_view(request, institute_unique_code):
	if authorized_admin_user_session(request) and request.is_ajax() and request.method == 'POST':
		change_password_data = json.loads(request.POST['changePasswordInfo'])
		user_login_view = get_user_login_view(request)
		response_data = change_password(user_login_view, change_password_data)
		return render(request, 'adminportal/status_modal.html',{"data":response_data})

def get_bell_notification_list(request, institute_unique_code, offset, limit):
	if authorized_admin_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		notification_list_with_count = bell_notification_list_with_count(user_login_view, institute_unique_code, offset, limit)
		bell_notification_count = notification_list_with_count['pendingNotificationCount']
		notification_list = notification_list_with_count['bellNotificationDetailsList']
		return render(request, 'adminportal/notifications/bell_notification_list.html',{'notification_list' : notification_list, 'institute_unique_code' : institute_unique_code, 'bell_notification_count' : bell_notification_count})

def mark_list_opened_view(request, institute_unique_code):
	if authorized_admin_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = mark_list_opened(user_login_view, institute_unique_code)
		return render(request, 'adminportal/notifications/bell_notification_list.html',{})

def mark_as_read_view(request, institute_unique_code):
	if authorized_admin_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		notification_id = request.GET.get("notification_id","")
		response = mark_as_read(user_login_view, institute_unique_code, notification_id)
		notification_list_with_count = bell_notification_list_with_count(user_login_view, institute_unique_code, 0, 10)
		bell_notification_count = notification_list_with_count['pendingNotificationCount']
		notification_list = notification_list_with_count['bellNotificationDetailsList']
		return render(request, 'adminportal/notifications/bell_notification_list.html',{'notification_list' : notification_list, 'institute_unique_code' : institute_unique_code, 'bell_notification_count' : bell_notification_count})

def generate_birthday_certificate_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_admin_user_session(request):
		user_login_view = get_user_login_view(request)
		document = get_birthday_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+document['file_name']
			return response
		return HttpResponse("<strong>Invalid information submitted.</strong>")
