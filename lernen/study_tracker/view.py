from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from core.controller.user.authentication import *
from core.controller.utils.tracking_events import *
from core.controller.utils.template_manager import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.reports_manager import *
from core.controller.utils.authorised_actions import *
from datetime import date
from math import *
from core.controller.utils.authorised_actions import *
from examination.controller.examination_manager import get_class_marks_feed_structure
from study_tracker.controller.study_tracker_manager import *

MODULE_NAME = 'STUDY_TRACKER'

def authorized_user_session(request):
	return authorized_module(request, MODULE_NAME)

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "STUDY_TRACKER_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		user_type = user_login_view['user']['userType']
		tutorial_videos = get_tutorial_video_details(MODULE_NAME, institute_unique_code, user_type)
		return render(request, 'study_tracker/layouts/default-study-tracker-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' :  payment_status_data, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access, 'tutorial_videos': tutorial_videos})
	
def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'study_tracker/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'study_tracker/layouts/dashboard-session-content.html',{'user': user_login_view['user']})

def reports_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])

		return render(request, 'study_tracker/reports/main_screen.html',{'standards' : standards, 'academic_years':academic_years, 'current_session':current_session})

def reports_session_change_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		
		return render(request, 'core/reports/standard_section_dropdown.html',{'standards' : standards})

def generate_report_view(request, institute_unique_code, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		report_request = {"report_type":report_type,"academic_session_id":request.GET.get("academic_session_id",0), "standard_id" : request.GET.get("standard_id",""), "section_ids" : request.GET.get("section_ids", ""), "course_type_str" : request.GET.get("course_type", ""),"startDate" : request.GET.get("startDate", 0), "endDate" : request.GET.get("endDate", 0), "download_format" : downloadFormat}
		if download_report :
			report = get_report(user_login_view, institute_unique_code, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_report_data(user_login_view, institute_unique_code, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})