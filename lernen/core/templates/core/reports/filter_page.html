<div class="form-row">
  <div class="form-group col-md-3">
    <label>Academic Session*</label>
    <select class="form-control report-academic-session mandatory-field">
      {% for academic_year in academic_years %}
        <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
      {% endfor %}
    </select>
 </div>
 <div class="form-group col-md-3">
   <label>Gender</label>
   <select class="form-control filter-gender select2 filter-criteria" multiple data-toggle="select2">
     <option value="MALE" >Male</option>
     <option value="FEMALE">Female</option>
     <option value="TRANSGENDER">Transgender</option>
   </select>
 </div>
 <div class="form-group col-md-3">
   <label>Religion</label>
   <select class="form-control filter-religion select2 filter-criteria" multiple data-toggle="select2">
     {% for religion in religion %}
       <option value={{religion}}>{{religion}}</option>
     {% endfor %}
   </select>
 </div>
 <div class="form-group col-md-3">
   <label>Category</label>
   <select class="form-control filter-category select2 filter-criteria" multiple data-toggle="select2">
     <option value="GENERAL">General</option>
     <option value="OBC">OBC</option>
     <option value="BC">BC</option>
     <option value="SC">SC</option>
     <option value="ST">ST</option>
     <option value="SBC">SBC</option>
     <option value="MIN">Minority</option>
     <option value="OTHER">Other</option>
   </select>
 </div>
 </div>
 <div class="form-row">
   <div class="form-group col-md-3">
     <label>Area Type</label>
     <select class="form-control filter-area-type select2 filter-criteria" multiple data-toggle="select2">
       <option value="RURAL">Rural</option>
       <option value="URBAN">Urban</option>
       <option value="OTHER">Other</option>
     </select>
   </div>
   <div class="form-group col-md-3">
     <label>Specially Abled</label>
     <select class="form-control filter-specially-abled select2 filter-criteria" multiple data-toggle="select2">
       <option value=""></option>
       <option value="true">Y</option>
       <option value="false">N</option>
     </select>
   </div>
   <div class="form-group col-md-3">
     <label>RTE</label>
     <select class="form-control filter-rte select2 filter-criteria" multiple data-toggle="select2" >
       <option value=""></option>
       <option value="true">Y</option>
       <option value="false">N</option>
     </select>
   </div>
   <div class="form-group col-md-3">
     <label>BPL</label>
     <select class="form-control filter-bpl select2 filter-criteria" multiple data-toggle="select2">
       <option value=""></option>
       <option value="true">Y</option>
       <option value="false">N</option>
     </select>
   </div>
</div>
 <div class="form-row">
   <div class="form-group col-md-3">
     <label>State</label>
     {% include 'core/utils/multiple_select_state.html'%}
   </div>

   <div class="form-group col-md-3 class-attendance-type-div">
     <label>Classes</label>
     <select class="form-control reports-student-class select2" multiple data-toggle="select2">
       {% for standard in standards %}
         {% if standard.standardSectionList %}
             {% for section in standard.standardSectionList %}
               {% with section.sectionId|stringformat:"s" as section_id %}
                 {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                   <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                 {% endwith %}
               {% endwith %}
             {% endfor%}
         {% else %}
           <option value={{standard.standardId}}>{{standard.displayName}}</option>
         {% endif %}
       {% endfor%}
     </select>
   </div>
   <div class="form-group col-md-3">
     <label>House</label>
     <select class="form-control filter-house select2 filter-criteria" multiple data-toggle="select2">
       {% for instituteHouse in instituteHouse %}
         <option value={{instituteHouse.instituteHouse.houseId}}>{{instituteHouse.instituteHouse.houseName}}</option>
       {% endfor %}
     </select>
   </div>
   <div class="form-group col-md-3">
     <label>Student Status</label>
     <select class="form-control student-status select2" multiple data-toggle="select2">
       <option value="ENROLLED">Enrolled</option>
       <option value="RELIEVED">Relieved</option>
       <option value="ENROLMENT_PENDING">Enrollment Pending</option>
       <option value="NSO">NSO</option>
     </select>
   </div>

 <!-- <div class="form-group col-md-3" style="padding-top:35px;">
    <div class="form-check">
     <input type="checkbox" class="form-check-input report-select-all-class" checked>
     <label class="form-check-label" for="select-report-student-class"><strong> All Classes?</strong></label>
   </div>
 </div> -->

 <!-- <div class="form-group col-md-3 select-report-student-class-menu" style="display:none;">
   <div class="dropdown" style="padding-top:25px;">
     <button class="btn btn-info dropdown-toggle reports-student-class" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
       Select Classes
     </button>
     <div class="dropdown-menu" aria-labelledby="dropdownMenu2" style="padding-left:5px; padding-right:5px;">
       {% for standard in standards %}
       <div class="form-check" >
         {% if standard.standardSectionList %}
             {% for section in standard.standardSectionList %}
               {% with section.sectionId|stringformat:"s" as section_id %}
                 {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                   <input type="checkbox" class="form-check-input select-report-class" id={{standard_section_map_id}}>
                   <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                 {% endwith %}
               {% endwith %}
             {% endfor%}
         {% else %}
           <input type="checkbox" class="form-check-input select-report-class" id={{standard.standardId}}>
           <option value={{standard.standardId}}>{{standard.displayName}}</option>
         {% endif %}
         </div>
       {% endfor%}
     </div>
  </div>
 </div> -->
</div>
