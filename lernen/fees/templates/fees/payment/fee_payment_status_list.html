{% load json%}
{% load displaytime %}
{% load round_to_decimals %}
<ul class="nav nav-tabs" id="myTab" role="tablist">
  <li class="nav-item">
    <a class="nav-link active" id="unpaid-fee-tab" data-toggle="tab" href="#unpaid-fee-tab-content" role="tab" aria-controls="contact" aria-selected="false">Unpaid Fees</a>
  </li>
  <li class="nav-item">
    <a class="nav-link" id="partially-paid-fee-tab" data-toggle="tab" href="#partially-paid-fee-tab-content" role="tab" aria-controls="profile" aria-selected="false">Installment Due Fees</a>
  </li>
  <li class="nav-item">
    <a class="nav-link" id="paid-fee-tab" data-toggle="tab" href="#paid-fee-tab-content" role="tab" aria-controls="home" aria-selected="true">Paid <PERSON></a>
  </li>
  <li class="nav-item">
    <a class="nav-link" id="other-session-due-fee-tab" data-toggle="tab" href="#other-session-due-fee-tab-content" role="tab" aria-controls="home" aria-selected="true"> {% if due_in_other_sessions  %} <span style="color: red;"> {%else%} <span
          style="color: green;"> {% endif %} Other Sessions Due Fees</span></a>
  </li>
  <li class="nav-item">
    <a class="nav-link" id="siblings-due-fee-tab" data-toggle="tab" href="#siblings-due-fee-tab-content" role="tab" aria-controls="home" aria-selected="true"> {% if sibling_due_fee_details  %} <span style="color: red;"> {%else%} <span
          style="color: green;"> {% endif %} Sibling Details </span></a>
  </li>
  <li class="nav-item">
    {% if status == "false"  %}
      <a class="nav-link" id="follow-up-details-tab" data-toggle="tab" href="#follow-up-details-tab-content" role="tab" aria-controls="home" aria-selected="true"> {% if fee_followup_details  %} <span style="color: red;"> {%else%} <span> {% endif %} Followup Details </span></a>
    {% else %}
      <a class="nav-link" id="follow-up-details-tab" data-toggle="tab" href="#follow-up-details-tab-content" role="tab" aria-controls="home" aria-selected="true"> {% if fee_followup_details  %} <span style="color: black;"> {%else%} <span> {% endif %} Followup Details </span></a>
    {% endif %}
  </li>

</ul>
<div class="tab-content" id="myTabContent">
  <div class="tab-pane fade" id="paid-fee-tab-content" role="tabpanel" aria-labelledby="paid-fee-tab">
    <br>
    <br>
    {% if fee_payment_details.selected_session.full_paid_fee_list %}
    <div>
      <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
        <caption>Paid Fees List</caption>
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col" class="w-25">Fees Name</th>
            <th scope="col"> Fees Type</th>
            <th scope="col"> Due Date</th>
            <th scope="col"> Status</th>
            <th scope="col"> Action </th>
            <!-- <th scope="col" colspan="2">View</th> -->
          </tr>
        </thead>
        <tbody>
          {% for full_paid_fee in fee_payment_details.selected_session.full_paid_fee_list %}
          <tr>
            <td style="display:none;">
              <p class="payment-fee-info" style="display:none;"> {{full_paid_fee|jsonstr}} </p>
            </td>
            <th scope="row">{{ forloop.counter }}</th>
            <td>{{full_paid_fee.feeConfigurationBasicInfo.feeName}}</td>
            <td>{{full_paid_fee.feeConfigurationBasicInfo.feeTypeDisplayName}}</td>
            {% load displaytime %}
            <td>{{full_paid_fee.feeConfigurationBasicInfo.dueDate|print_date}}</td>
            <td>
              <h5><span class="badge badge-success">PAID</span></h5>
            </td>
            <td style="width:10%;">
              <button type="button" class="btn btn-outline-info view-payment-fee-info" aria-haspopup="true" aria-expanded="false">
                View
              </button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <p>No paid fees present.</p>
    {% endif %}
  </div>

  <div class="tab-pane fade" id="partially-paid-fee-tab-content" role="tabpanel" aria-labelledby="partially-paid-fee-tab">
    <br>
    <br>
    {% if fee_payment_details.selected_session.partially_paid_fee_list %}
    <div>
      <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
        <caption>Installment Due Fees List</caption>
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col" class="w-25">Fees Name</th>
            <th scope="col"> Fees Type</th>
            <th scope="col"> Due Date</th>
            <th scope="col"> Status</th>
            <th scope="col"> Action </th>
          </tr>
        </thead>
        <tbody>
          {% for partially_paid_fee in fee_payment_details.selected_session.partially_paid_fee_list %}
          <tr>
            <td style="display:none;">
              <p class="payment-fee-info" style="display:none;"> {{partially_paid_fee|jsonstr}} </p>
            </td>
            <th scope="row">{{ forloop.counter }}</th>
            <td>{{partially_paid_fee.feeConfigurationBasicInfo.feeName}}</td>
            <td>{{partially_paid_fee.feeConfigurationBasicInfo.feeTypeDisplayName}}</td>
            {% load displaytime %}
            <td>{{partially_paid_fee.feeConfigurationBasicInfo.dueDate|print_date}}</td>
            <td>
              <h5><span class="badge badge-danger">Installment Due</span></h5>
            </td>
            <td style="width:10%;">
              <button type="button" class="btn btn-outline-info view-payment-fee-info" aria-haspopup="true" aria-expanded="false">
                View
              </button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <p>No installment remaining</p>
    {% endif %}
  </div>
  <div class="tab-pane fade show active" id="unpaid-fee-tab-content" role="tabpanel" aria-labelledby="unpaid-fee-tab">
    <br>
    <br>
    {% if fee_payment_details.selected_session.unpaid_fee_list %}
    <div>
      <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
        <caption>Installment Due Fees List</caption>
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col" class="w-25">Fees Name</th>
            <th scope="col"> Fees Type</th>
            <th scope="col"> Due Date</th>
            <th scope="col"> Status</th>
            <th scope="col"> Action </th>
          </tr>
        </thead>
        <tbody>
          {% for unpaid_fee in fee_payment_details.selected_session.unpaid_fee_list %}
          <tr>
            <td style="display:none;">
              <p class="payment-fee-info" style="display:none;"> {{unpaid_fee|jsonstr}} </p>
            </td>
            <th scope="row">{{ forloop.counter }}</th>
            <td>{{unpaid_fee.feeConfigurationBasicInfo.feeName}}</td>
            <td>{{unpaid_fee.feeConfigurationBasicInfo.feeTypeDisplayName}}</td>
            {% load displaytime %}
            <td>{{unpaid_fee.feeConfigurationBasicInfo.dueDate|print_date}}</td>
            <td>
              <h5><span class="badge badge-danger">Not Paid</span></h5>
            </td>
            <td style="width:10%;">
              <button type="button" class="btn btn-outline-info view-payment-fee-info" aria-haspopup="true" aria-expanded="false">
                View
              </button>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <p>No due fees</p>
    {% endif %}
  </div>

  <div class="tab-pane fade" id="other-session-due-fee-tab-content" role="tabpanel" aria-labelledby="other-session-due-fee-tab">
    <br>
    <br>
    {% if due_in_other_sessions %}
    <h5 style="color:red;">*Total due includes session dues and any applicable fines.</h5>
    <br/>
    {% for other_session in fee_payment_details.other_sessions %}
    <div>
      {% if other_session.due_fee_list %}
      <div class="row">
        <div class="col-md-6">
          <h5>{{other_session.student.studentAcademicSessionInfoResponse.academicSession.displayName}}</h5>
        </div>
        <div class="col-md-3">
        </div>
        <div class="col-md-3">
          {% with other_session.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId|stringformat:"s" as sessionId %}
          <td> <button style="width:100%;" type="button" class="btn btn-primary" onclick={{"feePayment.collectFeesDisplay('"|add:sessionId|add:"')"}}>Collect
              {{other_session.student.studentAcademicSessionInfoResponse.academicSession.shortYearDisplayName}} Fees</button></td>
          {% endwith %}
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h6>Total Due Amount : <span class="totalDueAmount">0.0</span></h6>
        </div>
      </div>
      <br />
      <div>
        <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
          <caption>Past Session Due Fees List</caption>
          <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col" class="w-25">Fees Name</th>
              <th scope="col"> Fees Type</th>
              <th scope="col"> Due Date</th>
              <th scope="col"> Status</th>
              <th scope="col"> Action </th>
            </tr>
          </thead>
          <tbody>
            {% for due_fee in other_session.due_fee_list %}
            <tr>
              <td style="display:none;">
                <p class="payment-fee-info" style="display:none;"> {{due_fee|jsonstr}} </p>
              </td>
              <th scope="row">{{ forloop.counter }}</th>
              <td>{{due_fee.feeConfigurationBasicInfo.feeName}}</td>
              <td>{{due_fee.feeConfigurationBasicInfo.feeTypeDisplayName}}</td>
              {% load displaytime %}
              <td>{{due_fee.feeConfigurationBasicInfo.dueDate|print_date}}</td>
              <td>
                <h5><span class="badge badge-danger">Due Fee</span></h5>
              </td>
              <td style="width:10%;">
                <button type="button" class="btn btn-outline-info view-payment-fee-info" aria-haspopup="true" aria-expanded="false">
                  View
                </button>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <br />
      {% endif %}
    </div>
    {% endfor %}
    {% else %}
    <p>No due fees in other sessions</p>
    {% endif %}
  </div>

  <div class="tab-pane fade" id="siblings-due-fee-tab-content" role="tabpanel" aria-labelledby="siblings-due-fee-tab">
    <br>
    <br>
    {% if sibling_due_fee_details %}
    <div>
      <div>
        <table id="datatables-reponsive" class="table table-striped table-bordered datatables-reponsive-table">
          <caption>Siblings Due Fees List</caption>
          <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Admission Number</th>
              <th scope="col"> Student Name</th>
              <th scope="col"> Class</th>
              <th scope="col"> Status</th>
              <th scope="col"> Session Dues</th>
              <th scope="col"> Due Fees Till Today </th>
              <th scope="col"> Action </th>
            </tr>
          </thead>
          <tbody>
            {% for sibling_due_fee_detail in sibling_due_fee_details %}
            <tr>
              <td style="display:none;">
                <p class="payment-fee-info" style="display:none;"> {{sibling_due_fee_detail|jsonstr}} </p>
              </td>
              <th scope="row">{{ forloop.counter }}</th>
              <td>{{sibling_due_fee_detail.admissionNumber}}</td>
              <td>
                {{ sibling_due_fee_detail.studentFullName }}
                {% if sibling_due_fee_detail.feePaymentRestricted %}
                <div style="color: red; font-size: smaller;">
                  Fee Payment Restricted
                </div>
                {% endif %}
              </td>
              <!-- <td>{{sibling_due_fee_detail.standardName}} {% if sibling_due_fee_detail.sectionName == "None" %} {{sibling_due_fee_detail.sectionName}} {% endif %}</td> -->
              <td>{{sibling_due_fee_detail.displayNameWithSection}}</td>
              {% if sibling_due_fee_detail.sessionStatus == 'ENROLLED' %}
              <td><span class="badge badge-success">ENROLLED</span> </td>
              {% elif sibling_due_fee_detail.sessionStatus == 'ENROLMENT_PENDING' %}
              <td><span class="badge badge-warning">ENROLMENT PENDING</span> </td>
              {% elif sibling_due_fee_detail.sessionStatus == 'RELIEVED' %}
              <td><span class="badge badge-danger">RELIEVED</span> </td>
              {% elif sibling_due_fee_detail.sessionStatus == 'NSO' %}
              <td><span class="badge badge-info">STRIKED OFF</span> </td>
              {% endif %}
              <td>{{sibling_due_fee_detail.dueAmount|to_2_decimals_without_traling_zeros}}</td>
              <td>{{sibling_due_fee_detail.dueAmountTillToday|to_2_decimals_without_traling_zeros}}</td>
              {% if sibling_due_fee_detail.dueAmount and sibling_due_fee_detail.dueAmount > 0 %}
              <td style="width:10%;">
                <button type="button" class="btn btn-sm btn-outline-info" aria-haspopup="true" aria-expanded="false" onclick="loadStudentPaymentDetails('{{sibling_due_fee_detail.studentId}}', true)" {% if sibling_due_fee_detail.feePaymentRestricted %} disabled {% endif %}>
                  Collect
                </button>
              </td>
              {% else %}
              <td style="width:10%;">
                -
              </td>
              {% endif %}
            </tr>
            {% endfor %}
          </tbody>
        </table>
        {% if authorised_to_collect_siblings_fees %}
          <div class="text-center">
            <button type="button" class="btn btn btn-primary" aria-haspopup="true" aria-expanded="false" onclick="feePayment.collectSiblingFeesPage('{{student.studentId}}', '{{session_id}}');">
              Collect Siblings Fees
            </button>
          </div>
        {% endif %}
      </div>
      <br />
    </div>
    {% else %}
    <p>No due fees for siblings</p>
    {% endif %}
  </div>

  <div class="tab-pane fade" id="follow-up-details-tab-content" role="tabpanel" aria-labelledby="follow-up-details-tab">
    <br>
    <br>
    <p style="display:none;">{{student}}</p>
    <div class="mt-3 mb-3">
      <h4 class="align-left" style="display:inline-block;">FOLLOW UP DETAILS</h4>
      <div style="display:inline-block;float:right;">
        <button class="btn btn-outline-primary mr-2" onclick="addFeeFollowUpDetails('{{student.studentId}}')">Add Follow up</button>
        {% if status == "false"  %}
            <button class="btn btn-outline-danger" onclick="closeFeeFollowUpDetails('{{student.studentId}}','{{student.studentId}}',true)">Close All</button>
        {% endif %}
      </div>
    </div>

    <div id="follow-up-details-container-div">

        {% include 'fees/payment/follow_up_detail.html' %}
    </div>
  </div>
</div>

{% include 'fees/fee_assignment/assigned_fee_view_modal.html' %}
{% include 'fees/payment/collected_fee_payment_details_modal.html' %}
{% include 'fees/follow_up_details/follow_up_details_modal.html' %}
