{% load json %}
<p id="staff-category-json"style="display:none;">{{staff_category|jsonstr}}</p>
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>REPORTS</h3>
    </div>
</div>
<br>

<div>
    <div class="row">

      <div class="col-sm-4" onclick="attendanceReports.resetPopUp();">
        <div id="staff-attendance-with-date-range" class="card-hover bg-light report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Attendance Details for Date Range</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report of staff attendance in date range.</p>
          </div>
        </div>
      </div>
      <div class="col-sm-4" onclick="attendanceReports.resetPopUp();">
        <div id="staff-attendance-summary" class="card-hover bg-light report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Monthly Summary Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Assess monthly attendance status for staffs at a glance</p>
          </div>
        </div>
      </div>
      <div class="col-sm-4" onclick="attendanceReports.resetPopUp();">
        <div id="staff-attendance-details" class="card-hover bg-light report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Staff Attendance Details</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed monthly attendance status for staffs at a glance</p>
          </div>
        </div>
      </div>

    </div>

</div>


<div class="modal fade" id="staff-attendance-with-date-range-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Attendance Details for Date Range </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">

          <div class="form-group col-md-6">
            <label>Select Start Date*</label>
            <input type="text" class="form-control select-date staff-attendance-start-date mandatory-field" placeholder="Attendance Start Date"/>
          </div>

          <div class="form-group col-md-6">
            <label>Select End Date*</label>
            <input type="text" class="form-control select-date staff-attendance-end-date mandatory-field" placeholder="Attendance End Date"/>
          </div>

        </div>
        <div class="form-row">

          <div class="form-group col-md-6">
            <label>Staff Status</label>
            <select class="form-control selectpicker filter-staff-status select2 filter-criteria" multiple data-toggle="select2" title="Status">
                <option value="JOINER">Joiner</option>
                <option value="ONBOARD" selected>Onboard</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>

          <div class="form-group col-md-6">
            <label>Attendance Status</label>
            <select class="form-control mr-sm-4 select2" id="staff-attendance-status"  data-style="bg-white border" title="All" multiple data-toggle="select2">
                <option value="PRESENT">Present</option>
                <option value="HALF_DAY">Half-Day</option>
                <option value="LEAVE">Leave</option>
            </select>
          </div>

          </div>
      </div>
      <p style="display:none;" class="report-type">STAFF_ATTENDANCE_DETAILS_IN_A_DAY</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="staff-attendance-summary-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Monthly Summary Report</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">

          <div class="form-group col-md-6">
            <label>Select Start Date*</label>
            <input type="text" class="form-control select-date staff-attendance-start-date mandatory-field" placeholder="Attendance Start Date"/>
          </div>

          <div class="form-group col-md-6">
            <label>Select End Date*</label>
            <input type="text" class="form-control select-date staff-attendance-end-date mandatory-field" placeholder="Attendance End Date"/>
          </div>

          </div>

          <div class="form-row">

            <div class="form-group col-md-6">
              <label for="">Staff Status</label>
              <select class="form-control selectpicker filter-staff-status select2 filter-criteria" multiple data-toggle="select2" title="Status">
                  <option value="JOINER">Joiner</option>
                  <option value="ONBOARD" selected>Onboard</option>
                  <option value="RELIEVED">Relieved</option>
              </select>
            </div>
            
            <div class="form-group col-md-6">
              <label for="">Staff Category</label>
              <select class="form-control selectpicker filter-staff-category select2 filter-criteria" multiple data-toggle="select2" title="Category">
                {% for category in staff_category %}
                  <option value="{{category.staffCategoryId}}">{{category.staffCategoryName}}</option>
                {% endfor %}
              </select>
            </div>
        </div>
        <p style="color:red;">*Start and end date range should be within 31 days.</p>
      <p style="display:none;" class="report-type">STAFF_MONTHLY_ATTENDANCE_SUMMARY</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>
</div>


<div class="modal fade" id="staff-attendance-details-modal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Staff Attendance Details</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">

          <div class="form-group col-md-6">
            <label>Select Start Date*</label>
            <input type="text" class="form-control select-date staff-attendance-start-date mandatory-field" placeholder="Attendance Start Date"/>
          </div>

          <div class="form-group col-md-6">
            <label>Select End Date*</label>
            <input type="text" class="form-control select-date staff-attendance-end-date mandatory-field" placeholder="Attendance End Date"/>
          </div>

          </div>

          <div class="form-row">

            <div class="form-group col-md-6">
              <label for="">Staff Status</label>
              <select class="form-control selectpicker filter-staff-status select2 filter-criteria" multiple data-toggle="select2" title="Status">
                  <option value="JOINER">Joiner</option>
                  <option value="ONBOARD" selected>Onboard</option>
                  <option value="RELIEVED">Relieved</option>
              </select>
            </div>
            
            <div class="form-group col-md-6">
              <label for="">Staff Category</label>
              <select class="form-control selectpicker filter-staff-category select2 filter-criteria" multiple data-toggle="select2" title="Category">
                {% for category in staff_category %}
                  <option value="{{category.staffCategoryId}}">{{category.staffCategoryName}}</option>
                {% endfor %}
              </select>
            </div>
        </div>
        <p style="color:red;">*Start and end date range should be within 31 days.</p>
      <p style="display:none;" class="report-type">STAFF_ATTENDANCE_DETAILS</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>
</div>
