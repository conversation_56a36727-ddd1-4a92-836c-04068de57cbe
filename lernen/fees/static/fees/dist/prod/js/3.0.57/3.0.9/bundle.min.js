var DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-discount-assignment",DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID="select-discount-view";function registerDiscountTabs(){$("#"+DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){var e=academicSessionHandler.getSelectedSessionDisplayName();$("#select-discount-assignment").addClass("active"),$("#select-discount-view").removeClass("active"),$("#academic-year-display").text(e),$("#student-discount-assign").html(""),$("#discount-assign-student-search-result").html(""),discountAssignment.loadDiscountAssignmentPage()}),$("#"+DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID).on("click",function(){var e=academicSessionHandler.getSelectedSessionDisplayName();$("#select-discount-assignment").removeClass("active"),$(".discount-id").prop("checked",!1),$("#select-discount-view").addClass("active"),$("#academic-year-display").text(e),$("#student-discount-view").html(""),initSelect2("Discounts"),bindSearchStudentDiscountViewEvent(),academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession),applyDiscountSearchFilter(),discountAssignment.loadDiscountViewPage()})}function bindSearchStudentDiscountViewEvent(){$("#discount-view-search-student").on("click",function(){bindSearchStudentDiscountView()}),$("#discount-view-search-student-text").on("keyup",function(e){13==e.keyCode&&bindSearchStudentDiscountView()})}function bindSearchStudentDiscountView(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#discount-view-search-student-text").val(),n=$("#discounts").val();n=n.join(),ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/"+e+"?searchText="+t+"&discountIds="+n,function(e){$("#student-discount-view").html(e)})}function bindSearchStudentDiscountAssignmentEvent(){var e="#discount-assign-student-search-result";$("#discount-assign-search-student").on("click",function(){discountAssignment.doneStudentSearchTyping(e)}),$("#discount-assign-search-student-text").on("keyup",function(t){13==t.keyCode&&discountAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#discount-assign-search-student-text",e,discountAssignment.doneStudentSearchTyping)}var discountAssignment={dataCache:{},doneStudentSearchTyping:function(e){var t=$("#discount-assign-search-student-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentDiscountAssignment)},changeSession:function(){var e=academicSessionHandler.getSelectedSessionDisplayName(),t=$(".discount-options.active").attr("id");t==DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?($("#academic-year-display").text(e),$("#discount-assign-search-student-text").val(""),$("#student-discount-assign").html(""),$("#discount-assign-student-search-result").html(""),discountAssignment.loadDiscountAssignmentPage()):t==DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID&&($("#academic-year-display").text(e),$(".discount-id").prop("checked",!1),$("#discount-view-search-student-text").val(""),$("#student-discount-view").html(""),applyDiscountSearchFilter(),discountAssignment.loadDiscountViewPage())},resetDataCache:function(){discountAssignment.dataCache={}},loadDiscountAssignmentPage:function(){ajaxClient.get("/fees/discount-assignment",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession),$("#discount-assignment-screen").attr("style","display:block"),$("#discount-view-screen").attr("style","display:none"),registerDiscountTabs(),bindSearchStudentDiscountAssignmentEvent()})},loadDiscountViewPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-student-fee-discount-list/"+e,function(e){$("#student-discount-view").html(e),$("#student-discount-view-search-tab").attr("style","display:block"),$("#discount-assignment-screen").attr("style","display:none"),$("#discount-view-screen").attr("style","display:block")})},loadFeeDiscountStructures:function(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-student-discount-assignment-details/"+e+"/"+t,function(e){$("#view-student-discount-assignment-details-modal-container").html(e),$("#view-student-assigned-discounts-modal").modal("toggle")})},viewFeeDiscountStructure:function(e,t){discount_structure_list=[],discount_structure_list=t?discountAssignment.dataCache.assignedDiscounts:discountAssignment.dataCache.unassignedDiscounts;for(var n=null,a=0;a<discount_structure_list.length;a++)if(e==discount_structure_list[a].feeDiscountMetadata.discountStructureId){n=discount_structure_list[a];break}null!=n?discountAssignment.displayDiscountStructure(n,t):showErrorDialogBox("Invalid discount structure")},displayDiscountStructure:function(e,t){$("#view-discount-modal").find("#view-discount-title").text(e.feeDiscountMetadata.name);var n=null;if(t){for(var a="",i=0;i<e.feeIdDiscountAssignmentDetailsList.length;i++){for(var s=e.feeIdDiscountAssignmentDetailsList[i],o="",d=0;d<s.feeHeadDiscountAssignmentDetailsList.length;d++){var r=s.feeHeadDiscountAssignmentDetailsList[d],c=r.amount;null!=r.appliedDiscountAmount&&(c=r.appliedDiscountAmount),r.percent?c+="%":c+="/-",o+=discountAssignment.getDiscountFeeHeadRow(r.feeHeadConfiguration.feeHead,c)}a+=discountAssignment.getDiscountFeeRow(s.feeConfigurationBasicInfo.feeName,o)}$("#view-discount-modal").find(".modal-body").html(a)}else{if(null==e.feeDiscountEntityStructures||0==e.feeDiscountEntityStructures.length)return;for(n=e.feeDiscountEntityStructures[0].feeIdDiscountStructures,a="",i=0;i<n.length;i++){var l=n[i];for(o="",d=0;d<l.feeHeadDiscountStructures.length;d++){var u=l.feeHeadDiscountStructures[d];c=u.amount;u.percent?c+="%":c+="/-",o+=discountAssignment.getDiscountFeeHeadRow(u.feeHeadConfiguration.feeHead,c)}a+=discountAssignment.getDiscountFeeRow(l.feeConfigurationBasicInfo.feeName,o)}$("#view-discount-modal").find(".modal-body").html(a)}$("#view-discount-modal").modal("toggle")},getDiscountFeeHeadRow:function(e,t){return"<tr> <td> "+e+" </td> <td>"+t+"</td> </tr>"},getDiscountFeeRow:function(e,t){return'<div class="fee-structure"> <div class="card text-center card-border"> <div class="card-body"> <h5 class="card-title"> <strong>'+e+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-bordered"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th>  </tr> </thead> <tbody class="fee-structure-body"> '+t+" </tbody> </table> </div> </div> </div> </br> </div>"}};function loadStudentDiscountAssignment(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/assigned-discounts/"+t+"/"+e,function(t){$("#student-discount-assign").html(t),bindDiscountInfoViewEvents(),bindAssignNewDiscountEvent(),addNewStudentDiscountAssignment(),discountAssignment.resetDataCache(),discountAssignment.dataCache.studentId=e;var n=readJson("#student-fee-discount-assigned-structures"),a=readJson("#student-fee-discount-unassigned-structures");discountAssignment.dataCache.assignedDiscounts=n,discountAssignment.dataCache.unassignedDiscounts=a,instantDiscountStructure.initDataCache()})}function applyDiscountSearchFilter(){$("#apply-discount-search-filter").on("click",function(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#discount-view-search-student-text").val(),n=$("#discounts").val();n=n.join(),ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/"+e+"?searchText="+t+"&discountIds="+n,function(e){$("#student-discount-view").html(e)})})}function returnToAssignedDiscountDisplay(){$("#top-academic-session-bar").attr("style","display:block"),$(".student-discount-assign-tab").attr("style","display:none"),$("#student-assigned-discount-list").attr("style","display:block"),$("#student-discount-assign-search-tab").attr("style","display:block")}function bindAssignNewDiscountEvent(){$("#student-assign-new-discount-button").on("click",function(){assignNewDiscountDisplay()})}function assignNewDiscountDisplay(){$("#top-academic-session-bar").attr("style","display:none"),$(".student-discount-assign-tab").attr("style","display:none"),$("#student-new-discount-assignment-tab").attr("style","display:block"),bindDiscountInfoViewEvents()}function addNewStudentDiscountAssignment(){$("#student-new-discount-assign-confirm").on("click",function(){$("#student-new-discount-assign-confirm-modal").modal("toggle");var e=$("#assign-new-discount-student-id").val(),t=academicSessionHandler.getSelectedSessionId(),n=getDiscountAssignmentInfo(e,t);ajaxClient.post("/fees/add-discount-assignment/"+t,{discountAssignmentInfo:JSON.stringify(n)},function(t){$("#discount-assignment-status-modal-container").html(t),$("#discount-assignment-status-modal").modal("toggle"),returnToAssignedDiscountDisplay(),loadStudentDiscountAssignment(e)})})}function getDiscountAssignmentInfo(e,t){var n=[];return $("tr.discount-row").each(function(){if($(this).find(".discount-assignment-enable").is(":checked")){var e=$(this).attr("id");n.push(e)}}),{studentIds:[e],discountStructureIds:n}}function confirmDeleteDiscountAssignment(e){$("#delete\\.discount-assignment-id").val(e),$("#delete-discount-assignment-info-modal").modal("toggle")}function deleteDiscountAssignment(){$("#delete-discount-assignment-info-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete\\.discount-assignment-id").val(),n=$("#fee-assign-view-student-id").text().trim(),a={academicSessionId:e,studentId:n,discountId:t};ajaxClient.post("/fees/delete-discount-assignment",{deleteDiscountAssignmentInfo:JSON.stringify(a)},function(e){$("#discount-assignment-status-modal-container").html(e),$("#discount-assignment-status-modal").modal("toggle"),returnToAssignedDiscountDisplay(),loadStudentDiscountAssignment(n)})}var instantDiscountStructure={dataCache:{},INSTITUTE_ENTITY:"institute",initDataCache:function(){for(var e=readJson("#fee-data"),t={},n={},a={},i=e.feeConfigurationBasicInfoList,s=e.feeHeadConfigurationResponseList,o=0;o<i.length;o++){var d=i[o];t[d.feeId]=d;var r=instantDiscountStructure.createFeeHeadSelectMenu(s,"");a[d.feeId]=r}for(o=0;o<s.length;o++){var c=s[o];n[c.feeHeadConfiguration.feeHeadId]=c}instantDiscountStructure.dataCache={},instantDiscountStructure.dataCache.feeMap=t,instantDiscountStructure.dataCache.feeHeadMap=n,instantDiscountStructure.dataCache.feeHeadConfigurationResponseList=s,instantDiscountStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData=a},createFeeHeadSelectMenu:function(e,t){for(var n='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',a=0;a<e.length;a++){var i=e[a].feeHeadConfiguration.feeHeadId,s=e[a].feeHeadConfiguration.feeHead;n+=t==i?'<option value="'+i+'" selected>'+s+"</option>":'<option value="'+i+'">'+s+"</option>"}return n+="</select>"},loadNewFeeStructureConfigureModal:function(){$("#discount-structure-config-modal").modal("toggle"),instantDiscountStructure.resetDiscountStructureModal(),$("#discount-structure-config-modal").find(".modal-title").html("Assign Instant Discounts"),$("#submit-fee-structure").html("Assign Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees={},instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards={},instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees={},instantDiscountStructure.dataCache.selectedStructureId=null,instantDiscountStructure.populateSelectFeesDropdown(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees)},resetDiscountStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text"> Select fees to add new discount structure</p>'),$("#discount-structure-name").val(""),$("#discount-structure-name").attr("disabled",!1),$("#discount-structure-config-modal").find(".modal-footer").css("display","")},populateSelectFeesDropdown:function(e,t,n){var a=instantDiscountStructure.dataCache.feeMap,i="";for(feeId in a)feeId in n||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+a[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),instantDiscountStructure.bindSelectFeesEvent(e,t,n)},bindSelectFeesEvent:function(e,t,n){$(e).find(".fee-select-option").on("click",function(){var t=$(this).attr("id").split("-select-")[0],a=$(this).attr("id").split("-select-")[1];instantDiscountStructure.insertFeeDetailsCard(e,a,t,n)})},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];instantDiscountStructure.insertStandardDetailsCard(e)})},insertFeeDetailsCard:function(e,t,n,a){a[n]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=instantDiscountStructure.createEntityContainerId(t),s=instantDiscountStructure.createFeeContainerId(t,n),o='<div class="fee-structure"> <div class="card card-border text-center" id="'+s+'"> <p class="fee-id" style="display:none;">'+n+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+instantDiscountStructure.dataCache.feeMap[n].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col">Percentage</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(o),instantDiscountStructure.bindFeeContainerEvents(e,s,t,n,a),instantDiscountStructure.populateSelectFeesDropdown(e,t,a)},insertStandardDetailsCard:function(e){instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=instantDiscountStructure.dataCache.standardsMap[e].standardName.toUpperCase(),n=instantDiscountStructure.getStandardContainerId(e),a='<div id="'+n+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+t+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(a),instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},instantDiscountStructure.populateSelectFeesDropdown("#"+n,e,instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[e])},createEntityContainerId:function(e){return e+"-fee-structure-container"},createFeeContainerId:function(e,t){return e+"-"+t},bindFeeContainerEvents:function(e,t,n,a,i){$("#"+t).find(".add-fee-head-button").on("click",function(){instantDiscountStructure.insertFeeHeadRow(t,a,null,0,"")}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[a],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),instantDiscountStructure.populateSelectFeesDropdown(e,n,i)})},populateSelectFeesDropdown:function(e,t,n){var a=instantDiscountStructure.dataCache.feeMap,i="";for(feeId in a)feeId in n||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+a[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),instantDiscountStructure.bindSelectFeesEvent(e,t,n)},getStandardContainerId:function(e){return e+"-container"},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;")})},insertFeeHeadRow:function(e,t,n,a,i){var s="";if(null==n){s='<tr id=""> <td> '+instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"> <span class="slider round"></span> </label> </td><td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var o=instantDiscountStructure.dataCache.feeHeadConfigurationResponseList;s='<tr id=""> <td> '+instantDiscountStructure.createFeeHeadSelectMenu(o,n)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+a+'"> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"'+(i=i?"checked":"")+' /><span class="slider round"></span> </label> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(s),instantDiscountStructure.bindFeeHeadRowEvents(e)},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var n=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&n++}),n>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()}),$("#"+e).find(".fee-head-amount").change(function(){if($(this).parent().parent().find(".is-percentage-switch").is(":checked")){var e=$(this).val();if(e>100||e<=0)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).val("")}else{var t=$(this).val();if(t<=0&&""!=t)return showErrorDialogBox("Amount should be a positive number."),void $(this).val("")}}),$("#"+e).find(".is-percentage-switch").change(function(){if($(this).is(":checked")){var e=$(this).parent().parent().parent().find(".fee-head-amount").val();if((e>100||e<=0)&&""!=e)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).parent().parent().parent().find(".fee-head-amount").val("")}})},submitDiscountStructure:function(){var e=$("#discount-structure-name").val();if(""!=e){var t=academicSessionHandler.getSelectedSessionId(),n=$("#instant-discount-structure-id").text();null!=instantDiscountStructure.dataCache.selectedStructureId&&""!=instantDiscountStructure.dataCache.selectedStructureId.trim()&&(n=instantDiscountStructure.dataCache.selectedStructureId);var a=instantDiscountStructure.getFeeStructureData("#institute-fee-structure-container");if(!Array.isArray(a)||a.length){var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:a},s=[];s.push(i);var o=$("#fee-assign-view-student-id").text().trim(),d=[{structureId:n,discountStructureType:"SYSTEM",metadata:{title:e},entityFeeAssignmentPayloads:s}];$("#discount-structure-config-modal").modal("toggle");var r="/fees/add-student-discount-structure/"+t+"/"+o;null!=n&&""!=n&&null!=n&&(r="/fees/update-discount-structure/"+n+"/"+t),ajaxClient.post(r,{discountAssignmentStructurePayload:JSON.stringify(d)},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),loadStudentDiscountAssignment(o)})}}else alert("Discount Detail is mandatory!")},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),n=[],a={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in a)){var t=$(this).find(".fee-head-amount").val(),i=$(this).find(".toggle-switch").is(":checked");if(""===t||void 0===t)return alert("Amount cannot be empty"),[];n.push({feeHeadId:e,amount:t,isPercentage:i})}}),n.length>0&&t.push({feeId:e,feeHeadAmountList:n})}),Array.isArray(t)&&!t.length?(alert("Please select atleast one fees to create discount structure"),[]):t},fillFeeDiscountStructureModal:function(e,t){$("#discount-structure-name").attr("disabled",!1);var n=readJson($(e).parent().parent().find(".student-assigned-discount-structure"));instantDiscountStructure.resetDiscountStructureModal(),$("#discount-structure-name").val(n.feeDiscountMetadata.metadata.title),$("#discount-structure-config-modal").find(".modal-title").html("Update Instant Discounts"),$("#submit-discount-structure").html("Update Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),$("#instant-discount-structure-id").text(n.feeDiscountMetadata.discountStructureId),instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees={};for(var a={},i=0;i<n.feeIdDiscountAssignmentDetailsList.length;i++){var s=n.feeIdDiscountAssignmentDetailsList[i],o=s,d=s.feeConfigurationBasicInfo.feeId;a[d]=o}for(d in console.log(a),a){instantDiscountStructure.insertFeeDetailsCard(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,d,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);var r=instantDiscountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,d);for(i=0;i<a[d].feeHeadDiscountAssignmentDetailsList.length;i++){var c=a[d].feeHeadDiscountAssignmentDetailsList[i].feeHeadConfiguration.feeHeadId,l=a[d].feeHeadDiscountAssignmentDetailsList[i].amount,u=a[d].feeHeadDiscountAssignmentDetailsList[i].percent;instantDiscountStructure.insertFeeHeadRow(r,d,c,l,u)}}instantDiscountStructure.populateSelectFeesDropdown(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".toggle-switch").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#discount-structure-config-modal").find(".modal-title").html("View Instant Discounts"),$("#discount-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#discount-structure-config-modal").find(".modal-footer").css("display","none")),$("#discount-structure-config-modal").modal("toggle")}},CLASS_ASSIGNED_FEE_LIST="class-assigned-fees-list",STUDENT_ASSIGNED_FEE_LIST="student-assigned-fees-list",STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-student-assignment",BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-bulk-student-assignment",FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-fee-structure-assignment",CLASS_ENTITY="CLASS",STUDENT_ENTITY="STUDENT",feeAssignment={loadMainScreen:function(){ajaxClient.get("/fees/fee-assignment",function(e){$("#main-content").html(e),registerFeeAssignmentTabs(),academicSessionHandler.bindSessionChangeEvent(feeAssignment.changeSession),addStudentNewFeeAssignmentCallBack(),bindSearchStudentFeeAssignmentEvent(),bindAssignmentFeeSelectionEvents()})},doneStudentSearchTyping:function(e){var t=$("#fee-assign-search-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentFeesAssignment,"ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO")},changeSession:function(){var e=academicSessionHandler.getSelectedSession();$("#academic-year-display").text(e.displayName);var t=$(".fee-assign-options.active").attr("id");t==STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?loadFeeAssignment.loadStudentScreen():t==BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?loadFeeAssignment.loadBulkStudentScreen():t==FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID&&loadFeeAssignment.loadFeeStructureAssignmentStudentScreen()},resetToMainScreen:function(){feeAssignment.returnToClassMainScreen(),feeAssignment.returnToStudentMainScreen(),feeAssignment.returnToBulkStudentMainScreen()},returnToClassMainScreen:function(){$(".class-fee-assign-tab").attr("style","display:none"),$("#class-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()},returnToStudentMainScreen:function(){$(".student-fee-assign-tab").attr("style","display:none"),$("#student-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()},returnToBulkStudentMainScreen:function(){$("#searched-student-list").html(""),$("#bulk-fee-assign-class").val(""),$(".bulk-fee-assign-tab").attr("style","display:none"),$("#bulk-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()}};function populateUnassignedFees(){for(var e=$("#assigned-fee-list").text().trim(),t=JSON.parse(e),n={},a=0;a<t.assigned_fees.length;a++)n[t.assigned_fees[a].feeConfigurationResponse.feeConfigurationBasicInfo.feeId]=!0;var i=$("#session-authorized-fee-assignments").text().trim(),s=JSON.parse(i),o=[],d=[],r=[];for(a=0;a<s.authorized_fee_assignments.length;a++)s.authorized_fee_assignments[a].feeConfigurationResponse.feeConfigurationBasicInfo.feeId in n||("REGULAR"==s.authorized_fee_assignments[a].feeConfigurationResponse.feeConfigurationBasicInfo.feeType?o.push(s.authorized_fee_assignments[a]):"ONE_TIME"==s.authorized_fee_assignments[a].feeConfigurationResponse.feeConfigurationBasicInfo.feeType?d.push(s.authorized_fee_assignments[a]):"SPECIAL"==s.authorized_fee_assignments[a].feeConfigurationResponse.feeConfigurationBasicInfo.feeType&&r.push(s.authorized_fee_assignments[a]));for(var c='<option selected value=""> </option>',l=0;l<o.length;l++)c+='<option value="'+o[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(o[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#regular-fee-select").empty().append(c),c='<option selected value=""> </option>';for(l=0;l<d.length;l++)c+='<option value="'+d[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(d[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#onetime-fee-select").empty().append(c),c='<option selected value=""> </option>';for(l=0;l<r.length;l++)c+='<option value="'+r[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(r[l].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#special-fee-select").empty().append(c)}function addClassNewFeeAssignmentCallBack(){$("#class-assign-new-fee-button").on("click",function(){var e=$("#fee-assign-class").find(":selected").val().trim();if(""!=e){var t=$("#fee-assign-class").find(":selected").text().trim();$("#assign-new-fee-class-name").val(t),$("#assign-new-fee-class-id").val(e),clearAuthorizedFeeHeads(),populateUnassignedFees(),$(".class-fee-assign-tab").attr("style","display:none"),$("#class-new-fee-assignment-tab").attr("style","display:block")}else showErrorDialogBox("Please select class for which you want to assign fees")})}function addStudentNewFeeAssignmentCallBack(){$("#student-assign-new-fee-button").on("click",function(){var e=$("#fee-assign-view-student-id").text().trim(),t=$("#search-student-name").text().trim(),n=$("#search-admission-number").text().trim(),a=$("#search-student-class").text().trim(),i=$("#search-student-class-section").text().trim();$("#assign-new-fee-student-id").val(e),$("#assign-new-fee-admission-number").val(n),$("#new-fee-view-student-name").text(t),$("#new-fee-view-admission-number").text(n),$("#new-fee-view-student-class").text(a),$("#new-fee-view-student-class-section").text(i),clearAuthorizedFeeHeads(),populateUnassignedFees(),$(".student-fee-assign-tab").attr("style","display:none"),$("#student-new-fee-assignment-tab").attr("style","display:block")})}function getBulkFeesAssignmentScreen(){$("#bulk-assignment-confirmation-modal").modal("toggle");var e=[];$("input.bulk-student-assignment-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-fee-assign-student-id").first().text().trim();e.push(t)}});var t=academicSessionHandler.getSelectedSessionId(),n=$("#bulk-fee-assign-class option:selected").text();ajaxClient.post("/fees/bulk-fee-assignment/status/"+t,{studentIds:JSON.stringify(e)},function(e){$("#bulk-new-fee-assignment-tab").html(e),$(".bulk-fee-assign-tab").attr("style","display:none"),$("#bulk-new-fee-assignment-tab").attr("style","display:block"),bindShowSelectedStudentModal(),bindAssignmentFeeSelectionEvents();var t=$("#student-text-underline-id").text().trim();-1==n.indexOf("Class")&&(n="Class "+n);var a=t+" of "+n;$("#student-text-underline-id").text(a)})}function updateBulkAssignmentStudentConfirmModal(){var e=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assignment-confirmation-modal-text").text("You have selected "+e+" students to assign fees at student level. Do you want to proceed?")}function bulkAssignmentStudentSelectCheckbox(e){var t=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(t)}function selectBulkAssignmentStudentList(e){if(e.checked){$(".bulk-student-assignment-checkbox").prop("checked",!0);var t=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(t)}else $(".bulk-student-assignment-checkbox").prop("checked",!1),$("#bulk-assign-selected-count").html(0)}function bindAssignmentFeeSelectionEvents(){bindFeeTypeChangeEvent(),bindAssignmentFeeChangeEvent(),enableFeeAssignmentFeeHeadCallback()}var loadFeeAssignment={loadStudentScreen:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-assignment/"+e,function(e){$("#fee-assignment-screen").html(e),addStudentNewFeeAssignmentCallBack(),bindSearchStudentFeeAssignmentEvent(),bindAssignmentFeeSelectionEvents()})},loadBulkStudentScreen:function(){ajaxClient.get("/fees/bulk-student-assignment",function(e){$("#fee-assignment-screen").html(e),bindBulkFeeClassChangeEvent()})},loadFeeStructureAssignmentStudentScreen:function(){ajaxClient.get("/fees/fee-structure-change-home",function(e){$("#fee-assignment-screen").html(e),loadFeeAssignment.bindPaymentSearchStudentEvent()})},bindPaymentSearchStudentEvent:function(){var e="#student-payment-search-result";$("#student-payment-search").on("click",function(){loadFeeAssignment.doneStudentSearchTyping(e)}),$("#student-payment-search-text").on("keyup",function(t){13==t.keyCode&&loadFeeAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#student-payment-search-text",e,loadFeeAssignment.doneStudentSearchTyping)},doneStudentSearchTyping:function(e){var t=$("#student-payment-search-text").val().trim(),n=academicSessionHandler.getSelectedSessionId();loadFeeAssignment.studentLiveSearchEvent(n,t,e,loadFeeAssignment.loadStudentPaymentDetails,"ENROLLED")},studentLiveSearchEvent:function(e,t,n,a,i){ajaxClient.get("/fees/student-live-search/"+e+"?searchText="+t+"&status="+i,function(e){$(n).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(n,a)})},loadStudentPaymentDetails:function(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-details/"+t+"/"+e,function(e){$("#student-payment-screen").html(e)})},classChangeStudentPopUp:function(){$("#student-promotion-modal").modal("toggle"),loadFeeAssignment.bindPromotingStandardChange()},bindPromotingStandardChange:function(){var e=academicSessionHandler.getSelectedSessionId(),t=JSON.parse($("#student-details-json").text().trim()).studentId;ajaxClient.get("/fees/fee-structure-configuration/"+e+"/"+t,function(e){$("#promotion-configuration-div").html(e)})},changeStudentClass:function(){var e=academicSessionHandler.getSelectedSessionId(),t=JSON.parse($("#student-details-json").text().trim()).studentId,n=[];$("input.fee-structure-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find(".structure-id").text().trim();n.push(e)}});n={uuids:n};$("#student-promotion-modal").modal("toggle"),ajaxClient.post("/fees/fee-structure-change/"+e+"/"+t,{uuids:JSON.stringify(n)},function(e){$("#attendance-feed-status-modal-container").html(e),$("#fee-assignment-status-modal").modal({backdrop:"static",keyboard:!1})})}};function registerFeeAssignmentTabs(){$("#"+STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadStudentScreen()}),$("#"+BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadBulkStudentScreen()}),$("#"+FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadFeeStructureAssignmentStudentScreen()})}function bindFeeTypeChangeEvent(){$("#fee-type-select").change(function(){var e=$(this).find(":selected").val().trim();$(".fee-select").attr("style","display:none"),$(".fee-select").removeClass("active"),$(".fee-select").val(""),"regular"==e?($("#regular-fee-select").attr("style","display:block"),$("#regular-fee-select").addClass("active")):"onetime"==e?($("#onetime-fee-select").attr("style","display:block"),$("#onetime-fee-select").addClass("active")):"special"==e&&($("#special-fee-select").attr("style","display:block"),$("#special-fee-select").addClass("active")),clearAuthorizedFeeHeads()})}function clearAuthorizedFeeHeads(){$(".authorized-assignment-feeheads").attr("style","display:none"),$(".authorized-assignment-feeheads").removeClass("active"),resetAuthorizedFeeHeads()}function resetAuthorizedFeeHeads(){$(".add-assignment-fee-head-row").find(".assignment-fee-head").prop("checked",!1),$(".add-assignment-fee-head-row").find(".assignment-amount").val(""),$(".add-assignment-fee-head-row").find(".assignment-amount").prop("readonly",!0)}function displaySelectedAuthorizedFeeHeads(e){$("#"+e+"-feeheads").addClass("active"),$("#"+e+"-feeheads").attr("style","display:block")}function bindAssignmentFeeChangeEvent(){$(".fee-select").change(function(){var e=$(this).find(":selected").val().trim();clearAuthorizedFeeHeads(),displaySelectedAuthorizedFeeHeads(e)})}function bindClassChangeEvent(){$("#fee-assign-class").change(function(){var e=$(this).find(":selected").val().trim();loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),CLASS_ENTITY,e,CLASS_ASSIGNED_FEE_LIST)})}function bindBulkFeeClassChangeEvent(){$("#bulk-fee-assign-class").change(function(){var e=$(this).find(":selected").val().trim();searchStudentsInBulkAssignment(academicSessionHandler.getSelectedSessionId(),e)})}function bindShowSelectedStudentModal(){$("#selected-bulk-student-text").on("click",function(){$("#bulk-assignment-selected-students-modal").modal("toggle")})}function searchStudentsInBulkAssignment(e,t){ajaxClient.get("/fees/class-students/"+e+"/"+t,function(e){$("#searched-student-list").html(e)})}function bindSearchStudentFeeAssignmentEvent(){var e="#fee-assign-student-search-result";$("#fee-assign-search-student").on("click",function(){feeAssignment.doneStudentSearchTyping(e)}),$("#fee-assign-search-text").on("keyup",function(t){13==t.keyCode&&feeAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#fee-assign-search-text",e,feeAssignment.doneStudentSearchTyping)}function loadStudentFeesAssignment(e){loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),STUDENT_ENTITY,e,STUDENT_ASSIGNED_FEE_LIST)}function loadEntityFeesAssignment(e,t,n,a){t=t.trim(),n=n.trim(),""!=e&&""!=t?ajaxClient.get("/fees/assigned-fees/"+e+"/"+t+"/"+n,function(e){$("#"+a).html(e),bindViewFeeAssignmentEvent(),bindUpdateFeeAssignmentEvent(),bindDeleteFeeAssignmentEvent(),t==STUDENT_ENTITY&&addStudentNewFeeAssignmentCallBack()}):showErrorDialogBox("Please select entity and session to assign fees.")}function enableFeeAssignmentFeeHeadCallback(){$(".assignment-fee-head").change(function(){$(this).is(":checked")?$(this).closest("tr").find("input.assignment-amount").prop("readonly",!1):$(this).closest("tr").find("input.assignment-amount").prop("readonly",!0)})}function addNewClassFeeAssignment(){if(!validateMandatoryFields($("#class-new-fee-assign-modal"))){$("#class-new-fee-assign-confirm-modal").modal("toggle");var e=$("#assign-new-fee-class-id").val(),t=getFeeAssignmentInfo(CLASS_ENTITY,[e]);null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(t){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(t),$("#fee-assignment-status-modal").modal("toggle"),loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),CLASS_ENTITY,e,CLASS_ASSIGNED_FEE_LIST)})}}function addNewStudentFeeAssignment(){$("#student-new-fee-assign-confirm-modal").modal("toggle");var e=$("#assign-new-fee-student-id").val(),t=($("#assign-new-fee-admission-number").val(),getFeeAssignmentInfo(STUDENT_ENTITY,[e]));null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(t){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(t),$("#fee-assignment-status-modal").modal("toggle"),loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),STUDENT_ENTITY,e,STUDENT_ASSIGNED_FEE_LIST)})}function addBulkStudentFeeAssignment(){$("#bulk-student-new-fee-assign-confirm-modal").modal("toggle");var e=getSelectedBulkStudentIds(),t=getFeeAssignmentInfo(STUDENT_ENTITY,e);null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(e){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(e),$("#fee-assignment-status-modal").modal("toggle")})}function getSelectedBulkStudentIds(){var e=[];return $("tr.bulk-assignment-selected-student").each(function(){var t=$(this).attr("id");e.push(t)}),e}function getFeeAssignmentInfo(e,t){var n=$(".fee-select.active").first().find(":selected").val().trim();if(""==n.trim())return showErrorDialogBox("Please select one fees before proceeding for assignment."),null;var a=getAssignedFeeHeadAmounts();return 0==a.length?(showErrorDialogBox("Atleast one fee head must be assigned"),null):{entityIds:t,feeEntity:e,feeId:n,feeHeadAmounts:a}}function getAssignedFeeHeadAmounts(){var e=[];return $(".authorized-assignment-feeheads.active").find("tr.add-assignment-fee-head-row").each(function(){if(0!=$(this).find(".assignment-fee-head").length&&$(this).find(".assignment-fee-head").is(":checked")){var t=$(this).attr("id"),n=0;""!=$(this).find("input.assignment-amount").val()&&(n=parseFloat($(this).find("input.assignment-amount").val()));var a={};a.feeHeadId=t,a.amount=n,e.push(a)}}),e}function bindViewFeeAssignmentEvent(){$(".view-assigned-fee").on("click",function(){var e=$(this).parent().find(".assigned-fee-info").text().trim();populateViewFeeAssignmentContent(JSON.parse(e))})}function bindUpdateFeeAssignmentEvent(){$(".update-assigned-fee").on("click",function(){for(var e=$(this).parent().find(".assigned-fee-info").text().trim(),t=JSON.parse(e),n=$("#session-authorized-fee-assignments").text().trim(),a=JSON.parse(n).authorized_fee_assignments,i=null,s=0;s<a.length;s++)if(a[s].feeConfigurationResponse.feeConfigurationBasicInfo.feeId==t.feeConfigurationResponse.feeConfigurationBasicInfo.feeId){i=a[s];break}populateUpdateFeeAssignmentContent(t,i)})}function moduleExists(e,t){for(var n=0;n<e.length;n++)if(e[n].moduleId==t)return!0;return!1}function populateUpdateFeeAssignmentContent(e,t){var n=$("#entity-type").text().trim();$("#update\\.assigned-fee-id").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeId),$("#update\\.assigned-fee-name").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeName),$("#update\\.assigned-fee-academic-session").val(e.feeConfigurationResponse.academicSession.displayName),$("#update\\.assigned-fee-description").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.description),"ONE_TIME"!=e.feeConfigurationResponse.feeConfigurationBasicInfo.feeType?$("#update\\.assigned-fee-duration").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month+" to "+e.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month):$("#update\\.assigned-fee-duration-container").attr("style","display:none");var a={};for(s=0;s<e.feeHeadAmountDetailsList.length;s++)a[e.feeHeadAmountDetailsList[s].feeHeadConfiguration.feeHeadId]=e.feeHeadAmountDetailsList[s];for(var i={},s=0;s<t.authorizedFeeHeadAssignments.length;s++)t.authorizedFeeHeadAssignments[s].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId in a||(i[t.authorizedFeeHeadAssignments[s].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId]=t.authorizedFeeHeadAssignments[s]);var o="";s=0;if(n==STUDENT_ENTITY){var d=$("#fee-assign-view-student-id").text().trim(),r=$("#fee-assign-search-text").val().trim();for(var c in $("#update\\.assigned-entity-id").val(d),$("#update\\.assigned-admission-number").val(r),a)o=a[c].feeEntity!=CLASS_ENTITY&&moduleExists(a[c].authorizedModules,"FEES")?o+'<tr class="fee-head-row" id = "'+c+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" checked><label class="form-check-label"> '+a[c].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+a[c].amount+'"></td></tr>':o+'<tr class="fee-head-row non-editable-row" id = "'+c+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+a[c].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+a[c].amount+'" readonly></td></tr>'}else{d=$("#fee-assign-class").val();for(var c in $("#update\\.assigned-entity-id").val(d),a)o=moduleExists(a[c].authorizedModules,"FEES")?o+'<tr class="fee-head-row" id = "'+c+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" checked><label class="form-check-label"> '+a[c].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+a[c].amount+'"></td></tr>':o+'<tr class="fee-head-row non-editable-row" id = "'+c+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+a[c].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+a[c].amount+'" readonly></td></tr>'}for(var l in i)o=i[l].module_ids.indexOf("FEES")<0?o+'<tr class="fee-head-row non-editable-row" id = "'+l+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+i[l].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" readonly></td></tr>':o+'<tr class = "fee-head-row" id = "'+l+'"><td> <div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head"><label class="form-check-label" for="fee-head"> '+i[l].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+' </label></div></td> <td> <input type="number" class="form-control form-control-sm assignment-amount" id="update.amount'+l+'" readonly></td></tr>';$("#update\\.assigned-fee-head-amounts").html(o),enableFeeAssignmentFeeHeadCallback(),$("#update-assigned-fee-modal").modal("toggle")}function updateFeeAssignment(){var e=$("#entity-type").text().trim();$("#update-assigned-fee-modal").modal("toggle");var t=$("#update\\.assigned-entity-id").val(),n=($("#update\\.assigned-admission-number").val(),getUpdateFeeAssignmentInfo(e,t));ajaxClient.post("/fees/update-fee-assignment",{updateFeeAssignmentInfo:JSON.stringify(n)},function(n){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(n),$("#fee-assignment-status-modal").modal("toggle");var a=academicSessionHandler.getSelectedSessionId();loadEntityFeesAssignment(a,e,t,e==STUDENT_ENTITY?STUDENT_ASSIGNED_FEE_LIST:CLASS_ASSIGNED_FEE_LIST)})}function getUpdateFeeAssignmentInfo(e,t){var n=$("#update\\.assigned-fee-id").val(),a=[];return $("tr.fee-head-row").each(function(){if($(this).find(".assignment-fee-head").is(":checked")&&!$(this).hasClass("non-editable-row")){var e=$(this).attr("id"),t=0;""!=$(this).find("input.assignment-amount").val()&&(t=parseFloat($(this).find("input.assignment-amount").val()));var n={};n.feeHeadId=e,n.amount=t,a.push(n)}}),{entityId:t,feeEntity:e,feeId:n,feeHeadAmounts:a}}function bindDeleteFeeAssignmentEvent(){$(".delete-assigned-fee").on("click",function(){var e=$(this).parent().find(".assigned-fee-info").text().trim(),t=JSON.parse(e),n=$("#entity-type").text().trim();if($("#delete\\.assigned-fee-id").val(t.feeConfigurationResponse.feeConfigurationBasicInfo.feeId),n==STUDENT_ENTITY){var a=$("#fee-assign-view-student-id").text().trim(),i=$("#fee-assign-search-text").val().trim();$("#delete\\.assigned-entity-id").val(a),$("#delete\\.assigned-admission-number").val(i)}else{a=$("#fee-assign-class").val();$("#delete\\.assigned-entity-id").val(a)}$("#delete-assigned-fee-modal").modal("toggle")})}function deleteAssignedFees(){var e=$("#entity-type").text().trim();$("#delete-assigned-fee-modal").modal("toggle");var t=$("#delete\\.assigned-entity-id").val(),n=($("#delete\\.assigned-admission-number").val(),$("#delete\\.assigned-fee-id").val());ajaxClient.post("/fees/delete-fee-assignment/"+e+"/"+t+"/"+n,{},function(n){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(n),$("#fee-assignment-status-modal").modal("toggle");var a=academicSessionHandler.getSelectedSessionId();loadEntityFeesAssignment(a,e,t,e==STUDENT_ENTITY?STUDENT_ASSIGNED_FEE_LIST:CLASS_ASSIGNED_FEE_LIST)})}var dueDateMaxPastDays=365,REGULAR_FEE_LIST_TAB_ID="regular-fee-tab",ONETIME_FEE_LIST_TAB_ID="onetime-fee-tab",SPECIAL_FEE_LIST_TAB_ID="special-fee-tab",NO_STREAM_VALUE="NA";function loadFeeConfigurationPage(){ajaxClient.get("/fees/fee-config",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(initFeeConfigScreen),registerAddFeeConfigCallback(),reloadFeeConfigPageBindEvents(),bindRemoveErrorDisplayEvent()})}function reloadFeeConfigPageBindEvents(){bindViewFeeEvents(),bindUpdateFeeEvents(),initDateWithYearRange("-5:+5",!0)}function initFeeConfigScreen(){var e=academicSessionHandler.getSelectedSession();$(".fee-config-academic-session").html('<option value="'+e.academicSessionId+'">'+e.displayName+"</option>"),$("#regular-fee-start-month").val(e.startMonth);var t=$(".fee-config-list-type.active").first().attr("id").trim();loadFeelist(e.academicSessionId,t)}function openAddFeeConfigModal(){resetFeeConfigModal();var e=$(".fee-config-list-type.active").first().attr("id").trim();e==REGULAR_FEE_LIST_TAB_ID?$("a#regular-fee").trigger("click"):e==ONETIME_FEE_LIST_TAB_ID?$("a#one-time-fee").trigger("click"):e==SPECIAL_FEE_LIST_TAB_ID&&$("a#special-fee").trigger("click"),$("#add-fee-config-modal").modal({backdrop:"static",keyboard:!1}),$("#add-fee-config-modal").modal("toggle")}function resetFeeConfigModal(){$("#add-onetime-fee-form").find("input").val(""),$("#add-onetime-fee-form").find("select:not(.fee-config-academic-session)").val(""),$("#add-onetime-fee-form").find("textarea").val(""),$("#add-special-fee-form").find("input").val(""),$("#add-special-fee-form").find("select:not(.fee-config-academic-session)").val(""),$("#add-special-fee-form").find("textarea").val("")}function resetFeeHeadConfigModal(){$("#add-fee-head-modal").find("input").val(""),$("#add-fee-head-modal").find("select").val(""),$("#add-fee-head-modal").find("textarea").val("")}function refreshFeeConfigPage(e){loadFeelist(academicSessionHandler.getSelectedSessionId(),e)}function loadFeeHeadConfigurationPage(){ajaxClient.get("/fees/fee-head-config",function(e){$("#main-content").html(e),registerAddFeeHeadCallback(),registerUpdateFeeHeadCallBack(),registerDeleteFeeHeadCallBack(),bindRemoveErrorDisplayEvent()})}function registerAddFeeHeadCallback(){$("#add-fee-head").on("click",function(){if(!validateMandatoryFields($("#add-fee-head-modal"))){var e=$("#fee-head-name").val(),t=$("#fee-head-category option:selected").val(),n=$("#fee-head-description").val();$("#add-fee-head-modal").modal("toggle");var a={feeCategoryId:t,feeHead:e,description:n};ajaxClient.post("/fees/add-fee-head",{feeHeadData:JSON.stringify(a)},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}})}function registerAddFeeConfigCallback(){$("#add-fee-config-button").on("click",function(){var e=$(".fee-config-type.active").attr("id");if("regular-fee"==e){if(validateMandatoryFields($("#add-regular-fee-form")))return;var t=$("#regular-fee-academic-session option:selected").val(),n=$("#regular-fee-duration").val();$("#add-fee-config-modal").modal("toggle"),ajaxClient.post("/fees/add-regular-fee-config",{sessionId:t,duration:n},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}else if("one-time-fee"==e){if(validateMandatoryFields($("#add-onetime-fee-form")))return;t=$("#onetime-fee-academic-session option:selected").val();var a=$("#onetime-fee-name").val(),i=$("#onetime-fee-description").val(),s=$("#onetime-fee-allow-pending-enrollment:checkbox:checked").length>0,o=null;null!=(d=getDate($("#onetime-fee-due-date").val()))&&(o=d.getTime()/1e3),$("#add-fee-config-modal").modal("toggle"),oneTimeFeeData={feeName:a,academicSessionId:t,description:i,dueDate:o,allowPendingEnrollment:s},ajaxClient.post("/fees/add-onetime-fee",{oneTimeFeeData:JSON.stringify(oneTimeFeeData)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID)})}else if("special-fee"==e){if(validateMandatoryFields($("#add-special-fee-form")))return;t=$("#special-fee-academic-session option:selected").val(),a=$("#special-fee-name").val(),i=$("#special-fee-description").val();var d,r=$("#special-fee-start-month option:selected").val(),c=$("#special-fee-end-month option:selected").val(),l=(s=$("#special-fee-allow-pending-enrollment:checkbox:checked").length>0,null);null!=(d=getDate($("#special-fee-due-date").val()))&&(l=d.getTime()/1e3),$("#add-fee-config-modal").modal("toggle"),specialFeeData={startMonth:r,endMonth:c,feeConfigurationBasicInfo:{feeName:a,academicSessionId:t,description:i,dueDate:l,allowPendingEnrollment:s}},ajaxClient.post("/fees/add-special-fee",{specialFeeData:JSON.stringify(specialFeeData)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID)})}})}function loadFeelist(e,t){ajaxClient.get("/fees/fee-list/"+e,function(e){$("#fees-list").html(e),reloadFeeConfigPageBindEvents(),null!=t&&$("#"+t).trigger("click")})}function loadFeeHeadlist(){ajaxClient.get("/fees/fee-head-list",function(e){$("#fee-head-list").html(e),registerUpdateFeeHeadCallBack(),registerDeleteFeeHeadCallBack()})}function bindViewFeeEvents(){bindViewRegularFeeEvent(),bindViewOneTimeFeeEvent(),bindViewSpecialFeeEvent(),bindDeleteOneTimeFeeEvent(),bindDeleteSpecialFeeEvent()}function bindViewRegularFeeEvent(){$(".view-regular-fee").on("click",function(){var e=$(this).parent().find(".regular-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.regular-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.regular-fee-academic-session").val(t.academicSession.displayName),$("#view\\.regular-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.regular-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view\\.regular-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#view-regular-fee-modal").modal("toggle")})}function bindViewOneTimeFeeEvent(){$(".view-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.onetime-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.onetime-fee-academic-session").val(t.academicSession.displayName),$("#view\\.onetime-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.onetime-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#view\\.onetime-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view-onetime-fee-modal").modal("toggle")})}function bindDeleteOneTimeFeeEvent(){$(".delete-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#delete-one-time-fee-id").text(t.feeConfigurationBasicInfo.feeId),$("#delete-one-time-fee-confirm-modal").modal("toggle")})}function bindViewSpecialFeeEvent(){$(".view-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.special-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.special-fee-academic-session").val(t.academicSession.displayName),$("#view\\.special-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.special-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view\\.special-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#view\\.special-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#view-special-fee-modal").modal("toggle")})}function bindDeleteSpecialFeeEvent(){$(".delete-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#delete-special-fee-id").text(t.feeConfigurationBasicInfo.feeId),$("#delete-special-fee-confirm-modal").modal("toggle")})}function deleteRegularFees(){var e=academicSessionHandler.getSelectedSessionId();$("#delete-regular-fee-confirm-modal").modal("toggle"),ajaxClient.post("/fees/delete-regular-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}function deleteSpecialFee(){$("#delete-special-fee-confirm-modal").modal("toggle");var e=$("#delete-special-fee-id").text();ajaxClient.post("/fees/delete-special-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID)})}function deleteOneTimeFee(){$("#delete-one-time-fee-confirm-modal").modal("toggle");var e=$("#delete-one-time-fee-id").text();ajaxClient.post("/fees/delete-one-time-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID)})}function bindUpdateFeeEvents(){bindUpdateRegularFeeEvent(),bindUpdateOneTimeFeeEvent(),bindUpdateSpecialFeeEvent()}function bindUpdateRegularFeeEvent(){$(".update-regular-fee").on("click",function(){var e=$(this).parent().find(".regular-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.regular-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.regular-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.regular-fee-academic-session").val(t.academicSession.displayName),$("#update\\.regular-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.regular-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update\\.regular-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#update-regular-fee-modal").modal("toggle")})}function bindUpdateOneTimeFeeEvent(){$(".update-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.onetime-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.one-time-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.one-time-fee-academic-session").val(t.academicSession.displayName),$("#update\\.one-time-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.one-time-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#update\\.one-time-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update-one-time-fee-modal").modal("toggle")})}function bindUpdateSpecialFeeEvent(){$(".update-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.special-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.special-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.special-fee-academic-session").val(t.academicSession.displayName),$("#update\\.special-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.special-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update\\.special-fee-start-month").val(t.feeConfigurationBasicInfo.startMonthYear.month),$("#update\\.special-fee-end-month").val(t.feeConfigurationBasicInfo.endMonthYear.month),$("#update\\.special-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#update-special-fee-modal").modal("toggle")})}function updateRegularFeeConfiguration(){if(!validateMandatoryFields($("#update-regular-fee-modal"))){var e=$("#update\\.regular-fee-id").val(),t=$("#update\\.regular-fee-name").val(),n=$("#update\\.regular-fee-description").val(),a=getDate($("#update\\.regular-fee-due-date").val()),i=null;null!=a&&(i=a.getTime()/1e3),$("#update-regular-fee-modal").modal("toggle");var s={feeId:e,feeName:t,dueDate:i,description:n};ajaxClient.post("/fees/update-fee-config",{updateFeeConfigData:JSON.stringify(s)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}}function updateOneTimeFeeConfiguration(){if(!validateMandatoryFields($("#update-one-time-fee-modal"))){var e=$("#update\\.onetime-fee-id").val(),t=$("#update\\.one-time-fee-name").val(),n=$("#update\\.one-time-fee-description").val(),a=$("#update\\.one-time-fee-allow-pending-enrollment:checkbox:checked").length>0,i=getDate($("#update\\.one-time-fee-due-date").val()),s=null;null!=i&&(s=i.getTime()/1e3),$("#update-one-time-fee-modal").modal("toggle");var o={feeId:e,feeName:t,dueDate:s,description:n,allowPendingEnrollment:a};ajaxClient.post("/fees/update-fee-config",{updateFeeConfigData:JSON.stringify(o)},function(e){refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID),$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle")})}}function updateSpecialFeeConfiguration(){if(!validateMandatoryFields($("#update-special-fee-modal"))){var e=$("#update\\.special-fee-id").val(),t=$("#update\\.special-fee-name").val(),n=$("#update\\.special-fee-start-month option:selected").val(),a=$("#update\\.special-fee-end-month option:selected").val(),i=$("#update\\.special-fee-allow-pending-enrollment:checkbox:checked").length>0,s=$("#update\\.special-fee-description").val(),o=getDate($("#update\\.special-fee-due-date").val()),d=null;null!=o&&(d=o.getTime()/1e3),$("#update-special-fee-modal").modal("toggle");var r={feeId:e,feeName:t,dueDate:d,description:s,startMonth:n,endMonth:a,allowPendingEnrollment:i};ajaxClient.post("/fees/update-special-fee-config",{updateSpecialFeeConfigData:JSON.stringify(r)},function(e){refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID),$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle")})}}function registerUpdateFeeHeadCallBack(){$(".update-fee-head").on("click",function(){var e=$(this).parent().find(".fee-head-info").text().trim(),t=JSON.parse(e);$("#update\\.fee-head-id").val(t.feeHeadConfiguration.feeHeadId),$("#update\\.fee-head-name").val(t.feeHeadConfiguration.feeHead),$("#update\\.fee-category-id").val(t.feeCategory.feeCategoryId),$("#update\\.fee-category-name").val(t.feeCategory.feeCategoryName),$("#update\\.fee-head-description").val(t.feeHeadConfiguration.description),$("#update-fee-head-modal").modal("toggle")})}function updateFeeHead(){if(!validateMandatoryFields($("#update-fee-head-modal"))){var e=$("#update\\.fee-head-id").val(),t=$("#update\\.fee-head-name").val(),n=$("#update\\.fee-category-id").val(),a=$("#update\\.fee-head-description").val();$("#update-fee-head-modal").modal("toggle");var i={feeHeadId:e,feeHead:t,feeCategoryId:n,description:a};ajaxClient.post("/fees/update-fee-head",{updateFeeHeadData:JSON.stringify(i)},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}}function registerDeleteFeeHeadCallBack(){$(".delete-fee-head").on("click",function(){var e=$(this).parent().find(".fee-head-info").text().trim(),t=JSON.parse(e);$("#delete-fee-head-id").val(t.feeHeadConfiguration.feeHeadId),$("#delete-fee-head-confirm-modal").modal("toggle")})}function deleteFeeHead(){$("#delete-fee-head-confirm-modal").modal("toggle");var e=$("#delete-fee-head-id").val();ajaxClient.post("/fees/delete-fee-head-configuration/"+e,{},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}var graphLoadTimer,feeStructure={dataCache:{},INSTITUTE_ENTITY:"institute",initDataCache:function(){for(var e=readJson("#session-authorized-fee-assignments"),t=readJson("#add-fee-standards"),n=readJson("#configured-fee-structure-info"),a={},i={},s={},o={},d={},r={},c=0;c<e.length;c++){var l=e[c].feeConfigurationResponse.feeConfigurationBasicInfo;s[l.feeId]=l;for(var u=[],f=0;f<e[c].authorizedFeeHeadAssignments.length;f++){var m=e[c].authorizedFeeHeadAssignments[f].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId;o[m]=e[c].authorizedFeeHeadAssignments[f].feeHeadConfigurationResponse.feeHeadConfiguration,e[c].authorizedFeeHeadAssignments[f].module_ids.includes("FEES")&&u.push(m)}d[l.feeId]=u}for(c=0;c<t.length;c++){var g=t[c];a[g.standardId]=g}for(c=0;c<n.length;c++){var p=n[c];i[p.structureId]=p}for(feeId in feeStructure.dataCache={},feeStructure.dataCache.feeStructureMap=i,feeStructure.dataCache.standards=t,feeStructure.dataCache.standardsMap=a,feeStructure.dataCache.feeMap=s,feeStructure.dataCache.feeHeadMap=o,feeStructure.dataCache.authorizedFeeIdFeeHeadMap=d,feeStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},d){u=d[feeId];var h=feeStructure.createFeeHeadSelectMenu(u,"");r[feeId]=h}feeStructure.dataCache.feeIdFeeHeadSelectOptionData=r},createFeeHeadSelectMenu:function(e,t){for(var n='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',a=0;a<e.length;a++){var i=e[a],s=feeStructure.dataCache.feeHeadMap[i].feeHead;n+=t==i?'<option value="'+i+'" selected>'+s+"</option>":'<option value="'+i+'">'+s+"</option>"}return n+="</select>"},loadFeeStructureConfigurationPageWithoutSession:function(){ajaxClient.get("/fees/fee-structure-config-without-session",function(e){$("#main-content").html(e),feeStructure.initDataCache(),academicSessionHandler.bindSessionChangeEvent(feeStructure.changeSession)})},loadFeeStructureConfigurationPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/fee-structure-config/"+e,function(e){$("#configured-fee-structure-list").html(e),feeStructure.initDataCache()})},changeSession:function(){feeStructure.loadFeeStructureConfigurationPage()},createFeeContainerId:function(e,t){return e+"-"+t},createEntityContainerId:function(e){return e+"-fee-structure-container"},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var n=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&n++}),n>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()})},insertFeeHeadRow:function(e,t,n,a){var i="";if(null==n){i='<tr id=""> <td> '+feeStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var s=feeStructure.dataCache.authorizedFeeIdFeeHeadMap[t];i='<tr id=""> <td> '+feeStructure.createFeeHeadSelectMenu(s,n)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+a+'"> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(i),feeStructure.bindFeeHeadRowEvents(e)},bindFeeContainerEvents:function(e,t,n,a,i){$("#"+t).find(".add-fee-head-button").on("click",function(){feeStructure.insertFeeHeadRow(t,a,null,0)}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[a],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),feeStructure.populateSelectFeesDropdown(e,n,i)})},insertFeeDetailsCard:function(e,t,n,a){a[n]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=feeStructure.createEntityContainerId(t),s=feeStructure.createFeeContainerId(t,n),o='<div class="fee-structure"> <div class="card card-border text-center" id="'+s+'"> <p class="fee-id" style="display:none;">'+n+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+feeStructure.dataCache.feeMap[n].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(o),feeStructure.bindFeeContainerEvents(e,s,t,n,a),feeStructure.populateSelectFeesDropdown(e,t,a)},bindSelectFeesEvent:function(e,t,n){$(e).find(".fee-select-option").on("click",function(){var t=$(this).attr("id").split("-select-")[0],a=$(this).attr("id").split("-select-")[1];feeStructure.insertFeeDetailsCard(e,a,t,n)})},populateSelectFeesDropdown:function(e,t,n){var a=feeStructure.dataCache.feeMap,i="";for(feeId in a)feeId in n||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+a[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),feeStructure.bindSelectFeesEvent(e,t,n)},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete feeStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(feeStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;"),feeStructure.populateStandardSelectDropdown()})},getStandardContainerId:function(e){return e+"-container"},insertStandardDetailsCard:function(e){feeStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=feeStructure.dataCache.standardsMap,n="";null!=t[e].stream&&t[e].stream!=NO_STREAM_VALUE&&(n=" ("+t[e].stream+")");var a=t[e].standardName.toUpperCase()+n,i=feeStructure.getStandardContainerId(e),s='<div id="'+i+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+a+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(s),feeStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},feeStructure.populateSelectFeesDropdown("#"+i,e,feeStructure.dataCache.configureNewStructure.standard.selectedFees[e]),feeStructure.bindStandardContainerEvents(i,e),feeStructure.populateStandardSelectDropdown()},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];feeStructure.insertStandardDetailsCard(e)})},populateStandardSelectDropdown:function(){var e=feeStructure.dataCache.standardsMap,t="";for(standardId in e){var n="";null!=e[standardId].stream&&e[standardId].stream!=NO_STREAM_VALUE&&(n=" ("+e[standardId].stream+")");var a=e[standardId].standardName.toUpperCase()+n;standardId in feeStructure.dataCache.configureNewStructure.standard.selectedStandards||(t+='<a class="dropdown-item standard-select-option" href="#" id="'+standardId+'-select">'+a+"</a>")}$(".class-fee-structure").find(".standard-select-dropdown").html(t),feeStructure.bindStandardSelectEvent()},resetFeeStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p>'),$("#class-fee-structure-config-accordion").html('<p class="standrad-add-structure-hint-text black-color text-center"> Select class to add new fee structure</p>'),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#fee-structure-name").val(""),$("#fee-structure-types").val(""),$("#fee-structure-name").attr("disabled",!1),$("#fee-structure-types").attr("disabled",!1),$("#fee-structure-config-modal").find(".modal-footer").css("display",""),$("#nav-structure-tab").find(".nav-item").removeClass("active"),$("#nav-institute-structure-config-tab").addClass("active"),$("#nav-structure-tabContent").find(".tab-pane").removeClass("show active"),$("#nav-institute-structure-config").addClass("show active")},loadNewFeeStructureConfigureModal:function(){feeStructure.resetFeeStructureModal(),$("#fee-structure-config-modal").find(".modal-title").html("Configure New Fee Structure"),$("#submit-fee-structure").html("Configure New Fee Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),feeStructure.dataCache.configureNewStructure.institute.selectedFees={},feeStructure.dataCache.configureNewStructure.standard.selectedStandards={},feeStructure.dataCache.configureNewStructure.standard.selectedFees={},feeStructure.dataCache.selectedStructureId=null,feeStructure.populateSelectFeesDropdown(".institute-structure",feeStructure.INSTITUTE_ENTITY,feeStructure.dataCache.configureNewStructure.institute.selectedFees),feeStructure.populateStandardSelectDropdown()},fillFeeStructureModal:function(e,t){feeStructure.dataCache.selectedStructureId=e;var n=feeStructure.dataCache.feeStructureMap;if(e in n){var a=n[e];feeStructure.resetFeeStructureModal(),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#fee-structure-name").val(a.structureName),$("#fee-structure-types").val(a.feeStructureType),$("#fee-structure-types").attr("disabled",!0),$("#fee-structure-config-modal").find(".modal-title").html("Update Fee Structure"),$("#submit-fee-structure").html("Update Fee Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),feeStructure.dataCache.configureNewStructure.institute.selectedFees={},feeStructure.dataCache.configureNewStructure.standard.selectedStandards={},feeStructure.dataCache.configureNewStructure.standard.selectedFees={};for(var i={},s={},o=0;o<a.entityFeeAssignments.length;o++){var d=a.entityFeeAssignments[o];if("INSTITUTE"==d.feeEntity)for(var r=0;r<d.feeIdFeeHeadDetailsList.length;r++){i[u=(l=d.feeIdFeeHeadDetailsList[r]).feeConfigurationResponse.feeConfigurationBasicInfo.feeId]=l}else if("CLASS"==d.feeEntity){var c=d.entityId;s[c]={};for(r=0;r<d.feeIdFeeHeadDetailsList.length;r++){var l,u=(l=d.feeIdFeeHeadDetailsList[r]).feeConfigurationResponse.feeConfigurationBasicInfo.feeId;s[c][u]=l}}}for(u in i){feeStructure.insertFeeDetailsCard(".institute-structure",feeStructure.INSTITUTE_ENTITY,u,feeStructure.dataCache.configureNewStructure.institute.selectedFees);var f=feeStructure.createFeeContainerId(feeStructure.INSTITUTE_ENTITY,u);for(o=0;o<i[u].feeHeadAmountDetailsList.length;o++){var m=i[u].feeHeadAmountDetailsList[o].feeHeadId,g=i[u].feeHeadAmountDetailsList[o].amount;feeStructure.insertFeeHeadRow(f,u,m,g)}}for(c in s){feeStructure.insertStandardDetailsCard(c);var p=feeStructure.getStandardContainerId(c);for(u in s[c]){feeStructure.insertFeeDetailsCard("#"+p,c,u,feeStructure.dataCache.configureNewStructure.standard.selectedFees[c]);for(f=feeStructure.createFeeContainerId(c,u),o=0;o<s[c][u].feeHeadAmountDetailsList.length;o++){m=s[c][u].feeHeadAmountDetailsList[o].feeHeadId,g=s[c][u].feeHeadAmountDetailsList[o].amount;feeStructure.insertFeeHeadRow(f,u,m,g)}}}feeStructure.populateSelectFeesDropdown(".institute-structure",feeStructure.INSTITUTE_ENTITY,feeStructure.dataCache.configureNewStructure.institute.selectedFees),feeStructure.populateStandardSelectDropdown(),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#fee-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#fee-structure-config-modal").find(".modal-footer").css("display","none"))}else showErrorDialogBox("Invalid Fee Structure.")},populateDeleteStructureModal:function(e){var t=feeStructure.dataCache.feeStructureMap[e];feeStructure.dataCache.feeStructureID=e,$("#delete-fee-structure-modal-text").html("Do you want to delete fee Structure : "+t.structureName+"?")},submitFeeStructure:function(){if(validateMandatoryFields($("#fee-structure-config-modal")))showErrorDialogBox("Please fill mandatory fields.");else{$("#fee-structure-config-modal").modal("toggle");var e=$("#fee-structure-name").val(),t=academicSessionHandler.getSelectedSessionId(),n=$("#fee-structure-types option:selected").val(),a=null;null!=feeStructure.dataCache.selectedStructureId&&""!=feeStructure.dataCache.selectedStructureId.trim()&&(a=feeStructure.dataCache.selectedStructureId);var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:feeStructure.getFeeStructureData("#institute-fee-structure-container")},s=feeStructure.getStandardsFeeStructureData();s.push(i);var o=[{structureId:a,structureName:e,feeStructureType:n,entityFeeAssignmentPayloads:s}],d="/fees/add-fee-structure/"+t;null!=a&&(d="/fees/update-fee-structure/"+a+"/"+t),ajaxClient.post(d,{feeAssignmentStructurePayload:JSON.stringify(o)},function(e){$("#fee-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),feeStructure.loadFeeStructureConfigurationPage()})}},getStandardsFeeStructureData:function(){var e=[];return $("#class-fee-structure-config-accordion").find(".standard-fee-structure").each(function(){var t=$(this).find(".standard-id").text().trim(),n=feeStructure.getFeeStructureData(this);e.push({entityId:t,feeEntity:"CLASS",feeIdFeeHeadsList:n})}),e},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),n=[],a={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in a)){var t=$(this).find(".fee-head-amount").val();n.push({feeHeadId:e,amount:t})}}),n.length>0&&t.push({feeId:e,feeHeadAmountList:n})}),t},deleteFeeStructure:function(){$("#delete-fee-structure-modal").modal("toggle");var e=feeStructure.dataCache.feeStructureID,t=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/fees/delete-fee-structure/"+e+"/"+t,{},function(e){$("#fee-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),feeStructure.loadFeeStructureConfigurationPage()})}},discountStructure={dataCache:{},INSTITUTE_ENTITY:"institute",loadDiscountStructureConfigurationPageWithoutSession:function(){ajaxClient.get("/fees/discount-structure-without-session",function(e){$("#main-content").html(e),discountStructure.initDataCache(),academicSessionHandler.bindSessionChangeEvent(discountStructure.changeSession)})},loadDiscountStructureConfigurationPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/discount-structure/"+e,function(e){$("#configured-discount-structure-list").html(e),discountStructure.initDataCache()})},changeSession:function(){discountStructure.loadDiscountStructureConfigurationPage()},initDataCache:function(){for(var e=readJson("#fee-data"),t=readJson("#discount-standards"),n=readJson("#discount-structures"),a={},i={},s={},o={},d={},r=e.feeConfigurationBasicInfoList,c=e.feeHeadConfigurationResponseList,l=0;l<r.length;l++){var u=r[l];s[u.feeId]=u;var f=discountStructure.createFeeHeadSelectMenu(c,"");d[u.feeId]=f}for(l=0;l<c.length;l++){var m=c[l];o[m.feeHeadConfiguration.feeHeadId]=m}for(l=0;l<t.length;l++){var g=t[l];a[g.standardId]=g}for(l=0;l<n.length;l++){var p=n[l];i[p.feeDiscountMetadata.discountStructureId]=p}discountStructure.dataCache={},discountStructure.dataCache.discountStructureMap=i,discountStructure.dataCache.standards=t,discountStructure.dataCache.standardsMap=a,discountStructure.dataCache.feeMap=s,discountStructure.dataCache.feeHeadMap=o,discountStructure.dataCache.feeHeadConfigurationResponseList=c,discountStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},discountStructure.dataCache.feeIdFeeHeadSelectOptionData=d},createFeeHeadSelectMenu:function(e,t){for(var n='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',a=0;a<e.length;a++){var i=e[a].feeHeadConfiguration.feeHeadId,s=e[a].feeHeadConfiguration.feeHead;n+=t==i?'<option value="'+i+'" selected>'+s+"</option>":'<option value="'+i+'">'+s+"</option>"}return n+="</select>"},loadNewFeeStructureConfigureModal:function(){discountStructure.resetDiscountStructureModal(),$("#discount-structure-config-modal").find(".modal-title").html("Configure New Discount Structure"),$("#submit-fee-structure").html("Configure New Discount Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={},discountStructure.dataCache.configureNewStructure.standard.selectedStandards={},discountStructure.dataCache.configureNewStructure.standard.selectedFees={},discountStructure.dataCache.selectedStructureId=null,discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees),discountStructure.populateStandardSelectDropdown()},resetDiscountStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text"> Select fees to add new discount structure</p>'),$("#discount-structure-name").val(""),$("#discount-structure-name").attr("disabled",!1),$("#discount-structure-config-modal").find(".modal-footer").css("display","")},populateSelectFeesDropdown:function(e,t,n){var a=discountStructure.dataCache.feeMap,i="";for(feeId in a)feeId in n||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+a[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),discountStructure.bindSelectFeesEvent(e,t,n)},populateStandardSelectDropdown:function(){var e=discountStructure.dataCache.standardsMap,t="";for(standardId in e)standardId in discountStructure.dataCache.configureNewStructure.standard.selectedStandards||(t+='<a class="dropdown-item standard-select-option" href="#" id="'+standardId+'-select">'+e[standardId].standardName+"</a>");$(".class-fee-structure").find(".standard-select-dropdown").html(t),discountStructure.bindStandardSelectEvent()},bindSelectFeesEvent:function(e,t,n){$(e).find(".fee-select-option").on("click",function(){var t=$(this).attr("id").split("-select-")[0],a=$(this).attr("id").split("-select-")[1];discountStructure.insertFeeDetailsCard(e,a,t,n)})},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];discountStructure.insertStandardDetailsCard(e)})},insertFeeDetailsCard:function(e,t,n,a){a[n]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=discountStructure.createEntityContainerId(t),s=discountStructure.createFeeContainerId(t,n),o='<div class="fee-structure"> <div class="card card-border text-center" id="'+s+'"> <p class="fee-id" style="display:none;">'+n+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+discountStructure.dataCache.feeMap[n].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col">Percentage</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(o),discountStructure.bindFeeContainerEvents(e,s,t,n,a),discountStructure.populateSelectFeesDropdown(e,t,a)},insertStandardDetailsCard:function(e){discountStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=discountStructure.dataCache.standardsMap[e].standardName.toUpperCase(),n=discountStructure.getStandardContainerId(e),a='<div id="'+n+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+t+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(a),discountStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},discountStructure.populateSelectFeesDropdown("#"+n,e,discountStructure.dataCache.configureNewStructure.standard.selectedFees[e]),discountStructure.bindStandardContainerEvents(n,e),discountStructure.populateStandardSelectDropdown()},createEntityContainerId:function(e){return e+"-fee-structure-container"},createFeeContainerId:function(e,t){return e+"-"+t},bindFeeContainerEvents:function(e,t,n,a,i){$("#"+t).find(".add-fee-head-button").on("click",function(){discountStructure.insertFeeHeadRow(t,a,null,0,"")}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[a],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),discountStructure.populateSelectFeesDropdown(e,n,i)})},populateSelectFeesDropdown:function(e,t,n){var a=discountStructure.dataCache.feeMap,i="";for(feeId in a)feeId in n||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+a[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),discountStructure.bindSelectFeesEvent(e,t,n)},getStandardContainerId:function(e){return e+"-container"},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete discountStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(discountStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;"),discountStructure.populateStandardSelectDropdown()})},insertFeeHeadRow:function(e,t,n,a,i){var s="";if(null==n){s='<tr id=""> <td> '+discountStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"> <span class="slider round"></span> </label> </td><td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var o=discountStructure.dataCache.feeHeadConfigurationResponseList;s='<tr id=""> <td> '+discountStructure.createFeeHeadSelectMenu(o,n)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+a+'"> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"'+(i=i?"checked":"")+' /><span class="slider round"></span> </label> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(s),discountStructure.bindFeeHeadRowEvents(e)},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var n=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&n++}),n>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()}),$("#"+e).find(".fee-head-amount").change(function(){if($(this).parent().parent().find(".is-percentage-switch").is(":checked")){var e=$(this).val();if(e>100||e<=0)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).val("")}else{var t=$(this).val();if(t<=0&&""!=t)return showErrorDialogBox("Amount should be a positive number."),void $(this).val("")}}),$("#"+e).find(".is-percentage-switch").change(function(){if($(this).is(":checked")){var e=$(this).parent().parent().parent().find(".fee-head-amount").val();if((e>100||e<=0)&&""!=e)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).parent().parent().parent().find(".fee-head-amount").val("")}})},submitDiscountStructure:function(){$("#discount-structure-config-modal").modal("toggle");var e=$("#discount-structure-name").val(),t=academicSessionHandler.getSelectedSessionId(),n=null;null!=discountStructure.dataCache.selectedStructureId&&""!=discountStructure.dataCache.selectedStructureId.trim()&&(n=discountStructure.dataCache.selectedStructureId);var a=discountStructure.getFeeStructureData("#institute-fee-structure-container");if(!Array.isArray(a)||a.length){var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:a},s=[];s.push(i);var o=[{structureId:n,structureName:e,discountStructureType:"CUSTOM",metadata:{title:e},entityFeeAssignmentPayloads:s}],d="/fees/add-discount-structure/"+t;null!=n&&(d="/fees/update-discount-structure/"+n+"/"+t),ajaxClient.post(d,{discountAssignmentStructurePayload:JSON.stringify(o)},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),discountStructure.loadDiscountStructureConfigurationPage()})}},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),n=[],a={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in a)){var t=$(this).find(".fee-head-amount").val(),i=$(this).find(".toggle-switch").is(":checked");if(""===t||void 0===t)return alert("Amount cannot be empty"),[];n.push({feeHeadId:e,amount:t,isPercentage:i})}}),n.length>0&&t.push({feeId:e,feeHeadAmountList:n})}),Array.isArray(t)&&!t.length?(alert("Please select atleast one fees to create discount structure"),[]):t},fillFeeDiscountStructureModal:function(e,t){$("#discount-structure-name").attr("disabled",!1),discountStructure.dataCache.selectedStructureId=e;var n=discountStructure.dataCache.discountStructureMap;if(e in n){var a=n[e];discountStructure.resetDiscountStructureModal(),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#discount-structure-name").val(a.feeDiscountMetadata.name),$("#discount-structure-config-modal").find(".modal-title").html("Update Discount Structure"),$("#submit-discount-structure").html("Update Discount Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={},discountStructure.dataCache.configureNewStructure.standard.selectedStandards={},discountStructure.dataCache.configureNewStructure.standard.selectedFees={};for(var i={},s={},o=0;o<a.feeDiscountEntityStructures.length;o++){var d=a.feeDiscountEntityStructures[o];if("INSTITUTE"==d.feeEntity)for(var r=0;r<d.feeIdDiscountStructures.length;r++){i[u=(l=d.feeIdDiscountStructures[r]).feeConfigurationBasicInfo.feeId]=l}else if("CLASS"==d.feeEntity){var c=d.entityId;s[c]={};for(r=0;r<d.feeIdDiscountStructures.length;r++){var l,u=(l=d.feeIdDiscountStructures[r]).feeConfigurationBasicInfo.feeId;s[c][u]=l}}}for(u in i){discountStructure.insertFeeDetailsCard(".institute-structure",discountStructure.INSTITUTE_ENTITY,u,discountStructure.dataCache.configureNewStructure.institute.selectedFees);var f=discountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,u);for(o=0;o<i[u].feeHeadDiscountStructures.length;o++){var m=i[u].feeHeadDiscountStructures[o].feeHeadConfiguration.feeHeadId,g=i[u].feeHeadDiscountStructures[o].amount,p=i[u].feeHeadDiscountStructures[o].percent;discountStructure.insertFeeHeadRow(f,u,m,g,p)}}for(c in s){discountStructure.insertStandardDetailsCard(c);var h=discountStructure.getStandardContainerId(c);for(u in s[c]){discountStructure.insertFeeDetailsCard("#"+h,c,u,discountStructure.dataCache.configureNewStructure.standard.selectedFees[c]);for(f=discountStructure.createFeeContainerId(c,u),o=0;o<s[c][u].feeHeadDiscountStructures.length;o++){m=s[c][u].feeHeadDiscountStructures[o].feeHeadConfiguration.feeHeadId,g=s[c][u].feeHeadDiscountStructures[o].amount,p=i[u].feeHeadDiscountStructures[o].percent;discountStructure.insertFeeHeadRow(f,u,m,g,p)}}}discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees),discountStructure.populateStandardSelectDropdown(),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".toggle-switch").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#discount-structure-config-modal").find(".modal-title").html("View Discount Structure"),$("#discount-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#discount-structure-config-modal").find(".modal-footer").css("display","none"))}else showErrorDialogBox("Invalid Fee Discount Structure.")},populateDeleteStructureModal:function(e){var t=discountStructure.dataCache.discountStructureMap[e];discountStructure.dataCache.discountStructureID=e,$("#delete-fee-structure-modal-text").html("Do you want to delete discount Structure : "+t.feeDiscountMetadata.name+"?")},deleteFeeStructure:function(){$("#delete-fee-structure-modal").modal("toggle");var e=discountStructure.dataCache.discountStructureID,t=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/fees/delete-discount-structure/"+e+"/"+t,{},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),discountStructure.loadDiscountStructureConfigurationPage()})}},graphLoadInterval=100;function getFeesHomePageGraphDetails(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/home-graph/"+e,function(e){$("#home-page-graph-area").html(e),drawHomeCharts()})}function graphReadyToRender(){graphLibraryLoaded=!0}function drawHomeCharts(){displayLoader();drawPieChart(),hideLoader()}function drawPieChart(){if(!graphLibraryLoaded)return clearTimeout(graphLoadTimer),void(typingTimer=setTimeout(function(){drawPieChart()},graphLoadInterval));var e=$("#home-page-stats").text().trim(),t=JSON.parse(e),n=google.visualization.arrayToDataTable([["Amount Type","Value"],["Total Collected Fees",t.collectedAmount],["Total Discount Fees",t.discountAmount],["Total Due Fees",t.dueAmount]]);t.classFeePaymentAggregatedDatas.length>0&&new google.visualization.PieChart(document.getElementById("piechart_3d")).draw(n,{title:"Total Fees Summary",is3D:!0})}var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1;$(document).ready(function(){menuLoader.registerSidebarMenu();var e=new URLSearchParams(window.location.search);if(actionType=e.get(ACTION_TYPE),""===actionType||null===actionType)homePage.initHomePage();else{$(".sidebar-nav").find(".sidebar-item").each(function(){$(this).removeClass("active")}),"payments"===actionType?($("#paymentNav").parent().addClass("active"),loadPaymentPage()):"send-reminders"===actionType?($("#sendReminderNav").parent().addClass("active"),reminders.loadSendReminderPage()):($("#homeNav").parent().addClass("active"),homePage.initHomePage());var t=window.location.href;url=removeURLParameter(t,ACTION_TYPE);let e={id:"100"};window.history.replaceState(e,t,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerFeeConfigurationMenu(),menuLoader.registerFeeStructureConfigurationMenu(),menuLoader.registerDiscountStructureConfigurationMenu(),menuLoader.registerFeeHeadConfigurationMenu(),menuLoader.registerFeeAssignmentMenu(),menuLoader.registerDiscountAssignmentMenu(),menuLoader.registerPaymentMenu(),menuLoader.registerPaymentTransactionsMenu(),menuLoader.registerReportsMenu(),menuLoader.registerSendReminderMenu(),menuLoader.registerNotificationHistoryMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerFeeConfigurationMenu:function(){$("#feeConfigurationNav").on("click",function(){loadFeeConfigurationPage()})},registerFeeHeadConfigurationMenu:function(){$("#feeHeadConfigNav").on("click",function(){loadFeeHeadConfigurationPage()})},registerFeeAssignmentMenu:function(){$("#feeAssignmentNav").on("click",function(){feeAssignment.loadMainScreen()})},registerDiscountAssignmentMenu:function(){$("#discountAssignmentNav").on("click",function(){discountAssignment.loadDiscountAssignmentPage()})},registerPaymentMenu:function(){$("#paymentNav").on("click",function(){loadPaymentPage()})},registerSendReminderMenu:function(){$("#sendReminderNav").on("click",function(){reminders.loadSendReminderPage()})},registerNotificationHistoryMenu:function(){$("#notificationHistoryNav").on("click",function(){reminders.loadNotificationHistoryHomePage()})},registerPaymentTransactionsMenu:function(){$("#transactionsNav").on("click",function(){loadPaymentTransactionsMenu()})},registerReportsMenu:function(){$("#reportsNav").on("click",function(){feeReports.loadReportsMenu()})},registerFeeStructureConfigurationMenu:function(){$("#feeStructureConfigurationNav").on("click",function(){feeStructure.loadFeeStructureConfigurationPageWithoutSession()})},registerDiscountStructureConfigurationMenu:function(){$("#discountStructureConfigurationNav").on("click",function(){discountStructure.loadDiscountStructureConfigurationPageWithoutSession()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession),homePage.displayDashboardContent(),homePage.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip(),homePage.onClickFeeStatsType()},onClickFeeStatsType:function(){$("#fee-stats-type").change(function(){"TODAY"===$(this).val()?homePage.loadHomePageForDate():homePage.loadHomePageForSession()})},onClickFeeStatsSwitchButton:function(){$("#fee-stats-switch-button").on("click",function(){$("#current-card-view-type").text()===TABULAR?($("#current-card-view-type").text(GRAPHICAL),$("#bar-graph-header-text").text("Class Fee Distribution (Graphical View)"),$(this).html("Tabular View"),homePage.loadStudentCountChart(),$("#tabularview-class-fee-distribution").attr("style","display:none;"),$("#chartjs-class-fee-distribution").attr("style","display:block;")):($("#current-card-view-type").text(TABULAR),$("#bar-graph-header-text").text("Class Fee Distribution (Tabular View)"),$(this).html("Graphical View"),homePage.loadBarGraphTabularView(),$("#chartjs-class-fee-distribution").attr("style","display:none;"),$("#tabularview-class-fee-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;"))})},loadHomePage:function(){ajaxClient.get("/fees/home",function(e){$("#main-content").html(e),homePage.initHomePage(),$("#fee-stats-type").val("SESSION")})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/session-home/"+e,function(e){$("#fees-dashboard-session-content").html(e),homePage.displayDashboardContent(),homePage.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip(),$("#fee-stats-type").val("SESSION")})},loadHomePageForDate:function(){var e=academicSessionHandler.getSelectedSessionId(),t=(getDate(new Date).getTime()/1e3).toFixed(0);ajaxClient.get("/fees/date-wise-fee-stats/"+e+"/"+t,function(e){$("#fees-dashboard-session-content").html(e),homePage.displayDashboardContent(),homePage.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip()})},refreshHomePage:function(){homePage.loadHomePageForSession()},loadBarGraphTabularView:function(){var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){for(var t=[],n=[],a=[],i=[],s=[],o=0;o<e.classFeePaymentAggregatedDatas.length;o++){var d=e.classFeePaymentAggregatedDatas[o];t.push(d.standardName),n.push(d.assignedAmount),a.push(d.collectedAmount),i.push(d.discountAmount),s.push(d.dueAmount)}homePage.renderBarGraphTabularView(t,n,a,i,s)}},renderBarGraphTabularView:function(e,t,n,a,i){for(var s='<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table"><thead><tr><th scope="col">Class</th><th scope="col">Total ('+RUPEE_SYMBOL+')</th><th scope="col">Collected ('+RUPEE_SYMBOL+')</th><th scope="col">Discounted ('+RUPEE_SYMBOL+')</th><th scope="col">Due ('+RUPEE_SYMBOL+")</th></tr></thead><tbody>",o=0;o<e.length;o++){s+='<tr><th scope="row">'+e[o]+"</th><td>"+formatINRCurrency(parseFloat(t[o].toFixed(2)))+"</td><td>"+formatINRCurrency(parseFloat(n[o].toFixed(2)))+"</td><td>"+formatINRCurrency(parseFloat(a[o].toFixed(2)))+"</td><td>"+formatINRCurrency(parseFloat(i[o].toFixed(2)))+"</td></tr>"}s+="</tbody></table>",$("#tabularview-class-fee-distribution").html(s)},displayDashboardContent:function(){homePage.loadStudentCountChart();var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){e.assignedAmount;var t=e.collectedAmount/e.assignedAmount*100,n=e.discountAmount/e.assignedAmount*100,a=e.dueAmount/e.assignedAmount*100,i=[t.toFixed(2),n.toFixed(2),a.toFixed(2)];homePage.renderFeeCollectionPieChart(["","",""],i)}},loadStudentCountChart:function(){var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){for(var t=[],n=[],a=[],i=[],s=[],o=0;o<e.classFeePaymentAggregatedDatas.length;o++){var d=e.classFeePaymentAggregatedDatas[o];t.push(d.standardName),n.push(d.assignedAmount),a.push(d.collectedAmount),i.push(d.discountAmount),s.push(d.dueAmount)}homePage.renderStudentCountChart(t,n,a,i,s)}},renderStudentCountChart:function(e,t,n,a,i){new Chart($("#chartjs-class-fee-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:i,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeeCollectionPieChart:function(e,t){new Chart($("#chartjs-fee-collection-pie"),{type:"pie",data:{datasets:[{data:t,backgroundColor:[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,"#108F2B","#C08143 ","#DCDF69","#719E90","#A8B9DF ","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"],borderWidth:5,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cutoutPercentage:70,legend:{display:!1},tooltips:{enabled:!0,mode:"single",displayColors:!1,callbacks:{label:function(e,t){return" "+t.datasets[0].data[e.index]+"% "}}}}})}};function loadPaymentTransactionsMenu(){ajaxClient.get("/fees/all-payment-transactions-home",function(e){$("#main-content").html(e),getPaymentTransactions(!0,"ACTIVE"),feeTransaction.dataCache.status="ACTIVE",academicSessionHandler.bindSessionChangeEvent(loadPaymentTransactionsForSession),bindPaymentTransactionClickEvents(),initDate(1825)})}function getPaymentTransactions(e,t){feeTransaction.dataCache.status=t;var n=$(".page-item.active").find(".page-number").text().trim();e&&(n=1);var a=$("#items-per-page").val();null!=n&&""!=n||(n=1);var i=(n-1)*a,s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/payment-transactions/session/"+s+"/"+t+"/"+i+"/"+a,function(e){$("#feeTransactionsResult").html(e),"CANCELLED"==t&&($("#nav-cancel-transaction-tab").addClass("active"),$("#nav-active-transaction-tab").removeClass("active"),$("#cancel-transaction-content").addClass("show active"),$("#active-transaction-content").removeClass("show active")),bindPaymentTransactionClickEvents(),feeTransaction.initPagination(),$(".datatables-reponsive").DataTable({searching:!0,bPaginate:!0,ordering:!1})})}function loadPaymentTransactionsForSession(){getPaymentTransactions(!0,"ACTIVE")}function bindPaymentTransactionClickEvents(){$(".view-payment-transaction").on("click",function(){showPaymentDetails($(this).attr("id").trim())}),$(".invoice-payment-transaction").on("click",function(){generateFeePaymentInvoice($(this).attr("id").trim())}),$(".print-payment-transaction").on("click",function(){printFeePaymentInvoice($(this).attr("id").trim())}),$(".cancel-payment-transaction").on("click",function(){showCancelPaymentDetails($(this).attr("id").trim())})}function generateFeePaymentInvoice(e){window.open(baseURL+"/fees/invoice/"+e+"/pdf-summary","_blank")}function printFeePaymentInvoice(e){printJS({printable:baseURL+"/fees/invoice/"+e+"/pdf-summary",type:"pdf",showModal:!0})}function showPaymentDetails(e){ajaxClient.get("/fees/payment-transaction-details/"+e,function(e){$("#fee-payment-transaction-detail-modal-container").html(e),$("#confirm-cancel-transaction-button").remove(),$("#fee-payment-transaction-view-modal").modal("toggle")})}function showCancelPaymentDetails(e){ajaxClient.get("/fees/payment-transaction-details/"+e,function(e){$("#fee-payment-transaction-detail-modal-container").html(e),$("#confirm-cancel-transaction-button").attr("style","display:block"),$("#fee-payment-transaction-view-modal").modal("toggle")})}function cancelTransaction(){var e=$("#cancel-transaction-id").text().trim();""!=e?($("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),ajaxClient.post("/fees/cancel-payment-transaction/"+e,{},function(e){$("#fee-payment-status-modal-container").html(e),$("#fee-payment-status-modal").modal({backdrop:"static",keyboard:!1})})):showErrorDialogBox("Invalid Transaction to cancel")}function sendEmails(e){baseURL2=baseURL,"http://127.0.0.1:9123"==baseURL&&(baseURL2="http://127.0.0.1"),ajaxClient.get(baseURL2+":8080/data-server/2.0/fee-payment/due-payment-emails/"+e,function(e){})}function bindDiscountInfoViewEvents(){}function showViewDiscount(){for(var e=$("tbody tr td p").text(),t=JSON.parse(e),n="None",a=0;a<t.assigned_discounts.length;a++)"None"===n?(n="",n+=t.assigned_discounts[a].discountBasicInfo.discountName):n+=", "+t.assigned_discounts[a].discountBasicInfo.discountName;$("#search-student-class-section").html(n)}function bindDiscountViewEvents(){$(".view-discount").on("click",function(){for(var e=$(this).parent().parent().find(".discount-assign").text(),t=JSON.parse(e),n='<table class="table borderless"><thead><tr><th scope="col" class="w-25">Discount Name</th><th scope="col" >Fee Head</th><th scope="col" >Amount</th><th scope="col" >Percentage</th></tr></thead><tbody>',a=0;a<t.assigned_discounts.length;a++){n+='<tr class="row-discount"><td id="discount-name">'+t.assigned_discounts[a].discountBasicInfo.discountName+"</td>";for(var i=!0,s=0;s<t.assigned_discounts[a].discountAmountFeeHeadResponseList.length;s++){var o=t.assigned_discounts[a].discountAmountFeeHeadResponseList[s].feeHeadConfiguration.feeHead,d=t.assigned_discounts[a].discountAmountFeeHeadResponseList[s].amount,r="";1==t.assigned_discounts[a].discountAmountFeeHeadResponseList[s].percentage&&(r="checked"),n+=i?'<td id="fee-head-name">'+o+'</td><td id="amount">'+d+'</td><td id="percentage"><label class="switch"> <input type="checkbox" class="primary toggle-switch" '+r+' disabled ><span class="slider round"></span></label></td></tr>':'<tr class="row-discount"><td></td><td id="fee-head-name">'+o+'</td><td id="amount">'+d+'</td><td id="percentage"><label class="switch"> <input type="checkbox" class="primary toggle-switch" '+r+' disabled ><span class="slider round"></span></label></td></tr>',i=!1}}n+="</tbody></table>",$("#view-popup").html(n),$("#view-discount-modal").modal("toggle")})}function deleteDiscountCallBack(){$(".delete-discount-info").on("click",function(){var e=$(this).parent().find(".discount-info").text().trim(),t=JSON.parse(e);$("#delete\\.discount-id").val(t.discountBasicInfo.discountId),$("#delete\\.discount-name").val(t.discountBasicInfo.discountName),$("#delete-discount-info-modal").modal("toggle")})}function deleteDiscount(){$("#delete-discount-info-modal").modal("toggle");var e=$("#delete\\.discount-id").val();ajaxClient.post("/fees/delete-discount-configuration/"+e,{},function(e){returnToMainScreen(),$("#discount\\.status-modal-container").html(e),$("#discount\\.status-modal").modal("toggle"),getAllDiscounts()})}function populateViewFeeAssignmentContent(e){$("#view\\.assigned-fee-name").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeName),$("#view\\.assigned-fee-academic-session").val(e.feeConfigurationResponse.academicSession.displayName),$("#view\\.assigned-fee-description").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.description),"ONE_TIME"!=e.feeConfigurationResponse.feeConfigurationBasicInfo.feeType?$("#view\\.assigned-fee-duration").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month+" to "+e.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month):$("#assigned-fee-duration-container").attr("style","display:none");var t="",n=0,a=0;for(n=0;n<e.feeHeadAmountDetailsList.length;n++)a+=e.feeHeadAmountDetailsList[n].amount,t=t+"<tr> <td>"+e.feeHeadAmountDetailsList[n].feeHeadConfiguration.feeHead+"</td> <td>"+e.feeHeadAmountDetailsList[n].amount+'</td><td><span class="badge badge-logo-primary">'+e.feeHeadAmountDetailsList[n].feeEntity+"</span></td></tr>";t=t+"<tr> <td> <strong> TOTAL </strong></td> <td>"+a+"</td> <td></td></tr>",$("#assigned-fee-head-amounts").html(t),$("#view-assigned-fee-modal").modal("toggle")}function studentLiveSearchEvent(e,t,n,a,i){ajaxClient.get("/fees/student-live-search/"+e+"?searchText="+t+"&status="+i,function(e){$(n).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(n,a)})}var feeTransaction={dataCache:{},initPagination:function(){pagination.bindEvents(function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!0,feeTransaction.dataCache.status)})},downloadBulkInvoice:function(){if(!validateMandatoryFields($("#download-bulk-invoice-modal"))){var e=$(".academic-session option:selected").val(),t=getDate($("#download-bulk-invoice-modal").find("input.start-date").val()).getTime()/1e3,n=getDate($("#download-bulk-invoice-modal").find("input.end-date").val()).getTime()/1e3,a=$("#download-bulk-invoice-modal").find("#fee-transaction-status").val(),i=(n-t)/86400;i<0||i>=65?alert("Total number of days cannot be greater than 65. Please reduce the range!"):($("#download-bulk-invoice-modal").modal("toggle"),window.open(baseURL+"/fees/bulk-invoice-pdf?academic_session_id="+e+"&start_date="+t+"&end_date="+n+"&fee_transaction_status="+a,"_blank"))}}};function formatINRCurrency(e){return null==e||null==e||""==e?0:e=e.toLocaleString("en-IN")}var pastFeePaymentTransactionDays=365,lastCollectedAmount=0,lastInstantDiscountAmount=0,lastTotalFineAmount=0,lastFeeLevelPaidFineAmount=0;function loadPaymentPage(){ajaxClient.get("/fees/payment",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(feePayment.changeSession),bindPaymentSearchStudentEvent()})}var feePayment={dataCache:{},initCache:function(){var e=readJson("#student-fee-payment-details-json");feePayment.dataCache.studentFeePaymentDetails=e;var t={},n={},a={},i=e.selected_session.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;t[i]=e.selected_session.due_fee_list,n[i]=e.selected_session.student.studentAcademicSessionInfoResponse.academicSession;for(var s=0;s<e.selected_session.due_fee_list.length;s++)a[e.selected_session.due_fee_list[s].feeConfigurationBasicInfo.feeId]=e.selected_session.due_fee_list[s];for(s=0;s<e.other_sessions.length;s++){var o=e.other_sessions[s],d=o.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;t[d]=o.due_fee_list,n[d]=o.student.studentAcademicSessionInfoResponse.academicSession;for(var r=0;r<o.due_fee_list.length;r++)a[o.due_fee_list[r].feeConfigurationBasicInfo.feeId]=o.due_fee_list[r]}feePayment.dataCache.sessionDueFees=t,feePayment.dataCache.sessionData=n,feePayment.dataCache.dueFeeDetailsMap=a},doneStudentSearchTyping:function(e){var t=$("#student-payment-search-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentPaymentDetails,"ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO")},changeSession:function(){$("#student-payment-search-text").val(""),$("#student-payment-screen").html(""),feePayment.dataCache={}},showFeeHeadPaymentDetails:function(e){$(e).hasClass("closed")?($(e).find(".arrow").attr("style","display:none"),$(e).find(".arrow.up").attr("style","display:block"),$(e).removeClass("closed"),$(e).addClass("open")):($(e).find(".arrow").attr("style","display:none"),$(e).find(".arrow.down").attr("style","display:block"),$(e).removeClass("open"),$(e).addClass("closed"))},populateCollectFeesScreen:function(e){var t=feePayment.dataCache.sessionDueFees[e],n=feePayment.dataCache.sessionData[e];$("#collect-fee-session-display").html(n.displayName),$("#collect-fee-session-id").html(e);for(var a="",i=0;i<t.length;i++)a+=' <tr class="collect-fee-row" id="'+t[i].feeConfigurationBasicInfo.feeId+'"> <td>   <div class="form-check">  <input type="checkbox" class="form-check-input collect-fee-enable" >  <label class="form-check-label"> '+t[i].feeConfigurationBasicInfo.feeName+" </label>  </div> </td> </tr>";$("#due-fee-list-container").html(a),$(".select-all-fee-enable").prop("checked",!1),$("#selected-fees-end").siblings().remove(),feePayment.bindFeesForPaymentCheckboxes(),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},collectFeesDisplay:function(e){$(".payment-main-screen-info").attr("style","display:none"),$(".fee-payment-tab").attr("style","display:none"),feePayment.populateCollectFeesScreen(e),$("#collect-fee-tab").attr("style","display:block")},bindFeesForPaymentCheckboxes:function(){$(".select-all-fee-enable").change(function(){$(this).is(":checked")?($(".collect-fee-enable").prop("checked",!0),$(".collect-fee-enable").trigger("change")):($(".collect-fee-enable").prop("checked",!1),$(".collect-fee-enable").trigger("change"))}),$(".collect-fee-row").on("click",function(){$(this).find(".collect-fee-enable").is(":checked")?($(this).find(".collect-fee-enable").prop("checked",!1),$(this).find(".collect-fee-enable").trigger("change")):($(this).find(".collect-fee-enable").prop("checked",!0),$(this).find(".collect-fee-enable").trigger("change"))}),$(".collect-fee-enable").on("click",function(){$(this).is(":checked")?($(this).prop("checked",!1),$(this).trigger("change")):($(this).prop("checked",!0),$(this).trigger("change"))}),$(".collect-fee-enable").change(function(){$(this).parent().find("label").text();var e=$(this).closest("tr").attr("id").trim(),t=feePayment.dataCache.dueFeeDetailsMap[e];if($(this).is(":checked"))0==$("#fee-card-"+e).length&&feePayment.insertFeesDetails(t);else{$(".select-all-fee-enable").prop("checked",!1);var n=feePayment.computeVariousTotalFeesAmounts();$("#fee-card-"+e).length>0&&$("#fee-card-"+e).remove(),feePayment.computeFeesPaymentAmounts(),feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay();var a=feePayment.computeVariousTotalFeesAmounts(),i=a.totalCollectedAmount+a.totalFineAmount,s=n.totalCollectedAmount+n.totalFineAmount;if(i<s){var o=s-i,d=feePayment.getPaidAmountValue();if(o<=d)$("#student-paid-amount").val(d-o);else{if(!feePayment.isWalletUsed())return void showErrorDialogBox("Invalid state. Fee amount was not included in paid amount");$("#student-paid-amount").val(0);var r=getUsedWalletAmount()-(o-d);if(r<0)return void showErrorDialogBox("Invalid state. Fee amount was not included in paid amount of wallet");$("#student-used-wallet-amount").val(r)}}}}),$("#student-used-wallet-amount").focus(function(){}).blur(function(){var e=feePayment.getStudentWalletAmount(),t=feePayment.getUsedWalletAmountValue();t>e&&(showErrorDialogBox("Used wallet amount "+t+" cannot be greater then student available wallet amount "+e+". Decreasing used wallet amount"),t=e,$("#student-used-wallet-amount").val(t));var n=feePayment.getPaidAmountValue(),a=t+n,i=feePayment.computeVariousTotalFeesAmounts(),s=i.totalCollectableAmount,o=i.totalFineAmount;a>s?(showErrorDialogBox("Total paid fee amount "+a+" cannot be greater then payble amount "+s+". Reducing the wallet amount."),t=s-n,$("#student-used-wallet-amount").val(t)):a<o&&(showErrorDialogBox("Total paid fee amount "+a+" cannot be less then fine payment amount "+o+". Increasing the wallet amount."),t=o-n,$("#student-used-wallet-amount").val(t)),feePayment.fillCollectedAmounts(a-o)}),$("#student-paid-amount").focus(function(){}).blur(function(){var e=feePayment.getPaidAmountValue(),t=getUsedWalletAmount(),n=t+e,a=feePayment.computeVariousTotalFeesAmounts(),i=a.totalCollectableAmount,s=a.totalFineAmount;n>i?(showErrorDialogBox("Total paid fee amount "+n+" cannot be greater then payble amount "+i+". Reducing the paid amount."),e=i-t,$("#student-paid-amount").val(e)):n<s&&(showErrorDialogBox("Total paid fee amount "+n+" cannot be less then fine payment amount "+s+". Increasing the paid amount."),e=s-t,$("#student-paid-amount").val(e)),feePayment.fillCollectedAmounts(n-s)}),$("#student-total-instant-discount-amount").focus(function(){}).blur(function(){var e=feePayment.computeVariousTotalFeesAmounts(),t=e.totalBalanceAmount-e.totalCollectedAmount,n=feePayment.getTotalInstantDiscountAmountValue();n>t&&(showErrorDialogBox("Discounted amount "+n+" cannot be greater then max discount amount "+t+". Reducing the discounted amount."),n=t),$("#student-total-instant-discount-amount").val(n),feePayment.fillInstantDiscountAmounts(n)}),$("#student-total-fine-collected-amount").focus(function(){lastTotalFineAmount=feePayment.getTotalFineAmountValue()}).blur(function(){var e=feePayment.getTotalFineAmountValue();feePayment.fillFineAmounts(e),feePayment.computeFineAmount(),feePayment.updatePaidAmountDelta(lastTotalFineAmount,e),feePayment.fillNetAmountDisplay()})},updatePaidAmountDelta:function(e,t){if(e<=t){var n=t-e,a=feePayment.getPaidAmountValue();$("#student-paid-amount").val(a+n)}else{var i=e-t;if(i<=(a=feePayment.getPaidAmountValue()))$("#student-paid-amount").val(a-i);else{if(!feePayment.isWalletUsed())return void showErrorDialogBox("Invalid state. Changed amount was not included in paid amount");$("#student-paid-amount").val(0);var s=getUsedWalletAmount()-(i-a);if(s<0)return void showErrorDialogBox("Invalid state. Changed amount was not included in paid amount of wallet");$("#student-used-wallet-amount").val(s)}}},isWalletUsed:function(){return $("#use-wallet-amount").is(":checked")},getStudentWalletAmount:function(){return parseFloat($("#student-wallet-amount-value").text().trim())},getUsedWalletAmountValue:function(){var e=$("#student-used-wallet-amount").val();return""!=e?parseFloat(e):0},insertFeesDetails:function(e){for(var t,n,a="",i=e.feeHeadPaymentDetails,s=e.totalDueFineAmount,o=e.totalPaidFineAmount,d=0;d<i.length;d++){var r=i[d].feeHeadConfiguration.feeHeadId,c=i[d].feeHeadConfiguration.feeHead,l=i[d].assignedAmount,u=i[d].paidAmount,f=i[d].instantDiscountAmount,m=i[d].assignedDiscountAmount;a+=feeHeadPaymentDetailRow(r,c,l,u,i[d].balanceAmount,f+m)}a+='<tr class="total-feehead-payment-row"> <td> <strong> <span>TOTAL</span> </strong> </td> <td class="total-feeHead-assigned-amount"><strong> <span>0</span></td> <td class="total-feeHead-assigned-discount"><strong> <span>0</span></td> <td class="total-feeHead-paid-amount"><strong> <span>0</span></td> <td class="total-feeHead-payment-balance-amount"><strong> <span>0</span></td> <td class="total-feeHead-collected-amount"><strong> <span>0</span></td> <td class="total-feeHead-instant-discount-amount"><strong> <span>0</span></td> <td class="total-feeHead-payment-due-amount"><strong> <span>0</span></td> </tr>';var g="fee-card-"+(n=e.feeConfigurationBasicInfo.feeId);t=generateFeesPaymentCard(g,n,e.feeConfigurationBasicInfo.feeName+" ("+getFeeTypeDisplayName(e.feeConfigurationBasicInfo.feeType)+")",a,'<div class="table-responsive"> <table class="table table-hover fine-table"> <thead> <tr> <th scope="col" class="w-25"></th> <th scope="col" >Computed Fine</th> <th scope="col" >Collected Fine</th>  <th scope="col" >Paid Fine</th> </tr> </thead> <tbody> <tr class=""> <td> <strong>FINE</strong> </td> <td><input type="text" class="form-control form-control-sm computed-fine-amount" value="'+s+'" readonly></td> <td><input type="text" class="form-control form-control-sm collected-fine-amount" value="'+o+'" readonly></td>  <td><input type="text" class="form-control form-control-sm paid-fine-amount" value="0"></td>  </tbody> </table> </div>'),$("#selected-fees-end").before(t),feePayment.computeFeesPaymentAmounts(),feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay(),feePayment.bindPaymentDueComputation($("#"+g))},getFloatValue:function(e){return null==e||null==e?0:""==e?0:parseFloat(e)},bindPaymentDueComputation:function(e){$(e).find(".collected-amount").focus(function(){lastCollectedAmount=feePayment.getFloatValue($(this).val())}).blur(function(){feePayment.getFloatValue($(this).val())>=0&&feePayment.computePaymentDueAmount($(this).parent().parent())?(feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay(),feePayment.updatePaidAmountDelta(lastCollectedAmount,feePayment.getFloatValue($(this).val()))):$(this).val(lastCollectedAmount)}),$(e).find(".instant-discount-amount").focus(function(){lastInstantDiscountAmount=$(this).val()}).blur(function(){feePayment.getFloatValue($(this).val())>=0&&feePayment.computePaymentDueAmount($(this).parent().parent())?(feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()):$(this).val(lastInstantDiscountAmount)}),$(e).find(".paid-fine-amount").focus(function(){lastFeeLevelPaidFineAmount=feePayment.getFloatValue($(this).val())}).blur(function(){feePayment.getFloatValue($(this).val())<0?$(this).val(lastFeeLevelPaidFineAmount):(feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay(),feePayment.updatePaidAmountDelta(lastFeeLevelPaidFineAmount,feePayment.getFloatValue($(this).val())))})},computeFineAmount:function(){var e=0,t=0,n=0;$("div.collected-fees-details").each(function(){e+=parseFloat($(this).find(".fine-table").find(".computed-fine-amount").val()),t+=parseFloat($(this).find(".fine-table").find(".collected-fine-amount").val()),n+=parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val())}),$("#net-total-fine-computed-amount").find("span").first().text(e),$("#net-total-fine-collected-amount").find("span").first().text(t),$("#net-total-fine-paid-amount").find("span").first().text(n)},getPaidAmountValue:function(){var e=$("#student-paid-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},getTotalInstantDiscountAmountValue:function(){var e=$("#student-total-instant-discount-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},getTotalFineAmountValue:function(){var e=$("#student-total-fine-collected-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},fillCollectedAmounts:function(e){feePayment.clearCollectedAmounts(),$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){if(!(e<=0)){feePayment.computePaymentDueAmount($(this));var t=$(this).find("input.payment-due-amount").val(),n=0;""!=t&&(n=parseFloat(t));var a=0;e>=n?(a=n,e-=n):(a=e,e=0),$(this).find("input.collected-amount").first().val(a),feePayment.computePaymentDueAmount($(this))}})}),e>0&&showErrorDialogBox(e+" Rs. amount is more than the assigned fees. Please select more fees or reduce the amount."),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},fillInstantDiscountAmounts:function(e){feePayment.clearDiscountedAmounts(),$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){if(!(e<=0)){feePayment.computePaymentDueAmount($(this));var t=$(this).find("input.payment-due-amount").val(),n=0;""!=t&&(n=parseFloat(t));var a=0;e>=n?(a=n,e-=n):(a=e,e=0),$(this).find("input.instant-discount-amount").first().val(a),feePayment.computePaymentDueAmount($(this))}})}),e>0&&showErrorDialogBox(e+" Rs. amount is more than the max applicable discount. Please select more fees or reduce the amount."),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},fillFineAmounts:function(e){feePayment.clearFineAmounts();var t=(new Date).getTime()/1e3;$("div.collected-fees-details").each(function(){var n=$(this).find(".collected-fee-id").text().trim(),a=feePayment.dataCache.dueFeeDetailsMap[n];if(!(null==a.feeConfigurationBasicInfo.dueDate||a.feeConfigurationBasicInfo.dueDate<=0||a.feeConfigurationBasicInfo.dueDate>t)){var i=$(this).find(".fine-table").find(".computed-fine-amount").val(),s=0;""!=i&&(s=parseFloat(i));var o=$(this).find(".fine-table").find(".collected-fine-amount").val(),d=0;if(""!=o&&(d=parseFloat(o)),!(s<=0)){var r=s-d;e<r?($(this).find(".fine-table").find(".paid-fine-amount").val(e),e=0):($(this).find(".fine-table").find(".paid-fine-amount").val(r),e-=r)}}}),e<=0||($("div.collected-fees-details").each(function(){var n=$(this).find(".collected-fee-id").text().trim(),a=feePayment.dataCache.dueFeeDetailsMap[n];if(!(null==a.feeConfigurationBasicInfo.dueDate||a.feeConfigurationBasicInfo.dueDate<=0||a.feeConfigurationBasicInfo.dueDate>t)){var i=$(this).find(".fine-table").find(".computed-fine-amount").val(),s=0;return""!=i&&(s=parseFloat(i)),s<=0?($(this).find(".fine-table").find(".paid-fine-amount").val(e),void(e=0)):void 0}}),e<=0||$("div.collected-fees-details").each(function(){var n=$(this).find(".collected-fee-id").text().trim(),a=feePayment.dataCache.dueFeeDetailsMap[n];if(null==a.feeConfigurationBasicInfo.dueDate||a.feeConfigurationBasicInfo.dueDate<=0||a.feeConfigurationBasicInfo.dueDate>t)return $(this).find(".fine-table").find(".paid-fine-amount").val(e),void(e=0)}))},clearCollectedAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){$(this).find("input.collected-amount").val(0)})})},clearDiscountedAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){$(this).find("input.instant-discount-amount").val(0)})})},clearFineAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find(".fine-table").find(".paid-fine-amount").val(0)})},computePaymentDueAmount:function(e){var t=$(e).find(".collected-amount").first().val(),n=0;t&&(n=parseFloat(t));var a=$(e).find(".instant-discount-amount").first().val(),i=0;a&&(i=parseFloat(a));var s=$(e).find(".payment-balance-amount").first().val(),o=0;s&&(o=parseFloat(s));var d=n+i,r=o-d;return r<0?(showErrorDialogBox("Collected amount = "+d+" is more than the Payable amount = "+o+". Please review the collected and instant discount amounts."),!1):($(e).find(".payment-due-amount").first().val(r.toFixed(0)),!0)},computeFeesPaymentAmounts:function(){var e=0,t=0,n=0,a=0,i=0,s=0,o=0;$("div.collected-fees-details").each(function(){var d=0,r=0,c=0,l=0,u=0,f=0,m=0;$(this).find("tr.feehead-payment-row").each(function(){feePayment.computePaymentDueAmount($(this)),d+=parseFloat($(this).find("input.assigned-amount").val()),r+=parseFloat($(this).find("input.assigned-discount").val()),c+=parseFloat($(this).find("input.paid-amount").val()),l+=parseFloat($(this).find("input.payment-balance-amount").val()),u+=parseFloat($(this).find("input.collected-amount").val()),f+=parseFloat($(this).find("input.instant-discount-amount").val()),m+=parseFloat($(this).find("input.payment-due-amount").val())}),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-assigned-amount").find("span").first().text(d),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-assigned-discount").find("span").first().text(r),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-paid-amount").find("span").first().text(c),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-payment-balance-amount").find("span").first().text(l),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-collected-amount").find("span").first().text(u),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-instant-discount-amount").find("span").first().text(f),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-payment-due-amount").find("span").first().text(m),e+=d,t+=r,n+=c,a+=l,i+=u,s+=f,o+=m}),$("#net-total-fees-assigned-amount").find("span").first().text(e),$("#net-total-fees-assigned-discount").find("span").first().text(t),$("#net-total-fees-paid-amount").find("span").first().text(n),$("#net-total-fees-payment-balance-amount").find("span").first().text(a),$("#net-total-fees-collected-amount").find("span").first().text(i),$("#net-total-fees-instant-discount-amount").find("span").first().text(s),$("#net-total-fees-payment-due-amount").find("span").first().text(o)},fillNetAmountDisplay:function(){var e=feePayment.computeVariousTotalFeesAmounts(),t=e.totalBalanceAmount,n=e.totalInstantDiscountAmount,a=e.totalFineAmount,i=t+a-n;$("#student-total-balance-amount-display").val(t),$("#student-total-fine-collected-amount").val(a),$("#student-total-instant-discount-amount").val(n),$("#student-net-payble-amount-display").val(i)},computeVariousTotalFeesAmounts:function(){var e=0,t=0,n=0,a=0;return $("div.collected-fees-details").each(function(){var i=$(this).find(".fine-table").find(".paid-fine-amount").val();""!=i&&(a+=parseFloat(i)),$(this).find("tr.feehead-payment-row").each(function(){var a=$(this).find("input.payment-balance-amount").val();""!=a&&(e+=parseFloat(a));var i=$(this).find("input.instant-discount-amount").val();""!=i&&(t+=parseFloat(i));var s=$(this).find("input.collected-amount").val();""!=s&&(n+=parseFloat(s))})}),{totalBalanceAmount:e,totalInstantDiscountAmount:t,totalCollectedAmount:n,totalFineAmount:a,totalCollectableAmount:e+a-t}}};function useWalletAmountCallBack(){$("#use-wallet-amount").change(function(){if($(this).is(":checked")){if(parseFloat($("#student-wallet-amount-value").text().trim())<=0)return showErrorDialogBox("You donot have required credits in your wallet. Please recharge it to use wallet amount."),void $(this).prop("checked",!1);$("#student-used-wallet-amount").val(0),$("#wallet-amount-input-container").attr("style","display:block")}else{var e=getUsedWalletAmount();$("#student-used-wallet-amount").val(0),$("#student-paid-amount").val(feePayment.getPaidAmountValue()+e),$("#wallet-amount-input-container").attr("style","display:none")}})}function bindPaymentSearchStudentEvent(){var e="#student-payment-search-result";$("#student-payment-search").on("click",function(){feePayment.doneStudentSearchTyping(e)}),$("#student-payment-search-text").on("keyup",function(t){13==t.keyCode&&feePayment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#student-payment-search-text",e,feePayment.doneStudentSearchTyping)}function confirmCancelTransaction(e,t){$("#fee-payment-transaction-view-modal").modal("toggle"),$("#cancel-transaction-student-id").text(t),$("#cancel-transaction-id").text(e),$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle")}function reviewCancelTransaction(){$("#cancel-transaction-student-id").text(""),$("#cancel-transaction-id").text(""),$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),$("#fee-payment-transaction-view-modal").modal("toggle")}function cancelStudentTransaction(){var e=$("#cancel-transaction-student-id").text().trim(),t=$("#cancel-transaction-id").text().trim();""!=t?($("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),ajaxClient.post("/fees/cancel-payment-transaction/"+t,{},function(t){returnToPaymentMainScreenDisplay(),$("#fee-payment-status-modal-container").html(t),$("#fee-payment-status-modal").modal("toggle"),loadStudentPaymentDetails(e)})):showErrorDialogBox("Invalid Transaction to cancel")}function bindPaymentActionEvents(){$("#past-payment-button").on("click",function(){pastTransactionsDisplay()})}function pastTransactionsDisplay(){$(".payment-main-screen-info").attr("style","display:none"),$(".fee-payment-tab").attr("style","display:none"),$("#fee-transactions-tab").attr("style","display:block")}function returnToPaymentMainScreenDisplay(){$(".payment-main-screen-info").attr("style","display:block"),$(".fee-payment-tab").attr("style","display:none"),$("#fee-payment-view-tab").attr("style","display:block")}function generateFeesPaymentCard(e,t,n,a,i){return'<div class="card card-border collected-fees-details" id="'+e+'"> <p style="display:none" class="collected-fee-id">'+t+'</p>  <div class="card-header" id="fee-heading-'+t+'"> <h5 class="mb-0"> <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-fee-'+t+'" aria-expanded="true" aria-controls="collapse-fee-'+t+'"> <strong>'+n.toUpperCase()+' </strong> </button> </h5> </div> <div id="collapse-fee-'+t+'" class="collapse show" aria-labelledby="fee-heading-'+t+'" data-parent="#selected-fees-payment-list"> <div class="card-body"> <div class="table-responsive"> <table class="table table-hover"> <thead> <tr> <th scope="col" class="w-25">Fee Head</th> \x3c!-- <th scope="col" >Fee Head</th> --\x3e <th scope="col" >Assigned Amount</th> <th scope="col" >Given Discount</th> <th scope="col" >Collected Amount</th> <th scope="col" >Payable Amount</th> <th scope="col" >Amount Paid</th> <th scope="col" >Instant Discount</th> <th scope="col" >Due Amount</th> </tr> </thead> <tbody> '+a+" </tbody> </table> </div> "+i+" </div> </div> </div>"}function feeHeadPaymentDetailRow(e,t,n,a,i,s){return'<tr class="feehead-payment-row"> <td class="payment-feehead-id" style="display:none" >'+e+"</td> <td>"+t+'</td> <td><input type="text" class="form-control form-control-sm assigned-amount" value="'+n+'" readonly></td> <td><input type="text" class="form-control form-control-sm assigned-discount" value="'+s+'" readonly></td> <td><input type="text" class="form-control form-control-sm paid-amount" value="'+a+'" readonly></td> <td><input type="text" class="form-control form-control-sm payment-balance-amount" value="'+i+'" readonly></td> <td><input type="number" class="form-control form-control-sm collected-amount" value="0" ></td> <td><input type="number" class="form-control form-control-sm instant-discount-amount" value="0" ></td> <td><input type="text" class="form-control form-control-sm payment-due-amount" value="'+i+'" readonly></td> </tr>'}function getUsedWalletAmount(){var e=parseFloat($("#student-wallet-amount-value").text().trim()),t=0;return $("#use-wallet-amount").is(":checked")&&((t=parseFloat($("#student-used-wallet-amount").val()))<=0&&(t=0),t>e&&(t=e)),t}function getFeeTypeDisplayName(e){var t="";return"REGULAR"==e?t="Regular Fees":"SPECIAL"==e?t="Special Fees":"ONE_TIME"==e&&(t="One Time Fees"),t}function loadStudentPaymentDetails(e){loadStudentPaymentDetails(e,!1)}function loadStudentPaymentDetails(e,t){var n=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-payment-details/"+n+"/"+e,function(e){$("#student-payment-screen").html(e),feePayment.initCache(),bindPaymentActionEvents(),bindPaymentTransactionClickEvents(),bindCollectFeesPaymentEvent(),initDate(pastFeePaymentTransactionDays),bindViewPaymentFeesEvent(),useWalletAmountCallBack(),feePayment.fillNetAmountDisplay(),setOtherSessionTotalDueAmount(),t&&feePayment.collectFeesDisplay(n)})}function bindCollectFeesPaymentEvent(){$("#fee-payment-confirm").on("click",function(){$("#fee-payment-confirm-modal").modal("toggle");var e=collectFeesInformation();null!=e&&ajaxClient.post("/fees/collect-fee-payment",{feePaymentPayload:JSON.stringify(e)},function(t){returnToPaymentMainScreenDisplay(),$("#fee-payment-status-modal-container").html(t),$("#collect-fee-payment-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentPaymentDetails(e.feePaymentTransactionMetaData.studentId)})})}function collectFeesInformation(){var e=$("#collect-fee-session-id").text().trim(),t=$("#collect-fee-student-id").text().trim(),n=$("#payment-mode-input").find(":selected").val().trim(),a=$("#payment-reference").val().trim(),i=getDate($("#payment-date").val()),s=$("#payment-remark").val();if(validateMandatoryFields($("#collect-fee-tab")))showErrorDialogBox("Please fill required fields before making payment.");else if(!validateAmounts()){if(null==i)return showErrorDialogBox("Please enter the date of payment"),null;var o=new Date;if(i.getTime()>o.getTime())return showErrorDialogBox("Invalid payment date. Future transactions not allowd"),null;$("div.collected-fees-details").each(function(){});var d=[];$("div.collected-fees-details").each(function(){var e=$(this).find("p.collected-fee-id").text().trim(),t=parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val()),n=[];if($(this).find("tr.feehead-payment-row").each(function(){var e={feeHeadId:$(this).find("td.payment-feehead-id").text().trim(),paidAmount:$(this).find("input.collected-amount").val(),instantDiscount:$(this).find("input.instant-discount-amount").val()};n.push(e)}),n.length>0)for(var a=t/n.length,i=0;i<n.length;i++)n[i].fineAmount=a;var s={feeId:e,feeHeadTransactionAmounts:n};d.push(s)});var r=getUsedWalletAmount();return{feePaymentTransactionMetaData:{studentId:t,transactionMode:n,transactionReference:a,transactionDate:i.getTime()/1e3,academicSessionId:e,debitWalletAmount:r,remark:s},feeIdFeeHeadTransactionList:d}}}function validateAmounts(){var e=parseFloat($("#net-total-fees-assigned-amount").text()),t=parseFloat($("#net-total-fees-collected-amount").text());parseFloat($("#net-total-fees-instant-discount-amount").text());return""==e||0==e?(showErrorDialogBox("No amount added to collect fees."),!0):(""==t||0==t)&&!confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")}function bindViewPaymentFeesEvent(){$(".view-payment-fee-info").on("click",function(){var e=$(this).parent().parent().find("p.payment-fee-info").text().trim();populateViewFeePaymentContent(JSON.parse(e))})}function setOtherSessionTotalDueAmount(){$(".totalDueAmount").each(function(){var e=0,t=0;$(this).parent().parent().parent().parent().find(".payment-fee-info").each(function(){var n=$(this).text().trim(),a=JSON.parse(n);for(e=0;e<a.feeHeadPaymentDetails.length;e++){a.feeHeadPaymentDetails[e].feeHeadConfiguration.feeHead;var i=a.feeHeadPaymentDetails[e].assignedAmount,s=a.feeHeadPaymentDetails[e].assignedDiscountAmount,o=a.feeHeadPaymentDetails[e].instantDiscountAmount,d=a.feeHeadPaymentDetails[e].paidAmount,r=a.feeHeadPaymentDetails[e].balanceAmount;i,s+o,d,t+=r}}),$(this).text(t.toFixed(2)+"/-"),$(this).css("font-weight","Bold")})}function populateViewFeePaymentContent(e){$("#view-payment-fee-name").text(e.feeConfigurationBasicInfo.feeName);var t="",n=0,a=0,i=0,s=0,o=0;for(n=0;n<e.feeHeadPaymentDetails.length;n++){var d=e.feeHeadPaymentDetails[n].feeHeadConfiguration.feeHead,r=e.feeHeadPaymentDetails[n].assignedAmount,c=e.feeHeadPaymentDetails[n].assignedDiscountAmount,l=e.feeHeadPaymentDetails[n].instantDiscountAmount,u=e.feeHeadPaymentDetails[n].paidAmount,f=e.feeHeadPaymentDetails[n].balanceAmount,m=c+l;a+=r,i+=m,s+=u,o+=f,t=t+"<tr> <td>"+d+'</td> <td><input type="text" class="form-control form-control-sm" value="'+r+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+m+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+u+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+f+'" readonly></td> </tr>'}t=t+"<tr> <td> <strong> TOTAL </strong></td> <td>"+a.toFixed(2)+"</td> <td>"+i.toFixed(2)+"</td> <td>"+s.toFixed(2)+"</td> <td>"+o.toFixed(2)+"</td> </tr>",$("#view-payment-feehead-details").html(t),$("#fee-payment-details-view-modal").modal("toggle")}var reminders={dataCache:{},loadSendReminderPage:function(){ajaxClient.get("/fees/due-fees-reminder-screen",function(e){$("#main-content").html(e),initDateWithYearRange("-10:+10",!0),channelCreditsHandler.initDataCache(),channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits"),reminders.bindSelectClassCheckboxEvent(),reminders.dataCache.sendReminders={},reminders.dataCache.selectedStudentCount=0,reminders.dataCache.templateLoaded=!1,initSelect2(),academicSessionHandler.bindSessionChangeEvent(reminders.loadBulkStudentScreen)})},loadBulkStudentScreen:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/load-bulk-student-screen/"+e,function(e){$("#fee-reminder-screen").html(e),initDateWithYearRange("-10:+10",!0),channelCreditsHandler.initDataCache(),channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits"),reminders.bindSelectClassCheckboxEvent(),reminders.dataCache.sendReminders={},reminders.dataCache.selectedStudentCount=0,reminders.dataCache.templateLoaded=!1,initSelect2()})},loadDueFeeStudentsForInstitute:function(){var e=academicSessionHandler.getSelectedSessionId(),t=getDate($("#reminder-fee-due-date").val());if(null!=t){var n=!1;$("#include-fee-fine").is(":checked")&&(n=!0);var a=$(".select-reminder-class").val();a=null!=a&&null!=a?a.join(","):"",ajaxClient.get("/fees/due-fees-students/"+e+"/"+t.getTime()/1e3+"/"+n+"?requiredStandards="+a,function(e){$("#due-fees-student-list").html(e),reminders.dataCache.sendReminders.dueFeesDate=t.getTime()/1e3,reminders.dataCache.sendReminders.requiredStandards=a,reminders.dataCache.selectedStudentCount=0})}else showErrorDialogBox("Please fill due date.")},selectBulkStudentList:function(e){if(e.checked){$(".bulk-student-select-checkbox").prop("checked",!0);var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),reminders.dataCache.selectedStudentCount=t}else $(".bulk-student-select-checkbox").prop("checked",!1),$("#bulk-students-selected-count").html(0),reminders.dataCache.selectedStudentCount=0},bindSelectClassCheckboxEvent:function(){$("#reminder-select-all-class").change(function(){$(this).is(":checked")?$(".select-reminder-student-class-menu").attr("style","display:none"):$(".select-reminder-student-class-menu").attr("style","display:block")})},getRequiredStandards:function(){var e=[];return $("#reminder-select-all-class").is(":checked")?e:($(".select-reminder-class").each(function(){$(this).prop("checked")&&e.push($(this).attr("id"))}),e)},getRequiredStandardsCSV:function(){for(var e="",t=reminders.getRequiredStandards(),n=0;n<t.length;n++)e=0==n?t[n]:e+","+t[n];return e},bulkStudentSelectCheckbox:function(e){var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),reminders.dataCache.selectedStudentCount=t},bulkReminderConfirmModal:function(){var e=$("input.bulk-student-select-checkbox:checkbox:checked").length;if(0!=e){reminders.dataCache.selectedStudentCount=e;var t=$("#include-fee-fine").is(":checked");reminders.dataCache.templateLoaded?(reminders.updateReminderModalDisplay(t),$("#bulk-reminder-confirmation-modal").modal({backdrop:"static",keyboard:!1}),$(".selected-student-count").text(e)):ajaxClient.get("/fees/reminder-templates",function(n){$("#bulk-reminder-template-modal-container").html(n),reminders.updateReminderModalDisplay(t),$("#bulk-reminder-confirmation-modal").modal({backdrop:"static",keyboard:!1});var a=readJson("#audio-templates-json");reminders.bindAudioTemplateClickAction(a),$(".selected-student-count").text(e),reminders.dataCache.templateLoaded=!0})}else showErrorDialogBox("Please select atleast one student for sending reminders.")},updateReminderModalDisplay:function(e){e?$(".fine-included").text("Yes"):$(".fine-included").text("No"),$("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count),$("#audio-voice-call-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count),$("#audio-template-selection-display").attr("style","display:none"),$("#audio-template-select").val(""),$("#send-bulk-voice-calls-button").prop("disabled",!0),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:none;"),reminders.dataCache.selectedStudentCount>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count?($("#send-bulk-sms-button").prop("disabled",!0),$("#insufficient-sms-credits-message").attr("style","color:red; display:block;")):($("#send-bulk-sms-button").prop("disabled",!1),$("#insufficient-sms-credits-message").attr("style","color:red; display:none;"))},bindAudioTemplateClickAction:function(e){$("#audio-template-select").change(function(){var t=$(this).val();if(""==t)return $("#audio-template-selection-display").attr("style","display:none"),void $("#send-bulk-voice-calls-button").prop("disabled",!0);$("#audio-template-selection-display").attr("style","display:block");for(var n=0;n<e.length;n++){var a=e[n];if(t==a.templateId){$("#template-name").text(a.templateName),$("#template-duration").text(round(a.metadata.audio_metadata.duration,0)),$("#template-credits").text(a.credits);var i=a.credits*reminders.dataCache.selectedStudentCount;$("#total-student-credits").text(i),reminders.dataCache.selectedAudioCredits=a.credits,i>channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count?($("#send-bulk-voice-calls-button").prop("disabled",!0),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:block;")):($("#send-bulk-voice-calls-button").prop("disabled",!1),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:none;"));break}}})},bulkDemandNoticeConfirmModal:function(){var e=$("input.bulk-student-select-checkbox:checkbox:checked").length;if(0!=e){var t='<span style="color:red">(FINE AMOUNT NOT INCLUDED)</span>',n="Yes, Demand Notice Without Fine";$("#include-fee-fine").is(":checked")&&(t='<span style="color:green">(FINE AMOUNT INCLUDED)</span>',n="Yes, Demand Notice With Fine"),$("#demand-notice-confirm-button").html(n),$("#bulk-demand-notice-confirmation-modal-text").html("You have selected "+e+" students to generate demand notice. Do you want to proceed? <br>"+t),$("#bulk-demand-notice-confirmation-modal").modal({backdrop:"static",keyboard:!1})}else showErrorDialogBox("Please select atleast one student for generating demand notice.")},sendBulkSMSReminders:function(){if(reminders.dataCache.selectedStudentCount>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count)showErrorDialogBox("You do not have sufficient credits to send "+reminders.dataCache.selectedStudentCount+" SMS. Please recharge.");else{var e=$("#sms-reminder-fee-batch-name").val();if(null!=e&&""!=e){$("#bulk-reminder-confirmation-modal").modal("toggle");var t=!1;$("#include-fee-fine").is(":checked")&&(t=!0);var n=reminders.getSelectedBulkStudentIds(),a={batchName:e,dueDate:reminders.dataCache.sendReminders.dueFeesDate,computeFine:t,academicSessionId:null,deliveryMode:"SMS",requiredStanardsCSV:reminders.dataCache.sendReminders.requiredStandards,studentIds:n};ajaxClient.post("/fees/send-due-payment-reminders",{sendRemindersPayload:JSON.stringify(a)},function(e){$("#fee-reminder-status-modal-container").html(e),$("#fee-reminder-status-modal").modal("toggle")})}else showErrorDialogBox("Please give batch name to identify the sent reminders.")}},sendAudioVoiceCall:function(){if(reminders.dataCache.selectedStudentCount*reminders.dataCache.selectedAudioCredits>channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count)showErrorDialogBox("You do not have sufficient credits to send "+reminders.dataCache.selectedStudentCount+" voice calls. Please recharge.");else{var e=$("#voice-reminder-fee-batch-name").val();if(null!=e&&""!=e){var t=!1;$("#include-fee-fine").is(":checked")&&(t=!0);var n=$("#audio-template-select").find(":selected").val().trim();if(""!=n){$("#bulk-reminder-confirmation-modal").modal("toggle");var a=reminders.getSelectedBulkStudentIds(),i={batchName:e,dueDate:reminders.dataCache.sendReminders.dueFeesDate,computeFine:t,academicSessionId:null,deliveryMode:"CALL",requiredStanardsCSV:reminders.dataCache.sendReminders.requiredStandards,studentIds:a,audioTemplateId:n};ajaxClient.post("/fees/send-due-payment-reminders",{sendRemindersPayload:JSON.stringify(i)},function(e){$("#fee-reminder-status-modal-container").html(e),$("#fee-reminder-status-modal").modal("toggle")})}else showErrorDialogBox("Please select audio clip for voice call")}else showErrorDialogBox("Please give batch name to identify the sent reminders.")}},generateDemandNotice:function(){$("#bulk-demand-notice-confirmation-modal").modal("toggle");var e=!1;$("#include-fee-fine").is(":checked")&&(e=!0);for(var t=reminders.getSelectedBulkStudentIds(),n=reminders.dataCache.sendReminders.dueFeesDate,a=reminders.dataCache.sendReminders.requiredStandards,i=academicSessionHandler.getSelectedSessionId(),s="",o=0;o<t.length;o++)0==o?s+=t[o]:s=s+","+t[o];demandNoticePayload={academicSessionId:i,dueFeesDate:n,includeFine:e,requiredStanardsCSV:a,students:t},ajaxClient.post("/fees/submit-demand-notice-students",{demandNoticePayload:JSON.stringify(demandNoticePayload)},function(e){window.open(baseURL+"/fees/generate-demand-notice/"+e,"_blank")})},getSelectedBulkStudentIds:function(){var e=[];return $("input.bulk-student-select-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-reminder-student-id").first().text().trim();e.push(t)}}),e},loadNotificationHistoryHomePage:function(){ajaxClient.get("/fees/notification-history-homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(reminders.changeSession),reminders.getNotificationHistory(!0,"SMS","BATCH"),reminders.dataCache.notificationHistoryTab="BATCH"})},changeSession:function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!0,t,e)},getNotificationHistory:function(e,t,n){reminders.dataCache.notificationHistoryTab=n,reminders.dataCache.deliveryModeTab=t;var a=$(".page-item.active").find(".page-number").text().trim();e&&(a=1);var i=$("#items-per-page").val();null!=a&&""!=a||(a=1);var s=(a-1)*i,o=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/notification-history/"+o+"/"+t+"/"+n+"/"+s+"/"+i,function(e){var i=0;"SMS"==t?($("#smsNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50):"CALL"==t&&($("#voiceNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50),$(".notification-history-fixed-height-list-wrapper").attr("style","height:"+i+"px;  overflow-y: scroll; cursor: pointer;");var s=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(s.itemsPerPage),$(".page-item").removeClass("active");s.offset,s.itemsPerPage;$("#page-number-"+a).addClass("active"),"INDIVIDUAL"==n&&($("#nav-individual-history-tab").addClass("active"),$("#nav-batch-history-tab").removeClass("active"),$("#individual-history-content").addClass("show active"),$("#batch-history-content").removeClass("show active")),reminders.bindIndividualNotificationsContentView(),reminders.initPagination()})},initPagination:function(){pagination.bindEvents(function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!0,t,e)})},getDeliveryModeTab:function(){var e=reminders.dataCache.deliveryModeTab;return null==e||""==e?"SMS":e},loadBatchNotificationsDetails:function(e){ajaxClient.get("/fees/batch-notification-details/"+e,function(e){$("#batch-notification-detail-modal-container").html(e),$("#view-batch-notification-modal").modal("toggle")})},bindIndividualNotificationsContentView:function(){$(".view-individual-notification").click(function(){var e=$(this).parent().find("p.notification-content").text().trim();$("#individual-notification-detail-modal").find(".modal-body").html('<span style="white-space: pre-line;">'+e+"</span>"),$("#individual-notification-detail-modal").modal("toggle")})}},studentWalletHandler={dataCache:{},confirmWalletRecharge:function(){var e=$("#wallet-recharge-student-id").text().trim(),t=$("#wallet-recharge-student-name-identifier").text().trim();if(!validateMandatoryFields($("#student-wallet-recharge-modal").find(".modal-body"))){var n=getFloatValue($("#student-recharge-wallet-amount").val());if(n<=0)return $("#student-recharge-wallet-amount").css("border","1px solid #ff8795"),void $("#student-recharge-wallet-amount").after('<p class="mandatory-field-text"> <span style="color:#e65f76;">Wallet amount should be > 0</span></p>');var a=$("#student-recharge-wallet-description").val(),i=$("#student-recharge-payment-mode").find(":selected").val().trim(),s=getDate($("#student-rechage-wallet-date").val());studentWalletHandler.dataCache.studentId=e,studentWalletHandler.dataCache.studentNameIdentifier=t,studentWalletHandler.dataCache.amount=n,studentWalletHandler.dataCache.description=a,studentWalletHandler.dataCache.paymentMode=i,studentWalletHandler.dataCache.transactionDate=s.getTime()/1e3,$("#student-wallet-recharge-modal").modal("toggle");var o="Do you want to recharge wallet of student "+t+" with amount = "+n+"/- ?";$("#student-wallet-recharge-confirm-modal").find(".student-wallet-recharge-confirm-text").text(o),$("#student-wallet-recharge-confirm-modal").modal({backdrop:"static",keyboard:!1})}},rechargeWallet:function(){$("#student-wallet-recharge-confirm-modal").modal("toggle");var e={studentId:studentWalletHandler.dataCache.studentId,amount:studentWalletHandler.dataCache.amount,description:studentWalletHandler.dataCache.description,transactionMode:studentWalletHandler.dataCache.paymentMode,transactionDate:studentWalletHandler.dataCache.transactionDate};ajaxClient.post("/fees/recharge-student-wallet",{walletRechargePayload:JSON.stringify(e)},function(e){$("#student-wallet-recharge-status-modal-container").html(e),$("#student-wallet-recharge-status-modal").modal("toggle"),loadStudentPaymentDetails(studentWalletHandler.dataCache.studentId)})},loadWalletTransactions:function(){var e=$("#wallet-recharge-student-id").text().trim();ajaxClient.get("/fees/wallet-transactions/"+e,function(e){$("#student-wallet-transactions-modal-container").html(e),$("#student-wallet-transactions-modal").modal({backdrop:"static",keyboard:!1})})}};pastReportAllowedDays=365;var feeReports={dataCache:{},loadReportsMenu:function(){ajaxClient.get("/fees/reports",function(e){if($("#main-content").html(e),initDateWithYearRange("-5:+5",!0),initSelect2("All"),feeReports.initDataCache(),feeReports.initMultiSelectAcademicSession(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),feeReports.bindGenerateReportEvent(),reportUtils.bindSelectClassCheckboxEvent(),feeReports.dataCache.allSessions.length>0){var t=feeReports.getFeesDropDown(feeReports.dataCache.allSessions[0].academicSessionId);$(".reports-student-fees").html(t)}})},initMultiSelectAcademicSession:function(e=""){$(".select2-multi-academic-session").each(function(){$(this).wrap('<div class="position-relative"></div>').select2({placeholder:e,dropdownParent:$(this).parent(),closeOnSelect:!1})})},initDataCache:function(){var e=readJson("#all-session-fees"),t=readJson("#all-sessions"),n=readJson("#selected-academic-session-json");feeReports.dataCache.allSessionFees=e,feeReports.dataCache.allSessions=t,$(".report-academic-session").val(n.academicSessionId)},getRequiredFeesCSV:function(e){$(".reports-student-fees").val().join()},getFeesDropDown:function(e){var t=feeReports.dataCache.allSessionFees[e],n="";if(null==t||null==t)return n;for(var a=0;a<t.length;a++)n+='<option value="'+t[a].feeConfigurationBasicInfo.feeId+'">'+t[a].feeConfigurationBasicInfo.feeName+"</option>";return n},bindGenerateReportEvent:function(){$(".report-academic-session").change(function(){var e=$(this).find(":selected").val().trim(),t=feeReports.getFeesDropDown(e);$(this).closest(".modal-body").find(".reports-student-fees").html(t)}),$(".is-due-report").on("click",function(){var e=!1;$(this).is(":checked")&&(e=!0),e?$(this).parent().parent().parent().find("#fee-due-date-div").attr("style","display:inline-block;"):$(this).parent().parent().parent().find("#fee-due-date-div").attr("style","display:none;")}),$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(e))){var t="";$(e).find(".report-academic-session option:selected").length>0&&(t=$(e).find(".report-academic-session option:selected").val()),t=""===t?0:t;var n="";$(e).find(".report-multi-academic-sessions").length>0&&(n=$(e).find(".report-multi-academic-sessions").val().join());var a=$(e).find("p.report-type").text().trim(),i=-1,s=-1,o=-1;$(e).find("input.report-start-date").length>0&&(i=getDate($(e).find("input.report-start-date").val()).getTime()/1e3),$(e).find("input.report-end-date").length>0&&(s=getDate($(e).find("input.report-end-date").val()).getTime()/1e3);var d="";"STUDENT_LEDGER"===a?d=$(e).find(".reports-student-class").val():$(e).find(".reports-student-class").length>0&&(d=$(e).find(".reports-student-class").val().join());var r="";$(e).find(".reports-student-fees").length>0&&(r=$(e).find(".reports-student-fees").val().join());var c="";$(e).find(".reports-student-status").length>0&&(c=$(e).find(".reports-student-status").val().join());var l=$(e).find(".download-format").val();l=void 0===l?"":l.trim();var u="";$(e).find(".reports-student-fee-heads").length>0&&(u=$(e).find(".reports-student-fee-heads").val().join());var f=$(e).find(".transaction-type").val();f=void 0===f?"":f.trim(),$(e).find("input.fee-due-date").length>0&&(feeDueDateObj=getDate($(e).find("input.fee-due-date").val()),null!=feeDueDateObj&&(o=feeDueDateObj.getTime()/1e3));var m=!1;if($(e).find(".is-due-report").length>0)if($(e).find("input.is-due-report").is(":checked")){if(m=!0,o<=0)return void $(e).find("input.fee-due-date").css("border","1px solid #ff8795")}else o=-1;$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/fees/generate-report/"+a+"?academic_session_id="+t+"&requiredMultiSession="+n+"&reportStartDate="+i+"&reportEndDate="+s+"&fee_ids="+r+"&requiredStandards="+d+"&feeDueDate="+o+"&studentStatus="+c+"&downloadFormat="+l+"&fee_head_ids="+u+"&transactionType="+f+"&includeDueStudentOnly="+m,"_blank")}})}};