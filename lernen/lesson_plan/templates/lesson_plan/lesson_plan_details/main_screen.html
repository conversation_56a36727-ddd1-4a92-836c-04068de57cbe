{% load json %}
<p class="chapter-details-list d-none">{{ lesson_plan_chapter_detail_list|jsonstr }}</p>

<div class="row mb-2 mb-xl-3" id="lesson-plan-academic-session-div">
    <div class="col-auto mr-auto text-left mt-n1">
        <h4> Lesson Plan Details</h4>
    </div>
    <div class="col-auto ml-auto text-right mt-n1">
        <button type="button" class="btn btn-outline-info btn-sm add-chapter-plan" onclick="lessonPlanAndStaffResponseUI.addNewChapter()">Add Chapter</button>
    </div>
</div>

<div id="chapter-details-div">
    <div class="no-chapter">
        <br><br><br><br><br><br><br><br><br><br>
        <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to see course and lesson Plan !</h5>
        <br><br><br><br><br><br><br><br><br><br>
    </div>
</div>
<div style="display: none; align-items: center; justify-content: center;" id="add-lesson-Plan">
    <button type="button" class="btn btn-outline-info" style="width: 20%;" id="add-lesson-plan-details">Save</button>
</div>
 <div class="modal fade" id="lesson-plan-alert-popup" tabindex="-1" role="dialog" aria-labelledby="exampleCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleLongTitle">Lesson Plan Details</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <span id="lesson-plan-save-details-popup-text">Do you want to save lesson plan details?</span>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-success" id="lesson-plan-save-confirm-button">Save</button>
        </div>
      </div>
    </div>
</div>
