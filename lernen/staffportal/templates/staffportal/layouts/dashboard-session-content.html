{% load json %}
{% load currency_utils %}
{% load displaytime %}
{% load displayPercentage %}
{% load round_to_decimals %}
{% load math_utils %}
{% load string_utils %}
<p style="display:none;" id="home-page-standards-stats">{{standards|jsonstr}}</p>
<p style="display:none;" id="home-page-fees-stats">{{class_payment_stats|jsonstr}}</p>
<p style="display:none;" id="date-wise-home-page-fees-stats">{{date_wise_class_payment_stats|jsonstr}}</p>
<p style="display:none;" id="home-page-admission-stats">{{admission_home_stats|jsonstr}}</p>
<p style="display:none;" id="invoice-details">{{invoice_details|jsonstr}}</p>
<p style="display:none;" id="staff_stats">{{staff_stats|jsonstr}}</p>
<p style="display:none;" id="transport_stats">{{transport_stats|jsonstr}}</p>
<div class="row">
  <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card illustration flex-fill">
        <div class="card-body p-0 d-flex flex-fill">
          <div class="row no-gutters w-100">
            <div class="col-6">
              <div class="illustration-text p-3 m-1">
                <h4 class="illustration-text">Welcome Back, {{user.firstName}}!</h4>
                <p class="mb-0">Staff Dashboard</p>
              </div>
            </div>
            <div class="col-6 align-self-center text-right">
                <!-- <img src="https://assetsv5.embrate.com/static/core/images/illustrations/customer-support.png" alt="Searching" class="img-fluid illustration-img"> -->
                <img src="{% if user.thumbnail %} data:image/jpg;base64, {{user.thumbnail}} {% else %}https://assetsv5.embrate.com/static/core/images/illustrations/customer-support.png{% endif %}" alt="Searching" class="img-fluid illustration-img">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <h3 class="mb-2">{{admission_home_stats.totalStudents|format_inr_currency_with_round}}</h3>
              <p class="mb-2">Enrolled Students</p>
              <div class="mb-0 mt-3">
                <span class="badge badge-soft-success" >
                  <i class="mdi mdi-arrow-bottom-right"></i>
                    {{admission_home_stats.newAdmissionStudents}}
                </span>
                <span class="text-muted">New</span>
                  <span class="badge badge-soft-danger" >
                  <i class="mdi mdi-arrow-bottom-right"></i>
                  {{relieved_students}}
                </span>
                  <span class="text-muted">Relieved</span>
              </div>
            </div>
            <div class="d-inline-block ml-3">
              <div class="stat">
                <i class="align-middle text-success" data-feather="users"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <h3 class="mb-2" style="display: inline-block;">
                <span class="align-middle">
                    {{attendance_stats.totalPresentAttendance|to_2_decimals_without_traling_zeros}}
                </span>
             </h3>
                <span class="badge badge-soft-success">
                    <i class="mdi mdi-arrow-bottom-right"></i>
                    {{present_percentage|to_2_decimals_without_traling_zeros}}%
                </span>
              <p class="mb-2">Present Today</p>
              <div class="mb-0 mt-3">
                <span class="badge badge-soft-danger" >
                  <i class="mdi mdi-arrow-bottom-right"></i>
                  {% if attendance_stats %}{{attendance_stats.totalAbsentAttendance|addition:attendance_stats.totalLeaveAttendance|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}
                </span>
                <span class="text-muted">Absent/Leave Today</span>
              </div>
            </div>
            <div class="d-inline-block ml-3">
              <div class="stat">
                <!-- <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span> -->
                <i class="align-middle text-success" data-feather="user-check"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
        <div class="card flex-fill">
            <div class="card-body py-4">
                <div class="media">
                    <div class="media-body">
                        <h3 class="mb-2"><i class="align-middle me-2 fas fa-fw fa-rupee-sign"></i>
                            <span class="align-middle">
                  {% if not view_fees_stats or not class_payment_stats %}
                    -
                  {% else %}
                    {{class_payment_stats.collectedAmount|format_inr_currency_with_round}}
                  {% endif %}
                </span>
                        </h3>
                        <p class="mb-2">Collected Fees</p>
                        <div class="mb-0 mt-3">
                <span class="badge badge-soft-info" >
                  <i class="mdi mdi-arrow-bottom-right"></i>
                  {% if not view_fees_stats or not class_payment_stats %}
                    -
                  {% else %}
                    ₹ {{class_payment_stats.todayCollectedAmount|format_inr_currency_with_round}}
                  {% endif %}
                </span>
                <span class="text-muted">Today's Collection</span>
              </div>
            </div>
            <div class="d-inline-block ml-3">
              <div class="stat">
                <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-lg-9 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <h5 class="card-title mb-0">Student Distribution</h5>
            </div>
            <div class="card-body d-flex w-100">
                <div class="align-self-center chart">
                    <canvas id="chartjs-student-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-3 d-flex">
        <div class="flex-fill w-100">
            <div class="card card-header" style="background-color:#e6f8fa;margin:0px;">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="d-flex">
                <div class="w-100">
                  <div class="mb-0">
                    {% for quick_action in quick_actions_details %}
                      <a class="sidebar-link card text-dark" href="{{quick_action.link}}" style="cursor: pointer;margin:1px;">
                        <p style="margin-bottom: .6rem!important;margin-top: .6rem!important;">{{quick_action.name}}</p>
                      </a>
                    {% endfor %}
                  </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if view_fees_stats or not class_payment_stats %}
<div class="row">
    <div class="col-12 col-lg-8 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <h5 class="card-title mb-0" style="float:left;" id="bar-graph-header-text">Today's Fees Collection</h5>
            </div>
            <div class="card-body d-flex w-100">
                <div class="align-self-center chart" {% if not view_fees_stats  %} style="display:none;" {% endif %}>
                    <canvas id="chartjs-class-fee-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Invoices</h5>
            </div>
            <div class="d-flex" style="display:block;overflow-y: auto;max-height: 400px;">
                <div class="w-100">
                    <div class="mb-0">
                        {% if invoice_details %}
                        {% for invoice in invoice_details %}
                        <div id={{invoice.transactionId}}  class=" invoice-payment-transaction card-border">
                            <div class="mt-3 ml-0 mr-2 mb-3 row">
                                <div class="col-3" style="padding-right: 0px;">
                                    <div class="stat mb-2" style="padding-right: 0px;">
                                        <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span>
                                    </div>
                                    <p class="mb-0" style="font-size:12px;">{{invoice.transactionDate|short_print_date}}</p>
                                </div>
                                <div class="col-6" style="padding-left: 0px;">
                                    <h5>Invoice: {{invoice.invoiceId}}</h5>
                                    <p class="mb-0">{{invoice.student.name}}</p>
                                    <p class="logo-color mb-0">{% if invoice.student.fathersName %}{{invoice.student.fathersName}}{% else %}-{% endif %}</p>
                                </div>
                                <div class="col-3" style="height: 45px;line-height: 45px;">
                                    <p style="float:right;">₹{{invoice.netPaidAmount|format_inr_currency_with_round}}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
  <div class="col-12 col-lg-4 d-flex">
    <div class="card flex-fill w-100 pl-0 pr-0">
      <div class="card-header">
        <h5 class="card-title mb-0" id="new-admission-graph-header-text">Student Birthdays</h5>
    </div>
      <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-after-enrollment-structure-tab" data-toggle="tab" href="#nav-after-enrollment-structure" role="tab" aria-controls="nav-home" aria-selected="true">Today</a>
          <a class="nav-item nav-link" id="nav-before-registration-structure-tab" data-toggle="tab" href="#before-registration-structure" role="tab" aria-controls="nav-profile" aria-selected="false">Upcoming</a>
        </div>
      </nav>
      <br/>
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-after-enrollment-structure" role="tabpanel" aria-labelledby="nav-after-enrollment-structure-tab">
              {% if today_birthday_details %}
              <div class="d-flex" style="display:block;overflow-y: auto;max-height: 400px;">
                <div class="w-100">
                  <div class="mb-0">
                {% for student in today_birthday_details %}
                <div class="card-border" style="border-right: 0px; border-left: 0px;">
                    <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                    <div class="mt-3 ml-0 mr-2 mb-3 row">
                      <div class="col-3" style="padding-right: 0px;">
                        <div class="stat">
                          <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                        </div>
                      </div>
                      <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                        <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                        <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                      </div>
                      <div class="col-3">
                        <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                      </div>
                    </div>
                  </div>
                {% endfor %}
                </div>
                </div>
                </div>
              {% endif %}
          </div>
          <div class="tab-pane fade" id="before-registration-structure" role="tabpanel" aria-labelledby="nav-before-registration-structure-tab">
            {% if upcoming_birthday_details %}
            <div class="d-flex" style="display:block;overflow-y: auto;max-height: 400px;">
              <div class="w-100">
                <div class="mb-0">
                  {% for student in upcoming_birthday_details %}
                  <div class="card-border" style="border-right: 0px; border-left: 0px;" >
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                      <div class="mt-3 ml-0 mr-2 mb-3 row">
                        <div class="col-3" style="padding-right: 0px;">
                          <div class="stat">
                            <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                          </div>
                        </div>
                        <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                          <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                          <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                        </div>
                        <div class="col-3">
                          <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                        </div>
                      </div>
                    </div>
                  {% endfor %}
                </div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <div class="col-12 col-lg-4 d-flex" style="cursor: pointer" >
    <div class="card flex-fill w-100">
        <div class="card-header">
            <h5 class="card-title mb-0" id="new-admission-graph-header-text">Staff Gender Wise Attendance</h5>
        </div>
        <div class="card-body d-flex">
            <div class="w-100">
                <div class="py-3">
                    <div id="tabularview-new-admission-student-distribution" style="display:block;overflow-y: auto;max-height: 450px;"></div>
                    <div class="chart chart-xs" id="chartjs-new-admission">
                      <div id="percentage-change-new-admission" style="float:right;"></div>
                      <canvas id="chartjs-staff-count-bar-distribution"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="col-12 col-lg-4 d-flex">
  <div class="card flex-fill w-100">
      <div class="card-header">
          <h5 class="card-title mb-0" id="rte-graph-header-text">Active Transport</h5>
      </div>
      <div class="card-body d-flex">
          <div class="w-100">
              <div class="py-3">
                  <div class="chart chart-xs">
                      <div id="tabularview-rte-student-distribution" style="display:block;overflow-y: auto;max-height: 450px;"></div>
                      <canvas id="chartjs-active-transport-pie"></canvas>
                      <div id="percentage-active-transport" class="text-center mt-4"></div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>
</div>

