<div id="add-holiday-template-modal" class="modal fade bd-example-modal-xl" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
     <div class="modal-dialog modal-xl">
         <div class="modal-content">
             <div class="modal-header">
                 <h5 class="modal-title" > New Calendar </h5>
                 <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                     <span aria-hidden="true"> &times; </span>
                 </button>
             </div>
             <div class="modal-body">
               <div class="form-row">
                 <div class="form-group col-md-4">
                   <label>Calendar Name*</label>
                   <input type="text" class="form-control mandatory-field" id="new-holiday-template-name" placeholder="Calendar Name..." >
                 </div>
                 <div class="form-group col-md-4">
                   <label>Calendar Type *</label>
                   <select id="new-holiday-template-type" class="form-control mandatory-field">
                        <option value="GENERAL">General</option>
                        <option value="STAFF">Staff</option>
                        <option value="STUDENT">Student</option>
                   </select>
                 </div>
                <div class="form-group col-md-4">
                  <label>Description</label>
                  <textarea class="form-control" id="new-holiday-template-description" rows="1" placeholder="Description"></textarea>
                </div>
               </div>
               <br />
               <div id="select-template-holiday-div">
                    {% include 'holiday_calendar/templates/template_holiday_list.html' %}
               </div>
             </div>
             <div class="modal-footer">
                 <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="">Cancel</button>
                 <button id="submit-holiday-template" type="button" class="btn btn-primary" onclick="configureTemplate.submitNewHolidayTemplate()">Add New Calendar</button>
             </div>
         </div>
     </div>
 </div>