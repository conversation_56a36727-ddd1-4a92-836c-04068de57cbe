{% load json %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>REPORTS</h3>
    </div>
</div>
<p style="display:none;" id="session-class-exams">{{session_class_exams|jsonstr}}</p>
<p style="display:none;" id="session-class-courses">{{class_courses|jsonstr}}</p>
<p style="display:none;" id="current-session">{{currect_session|jsonstr}}</p>

<br>

<div>
    <div class="row">

      <div class="col-sm-4">
        <div id="session-class-exam-marks-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Exam Marks Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports exam marks for all the students in the selected class and session</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="session-class-exam-feed-marks-sheet" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Fill Exam Marks Sheet</strong></h4>
            <p class="card-body" style="color:#6c757d">Sheet containing student and exam marks columns in the selected exam</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="session-class-course-marks-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Exam Consolidated Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports exam marks for all the students in the selected class and session</p>
          </div>
        </div>
      </div>

    </div>


    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="subject-wise-class-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Subjectwise Class Result Analysis</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports subjectwise class result analysis.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="subjectwise-exam-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Subjectwise Exam Grade Analysis</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports with subjectwise exam result analysis.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="examwise-grade-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Exam Wise Grade Analysis</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports with exam wise grade result analysis.</p>
          </div>
        </div>
      </div>

    </div>

    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="staff-wise-division-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Teacher Wise Result Analysis Report (Division Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Teacher Wise division Result Analysis detailed report.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="staff-wise-grade-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Teacher Wise Result Analysis Report (Grade Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Teacher Wise grade Result Analysis detailed report.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="class-wise-division-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Class Result Report (Division Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Class Wise division Result Analysis detailed report.</p>
          </div>
        </div>
      </div>

    </div>

    <br/>

    <div class="row">

      <div class="col-sm-4">
        <div id="class-wise-grade-result-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Class Result Report (Grade Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Class Wise grade Result Analysis detailed report.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="subject-wise-rank-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Subjectwise Rank Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Subject-wise Rank Analysis detailed report.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="overall-rank-analysis-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Overall Rank Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Overall Rank Analysis detailed report.</p>
          </div>
        </div>
      </div>

    </div>

    <br/>

    <div class="row">

      <div class="col-sm-4">
        <div id="result-summary-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Result Summary Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Result Summary Analysis detailed report.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="multiple-exam-marks-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Multiple Exam Marks Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Reports exam marks for all multiple exams of students in the selected class and session</p>
          </div>
        </div>
      </div>


      <div class="col-sm-4">
        <div id="growth-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Growth Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Growth Report of multiple exams in the selected class and session</p>
          </div>
        </div>
      </div>
    </div>

</div>

<div class="modal fade" id="session-class-exam-marks-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Exam Marks Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section select2" multiple>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-3">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field">
            </select>
          </div>
        </div>

        <div class="form-row mt-4 pl-2">
          <p class="mb-0"><u>Additional Configurations</u></p>
        </div>

        <div class="form-row" style="padding-top:1%;">
          <div class="form-group col-md-4">
            <div class="form-check">
             <input type="checkbox" class="form-check-input show-dimensions">
             <label class="form-check-label" for="select-report-show-dimensions"> Show Exam Dimensions</label>
           </div>
          </div>
          <div class="form-group col-md-4">
             <div class="form-check">
              <input type="checkbox" class="form-check-input exclude-coscholastic-subjects">
              <label class="form-check-label" for="select-report-exclude-coscholastic-subjects"> Exclude Coscholastic Subjects</label>
            </div>
          </div>
          <div class="form-group col-md-4">
             <div class="form-check">
              <input type="checkbox" class="form-check-input show-scholastic-grade">
              <label class="form-check-label" for="select-report-display-attendance"> Show Grade in Scholastic Subjects</label>
            </div>
          </div>
          <div class="form-group col-md-4">
             <div class="form-check">
              <input type="checkbox" class="form-check-input show-co-scholastic-grade">
              <label class="form-check-label" for="select-report-display-attendance"> Show Grade in Co-Scholastic Subjects</label>
            </div>
          </div>
        </div>

        {% include 'core/reports/header_list_for_exam.html' with  header_columns=report_header_columns.EXAM_MARKS_REPORT %}
      </div>
      <p style="display:none;" class="report-type">EXAM_MARKS_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="session-class-exam-feed-marks-sheet-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fill Exam Marks Sheet </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select id="select-standards" class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section">
            </select>
          </div>
        </div>

        <div class="form-row" >
          <div class="form-group col-md-3">
            <label>Examination*</label>
              <select id="leaf-exams" class="form-control report-exams mandatory-field">
              </select>
          </div>
          <div class="form-group col-md-3" style="padding-top:4%;">
            <div class="form-check">
               <input type="checkbox" class="form-check-input relieved-student-checkbox" id="relieved-student-checkbox">
               <label class="form-check-label" for="relieved-student-checkbox"> Relieved Students </label>
           </div>
          </div>
          <div class="form-group col-md-3">
            <label>Student Per Page</label>
            <input type="number" class="form-control mandatory-field no-special-chars student-per-page" placeholder="Enter Number of Student Per Page ..." value="40">
          </div>
          <div class="form-group col-md-3">
            <label>Sorting On*</label>
            <select class="form-control mandatory-field" id="student-sorting-parameters">
              <option value="STUDENT_NAME" selected>Student Name</option>
              <option value="ADMISSION_NUMBER">Admission Number</option>
              <option value="ROLL_NUMBER">Roll Number</option>
            </select>
          </div>
        </div>
      </div>
      <p style="display:none;" class="report-type"></p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-feed-marks-sheet">Generate Sheet</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="session-class-course-marks-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Exam Consolidated Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field">
            </select>
          </div>
          <div class="form-group col-md-4" style="padding-top:35px;">
             <div class="form-check">
              <input type="checkbox" class="form-check-input exclude-coscholastic-subjects">
              <label class="form-check-label" for="select-report-exclude-coscholastic-subjects"> Exclude Coscholastic Subjects</label>
            </div>
          </div>
        </div>
      </div>
      <p style="display:none;" class="report-type"></p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary course-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="subject-wise-class-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Subjectwise Class Result Analysis Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Courses*</label>
            <select class="form-control report-courses mandatory-field">
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field">
            </select>
          </div>
          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
      </div>
      <p style="display:none;" class="report-type">SUBJECT_WISE_CLASS_RESULT_ANALYSIS</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>



<div class="modal fade" id="subjectwise-exam-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Subjectwise Exam Grade Analysis Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control section-ids-multiple select2" multiple>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Course Type*</label>
            <select class="form-control report-course-type mandatory-field">
              <option value="SCHOLASTIC" selected>Scholastic</option>
              <!-- <option value="COSCHOLASTIC">Co-Scholastic</option> -->
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field">
            </select>
          </div>
          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
      </div>
      <p style="display:none;" class="report-type">SUBJECT_WISE_EXAM_GRADE_ANALYSIS</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>



<div class="modal fade" id="examwise-grade-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Exam Wise Grade Analysis Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Course Type*</label>
            <select class="form-control report-course-type mandatory-field">
              <option value="SCHOLASTIC" selected>Scholastic</option>
              <!-- <option value="COSCHOLASTIC">Co-Scholastic</option> -->
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field">
            </select>
          </div>
          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
      </div>
      <p style="display:none;" class="report-type">EXAM_WISE_GRADE_ANALYSIS</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="staff-wise-division-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Teacher Wise Result Analysis Report (Division Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Staff*</label>
            <select class="form-control report-staff mandatory-field">
            {% for staff_detail in staff_details %}
                {% if staff_detail.staffBasicInfo.initials == None %}
                  <option value="{{staff_detail.staffId}}">{{staff_detail.staffBasicInfo.name}}</option>
                {% else %}
                  <option value="{{staff_detail.staffId}}">{{staff_detail.staffBasicInfo.name}} ({{staff_detail.staffBasicInfo.initials}})</option>
                {% endif %}
            {% endfor %}
            </select>
          </div>

          <div class="different-value-wrapper" style="display:none;">{% include 'examination/wrapper.html' %}</div>

          <div class="form-group col-md-4">
            <label>Report Card*</label>
            <select class="form-control report-type-name mandatory-field">
              <option value=""></option>
              {% for report_card_type in report_card_types %}
                <option value={{report_card_type.reportCardType}}>{{report_card_type.reportCardName}}</option>
              {% endfor%}
            </select>
          </div>
        </div>

        <div class="form-row">
          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>

      </div>
      <p style="display:none;" class="report-type">TEACHER_WISE_DIVISION_RESULT_ANALYSIS_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="staff-wise-grade-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Teacher Wise Result Analysis Report (Grade Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Staff*</label>
            <select class="form-control report-staff mandatory-field">
            {% for staff_detail in staff_details %}
                {% if staff_detail.staffBasicInfo.initials == None %}
                  <option value="{{staff_detail.staffId}}">{{staff_detail.staffBasicInfo.name}}</option>
                {% else %}
                  <option value="{{staff_detail.staffId}}">{{staff_detail.staffBasicInfo.name}} ({{staff_detail.staffBasicInfo.initials}})</option>
                {% endif %}
            {% endfor %}
            </select>
          </div>

          <div class="different-value-wrapper" style="display:none;">{% include 'examination/wrapper.html' %}</div>

          <div class="form-group col-md-4">
            <label>Report Card*</label>
            <select class="form-control report-type-name mandatory-field">
              <option value=""></option>
              {% for report_card_type in report_card_types %}
                <option value={{report_card_type.reportCardType}}>{{report_card_type.reportCardName}}</option>
              {% endfor%}
            </select>
          </div>
        </div>

        <div class="form-row">
          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>

      </div>
      <p style="display:none;" class="report-type">TEACHER_WISE_GRADE_RESULT_ANALYSIS_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="class-wise-division-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Class Result Report (Division Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="different-value-wrapper" style="display:none;">{% include 'examination/wrapper.html' %}</div>

          <div class="form-group col-md-4">
            <label>Report Card*</label>
            <select class="form-control report-type-name mandatory-field">
              <option value=""></option>
              {% for report_card_type in report_card_types %}
                <option value={{report_card_type.reportCardType}}>{{report_card_type.reportCardName}}</option>
              {% endfor%}
            </select>
          </div>

          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->

        </div>

      </div>
      <p style="display:none;" class="report-type">DIVISION_WISE_CLASS_RESULT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="class-wise-grade-result-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Class Result Report (Grade Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="different-value-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>

          <div class="form-group col-md-4">
            <label>Report Card*</label>
            <select class="form-control report-type-name mandatory-field">
              <option value=""></option>
              {% for report_card_type in report_card_types %}
                <option value={{report_card_type.reportCardType}}>{{report_card_type.reportCardName}}</option>
              {% endfor%}
            </select>
          </div>

          <!-- <div class="form-group col-md-4">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->

        </div>

      </div>
      <p style="display:none;" class="report-type">GRADE_WISE_CLASS_RESULT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="subject-wise-rank-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Subjectwise Rank Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section section-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field exam-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Courses</label>
            <select class="form-control report-courses course-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Compute Rank Till*</label>
            <input type="number" class="form-control mandatory-field no-special-chars rank-till" placeholder="Compute Rank Till...">
          </div>

        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Cumulative Examination</label>
            <select class="form-control report-cumulative-exams select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>


      </div>
      <p style="display:none;" class="report-type">SUBJECT_WISE_RANK_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="overall-rank-analysis-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Overall Rank Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section section-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field exam-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Compute Rank Till*</label>
            <input type="number" class="form-control mandatory-field no-special-chars rank-till" placeholder="Compute Rank Till...">
          </div>

          <div class="form-group col-md-4">
            <label>Cumulative Examination</label>
            <select class="form-control report-cumulative-exams select2" multiple data-toggle="select2">
            </select>
          </div>

        </div>
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Additional Courses</label>
            <div class="additional-report-courses"></div>
            <select class="form-control report-courses additional-course-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

      </div>
      <p style="display:none;" class="report-type">OVERALL_RANK_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="result-summary-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Result Summary Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section section-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field exam-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Courses</label>
            <select class="form-control report-courses course-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

        </div>

      </div>
      <p style="display:none;" class="report-type">RESULT_SUMMARY_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="multiple-exam-marks-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Multiple Exam Marks Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section section-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field exam-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Additional Courses</label>
            <div class="additional-report-courses"></div>
            <select class="form-control report-courses additional-course-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>

        </div>

        <div class="form-row mt-4 pl-2">
          <p class="mb-0"><u>Additional Configurations</u></p>
        </div>

        <div class="form-row" style="padding-top:1%;">
          <div class="form-group col-md-4">
            <label>Scholastic Columns</label>
            <select class="form-control scholastic-marks-display-type-multiple select2" multiple data-toggle="select2">
              <option value="MARKS" selected>Marks</option>
              <option value="GRADE">Grade</option>
              <option value="PERCENTAGE">Percentage</option>
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Co Scholastic Columns</label>
            <select class="form-control coscholastic-marks-display-type-multiple select2" multiple data-toggle="select2">
              <option value="MARKS" selected>Marks</option>
              <option value="GRADE">Grade</option>
              <option value="PERCENTAGE">Percentage</option>
            </select>
          </div>
          <div class="form-group col-md-4 pt-4 mt-2">
             <div class="form-check">
              <input type="checkbox" class="form-check-input exclude-coscholastic-subjects">
              <label class="form-check-label" for="select-report-exclude-coscholastic-subjects"> Exclude Coscholastic Subjects</label>
            </div>
          </div>
          <div class="form-group col-md-4">
             <div class="form-check">
              <input type="checkbox" class="form-check-input sort-student-on-rank">
              <label class="form-check-label" for="select-report-sort-student-on-rank"> Sort Report On Rank Basis</label>
            </div>
          </div>
          <div class="form-group col-md-4">
            <div class="form-check">
             <input type="checkbox" class="form-check-input show-class-average-details">
             <label class="form-check-label" for="select-report-show-class-average-details"> Show Class Avg. Summary</label>
           </div>
         </div>
         <div class="form-group col-md-4">
          <div class="form-check">
           <input type="checkbox" class="form-check-input show-staff-details">
           <label class="form-check-label" for="select-report-show-staff-details"> Show Subject Teacher Details</label>
         </div>
       </div>
        </div>

        {% include 'core/reports/header_list_for_exam.html' with  header_columns=report_header_columns.EXAM_MARKS_REPORT %}
      </div>
      <p style="display:none;" class="report-type">MULTIPLE_EXAM_MARKS_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-reports">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="growth-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Growth Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
              <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="standards-json-wrapper" style="display:none;">{% include 'examination/standards_wrapper.html' %}</div>
          <div class="section-json-wrapper" style="display:none;">{% include 'examination/reports/section_details.html' %}</div>
          <div class="form-group col-md-4">
            <label>Class*</label>
            <select class="form-control report-standards mandatory-field">
              <option value="">select</option>
              {% for standard in standards %}
              <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-4">
            <label>Section</label>
            <select class="form-control report-standard-section section-ids-multiple select2" multiple data-toggle="select2">
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Examination*</label>
            <select class="form-control report-exams mandatory-field exam-ids-multiple select2" multiple data-toggle="select2"  data-placeholder="Select">
            </select>
          </div>
        </div>
      </div>
      <p style="display:none;" class="report-type">GROWTH_REPORT</p>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
        <button type="button"   class="btn btn-primary generate-graphical-report">View Report</button>
      </div>
    </div>
  </div>
</div>

