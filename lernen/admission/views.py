from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from core.controller.user.follow_up import *
from core.controller.user.notification_manager import *
from core.controller.utils.reports_manager import *
from fees.controller.fee_assignment_manager import *
from fees.controller.fee_config_manager import *
from .controller.reports_manager import *
from .controller.admission_manager import *
from core.controller.utils.tracking_events import *
from core.controller.client.institute_payment_manager import *
from datetime import date
from math import *
from holiday_calendar.controller.holiday_calendar_manager import get_session_holiday_templates
from django.http import HttpResponseServerError

module_name = 'ADMISSION'
def authorized_user_session(request):
	return authorized_module(request,module_name)


def raise_error_page_view(request, institute_unique_code):
	a = None
	print(a['access'])
	return render(request, 'admission/pending_enrolments/status_modal.html',{"data":None})

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "ADMISSION_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		user_type = user_login_view['user']['userType']
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		tutorial_videos = get_tutorial_video_details(module_name, institute_unique_code, user_type)
		return render(request, 'admission/layouts/default-admission-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes' : app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data': payment_status_data, 'view_only_access': view_only_access, 'bell_notification_count' : bell_notification_count, 'web_ui_preferences' : web_ui_preferences, 'tutorial_videos': tutorial_videos })

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		admission_home_stats = get_admission_home_stats(user_login_view, institute_unique_code, current_session['academicSessionId'])
		standards = get_standards_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'admission/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session, 'admission_home_stats' : admission_home_stats, 'standards' : standards})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		admission_home_stats = get_admission_home_stats(user_login_view, institute_unique_code, academic_session_id)
		standards = get_standards_with_count(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'admission/layouts/dashboard-session-content.html',{'user': user_login_view['user'], 'admission_home_stats' : admission_home_stats, 'standards' : standards})

def verify_restrict_enrollment(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		verification_restrict_enrollment = get_verification_restrict_enrollment(user_login_view, institute_unique_code, academic_session_id, student_id)
		return render(request, 'admission/pending_enrolments/status_modal.html',{"data":verification_restrict_enrollment})
	
def enroll_student_metadata_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		enrollment_assign_payload = get_enrollment_assign_payload(user_login_view, institute_unique_code, academic_session_id, student_id)
		holiday_template_list = get_session_holiday_templates(user_login_view, institute_unique_code, academic_session_id, "STUDENT")
		default_fee_assignment_structure = enrollment_assign_payload['feeAssignmentStructures']
		optional_courses = enrollment_assign_payload['optionalCourses']
		sms_preferences = enrollment_assign_payload['smsPreferences']
		default_discount_assignment_structure = enrollment_assign_payload['defaultEntityDiscountAssignmentStructures']
		transport_details = enrollment_assign_payload['transportDetails']
		fee_data = get_fee_data(user_login_view, institute_unique_code, academic_session_id)
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, academic_session_id)
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		return render(request, 'admission/pending_enrolments/enroll_student.html',{'institute_metadata':institute_metadata, 'default_fee_assignment_structure':default_fee_assignment_structure, 'optional_courses' : optional_courses, 'sms_preferences' : sms_preferences, 'default_discount_assignment_structure' : default_discount_assignment_structure, 'transport_details' : transport_details, 'fee_data' : fee_data, 'institute_houses_with_count' : institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str, 'holiday_template_list' : holiday_template_list})

@csrf_exempt
def auto_suggest_house_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		auto_suggest_house_payload = json.loads(request.POST['autoSuggestHousePayload'])
		auto_suggest_house_details = get_auto_suggest_house_details(user_login_view, institute_unique_code, academic_session_id, auto_suggest_house_payload)
		return render(request, 'admission/pending_enrolments/auto_suggest_house.html',{'auto_suggest_house_details' : auto_suggest_house_details})

def add_student_screen_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		latest_session = get_latest_academic_year_without_stored_preference(academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		return render(request, 'admission/new_student.html',{'academic_years':academic_years, 'current_session':current_session, 'latest_session':latest_session, 'standards':standards, 'institute_metadata' : institute_metadata, "institute_houses_with_count" : institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str})

def get_student_houses_stats_str(institute_houses_with_count):
	student_houses_stats_str = ""
	if institute_houses_with_count:
		first = True
		for house_with_count in institute_houses_with_count:
			studentCountInt = house_with_count['studentCount']
			houseName = house_with_count['instituteHouse']['houseName']
			if not first:
				student_houses_stats_str += ";"
			student_houses_stats_str = student_houses_stats_str + houseName + ":" + str(studentCountInt)
			first = False
	return student_houses_stats_str

@csrf_exempt
def add_new_student_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		document_name = None
		if 'documentName' in request.POST:
			document_name = request.POST['documentName']
		document = None
		if 'document' in request.FILES:
			document = request.FILES['document']
		add_student_payload = json.loads(request.POST['registerStudentPayload'])

		response_data = add_new_student(user_login_view, institute_unique_code, add_student_payload, document_name, document)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})


@csrf_exempt
def student_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/student_details_wrapper.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': True, 'institute_houses_with_count' : institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str, 'institute_metadata' : institute_metadata})

@csrf_exempt
def student_details_reload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/student_details_wrapper.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': True, 'institute_houses_with_count' : institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str, 'institute_metadata' : institute_metadata})

@csrf_exempt
def student_search_view(request, institute_unique_code, academic_session_id, status, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_list_with_pagination_info = search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("text",""), status, offset, limit, request.GET.get("requiredStandards",""))
		pagination_info = student_list_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		return render(request, 'admission/student_list.html',{"status":status, "results":student_list_with_pagination_info['result'], "pagination_details":pagination_details, "standards" : standards, 'academic_years' : academic_years})

@csrf_exempt
def search_relieved_students_view(request, institute_unique_code, academic_session_id, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_list_with_pagination_info = search_relieved_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("text",""), offset, limit, request.GET.get("requiredStandards",""))
		pagination_info = student_list_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		return render(request, 'admission/student_list.html',{"status":"RELIEVED", "results":student_list_with_pagination_info['result'], "pagination_details":pagination_details, "standards" : standards, 'academic_years' : academic_years})

@csrf_exempt
def update_student_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		document_name = None
		if 'documentName' in request.POST:
			document_name = request.POST['documentName']
		document = None
		if 'document' in request.FILES:
			document = request.FILES['document']
		update_student_payload = json.loads(request.POST['updateStudentPayload'])

		response_data = update_student(user_login_view, institute_unique_code, update_student_payload, academic_session_id, document_name, document)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})



def relieve_student_screen_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		return render(request, 'admission/relieve_student/main_screen.html',{})

def relieve_student_screen_reload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		return render(request, 'admission/relieve_student/main_screen.html',{})

def student_search_without_session_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_list = search_students_without_session(user_login_view, institute_unique_code, request.GET.get("text",""), "ENROLLED")
		return render(request, 'admission/relieve_student/student_list.html',{"results":student_list})


def relieve_student_status_view(request, institute_unique_code, student_id, status_change_type):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_relieve_status = get_relieve_student_status(user_login_view, institute_unique_code, student_id)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		current_date = date.today()
		todays_date = current_date.strftime("%d-%b-%Y")
		return render(request, 'admission/relieve_student/relieve_student_status.html',{"student_relieve_status":student_relieve_status,'status_change_type':status_change_type, 'academic_years':academic_years, 'current_session':current_session,'date':todays_date})


@csrf_exempt
def relieve_student_view(request, institute_unique_code, student_id, status_change_type, updating):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		relieve_payload = json.loads(request.POST['relievePayload'])
		relieved = relieve_student(user_login_view, institute_unique_code, student_id, status_change_type, relieve_payload)
		if updating:
			return render(request, 'admission/relieve_student/relieve_student_success_modal.html',{"success":relieved['success'], "message": relieved['message'], 'status_change_type':status_change_type})
		return render(request, 'admission/relieve_student/update_relieved_status.html',{"success":relieved['success'], "message": relieved['message'], 'status_change_type':status_change_type})


@csrf_exempt
def enroll_student_view(request, institute_unique_code, student_id, status_change_type):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		enrolled = enroll_student(user_login_view, institute_unique_code, student_id, status_change_type)
		return render(request, 'admission/relieve_student/enroll_student_success_modal.html',{"success":enrolled, 'status_change_type':status_change_type})

def generate_document_view(request, institute_unique_code, academic_session_id, student_id, document_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = None
		if(document_type == "ADMISSION_FORM"):
			document = get_admission_form_pdf(user_login_view, institute_unique_code, student_id, academic_session_id)
		# elif(document_type == "IDENTITY_CARD"):
		# 	academic_years = get_academic_years(user_login_view, institute_unique_code)
		# 	current_session = get_current_academic_year(request, academic_years)
		# 	document = get_student_identitycard(user_login_view, institute_unique_code, current_session['academicSessionId'], student_id)
		elif(document_type == "BOARD_REGISTRATION_FORM"):
			document = get_board_registration_form_pdf(user_login_view, institute_unique_code, student_id, academic_session_id)
		elif(document_type == "STUDY_CERTIFICATE"):
			document = get_study_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "PROMOTION_CERTIFICATE"):
			document = get_promotion_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "CHARACTER_CERTIFICATE"):
			document = get_character_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "TUITION_FEES_CERTIFICATE"):
			document = get_tuition_fees_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "BIRTHDAY_CERTIFICATE"):
			document = get_birthday_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "BOOKS_RECEIPT"):
			document = get_books_receipt(user_login_view, institute_unique_code, academic_session_id, student_id)
		elif(document_type == "BONAFIDE_CERTIFICATE"):
			document = get_bonafide_certificate(user_login_view, institute_unique_code, academic_session_id, student_id)
		else:
			return HttpResponse("<strong>Invalid document type.</strong>")
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+document['file_name']
			return response
		return HttpResponse("<strong>Invalid information submitted.</strong>")


def generate_static_document_view(request, institute_unique_code, academic_session_id, document_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = None
		if(document_type == "ADMISSION_FORM"):
			document = get_static_admission_form_pdf(user_login_view, institute_unique_code, academic_session_id)
		else:
			return HttpResponse("<strong>Invalid document type.</strong>")
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+document['file_name']
			return response
		return HttpResponse("<strong>Invalid information submitted.</strong>")


@csrf_exempt
def document_upload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' and request.FILES['document']:
		user_login_view = get_user_login_view(request)
		document = request.FILES['document']
		student_id = request.POST['studentId']
		document_type = request.POST['documentType']
		document_name = request.POST['documentName']

		response = upload_student_document(user_login_view, institute_unique_code, student_id, document_type, document_name, document)
		return render(request, 'admission/documents/document_upload_status_modal.html',{'response' : response})

def document_download_view(request, institute_unique_code, student_id, document_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = download_student_document(user_login_view, institute_unique_code, student_id, document_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/octet-stream")
			response['Content-Disposition'] = 'inline; filename='+document['file_name']
			return response

@csrf_exempt
def document_delete_view(request, institute_unique_code, student_id, document_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response = delete_student_document(user_login_view, institute_unique_code, student_id, document_id)
		return render(request, 'admission/documents/document_delete_status_modal.html',{'response' : response})

@csrf_exempt
def relieved_student_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/relieve_student/relieve_student_details.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': True, 'institute_metadata' : institute_metadata})

@csrf_exempt
def relieved_student_details_reload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/relieve_student/relieve_student_details.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': True, 'institute_metadata' : institute_metadata})

def reports_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		headers = get_report_headers(user_login_view, institute_unique_code, module_name)
		header_columns = get_report_headers_for_display(headers)
		report_header_columns = set_order(header_columns)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		religion = get_religion(user_login_view, institute_unique_code)
		instituteHouse = get_institute_houses_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'admission/reports/main_screen.html',{'academic_years': academic_years, 'standards' : standards, 'report_header_columns' : report_header_columns, 'religion' : religion, 'instituteHouse' : instituteHouse})

def set_order(header_columns):
    report_header_columns = {}
    for key in header_columns:
        student_basic_info = []
        student_family_info = []
        student_guardian_info = []
        student_previous_school_info = []
        student_medical_info = []
        student_session_info = []
        student_user_info = []
        student_tc_info = []

        for rows in header_columns[key]:
            for row in rows:
                if row.get('parentCategory') == "StudentBasicInfo":
                    student_basic_info.append(row)
                elif row.get('parentCategory') == "StudentFamilyInfo":
                    student_family_info.append(row)
                elif row.get('parentCategory') == "StudentGuardianInfo":
                    student_guardian_info.append(row)
                elif row.get('parentCategory') == "StudentPreviousSchoolInfo":
                    student_previous_school_info.append(row)
                elif row.get('parentCategory') == "StudentMedicalInfo":
                    student_medical_info.append(row)
                elif row.get('parentCategory') == "StudentSessionInfo":
                    student_session_info.append(row)
                elif row.get('parentCategory') == "StudentUserInfo":
                    student_user_info.append(row)
                elif row.get('parentCategory') == "StudentTCInfo":
                    student_tc_info.append(row)

        report_header_columns[key] = {'StudentBasicInfo': student_basic_info, 'StudentFamilyInfo': student_family_info, 'StudentGuardianInfo': student_guardian_info, 'StudentPreviousSchoolInfo': student_previous_school_info, 'StudentMedicalInfo': student_medical_info, 'StudentSessionInfo': student_session_info, 'StudentUserInfo': student_user_info, 'StudentTcInfo': student_tc_info}
    return report_header_columns

# def generate_reports_view(request, institute_unique_code):
# 	if authorized_user_session(request):
# 		user_login_view = get_user_login_view(request)
# 		report_request = {"report_type":request.GET.get("reportType",""), "academic_session_id":request.GET.get("academicSession",0),"required_headers":request.GET.get("requiredHeaders",""), 'required_standards' : request.GET.get("requiredStandards",""),'download_format' : request.GET.get("downloadFormat",""),'student_status' : request.GET.get("studentStatus",""),'new_admission' : request.GET.get("newAdmission","") }
# 		filterationCriteria = getFilterationCriteria(request)
# 		report = get_admission_report(user_login_view, institute_unique_code, report_request, filterationCriteria)
# 		if(report['success']):
# 			response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
# 			response['Content-Disposition'] = 'inline; filename='+report["file_name"]
# 			return response
# 		else:
# 			return render(request, 'admission/reports/status.html',{"data":report})

def reports_session_change_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'admission/reports/standard_dropdown.html',{'standards' : standards})


def generate_report_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		report_request = {"report_type":request.GET.get("reportType",""), "academic_session_id":academic_session_id,"required_headers":request.GET.get("requiredHeaders",""), 'required_standards' : request.GET.get("requiredStandards",""),'download_format' : request.GET.get("downloadFormat",""),'student_status' : request.GET.get("studentStatus",""),'final_student_status' : request.GET.get("finalStudentStatus",""),'new_admission' : request.GET.get("newAdmission",""),"document_types":request.GET.get("documentTypes",""),"document_status":request.GET.get("documentStatus",""),"download_format" : downloadFormat }
		filterationCriteria = getFilterationCriteria(request)
		if download_report :
			report = get_admission_reports(user_login_view, institute_unique_code, report_request, filterationCriteria)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_admission_report_data(user_login_view, institute_unique_code, report_request, filterationCriteria)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL"
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF"

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})


# def generate_student_report_view(request, institute_unique_code):
# 	if authorized_user_session(request):
# 		user_login_view = get_user_login_view(request)
# 		downloadFormat = request.GET.get("downloadFormat")
# 		report_request =  {"academic_session_id":request.GET.get("academicSession",0), "report_type":request.GET.get("reportType",""),"download_format":downloadFormat}
# 		report = get_student_reports(user_login_view, institute_unique_code, report_request)
# 		if(downloadFormat == "PDF"):
# 			if(report['success']):
# 				response = HttpResponse(report['content'], content_type="application/pdf")
# 				response['Content-Disposition'] = 'filename='+report['file_name']
# 				return response
# 		else:
# 			if(report['success']):
# 				response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
# 				response['Content-Disposition'] = 'inline; filename='+report["file_name"]
# 				return response
#
# 		return HttpResponse("<strong>"+report['error_reason']+"</strong>")


def getFilterationCriteria(request):
    rte_csv = request.GET.get("rte")
    category_csv = request.GET.get("category")
    gender_csv = request.GET.get("gender")
    religion_csv = request.GET.get("religion")
    areaType_csv = request.GET.get("area_type")
    speciallyAbled_csv = request.GET.get("specially_abled")
    bpl_csv = request.GET.get("bpl")
    state_csv = request.GET.get("state")
    house_csv = request.GET.get("instituteHouseId")
    startDate = request.GET.get("startDate")
    endDate = request.GET.get("endDate")
    sortBy = request.GET.get("sortBy")

    rte = None
    if rte_csv.strip():
        rte = rte_csv
    category = None
    if category_csv.strip():
        category = category_csv.split(',')
    gender = None
    if gender_csv.strip():
        gender = gender_csv.split(',')
    religion = None
    if religion_csv.strip():
        religion = religion_csv.split(',')
    areaType = None
    if areaType_csv.strip():
        areaType = areaType_csv.split(',')
    speciallyAbled = None
    if speciallyAbled_csv.strip():
        speciallyAbled = speciallyAbled_csv
    bpl = None
    if bpl_csv.strip():
        bpl = bpl_csv
    state = None
    if state_csv.strip():
        state = state_csv.split(',')
    instituteHouseId = None
    if house_csv.strip():
        instituteHouseId = house_csv.split(',')
    studentSortingParameters = None
    if sortBy:
        studentSortingParameters = sortBy

    filterationCriteria	= {'rte' : rte, 'category' : category, 'gender' : gender, 'religion' : religion,
    	'areaType' : areaType, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl, 'state' : state, 'instituteHouseId' : instituteHouseId, 'startDate' : startDate, 'endDate' : endDate, 'studentSortingParameters' : studentSortingParameters }
    return filterationCriteria


@csrf_exempt
def pending_enrolments_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		restrictEnrollmentPaymentPending = institute_metadata_preferences['restrictEnrollmentPaymentPending']
		return render(request, 'admission/student_details_wrapper.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': False, 'institute_metadata':institute_metadata, 'restrictEnrollmentPaymentPending' : restrictEnrollmentPaymentPending })

@csrf_exempt
def pending_enrolments_details_reload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, current_session['academicSessionId'])
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		institute_metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
		restrictEnrollmentPaymentPending = institute_metadata_preferences['restrictEnrollmentPaymentPending']
		return render(request, 'admission/student_details_wrapper.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards,'enrolled': False, 'institute_metadata':institute_metadata, 'institute_houses_with_count' : institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str, 'restrictEnrollmentPaymentPending' : restrictEnrollmentPaymentPending})

@csrf_exempt
def admit_student_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		admission_details = json.loads(request.POST['admissionDetails'])
		response_data = admit_student(user_login_view, institute_unique_code, admission_details)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})


def student_tc_pdf_view(request, institute_unique_code, student_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		student_tc_payload = get_student_tc_payload(request)
		tc_pdf = get_student_tc_pdf(user_login_view, institute_unique_code, student_id, student_tc_payload)
		if(tc_pdf['success']):
			response = HttpResponse(tc_pdf['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+tc_pdf['file_name']
			return response
		return render(request, 'admission/reports/status.html',{"data":tc_pdf})

def generate_student_tc_pdf_view(request, institute_unique_code, student_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		tc_pdf = generate_student_tc_pdf(user_login_view, institute_unique_code, student_id)
		if(tc_pdf['success']):
			response = HttpResponse(tc_pdf['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+tc_pdf['file_name']
			return response
		return render(request, 'admission/reports/status.html',{"data":tc_pdf})

def get_student_tc_payload(request):

	student_tc_payload = {}

	student_tc_payload['bookNumber'] = request.GET.get("book_number","")
	student_tc_payload['tcNumber'] = request.GET.get("tc_number","")
	student_tc_payload['tcnumberFixed'] = request.GET.get("is_tc_number_fixed",False)
	student_tc_payload['tcGenerationDate'] = request.GET.get("tc_generation_date",0)

	student_tc_payload['tcSchoolDetails'] = {}
	student_tc_payload['tcSchoolDetails']['affiliationNumber'] = request.GET.get("affiliation_number","")
	student_tc_payload['tcSchoolDetails']['diseCode'] = request.GET.get("dise_code","")
	student_tc_payload['tcSchoolDetails']['schoolCode'] = request.GET.get("school_code","")
	student_tc_payload['tcSchoolDetails']['primaryEducationAffiliationNumber'] = request.GET.get("primary_education_affiliation_number","")
	student_tc_payload['tcSchoolDetails']['rteAffiliationNumber'] = request.GET.get("rte_affiliation_number","")

	student_tc_payload['tcStudentDetails'] = {}
	student_tc_payload['tcStudentDetails']['admissionNumber'] = request.GET.get("admission_number","")
	student_tc_payload['tcStudentDetails']['studentName'] = request.GET.get("student_name","")
	student_tc_payload['tcStudentDetails']['admissionDate'] = request.GET.get("admission_date",0)
	student_tc_payload['tcStudentDetails']['admissionClass'] = request.GET.get("admission_class","")
	student_tc_payload['tcStudentDetails']['fatherGuardianName'] = request.GET.get("father_guardian_name","")
	student_tc_payload['tcStudentDetails']['motherName'] = request.GET.get("mother_name","")
	student_tc_payload['tcStudentDetails']['proofOfDoBAtTheTimeOfAdmission'] = request.GET.get("proof_of_dob_at_the_time_of_admission","")
	student_tc_payload['tcStudentDetails']['dob'] = request.GET.get("dob",0)
	student_tc_payload['tcStudentDetails']['apaarIdNo'] = request.GET.get("apaar_id_number","")
	student_tc_payload['tcStudentDetails']['livingTimeInCurrectCity'] = request.GET.get("living_time_in_currect_city","")

	
	category_val = request.GET.get("category",None)
	if(category_val != None and len(category_val) == 0):
		category_val = None
	student_tc_payload['tcStudentDetails']['category'] = category_val
	student_tc_payload['tcStudentDetails']['nationality'] = request.GET.get("nationality","")

	student_tc_payload['tcStudentLastActiveSessionDetails'] = {}
	student_tc_payload['tcStudentLastActiveSessionDetails']['lastActiveSessionClass'] = request.GET.get("last_active_session_class","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['lastActiveSessionClassInFigures'] = request.GET.get("last_active_session_class_in_figures","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['lastActiveSessionClassInWords'] = request.GET.get("last_active_session_class_in_words","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['lastExamTakenWithResult'] = request.GET.get("last_exam_taken_with_result","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['numberOfTimeExamFailed'] = request.GET.get("number_of_time_exam_failed","")
	scholasticCoursesLastActiveSessionStr = request.GET.get("scholastic_courses_last_active_session_str","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['scholasticCoursesLastActiveSession'] = scholasticCoursesLastActiveSessionStr.split(",")
	student_tc_payload['tcStudentLastActiveSessionDetails']['promotionToHigherClass'] = request.GET.get("promotion_to_higher_class","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['promotingClassName'] = request.GET.get("promoting_class_name","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['promotingClassNameInFigures'] = request.GET.get("promoting_class_name_in_figures","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['promotingClassNameInWords'] = request.GET.get("promoting_class_name_in_words","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['lastFeesPaid'] = request.GET.get("last_fees_paid","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['discountWithNature'] = request.GET.get("discount_with_nature","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['totalWorkingDays'] = request.GET.get("total_working_days","")
	student_tc_payload['tcStudentLastActiveSessionDetails']['totalAttendedDays'] = request.GET.get("total_attended_days","")

	student_tc_payload['tcOtherDetails'] = {}
	student_tc_payload['tcOtherDetails']['nccCadetBoyScoutGirlGuideWithDetails'] = request.GET.get("ncc_cadet_boy_scout_girl_guide_with_details","")
	student_tc_payload['tcOtherDetails']['coCurricularActivities'] = request.GET.get("co_curricular_activities","")

	student_tc_payload['tcStudentRelievingDetails'] = {}
	student_tc_payload['tcStudentRelievingDetails']['codeOfConduct'] = request.GET.get("code_of_conduct","")
	student_tc_payload['tcStudentRelievingDetails']['relieveDate'] = request.GET.get("relieve_date",0)
	student_tc_payload['tcStudentRelievingDetails']['relieveReason'] = request.GET.get("relieve_reason","")
	student_tc_payload['tcStudentRelievingDetails']['remarks'] = request.GET.get("remarks","")
	student_tc_payload['tcStudentRelievingDetails']['dateOfApplicationOfCertificate'] = request.GET.get("date_of_application_of_certificate",0)
	student_tc_payload['tcStudentRelievingDetails']['datePupilsNameStuckOffTheRolls'] = request.GET.get("date_pupils_name_stuck_off_the_rolls",0)
	prev_session_details_list = request.GET.get("prev_session_details_list", None)
	if prev_session_details_list:
			student_tc_payload['tcPreviousSessionDetailSummaryList'] = json.loads(prev_session_details_list)
	else:
		student_tc_payload['tcPreviousSessionDetailSummaryList'] = None
	return student_tc_payload


def get_default_fee_assignment_structure_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		default_fee_assignment_structure = get_default_fee_assignment_structure(user_login_view, institute_unique_code, academic_session_id,standard_id, "REGISTRATION")
		return render(request, 'admission/create_student_confirm_modal.html',{"default_fee_assignment_structure":default_fee_assignment_structure})


def student_registration_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'admission/student_registration/main_screen.html',{'academic_years':academic_years, 'current_session':current_session})


def student_registration_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		search_text = request.GET.get("search_text",0)
		offset = request.GET.get("offset",0)
		items_per_page = request.GET.get("itemsPerPage",5)
		status = request.GET.get("status","")
		registration_students = search_registration_students(user_login_view, institute_unique_code, academic_session_id, search_text, status, offset, items_per_page)
		pagination_details = get_pagination_details(registration_students['paginationInfo'])
		return render(request, 'admission/student_registration/student_list.html',{"registration_students_list" : registration_students['result'], "pagination_details" : pagination_details, 'pagination_info' : registration_students['paginationInfo']})

def student_registration_enrollment_form(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		default_fee_assignment_structure = get_default_fee_assignment_structure(user_login_view, institute_unique_code, academic_session_id,standard_id, "REGISTRATION")
		return render(request, 'admission/student_registration/enroll_student_form.html',{'institute_metadata':institute_metadata, 'default_fee_assignment_structure':default_fee_assignment_structure})

@csrf_exempt
def student_registration_apply_action_view(request, institute_unique_code, registration_id, action):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		registration_details = None
		if action == "APPROVED":
			registration_details = json.loads(request.POST['registrationDetails'])
		response_data = apply_student_registration_action(user_login_view, institute_unique_code, registration_id, action, registration_details)
		return render(request, 'admission/student_registration/status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})


def identitycard_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		standards = get_standards_without_section(user_login_view, institute_unique_code)
		return render(request, 'admission/identitycard/main_screen.html',{'academic_years': academic_years, 'standards' : standards})


def generate_identitycard_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		identitycards = get_class_identitycards(user_login_view, institute_unique_code, academic_session_id, standard_id)
		if(identitycards is not None):
			response = HttpResponse(identitycards['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+identitycards['file_name']
			return response
		return HttpResponse("<strong>Invalid Information.</strong>")

def generate_study_certificate_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		report_card = get_study_certificate(user_login_view, institute_unique_code, session_id, student_id)
		if(report_card is not None):
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+report_card['file_name']
			return response
		return HttpResponse("<strong>Invalid Information.</strong>")

def get_session_standards_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'admission/standard_json.html',{'standards':standards})

def get_institute_houses_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_houses_with_count = get_institute_houses_with_count(user_login_view, institute_unique_code, academic_session_id)
		student_houses_stats_str = get_student_houses_stats_str(institute_houses_with_count)
		return render(request, 'admission/institute_houses_info.html',{'institute_houses_with_count':institute_houses_with_count, 'student_houses_stats_str' : student_houses_stats_str})

def get_siblings_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		status = request.GET.get("status","")
		sibling_detail_list = get_sibling_list_with_session(user_login_view, institute_unique_code, "", current_session['academicSessionId'], status)
		add_viewing_data(sibling_detail_list)
		return render(request, 'admission/siblings/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'sibling_detail_list' : sibling_detail_list})

def add_viewing_data(sibling_detail_list):
	for sibling_detail in sibling_detail_list:
		first = True
		text = ""
		for student in sibling_detail['studentList']:
			if first:
				first = False
			else:
				text += ", "
			text += student['name'] + " (" + student['admissionNumber'] + ", " + student['studentSessionData']['standardNameWithSection'] + ", " + student['studentStatus'].capitalize() + ")"
		sibling_detail['viewingText'] = text

def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# students = search_lite_students_without_session(user_login_view, institute_unique_code, request.GET.get("searchText",""), request.GET.get("status",""))
		students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), "", request.GET.get("status",""))
		students = removeStudentWithSiblings(students['result'])
		return render(request, 'core/student/live_search_student_list.html',{"students":students})

def removeStudentWithSiblings(students):
	finalStudentList = []
	if students:
		for student in students:
			if not student['siblingGroupId']:
				finalStudentList.append(student)
	student_result = {'result' : finalStudentList}
	return student_result

@csrf_exempt
def add_siblings_view(request, institute_unique_code, is_student_level_action):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		student_sibling_payload = json.loads(request.POST['studentSiblingPayload'])
		response_data = add_siblings(user_login_view, institute_unique_code, is_student_level_action, student_sibling_payload)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})

def get_siblings_list_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		sibling_detail_list = get_sibling_list_with_session(user_login_view, institute_unique_code, request.GET.get("text",""), academic_session_id, status)
		add_viewing_data(sibling_detail_list)
		return render(request, 'admission/siblings/siblings_list.html',{'sibling_detail_list' : sibling_detail_list})

@csrf_exempt
def update_siblings_view(request, institute_unique_code, is_student_level_action):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		student_sibling_payload = json.loads(request.POST['studentSiblingPayload'])
		response_data = update_siblings(user_login_view, institute_unique_code, is_student_level_action, student_sibling_payload)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})

@csrf_exempt
def delete_sibling_details_view(request, institute_unique_code, sibling_group_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_sibling_details(user_login_view, institute_unique_code, sibling_group_id)
		return render(request, 'admission/create_student_status_modal.html',{"data":response_data, 'dataJson' : json.dumps(response_data)})

def get_student_tc_details_view(request, institute_unique_code, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_tc_details = get_student_tc_details(user_login_view, institute_unique_code, student_id, False)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/tc/tc_form.html',{'student_tc_details' : student_tc_details, 'studentId' : student_id, 'institute_metadata' : institute_metadata})

def get_student_tc_field_details_view(request, institute_unique_code, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_tc_details = get_student_tc_details(user_login_view, institute_unique_code, student_id, True)
		institute_metadata = get_institute_metadata(user_login_view, institute_unique_code)
		return render(request, 'admission/tc/tc_fields_details.html',{'student_tc_details' : student_tc_details, 'studentId' : student_id, 'institute_metadata' : institute_metadata})

def student_live_search_with_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		if(status == 'undefined'):
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""))
		else:
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), "", request.GET.get("status",""))
		return render(request, 'core/student/live_search_student_list.html',{"students":students})

def student_sibling_details_view(request, institute_unique_code, academic_session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_sibling_details = get_student_sibling_details_with_session(user_login_view, institute_unique_code, student_id, academic_session_id)
		student_details = get_student(user_login_view, institute_unique_code, academic_session_id, student_id, False)
		return render(request, 'admission/pending_enrolments/student_sibling_details.html',{'student_sibling_details' : student_sibling_details, 'student_details' : student_details})


def enquiry_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'admission/enquiry_details/main_screen.html',{'academic_years':academic_years, 'current_session':current_session})

def get_session_enquiry_details_view(request, institute_unique_code, academic_session_id, status, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		all_enquiry_details_with_pagination_info = get_session_enquiry_details(user_login_view, institute_unique_code, academic_session_id, status, offset, limit, request.GET.get("filter_date",0))
		pagination_info = all_enquiry_details_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)
		return render(request, 'admission/enquiry_details/enquiry_details_list.html',{'pagination' : True, 'enquiry_details' : all_enquiry_details_with_pagination_info['enquiry_details'], "pagination_details" : pagination_details, "status" : status})


@csrf_exempt
def update_enquiry_status_view(request,institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' :
		user_login_view = get_user_login_view(request)
		enquiry_details_payload = json.loads(request.POST['enquiryDetailsPayload'])
		response_data = update_enquiry_status(user_login_view, institute_unique_code, enquiry_details_payload)
		return render(request, 'admission/enquiry_details/status_modal.html',{"data":response_data})


def get_session_enquiry_detail_view(request, institute_unique_code, academic_session_id, enquiry_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		enquiry_details = get_enquiry_detail_with_enquiry_id(user_login_view, institute_unique_code, academic_session_id, enquiry_id)
		return render(request, 'admission/enquiry_details/enquiry_detail.html',{'enquiry_details' : enquiry_details})

def enquiry_student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), "", request.GET.get("status",""))
		return render(request, 'admission/enquiry_details/student_list.html',{"students":students})

@csrf_exempt
def add_followup_view(request,institute_unique_code,session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' :
		user_login_view = get_user_login_view(request)
		follow_up_payload = json.loads(request.POST['followUpPayload'])
		response_data = add_enquiry_followup(user_login_view, institute_unique_code, session_id,follow_up_payload)
		return render(request, 'admission/enquiry_details/status_modal.html',{"data":response_data})

@csrf_exempt
def update_followup_view(request,institute_unique_code,session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' :
		user_login_view = get_user_login_view(request)
		update_follow_up_payload = json.loads(request.POST['updateFollowUpPayload'])
		response_data = update_enquiry_followup(user_login_view, institute_unique_code, session_id,update_follow_up_payload)
		return render(request, 'admission/enquiry_details/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_followup_view(request,institute_unique_code,academic_session_id,follow_up_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_enquiry_followup(user_login_view, institute_unique_code, academic_session_id,follow_up_id)
		return render(request, 'admission/enquiry_details/status_modal.html',{"data":response_data})





