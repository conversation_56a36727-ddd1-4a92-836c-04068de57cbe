{% load json %}
<div class="row mb-2 mb-xl-3" id="lesson-plan-academic-session-div">
    {% include 'core/utils/v2/academic_session_display.html'%}
    <div class="col-auto ml-auto text-right mt-n1">
        {% include 'core/utils/v2/academic_session_dropdown.html'%}
    </div>
</div>
<div id="lesson-plan-form-navbar" style="display : none;">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <i class="bi bi-chevron-left" onclick="lessonPlanPage.back()" style="cursor:pointer; margin-right:0.2rem;"></i>
            <li class="breadcrumb-item" id="lesson-plan-course-name"></li>
            <li class="breadcrumb-item active" aria-current="page" id="lesson-plan-standard-name"></li>
        </ol>
    </nav>
</div>
<div class="card">
    <div id="lesson-plan-main-container" class="card-body">
        {% include 'lesson_plan/course_details_lesson_plan/course_and_lesson_plan_details_staff.html' %}
    </div>
    <div id="lesson-plan-staff-response-detail-container" class="card-body" style="display: none;">
    </div>
</div>
 <div class="modal fade" id="lesson-plan-delete-warning-popup" tabindex="-1" role="dialog" aria-labelledby="exampleCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleLongTitle">Delete Lesson Plan Infomation </h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <span style="color:red" id="lesson-plan-delete-warning-popup-text">Do you want to delete lesson plan details?</span>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-success" data-dismiss="modal">No</button>
            <button type="button" class="btn btn-danger" id="lesson-plan-delete-confirm-button">Yes, delete</button>
        </div>
      </div>
    </div>
</div>
<div id="lesson-plan-status-modal-container"></div>