var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;
var graphLoadTimer; //timer identifier
var graphLoadInterval = 100; //time in ms
var STAFF_IMAGE_SIZE_LIMIT = 500;
var regexEmail = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
var regexPhoneNumber = /^\d{10}$/;
var regexAadharNumber = /^\d{12}$/;
var regexPANNumber = /[a-zA-z]{5}\d{4}[a-zA-Z]{1}/;
var OTHER_DOCUMENT_TYPE = "OTHER";
var IN_TIME = "In Time";
var OUT_TIME = "Out Time";
var HALF_DAY_DURATION = "Half Day Duration(in hrs)";
var FULL_DAY_DURATION = "Full Day Duration(in hrs)";
var daysOfWeekMap = new Map([
                 ["MONDAY", 0],
                 ["TUESDAY", 1],
                 ["WEDNESDAY", 2],
                 ["THURSDAY", 3],
                 ["FRIDAY", 4],
                 ["SATURDAY", 5],
                 ["SUNDAY", 6]
      ]);



$(document).ready(function() {
  menuLoader.registerSidebarMenu();
  // homePage.initHomePage();

  var urlParams = new URLSearchParams(window.location.search);
  actionType = urlParams.get(ACTION_TYPE);

  if(actionType === "" || actionType === null) {
      homePage.initHomePage();
  }
  else if (actionType === "student-dossier"){
    $(".sidebar-nav").find('.sidebar-item').each(function() {
      $(this).removeClass("active");
    });
    // $("#paymentNav").parent().addClass("active");
    homePage.initHomePage();


    //remove params from url
    var url_string = window.location.href;
    url = removeURLParameter(url_string, ACTION_TYPE)
    let stateObj = { id: "100" };
    window.history.replaceState(stateObj, url_string, url);
  }

  paymentReminder.readPaymentState();
});

var menuLoader = {

  registerSidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.registerHomeMenu();
      menuLoader.registerConfigurationMenu();
      menuLoader.registerAddStaffMenu();
      menuLoader.registerJoinerMenu();
      menuLoader.registerOnboardMenu();
      menuLoader.registerBulkUpdateMenu();
      menuLoader.registerRelieveMenu();
      menuLoader.registerReportsMenu();
  },

  registerHomeMenu : function () {
      $('#homeNav').on('click', function() {
          homePage.loadHomePage();
      });
  },

  registerConfigurationMenu : function () {
      $('#configurationNav').on('click', function() {
          configuration.loadConfigurationMainPage();
      });
  },

  registerAddStaffMenu : function () {
      $('#addStaffNav').on('click', function() {
          admitStaff.loadAdmitStaffMenu();
      });
  },

  registerJoinerMenu : function () {
      $('#joinerNav').on('click', function() {
          joinerStaff.loadJoinerStaffMainPage();
      });
  },

  registerOnboardMenu : function () {
      $('#onboardNav').on('click', function() {
          onboardStaff.loadOnboardStaffMainPage();
      });
  },

  registerBulkUpdateMenu : function () {
      $('#bulkUpdateNav').on('click', function() {
          bulkUpdateStaff.loadUpdateStaffMainPage();
      });
  },

  registerRelieveMenu : function () {
      $('#relieveNav').on('click', function() {
          relieveStaff.loadRelieveStaffMainPage();
      });
  },

  registerReportsMenu : function () {
      $('#reportsNav').on('click', function() {
          staffReports.loadReportsMenu();
      });
  },

};

var homePage =  {

    initHomePage : function () {
      academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        homePage.displayDashboardContent();
        homePage.onClickFeeStatsSwitchButton();
    },

    loadHomePage : function () {
      ajaxClient.get("/staff-management/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      ajaxClient.get("/staff-management/session-home", function(data) {
          $("#staffmanagement-dashboard-session-content").html(data);
          homePage.displayDashboardContent();
          homePage.onClickFeeStatsSwitchButton();
      });
    },

    onClickFeeStatsSwitchButton : function () {
      $("#staff-stats-switch-button").on('click', function () {
        var currentCardViewType = $("#current-card-view-type").text();
        if(currentCardViewType === TABULAR) {
          $("#current-card-view-type").text(GRAPHICAL);
          $("#catogorywise-graph-header-text").text("Staff Distribution Category Wise (Graphical View)");
          $(this).html("Tabular View");
          homePage.displayDashboardContent();
          $("#tabularview-catogorywise-staff-distribution").attr("style", "display:none;");
          $("#chartjs-staff-distribution").attr("style", "display:block;");
        } else {
          $("#current-card-view-type").text(TABULAR);
          $("#catogorywise-graph-header-text").text("Staff Distribution Category Wise (Tabular View)");
          $(this).html("Graphical View");
          homePage.renderBarGraphTabularView();
          $("#chartjs-staff-distribution").attr("style", "display:none;");
          $("#tabularview-catogorywise-staff-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
        }
      });
    },

    loadStaffList : function( inputStaffMapKey , inputStaffStatus) {
      var staffstats = readJson("#home-page-stats");
      if(staffstats == null){
        return;
      }      
      var tabularViewHtml = "<table id=\"datatables-reponsive\" class=\"table table-bordered table-striped datatables-reponsive-table\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Sr No.</th><th scope=\"col\">Staff ID</th><th scope=\"col\">Staff Name</th><th scope=\"col\">Gender</th><th scope=\"col\">Primary Contact Number</th><th scope=\"col\">Primary Email</th><th scope=\"col\">Category</th><th scope=\"col\">Department (Designation)</th></tr></thead><tbody>";
      var staffDetails;
      if (inputStaffMapKey !== null) {
        if (staffstats.categoryWiseStaffDetailsMap.hasOwnProperty(inputStaffMapKey)) {
            staffDetails = staffstats.categoryWiseStaffDetailsMap[inputStaffMapKey];
        } else if (staffstats.genderWiseStaffDetailsMap.hasOwnProperty(inputStaffMapKey)) {
            staffDetails = staffstats.genderWiseStaffDetailsMap[inputStaffMapKey];
        }
      } else if(inputStaffStatus == "PENDING_STAFF") {
        staffDetails = staffstats.pendingStaffDetailsList;
      } else if(inputStaffStatus == "TRANSPORT_STAFF") {
        staffDetails = staffstats.transportStaffDetailsList;
      } else if(inputStaffStatus == "TOTAL_ONBOARD_STAFF"){
        staffDetails = staffstats.totalOnboardStaffDetailsList;
      }
      var srNo = 1;
      for( var i = 0 ; i < staffDetails.length ; i++){
        var staffId = staffDetails[i].staffInstituteId;
        var staffName = staffDetails[i].name;
        var gender = staffDetails[i].gender;
        var contactNumber = staffDetails[i].primaryContactNumber;
        var email = staffDetails[i].primaryEmail;
        var category = staffDetails[i].staffCategory.staffCategoryName;

        var departmentAndDesignation = "";
        var seprator = ""
        for (let j = 0; j<staffDetails[i].departmentDesignationNameList.length; j++){
          departmentAndDesignation += seprator + staffDetails[i].departmentDesignationNameList[j];
          seprator = ", ";
        }
        
        
        tabularViewHtml += "<tr><th scope=\"row\">" + srNo++ + "</th><td>" + staffId + "</td><td>" + staffName +"</td><td>" + gender + "</td> <td>" + contactNumber + "</td> <td>" + email + "</td> <td>" + category + "</td> <td>" + departmentAndDesignation + "</td></tr>";
      }
      tabularViewHtml += "</tbody></table>";
      $("#staff-data-display").html(tabularViewHtml);
      $("#display-staff-data-modal").modal({backdrop: 'static', keyboard: false});
    },

    renderBarGraphTabularView : function () {
      var staffStatsJson = $("#home-page-stats").text().trim();
      var staffStats = JSON.parse(staffStatsJson);
      if(staffStats == null || !staffStats.categoryWiseStaffDetailsMap){
        return;
      }
      var grandTotal = 0.0;
      var totalPercentage = 0.0;
      var totalStaff = staffStats.totalOnboardStaff;
      var tabularViewHtml = "<table id=\"datatables-reponsive\" class=\"table table-bordered table-striped datatables-reponsive-table\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Category</th><th scope=\"col\">Count</th><th scope=\"col\">Percentage %</th></tr></thead><tbody>";
      
      for (const [category, categoryData] of Object.entries(staffStats.categoryWiseStaffDetailsMap)) {
        var categoryId = category;
        var categoryName = categoryData[0].staffCategory.staffCategoryName;
        var categoryStaffCount = categoryData.length;
        var percentage = (categoryData.length / totalStaff) * 100;
        
        tabularViewHtml += "<tr><td scope=\"row\" style=\"cursor: pointer\" onclick='homePage.loadStaffList(\""+categoryId+"\", null)'>" +  categoryName + "</td><td style=\"cursor: pointer\" onclick='homePage.loadStaffList(\""+categoryId+"\", null)'>" + categoryStaffCount + "</td><td style=\"cursor: pointer\" onclick='homePage.loadStaffList(\""+categoryId+"\", null)'>" + percentage.toFixed(2) +"</td></tr>";
        
        grandTotal += categoryStaffCount;
        totalPercentage += percentage;
      }      

      tabularViewHtml += "<tr><th scope=\"row\" style=\"cursor: pointer\"onclick='homePage.loadStaffList(null,\""+'TOTAL_ONBOARD_STAFF'+"\")'>" + "Grand Total " + "</th><td style=\"cursor: pointer\"onclick='homePage.loadStaffList(null,\""+'TOTAL_ONBOARD_STAFF'+"\")'>" + grandTotal + "</td><td style=\"cursor: pointer\"onclick='homePage.loadStaffList(null,\""+'TOTAL_ONBOARD_STAFF'+"\")'>"+totalPercentage.toFixed(2)+"</td></tr>";
      tabularViewHtml += "</tbody></table>";
      $("#tabularview-catogorywise-staff-distribution").html(tabularViewHtml);
      },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

   displayDashboardContent : function () {

     var staffStatsJson = $("#home-page-stats").text().trim();
     var staffStats = JSON.parse(staffStatsJson);
     var staffCategory = staffStats.staffCategory;
     var staffCategoryMap = {};
     for(var i = 0; i < staffCategory.length; i++) {
       staffCategoryMap[staffCategory[i].staffCategoryId] = staffCategory[i];
     }
     var categoryLabelArr = [];
     var categoryDataArr = [];
     var totalOnboardStaff = staffStats.totalOnboardStaff;
     var pendingStaff = staffStats.totalPendingStaff;
     var transportStaff = staffStats.totalTransportStaff;
     var i = 0;
     var staffCategoryOnboardStaffCount = staffStats.staffCategoryOnboardStaffCount;

     $.each(staffCategoryOnboardStaffCount, function(key, value) {
        categoryLabelArr.push(staffCategoryMap[key].staffCategoryName);
        categoryDataArr.push(value);

     });

     var genderLabelArr = [];
     var genderDataArr = []
     var staffGenderWiseCounts = staffStats.staffGenderWiseCount;
     $.each(staffGenderWiseCounts, function(key, value) {
      genderLabelArr.push(key);
      genderDataArr.push(value);

   });

     $("#total-staff-count").text(totalOnboardStaff);
     $("#pending-staff").text(pendingStaff);
     $("#transport-staff").text(transportStaff);
     homePage.renderStaffCountChart(categoryLabelArr, categoryDataArr);
     homePage.renderFeeCollectionPieChart(genderLabelArr, genderDataArr);
   },

   renderStaffCountChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-staff-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Count",
           backgroundColor: window.theme.primary,
           borderColor: window.theme.primary,
           hoverBackgroundColor: window.theme.primary,
           hoverBorderColor: window.theme.primary,
           data: dataArr,
           barPercentage: .325,
           categoryPercentage: .5
         }]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             gridLines: {
               display: false
             },
             stacked: false,
             ticks: {
               stepSize: 20
             },
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

   renderFeeCollectionPieChart : function (labelArr, dataArr) {
     new Chart($("#chartjs-fee-collection-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: [
             window.theme.success,
             window.theme.warning,
             window.theme.danger,
             window.theme.info,
             window.theme.primary,
             "#108F2B",
             "#C08143 ",
             "#DCDF69",
             "#719E90",
             "#A8B9DF ",
             "#B74034",
             "#F196CD",
             "#6486B9",
             "#5551FA",
             "#E8F697",
             "#0591F6",
             "#4C6C42",
             "#442BC6",
           ],
           borderWidth: 5,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: true,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       }
     });
   },
};

function previousTabSwtichingEvent(){
  $('.previous-switch-tab').on('click', function () {
    var tabPrev = $('.switch-tab.active').prev();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabPrev.trigger("click");
    tabPrev.removeClass('disabled').addClass('active');
  });
}

function nextTabSwtichingEvent(){
  $('.next-switch-tab').on('click', function () {
    switchToNextTab();
  });
}

function switchToNextTab(){
    var tabNext = $('.switch-tab.active').next();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    tabNext.removeClass('disabled').addClass('active');
}

function swtichingTostaffUpdateFormFirstTab(){
    var tabNext = $('.switch-tab').first();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    tabNext.removeClass('disabled').addClass('active');
}

function closeModal(){
  clearMandatoryFieldsErrorDisplay();
}

function returnToMainScreen(staffStatus) {
  var screenVal = $('#screen-name').text().trim();
  if(screenVal == 'ADMIT-STAFF'){
    admitStaff.loadAdmitStaffMenu();
  } else{
    if(staffStatus == undefined) {
      staffStatus = $("#staff-status-screen").text().trim();
    }
    $('.staff-details-screen').attr('style','display:none');
    $('#staff-details').attr('style','display:block');
    swtichingTostaffUpdateFormFirstTab();
    if(staffStatus === "RELIEVED") {
      relieveStaff.loadRelieveStaffMainPage();
    }
    else if(staffStatus === "ONBOARD") {
      onboardStaff.loadOnboardStaffMainPage();
    }
    else {
      joinerStaff.loadJoinerStaffMainPage();
    }
  }
}

function fillStaffInformation(staffInfo) {
    $("#update-admission-staff-id").text(staffInfo.staffId);
    $("#update-staff-number").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffInstituteId);
    $("#update-staff-name").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.name);
    $("#update-staff-initials").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.initials);
    $('#update-staff-gender').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.gender);
    $('#update-staff-date-of-birth').val(getFormattedDate(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth));
    $('#update-user-staff-category').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.category);
    // if(staffInfo.staffImage != null){
    //     $('#update-staff-photo-label').html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>");
    // }else{
    //     $('#update-staff-photo-label').html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>");
    // }

    $('#update-staff-maritial-status').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.maritalStatus);
    $('#update-staff-father-name').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.fatherName);
    $('#update-staff-mother-name').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.motherName);
    $('#update-staff-birth-place').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.birthPlace);
    $("#update-staff-mother-tongue").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.motherTongue);
    $('#update-staff-religion').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.religion);
    $("#update-staff-specially-abled").prop("checked", staffInfo.staffBasicDetailsWithCategoryDepartDesignation.speciallyAbled);
    $("#update-staff-bpl").prop("checked", staffInfo.staffBasicDetailsWithCategoryDepartDesignation.bpl);
    $("#update-transport-staff").prop("checked", staffInfo.transportStaff);
    $('#update-staff-primary-email').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.primaryEmail);
    $('#update-staff-secondary-email').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.secondaryEmail);
    $('#update-staff-primary-contact-number').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.primaryContactNumber);
    $('#update-staff-alternate-contact-number').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.alternateContactNumber);
    $('#update-staff-aadhar-number').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.aadharNumber);
    $('#update-staff-pan-number').val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.panNumber);
    $("#update-staff-category").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffCategory.staffCategoryId);
    $("#update-staff-oasis-id").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.oasisId);
    $("#update-staff-national-code").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.nationalCode);
    $("#update-staff-appointment-type").val(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffNatureOfAppointment);

    var staffDepartment = JSON.parse($("#staff-department-json").text());
    var staffDesignation = JSON.parse($("#staff-designation-json").text());
    var len = Object.keys(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID).length;
    if(len > 0) {
      var html = "";
      var count = 1;
      $.each(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID, function(key, value) {
        var departmentHTML = getDepartmentHTML(staffDepartment, key);
        var designationHTML = getDesignationHTML(staffDesignation, value);
        if(len === 1) {
          html += "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control update-staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control update-staff-designation\">" + designationHTML + "</select></div><div class=\"form-group\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.addNewDepartmentDesignationUpdateRow(this);\"><span aria-hidden=\"true\">+</span></button></div></div>";
        }
        else if(count < len) {
          html  += "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control update-staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control update-staff-designation\">" + designationHTML + "</select></div><div class=\"form-group\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.addNewDepartmentDesignationUpdateRow(this);\"><span aria-hidden=\"true\">+</span></button></div></div></div>";
        } else {
            html += "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control update-staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control update-staff-designation\">" + designationHTML + "</select></div><div class=\"form-group remove-class\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.removeDepartmentDesignationRow(this);\"><span aria-hidden=\"true\">x</span></button></div></div>";
        }
        count = count + 1;
      });
      $("#basic-close-btn").before(html);
    } else {
      var departmentHTML = admitStaff.getDepartmentHTML(staffDepartment);
      var designationHTML = admitStaff.getDesignationHTML(staffDesignation);

      var html = "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control update-staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control update-staff-designation\">" + designationHTML + "</select></div><div class=\"form-group\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.addNewDepartmentDesignationUpdateRow(this);\"><span aria-hidden=\"true\">+</span></button></div><div class=\"form-group remove-class\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.removeDepartmentDesignationRow(this);\"><span aria-hidden=\"true\">x</span></button></div></div>";

      $("#basic-close-btn").before(html);
    }

    $("#staff-present-address-1").val(staffInfo.staffAddressContactInfo.presentAddress1);
    $("#staff-present-address-2").val(staffInfo.staffAddressContactInfo.presentAddress2);
    $("#staff-present-city").val(staffInfo.staffAddressContactInfo.presentCity);
    $('#staff-present-state').find(".country-states").val(staffInfo.staffAddressContactInfo.presentState);
    $('#staff-present-country').find(".country").val(staffInfo.staffAddressContactInfo.presentCountry);
    $("#staff-present-zipcode").val(staffInfo.staffAddressContactInfo.presentZipcode);
    $("#staff-permanent-address-1").val(staffInfo.staffAddressContactInfo.permanentAddress1);
    $("#staff-permanent-address-2").val(staffInfo.staffAddressContactInfo.permanentAddress2);
    $("#staff-permanent-city").val(staffInfo.staffAddressContactInfo.permanentCity);
    $('#staff-permanent-state').find(".country-states").val(staffInfo.staffAddressContactInfo.permanentState);
    $('#staff-permanent-country').find(".country").val(staffInfo.staffAddressContactInfo.permanentCountry);
    $("#staff-permanent-zipcode").val(staffInfo.staffAddressContactInfo.permanentZipcode);
    $("#update-staff-emergancy-contact-number").val(staffInfo.staffAddressContactInfo.emergancyContactNumber);

    $("#staff-highest-qualification").val(staffInfo.staffQualificationExprienceInfo.highestQualification);
    $("#staff-experience").val(staffInfo.staffQualificationExprienceInfo.experience);
    $("#staff-last-organization-name").val(staffInfo.staffQualificationExprienceInfo.lastOrganizationName);
    $("#staff-last-organization-address").val(staffInfo.staffQualificationExprienceInfo.lastOrganizationAddress);
    $("#staff-last-designation").val(staffInfo.staffQualificationExprienceInfo.lastDesignation);
    $("#staff-last-job-duration").val(staffInfo.staffQualificationExprienceInfo.lastJobDuration);

    $('#staff-offer-acceptance-date').val(getFormattedDate(staffInfo.staffJoiningInfo.offerAcceptanceDate));
    $('#staff-tentative-date-of-joining').val(getFormattedDate(staffInfo.staffJoiningInfo.tentativeDateOfJoining));
    $("#staff-background-verification").prop("checked", staffInfo.staffJoiningInfo.backgroundVerification);
    $("#staff-service-start-date").val(getFormattedDate(staffInfo.startDate));
    $("#staff-service-end-date").val(getFormattedDate(staffInfo.endDate));
    $("#staff-rehire").prop("checked", staffInfo.staffJoiningInfo.rehire);
    $("#staff-absconding").prop("checked", staffInfo.staffJoiningInfo.absconding);

    $('#staff-account-type').val(staffInfo.staffBankInfo.accountType);
    $("#staff-bank-name").val(staffInfo.staffBankInfo.bankName);
    $("#staff-account-holder-name").val(staffInfo.staffBankInfo.accountHolderName);
    $("#staff-account-number").val(staffInfo.staffBankInfo.accountNumber);
    $("#staff-ifsc-code").val(staffInfo.staffBankInfo.ifscCode);
    $("#update-staff-pan-number").val(staffInfo.staffBankInfo.panNumber);
    $("#epf-consent-type").val(staffInfo.staffBankInfo.epfConsentType);
    $("#esic-consent-type").val(staffInfo.staffBankInfo.esicConsentType);

    $('#staff-blood-group').val(staffInfo.staffMedicalInfo.bloodGroup);
    $("#staff-blood-pressure").val(staffInfo.staffMedicalInfo.bloodPressure);
    $("#staff-pulse").val(staffInfo.staffMedicalInfo.pulse);
    $("#staff-height").val(staffInfo.staffMedicalInfo.height);
    $("#staff-weight").val(staffInfo.staffMedicalInfo.weight);
    $("#update-date-of-physical-examination").val(getFormattedDate(staffInfo.staffMedicalInfo.dateOfPhysicalExamination));
    if(validateStaffTimingDetails(staffInfo.staffTimingDetails)) {

      if(validateStaffTimingDetails(staffInfo.staffTimingDetails.inTime)) {
        $("#in-time").val(("0" + staffInfo.staffTimingDetails.inTime['hour']).slice(-2) + ":" + ("0" + staffInfo.staffTimingDetails.inTime['minute']).slice(-2));
      }

      if(validateStaffTimingDetails(staffInfo.staffTimingDetails.outTime)) {
        $("#out-time").val(("0" + staffInfo.staffTimingDetails.outTime['hour']).slice(-2) + ":" + ("0" + staffInfo.staffTimingDetails.outTime['minute']).slice(-2));
      }

      $("#staff-half-day-duration").val(staffInfo.staffTimingDetails.halfDayDuration);
      $("#staff-full-day-duration").val(staffInfo.staffTimingDetails.fullDayDuration);

  if (staffInfo.staffVisitingDaysList) {
    staffInfo.staffVisitingDaysList.forEach(function(day) {
        var count = 0;
        var visitingHoursHtml = "";
        day.visitingHoursList.forEach(function(hours) {
        var startAndEndTimeInputHTML = onboardStaff.generateStartAndEndTimeHTML(hours.startDisplayTime.substring(0, 5), hours.endDisplayTime.substring(0, 5), false)
        visitingHoursHtml +=
                "<tr>" +
                   startAndEndTimeInputHTML +
                    "<td  style=\" vertical-align:  top; \">";

            if (count > 0) {
                visitingHoursHtml +=
                      "<button type=\"button\" class=\"close\" aria-label=\"Close\" onclick=\"onboardStaff.deleteVisitingHours(this);\" style=\"margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;\">" +
                               "<span aria-hidden=\"true\">×</span>" +
                      "</button>" ;
            }
            visitingHoursHtml +=
                    "</td>" +
                "</tr>";
            count++;
        });
        var weekDaysUpperHTML = onboardStaff.generateWeekDaysUpperHTML(day.visitingDay, '400px', '150px')
        var weekDaysLowerHTML = onboardStaff.generateWeekDaysLowerHTML(day.visitingDay, '370px')
        var weekDays = weekDaysUpperHTML + visitingHoursHtml + weekDaysLowerHTML;
        $("#staff-visiting-hours-div").append(weekDays);
        $('#weekdays option[value="' + day.visitingDay + '"]').remove();
        $('#weekdays').trigger('change');
        $('.clockpicker').clockpicker({
           autoclose: true,
           'default': 'now'
        });
    });
   }
 }
}

function validateStaffTimingDetails(timing) {
    if(timing == undefined || timing == null || timing == "") {
      return false;
    }
    return true;
}

function fillStaffViewInformation(staffInfo) {
    $("#detail-view-staff-number").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffInstituteId);
    $("#detail-view-staff-name").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.name);
    $("#detail-view-staff-initials").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.initials);
    $('#detail-view-gender').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.gender);
    if(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth != null && staffInfo.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth > 0){
      $('#detail-view-dob').text(getFormattedDate(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth));
    }else{
      $('#detail-view-dob').text("");
    }
    $('#detail-view-category').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.category);
    $('#detail-view-maritial-status').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.maritalStatus);
    $('#detail-view-father-name').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.fatherName);
    $('#detail-view-mother-name').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.motherName);
    $('#detail-view-birth-place').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.birthPlace);
    $("#detail-view-mother-tongue").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.motherTongue);
    $('#detail-view-religion').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.religion);
    $("#detail-view-specially-abled").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.speciallyAbled ? "Yes" : "No");
    $("#detail-view-bpl").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.bpl ? "Yes" : "No");
    $("#detail-view-transport-staff").text(staffInfo.transportStaff ? "Yes" : "No");
    $('#detail-view-primary-email').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.primaryEmail);
    $('#detail-view-secondary-email').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.secondaryEmail);
    $('#detail-view-primary-contact-number').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.primaryContactNumber);
    $('#detail-view-alternate-contact-number').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.alternateContactNumber);
    $('#detail-view-aadhar-number').text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.aadharNumber);
    $("#detail-staff-oasis-id").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.oasisId);
    $("#detail-staff-national-code").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.nationalCode);
    $("#detail-staff-appointment-type").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffNatureOfAppointmentDisplayName);


    var staffCategory = JSON.parse($("#staff-category-json").text());
    var staffCategoryName = "";
    $.each(staffCategory, function(key, value) {
      if(value.staffCategoryId === staffInfo.staffBasicDetailsWithCategoryDepartDesignation.staffCategory.staffCategoryId) {
        staffCategoryName = value.staffCategoryName;
      }
    });

    $("#detail-view-staff-category").text(staffCategoryName);

    var staffDepartment = JSON.parse($("#staff-department-json").text());
    var staffDesignation = JSON.parse($("#staff-designation-json").text());
    var departmentDesignation = "";
    var departmentDesignationNameList = staffInfo.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationNameList;
    for(var i = 0 ; i < departmentDesignationNameList.length ; i++) {
      if(i > 0) {
        departmentDesignation += ", " + departmentDesignationNameList[i];
      } else {
        departmentDesignation += departmentDesignationNameList[i];
      }
    }
    $("#detail-view-staff-department-designation").text(departmentDesignation);

    if(staffInfo.staffImage != null){
      $('#detail-view-staff-image').text("Uploaded");
    }else{
      $('#detail-view-staff-image').text("Not present");
    }

    $("#detail-view-present-address-1").text(staffInfo.staffAddressContactInfo.presentAddress1);
    $("#detail-view-present-address-2").text(staffInfo.staffAddressContactInfo.presentAddress2);
    $("#detail-view-present-city").text(staffInfo.staffAddressContactInfo.presentCity);
    $('#detail-view-present-state').text(staffInfo.staffAddressContactInfo.presentState);
    $('#detail-view-present-country').text(staffInfo.staffAddressContactInfo.presentCountry);
    $("#detail-view-present-zipcode").text(staffInfo.staffAddressContactInfo.presentZipcode);
    $("#detail-view-permanent-address-1").text(staffInfo.staffAddressContactInfo.permanentAddress1);
    $("#detail-view-permanent-address-2").text(staffInfo.staffAddressContactInfo.permanentAddress2);
    $("#detail-view-permanent-city").text(staffInfo.staffAddressContactInfo.permanentCity);
    $('#detail-view-permanent-state').text(staffInfo.staffAddressContactInfo.permanentState);
    $('#detail-view-permanent-country').text(staffInfo.staffAddressContactInfo.permanentCountry);
    $("#detail-view-permanent-zipcode").text(staffInfo.staffAddressContactInfo.permanentZipcode);
    $("#detail-view-emergancy-contact-number").text(staffInfo.staffAddressContactInfo.emergancyContactNumber);

    $("#detail-view-highest-qualification").text(staffInfo.staffQualificationExprienceInfo.highestQualification);
    $("#detail-view-experience").text(staffInfo.staffQualificationExprienceInfo.experience);
    $("#detail-view-last-organization-name").text(staffInfo.staffQualificationExprienceInfo.lastOrganizationName);
    $("#detail-view-last-organization-address").text(staffInfo.staffQualificationExprienceInfo.lastOrganizationAddress);
    $("#detail-view-last-designation").text(staffInfo.staffQualificationExprienceInfo.lastDesignation);
    $("#detail-view-last-job-duration").text(staffInfo.staffQualificationExprienceInfo.lastJobDuration);
    if(staffInfo.staffJoiningInfo.offerAcceptanceDate != null && staffInfo.staffJoiningInfo.offerAcceptanceDate > 0){
      $('#detail-view-offer-acceptance-date').text(getFormattedDate(staffInfo.staffJoiningInfo.offerAcceptanceDate));
    }else{
      $('#detail-view-offer-acceptance-date').text("");
    }
    if(staffInfo.staffJoiningInfo.tentativeDateOfJoining != null && staffInfo.staffJoiningInfo.tentativeDateOfJoining > 0){
      $('#detail-view-tentative-date-of-joining').text(getFormattedDate(staffInfo.staffJoiningInfo.tentativeDateOfJoining));
    }else{
      $('#detail-view-tentative-date-of-joining').text("");
    }
    if(staffInfo.staffJoiningInfo.relieveDate != null && staffInfo.staffJoiningInfo.relieveDate > 0){
      $('#detail-view-relieve-date').text(getFormattedDate(staffInfo.staffJoiningInfo.relieveDate));
    }else{
      $('#detail-view-relieve-date').text("");
    }
    $("#detail-view-background-verification").text(staffInfo.staffJoiningInfo.backgroundVerification ? "Yes" : "No");
    if(staffInfo.startDate != null && staffInfo.startDate > 0){
      $('#detail-view-service-start-date').text(getFormattedDate(staffInfo.startDate));
    }else{
      $('#detail-view-service-start-date').text("");
    }
    $("#detail-view-rehire").text(staffInfo.staffJoiningInfo.rehire ? "Yes" : "No");
    $("#detail-view-absconding").text(staffInfo.staffJoiningInfo.absconding ? "Yes" : "No");

    $('#detail-view-account-type').text(staffInfo.staffBankInfo.accountType);
    $("#detail-view-bank-name").text(staffInfo.staffBankInfo.bankName);
    $("#detail-view-account-holder-name").text(staffInfo.staffBankInfo.accountHolderName);
    $("#detail-view-account-number").text(staffInfo.staffBankInfo.accountNumber);
    $("#detail-view-ifsc-code").text(staffInfo.staffBankInfo.ifscCode);
    $("#detail-view-epf-consent-type").text(staffInfo.staffBankInfo.epfConsentType);
    $("#detail-view-esic-consent-type").text(staffInfo.staffBankInfo.esicConsentType);
    $("#detail-view-pan-number").text(staffInfo.staffBankInfo.panNumber);

    var inTimeVal = "";
    if(validateStaffTimingDetails(staffInfo.staffTimingDetails.inTime)) {
      inTimeVal = ("0" + staffInfo.staffTimingDetails.inTime['hour']).slice(-2) + ":" + ("0" + staffInfo.staffTimingDetails.inTime['minute']).slice(-2);
    }
    $('#detail-in-time').text(inTimeVal);

    var outTimeVal = "";
    if(validateStaffTimingDetails(staffInfo.staffTimingDetails.outTime)) {
      outTimeVal = ("0" + staffInfo.staffTimingDetails.outTime['hour']).slice(-2) + ":" + ("0" + staffInfo.staffTimingDetails.outTime['minute']).slice(-2);
    }
    $("#detail-out-time").text(outTimeVal);

    $("#detail-half-day-duration").text(staffInfo.staffTimingDetails.halfDayDuration);
    $("#detail-full-day-duration").text(staffInfo.staffTimingDetails.fullDayDuration);
    $("#view-collapse-staff-visiting-hours-info").empty();

     if (staffInfo.staffVisitingDaysList){
          staffInfo.staffVisitingDaysList.forEach(function(day) {
              var visitingHoursHtml = "";
              day.visitingHoursList.forEach(function(hours) {
              var startAndEndTimeInputHTML = onboardStaff.generateStartAndEndTimeHTML(hours.startDisplayTime.substring(0, 5), hours.endDisplayTime.substring(0, 5), true)
                  visitingHoursHtml +=
                   "<div>"+
                      "<tr>" +
                          startAndEndTimeInputHTML+
                      "</tr>"+
                       "</div>";
              });

              var weekDays =
                  "<div class=\"col-md-12 mb-3 view-staff-visiting-hours-list\">" +
                      "<div class=\"row view_visiting_hours\">" +
                          "<div class=\"mb-3 col-md-2\">" +
                              "<label>" + day.visitingDay.charAt(0).toUpperCase() + day.visitingDay.slice(1).toLowerCase() + "</label>" +
                          "</div>" +
                          "<div class=\"col-md-9\">" +
                              "<div style=\"display: flex;\">" +
                                  "<table id=\"visiting-hours-" + day.visitingDay.toLowerCase() + "\">" +
                                      visitingHoursHtml +
                                  "</table>" +
                              "</div>" +
                          "</div>" +
                      "</div>"
                  "</div>";
               $("#view-collapse-staff-visiting-hours-info").append(weekDays);
          });
     }
}

function getDepartmentHTML (staffDepartment, departmentId) {
  var departmentHTML = "<option selected></option>";
  $.each(staffDepartment, function(key, value) {
    var selected = "";
    if(value.staffDepartmentId === departmentId) {
      selected = "selected";
    }
    departmentHTML += "<option value=" + value.staffDepartmentId + " " + selected + ">" + value.staffDepartmentName + "</option>";
  });
  return departmentHTML;
}

function getDesignationHTML (staffDesignation, designationId) {
  var designationHTML = "<option selected></option>";
  $.each(staffDesignation, function(key, value) {
    var selected = "";
    if(value.staffDesignationId === designationId) {
      selected = "selected";
    }
    designationHTML += "<option value=" + value.staffDesignationId + " " + selected + ">" + value.staffDesignationName + "</option>";
  });
  return designationHTML;
}

function getDepartmentName (staffDepartment, departmentId) {
  var staffDepartmentName = null
  $.each(staffDepartment, function(key, value) {
    if(value.staffDepartmentId === departmentId) {
      staffDepartmentName = value.staffDepartmentName;
    }
  });
  return staffDepartmentName;
}

function getDesignationName (staffDesignation, designationId) {
  var staffDesignationName = null;
  $.each(staffDesignation, function(key, value) {
    if(value.staffDesignationId === designationId) {
      staffDesignationName = value.staffDesignationName;
    }
  });
  return staffDesignationName;
}

var configuration = {

  loadConfigurationMainPage : function () {
    ajaxClient.get("/staff-management/configuration-details", function(data) {
      $("#main-content").html(data);
      configuration.addCategory();
      configuration.addDepartment();
      configuration.addDesignation();
    });
  },

  loadCategoryDetails : function () {
    ajaxClient.get("/staff-management/category-details", function(data) {
      $("#nav-staff-category").html(data);
      configuration.addCategory();
    });
  },

  addCategory : function () {
    $('#add-category-button').on('click', function () {
      var staffCategoryName = $("#category-name-textarea").val();
      if(staffCategoryName === "" || staffCategoryName === null || staffCategoryName === undefined) {
        $("#category-name-textarea").css("border", "1px solid #ff8795");
        showErrorDialogBox("Please add category name!");
        return;
      }
      var staffCategory = {'instituteId': null, 'staffCategoryName' : staffCategoryName};

      ajaxClient.post("/staff-management/add-category", {'staffCategory' : JSON.stringify(staffCategory)}, function(data){
          $("#configurations-status-modal-container").html(data);
          $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
          configuration.loadCategoryDetails();
      });
    });
  },

  fillUpdateCategoryPopup : function (ref) {
    var categoryJson = JSON.parse($(ref).parent().find(".category-info").text());
    $("#update-staff-category-id").text(categoryJson.staffCategoryId);
    $("#staff-category-name-text").val(categoryJson.staffCategoryName);
  },

  updateCategory : function () {
    var staffCategoryId = $("#update-staff-category-id").text();
    var staffCategoryName = $("#staff-category-name-text").val();
    if(staffCategoryName === "" || staffCategoryName === null || staffCategoryName === undefined) {
      $("#staff-category-name-text").css("border", "1px solid #ff8795");
      showErrorDialogBox("Please add category name!");
      return;
    }
    var staffCategory = {'instituteId': null, 'staffCategoryId' : staffCategoryId, 'staffCategoryName' : staffCategoryName};
    $("#update-category-popup").modal('toggle');
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/update-category", {'staffCategory' : JSON.stringify(staffCategory)}, function(data){
        $("#configurations-status-modal-container").html(data);
        $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
        configuration.loadCategoryDetails();
    });
  },

  deleteCategoryPopup : function (ref) {
    var categoryJson = JSON.parse($(ref).parent().find(".category-info").text());
    $("#delete-category-id").text(categoryJson.staffCategoryId);
  },

  deleteCategory : function () {
    var staffCategoryId = $("#delete-category-id").text();
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/delete-category/" + staffCategoryId, {}, function(data){
      $("#configurations-status-modal-container").html(data);
      $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
      configuration.loadCategoryDetails();
    });
  },


  loadDepartmentDetails : function () {
    ajaxClient.get("/staff-management/department-details", function(data) {
      $("#nav-staff-department").html(data);
      configuration.addDepartment();
    });
  },

  addDepartment : function () {
    $('#add-department-button').on('click', function () {
      var staffDepartmentName = $("#department-name-textarea").val();
      if(staffDepartmentName === "" || staffDepartmentName === null || staffDepartmentName === undefined) {
        $("#department-name-textarea").css("border", "1px solid #ff8795");
        showErrorDialogBox("Please add department name!");
        return;
      }
      var staffDepartment = {'instituteId': null, 'staffDepartmentName' : staffDepartmentName};

      ajaxClient.post("/staff-management/add-department", {'staffDepartment' : JSON.stringify(staffDepartment)}, function(data){
          $("#configurations-status-modal-container").html(data);
          $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
          configuration.loadDepartmentDetails();
      });
    });
  },

  fillUpdateDepartmentPopup : function (ref) {
    var departmentJson = JSON.parse($(ref).parent().find(".department-info").text());
    $("#update-staff-department-id").text(departmentJson.staffDepartmentId);
    $("#staff-department-name-text").val(departmentJson.staffDepartmentName);
  },

  updateDepartment : function () {
    var staffDepartmentId = $("#update-staff-department-id").text();
    var staffDepartmentName = $("#staff-department-name-text").val();
    if(staffDepartmentName === "" || staffDepartmentName === null || staffDepartmentName === undefined) {
      $("#staff-department-name-text").css("border", "1px solid #ff8795");
      showErrorDialogBox("Please add department name!");
      return;
    }
    var staffDepartment = {'instituteId': null, 'staffDepartmentId' : staffDepartmentId, 'staffDepartmentName' : staffDepartmentName};
    $("#update-department-popup").modal('toggle');
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/update-department", {'staffDepartment' : JSON.stringify(staffDepartment)}, function(data){
        $("#configurations-status-modal-container").html(data);
        $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
        configuration.loadDepartmentDetails();
    });
  },

  deleteDepartmentPopup : function (ref) {
    var departmentJson = JSON.parse($(ref).parent().find(".department-info").text());
    $("#delete-department-id").text(departmentJson.staffDepartmentId);
  },

  deleteDepartment : function () {
    var staffDepartmentId = $("#delete-department-id").text();
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/delete-department/" + staffDepartmentId, {}, function(data){
      $("#configurations-status-modal-container").html(data);
      $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
      configuration.loadDepartmentDetails();
    });
  },


  loadDesignationDetails : function () {
    ajaxClient.get("/staff-management/designation-details", function(data) {
      $("#nav-staff-designation").html(data);
      configuration.addDesignation();
    });
  },

  addDesignation : function () {
    $('#add-designation-button').on('click', function () {
      var staffDesignationName = $("#designation-name-textarea").val();
      if(staffDesignationName === "" || staffDesignationName === null || staffDesignationName === undefined) {
        $("#designation-name-textarea").css("border", "1px solid #ff8795");
        showErrorDialogBox("Please add designation name!");
        return;
      }
      var staffDesignation = {'instituteId': null, 'staffDesignationName' : staffDesignationName};

      ajaxClient.post("/staff-management/add-designation", {'staffDesignation' : JSON.stringify(staffDesignation)}, function(data){
          $("#configurations-status-modal-container").html(data);
          $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
          configuration.loadDesignationDetails();
      });
    });
  },

  fillUpdateDesignationPopup : function (ref) {
    var designationJson = JSON.parse($(ref).parent().find(".designation-info").text());
    $("#update-staff-designation-id").text(designationJson.staffDesignationId);
    $("#staff-designation-name-text").val(designationJson.staffDesignationName);
  },

  updateDesignation : function () {
    var staffDesignationId = $("#update-staff-designation-id").text();
    var staffDesignationName = $("#staff-designation-name-text").val();
    if(staffDesignationName === "" || staffDesignationName === null || staffDesignationName === undefined) {
      $("#staff-designation-name-text").css("border", "1px solid #ff8795");
      showErrorDialogBox("Please add designation name!");
      return;
    }
    var staffDesignation = {'instituteId': null, 'staffDesignationId' : staffDesignationId, 'staffDesignationName' : staffDesignationName};
    $("#update-designation-popup").modal('toggle');
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/update-designation", {'staffDesignation' : JSON.stringify(staffDesignation)}, function(data){
        $("#configurations-status-modal-container").html(data);
        $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
        configuration.loadDesignationDetails();
    });
  },

  deleteDesignationPopup : function (ref) {
    var designationJson = JSON.parse($(ref).parent().find(".designation-info").text());
    $("#delete-designation-id").text(designationJson.staffDesignationId);
  },

  deleteDesignation : function () {
    var staffDesignationId = $("#delete-designation-id").text();
    $('.modal-backdrop').remove();
    ajaxClient.post("/staff-management/delete-designation/" + staffDesignationId, {}, function(data){
      $("#configurations-status-modal-container").html(data);
      $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
      configuration.loadDesignationDetails();
    });
  },

};

var admitStaff = {
  loadAdmitStaffMenu: function () {
    joinerStaff.staffStatus = "";
    onboardStaff.staffStatus = "";
    relieveStaff.staffStatus = "";
    ajaxClient.get("/staff-management/add-staff-view", function(data) {
      $("#main-content").html(data);
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      initSelect2("None");
      onboardStaff.sameForAll();
      enforceConstraints();
      initDate(365*100);
      staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
          "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
          "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
          "staff-present-state", "staff-present-country", "staff-present-zipcode");
    });
  },

  registerStaff : function () {
    var isError = false;
    var impactedElement = null;
    var string = "";
    var invalid = validateMandatoryFields($("#add\\.basic-info-content"));
    if(invalid){
      isError = true;
      impactedElement = null;
      string = "Please fill all Mandatory fields.";
    }

    var documentName = "";
    var file = "";
    // var ONE_KB = 1024;
    // if (($("#staff-photo"))[0].files.length > 0) {
    //   file = ($("#staff-photo"))[0].files[0];
    //   if((file.size / ONE_KB) > STAFF_IMAGE_SIZE_LIMIT){
    //     isError = true;
    //     impactedElement = null;
    //     string = "File size exceeds " + STAFF_IMAGE_SIZE_LIMIT + " KB after compression. Please reduce the file size and try uploading again.";
    //     // return;
    //   }
    //   var fileType = file["type"];
    //   var validImageTypes = ["image/gif", "image/jpeg", "image/png", "image/jpg"];
    //   if ($.inArray(fileType, validImageTypes) < 0) {
    //     isError = true;
    //     impactedElement = null;
    //     string = "Image format should be in png, jpeg, jpg or gif";
    //     // return;
    //   }
    // }

    if(isError) {
      if(impactedElement != null) {
        $("#" + impactedElement).css("border", "1px solid #ff8795");
      }
      showErrorDialogBox(string);
      return;
    }

    var staffBasicInfo = admitStaff.getStaffBasicInfo();
    if(staffBasicInfo === undefined) {
      return;
    }
    var staffAddressContactInfo = null;

    var staffQualificationExprienceInfo = null;
    var staffJoiningInfo = null;
    var staffBankInfo = null;
    var transportStaff = $("#transport-staff").is(":checked");

    var registerStaffPayload = {'staffStatus' : 'JOINER',
     'staffBasicInfo': staffBasicInfo, 'staffAddressContactInfo' : staffAddressContactInfo,
     'staffQualificationExprienceInfo':staffQualificationExprienceInfo,
   'staffJoiningInfo' : staffJoiningInfo, 'staffBankInfo' : staffBankInfo, 'transportStaff' : transportStaff};

     var formData = new FormData();
     if(file != "") {
       formData.append('document', file);
       formData.append('documentName', documentName);
     }

     formData.append('registerStaffPayload', JSON.stringify(registerStaffPayload));
     ajaxClient.uploadFile("/staff-management/register-staff", formData, function(data) {
         $("#create-staff-confirm-modal-container").html(data);
         var response = JSON.parse($("#staff-data-response").text());
         if(response.success){
           fillStaffInformation(response.staff);
           $("#update-staff-previous-school-info").trigger("click");
           $('.add-staff-screen').attr('style','display:none');
           $('.update-staff-screen').attr('style','display:block');
           $('#screen-name').text('ADMIT-STAFF');
         }
         enforceConstraints();
         $("#admission-status-modal").modal('toggle');
     });
  },

   getStaffBasicInfo : function () {
    var staffInstituteId = $("#staff-number").val();
    var staffName = $("#staff-name").val();
    var staffInitials = $("#staff-initials").val();
    var gender = $("#staff-gender").val();
    if(gender == ""){
      gender = null;
    }
    var dob = getDate($("#staff-date-of-birth").val());
    var dobInt = null;
    if(dob != null){
      dobInt = dob.getTime()/1000;
    }
    var userCategory = $("#user-staff-category").val();
    if(userCategory == ""){
      userCategory = null;
    }
    var maritalStatus = $("#staff-maritial-status").val();
    if(maritalStatus == ""){
      maritalStatus = null;
    }
    var fatherName = $("#staff-father-name").val();
    var motherName = $("#staff-mother-name").val();
    var birthPlace = $("#staff-birth-place").val();
    var motherTongue = $("#staff-mother-tongue").val();
    var religion = $("#staff-religion").val();
    var speciallyAbled = $("#staff-specially-abled").is(":checked");
    var bpl = $("#staff-bpl").is(":checked");

    var isError = false;
    var impactedElement = null;
    var aadharNumber = $("#staff-aadhar-number").val().trim();
    if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
      isError = true;
      impactedElement = "staff-aadhar-number";
      string = "Invalid Aadhar Number";
    }

    var alternateContactNumber = $("#staff-alternate-contact-number").val().trim();
    if(alternateContactNumber != "" && !regexPhoneNumber.test(alternateContactNumber)) {
      isError = true;
      impactedElement = "staff-alternate-contact-number";
      string = "Alternate Contact Number should be of 10 digits.";
    }

    var primaryContactNumber = $("#staff-primary-contact-number").val().trim();
    if(!regexPhoneNumber.test(primaryContactNumber)) {
      isError = true;
      impactedElement = "staff-primary-contact-number";
      string = "Primary Contact Number should be of 10 digits.";
    }


    if(primaryContactNumber === alternateContactNumber) {
      isError = true;
      impactedElement = null;
      string = "Primary Contact Number cannot be same as Alternate Contact Number";
    }

    var secondaryEmail = $("#staff-secondary-email").val().trim();
    if(secondaryEmail != "" && !regexEmail.test(secondaryEmail)) {
      isError = true;
      impactedElement = "staff-secondary-email";
      string = "Type correct format of Secondary Email";
    }

    var primaryEmail = $("#staff-primary-email").val().trim();
    if(!regexEmail.test(primaryEmail)) {
      isError = true;
      impactedElement = "staff-primary-email";
      string = "Type correct format of Primary Email";
    }

    if(primaryEmail === secondaryEmail) {
      isError = true;
      impactedElement = null;
      string = "Primary Email cannot be same as secondary Email";
    }

     var oasisId = $("#staff-oasis-id").val();
     var nationalCode = $("#staff-national-code").val();
     var natureOfAppointment = $("#staff-appointment-type").val();

    if(isError) {
      if(impactedElement != null) {
        $("#" + impactedElement).css("border", "1px solid #ff8795");
      }
      showErrorDialogBox(string);
      return;
    }


    var staffCategoryId = $("#staff-category").val();
    departmentDesignationMapping = {};
    $(".department-designation-mapping").each(function() {
      var departmentId = $(this).find('.staff-department').val();
      var designationId = $(this).find('.staff-designation').val();
      departmentDesignationMapping[departmentId] = designationId;
    });

    var staffBasicInfo = {'staffInstituteId' : staffInstituteId, 'name' : staffName, 'initials' : staffInitials, 'gender' :gender, 'dateOfBirth' : dobInt,
    'staffCategoryId' : staffCategoryId, 'departmentDesignationMapping' : departmentDesignationMapping, 'category' : userCategory,
    'maritalStatus' : maritalStatus, 'religion' : religion, 'fatherName' : fatherName, 'motherName' : motherName,
    'birthPlace' :  birthPlace, 'motherTongue' : motherTongue, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl,
    'aadharNumber' : aadharNumber, 'primaryEmail' : primaryEmail,
    'secondaryEmail' : secondaryEmail, 'primaryContactNumber' : primaryContactNumber,
    'alternateContactNumber' : alternateContactNumber, 'oasisId' : oasisId, 'nationalCode' : nationalCode,
    'staffNatureOfAppointment' : natureOfAppointment};

    return staffBasicInfo;
  },

  updateStaff : function (finish) {

    var isError = false;
    var impactedElement = null;
    var string = "";

    var invalid = validateMandatoryFields($("#update-basic-info-content"));
    invalid |= validateMandatoryFields($("#update-staff-address-contact-info-content"));
    invalid |= validateMandatoryFields($("#update-qualification-experience-info-content"));
    invalid |= validateMandatoryFields($("#update-joining-documents-info-content"));
    invalid |= validateMandatoryFields($("#update-bank-info-content"));

    if(invalid){
      isError = true;
      impactedElement = null;
      string = "Please fill all Mandatory fields.";
    }

    var documentName = "";
    var file = "";
    // var ONE_KB = 1024;
    // if (($("#update-staff-photo"))[0].files.length > 0) {
    //   file = ($("#update-staff-photo"))[0].files[0];
    //   if((file.size / ONE_KB) > STAFF_IMAGE_SIZE_LIMIT){
    //     isError = true;
    //     impactedElement = null;
    //     string = "File size exceeds " + STAFF_IMAGE_SIZE_LIMIT + " KB after compression. Please reduce the file size and try uploading again.";
    //   }
    //   var fileType = file["type"];
    //   var validImageTypes = ["image/gif", "image/jpeg", "image/png", "image/jpg"];
    //   if ($.inArray(fileType, validImageTypes) < 0) {
    //     isError = true;
    //     impactedElement = null;
    //     string = "Image format should be in png, jpeg, jpg or gif";
    //   }
    // }

    if(isError) {
      if(impactedElement != null) {
        $("#" + impactedElement).css("border", "1px solid #ff8795");
      }
      showErrorDialogBox(string);
      return;
    }

    var staffId = $("#update-admission-staff-id").text().trim();

    var staffBasicInfo = admitStaff.getStaffBasicInfoUpdate();
    if(staffBasicInfo === undefined) {
      return;
    }
    var staffAddressContactInfo = admitStaff.getStaffAddressContactInfo();
    var staffQualificationExprienceInfo = admitStaff.getStaffQualificationExprienceInfo();
    var staffJoiningInfo = admitStaff.getStaffJoiningInfo();
    var staffBankInfo = admitStaff.getStaffBankInfo();
    var staffMedicalInfo = admitStaff.getStaffMedicalInfo();
    var staffTimingDetails = admitStaff.getStaffTimingDetails()
    var staffVisitingDaysList = admitStaff.getStaffVisitingDetails()
      if(staffVisitingDaysList === undefined) {
          return;
        }

    if(staffBankInfo === undefined) {
      return;
    }
    var serviceStartDate = getDate($("#staff-service-start-date").val());
    var serviceStartDateInt = null;
    if(serviceStartDate != null){
      serviceStartDateInt = serviceStartDate.getTime()/1000;
    }
    var serviceEndDate = getDate($("#staff-service-end-date").val());
    var serviceEndDateInt = null;
    if(serviceEndDate != null){
      serviceEndDateInt = serviceEndDate.getTime()/1000;
    }
    var staffStatus = relieveStaff.staffStatus === "" ?  onboardStaff.staffStatus === "" ? joinerStaff.staffStatus : onboardStaff.staffStatus : relieveStaff.staffStatus;
    if(joinerStaff.staffStatus === "" && onboardStaff.staffStatus === "" && relieveStaff.staffStatus === "") {
      staffStatus = 'JOINER';
    }

    var transportStaff = $("#update-transport-staff").is(":checked");



    var updateStaffPayload = {'instituteId' : null, 'staffId' : staffId,
    'serviceStartDate'  : serviceStartDateInt, 'serviceEndDate' : serviceEndDateInt,
    'staffStatus' : staffStatus, 'staffBasicInfo': staffBasicInfo,
    'staffAddressContactInfo' : staffAddressContactInfo,
    'staffQualificationExprienceInfo' : staffQualificationExprienceInfo,
    'staffJoiningInfo' : staffJoiningInfo, 'staffBankInfo' : staffBankInfo, 'staffMedicalInfo' : staffMedicalInfo,
    'staffTimingDetails' : staffTimingDetails, 'transportStaff' : transportStaff, 'staffVisitingDaysList' : staffVisitingDaysList};

     var formData = new FormData();
     if(file != "") {
       formData.append('document', file);
       formData.append('documentName', documentName);
     }
     formData.append('updateStaffPayload', JSON.stringify(updateStaffPayload));
     ajaxClient.uploadFile("/staff-management/update-staff", formData, function(data) {

       $("#admission-status-modal-container").html(data);
       var response = JSON.parse($("#staff-data-response").text());
       if(response.success){

         if(finish){
           joinerStaff.staffStatus = "";
           onboardStaff.staffStatus = "";
           relieveStaff.staffStatus = "";
           returnToMainScreen(staffStatus);
         }else {
           switchToNextTab();
        }
      } else {
        $("#admission-status-modal").modal('toggle');
      }
    });
  },

  getStaffBasicInfoUpdate : function () {

    var staffInstituteId = $("#update-staff-number").val();
    var staffName = $("#update-staff-name").val();
    var staffInitials = $("#update-staff-initials").val();
    var gender = $("#update-staff-gender").val();
    if(gender == ""){
      gender = null;
    }
    var dob = getDate($("#update-staff-date-of-birth").val());
    var dobInt = null;
    if(dob != null){
      dobInt = dob.getTime()/1000;
    }
    var userCategory = $("#update-user-staff-category").val();
    if(userCategory == ""){
      userCategory = null;
    }
    var maritalStatus = $("#update-staff-maritial-status").val();
    if(maritalStatus == ""){
      maritalStatus = null;
    }
    var fatherName = $("#update-staff-father-name").val();
    var motherName = $("#update-staff-mother-name").val();
    var birthPlace = $("#update-staff-birth-place").val();
    var motherTongue = $("#update-staff-mother-tongue").val();
    var religion = $("#update-staff-religion").val();
    var speciallyAbled = $("#update-staff-specially-abled").is(":checked");
    var bpl = $("#update-staff-bpl").is(":checked");

    var isError = false;
    var impactedElement = null;
    var aadharNumber = $("#update-staff-aadhar-number").val().trim();
    if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
      isError = true;
      impactedElement = "update-staff-aadhar-number";
      string = "Invalid Aadhar Number";
    }

    var alternateContactNumber = $("#update-staff-alternate-contact-number").val().trim();
    if(alternateContactNumber != "" && !regexPhoneNumber.test(alternateContactNumber)) {
      isError = true;
      impactedElement = "update-staff-alternate-contact-number";
      string = "Alternate Contact Number should be of 10 digits.";
    }

    var primaryContactNumber = $("#update-staff-primary-contact-number").val().trim();
    if(!regexPhoneNumber.test(primaryContactNumber)) {
      isError = true;
      impactedElement = "update-staff-primary-contact-number";
      string = "Primary Contact Number should be of 10 digits.";
    }


    if(primaryContactNumber === alternateContactNumber) {
      isError = true;
      impactedElement = null;
      string = "Primary Contact Number cannot be same as Alternate Contact Number";
    }

    var secondaryEmail = $("#update-staff-secondary-email").val().trim();
    if(secondaryEmail != "" && !regexEmail.test(secondaryEmail)) {
      isError = true;
      impactedElement = "update-staff-secondary-email";
      string = "Type correct format of Secondary Email";
    }

    var primaryEmail = $("#update-staff-primary-email").val().trim();
    if(!regexEmail.test(primaryEmail)) {
      isError = true;
      impactedElement = "update-staff-primary-email";
      string = "Type correct format of Primary Email";
    }

    if(primaryEmail === secondaryEmail) {
      isError = true;
      impactedElement = null;
      string = "Primary Email cannot be same as secondary Email";
    }

    if(isError) {
      if(impactedElement != null) {
        $("#" + impactedElement).css("border", "1px solid #ff8795");
      }
      showErrorDialogBox(string);
      return;
    }
    var staffCategoryId = $("#update-staff-category").val();

    departmentDesignationMapping = {};
    $(".department-designation-mapping").each(function() {
      var departmentId = $(this).find('.update-staff-department').val();
      var designationId = $(this).find('.update-staff-designation').val();
      departmentDesignationMapping[departmentId] = designationId;
    });

    var oasisId = $("#update-staff-oasis-id").val();
    var nationalCode = $("#update-staff-national-code").val();
    var natureOfAppointment = $("#update-staff-appointment-type").val();

    var staffBasicInfo = {'staffInstituteId' : staffInstituteId, 'name' : staffName, 'initials' : staffInitials, 'gender' :gender, 'dateOfBirth' : dobInt,
    'staffCategoryId' : staffCategoryId, 'departmentDesignationMapping' : departmentDesignationMapping, 'category' : userCategory,
    'maritalStatus' : maritalStatus, 'religion' : religion, 'fatherName' : fatherName, 'motherName' : motherName,
    'birthPlace' :  birthPlace, 'motherTongue' : motherTongue, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl,
    'aadharNumber' : aadharNumber, 'primaryEmail' : primaryEmail,
    'secondaryEmail' : secondaryEmail, 'primaryContactNumber' : primaryContactNumber,
    'alternateContactNumber' : alternateContactNumber, 'oasisId' : oasisId, 'nationalCode' : nationalCode,
    'staffNatureOfAppointment' : natureOfAppointment };

    return staffBasicInfo;
  },

  getStaffAddressContactInfo : function () {

    var permanentAddress1 = $("#staff-permanent-address-1").val();
    var permanentAddress2 = $("#staff-permanent-address-2").val();
    var permanentCity = $("#staff-permanent-city").val();
    var permanentState = $("#staff-permanent-state").find('.country-states').val();
    if(permanentState == ""){
      permanentState = null;
    }
    var permanentCountry = $("#staff-permanent-country").find('.country').val();
    if(permanentCountry == ""){
      permanentCountry = null;
    }
    var permanentZipcode = $("#staff-permanent-zipcode").val();


    var presentAddress1 = $("#staff-present-address-1").val();
    var presentAddress2 = $("#staff-present-address-2").val();
    var presentCity = $("#staff-present-city").val();
    var presentState = $("#staff-present-state").find('.country-states').val();
    if(presentState == ""){
      presentState = null;
    }
    var presentCountry = $("#staff-present-country").find('.country').val();
    if(presentCountry == ""){
      presentCountry = null;
    }
    var presentZipcode = $("#staff-present-zipcode").val();

    var emergancyContactNumber = $("#update-staff-emergancy-contact-number").val();


    var staffAddressContactInfo = {'permanentAddress1' : permanentAddress1, 'permanentAddress2' : permanentAddress2,
      'permanentCity' : permanentCity, 'permanentState' : permanentState, 'permanentCountry' : permanentCountry,
      'permanentZipcode' : permanentZipcode, 'presentAddress1' : presentAddress1, 'presentAddress2' : presentAddress2,
      'presentCity' : presentCity,  'presentState' : presentState, 'presentCountry' : presentCountry,
      'presentZipcode' : presentZipcode, 'emergancyContactNumber' : emergancyContactNumber};

    return staffAddressContactInfo;
  },

  getStaffQualificationExprienceInfo : function () {

    var highestQualification = $("#staff-highest-qualification").val();
    var experience = $("#staff-experience").val();
    var lastOrganizationName = $("#staff-last-organization-name").val();
    var lastOrganizationAddress = $("#staff-last-organization-address").val();
    var lastDesignation = $("#staff-last-designation").val();
    var lastJobDuration = $("#staff-last-job-duration").val();

    var staffQualificationExprienceInfo = {'highestQualification' : highestQualification,
     'experience' : experience, 'lastOrganizationName' : lastOrganizationName,
     'lastOrganizationAddress' : lastOrganizationAddress, 'lastDesignation' : lastDesignation,
     'lastJobDuration' : lastJobDuration};
    return staffQualificationExprienceInfo;

  },

  getStaffJoiningInfo : function () {

    var offerAcceptanceDate = getDate($("#staff-offer-acceptance-date").val());
    var offerAcceptanceDateInt = null;
    if(offerAcceptanceDate != null){
      offerAcceptanceDateInt = offerAcceptanceDate.getTime()/1000;
    }
    var tentativeDateOfJoining = getDate($("#staff-tentative-date-of-joining").val());
    var tentativeDateOfJoiningInt = null;
    if(tentativeDateOfJoining != null){
      tentativeDateOfJoiningInt = tentativeDateOfJoining.getTime()/1000;
    }
    var backgroundVerification = $("#staff-background-verification").is(":checked");
    var relieveDate = getDate($("#staff-relieve-date").val());
    var relieveDateInt = null;
    if(relieveDate != null){
      relieveDateInt = relieveDate.getTime()/1000;
    }
    var rehire = $("#staff-rehire").is(":checked");
    var absconding = $("#staff-absconding").is(":checked");

    var staffJoiningInfo = {'offerAcceptanceDate' : offerAcceptanceDateInt,
     'tentativeDateOfJoining' : tentativeDateOfJoiningInt, 'backgroundVerification' : backgroundVerification,
     'relieveDate' : relieveDateInt, 'rehire' : rehire, 'absconding' : absconding};

    return staffJoiningInfo;

  },

  getStaffBankInfo : function () {

    var accountType = $("#staff-account-type").val();
    var bankName = $("#staff-bank-name").val();
    var accountHolderName = $("#staff-account-holder-name").val();
    var accountNumber = $("#staff-account-number").val();
    var ifscCode = $("#staff-ifsc-code").val();
    var epfConsentType = $("#epf-consent-type").val();
    var esicConsentType = $("#esic-consent-type").val();

    var isError = false;
    var impactedElement = null;
    var panNumber = $("#update-staff-pan-number").val().trim();
    if(panNumber != "" && !regexPANNumber.test(panNumber)) {
      isError = true;
      impactedElement = "update-staff-pan-number";
      string = "Invalid PAN Number";
    }


    if(isError) {
      if(impactedElement != null) {
        $("#" + impactedElement).css("border", "1px solid #ff8795");
      }
      showErrorDialogBox(string);
      return;
    }

    var staffBankInfo = {'accountType' : accountType, 'bankName' : bankName,
     'accountHolderName' : accountHolderName, 'accountNumber' : accountNumber, 'ifscCode' : ifscCode,
     'panNumber' : panNumber, 'epfConsentType' : epfConsentType, 'esicConsentType' : esicConsentType};

    return staffBankInfo;

  },

  getStaffMedicalInfo : function () {

    var bloodGroup = $("#staff-blood-group").val();
    var bloodPressure = $("#staff-blood-pressure").val();
    var pulse = $("#staff-pulse").val();
    var height = $("#staff-height").val();
    var weight = $("#staff-weight").val();
    var dateOfPhysicalExaminationDate = getDate($("#update-date-of-physical-examination").val());
    var dateOfPhysicalExaminationDateInt = null;
    if(dateOfPhysicalExaminationDate != null){
      dateOfPhysicalExaminationDateInt = dateOfPhysicalExaminationDate.getTime()/1000;
    }

    var staffMedicalInfo = {'bloodGroup' : bloodGroup, 'bloodPressure' : bloodPressure,
     'pulse' : pulse, 'height' : height, 'weight' : weight,
     'dateOfPhysicalExamination' : dateOfPhysicalExaminationDateInt};

    return staffMedicalInfo;
  },

  getStaffTimingDetails : function () {

    var inTimeVal = $("#in-time").val();
    var inTime = null;
    if(inTimeVal != "" && inTimeVal != undefined && inTimeVal != null) {
      var token = inTimeVal.split(":");
      var inHour = token[0];
      var inMinute = token[1];
      var inSecond = '00';
      inTime = {'hour' : inHour, 'minute' : inMinute, 'second' : inSecond};
    }

    var outTimeVal = $("#out-time").val();
    var outTime = null;
    if(outTimeVal != "" && outTimeVal != undefined && outTimeVal != null) {
      var token = outTimeVal.split(":");
      var outHour = token[0];
      var outMinute = token[1];
      var outSecond = '00';
      outTime = {'hour' : outHour, 'minute' : outMinute, 'second' : outSecond};
    }

    var halfDayDuration = $("#staff-half-day-duration").val();
    if(halfDayDuration != "" && halfDayDuration != undefined && halfDayDuration != null) {
      halfDayDuration = parseFloat(halfDayDuration)
    } else {
      halfDayDuration = null;
    }
    var fullDayDuration = $("#staff-full-day-duration").val();
    if(fullDayDuration != "" && fullDayDuration != undefined && fullDayDuration != null) {
      fullDayDuration = parseFloat(fullDayDuration)
    } else {
      fullDayDuration = null;
    }

    staffTimingDetails = {'inTime' : inTime, 'outTime' : outTime, 'halfDayDuration' : halfDayDuration, 'fullDayDuration' : fullDayDuration};

    return staffTimingDetails;

  },

getStaffVisitingDetails: function() {

    let staffVisitingDaysList = [];
    var isError = false;
    var impactedElement = null;
    $(".staff-visiting-hours-list").each(function() {
        let weekDay = $(this).find("h5").text().trim();
        let visitingHoursList = [];
        $(this).find("table tr").each(function() {
            let startTimeValue = $(this).find(".start-time").val();
            let endTimeValue = $(this).find(".end-time").val();
            var startTime = admitStaff.validateVisitingTimeDetails(startTimeValue)
            var endTime = admitStaff.validateVisitingTimeDetails(endTimeValue)

            if (startTime === true || endTime === true) {
                 isError = true;
            } else {
                 visitingHoursList.push({
                     'startTime': startTime,
                     'endTime': endTime
                 });
            }

        });

        staffVisitingDaysList.push({
            'visitingDay' : weekDay.toUpperCase(),
            'visitingHoursList' : visitingHoursList,
            'recurringPattern' : null
        });

    });

    if (isError) {
        showErrorDialogBox("Please enter visiting hours correctly");
        return;
    }
    return staffVisitingDaysList;
},

  validateVisitingTimeDetails: function (time) {
         if (time == "" || time == undefined || time == null) {
             return true;
         }
         var token = time.split(":");
         var hour = token[0];
         var minute = token[1];
         var second = '00';
         if (isNaN(hour) || isNaN(minute) || hour < 0 || hour >= 24 || minute < 0 || minute >= 60) {
             return true;
         }

        return {'hour': hour, 'minute': minute, 'second': second};
  },

  addNewDepartmentDesignationRow : function (ref) {
    var staffDepartment = JSON.parse($("#staff-department-json").text());
    var staffDesignation = JSON.parse($("#staff-designation-json").text());

    var departmentHTML = admitStaff.getDepartmentHTML(staffDepartment);
    var designationHTML = admitStaff.getDesignationHTML(staffDesignation);

    var html = "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control staff-designation\">" + designationHTML + "</select></div><div class=\"form-group remove-class\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.removeDepartmentDesignationRow(this);\"><span aria-hidden=\"true\">x</span></button></div></div>";

    $("#register-staff-div").before(html);
  },

  addNewDepartmentDesignationUpdateRow : function (ref) {
    var staffDepartment = JSON.parse($("#staff-department-json").text());
    var staffDesignation = JSON.parse($("#staff-designation-json").text());

    var departmentHTML = admitStaff.getDepartmentHTML(staffDepartment);
    var designationHTML = admitStaff.getDesignationHTML(staffDesignation);

    var html = "<div class=\"form-row department-designation-mapping\"><div class=\"form-group col-md-4\"><label>Staff Department</label><select class=\"form-control update-staff-department\">" + departmentHTML + "</select></div><div class=\"form-group col-md-4\"><label>Staff Designation</label><select class=\"form-control update-staff-designation\">" + designationHTML + "</select></div><div class=\"form-group remove-class\" style=\"float:left;padding-top:3.2%;padding-left:2%;\"><button type=\"button\" class=\"close\" onclick=\"admitStaff.removeDepartmentDesignationRow(this);\"><span aria-hidden=\"true\">x</span></button></div></div>";

    $("#basic-close-btn").before(html);
  },

  getDepartmentHTML : function (staffDepartment) {
    var departmentHTML = "<option selected></option>";
    $.each(staffDepartment, function(key, value) {
      departmentHTML += "<option value=" + value.staffDepartmentId + ">" + value.staffDepartmentName + "</option>";
    });
    return departmentHTML;
  },

  getDesignationHTML : function (staffDesignation) {
    var designationHTML = "<option selected></option>";
    $.each(staffDesignation, function(key, value) {
      designationHTML += "<option value=" + value.staffDesignationId + ">" + value.staffDesignationName + "</option>";
    });
    return designationHTML;
  },

  removeDepartmentDesignationRow : function (ref) {
    var count = 0;
    $(ref).parent().parent().parent().find('.department-designation-mapping').each(function() {
      count = count + 1;
    });
    if(count <= 2) {
      $('.remove-class').each(function() {
        $(ref).parent().parent().remove();
        $(this).remove();
      });
    } else {
      $(ref).parent().parent().remove();
    }

  },

};

var joinerStaff = {

  staffStatus :  "",

  loadJoinerStaffMainPage : function () {
    var staffStatus = "JOINER";
    ajaxClient.get("/staff-management/staff-details/"+staffStatus, function(data) {
      $("#main-content").html(data);
      joinerStaff.bindSearchStaffJoinerDetails();
      joinerStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      joinerStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
          "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
          "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
          "staff-present-state", "staff-present-country", "staff-present-zipcode");
      initDateWithYearRange("-70:+10", true);
      $('.selectpicker').selectpicker();
      joinerStaff.onAuthorizedModuleChange();
      $('.clockpicker').clockpicker({
        'default': 'now'
      });
      $('#datatables-reponsive').DataTable( {
        "paging":   false,
        searching: false,
        columnDefs: [
          { targets: "no-sort", orderable: false },
        ],
        order: [[2, 'asc']]
      });
    });
  },

  loadJoinerStaffPageReload : function () {
    var staffStatus = "JOINER";
    ajaxClient.get("/staff-management/staff-details-page-reload/"+staffStatus, function(data) {
      $("#search-joiner-staff-results").html(data);
      joinerStaff.refreshSearchFields();
      joinerStaff.bindSearchStaffJoinerDetails();
      joinerStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      joinerStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      initDateWithYearRange("-70:+10", true);
    });
  },

  refreshSearchFields : function () {
    $("#joiner-staff-search-text").val("");
    $(".staff-type-dropdown-div").each(function() {
      if($(this).find('.staff-type:checked').length > 0) {
          $(this).prop("disabled",true);
      }
    });
  },

  bindSearchStaffJoinerDetails : function () {
    $('#joiner-staff-search').on('click', function () {
        joinerStaff.bindSearchStaffJoinerDetailsView();
    });
    $("#joiner-staff-search-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          joinerStaff.bindSearchStaffJoinerDetailsView();
      }
    });
    $('#apply-staff-type-search-filter').on('click', function () {
        joinerStaff.bindSearchStaffJoinerDetailsView();
    });
  },

  bindSearchStaffJoinerDetailsView : function () {
     var search_text = $("#joiner-staff-search-text").val();
     var staffStatus = "JOINER";
     var staffType = "";
     var count = 0;
     $(".staff-type-dropdown-div").each(function() {
       if($(this).find('.staff-type:checked').length > 0) {
         if(count == 0) {
           staffType += $(this).find('.staff-type:checked').attr('id').toString();
         }
         else {
           staffType += "," + $(this).find('.staff-type:checked').attr('id').toString();
         }
         count++;
       }
     });

     var category = "";
     var department = "";
     var designation = "";

     if($('#advance-search-option-div').css('display') != 'none') {
       var category = $('select.filter-category').val();
       var department = $('select.filter-department').val();
       var designation = $('select.filter-designation').val();
     }

     ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/" + staffStatus + "?searchText=" + search_text
     + "&category=" + category+ "&department=" + department+ "&designation=" + designation, function(data) {

       $("#joiner-staff-list").html(data);

       joinerStaff.bindSearchStaffJoinerDetails();
       joinerStaff.registerUpdateStaffDetailsCallBack();
       previousTabSwtichingEvent();
       nextTabSwtichingEvent();
       enforceConstraints();
       joinerStaff.registerViewStaffDetailsCallBack();
       staffDocumentsDetails.registerUploadStaffDocumentCallBack();
       staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
           "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
           "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
           "staff-present-state", "staff-present-country", "staff-present-zipcode");
       initDateWithYearRange("-70:+10", true);

   });
 },

 registerViewStaffDetailsCallBack : function () {
   $('.view-staff-info').on('click', function () {
     $('#staff-list-view-staff-screen').attr('style','display:block');
     $('#view-staff-details-modal').modal('toggle');
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffViewInformation(staffInfo);
   });
 },

 registerUpdateStaffDetailsCallBack : function (){
   $('.update-staff-info').on('click', function () {
     joinerStaff.staffStatus = 'JOINER';
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffInformation(staffInfo);
     enforceConstraints();
     $('.staff-details-screen').attr('style','display:none');
     $('#staff-list-update-staff-screen').attr('style','display:block');
     $('#screen-name').text('STAFF-LIST');
     $('#staff-status-screen').text('JOINER');
     $('#update-staff-timing-content').attr('style','display:none');
     $('#update-staff-timing').attr('style','display:none');
     $('#staff-medical-info-finish-div').attr('style','display:block;float:right');
     $('#staff-medical-info-save-next-div').attr('style','display:none;float:right');
     $('#update-visiting-hours-content').attr('style','display:none');
     $('#update-visiting-hours').attr('style','display:none');
   });
 },

  markStaffOnboard : function () {
    $("#staff-list-onboard-staff-screen").modal('toggle');
    var staffId = $("#joiner-staff-id").text();
    var createUser = $("#staff-create-user").is(":checked");
    var userType = "STAFF";
    var organisationDetails = $("#organisation-details-json").text().trim();
    var currentInstituteId = $("#current-institute-id").text();
    var instituteScope = null;
    if(organisationDetails != "None") {
      var organisationDetailsJson = JSON.parse(organisationDetails);
      instituteScope = $('select.filter-institute-scope').val();
      if (jQuery.inArray(currentInstituteId, instituteScope) <= -1) {
        showErrorDialogBox("Please select current institute in institute scope!");
        return;
      }
    }
    var authorizedModules = $('select.filter-authorized-module').val();
    if(createUser) {
      if(Array.isArray(authorizedModules) && !authorizedModules.length) {
        showErrorDialogBox("To create a user of staff, it should have access to atleast one module. Please select that from authorized modules drop down.");
        return;
      }
    }
    var roles = $('select.filter-roles').val();
    if(createUser) {
      if(Array.isArray(roles) && !roles.length) {
        showErrorDialogBox("To create a user of staff, it should have access to atleast one role. Please select that from roles drop down.");
        return;
      }
    }
    user = {'instituteId' : null, 'uuid' : staffId, 'userType' : userType, 'authorizedModules' : authorizedModules, 'roles' : roles, 'instituteScope' : instituteScope}

    var inTimeVal = $("#in-time").val();
    var inTime = null;
    if(inTimeVal != "" && inTimeVal != undefined && inTimeVal != null) {
      var token = inTimeVal.split(":");
      var inHour = token[0];
      var inMinute = token[1];
      var inSecond = '00';
      inTime = {'hour' : inHour, 'minute' : inMinute, 'second' : inSecond};
    }

    var outTimeVal = $("#out-time").val();
    var outTime = null;
    if(outTimeVal != "" && outTimeVal != undefined && outTimeVal != null) {
      var token = outTimeVal.split(":");
      var outHour = token[0];
      var outMinute = token[1];
      var outSecond = '00';
      outTime = {'hour' : outHour, 'minute' : outMinute, 'second' : outSecond};
    }

    var halfDayDuration = $("#staff-half-day-duration").val();
    if(halfDayDuration != "" && halfDayDuration != undefined && halfDayDuration != null) {
      halfDayDuration = parseFloat(halfDayDuration)
    } else {
      halfDayDuration = null;
    }
    var fullDayDuration = $("#staff-full-day-duration").val();
    if(fullDayDuration != "" && fullDayDuration != undefined && fullDayDuration != null) {
      fullDayDuration = parseFloat(fullDayDuration)
    } else {
      fullDayDuration = null;
    }

    staffTimingDetails = {'inTime' : inTime, 'outTime' : outTime, 'halfDayDuration' : halfDayDuration, 'fullDayDuration' : fullDayDuration};
    var staffOnboardPayload = {'staffTimingDetails' : staffTimingDetails, 'userPayload' : user};

    ajaxClient.post("/staff-management/onboard-staff/" + staffId + "/" + createUser,{'staffOnboardPayload':JSON.stringify(staffOnboardPayload)}, function(data) {
       $("#joiner-staff-confirmation-modal-container").html(data);
       $("#staff-joiner-status-modal").modal({backdrop: 'static', keyboard: false});
       joinerStaff.loadJoinerStaffPageReload();
    });
  },

  populateMarkStaffOnboardStaffModal : function (staffId) {
    $("#joiner-staff-id").text(staffId);
    $("#joiner-staff-details-modal-text").html("Are you sure to onboard staff? Please check below checkbox if you want to create staff's user.");
  },

  deleteStaff : function () {
    $("#staff-list-joiner-delete-staff-screen").modal('toggle');
      var staffStatus = "JOINER";
      var staffId = $("#delete-joiner-staff-id").text();
      ajaxClient.get("/staff-management/delete-staff/" + staffId + "/" + staffStatus, function(data) {
        $("#joiner-staff-confirmation-modal-container").html(data);
        $("#staff-joiner-status-modal").modal({backdrop: 'static', keyboard: false});
        joinerStaff.loadJoinerStaffPageReload();
        });
   },

   populateDeleteStaffModal : function (staffId) {
     $("#delete-joiner-staff-id").text(staffId);
     $("#delete-joiner-staff-details-modal-text").html("Are you sure to delete staff details?");
   },

   onAuthorizedModuleChange : function () {
     $('select.filter-authorized-module').on('change', function(){
         authorizedModules = $(this).val();
         rolesValue = $(this).parent().parent().parent().find('select.filter-roles').val();
         var roleDropDown = "";
         var roles = []
         var roleModuleMapping = JSON.parse($('#role-module-mapping').text().trim());
         for(var i = 0; i < authorizedModules.length; i++) {
           for (var j = 0; j < roleModuleMapping.length; j++) {
             if(authorizedModules[i] === roleModuleMapping[j].module.moduleId) {
               if (!(roles.indexOf(roleModuleMapping[j].roleId) >= 0)) {
                 roles.push(roleModuleMapping[j].roleId);
                 roleDropDown += "<option value=" + roleModuleMapping[j].roleId + ">" + roleModuleMapping[j].roleName + "</option>";
               }
             }
           }
         }
         if(roleDropDown === "") {
           roleDropDown = "<option> Please select any one of the Authorized Module!</option>";
         }
         roleDropDown = "<select class=\"form-control filter-roles selectpicker\" multiple data-style=\"bg-white border\" title=\"None\">" + roleDropDown + "</select>";
         $('div.filter-roles').remove();
         $(".roles-label").after(roleDropDown);
         $('select.filter-roles').val(rolesValue);
         $('.selectpicker').selectpicker();

     });
   },


};

var onboardStaff = {

  staffStatus :  "",

  loadOnboardStaffMainPage : function () {
    var staffStatus = "ONBOARD";
    ajaxClient.get("/staff-management/staff-details/"+staffStatus, function(data) {
      $("#main-content").html(data);
      onboardStaff.bindSearchStaffOnboardDetails();
      onboardStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      onboardStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
          "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
          "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
          "staff-present-state", "staff-present-country", "staff-present-zipcode");
      initDateWithYearRange("-70:+10", true);
      $('.selectpicker').selectpicker();
      $('.clockpicker').clockpicker({
        'default': 'now'
      });

      initSelect2("None")
      $('#datatables-reponsive').DataTable( {
        "paging":   false,
        searching: false,
        columnDefs: [
          { targets: "no-sort", orderable: false },
        ],
        order: [[2, 'asc']]
      });
    });
  },

  loadOnboardStaffPageReload : function () {
    var staffStatus = "ONBOARD";
    ajaxClient.get("/staff-management/staff-details-page-reload/"+staffStatus, function(data) {
      $("#search-onboard-staff-results").html(data);
      onboardStaff.refreshSearchFields();
      onboardStaff.bindSearchStaffOnboardDetails();
      onboardStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      onboardStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      initDateWithYearRange("-70:+10", true);
    });
  },

  refreshSearchFields : function () {
    $("#onboard-staff-search-text").val("");
    $(".staff-type-dropdown-div").each(function() {
      if($(this).find('.staff-type:checked').length > 0) {
          $(this).prop("disabled",true);
      }
    });
  },

  bindSearchStaffOnboardDetails : function () {
    $('#onboard-staff-search').on('click', function () {
        onboardStaff.bindSearchStaffOnboardDetailsView();
    });
    $("#onboard-staff-search-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          onboardStaff.bindSearchStaffOnboardDetailsView();
      }
    });
    $('#apply-staff-type-search-filter').on('click', function () {
        onboardStaff.bindSearchStaffOnboardDetailsView();
    });
  },

  bindSearchStaffOnboardDetailsView : function () {
     var search_text = $("#onboard-staff-search-text").val();
     var staffStatus = "ONBOARD";
     var staffType = "";
     var count = 0;
     $(".staff-type-dropdown-div").each(function() {
       if($(this).find('.staff-type:checked').length > 0) {
         if(count == 0) {
           staffType += $(this).find('.staff-type:checked').attr('id').toString();
         }
         else {
           staffType += "," + $(this).find('.staff-type:checked').attr('id').toString();
         }
         count++;
       }
     });

     var category = "";
     var department = "";
     var designation = "";

     if($('#advance-search-option-div').css('display') != 'none') {
       var category = $('select.filter-category').val();
       var department = $('select.filter-department').val();
       var designation = $('select.filter-designation').val();
     }


     ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/" + staffStatus + "?searchText=" + search_text
     + "&category=" + category+ "&department=" + department+ "&designation=" + designation, function(data) {

       $("#onboard-staff-list").html(data);

       onboardStaff.bindSearchStaffOnboardDetails();
       onboardStaff.registerUpdateStaffDetailsCallBack();
       previousTabSwtichingEvent();
       nextTabSwtichingEvent();
       enforceConstraints();
       onboardStaff.registerViewStaffDetailsCallBack();
       staffDocumentsDetails.registerUploadStaffDocumentCallBack();
       staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
           "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
           "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
           "staff-present-state", "staff-present-country", "staff-present-zipcode");
       initDateWithYearRange("-70:+10", true);
   });
 },

 registerUpdateStaffDetailsCallBack : function (){
   $('.update-staff-info').on('click', function () {
     onboardStaff.staffStatus = 'ONBOARD';
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffInformation(staffInfo);
     onboardStaff.sameForAll();
     enforceConstraints();
     $('.staff-details-screen').attr('style','display:none');
     $('#staff-list-update-staff-screen').attr('style','display:block');
     $('#screen-name').text('STAFF-LIST');
     $('#staff-status-screen').text('ONBOARD');
   });
 },

 registerViewStaffDetailsCallBack : function () {
   $('.view-staff-info').on('click', function () {
     $('#staff-list-view-staff-screen').attr('style','display:block');
     $('#view-staff-details-modal').modal('toggle');
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffViewInformation(staffInfo);
   });
 },

 markStaffRelieve : function () {
   $("#staff-list-relieve-staff-screen").modal('toggle');
   var staffId = $("#onboard-staff-id").text();
     ajaxClient.get("/staff-management/relieve-staff/" + staffId, function(data) {
        $("#onboard-staff-confirmation-modal-container").html(data);
        $("#staff-onboard-status-modal").modal({backdrop: 'static', keyboard: false});
        onboardStaff.loadOnboardStaffPageReload();
       });
 },

 populateMarkStaffRelieveStaffModal : function (staffId) {
   $("#onboard-staff-id").text(staffId);
   $("#onboard-staff-details-modal-text").html("Are you sure to relieve staff?");
 },

 deleteStaff : function () {
   $("#staff-list-onboard-delete-staff-screen").modal('toggle');
     var staffStatus = "ONBOARD";
     var staffId = $("#delete-onboard-staff-id").text();
     ajaxClient.get("/staff-management/delete-staff/" + staffId + "/" + staffStatus, function(data) {
       $("#onboard-staff-confirmation-modal-container").html(data);
       $("#staff-onboard-status-modal").modal({backdrop: 'static', keyboard: false});
       onboardStaff.loadOnboardStaffPageReload();
       });
  },

  populateDeleteStaffModal : function (staffId) {
    $("#delete-onboard-staff-id").text(staffId);
    $("#delete-onboard-staff-details-modal-text").html("Are you sure to delete staff details?");
  },

  loadWeekDays : function (){
     var weekDaysStr = $("#weekdays").val();
     var onScreen = $(".onScreen").text();
     var startLabelMargin = onScreen == 'VISITING_HOURS' ? '270px' : '400px';
     var endLabelMargin = onScreen == 'VISITING_HOURS' ? '130px' : '150px';
     var addVisitingHoursButtonMargin = onScreen == 'VISITING_HOURS' ? '220px'  : '370px'

     if(weekDaysStr.length == 0){
         if (onScreen == "VISITING_HOURS"){
             $("#bulk-update-staff-visiting-hours-details-modal").modal('toggle');
         }
        showErrorDialogBox("Please select a day to load details.")
        return;
     }

   for (var weekDay = 0; weekDay < weekDaysStr.length; weekDay++) {
      var startAndEndTimeInputHTML = onboardStaff.generateStartAndEndTimeHTML('', '', false);
      var weekDaysUpperHTML = onboardStaff.generateWeekDaysUpperHTML(weekDaysStr[weekDay], startLabelMargin, endLabelMargin)
      var weekDaysLowerHTML = onboardStaff.generateWeekDaysLowerHTML(weekDaysStr[weekDay], addVisitingHoursButtonMargin)
      var weekDays = weekDaysUpperHTML + "<tr>" + startAndEndTimeInputHTML + "<td>" + "</td>" + "</tr>" + weekDaysLowerHTML;
         $("#staff-visiting-hours-div").append(weekDays);
         $('.clockpicker').clockpicker({
             autoclose: true,
            'default': 'now'
         });
   $('#weekdays').val([]).trigger('change');
   $('#weekdays option[value="' + weekDaysStr[weekDay] + '"]').remove()
   $('#weekdays').trigger('change');
 }
},

 validateVisitingTime: function(endTimeInput) {

    var row = $(endTimeInput).closest('tr');
    var startTimeInput = row.find('.start-time');
    var endTime = $(endTimeInput).val();
    var startTime = startTimeInput.val();
    var startTimeHour = startTime.hour;
    var errorMessage = "Start time should not be greater than or equal to end time.";
    var table = row.closest('div').find('table').parent();
    var existingErrorMessage = table.next('.error-message');

    if (startTime >= endTime) {
          row.find('.end-time').css("border", "1px solid #ff8795");
           if (existingErrorMessage.length === 0) {
                      table.after('<div class="error-message" style="color: red; margin-top: 10px; text-align: center;">' + errorMessage + '</div>');
           }
           return;
      }

    row.find('.end-time').css('border', '');
    existingErrorMessage.remove();
},

sameForAll: function() {
    $(document).on("click", ".same-for-all", function() {
        var currentWeekDay = $(this).data("weekday");
        var currentTable = $("#visiting-hours-" + currentWeekDay);

        var visitingHoursDetails = [];
        currentTable.find("tr").each(function() {
            var startTime = $(this).find(".start-time").val();
            var endTime = $(this).find(".end-time").val();
            if (startTime && endTime) {
                visitingHoursDetails.push({
                    startTime: startTime,
                    endTime: endTime
                });
            }
        });

        if (visitingHoursDetails.length === 0) {
            showErrorDialogBox("Please fill the visiting hours");
            return;
        }
        $(".staff-visiting-hours-list").each(function() {
            var weekDayLabel = $(this).find("h5").text().trim().toLowerCase();
            if (weekDayLabel !== currentWeekDay) {
                var table = $("#visiting-hours-" + weekDayLabel);
                table.empty();
                var count = 0;
                visitingHoursDetails.forEach(function(detail) {
                    var startAndEndTimeInputHTML = onboardStaff.generateStartAndEndTimeHTML(detail.startTime, detail.endTime, false)
                    var rowHTML =
                        "<tr>" +
                            startAndEndTimeInputHTML +
                             "<td style=\"vertical-align: top;\">"  ;
                              if (count > 0) {
                                 rowHTML +=
                                       "<button type=\"button\" class=\"close delete_stoppage\" aria-label=\"Close\" onclick=\"onboardStaff.deleteVisitingHours(this)\" style=\"margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;\">" +
                                                "<span aria-hidden=\"true\">×</span>" +
                                       "</button>";
                              }

                         rowHTML+=  "</td>" +
                               "</tr>";
                    count++;
                    table.append(rowHTML);
                });
            }
        });
        $('.clockpicker').clockpicker({
            autoclose: true,
           'default': 'now'
        });
    });
},

 addVisitingHours: function(id){
   const startAndEndTimeInputHTML = onboardStaff.generateStartAndEndTimeHTML('', '', false);
    var html =
        "<tr>" +
            startAndEndTimeInputHTML+
            "<td  style=\" vertical-align:  top; \">" +
                "<button type=\"button\" class=\"close\" aria-label=\"Close\" onclick=\"onboardStaff.deleteVisitingHours(this);\" style=\"margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;\">" +
                    "<span aria-hidden=\"true\">×</span>" +
                "</button>" +
            "</td>" +
        "</tr>";
    $("#" + id).append(html);
    $('.clockpicker').clockpicker({
        autoclose: true,
        'default': 'now'
    });
},

 deleteVisitingHours : function (ref) {
      $(ref).closest('tr').remove();
 },

 generateStartAndEndTimeHTML : function (startTime, endTime , readonly){
     const readonlyAttr = readonly ? 'readonly' : '';
             return "<td>" +
                        "<div class=\"input-group input-group-sm mb-3 \">" +
                                "<input type=\"text\" class=\"form-control form-control-sm time-hour hour-range clockpicker start-time\" value=\"" + startTime + "\" placeholder=\"\" " + readonlyAttr + ">" +
                        "</div>" +
                     "</td>" +
                     "<td>" +
                         "<div style=\"margin : 10px;\">to</div>" +
                      "</td>" +
                      "<td>" +
                        "<div class=\"input-group input-group-sm mb-3\">" +
                               "<input type=\"text\" class=\"form-control form-control-sm time-hour hour-range clockpicker end-time\"  value=\"" + endTime + "\" placeholder=\"\" onchange=\"onboardStaff.validateVisitingTime(this)\" " + readonlyAttr + ">" +
                        "</div>" +
                     "</td>";
  },

  generateWeekDaysUpperHTML : function(day, startLabelMargin, endLabelMargin) {
  return      "<div class=\"card card-border text-center staff-visiting-hours-list\" style=\"margin: auto; margin-bottom: 20px; width: 100%;\">" +
                  "<div class=\"card-header-color\" style=\"height: 50px;\">" +
                      "<div class=\"col-md-12 mb-3 card-body\">" +
                          "<div class=\"row add_visiting_hours\">" +
                              "<div class=\"col-md-12 d-flex align-items-center\">" +
                                  "<div class=\"flex-grow-1 text-center\">" +
                                      "<h5 class=\"mb-0 card-title\"><strong>" + day + "</strong></h5>" +
                                  "</div>" +
                                  "<button type=\"button\" class=\"close delete_stoppage\" style=\"position: absolute; right: 5px; margin-top: 0px;\" onclick=\"onboardStaff.deleteVisitingDetails(this, '" + day + "')\" aria-label=\"Close\">" +
                                      "<span aria-hidden=\"true\">&times;</span>" +
                                  "</button>" +
                              "</div>" +
                          "</div>" +
                      "</div>" +
                  "</div>" +
                  "<div class=\"col-md-12 d-flex align-items-center\" style=\"margin-top: 10px;\">" +
                      "<div class=\"mb-2\" style=\"margin-left:" + startLabelMargin + ";\">" +
                          "<label for=\"start-time\">Start Time</label>" +
                      "</div>" +
                      "<div class=\"mb-2\" style=\"margin-left:" + endLabelMargin + ";\">" +
                          "<label for=\"end-time\">End Time</label>" +
                      "</div>" +
                  "</div>" +
                  "<div class=\"col-md-12 d-flex justify-content-center align-items-center\">" +
                      "<table id=\"visiting-hours-" + day.toLowerCase() + "\" style=\"display: block;\">"
  },

 generateWeekDaysLowerHTML : function (day, addVisitingHoursButtonMargin) {
     return   "</table>" +
                   "</div>" +
                      "<div class=\"col-md-12 d-flex\" style=\"margin: 15px;\">" +
                          "<div class=\"d-flex align-items-center\">" +
                              "<button type=\"button\" class=\"btn btn-outline-primary btn-sm same-for-all\" data-weekday=\"" + day.toLowerCase() + "\">Same for All</button>" +
                          "</div>" +
                          "<button type=\"button\" class=\"btn btn-outline-secondary btn-sm add_visiting_hours\" style=\"margin-left:" + addVisitingHoursButtonMargin + ";\"  onclick=\"onboardStaff.addVisitingHours('visiting-hours-" + day.toLowerCase() + "');\"> + Add More Visiting Hour</button>"+
                      "</div>" +
                   "</div>";
 },

 deleteVisitingDetails: function (ref, day) {
      $(ref).parent().parent().parent().parent().parent().remove();
      var optionHTML = '<option value="'+ day +'">'+day.charAt(0).toUpperCase() + day.slice(1).toLowerCase()+'</option>';
      var currentOptions = $('#weekdays option').map(function() {
               return $(this).val();
      }).get();

      currentOptions.push(day)
     var currentOptionsMap = new Map();

     currentOptions.forEach(day => {
     if (daysOfWeekMap.has(day)) {
         currentOptionsMap.set(day, daysOfWeekMap.get(day));
     }
 });
    const sortedCurrentOptionsMap = new Map([...currentOptionsMap.entries()].sort((a, b) => a[1] - b[1]));
    $('#weekdays').empty();
    sortedCurrentOptionsMap.forEach((value, day) => {
        var optionHTML = '<option value="'+ day +'">'+ day.charAt(0).toUpperCase() + day.slice(1).toLowerCase() +'</option>';
        $('#weekdays').append(optionHTML);
    });
        $('#weekdays').trigger('change');
    }
};


var bulkUpdateStaff = {
  dataCache : {},

  loadUpdateStaffMainPage : function(){
    var staffStatus = "ONBOARD";
    ajaxClient.get("/staff-management/bulk-update-staff-details/"+staffStatus, function(data) {
      $("#main-content").html(data);
      bulkUpdateStaff.bindBulkStaffDetailsOptions();
      $('.selectpicker').selectpicker();
      initSelect2("none");
      initDateWithYearRange("-70:+10", true);
      $('.clockpicker').clockpicker({
        'default': 'now'
      });
      bulkUpdateStaff.bindUpdatingFieldTypeChangeEvent();
      advanceSearch();
    });
  },

  bindBulkStaffDetailsOptions :function () {
        $('#bulk-staff-timing-details-option').on('click', function () {
           $("#bulk-staff-visiting-hours").attr("style","display:none;");
          $("#bulk-staff-timing-details").attr("style","display:block;");
          $(".onScreen").html("TIMING_DETAILS")

        });

      $('#bulk-staff-visiting-hours-option').on('click', function () {
         $(".identity-card-screen").attr("style","display:none;");
         $("#bulk-staff-visiting-hours").attr("style","display:block;");
         $(".onScreen").html("VISITING_HOURS")
         bulkUpdateStaff.bindBulkUpdateOnboardStaffView();
      });
  },



  bindBulkUpdatefOnboardStaffView : function () {
     var staffStatus = "ONBOARD";
     var search_text = "";
     var category = "";
     var department = "";
     var designation = "";
     var updatingFieldType = $("#updating-field-type").find(':selected').val();
     var filter = "";

     if(updatingFieldType == undefined){
       showErrorDialogBox("Please select a field to update.");
       return;
     }

     if(updatingFieldType == "UPDATE_TIMING_DETAILS"){
        filter = "In Time,Out Time,Half Day Duration(in hrs),Full Day Duration(in hrs)";
     }

     if($('#advance-search-option-div').css('display') != 'none') {
       var category = $('select.filter-category').val();
       var department = $('select.filter-department').val();
       var designation = $('select.filter-designation').val();
     }
     ajaxClient.get("/staff-management/get-bulk-staff-list-by-advance-search-text/" + staffStatus + "/" + filter +"?searchText=" + search_text
     + "&category=" + category+ "&department=" + department+ "&designation=" + designation, function(data) {
       $("#bulk-onboard-staff-list").html(data);
       $("#button").attr('style',"display:flex;");
       $("#datatables-reponsive").attr('style',"display:block");
       $('.clockpicker').clockpicker({
         'default': 'now'
       });
       bulkUpdateStaff.feedStaffDetails();
     });
   },

   bindUpdatingFieldTypeChangeEvent : function () {
     $("#updating-field-type").change(function() {
         var updatingFieldType = $(this).find(':selected').val().trim();
         var htmlUpdatingFieldType = "<option value=\"IN_TIME\">In Time</option><option value=\"OUT_TIME\">Out Time</option><option value=\"HALF_DAY_DURATION\">Half Day Duration</option><option value=\"FULL_DAY_DURATION\">Full Day Duration</option>";
         $("select#attendance-types").html(htmlUpdatingFieldType);
         $('select#attendance-types').selectpicker('refresh');
     });
   },

   bindBulkUpdateOnboardStaffView : function () {
          var staffStatus = "ONBOARD";

          var category = "";
          var department = "";
          var designation = "";

          var category = $('select.staff-category').val();
          var department = $('select.staff-department').val();
          var designation = $('select.staff-designation').val();
        ajaxClient.get("/staff-management/get-bulk-staff-list/" + staffStatus
        + "?category=" + category+ "&department=" + department+ "&designation=" + designation, function(data) {
          $("#bulk-onboard-staff-visit").html(data);
          $("#button").attr('style',"display:flex;");
          $("#datatables-reponsive").attr('style',"display:block");
          $('.clockpicker').clockpicker({
            'default': 'now'
          });

          bulkUpdateStaff.updateBulkStaffVisitingHoursDetails();
        });
   },

      bindUpdatingFieldTypeChangeEvent : function () {
        $("#updating-field-type").change(function() {
            var updatingFieldType = $(this).find(':selected').val().trim();
            var htmlUpdatingFieldType = "<option value=\"IN_TIME\">In Time</option><option value=\"OUT_TIME\">Out Time</option><option value=\"HALF_DAY_DURATION\">Half Day Duration</option><option value=\"FULL_DAY_DURATION\">Full Day Duration</option>";
            $("select#attendance-types").html(htmlUpdatingFieldType);
            $('select#attendance-types').selectpicker('refresh');
        });
      },

  selectStaffList : function (selectAllCheckbox) {
           if (selectAllCheckbox.checked) {
                  $(".staff-select-checkbox").prop('checked', true);
                  var selectedStaffCount = $('input.staff-select-checkbox:checkbox:checked').length;
                  $("#staff-selected-count").html(selectedStaffCount);
                  bulkUpdateStaff.dataCache.selectedStaffCount = selectedStaffCount;
                  return;
              }

              $(".staff-select-checkbox").prop('checked', false);
              $("#staff-selected-count").html(0);
              bulkUpdateStaff.dataCache.selectedStaffCount = 0;
  },

 updateBulkStaffVisitingHoursDetails: function(){
         $("#updateVisitingDetails").on('click', function () {
         $("#weekdays").empty();
         daysOfWeekMap.forEach(function(value, day) {
              $("#weekdays").append("<option value='" + day + "'>" + day.charAt(0).toUpperCase() + day.slice(1).toLowerCase() + "</option>");
         });
         $("#staff-visiting-hours-div").empty();
         var value = parseInt($("#staff-selected-count").text());
          if(value<=0){
               showErrorDialogBox("Please select a staff to update details")
               return;
          }
          $("#bulk-update-staff-visiting-hours-details-modal").modal({backdrop: 'static', keyboard: false});
          onboardStaff.sameForAll();
          });
 },

 staffSelectCheckbox : function(staffSelectCheckbox) {
         var staffStudentCount = $('input.staff-select-checkbox:checkbox:checked').length;
         $("#staff-selected-count").html(staffStudentCount);
         bulkUpdateStaff.dataCache.selectedStaffCount = staffStudentCount;
  },

   feedStaffDetails : function () {
     $("tr.staff-details-row").each(function() {
       var staff = JSON.parse($(this).find('.staff').text());
       var inTimeVal = "";
       var outTimeVal = "";
       var halfDayDurationVal = "";
       var fullDayDurationVal = "";

       if(staff.staffTimingDetails != null && staff.staffTimingDetails != "" && staff.staffTimingDetails != undefined && staff.staffTimingDetails.inTime != null && staff.staffTimingDetails.outTime != null && staff.staffTimingDetails.halfDayDuration != null && staff.staffTimingDetails.fullDayDuration != null){
          inTimeVal = staff.staffTimingDetails.inTime;

          if(inTimeVal.hour >= 0 && inTimeVal.hour<=9){
               inTimeVal.hour = "0"+inTimeVal.hour;
          }

          if(inTimeVal.minute >= 0 && inTimeVal.minute<=9){
              inTimeVal.minute = "0"+inTimeVal.minute;
          }

           outTimeVal = staff.staffTimingDetails.outTime;

           if(outTimeVal.hour >= 0 && outTimeVal.hour<=9){
                outTimeVal.hour = "0"+outTimeVal.hour;
           }

           if(outTimeVal.minute >= 0 && outTimeVal.minute<=9){
               outTimeVal.minute = "0"+outTimeVal.minute;
           }

           halfDayDurationVal = staff.staffTimingDetails.halfDayDuration;
           fullDayDurationVal = staff.staffTimingDetails.fullDayDuration;
        }
       $(this).find('.staff-attendance-status').each(function() {
         var filterType = JSON.parse($(this).find('.filter_type').text().trim());
         if(filterType == IN_TIME) {
           var inTime = "<input type=\"text\" class=\"form-control clockpicker\" id=\"in-time\" placeholder=\"Enter In Time ...\">";
           var inTimeValString = ""
           if(inTimeVal != ""){
              inTimeValString = inTimeVal.hour +":"+inTimeVal.minute;
           }
           $(this).append(inTime);
           $(this).find('#in-time').val(inTimeValString);
         }
         else if(filterType == OUT_TIME) {
           var outTime = "<input type=\"text\" class=\"form-control clockpicker\" id=\"out-time\" placeholder=\"Enter Out Time ...\">";
           var outTimeValString = ""
           if(outTimeVal != ""){
              outTimeValString = outTimeVal.hour +":"+outTimeVal.minute;
           }
           $(this).append(outTime);
           $(this).find('#out-time').val(outTimeValString);
         }
         else if(filterType == HALF_DAY_DURATION){
           var halfTime = "<input type=\"number\" class=\"form-control\" id=\"half-duration\" placeholder=\"Enter Half Day Duration ...\">";
           $(this).append(halfTime);
           $(this).find('#half-duration').val(halfDayDurationVal);
         }
         else if(filterType == FULL_DAY_DURATION){
           var fullTime = "<input type=\"number\" class=\"form-control\" id=\"full-duration\" placeholder=\"Enter Full Day Duration ...\">";
           $(this).append(fullTime);
           $(this).find('#full-duration').val(fullDayDurationVal);
         }
         $('.clockpicker').clockpicker({
          'default': 'now'
        });
       });
     });
   },

   autofillData : function(){
      var inTimeData = $('#inTimeFill').val();
      var outTimeData = $('#outTimeFill').val();
      var halfDayData = $('#halfDayFill').val();
      var fullDayData = $("#fullDayFill").val();
      $("tr.staff-details-row").each(function() {
        if(inTimeData != ""){
           $(this).find('#in-time').val(inTimeData);
        }

        if(outTimeData!=""){
          $(this).find('#out-time').val(outTimeData);
        }

        if(halfDayData!=""){
          $(this).find('#half-duration').val(halfDayData);
        }

        if(fullDayData!=""){
          $(this).find('#full-duration').val(fullDayData);
        }

      });
   },

   validateTime : function(){
     var inTimeData = $('#inTimeFill').val();
     var outTimeData = $('#outTimeFill').val();
     var halfTimeData = $('#halfDayFill').val();
     var fullTimeData = $('#fullDayFill').val();
     if(inTimeData != ""){
        if(inTimeData >= outTimeData){
          showErrorDialogBox("In Time should not be greater than or equal to Out Time");
          $('#outTimeFill').val("");
          return;
        }
     }
     if(halfTimeData < 0){
        showErrorDialogBox("Please Enter any positive value for the Half Time");
        $('#halfDayFill').val("");
        return;
     }
     if(fullTimeData < 0){
        showErrorDialogBox("Please Enter any positive value for the Full Time");
        $('#fullDayFill').val("");
        return;
     }
   },

     saveStaffDetails : function () {
        var staffDetailsList = [];
        var staffVisitingDetailsPayload = null;
        var staffDetailsParametersList = [];
            staffDetailsParametersList.push(null);
        var onScreen = $(".onScreen").text();
        if(onScreen == "TIMING_DETAILS"){
          $("tr.staff-details-row").each(function() {
              var staff = JSON.parse($(this).find('.staff').text());
              var staffId = staff.staffId;
              var staffCategoryId = staff.staffCategoryId;

              var inTimeValString = "";
              var outTimeValString = "";
              var halfDayDurationVal = "";
              var fullDayDurationVal = "";

              if(staff.staffTimingDetails == null || staff.staffTimingDetails == ""){
                inTimeValString = {"hour":"00","minute":"00"};
                outTimeValString = {"hour":"00","minute":"00"};
                halfDayDurationVal = 0;
                fullDayDurationVal = 0;
              }

              else{
                  var inTimeVal = staff.staffTimingDetails.inTime;
                  var outTimeVal = staff.staffTimingDetails.outTime;
                  var halfDayDurationVal = staff.staffTimingDetails.halfDayDuration;
                  var fullDayDurationVal = staff.staffTimingDetails.fullDayDuration;

                  if(inTimeVal!= null){
                    inTimeValString = inTimeVal.hour +":"+inTimeVal.minute;
                  }
                  else{
                    inTimeValString = "00" +":"+ "00";
                  }

                  if(outTimeVal!= null){
                    outTimeValString = outTimeVal.hour +":"+outTimeVal.minute;
                  }
                  else{
                    outTimeValString = "00" +":"+ "00";
                  }

                  if(halfDayDurationVal!=null){
                    halfDayDurationVal = staff.staffTimingDetails.halfDayDuration;
                  }
                  else{
                     halfDayDurationVal = 0;
                  }

                  if(fullDayDurationVal!=null){
                    fullDayDurationVal = staff.staffTimingDetails.fullDayDuration;
                  }
                  else{
                     fullDayDurationVal = 0;
                  }
                }


              var departmentDesignationMapping = staff.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID;
              var inTime = $(this).find('#in-time').val() === undefined
                  ? inTimeValString : $(this).find('#in-time').val();
              var outTime = $(this).find('#out-time').val() === undefined
                      ? outTimeValString : $(this).find('#out-time').val();
              var halfDuration = $(this).find('#half-duration').val() === undefined
                          ? halfDayDurationVal : $(this).find('#half-duration').val();
              var fullDuration = $(this).find('#full-duration').val() === undefined
                                      ? fullDayDurationVal : $(this).find('#full-duration').val();
              var staffTimings = bulkUpdateStaff.getStaffTimingDetails(inTime,outTime,halfDuration,fullDuration);

              staffDetailsList.push({'staffId':staffId,'staffTimingDetails':staffTimings});
         }
       );
       staffDetailsParametersList = ['TIMING_DETAILS'];
     } else {
        var staffIdList = bulkUpdateStaff.getSelectedBulkStaffIds();
        var staffVisitingDaysList = admitStaff.getStaffVisitingDetails();
        if(staffVisitingDaysList === undefined || staffVisitingDaysList.length === 0) {
               $("#bulk-update-staff-visiting-hours-details-modal").modal('toggle');
               showErrorDialogBox("Please fill data correctly")
               return;
        }
        $("#bulk-update-staff-visiting-hours-details-modal").modal('toggle');
        staffVisitingDetailsPayload = {'staffIdList': staffIdList, 'staffVisitingDaysList': staffVisitingDaysList}
        staffDetailsParametersList = ['VISITING_HOURS']
        staffDetailsList = null
        }

       var bulkUpdateStaffPayload = {'instituteId' : null,'staffDetailsParametersList':staffDetailsParametersList,'staffTimingDetails' : staffDetailsList, 'staffVisitingDaysPayload': staffVisitingDetailsPayload}
       ajaxClient.post("/staff-management/bulk-update-staff", {'bulkUpdateStaffPayload': JSON.stringify(bulkUpdateStaffPayload)}, function(data) {
         $("#onboard-staff-confirmation-modal-container").html(data);
         if(onScreen == "TIMING_DETAILS"){
            $("#admission-status-modal").modal('toggle');
            $('.clockpicker').clockpicker({
              'default': 'now'
            });
            bulkUpdateStaff.bindBulkUpdatefOnboardStaffView()
         } else {
            bulkUpdateStaff.bindBulkUpdateOnboardStaffView()
         }
       });
     },

    getSelectedBulkStaffIds :function () {
       var staffIds = [];
       $("input.staff-select-checkbox").each(function() {
          if(!$(this).is(":checked")) {
            return;
          }
          var staffId = $(this).parent().find('p.bulk-notification-staff-id').first().text().trim();
          staffIds.push(staffId);
       });
        return staffIds;
    },

     getStaffTimingDetails : function (inTime,outTime,halfDuration,fullDuration) {

      var inTimeVal = inTime;
       var inTime = null;
       if(inTimeVal != "" && inTimeVal != undefined && inTimeVal != null) {
         var token = inTimeVal.split(":");
         var inHour = token[0];
         var inMinute = token[1];
         var inSecond = '00';
         inTime = {'hour' : inHour, 'minute' : inMinute, 'second' : inSecond};
       }

       var outTimeVal = outTime;
       var outTime = null;
       if(outTimeVal != "" && outTimeVal != undefined && outTimeVal != null) {
         var token = outTimeVal.split(":");
         var outHour = token[0];
         var outMinute = token[1];
         var outSecond = '00';
         outTime = {'hour' : outHour, 'minute' : outMinute, 'second' : outSecond};
       }

       var halfDayDuration = halfDuration;
       if(halfDayDuration != "" && halfDayDuration != undefined && halfDayDuration != null) {
         halfDayDuration = parseFloat(halfDayDuration)
       } else {
         halfDayDuration = null;
       }

      var fullDayDuration = fullDuration;
       if(fullDayDuration != "" && fullDayDuration != undefined && fullDayDuration != null) {
         fullDayDuration = parseFloat(fullDayDuration)
       } else {
         fullDayDuration = null;
       }

       staffTimingDetails = {'inTime' : inTime, 'outTime' : outTime, 'halfDayDuration' : halfDayDuration, 'fullDayDuration' : fullDayDuration};

       return staffTimingDetails;
     },
};

var relieveStaff = {

  staffStatus : "",

  loadRelieveStaffMainPage : function () {
    var staffStatus = "RELIEVED";
    ajaxClient.get("/staff-management/staff-details/"+staffStatus, function(data) {
      $("#main-content").html(data);
      relieveStaff.bindSearchStaffRelieveDetails();
      relieveStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      relieveStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
          "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
          "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
          "staff-present-state", "staff-present-country", "staff-present-zipcode");
      initDateWithYearRange("-70:+10", true);
      $('.selectpicker').selectpicker();
      $('.clockpicker').clockpicker({
        'default': 'now'
      });
      $('#datatables-reponsive').DataTable( {
        "paging":   false,
        searching: false,
        columnDefs: [
          { targets: "no-sort", orderable: false },
        ],
        order: [[2, 'asc']]
      });
    });
  },

  loadRelieveStaffPageReload : function () {
    var staffStatus = "RELIEVED";
    ajaxClient.get("/staff-management/staff-details-page-reload/"+staffStatus, function(data) {
      $("#search-relieve-staff-results").html(data);
      relieveStaff.refreshSearchFields();
      relieveStaff.bindSearchStaffRelieveDetails();
      relieveStaff.registerUpdateStaffDetailsCallBack();
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      relieveStaff.registerViewStaffDetailsCallBack();
      staffDocumentsDetails.registerUploadStaffDocumentCallBack();
      initDateWithYearRange("-70:+10", true);
    });
  },

  refreshSearchFields : function () {
    $("#relieve-staff-search-text").val("");
    $(".staff-type-dropdown-div").each(function() {
      if($(this).find('.staff-type:checked').length > 0) {
          $(this).prop("disabled",true);
      }
    });
  },

  bindSearchStaffRelieveDetails : function () {
    $('#relieve-staff-search').on('click', function () {
        relieveStaff.bindSearchStaffRelieveDetailsView();
    });
    $("#relieve-staff-search-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          relieveStaff.bindSearchStaffRelieveDetailsView();
      }
    });
    $('#apply-staff-type-search-filter').on('click', function () {
        relieveStaff.bindSearchStaffRelieveDetailsView();
    });
  },

  bindSearchStaffRelieveDetailsView : function () {
     var search_text = $("#relieve-staff-search-text").val();
     var staffStatus = "RELIEVED";
     var staffType = "";
     var count = 0;
     $(".staff-type-dropdown-div").each(function() {
       if($(this).find('.staff-type:checked').length > 0) {
         if(count == 0) {
           staffType += $(this).find('.staff-type:checked').attr('id').toString();
         }
         else {
           staffType += "," + $(this).find('.staff-type:checked').attr('id').toString();
         }
         count++;
       }
     });

     var category = "";
     var department = "";
     var designation = "";

     if($('#advance-search-option-div').css('display') != 'none') {
       var category = $('select.filter-category').val();
       var department = $('select.filter-department').val();
       var designation = $('select.filter-designation').val();
     }


     ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/" + staffStatus + "?searchText=" + search_text
     + "&category=" + category+ "&department=" + department+ "&designation=" + designation, function(data) {

       $("#relieve-staff-list").html(data);

       relieveStaff.bindSearchStaffRelieveDetails();
       relieveStaff.registerUpdateStaffDetailsCallBack();
       previousTabSwtichingEvent();
       nextTabSwtichingEvent();
       enforceConstraints();
       relieveStaff.registerViewStaffDetailsCallBack();
       staffDocumentsDetails.registerUploadStaffDocumentCallBack();
       staffPermanentAddressUpdate("permanent-same-as-present", "staff-permanent-address-1",
           "staff-permanent-address-2", "staff-permanent-city",  "staff-permanent-state", "staff-permanent-country",
           "staff-permanent-zipcode", "staff-present-address-1", "staff-present-address-2", "staff-present-city",
           "staff-present-state", "staff-present-country", "staff-present-zipcode");
       initDateWithYearRange("-70:+10", true);
   });
 },

 registerUpdateStaffDetailsCallBack : function (){
   $('.update-staff-info').on('click', function () {
     relieveStaff.staffStatus = 'RELIEVED';
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffInformation(staffInfo);
     enforceConstraints();
     $('.staff-details-screen').attr('style','display:none');
     $('#staff-list-update-staff-screen').attr('style','display:block');
     $('#screen-name').text('STAFF-LIST');
     $('#staff-status-screen').text('RELIEVED');
   });
 },

 registerViewStaffDetailsCallBack : function () {
   $('.view-staff-info').on('click', function () {
     $('#staff-list-view-staff-screen').attr('style','display:block');
     $('#view-staff-details-modal').modal('toggle');
     var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
     var staffInfo = JSON.parse(staffInfoJson);
     fillStaffViewInformation(staffInfo);
   });
 },

  deleteStaff : function () {
    $("#staff-list-relieve-delete-staff-screen").modal('toggle');
      var staffStatus = "RELIEVED";
      var staffId = $("#delete-relieve-staff-id").text();
      ajaxClient.get("/staff-management/delete-staff/" + staffId + "/" + staffStatus, function(data) {
        $("#relieve-staff-confirmation-modal-container").html(data);
        $("#staff-relieve-status-modal").modal({backdrop: 'static', keyboard: false});
        relieveStaff.loadRelieveStaffPageReload();
      });
   },

   populateDeleteStaffModal : function (staffId) {
     $("#delete-relieve-staff-id").text(staffId);
     $("#delete-relieve-staff-details-modal-text").html("Are you sure to delete staff details?");
   },

   markStaffOnboard : function () {
     $("#staff-list-onboard-staff-screen").modal('toggle');
     var staffId = $("#relieve-staff-id").text();
     var createUser = true;
     var userType = "STAFF";
     var instituteScope = $('select.filter-institute-scope').val();
     var authorizedModules = $('select.filter-authorized-module').val();
     var roles = $('select.filter-roles').val();

     var user = {'instituteId' : null, 'uuid' : staffId, 'userType' : userType, 'authorizedModules' : authorizedModules, 'roles' : roles, 'instituteScope' : instituteScope}
     var staffOnboardPayload = {'staffTimingDetails' : null, 'userPayload' : user};

       ajaxClient.post("/staff-management/onboard-staff/" + staffId + "/" + createUser,{'staffOnboardPayload':JSON.stringify(staffOnboardPayload)}, function(data) {
          $("#relieve-staff-confirmation-modal-container").html(data);
          $("#staff-relieve-status-modal").modal({backdrop: 'static', keyboard: false});
          relieveStaff.loadRelieveStaffPageReload();
         });
   },

   populateMarkStaffOnboardStaffModal : function (staffId) {
     $("#relieve-staff-id").text(staffId);
     $("#relieve-staff-details-modal-text").html("Are you sure to onboard staff? If staff have user it will automatically get enable.");
   },

};

var staffReports = {

    dataCache : {},

    loadReportsMenu: function (){
        ajaxClient.get("/staff-management/reports", function(data) {
            $("#main-content").html(data);
            initSelect2("All");
            commonUtils.bindCardHoverEvent();
            commonUtils.bindReportCardClickEvent();
            staffReports.bindGenerateReportEvent();
            staffReports.loadOnChangeEventsFilter();
            staffReports.checkboxEvents();
            staffReports.dataCache.filterationCriteria = new Object();
        });
    },

    loadOnChangeEventsFilter : function () {
      $('select.filter-gender').on('change', function(){
          staffReports.dataCache.filterationCriteria.gender = $(this).val();
      });
      $('select.filter-religion').on('change', function(){
          staffReports.dataCache.filterationCriteria.religion = $(this).val();
      });
      $('select.filter-category').on('change', function(){
          staffReports.dataCache.filterationCriteria.category = $(this).val();
      });
      $('select.filter-states').on('change', function(){
          staffReports.dataCache.filterationCriteria.states = $(this).val();
      });
    },

    bindGenerateReportEvent: function (){
      $('.generate-report').on('click', function () {
        var gender = "";
        if(staffReports.dataCache.filterationCriteria.gender != undefined && staffReports.dataCache.filterationCriteria.gender != null) {
          gender = staffReports.dataCache.filterationCriteria.gender.join(",");
        }

        var religion = "";
        if(staffReports.dataCache.filterationCriteria.religion != undefined && staffReports.dataCache.filterationCriteria.religion != null) {
          religion = staffReports.dataCache.filterationCriteria.religion.join(",");
        }

        var category = "";
        if(staffReports.dataCache.filterationCriteria.category != undefined && staffReports.dataCache.filterationCriteria.category != null) {
          category = staffReports.dataCache.filterationCriteria.category.join(",");
        }

        var states= "";
        if(staffReports.dataCache.filterationCriteria.states != undefined && staffReports.dataCache.filterationCriteria.states != null) {
          states = staffReports.dataCache.filterationCriteria.states.join(",");
        }

          var containerElement = $(this).closest('div.report-field-container');
          var invalid = validateMandatoryFields($(containerElement));
          if(invalid){
            return;
          }

          var staffStatus = "";
          if($(containerElement).find(".staff-status").length > 0){
                staffStatus = $(containerElement).find(".staff-status").val().join();
          }

          var reportType = $(containerElement).find('p.report-type').text().trim();

          var staffCategory="";
          if($(containerElement).find("select.filter-staff-category").length > 0){
            staffCategory = $(containerElement).find("select.filter-staff-category").val().join();
          }

          var staffDepartment="";
          if($(containerElement).find("select.filter-department").length > 0){
            staffDepartment = $(containerElement).find("select.filter-department").val().join();
          }

          var staffDesignation="";
          if($(containerElement).find("select.filter-designation").length > 0){
            staffDesignation = $(containerElement).find("select.filter-designation").val().join();
          }

          $(this).closest('div.modal').modal('toggle');
          var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);

          window.open(baseURL+"/staff-management/generate-report/"+reportType+"?staffStatus="+staffStatus+"&staffCategory="+staffCategory+"&staffDepartment="+staffDepartment+"&staffDesignation="+staffDesignation+"&requiredHeaders="+requiredHeadersCSV+"&userCategory="+category+"&gender="+gender+"&religion="+religion+"&state="+states, '_blank');
      });
    },

    resetPopup : function () {
      staffReports.resetCheckBoxes();
    },

    resetCheckBoxes : function () {
      $( ".parent" ).prop( "checked", false );
      $( ".child" ).prop( "checked", false );
      $( ".basic" ).prop( "checked", true );
      $( ".basic-report-column" ).prop( "checked", true );
    },

    checkboxEvents : function () {
      $('.child').on('change', function(){
        $(this).parent().parent().parent().find($(".parent")).prop( "checked", false );
      });
      $('.parent').on('change', function(){
        if($(this).prop("checked")){
          $(this).parent().parent().find($(".child")).prop( "checked", true );
        }
        else if(!($(this).prop("checked"))){
          $(this).parent().parent().find($(".child")).each(function() {
            if (!$(this).is(':disabled')) {
                $(this).prop( "checked", false );
            }
          });
        }
      });
    }
};

var staffDocumentsDetails = {

  registerUploadStaffDocumentCallBack : function(){
    $('.upload-staff-document').on('click', function () {
      var staffInfoJson = $(this).parent().parent().find('.staff-info-json').text().trim();
      var staffInfo = JSON.parse(staffInfoJson);
      $("#upload-document-staff-id").text(staffInfo.staffId);
      $('.staff-details-screen').attr('style','display:none');
      $('#staff-list\\.upload-document-screen').attr('style','display:block');
      $("#upload-document-staff-name").text(staffInfo.staffBasicDetailsWithCategoryDepartDesignation.name);
      staffDocumentsDetails.populateUploadedDocuments(staffInfo.staffDocuments);
      $('#screen-name').text('STAFF-LIST');
      $('#staff-status-screen').text(staffInfo.staffStatus);
    });

    $("#upload-staff-document-type").change(function() {
        var documentType = $(this).find(':selected').val().trim();
        if(documentType == OTHER_DOCUMENT_TYPE){
          $("#upload-staff-document-name").parent().attr("style","display:block");
        }else{
          $("#upload-staff-document-name").parent().attr("style","display:none");
        }
    });
    registerUploadFileCallback();
  },

  bindStaffDocumentActions : function() {
    $('.download-staff-document').on('click', function () {
        var staffId = $("#upload-document-staff-id").text().trim();
        var documentId = $(this).parent().find('p.view-document-id').text().trim();
        window.open(baseURL+"/staff-management/document-download/"+staffId+"/"+documentId, '_blank');
    });

    $('.delete-staff-document').on('click', function () {
        var documentId = $(this).parent().find('p.view-document-id').text().trim();
        $("#staff-document-delete-confirm-button").attr("onclick","staffDocumentsDetails.deleteStaffDocument('"+documentId+"')");
        $("#staff-document-delete-confirm-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  deleteStaffDocument : function (documentId){
      var staffId = $("#upload-document-staff-id").text().trim();
      ajaxClient.post("/staff-management/document-delete/"+staffId+"/"+documentId,{},function(data){
        $("#staff-document-status-modal-container").html(data);
        $("#staff-document-delete-status-modal").modal({backdrop: 'static', keyboard: false});
        var staffDocumentsJson = $("#success-document-delete-response").text().trim();
        var staffDocuments = JSON.parse(staffDocumentsJson);
        staffDocumentsDetails.populateUploadedDocuments(staffDocuments);
      });
  },

  populateUploadedDocuments : function (staffDocuments) {
      if(staffDocuments == null || staffDocuments.length == 0){
        $("#staff-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
        return;
      }
      var documentsList = "<br>";
      var itemsPerRow = 3;
      var numberOfDocument = 0;
      for(var i = 0 ; i < staffDocuments.length; i++){
          if(numberOfDocument % 3 == 0){
              if(numberOfDocument != 0){
                  documentsList += "</div>";
              }
              documentsList += "<div class=\"row\">";
          }
          var staffDocument = staffDocuments[i];
          if(staffDocument.documentType === "STAFF_PROFILE_IMAGE_THUMBNAIL") {
            continue;
          }
          numberOfDocument++;
          var uplaodTimeText = "Uploaded on : " + getFormattedDate(staffDocument.uploadTime);
          documentsList += "<div class=\"col-sm-4\"> <div class=\"card bg-light text-center\"> <div class=\"card-header\"> <h5> <strong> "+ staffDocument.documentName + " </strong></h5> </div> <div class=\"card-body\"> <p style=\"display:none;\" class=\"view-document-id\"> "+ staffDocument.documentId + " </p> <p class=\"card-text\"> Category : "+ staffDocument.documentTypeDisplayName+" </p> <a href=\"#\" class=\"btn btn-outline-info download-staff-document\">Download </a> <a href=\"#\" class=\"btn btn-outline-danger delete-staff-document\">Delete </a> </div> <div class=\"card-footer text-muted\"> "+ uplaodTimeText + " </div> </div> </div>"
      }
      documentsList += "</div> <br>";
      $("#staff-uploaded-documents").html(documentsList);
      staffDocumentsDetails.bindStaffDocumentActions();
  },

  resetNewDocumentUploadPopup : function() {
      $("#upload-staff-document-type").val("");
      $("#upload-document-file").val("");
      $("#upload-document-file-label").text("");
      $("#upload-staff-document-name").val("");
      $("#upload-staff-document-name").parent().attr("style","display:none");
  },

  uploadStaffDocument : async function() {
      var staffId = $("#upload-document-staff-id").text();
      var documentType = $("#upload-staff-document-type option:selected").val().trim();
      if(documentType == ""){
        showErrorDialogBox("Document type field is mandatory please fill it then proceed.");
        return;
      }
      var file ;
      var ONE_KB = 1024;
      if (($("#upload-document-file"))[0].files.length > 0) {
        var uncompressedFile = ($("#upload-document-file"))[0].files[0];
        console.log(uncompressedFile);
        file = await compressFileUtils.compress(uncompressedFile);
        console.log(file);
        console.log("final file size : " + file.size);
        if((file.size / ONE_KB) > STAFF_IMAGE_SIZE_LIMIT){
          showErrorDialogBoxWithExistingModalDetails("File size exceeds " + STAFF_IMAGE_SIZE_LIMIT + " KB after compression. Please reduce the file size and try uploading again.", "#upload-new-document-modal");
          return;
        }
      } else {
          showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload", "#upload-new-document-modal");
          return;
      }
      var documentName = "";
      if(documentType == OTHER_DOCUMENT_TYPE){
        documentName = $("#upload-staff-document-name").val();
        if(documentName == "") {
          showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed", "#upload-new-document-modal");
          return;
        }
      }
      var formData = new FormData();
      formData.append('document', file);
      formData.append('documentType', documentType);
      formData.append('documentName', documentName);
      formData.append('staffId', staffId);
      $("#upload-new-document-modal").modal("toggle");
      ajaxClient.uploadFile("/staff-management/document-upload", formData, function(data){
          $("#staff-document-status-modal-container").html(data);
          $("#staff-document-upload-status-modal").modal({backdrop: 'static', keyboard: false});
          var staffDocumentsJson = $("#success-document-upload-response").text().trim();
          var staffDocuments = JSON.parse(staffDocumentsJson);
          staffDocumentsDetails.populateUploadedDocuments(staffDocuments);
      });
  },
};

var generateDocuments =  {

  dataCache : {},

  generateDocument : function () {
    var documentType = $("#generate-staff-document-type").find(':selected').val().trim();
    var staffId = $("#upload-document-staff-id").text();

    window.open(baseURL + "/staff-management/generate-document/"+staffId+"/"+documentType, '_blank');
  },

};

function advanceSearch () {
  $("#advance-search-option-div").toggle();
};
