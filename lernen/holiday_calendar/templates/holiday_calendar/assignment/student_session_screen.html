<div class="container-fluid card">
    <div class="card-body">
        <div class="bulk-reminder-tab active" id='bulk-reminder-main-tab'>
          <form>
            <div class="form-row align-items-center">
              <div class="col-md-3">
                <label>Classes</label>
                <select class="form-control select-template-class select2" multiple data-toggle="select2">
                  {% for standard in standards %}
                    {% if standard.standardSectionList %}
                        {% for section in standard.standardSectionList %}
                          {% with section.sectionId|stringformat:"s" as section_id %}
                            {% with standard.standardId|add:":"|add:section_id as standard_section_map_id %}
                              <option value={{standard_section_map_id}}>{{standard.displayName}} - {{section.sectionName}}</option>
                            {% endwith %}
                          {% endwith %}
                        {% endfor%}
                    {% else %}
                      <option value={{standard.standardId}}>{{standard.displayName}}</option>
                    {% endif %}
                  {% endfor%}
                </select>
              </div>
              <div class="form-group col-md-3" style="padding-top:2rem">
                 <div class="form-check">
                   <input type="checkbox" id="template-not-assigned-checkbox" class="form-check-input" >
                   <label class="form-check-label"> Calendar Not Assigned Students Only</label>
                 </div>
               </div>
              <div class="col-md-3">
                <button style="width:50%;margin-top: 35px;" type="button" class="btn btn-primary mb-2" onclick="assignHolidayTemplate.searchStudents()">Search</button>
              </div>
            </div>
          </form>

          <br>

          <div id="student-template-list">
            {% include 'holiday_calendar/assignment/student_template_list.html' %}
          </div>
        </div>
    </div>
     <br>
   <br>
</div>

<div id="bulk-student-template-assignment-confirmation-modal" class="modal" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Update Calendar Assignment</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
     <strong> <span id="bulk-student-template-assignment-confirmation-modal-text"> </span> </strong>
     <br>
     <br>
      <div class="form-row align-items-center">
        <div class="col-md-6">
         <label>Calendar*</label>
         <select id="assignment-holiday-template" class="form-control mandatory-field">
              <option value="">select</option>
              {% for session_holiday_template in session_holiday_templates %}
                <option value="{{session_holiday_template.templateId}}">{{session_holiday_template.templateName}} ({{session_holiday_template.templateType}})</option>
              {% endfor %}
         </select>
        </div>
      </div>
     </div>
     <div class="modal-footer">
       <button type="button" class="btn btn-secondary" data-dismiss="modal">Review</button>
       <button type="button" class="btn btn-danger" onclick="assignHolidayTemplate.assignStudentTemplate()">Yes, Update Calendar</button>
     </div>
   </div>
 </div>
</div>