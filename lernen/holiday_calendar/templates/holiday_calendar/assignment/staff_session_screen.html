<div class="container-fluid card">
    <div class="card-body">
        <div class="bulk-reminder-tab active" id='bulk-reminder-main-tab'>
          <br>
          <div id="staff-template-list">
            {% include 'holiday_calendar/assignment/staff_template_list.html' %}
          </div>
        </div>
    </div>
     <br>
   <br>
</div>

<div id="bulk-staff-template-assignment-confirmation-modal" class="modal" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Update Calendar Assignment</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
     <strong> <span id="bulk-staff-template-assignment-confirmation-modal-text"> </span> </strong>
     <br>
     <br>
      <div class="form-row align-items-center">
        <div class="col-md-6">
         <label>Calendar*</label>
         <select id="assignment-holiday-template" class="form-control mandatory-field">
              <option value="">select</option>
              {% for session_holiday_template in session_holiday_templates %}
                <option value="{{session_holiday_template.templateId}}">{{session_holiday_template.templateName}} ({{session_holiday_template.templateType}})</option>
              {% endfor %}
         </select>
        </div>
      </div>
     </div>
     <div class="modal-footer">
       <button type="button" class="btn btn-secondary" data-dismiss="modal">Review</button>
       <button type="button" class="btn btn-danger" onclick="assignHolidayTemplate.assignStaffTemplate()">Yes, Update Calendar</button>
     </div>
   </div>
 </div>
</div>