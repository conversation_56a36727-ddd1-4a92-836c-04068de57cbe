<div class="container-fluid" style="width:100%; height:100%;" id="update.product-group">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="#">Product Groups</a></li>
        <li class="breadcrumb-item " aria-current="page">Update</li>
      </ol>
    </nav>
    <div class="container-fluid border rounded" style="width:100%; height:100%; background-color:#fff; overflow: hidden;">
        <br>
        <div class="form-row">
            <div class="form-group col-md-6">
              <label>Group Name*</label>
              <input type="text" class="form-control mandatory-field no-special-chars" id="groups.update.group-name" placeholder="Group Name">
              <p class="mandatory-field" style="display: none;"> <span style="color:#e65f76; ">Mandatory Field</span></p>
              <p id ="groups.group-id" style="display: none;"></p>
            </div>
            <div class="form-group col-md-6">
                <label>Remark</label>
                <textarea class="form-control" id="groups.update.description" rows="1" placeholder="Group Description"></textarea>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-md-4">
              <label>Group Discount(%)</label>
              <input type="number" class="form-control no-special-chars percentage-range" id="groups.update.discount" placeholder="Discount...">
            </div>
        </div>
        <br>
        <h4>  Group products </h4>
        <table class="table" id="groups.update.products_list">
            <thead>
                <tr>
                    <th scope="col" class="w-25">Product Name*</th>
                    <th scope="col">Batch*</th>
                    <th scope="col"></th>
                    <th scope="col">Quantity*</th>
                    <th scope="col">Price*</th>
                    <th scope="col">Discount(%)</th>
                    <th scope="col">Amount</th>
                    <th scope="col"> </th>
                </tr>
            </thead>
            <tbody>
                <tr id="groups.update.search_row">
                    <td colspan="1">
                        <input type="text" class="form-control" id="groups.update.search-product" placeholder="Type Item name to add . . .">
                        <div id="groups.update.search-result"></div>
                    </td>
                    <td style="width:15%;">
                        <select class="custom-select product-batch-select" disabled>
                            <option selected>select...</option>
                        </select>
                    </td>
                    <td></td>
                    <td><input type="number" class="form-control form-control-sm"  placeholder="Quanity" readonly></td>
                    <td><input type="number" class="form-control form-control-sm"  placeholder="Price" readonly></td>
                    <td><input type="number" class="form-control form-control-sm"  placeholder="Discount" readonly></td>
                    <td><input type="number" class="form-control form-control-sm"  placeholder="Amount" readonly></td>
                    <!-- <td><input type="number" class="form-control form-control-sm"  placeholder="Price" readonly></td> -->
                </tr>
            </tbody>
            <p id="mandatory-row" style="display: none;"> <span style="color:#e65f76; ">Add atleast one product</span></p>
        </table>

    </div>
     <br>
    <div style="float:right">
       <button type="submit" class="btn btn-secondary" onclick="inventoryProductGroup.returnToMainScreen()">Close</button>
       <button type="submit" class="btn btn-primary" data-toggle="modal" data-target="#groups-update-confirm-modal">Update Group</button>
   </div>
   <br>
</div>
