{% load json %}
{% load string_utils %}
<div>
  {% if update is False %}
  <p style="display:none;" id="attendance_data_json"> {{attendance_register | jsonstr}}</p>
  <br /><br />
  <div class="row text-center" style="background-color:#f7f9fc;">
    <div class="col-12 col-sm-6 col-xxl">
      <div class="card mt-4">
        <div class="card-body" style="padding: 10px;">
              <span class="badge badge-soft-info pl-3 pr-3"  id="total_student_span">
                <i class="mdi mdi-arrow-bottom-right"></i>
              </span>
              <span>&nbsp;&nbsp;Total</span>
        </div>
      </div>
    </div>

    <div class="col-12 col-sm-6 col-xxl">
      <div class="card mt-4">
        <div class="card-body" style="padding: 10px;">
          <span class="badge badge-soft-secondary pl-3 pr-3" id="total_unmarked_student_span">
            <i class="mdi mdi-arrow-bottom-right"></i>
          </span>
          <span>&nbsp;&nbsp;Unmarked</span>
        </div>
      </div>
    </div>

    <div class="col-12 col-sm-6 col-xxl">
      <div class="card mt-4">
        <div class="card-body" style="padding: 10px;">
        <span class="badge badge-soft-success pl-3 pr-3" id="total_present_student_span">
          <i class="mdi mdi-arrow-bottom-right"></i>
        </span>
        <span>&nbsp;&nbsp;Present</span>
     </div>
    </div>
  </div>

  <div class="col-12 col-sm-6 col-xxl">
    <div class="card mt-4">
      <div class="card-body" style="padding: 10px;">
        <span class="badge badge-soft-danger pl-3 pr-3"  id="total_absent_student_span">
          <i class="mdi mdi-arrow-bottom-right"></i>
        </span>
        <span>&nbsp;&nbsp;Absent</span>
      </div>
    </div>
  </div>

  <div class="col-12 col-sm-6 col-xxl">
    <div class="card mt-4">
      <div class="card-body" style="padding: 10px;">
        <span class="badge badge-soft-warning pl-3 pr-3" id="total_leave_student_span">
          <i class="mdi mdi-arrow-bottom-right"></i>
        </span>
        <span>&nbsp;&nbsp;Leave</span>
      </div>
    </div>
  </div>

  <div class="col-12 col-sm-6 col-xxl">
    <div class="card mt-4">
      <div class="card-body" style="padding: 10px;">
        <span class="badge badge-soft-warning pl-3 pr-3" id="total_half_day_student_span">
          <i class="mdi mdi-arrow-bottom-right"></i>
        </span>
        <span>&nbsp;&nbsp;Half Day</span>
      </div>
    </div>
  </div>
  </div>
 {% endif %}
  <br /><br />
  <div>
    <p style="display:none;" id="attendance_types"> {{attendance_types | jsonstr}}</p>
    <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
      <thead>
        <tr>
          <th style="display:none;" id="attendance_register"> {{attendance_register | jsonstr}}</th>
          <th scope="col" >Admission</th>
          <th scope="col" >Roll Number</th>
          <th scope="col" >Student Name</th>
          {% for attendance_type in attendance_types %}
            <th scope="col" class="no-sort">{{attendance_type.name}}</th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% for attendance_register_entry in attendance_register %}
          <tr class="student-attendance-row" id={{attendance_register_entry.student.studentId}}>
            <td style="display:none;" class="attendance_register_entry">{{attendance_register_entry|jsonstr}}</td>
            <td>{{attendance_register_entry.student.studentBasicInfo.admissionNumber}}</td>
            <td>{{attendance_register_entry.student.studentAcademicSessionInfoResponse.rollNumber}}</td> 
            <td>{{attendance_register_entry.student.studentBasicInfo.name}}</td>
            
            {% for attendance_type in attendance_types %}

              {% if update is True%}

                <td class="student-attendance-status">
                  <p class="attendance_type" style="display:none">{{attendance_type|jsonstr}}</p>
                  <select class="form-control form-control-sm attendance-status-select-tag">
                  <option value="">-select-</option>
                  {% for attendance_status in attendance_statuses %}
                    <option value={{attendance_status}}>{{attendance_status | string_capitalize}}</option>
                  {% endfor %}
                </td>

              {% else %}

                <td class="student-attendance-status">
                  <p class="attendance_type" style="display:none">{{attendance_type|jsonstr}}</p>
                  {% if attendance_register_entry.attendanceRecordList is None %}
                    -
                  {% endif %}
                </td>

              {% endif %}

            {% endfor %}

          </tr>
        {% endfor %}
      </tbody>
    </table>
    {% if update is True %}
      <div style="display: flex;  align-items: center;  justify-content: center;">
        <button type="button" class="btn btn-primary" style="width:20%;" onclick="attendanceRegister.submitAttendance()">Save Attendance</button>
      </div>
    {% endif %}
    <br />
  </div>
</div>

<div id="attendance-student-data-modal" class="modal fade bd-example-modal-xl" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="update.attendance-type-title">Student List</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true"> &times; </span>
              </button>
          </div>
          <div class="modal-body" id="attendance-student-data">
          </div>
          <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
          </div>
      </div>
  </div>
</div>
