var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1;function sidebarHoverOutState(t){$(t).find("img.sidebar_icon").attr("style",""),$(t).find("img.sidebar_icon_active").attr("style","display:none")}function sidebarHoverOnState(t){$(t).find("img.sidebar_icon").attr("style","display:none"),$(t).find("img.sidebar_icon_active").attr("style","")}function sideBarHoverEventCallback(){$(".sidebar-menu-item").hover(function(){sidebarHoverOnState(this)},function(){$(this).parent().hasClass("active")?sidebarHoverOnState(this):sidebarHoverOutState(this)})}function activateMenuItem(){$(".sidebar-menu-item").click(function(t){$(".sidebar-menu li.active").removeClass("active"),$(this).parent().addClass("active"),sidebarHoverOutState($(".sidebar-menu").find("li").not(".active").find(".sidebar-menu-item")),t.preventDefault()})}function registerSidebarMenu(){sideBarHoverEventCallback(),activateMenuItem(),registerHomeMenu(),registerOnboardNewInstituteMenu(),registerIngestStudentMenu(),registerInstituteManagementMenu()}function registerHomeMenu(){$("#homeNav").on("click",function(){instituteOnboarding.loadHomePage()})}function registerOnboardNewInstituteMenu(){$("#onboardNewInstituteNav").on("click",function(){instituteOnboarding.loadHomePage()})}function registerIngestStudentMenu(){$("#ingestStudentNav").on("click",function(){ingestStudent.loadHomePage()})}function registerInstituteManagementMenu(){$("#instituteManagementNav").on("click",function(){instituteManagement.loadHomePage()})}$(document).ready(function(){$("#sidebarCollapseImage").click(function(){var t=1;0==$("#sidebar.active").length&&(t=0);var e="";1==t?e=sidebarFull+20+"px":e=sidebarCollapsed+20+"px";$("#sidebar").toggleClass("active"),$("#company_name_footer").toggleClass("active"),$("#content").css("margin-left",e)}),registerSidebarMenu(),instituteOnboarding.loadHomePage(),paymentReminder.readPaymentState()});var instituteOnboarding={loadHomePage:function(){ajaxClient.get("/institute-onboarding/home",function(t){$("#content").html(t)})}},ingestStudent={dataCache:{},resetDataCache:function(){ingestStudent.dataCache={}},loadHomePage:function(){ajaxClient.get("/institute-onboarding/ingest-student-home",function(t){ingestStudent.resetDataCache(),$("#content").html(t);var e=readJson("#all-institute");ingestStudent.dataCache.allInstitutes=e,$("#institute-select-modal").modal({backdrop:"static",keyboard:!1})})},updateSelectedInstitute:function(){var t=$("#select-institute").find(":selected").val().trim();if(null!=t&&""!=t){for(var e=ingestStudent.dataCache.allInstitutes,n=null,a=0;a<e.length;a++){var i=e[a];i.instituteId==parseInt(t)&&(n=i)}null!=n?(ingestStudent.dataCache.selectedInstitute=n,$("#selected-institute-display").text(n.instituteId+" ("+n.instituteName+")"),$("#institute-select-modal").modal("toggle"),ajaxClient.get("/institute-onboarding/institute-academic-session/"+n.instituteId,function(t){$("#institute-academic-sessions-container").html(t)})):showErrorDialogBox("Selected institute does not exists. Please select valid institute")}else showErrorDialogBox("Invalid Institute. Please select valid institute")},loadIngestionConfigs:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId;ajaxClient.get("/institute-onboarding/student-ingestion-config/"+t,function(t){$("#student-ingestion-configuration-modal-container").html(t),ingestStudent.bindMultiColumnClassCheckBox(),$("#student-ingestion-configuration-modal").modal({backdrop:"static",keyboard:!1})})},loadIngestionresult:function(){$("#student-data-ingestion-result-modal").modal({backdrop:"static",keyboard:!1})},loadUploadFile:function(){$("#upload-student-data-file-modal").modal({backdrop:"static",keyboard:!1})},bindMultiColumnClassCheckBox:function(){$("#multi-column-class").change(function(){this.checked?$("#multi-class-column-info").attr("style",""):$("#multi-class-column-info").attr("style","display:none")}),$("#clear-all-fields").on("click",function(){$(".column-index").val(""),$(".table-mandatory-switch").prop("checked",!1)})},confirmApplyIngestionConfig:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId,e=ingestStudent.dataCache.selectedInstitute.instituteName;if(confirm("Do you want to update student ingestion configuraton for "+t+" - "+e+" ?")){$("#student-ingestion-configuration-modal").modal("toggle");var n=ingestStudent.getIngestionConfig();ajaxClient.post("/institute-onboarding/update-student-ingestion-config/"+t,{studentIngestionConfigs:JSON.stringify(n)},function(t){$("#status-modal-container").html(t),$("#status-modal").modal("toggle")})}},getIngestionConfig:function(){var t=$("#file-delimiter").val(),e=$("#select-class-input-format").find(":selected").val().trim(),n=$("#file-header").is(":checked"),a=$("#multi-column-class").is(":checked"),i=$("#dob-format").val(),s=$("#admission-date-format").val(),d=null,o=null,r=null;if(a){var u=$("#class-column-index").val().trim();""!=u&&(d=parseInt(u));var l=$("#section-column-index").val().trim();""!=l&&(o=parseInt(l));var c=$("#stream-column-index").val().trim();""!=c&&(r=parseInt(c))}var m=[];return $("tr.student-data-field-row").each(function(){var t=$(this).attr("id"),e=$(this).find(".toggle-switch:checkbox:checked").length>0,n=$(this).find(".column-index").val().trim();m.push({studentDataFieldName:t,index:n,mandatory:e})}),{studentDataFieldList:m,studentDataDelimiter:t,studentDataHeader:n,dobFormat:i,admissionDateFormat:s,studentClassColumnField:{studentDataMultiColumnClass:a,studentClassColumnFormat:e,classColumn:d,sectionColumn:o,streamColumn:r}}},uploadStudentData:function(){if($("#student-data-upload-file-input")[0].files.length<=0)showErrorDialogBox("Provide valid student data file");else{$("#upload-student-data-file-modal").modal("toggle");var t=$("#student-data-upload-file-input")[0].files[0],e=new FormData;""!=t&&e.append("document",t);var n=ingestStudent.dataCache.selectedInstitute.instituteId;ajaxClient.uploadFile("/institute-onboarding/upload-student-data-file/"+n,e,function(t){$("#status-modal-container").html(t),$("#status-modal").modal("toggle")})}},loadStudentFileData:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId,e=$("#student-file-id").val().trim();ingestStudent.dataCache.selectedFileId=e,ajaxClient.get("/institute-onboarding/load-student-file-data/"+t+"?fileId="+e,function(t){$("#student-file-data-container").html(t)})},studentIngestionDryRun:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId,e=ingestStudent.dataCache.selectedFileId,n=$("#admission-number-check").is(":checked"),a=$("#create-user-check").is(":checked");ingestStudent.dataCache.admissionNumberOnlyCheck=n,ingestStudent.dataCache.createUserCheck=a;var i=$("#institute-academic-sessions").find(":selected").val().trim();ajaxClient.post("/institute-onboarding/ingest-student-dry-run/"+t+"?fileId="+e+"&academicSessionId="+i+"&admissionNumberOnlyCheck="+n,{},function(t){$("#student-ingestion-summary-modal-container").html(t),$("#student-ingestion-dry-run-summary-modal").modal({backdrop:"static",keyboard:!1})})},confirmExecuteStudentIngestion:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId,e=ingestStudent.dataCache.selectedInstitute.instituteName,n=ingestStudent.dataCache.admissionNumberOnlyCheck,a=ingestStudent.dataCache.createUserCheck;if(confirm("Do you want to execute student ingestion for "+t+" - "+e+" ?")){$("#student-ingestion-dry-run-summary-modal").modal("toggle");var i=ingestStudent.dataCache.selectedFileId,s=$("#institute-academic-sessions").find(":selected").val().trim();ajaxClient.post("/institute-onboarding/ingest-student-execute/"+t+"?fileId="+i+"&academicSessionId="+s+"&admissionNumberOnlyCheck="+n+"&createUserCheck="+a,{},function(t){$("#status-modal-container").html(t),$("#status-modal").modal("toggle")})}},loadStudentIngestionResult:function(){var t=ingestStudent.dataCache.selectedInstitute.instituteId,e=$("#student-data-ingestion-result-id").val().trim();$("#student-data-ingestion-result-modal").modal("toggle"),ajaxClient.get("/institute-onboarding/ingest-student-result/"+t+"?resultId="+e,function(t){$("#student-ingestion-summary-modal-container").html(t),$("#student-ingestion-execution-summary-modal").modal({backdrop:"static",keyboard:!1})})}},instituteManagement={dataCache:{},resetDataCache:function(){instituteManagement.dataCache={}},loadHomePage:function(){ajaxClient.get("/institute-onboarding/management/home",function(t){instituteManagement.resetDataCache(),$("#content").html(t);var e=readJson("#all-institute");instituteManagement.dataCache.allInstitutes=e,$("#institute-select-modal").modal({backdrop:"static",keyboard:!1}),instituteManagement.bindSelectFeatureInput()})},bindSelectFeatureInput:function(){$("#feature-select").on("change",function(){var t=$(this).find(":selected").val().trim();"preferences"==t?instituteManagement.loadInstitutePreferences():"recharge-sms"==t?instituteManagement.loadInstituteSMSRechargeScreen():alert("Feature not supported")})},updateSelectedInstitute:function(){var t=$("#select-institute").find(":selected").val().trim();if(null!=t&&""!=t){var e=null;if("GLOBAL"==t)e={instituteId:"GLOBAL",instituteName:"GLOBAL"};else{for(var n=instituteManagement.dataCache.allInstitutes,a=0;a<n.length;a++){var i=n[a];i.instituteId==parseInt(t)&&(e=i)}if(null==e)return void showErrorDialogBox("Selected institute does not exists. Please select valid institute")}instituteManagement.dataCache.selectedInstitute=e,$("#selected-institute-display").text(e.instituteId+" ("+e.instituteName+")"),$("#institute-select-modal").modal("toggle")}else showErrorDialogBox("Invalid Institute. Please select valid institute")},loadInstitutePreferences:function(){var t=instituteManagement.dataCache.selectedInstitute.instituteId;ajaxClient.get("/institute-onboarding/preferences/data/"+t,function(t){$("#institute-feature-screen").html(t),instituteManagement.bindEditPreference()})},bindEditPreference:function(){$(".edit-preference").change(function(){this.checked?$(this).closest("tr").find(".preference-value").prop("disabled",!1):$(this).closest("tr").find(".preference-value").prop("disabled",!0)})},updatePreferences:function(){var t="",e={},n=1;$("tr.feature-preference-entity").each(function(){if($(this).find(".edit-preference").is(":checked")){var a=$(this).closest("tr.feature-preference-entity").attr("id"),i=$(this).closest("div.feature-preference-group").attr("id"),s=$(this).closest("div.product-feature").attr("id");s in e||(e[s]={}),i in e[s]||(e[s][i]={});var d=$(this).closest("tr.feature-preference-entity").find(".feature-preference-entity-name").text(),o=null;$(this).closest("tr").find(".preference-value").is("input")?o=$(this).closest("tr").find(".preference-value").val().toString():$(this).closest("tr").find(".preference-value").is("select")&&(o=$(this).closest("tr").find(".preference-value").find(":selected").val()),e[s][i][a]=o,t+="<tr> <td> "+n+"</td> <td> "+s+" </td> <td> "+i+"</td> <td>"+a+" </td> <td>"+d+" </td> <td> "+o+"</td> </tr>",n+=1}}),instituteManagement.dataCache.preferenceUpdatePayload=e,$("#preview-table-content").html(t),$("#institute-preference-update-preview-modal").modal("toggle")},executePreferencesUpdate:function(){var t=instituteManagement.dataCache.selectedInstitute.instituteId,e=instituteManagement.dataCache.preferenceUpdatePayload;console.log(e),$("#institute-preference-update-preview-modal").modal("toggle"),ajaxClient.post("/institute-onboarding/preferences/update/"+t,{preferenceUpdatePayload:JSON.stringify(e)},function(t){$("#status-modal-container").html(t),$("#preference-update-status-modal").modal({backdrop:"static",keyboard:!1})})},loadInstituteSMSRechargeScreen:function(){var t=instituteManagement.dataCache.selectedInstitute.instituteId;ajaxClient.get("/institute-onboarding/sms-recharge/home/"+t,function(t){$("#institute-feature-screen").html(t)})},updateSMSRechargeConfirmModal:function(){var t=$("#sms-recharge-count").val();$("#confirm-sms-recharge-count").text(t),$("#sms-recharge-confirm-modal").modal("toggle")},rechargeSMS:function(){$("#sms-recharge-confirm-modal").modal("toggle");var t=$("#sms-recharge-count").val(),e=$("#sms-recharge-amount").val(),n=$("#amount-status").find(":selected").val().trim(),a=$("#service-provider-type").find(":selected").val().trim(),i=$("#sms-recharge-description").val(),s=0;""!=t&&(s=parseInt(t));var d=0;if(""!=e&&(d=parseInt(e)),s<=0||d<0||""==n||""==a)alert("Please fill all the required fields");else{var o={smsRechargeCount:s,smsRechargeAmount:d,amountStatus:n,serviceProvider:a,smsRechargeDescription:i},r=instituteManagement.dataCache.selectedInstitute.instituteId;console.log(o),ajaxClient.post("/institute-onboarding/sms-recharge/execute/"+r,{smsRechargePayload:JSON.stringify(o)},function(t){$("#status-modal-container").html(t),$("#sms-recharge-status-modal").modal({backdrop:"static",keyboard:!1})})}},viewPreviousRecharges:function(){var t=instituteManagement.dataCache.selectedInstitute.instituteId;ajaxClient.get("/institute-onboarding/sms-recharge/history/"+t,function(t){$("#previous-recharge-history-container").html(t),$("#previous-recharge-history-modal").modal({backdrop:"static",keyboard:!1})})}};