pastReportAllowedDays = 365
var ALL = "ALL";
var ONLY_DUE_STUDENTS = "ONLY_DUE_STUDENTS";
var ONLY_NON_DUE_STUDENTS = "ONLY_NON_DUE_STUDENTS";

var feeReports = {

  dataCache : {},

  loadReportsMenu: function (){
      ajaxClient.get("/fees/reports", function(data) {
          $("#main-content").html(data);
          initDateWithYearRange("-5:+5", true);
          initSelect2("All");
          feeReports.initDataCache();
          feeReports.initMultiSelectAcademicSession();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          feeReports.bindGenerateReportEvent();
          feeReports.checkboxEvents();
          reportUtils.bindSelectClassCheckboxEvent();

          if(feeReports.dataCache.allSessions.length > 0){
            var feeDropDown = feeReports.getFeesDropDown(feeReports.dataCache.allSessions[0].academicSessionId);
            var dropDownElement = $(".reports-student-fees");
            dropDownElement.html(feeDropDown);
          }
      });
  },

  initMultiSelectAcademicSession : function(title = "") {
    $(".select2-multi-academic-session").each(function() {
        $(this)
          .wrap("<div class=\"position-relative\"></div>")
          .select2({
            placeholder: title,
            dropdownParent: $(this).parent(),
            closeOnSelect: false
          });
      });
  },

  initDataCache : function () {
    var allSessionFees = readJson("#all-session-fees");
    var allSessions = readJson("#all-sessions");
    var currentSession = readJson("#selected-academic-session-json");

    feeReports.dataCache.allSessionFees = allSessionFees;
    feeReports.dataCache.allSessions = allSessions;

    $(".report-academic-session").val(currentSession.academicSessionId);

  },

  getRequiredFeesCSV : function (containerElement) {
    var requiredFeesCSV = "";

    var requiredFees = $(".reports-student-fees").val();
    requiredFeesCSV = requiredFees.join();
  },

  getFeesDropDown : function (selectedSessionId) {
    var allSessionFees = feeReports.dataCache.allSessionFees;
    var sessionFees = allSessionFees[selectedSessionId];

    var feeDropDown = "";
    if(sessionFees == undefined || sessionFees == null){
      return feeDropDown;
    }

    for(var i = 0; i < sessionFees.length; i++){
      feeDropDown += "<option value=\""+ sessionFees[i].feeConfigurationBasicInfo.feeId + "\">" + sessionFees[i].feeConfigurationBasicInfo.feeName + "</option>"
    }
    return feeDropDown;
  },

  checkboxEvents : function () {
    $('.all-column-select').on('change', function() {
        const childCheckboxes = $(this).closest('.report-header-container').find('.select-report-column');

        if ($(this).is(':checked')) {
            childCheckboxes.prop('checked', true);
        } else {
            childCheckboxes.each(function() {
                if (!$(this).is(':disabled')) {
                    $(this).prop('checked', false);
                }
            });
        }
    });
    },


  bindGenerateReportEvent: function (){

    $(".report-academic-session").change(function() {
      var selectedSessionId = $(this).find(':selected').val().trim();
      if(selectedSessionId === undefined || selectedSessionId === "" || selectedSessionId === null) {
        return;
      }
      var feeDropDown = feeReports.getFeesDropDown(selectedSessionId);
      var dropDownElement = $(this).closest(".modal-body").find(".reports-student-fees");
      dropDownElement.html(feeDropDown);
      ajaxClient.get("/fees/standards/"+selectedSessionId, function(data) {
        $(".standard-with-section-options-wrapper").html(data);
        initSelect2("All");
      });
    });

    $(".reports-fee-report-data-type").on('change', function () {
      var selectedFeeReportDataType = $(this).find(':selected').val().trim();
      if(selectedFeeReportDataType === ALL) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:none;");
      } else if(selectedFeeReportDataType === ONLY_DUE_STUDENTS) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:inline-block;");
      } else if(selectedFeeReportDataType === ONLY_NON_DUE_STUDENTS) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:inline-block;");
      }
    });

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }

        var academicSession = "";
        if($(containerElement).find(".report-academic-session option:selected").length > 0){
          academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        }

        academicSession = academicSession === "" ? 0 : academicSession;

        var requiredMultiSession = "";
        if($(containerElement).find(".report-multi-academic-sessions").length > 0){
              requiredMultiSession = $(containerElement).find(".report-multi-academic-sessions").val().join();
        }

        var transactionModeArr = "";
        if($(containerElement).find(".reports-transaction-mode ").length > 0){
          transactionModeArr = $(containerElement).find(".reports-transaction-mode ").val();
        }
        var transactionMode=transactionModeArr.toString();

        var reportType = $(containerElement).find('p.report-type').text().trim();
        var reportStartDate = -1;
        var reportEndDate = -1;
        var feeDueDate = -1;

        if($(containerElement).find('input.report-start-date').length > 0){
            reportStartDate = getDate($(containerElement).find('input.report-start-date').val()).getTime()/1000;
        }
        if($(containerElement).find('input.report-end-date').length > 0){
            reportEndDate = getDate($(containerElement).find('input.report-end-date').val()).getTime()/1000;
        }

        var requiredStandards = "";
        if(reportType === "STUDENT_LEDGER") {
          requiredStandards = $(containerElement).find(".reports-student-class").val();
        } else {
          if($(containerElement).find(".reports-student-class").length > 0){
                requiredStandards = $(containerElement).find(".reports-student-class").val().join();
          }

        }

        var requiredFees = "";
        if($(containerElement).find(".reports-student-fees").length > 0){
              requiredFees = $(containerElement).find(".reports-student-fees").val().join();
        }

        var studentStatus = "";
          if($(containerElement).find(".reports-student-status").length > 0){
            studentStatus = $(containerElement).find(".reports-student-status").val().join();
        }

        var requiredFeeHeads = "";
        if($(containerElement).find(".reports-student-fee-heads").length > 0){
              requiredFeeHeads = $(containerElement).find(".reports-student-fee-heads").val().join();
        }

        var transactionType = $(containerElement).find('.transaction-type').val();
        if(transactionType === undefined) {
          transactionType = "";
        } else {
          transactionType = transactionType.trim();
        }

        if($(containerElement).find('input.fee-due-date').length > 0){
          feeDueDateObj =  getDate($(containerElement).find('input.fee-due-date').val());
          if(feeDueDateObj != null){
            feeDueDate = feeDueDateObj.getTime()/1000;
          }
        }

        var feeReportDataType = "";
        if($(containerElement).find(".reports-fee-report-data-type option:selected").length > 0){
          feeReportDataType = $(containerElement).find(".reports-fee-report-data-type option:selected").val();
          if(feeReportDataType === ALL) {
            feeDueDate = -1;
          } else if(feeReportDataType === ONLY_DUE_STUDENTS) {
              if(feeDueDate <= 0) {
                $(containerElement).find("input.fee-due-date").css("border", "1px solid #ff8795");
                return;
              }
          } else if(feeReportDataType === ONLY_NON_DUE_STUDENTS) {
            if(feeDueDate <= 0) {
              $(containerElement).find("input.fee-due-date").css("border", "1px solid #ff8795");
              return;
            }
          }
        }

        var reportDataVisibility = "";
        if($(containerElement).find(".reports-show-fee-head option:selected").length > 0){
           reportDataVisibility = $(containerElement).find(".reports-show-fee-head option:selected").val();
        }

        var hideStudentWithZeroFeesAssignment = $(containerElement).find('input.students-with-zero-fees-assignment').is(':checked');
        var showStudentWithZeroWalletBalance = $(containerElement).find('input.student-with-zero-wallet-balance').is(':checked');
        
        $(this).closest('div.modal').modal('toggle');
        window.open(baseURL+"/fees/generate-report/"+reportType+"?academic_session_id="+academicSession+"&requiredHeaders="+requiredHeadersCSV+"&requiredMultiSession="+requiredMultiSession+"&reportStartDate="+reportStartDate+"&reportEndDate="+reportEndDate+"&fee_ids="+requiredFees+"&requiredStandards="+requiredStandards+"&feeDueDate="+feeDueDate +"&studentStatus="+studentStatus+"&fee_head_ids="+requiredFeeHeads+"&transactionType="+transactionType+"&feeReportDataType="+feeReportDataType+"&transactionMode="+transactionMode+"&hideStudentWithZeroFeesAssignment="+hideStudentWithZeroFeesAssignment+"&showStudentWithZeroWalletBalance="+showStudentWithZeroWalletBalance+"&reportDataVisibility="+reportDataVisibility,'_blank');
    });
  }

};
