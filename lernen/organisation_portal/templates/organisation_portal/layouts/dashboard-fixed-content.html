<div class="card">
  <div class="card-body p-0">
    <div class="row">
      <div class="col-sm-12 col-lg-12 col-xxl-12 text-center pt-5">
        <h1 style="color:#212121;">{{user_login_view.organisation.organisationName}}</h1>
      </div>
    </div>
    <div class="row">
    <div class="col-sm-6 col-lg-6 col-xxl-6 text-center pb-6">
      <img src="https://assetsv5.embrate.com/static/core/images/org_static_dashboard_image.png" class="col mx-auto d-block" style="width:80%;"/>
      <button type="button" class="btn btn-outline-primary" style="width: 30%;" onclick="viewStatistics();">View Statistics</button>
      <button type="button" class="btn btn-outline-primary" style="width: 30%; display:none;" onclick="viewStatisticsV2();">View Statistics V2</button>
    </div>
    <div class="col-sm-6 col-lg-6 col-xxl-6 pl-4 pb-5">
      <div>
        <ul class="sidebar-nav">
          {% for institute_row in institute_rows %}
              {% for institute in institute_row %}
               <a class="text-dark" href="{{institute.portal_path}}" style="cursor: pointer;margin:1px;">
                   <div class="row">
                      <div class="col-2 pr-0">
                        {% if institute.logoUrl %}
                         <img src="{{institute.logoUrl}}" style="width: 70px;"/>
                         {% else %}
                         <div style="width: 70px;display:inline-block;"></div>
                        {% endif %}
                      </div>
                     <div class="col-10 pl-0 pt-2 pr-5">
                       <p style="font-size: 1rem;display: inline-block;color:#223b5c;"> {{institute.instituteName}} {% if institute.branchName %} ({{institute.branchName}}) {% endif %}</p>
                     </div>
                   </div>
               </a>
              {% endfor %}
          {% endfor %}
        </ul>
      </div>
    </div>
    </div>
  </div>
</div>
