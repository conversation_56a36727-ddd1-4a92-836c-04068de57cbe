from django.contrib import admin
from django.urls import path
from django.urls import include
from holiday_calendar.views import *

urlpatterns = [

    # Holiday Calendar Home Page
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('manage-holiday/home', manage_holiday_home_page_view, name='manage_holiday_home_page_view'),
    path('manage-holiday/<academic_session_id>/holidays', session_holidays_view, name='session_holidays_view'),
    path('manage-holiday/new', new_holidays_view, name='new_holidays_view'),
    path('manage-holiday/update/<holiday_id>', update_holidays_view, name='update_holidays_view'),
    path('manage-holiday/delete/<academic_session_id>/<holiday_id>', delete_holidays_view, name='delete_holidays_view'),

    path('configure-template/home', holiday_template_home_page_view, name='holiday_template_home_page_view'),
    path('configure-template/home/<USER>/<academic_session_id>', holiday_template_session_view, name='holiday_template_session_view'),
    path('configure-template/form/<academic_session_id>', holiday_template_form_view, name='holiday_template_form_view'),
    path('configure-template/add', add_new_holiday_template_view, name='add_new_holiday_template_view'),
    path('configure-template/view/<template_id>', get_holiday_template_view, name='get_holiday_template_view'),
    path('configure-template/update-form/<template_id>', holiday_template_update_form_view, name='holiday_template_update_form_view'),
    path('configure-template/update/<template_id>', update_holiday_template_view, name='update_holiday_template_view'),
    path('configure-template/delete/<template_id>', delete_holiday_template_view, name='delete_holiday_template_view'),

    path('assign-template/home/<USER>', template_assign_home_page_view, name='template_assign_home_page_view'),
    path('assign-template/home/<USER>/<academic_session_id>/<user_type>', template_assign_session_view, name='template_assign_session_view'),
    path('assign-template/user-search/<academic_session_id>/<user_type>', template_user_search_view, name='template_user_search_view'),
    path('assign-template/update', user_template_assignment_view, name='user_template_assignment_view'),

    path('event-management-main-screen', event_management_main_screen_view, name='event_management_main_screen_view'),
    path('add-event', add_event_view, name='add_event_view'),
    path('update-event', update_event_view, name='update_event_view'),
    path('delete-event/<academic_session_id>/<event_id>', delete_event_view, name='delete_event_view'),
    path('event-session-change-screen/<academic_session_id>', event_session_change_screen_view, name='event_session_change_screen_view'),

]
