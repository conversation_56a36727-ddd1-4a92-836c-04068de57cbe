import json
from django.http import JsonResponse
from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from .controller.student_finance_manager import *
from core.controller.user.authentication import *
from core.controller.utils.tracking_events import *
from core.controller.utils.template_manager import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.reports_manager import *
from core.controller.utils.authorised_actions import *
from core.controller.utils.wallet_manager import*
from datetime import date
from math import *
from core.controller.utils.authorised_actions import *


MODULE_NAME = 'STUDENT_FINANCE'

def authorized_user_session(request):
	return authorized_module(request,'STUDENT_FINANCE')


def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "STUDENT_FINANCE_LOADED"})
		user_login_view = get_user_login_view(request)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		user_type = user_login_view['user']['userType']
		tutorial_videos = get_tutorial_video_details(MODULE_NAME, institute_unique_code, user_type)
		return render(request, 'student_finance/layouts/default-student-finance-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' :  payment_status_data, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access, 'tutorial_videos': tutorial_videos})

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'student_finance/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})


def home_page_session_view(request):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'student_finance/layouts/dashboard-session-content.html',{'user': user_login_view['user']})


def student_finance_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'student_finance/student_expense_details/main_screen.html',{'academic_years':academic_years,'current_session':current_session})

def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		# Here status is studentStatus
		status = request.GET.get("status","")
		students = lite_v2_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), status)
		return render(request, 'core/student/live_search_student_list.html',{"students":students, "search_version" : "v2"})

def get_student_expence_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_finance_preferences = load_student_finance_preferences(user_login_view, institute_unique_code)
		student_wallet_preferences = load_student_wallet_preferences(user_login_view, institute_unique_code)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id, True, True)
		services = get_services(user_login_view, institute_unique_code)
		return render(request, 'student_finance/student_expense_details/student_expense.html',{'student':student, 'services':services, 'session_id' : session_id,'student_finance_preferences' : student_finance_preferences, 'student_wallet_preferences' : student_wallet_preferences})

@csrf_exempt
def add_student_expence_transaction_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		serviceExpensePayload = json.loads(request.POST['serviceExpensePayload'])
		response_data = add_student_expence_transaction(user_login_view, institute_unique_code, serviceExpensePayload)
		return render(request, 'student_finance/student_expense_details/status_modal.html',{"data":response_data})

def generate_report_view(request, institute_unique_code, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		report_request = {"report_type":report_type, "academic_session_id" : request.GET.get("sessionId", ""), "startDate" : request.GET.get("startDate", 0), "endDate" : request.GET.get("endDate", 0), "student_id" : request.GET.get("studentId",""),"download_format" : downloadFormat}
		if download_report :
			report = get_report(user_login_view, institute_unique_code, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_report_data(user_login_view, institute_unique_code, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL"
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF"

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})
