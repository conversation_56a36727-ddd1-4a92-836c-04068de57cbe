$(document).ready(function() {
  menuLoader.sidebarMenu();
  homePage.initHomePage();  
});

var menuLoader = {

  sidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.homeMenu();
      menuLoader.studentFinanceDetailsMenu();
  },

  homeMenu : function () {
      $('#homeNav').on('click', function() {
          homePage.loadHomePage();
      });
  },

  studentFinanceDetailsMenu : function () {
      $('#studentFinanceManagementNav').on('click', function() {
        manageStudentExpenseDetails.loadStudentFinanceDetailsPage("");
      });
  },

};
var homePage =  {
    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);

    },

    loadHomePage : function () {
      ajaxClient.get("/student-finance/home", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/student-finance/session-home/"+academicSessionId, function(data) {
          $("#student-finance-dashboard-session-content").html(data);
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

};

var manageStudentExpenseDetailsReport = {
  bindGenerateReportEvent : function () {

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }
        var expenseReportType = $(containerElement).find('p.report-type').text().trim();
        var expenseStartDate = -1;
        var expenseEndDate = -1;
        var sessionId = academicSessionHandler.getSelectedSessionId();
        var studentId = $(containerElement).find('p.report-type-view-student-id').text().trim();

        if($(containerElement).find('input.student-expense-start-date').length > 0){
          if($(containerElement).find('input.student-expense-start-date').val() != "") {
              expenseStartDate = getDate($(containerElement).find('input.student-expense-start-date').val()).getTime()/1000;
          }
        }

        if($(containerElement).find('input.student-expense-end-date').length > 0){
          if($(containerElement).find('input.student-expense-end-date').val() != "") {
            expenseEndDate = getDate($(containerElement).find('input.student-expense-end-date').val()).getTime()/1000;
          }
        }

        if(expenseEndDate == -1 || expenseStartDate == -1){
          showErrorDialogBox("Please Select Both Start Date and End Date");
          return;
        }

        if (expenseEndDate != -1 && expenseStartDate != -1 && expenseEndDate < expenseStartDate) {
          showErrorDialogBox("End date cannot be less than start date");
          return;
        }

        $(this).closest('div.modal').modal('toggle');

        window.open(baseURL+"/student-finance/generate-report/"+expenseReportType
        + "?sessionId="+ sessionId
        +"&startDate="+expenseStartDate
        +"&endDate="+expenseEndDate+"&studentId="+studentId, '_blank');
    });
  },
};

var manageStudentExpenseDetails = {
  dataCache: {},

  loadStudentFinanceDetailsPage : function() {
    ajaxClient.get("/student-finance/student-finance-details", function(data) {
      $("#main-content").html(data);
      academicSessionHandler.bindSessionChangeEvent(manageStudentExpenseDetails.changeSession);
      bindSearchStudentEvent();
    });
  },

  changeSession: function() {
    $("#student-expense-search-text").val("");
    $("#student-expense-screen").html("");
    manageStudentExpenseDetails.dataCache = {};
  },

  doneStudentSearchTyping: function(resultArea) {
    var searchText = $('#student-expense-search-text').val().trim();
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var status = "ENROLLED";
    manageStudentExpenseDetails.studentLiveSearchEvent(sessionId, searchText, resultArea, loadStudentExpenseDetails, status);
  },
  
  studentLiveSearchEvent: function(sessionId, searchText, resultArea, triggerMethod, status){
    ajaxClient.get("/student-finance/student-live-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
        $(resultArea).html(data);
        studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
    });
  },

  addStudentExpense : function () {    
    $("#add-expense-action-modal").modal('toggle');
    addStudentExpenseHandler.init();
  },

};


function bindSearchStudentEvent() {
  var resultArea = "#student-expense-search-result";
  $('#student-expense-search').on('click', function () {
    manageStudentExpenseDetails.doneStudentSearchTyping(resultArea);
  });
  $("#student-expense-search-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
      manageStudentExpenseDetails.doneStudentSearchTyping(resultArea);
    }
  });
  liveSearchHandler.bindEvent('#student-expense-search-text',resultArea,manageStudentExpenseDetails.doneStudentSearchTyping);
}

function loadStudentExpenseDetails(studentId) {
  var academicSessionId = academicSessionHandler.getSelectedSessionId();
  ajaxClient.get( "/student-finance/student-expense-details/"+academicSessionId+"/"+studentId, function(data) {
      $("#student-expense-screen").html(data);
      initDate(5 * 365);    
      var todayTimestamp = Date.now(); // current time in ms
    $('#expense-transaction-date').val(getFormattedDate(todayTimestamp / 1000));
    $('#generate-student-finance-popup').on('click', function(){
      $('#student-expense-report-with-date-range-modal').modal('toggle');
    });
    manageStudentExpenseDetailsReport.bindGenerateReportEvent();
  });
}

var addStudentExpenseHandler = {

  servicesByCategory: {},

  init: function () {
    addStudentExpenseHandler.cacheServicesByCategory();
    addStudentExpenseHandler.bindCategoryChangeEvent();
  },

  cacheServicesByCategory: function () {
    addStudentExpenseHandler.servicesByCategory = {};

    var servicesJson = $('#services-json').text();
    var services = [];

    try {
      services = JSON.parse(servicesJson);
    } catch (e) {
      console.error("Invalid services JSON", e);
      return;
    }

    // Build cache grouped by category
    services.forEach(function (service) {
      var category = service.serviceCategory;
      if (!addStudentExpenseHandler.servicesByCategory[category]) {
        addStudentExpenseHandler.servicesByCategory[category] = [];
      }
      addStudentExpenseHandler.servicesByCategory[category].push(service);
    });
  },

  bindCategoryChangeEvent: function () {
    $('#student-service-category').on('change', function () {
      var selectedCategory = $(this).val();
      var serviceDropdown = $('#service');

      serviceDropdown.empty().append('<option value=""></option>');

      var services = addStudentExpenseHandler.servicesByCategory[selectedCategory] || [];
      services.forEach(function (service) {
        serviceDropdown.append(
          $('<option>', {
            value: service.serviceId,
            text: service.serviceName
          })
        );
      });
    });
  },

  confirmAddExpance: function () {
    var invalid = validations($("#add-expense-action-modal"));
    if(invalid){
      return;
    }

    var financePreferences = readJson("#student-finance-preferences-json");
    var walletPreferences = readJson("#student-wallet-preferences-json");

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var studentId = $('#fee-assign-view-student-id').text().trim();
    var serviceId = $('#service').val();
    var transactionMode = $('#payment-mode').val();
    var paidAmount = parseFloat($('#expense-amount').val()) || 0;

    var walletAmount = parseFloat($('#available-wallet-amount-display').text()) || 0;

    if (!isCreditAllowed(paidAmount, walletAmount, financePreferences, walletPreferences)) return;
  
    var transactionDateFormat = getDate($("#expense-transaction-date").val());
    var transactionDate = null;
    if (transactionDateFormat) {
      transactionDate = transactionDateFormat.getTime()/1000;
    }
  
    var description = $('#description').val();
    var reference = $('#transaction-refrence').val();

    var payload = {"academicSessionId": academicSessionId,"serviceId":serviceId,"studentId":studentId,"transactionMode":transactionMode,"paidAmount":paidAmount,"transactionDate":transactionDate,"description":description,"reference":reference};
    $("#add-expense-action-modal").modal('toggle');
    ajaxClient.post( "/student-finance/add-student-expense-transaction", {'serviceExpensePayload' : JSON.stringify(payload)}, function(data) {
      $("#student-expense-status-modal-container").html(data);
      $("#student-expense-status-modal").modal('toggle');
    });
  },
};

function isCreditAllowed(paidAmount, walletAmount, financePreferences, walletPreferences) {
  const usedWalletAmount = Math.max(walletAmount, 0);
  const remainingAmount = paidAmount - usedWalletAmount;
  if (walletAmount >= paidAmount) return true;
  if (financePreferences.allowWalletBasedCredit ) {
    if(walletAmount - usedWalletAmount - remainingAmount < -walletPreferences.walletBasedCreditLimit){
      showErrorDialogBox("Cannot give more credit then allowed max limit");
      $("#add-expense-action-modal").modal('toggle');
      resetAddExpenseModal();
      return false;
    }
  } else {
    showErrorDialogBox("Credit on wallet for student finance is not enabled for institute.");
    $("#add-expense-action-modal").modal('toggle');
    resetAddExpenseModal();
    return false;
  }

  return true;
}

function resetAddExpenseModal() {
  $('#student-service-category').val('');
  $('#service').empty().append('<option selected value=""></option>');
  $('#expense-amount').val('');
  $('#description').val('');
  $('#transaction-refrence').val('');
  $('#payment-mode').val('WALLET');
}

function validations(element){
  clearValidationErrorDisplay();
  var invalid = false;
  $(element).find("input.mandatory-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("select.mandatory-field").each(function() {
      if ($(this).find(':selected').length == 0 || $(this).find(':selected').val().trim() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.mandatory-table-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-table-field-text\"> <span style=\"color:#e65f76;\">Mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.positive-number").each(function() {
      if ($(this).val() < 0) {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  return invalid;
}

function clearValidationErrorDisplay(){

  $(".hour-range").css("border", "");
  $(".invalid-input-text").remove();

  $(".mandatory-field").css("border", "");
  $(".mandatory-field-text").remove();

  $(".mandatory-table-field").css("border", "");
  $(".mandatory-table-field-text").remove();

}



    


