{% load json %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>REPORTS</h3>
    </div>
</div>
<br>

<div>
  <p id="all-session-fees" style="display:none;">{{all_session_fees|jsonstr}}</p>
  <p id="all-sessions" style="display:none;">{{academic_years|jsonstr}}</p>
  <p id="selected-academic-session-json" style="display:none;"> {{current_session|jsonstr}} </p>

    <div class="row">

      <div class="col-sm-4">
        <div id="student-aggregated-report" class="card-hover bg-light report-card report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Aggregated Fees Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Fees summary report for all the students of the selected session</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-fees-level-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Fees Level Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed fees report for all the students of the selected session</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-fees-head-level-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Fees Head Level Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed fees head report for all the students of the selected session</p>
          </div>
        </div>
      </div>

    </div>
    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="feehead-day-level-collected-amount-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Fees Collection Report (Head Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report for collected fee amount at day level in feeheads</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="transaction-mode-collected-amount-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Fees Collection Report (Mode Wise)</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report for collected fee amount at day level by different transaction modes</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="user-day-level-fee-collection-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>User Day Level Fee Collection</strong></h4>
            <p class="card-body" style="color:#6c757d">Day level aggregated collection of fees on user basis</p>
          </div>
        </div>
      </div>

    </div>

    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="fee-transactions-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Transactions Summary</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report of all fee transactions by transaction date & feeHead</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="cancelled-transactions-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Cancelled Transactions Summary</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report of cancelled transactions by cancelled date</p>
          </div>
        </div>
      </div>


      <div class="col-sm-4">
        <div id="fee-transactions-detailed-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Detailed Transactions Summary</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report of all fee transactions by transaction date, fees & feehead</p>
          </div>
        </div>
      </div>

    </div>

    <br/>
    <div class="row">

      <div class="col-sm-4">
        <div id="student-wallet-transactions-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Wallet Transactions</strong></h4>
            <p class="card-body" style="color:#6c757d">Detailed report of wallet transcations for students</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-wallet-balance-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Student Wallet Balance</strong></h4>
            <p class="card-body" style="color:#6c757d">Report with detailed wallet balance for students</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-discount-assignment-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Discount Assignment Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Get information of discount assigned to all the students.</p>
          </div>
        </div>
      </div>

    </div>
    <br/>

    <div class="row">

      <div class="col-sm-4">
        <div id="discount-summary-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Discount Sumary Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Details about discount assigned in discount structures and instant discount given.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-ledger-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Students' Ledger</strong></h4>
            <p class="card-body" style="color:#6c757d">Student-wise ledger for a particular class in a session.</p>
          </div>
        </div>
      </div>

      <div class="col-sm-4">
        <div id="student-aggregated-multi-session-due-report" class="card-hover bg-light report-card report-card" >
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Agg. Multi Session Fees Due Report</strong></h4>
            <p class="card-body" style="color:#6c757d">Due fees summary report for all the students having some dues in multiple sessions</p>
          </div>
        </div>
      </div>
    </div>
</div>

<div class="modal fade" id="student-aggregated-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Aggregated Fees Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
            {% include 'fees/reports/standard_section.html' %}
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Fees</label>
            <select class="form-control select2 reports-student-fees" multiple data-toggle="select2">
              <option value=""></option>
            </select>
          </div>
          <div class="form-group col-md-6">
            <label>Fee Heads</label>
            <select class="form-control select2 reports-student-fee-heads" multiple data-toggle="select2">
              {% for fee_head in fee_head_list %}
                <option value={{fee_head.feeHeadConfiguration.feeHeadId}}>{{fee_head.feeHeadConfiguration.feeHead}}</option>
              {% endfor %}
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group col-md-4">
            <label>Student Status</label>
            <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
                <option value="ENROLLED">Enrolled</option>
                <option value="ENROLMENT_PENDING">Pending</option>
                <option value="NSO">NSO</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>

          <div class="form-group col-md-4">
            <label>Show Data For*</label>
            <select class="form-control reports-fee-report-data-type mandatory-field">
               <option value="ALL" selected>All Students</option>
               <option value="ONLY_DUE_STUDENTS">Students With Dues</option>
               <option value="ONLY_NON_DUE_STUDENTS">Students Without Dues</option>
           </select>
          </div>
          <div class="form-group col-md-4" style="display:none;" id="fee-due-date-div">
            <label>Data by date*</label>
            <input type="text" class="form-control select-date fee-due-date" placeholder="Due Date..."/>
          </div>
       </div>
        <div class="form-row">
          <div class="form-group col-md-12">
            <div class="custom-control custom-checkbox">
              <input type="checkbox" class="custom-control-input students-with-zero-fees-assignment" id="hide-students-with-zero-fees-assignment">
              <label class="custom-control-label" for="hide-students-with-zero-fees-assignment">Hide Students with Zero Fees Assignment</label>
            </div>
          </div>
        </div>

       {% include 'core/reports/header_list_for_fees.html' with  header_columns=report_header_columns.STUDENT_AGGREGATED_PAYMENT %}
      </div>
      <p style="display:none;" class="report-type">STUDENT_AGGREGATED_PAYMENT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>



<div class="modal fade" id="student-fees-level-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Student Fees Level Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
            {% include 'fees/reports/standard_section.html' %}
          </div>
          <div class="form-group col-md-6">
            <label>Fees</label>
            <select class="form-control select2 reports-student-fees" multiple data-toggle="select2">
              <option value=""></option>
            </select>
          </div>

        </div>
       <div class="form-row">
         <div class="form-group col-md-4">
           <label>Student Status</label>
           <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
               <option value="ENROLLED">Enrolled</option>
               <option value="ENROLMENT_PENDING">Pending</option>
               <option value="NSO">NSO</option>
               <option value="RELIEVED">Relieved</option>
           </select>
         </div>
         <div class="form-group col-md-4">
           <label>Show Data For*</label>
           <select class="form-control reports-fee-report-data-type mandatory-field">
              <option value="ALL" selected>All Students</option>
              <option value="ONLY_DUE_STUDENTS">Students With Dues</option>
              <option value="ONLY_NON_DUE_STUDENTS">Students Without Dues</option>
          </select>
         </div>
         <div class="form-group col-md-4" style="display:none;" id="fee-due-date-div">
           <label>Data by date*</label>
           <input type="text" class="form-control select-date fee-due-date" placeholder="Due Date..."/>
         </div>
      </div>

      </div>
      <p style="display:none;" class="report-type">STUDENT_FEES_PAYMENT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="feehead-day-level-collected-amount-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fees Collection Report (Head Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group col-md-6">
            <label>Student Status</label>
            <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
                <option value="ENROLLED">Enrolled</option>
                <option value="ENROLMENT_PENDING">Pending</option>
                <option value="NSO">NSO</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>
      </div>
      <p style="display:none;" class="report-type">FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="fee-transactions-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fee Transactions Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session">
              <option value=""></option>
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->

        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>

       <div class="form-row">
        <div class="form-group col-md-6 standard-with-section-options-wrapper">
          {% include 'fees/reports/standard_section.html' %}
        </div>
         <div class="form-group col-md-6">
           <label>Transaction Type</label>
            <select class="form-control transaction-type">
              <option value="ACTIVE" selected>Active</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
         </div>
          <div class="form-group col-md-6">
            <label>Transaction Mode</label>
            <select class="form-control select2 reports-transaction-mode" multiple data-toggle="select2">
              <option value="CASH" >Cash</option>
              <option value="ACCOUNT_TRANSFER" >Account Transfer</option>
              <option value="PAYMENT_GATEWAY" >Payment Gateway</option>
              <option value="CHEQUE" >Cheque</option>
              <option value="CARD" >Card</option>
              <option value="DEMAND_DRAFT" >Demand Draft</option>
              <option value="QR_PAYMENT" >QR Payment</option>
              <option value="GOOGLE_PAY_UPI" >Google Pay UPI</option>
              <option value="PHONE_PE_UPI" >PhonePe UPI</option>
              <option value="PAYTM" >Paytm</option>
              <option value="BHIM_UPI" >Bhim UPI</option>
              <option value="OTHER_UPI" >Other UPI</option>
              <option value="OTHER" >Other</option>
            </select>
          </div>
      </div>

      {% include 'core/reports/header_list_for_fees.html' with  header_columns=report_header_columns.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL %}
      </div>
      <p style="display:none;" class="report-type">TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="cancelled-transactions-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fee Transactions Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session">
              <option value=""></option>
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->

        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>

       <div class="form-row">
        <div class="form-group col-md-6 standard-with-section-options-wrapper">
          {% include 'fees/reports/standard_section.html' %}
        </div>
      </div>

      </div>
      <p style="display:none;" class="report-type">CANCELLED_TRANSACTION_DETAIL</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="fee-transactions-detailed-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fee Transactions Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session">
              <option value=""></option>
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>

       <div class="form-row">
        <div class="form-group col-md-6 standard-with-section-options-wrapper">
          {% include 'fees/reports/standard_section.html' %}
        </div>
         <div class="form-group col-md-6">
           <label>Transaction Type</label>
            <select class="form-control transaction-type">
              <option value="ACTIVE" selected>Active</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
         </div>
          <div class="form-group col-md-6">
            <label>Transaction Mode</label>
            <select class="form-control select2 reports-transaction-mode" multiple data-toggle="select2">
              <option value="CASH" >Cash</option>
              <option value="ACCOUNT_TRANSFER" >Account Transfer</option>
              <option value="PAYMENT_GATEWAY" >Payment Gateway</option>
              <option value="CHEQUE" >Cheque</option>
              <option value="CARD" >Card</option>
              <option value="DEMAND_DRAFT" >Demand Draft</option>
              <option value="QR_PAYMENT" >QR Payment</option>
              <option value="GOOGLE_PAY_UPI" >Google Pay UPI</option>
              <option value="PHONE_PE_UPI" >PhonePe UPI</option>
              <option value="PAYTM" >Paytm</option>
              <option value="BHIM_UPI" >Bhim UPI</option>
              <option value="OTHER_UPI" >Other UPI</option>
              <option value="OTHER" >Other</option>
            </select>
          </div>
      </div>

      {% include 'core/reports/header_list_for_fees.html' with  header_columns=report_header_columns.TRANSACTION_SUMMARY_DETAILED_REPORT %}
      </div>
      <p style="display:none;" class="report-type">TRANSACTION_SUMMARY_DETAILED_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="user-day-level-fee-collection-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> User Day Level Fee Collection </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Sessions*</label>
            <select class="form-control select2-multi-academic-session report-multi-academic-sessions mandatory-field" multiple data-toggle="select2">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
          <p style="color:red">*Only 3 session can be selected for a single report.</p>
       </div>
      </div>
      <p style="display:none;" class="report-type">USER_DAY_LEVEL_COLLECTION</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="student-ledger-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle">Students' Ledger </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Classes</label>
            <select class="form-control reports-student-class" data-toggle="select2">
              {% for standard in standards %}
                <option value={{standard.standardId}}>{{standard.displayName}}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group col-md-6">
            <label>Fees</label>
            <select class="form-control select2 reports-student-fees" multiple data-toggle="select2">
              <option value=""></option>
            </select>
          </div>
       </div>

       <div class="form-row">
         <div class="form-group col-md-6">
           <label>Student Status</label>
           <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
               <option value="ENROLLED" selected>Enrolled</option>
               <option value="ENROLMENT_PENDING">Pending</option>
               <option value="NSO">NSO</option>
               <option value="RELIEVED">Relieved</option>
           </select>
         </div>
       </div>


      </div>
      <p style="display:none;" class="report-type">STUDENT_LEDGER</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="student-wallet-transactions-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Student Wallet Transactions Report</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
       </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>
      </div>
      <p style="display:none;" class="report-type">STUDENT_WALLET_TRANSACTIONS</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="student-discount-assignment-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Discount Assignment Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
              {% include 'fees/reports/standard_section.html' %}
          </div>

          <div class="form-group col-md-6">
            <label>Student Status</label>
            <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
                <option value="ENROLLED" selected>Enrolled</option>
                <option value="ENROLMENT_PENDING">Pending</option>
                <option value="NSO">NSO</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>

       </div>
      </div>
      <p style="display:none;" class="report-type">STUDENT_DISCOUNT_ASSIGNMENT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="discount-summary-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Discount Summary Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
              {% include 'fees/reports/standard_section.html' %}
          </div>

          <div class="form-group col-md-6">
            <label>Student Status</label>
            <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
                <option value="ENROLLED" selected>Enrolled</option>
                <option value="ENROLMENT_PENDING">Pending</option>
                <option value="NSO">NSO</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>

       </div>
      </div>
      <p style="display:none;" class="report-type">DISCOUNT_SUMMARY_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="student-wallet-balance-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Student Wallet Balance Report</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
            {% include 'fees/reports/standard_section.html' %}
          </div>
          <div class="form-group col-md-6">
            <label>Student Status</label>
            <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
                <option value="ENROLLED" selected>Enrolled</option>
                <option value="ENROLMENT_PENDING">Pending</option>
                <option value="NSO">NSO</option>
                <option value="RELIEVED">Relieved</option>
            </select>
          </div>
          <div class="form-row">
          <div class="form-group col-md-12">
            <div class="custom-control custom-checkbox">
              <input type="checkbox" class="custom-control-input student-with-zero-wallet-balance" id="show-student-with-zero-wallet-balance">
              <label class="custom-control-label" for="show-student-with-zero-wallet-balance">Show Students with Zero Wallet Balance</label>
            </div>
          </div>
        </div>
       </div>
      </div>
      <p style="display:none;" class="report-type">STUDENT_WALLET_BALANCE</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="student-aggregated-multi-session-due-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Aggregated Fees Multi Session Due Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Sessions*</label>
            <select class="form-control select2-multi-academic-session report-multi-academic-sessions mandatory-field" multiple data-toggle="select2">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Due by date</label>
            <input type="text" class="form-control select-date fee-due-date" placeholder="Due Date..."/>
          </div>
       </div>
       <p style="color:red">*Only 3 session can be selected for a single report.</p>
      </div>
      <p style="display:none;" class="report-type">STUDENT_AGGREGATED_MULTI_SESSION_DUE_PAYMENT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="transaction-mode-collected-amount-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Fees Collection Report (Mode Wise) </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Start Date*</label>
            <input type="text" class="form-control select-date report-start-date mandatory-field" placeholder="Start Date"/>
          </div>
          <div class="form-group col-md-6">
            <label>End Date*</label>
            <input type="text" class="form-control select-date report-end-date mandatory-field" placeholder="End Date"/>
          </div>
       </div>
      </div>
      <p style="display:none;" class="report-type">TRANSACTION_MODE_AMOUNTS_REPORT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="student-fees-head-level-report-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content report-field-container">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLongTitle"> Student Fees Head Level Report </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label>Academic Session*</label>
            <select class="form-control report-academic-session mandatory-field">
              {% for academic_year in academic_years %}
                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
              {% endfor %}
            </select>
          </div>
          <!-- <div class="form-group col-md-6">
            <label>Download As*</label>
             <select class="form-control download-format mandatory-field">
                 <option value="EXCEL" selected>Excel</option>
                 <option value="PDF">Pdf</option>
             </select>
          </div> -->
        </div>
        <div class="form-row">
          <div class="form-group col-md-6 standard-with-section-options-wrapper">
            {% include 'fees/reports/standard_section.html' %}
          </div>
          <div class="form-group col-md-6">
            <label>Fee Heads</label>
            <select class="form-control select2 reports-student-fee-heads" multiple data-toggle="select2">
              {% for fee_head in fee_head_list %}
                <option value={{fee_head.feeHeadConfiguration.feeHeadId}}>{{fee_head.feeHeadConfiguration.feeHead}}</option>
              {% endfor %}
            </select>
          </div>

        </div>

       <div class="form-row">
         <div class="form-group col-md-4">
           <label>Student Status</label>
           <select class="form-control select2 reports-student-status" multiple data-toggle="select2">
               <option value="ENROLLED">Enrolled</option>
               <option value="ENROLMENT_PENDING">Pending</option>
               <option value="NSO">NSO</option>
               <option value="RELIEVED">Relieved</option>
           </select>
         </div>
         <div class="form-group col-md-4">
           <label>Show Data For*</label>
            <select class="form-control reports-fee-report-data-type mandatory-field">
               <option value="ALL" selected>All Students</option>
               <option value="ONLY_DUE_STUDENTS">Students With Dues</option>
               <option value="ONLY_NON_DUE_STUDENTS">Students Without Dues</option>
           </select>
         </div>
         <div class="form-group col-md-4">
           <label>Show Fee Head In*</label>
           <select class="form-control reports-show-fee-head mandatory-field">
             <option value="ROW_WISE" selected>Row</option>
             <option value="COLUMN_WISE" >Column</option>

           </select>
         </div>
         <div class="form-group col-md-4" style="display:none;" id="fee-due-date-div">
           <label>Data by date*</label>
           <input type="text" class="form-control select-date fee-due-date" placeholder="Due Date..."/>
         </div>
      </div>
      {% include 'core/reports/header_list_for_fees.html' with  header_columns=report_header_columns.STUDENT_FEES_HEAD_PAYMENT %}
      </div>
      <p style="display:none;" class="report-type">STUDENT_FEES_HEAD_PAYMENT</p>
      <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
            <button type="button" class="btn btn-primary generate-report">View Report</button>
      </div>
    </div>
  </div>
</div>
