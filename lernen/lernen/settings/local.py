"""
Django settings for lernen project.

Generated by 'django-admin startproject' using Django 2.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""

import os
from .base import *
from dotenv import load_dotenv

# Load environment variables from the .env file in same folder as this file (if present)
load_dotenv()



# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

SECRET_KEY = 'j0=mywk*jz$*%3w6k^*st5u6nm!b@g1-kql_j_bsl9-u1d)hbs'
ALLOWED_HOSTS = []

WSGI_APPLICATION = 'lernen.wsgi.local.application'

BACKEND_URL = "http://127.0.0.1:8080/data-server"
AUTH_ENABLED = True
API_USER_NAME = 'admin'
API_PASSWORD = 'admin'

ENV='local'

# Database
# https://docs.djangoproject.com/en/2.0/ref/settings/#databases

# 'default': {
#     'ENGINE': 'django.db.backends.sqlite3',
#     'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
# }
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DB_NAME', 'lernen'),
        'USER': os.getenv('DB_USERNAME', 'root'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'root'),
        'HOST': 'localhost',   # Or an IP Address that your DB is hosted on
        'PORT': '3306'
    }
}


STATIC_URL = '/static/'
# STATIC_ROOT = os.path.join(BASE_DIR, 'static/')
# This is done bcz settings is folder now instead of file for multiple envs. Ow above one works
