{% load json %}
<div class="row mb-2 mb-xl-3">
    {% include 'core/utils/v2/academic_session_display.html'%}
    <div class="col-auto ml-auto text-right mt-n1">
      {% include 'core/utils/v2/academic_session_dropdown.html'%}
    </div>
</div>

<p id="add-courses-standards" style="display:none;">
  {{standards|jsonstr}}
</p>

<div id='configured-class-courses-list' class="container-fluid p-0">
  {% include 'courses/course_config/class_courses_list.html' %}
</div>

<div id="update-class-courses-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Update Courses</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-courses-form">
                  <div class="form-row">
                    <div class="form-group col-md-6">
                      <label >Academic Session*</label>
                      <input type="text" class="form-control" id="update-class-courses-session-display" placeholder="" readonly>
                    </div>
                    <div class="form-group col-md-6">
                      <p style="display:none" id = "update-class-courses-standrad-id"></p>
                      <label>Class</label>
                      <input type="text" class="form-control" id="update-class-courses-class-display" placeholder="" readonly>
                    </div>
                  </div>
                </form>
                <h5>Update Courses</h5>
                <div id="update-class-courses-input-div">
                </div>
                 <div style="display: flex;  align-items: center;  justify-content: center;">
                 </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                <button id="update-course-config-button" type="button" class="btn btn-primary"   onclick="courses.updateClassCourses()">Update Course Details</button>
            </div>
        </div>
    </div>
</div>

<div id="add-class-courses-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Add Courses</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-courses-form">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label >Academic Session*</label>
                            <input type="text" class="form-control" id="add-class-courses-session-display" placeholder="" readonly>
                        </div>
                        <div class="form-group col-md-6">
                            <p style="display:none" id = "add-class-courses-standrad-id"></p>
                            <label>Class</label>
                            <input type="text" class="form-control" id="add-class-courses-class-display" placeholder="" readonly>
                        </div>
                    </div>
                </form>
                <h5>Add Courses</h5>
                <div id="add-class-courses-input-div">
                </div>
                <div style="display: flex;  align-items: center;  justify-content: center;">
                    <button type="button" class="btn btn-info" onclick='courses.addNewCourseRow()'>Add More Courses</button>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                <button id="add-course-config-button" type="button" class="btn btn-primary"   onclick="courses.addClassCourses()">Add Course Details</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-id-manage-course-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="delete-standard-course-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Delete  Class Courses</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div id="delete-standard-course-manage-id">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="courses.deleteStudentCourseDetails()">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-class-courses-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" >
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Delete Class Courses </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-courses-form">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label >Academic Session*</label>
                            <input type="text" class="form-control" id="delete-class-courses-session-display" placeholder="" readonly>
                        </div>
                        <div class="form-group col-md-6">
                            <p style="display:none" id = "delete-class-courses-standrad-id"></p>
                            <label>Class</label>
                            <input type="text" class="form-control" id="delete-class-courses-class-display" placeholder="" readonly>
                        </div>
                    </div>
                </form>

                <h5>List of Courses</h5>

                <div id="delete-class-courses-input-div" style="">
            </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                <button id="delete-course-config-button" type="button" class="btn btn-primary"   onclick="courses.deleteClassCourses()">Delete Course Details</button>
            </div>
        </div>

    </div>

</div>
<div id="course-page-status-modal-container"></div>
