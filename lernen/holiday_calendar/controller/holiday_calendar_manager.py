import time
import json
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *


def get_session_holidays(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/calendar/holiday/session?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def add_new_holidays(user_login_view, institute_unique_code, holiday_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/calendar/holiday?institute_id="+str(institute_id),holiday_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Added New Holiday Successfully!!!"}


def update_holidays(user_login_view, institute_unique_code, holiday_id, holiday_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/calendar/holiday/"+str(holiday_id)+"?institute_id="+str(institute_id),holiday_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Updated Holiday Successfully!!!"}


def delete_holiday(user_login_view, institute_unique_code, academic_session_id, holiday_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/calendar/holiday/"+str(holiday_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Deleted Holiday Successfully!!!"}


def get_session_holiday_templates(user_login_view, institute_unique_code, academic_session_id, primary_template_type = None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    if primary_template_type == None:
        primary_template_type = ""
    return restclient.get(user_login_view, "/2.0/calendar/holiday/session-templates?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+ "&primary_template_type="+primary_template_type).get_data()


def add_new_holiday_template(user_login_view, institute_unique_code, holiday_template_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/calendar/holiday/new-template?institute_id="+str(institute_id), holiday_template_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Added New Calendar Successfully!!!"}


def get_holiday_template_details(user_login_view, institute_unique_code, template_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/calendar/holiday/templates/{template_id}?institute_id={institute_id}".format(template_id = template_id, institute_id = institute_id)).get_data()



def update_holiday_template(user_login_view, institute_unique_code, template_id, holiday_template_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/calendar/holiday/templates/{template_id}?institute_id={institute_id}".format(template_id = template_id , institute_id = institute_id), holiday_template_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Updated Calendar Successfully!!!"}

def delete_holiday_template(user_login_view, institute_unique_code, template_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/calendar/holiday/templates/{template_id}?institute_id={institute_id}".format(template_id = template_id , institute_id = institute_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Deleted Calendar Successfully!!!"}




def get_session_holiday_template_assignment(user_login_view, institute_unique_code, academic_session_id, user_type, template_not_assigned_users_only = False, standard_ids_csv = None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    if standard_ids_csv == None:
        standard_ids_csv = ""
    return restclient.get(user_login_view, "/2.0/calendar/holiday/template-assignment?institute_id={institute_id}&academic_session_id={academic_session_id}&user_type={user_type}&standard_ids={standard_ids_csv}&template_not_assigned_users_only={template_not_assigned_users_only}".format(institute_id = str(institute_id), academic_session_id = str(academic_session_id), user_type = user_type, standard_ids_csv = standard_ids_csv, template_not_assigned_users_only = template_not_assigned_users_only)).get_data()


def update_user_holiday_template_assignment(user_login_view, institute_unique_code, holiday_template_assign_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/calendar/holiday/user-template-assignment?institute_id={institute_id}".format(institute_id = institute_id), holiday_template_assign_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Calendar Assigned Successfully!!!"}

def get_event_details(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/calendar/event-details/institute/"+str(institute_id)+"?academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return response.get_data()

def add_event_details(user_login_view, institute_unique_code, eventDetails):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/calendar/new-event?institute_id="+str(institute_id)+"&user_id="+user['uuid'], eventDetails)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Event Added Successfully!"}

def update_event_details(user_login_view, institute_unique_code, eventDetailsPayload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/calendar/event?institute_id="+str(institute_id)+"&user_id="+user['uuid'], eventDetailsPayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Event Updated Successfully!"}
    
def delete_event(user_login_view, institute_unique_code, academic_session_id, event_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/calendar/event-details/"+str(event_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id) + "&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Event deleted Successfully!!!"}