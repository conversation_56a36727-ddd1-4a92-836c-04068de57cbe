var DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-discount-assignment",DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID="select-discount-view";function registerDiscountTabs(){$("#"+DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){var e=academicSessionHandler.getSelectedSessionDisplayName();$("#select-discount-assignment").addClass("active"),$("#select-discount-view").removeClass("active"),$("#academic-year-display").text(e),$("#student-discount-assign").html(""),$("#discount-assign-student-search-result").html(""),discountAssignment.loadDiscountAssignmentPage()}),$("#"+DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID).on("click",function(){var e=academicSessionHandler.getSelectedSessionDisplayName();$("#select-discount-assignment").removeClass("active"),$(".discount-id").prop("checked",!1),$("#select-discount-view").addClass("active"),$("#academic-year-display").text(e),$("#student-discount-view").html(""),initSelect2("Discounts"),bindSearchStudentDiscountViewEvent(),academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession),applyDiscountSearchFilter(),discountAssignment.loadDiscountViewPage()})}function bindSearchStudentDiscountViewEvent(){$("#discount-view-search-student").on("click",function(){bindSearchStudentDiscountView()}),$("#discount-view-search-student-text").on("keyup",function(e){13==e.keyCode&&bindSearchStudentDiscountView()})}function bindSearchStudentDiscountView(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#discount-view-search-student-text").val(),a=$("#discounts").val();a=a.join(),ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/"+e+"?searchText="+t+"&discountIds="+a,function(e){$("#student-discount-view").html(e)})}function bindSearchStudentDiscountAssignmentEvent(){var e="#discount-assign-student-search-result";$("#discount-assign-search-student").on("click",function(){discountAssignment.doneStudentSearchTyping(e)}),$("#discount-assign-search-student-text").on("keyup",function(t){13==t.keyCode&&discountAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#discount-assign-search-student-text",e,discountAssignment.doneStudentSearchTyping)}var discountAssignment={dataCache:{},doneStudentSearchTyping:function(e){var t=$("#discount-assign-search-student-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentDiscountAssignment)},changeSession:function(){var e=academicSessionHandler.getSelectedSessionDisplayName(),t=$(".discount-options.active").attr("id");t==DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?($("#academic-year-display").text(e),$("#discount-assign-search-student-text").val(""),$("#student-discount-assign").html(""),$("#discount-assign-student-search-result").html(""),discountAssignment.loadDiscountAssignmentPage()):t==DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID&&($("#academic-year-display").text(e),$(".discount-id").prop("checked",!1),$("#discount-view-search-student-text").val(""),$("#student-discount-view").html(""),applyDiscountSearchFilter(),discountAssignment.loadDiscountViewPage())},resetDataCache:function(){discountAssignment.dataCache={}},loadDiscountAssignmentPage:function(){ajaxClient.get("/fees/discount-assignment",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession),$("#discount-assignment-screen").attr("style","display:block"),$("#discount-view-screen").attr("style","display:none"),registerDiscountTabs(),bindSearchStudentDiscountAssignmentEvent()})},loadDiscountViewPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-student-fee-discount-list/"+e,function(e){$("#student-discount-view").html(e),$("#student-discount-view-search-tab").attr("style","display:block"),$("#discount-assignment-screen").attr("style","display:none"),$("#discount-view-screen").attr("style","display:block")})},loadFeeDiscountStructures:function(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-student-discount-assignment-details/"+e+"/"+t,function(e){$("#view-student-discount-assignment-details-modal-container").html(e),$("#view-student-assigned-discounts-modal").modal("toggle")})},viewFeeDiscountStructure:function(e,t){discount_structure_list=[],discount_structure_list=t?discountAssignment.dataCache.assignedDiscounts:discountAssignment.dataCache.unassignedDiscounts;for(var a=null,n=0;n<discount_structure_list.length;n++)if(e==discount_structure_list[n].feeDiscountMetadata.discountStructureId){a=discount_structure_list[n];break}null!=a?discountAssignment.displayDiscountStructure(a,t):showErrorDialogBox("Invalid discount structure")},displayDiscountStructure:function(e,t){$("#view-discount-modal").find("#view-discount-title").text(e.feeDiscountMetadata.name);var a=null;if(t){for(var n="",i=0;i<e.feeIdDiscountAssignmentDetailsList.length;i++){for(var o=e.feeIdDiscountAssignmentDetailsList[i],s="",d=0;d<o.feeHeadDiscountAssignmentDetailsList.length;d++){var l=o.feeHeadDiscountAssignmentDetailsList[d],r=l.amount;null!=l.appliedDiscountAmount&&(r=l.appliedDiscountAmount),l.percent?r+="%":r+="/-",s+=discountAssignment.getDiscountFeeHeadRow(l.feeHeadConfiguration.feeHead,r)}n+=discountAssignment.getDiscountFeeRow(o.feeConfigurationBasicInfo.feeName,s)}$("#view-discount-modal").find(".modal-body").html(n)}else{if(null==e.feeDiscountEntityStructures||0==e.feeDiscountEntityStructures.length)return;for(a=e.feeDiscountEntityStructures[0].feeIdDiscountStructures,n="",i=0;i<a.length;i++){var c=a[i];for(s="",d=0;d<c.feeHeadDiscountStructures.length;d++){var u=c.feeHeadDiscountStructures[d];r=u.amount;u.percent?r+="%":r+="/-",s+=discountAssignment.getDiscountFeeHeadRow(u.feeHeadConfiguration.feeHead,r)}n+=discountAssignment.getDiscountFeeRow(c.feeConfigurationBasicInfo.feeName,s)}$("#view-discount-modal").find(".modal-body").html(n)}$("#view-discount-modal").modal("toggle")},getDiscountFeeHeadRow:function(e,t){return"<tr> <td> "+e+" </td> <td>"+t+"</td> </tr>"},getDiscountFeeRow:function(e,t){return'<div class="fee-structure"> <div class="card text-center card-border"> <div class="card-body"> <h5 class="card-title"> <strong>'+e+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-bordered"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th>  </tr> </thead> <tbody class="fee-structure-body"> '+t+" </tbody> </table> </div> </div> </div> </br> </div>"}};function loadStudentDiscountAssignment(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/assigned-discounts/"+t+"/"+e,function(t){$("#student-discount-assign").html(t),bindDiscountInfoViewEvents(),bindAssignNewDiscountEvent(),addNewStudentDiscountAssignment(),discountAssignment.resetDataCache(),discountAssignment.dataCache.studentId=e;var a=readJson("#student-fee-discount-assigned-structures"),n=readJson("#student-fee-discount-unassigned-structures");discountAssignment.dataCache.assignedDiscounts=a,discountAssignment.dataCache.unassignedDiscounts=n,instantDiscountStructure.initDataCache()})}function applyDiscountSearchFilter(){$("#apply-discount-search-filter").on("click",function(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#discount-view-search-student-text").val(),a=$("#discounts").val();a=a.join(),ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/"+e+"?searchText="+t+"&discountIds="+a,function(e){$("#student-discount-view").html(e)})})}function returnToAssignedDiscountDisplay(){$("#top-academic-session-bar").attr("style","display:block"),$(".student-discount-assign-tab").attr("style","display:none"),$("#student-assigned-discount-list").attr("style","display:block"),$("#student-discount-assign-search-tab").attr("style","display:block")}function bindAssignNewDiscountEvent(){$("#student-assign-new-discount-button").on("click",function(){assignNewDiscountDisplay()})}function assignNewDiscountDisplay(){$("#top-academic-session-bar").attr("style","display:none"),$(".student-discount-assign-tab").attr("style","display:none"),$("#student-new-discount-assignment-tab").attr("style","display:block"),bindDiscountInfoViewEvents()}function addNewStudentDiscountAssignment(){$("#student-new-discount-assign-confirm").on("click",function(){$("#student-new-discount-assign-confirm-modal").modal("toggle");var e=$("#assign-new-discount-student-id").val(),t=academicSessionHandler.getSelectedSessionId(),a=getDiscountAssignmentInfo(e,t);ajaxClient.post("/fees/add-discount-assignment/"+t,{discountAssignmentInfo:JSON.stringify(a)},function(t){$("#discount-assignment-status-modal-container").html(t),$("#discount-assignment-status-modal").modal("toggle"),returnToAssignedDiscountDisplay(),loadStudentDiscountAssignment(e)})})}function getDiscountAssignmentInfo(e,t){var a=[];return $("tr.discount-row").each(function(){if($(this).find(".discount-assignment-enable").is(":checked")){var e=$(this).attr("id");a.push(e)}}),{studentIds:[e],discountStructureIds:a}}function confirmDeleteDiscountAssignment(e){$("#delete\\.discount-assignment-id").val(e),$("#delete-discount-assignment-info-modal").modal("toggle")}function deleteDiscountAssignment(){$("#delete-discount-assignment-info-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete\\.discount-assignment-id").val(),a=$("#fee-assign-view-student-id").text().trim(),n={academicSessionId:e,studentId:a,discountId:t};ajaxClient.post("/fees/delete-discount-assignment",{deleteDiscountAssignmentInfo:JSON.stringify(n)},function(e){$("#discount-assignment-status-modal-container").html(e),$("#discount-assignment-status-modal").modal("toggle"),returnToAssignedDiscountDisplay(),loadStudentDiscountAssignment(a)})}var instantDiscountStructure={dataCache:{},INSTITUTE_ENTITY:"institute",initDataCache:function(){for(var e=readJson("#fee-data"),t={},a={},n={},i=e.feeConfigurationBasicInfoList,o=e.feeHeadConfigurationResponseList,s=0;s<i.length;s++){var d=i[s];t[d.feeId]=d;var l=instantDiscountStructure.createFeeHeadSelectMenu(o,"");n[d.feeId]=l}for(s=0;s<o.length;s++){var r=o[s];a[r.feeHeadConfiguration.feeHeadId]=r}instantDiscountStructure.dataCache={},instantDiscountStructure.dataCache.feeMap=t,instantDiscountStructure.dataCache.feeHeadMap=a,instantDiscountStructure.dataCache.feeHeadConfigurationResponseList=o,instantDiscountStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData=n},createFeeHeadSelectMenu:function(e,t){for(var a='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',n=0;n<e.length;n++){var i=e[n].feeHeadConfiguration.feeHeadId,o=e[n].feeHeadConfiguration.feeHead;a+=t==i?'<option value="'+i+'" selected>'+o+"</option>":'<option value="'+i+'">'+o+"</option>"}return a+="</select>"},loadNewFeeStructureConfigureModal:function(){$("#discount-structure-config-modal").modal("toggle"),instantDiscountStructure.resetDiscountStructureModal(),$("#discount-structure-config-modal").find(".modal-title").html("Assign Instant Discounts"),$("#submit-fee-structure").html("Assign Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees={},instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards={},instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees={},instantDiscountStructure.dataCache.selectedStructureId=null,instantDiscountStructure.populateSelectFeesDropdown(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees)},resetDiscountStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text pt-7 pb-7"> Select fees to add new discount structure</p>'),$("#discount-structure-name").val(""),$("#discount-structure-name").attr("disabled",!1),$("#discount-structure-config-modal").find(".modal-footer").css("display","")},populateSelectFeesDropdown:function(e,t,a){var n=instantDiscountStructure.dataCache.feeMap,i="";for(feeId in n)feeId in a||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+n[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),instantDiscountStructure.bindSelectFeesEvent(e,t,a)},bindSelectFeesEvent:function(e,t,a){$(e).find(".fee-select-option").on("click",function(){var t=$(this).attr("id").split("-select-")[0],n=$(this).attr("id").split("-select-")[1];instantDiscountStructure.insertFeeDetailsCard(e,n,t,a)})},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];instantDiscountStructure.insertStandardDetailsCard(e)})},insertFeeDetailsCard:function(e,t,a,n){n[a]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=instantDiscountStructure.createEntityContainerId(t),o=instantDiscountStructure.createFeeContainerId(t,a),s='<div class="fee-structure"> <div class="card card-border text-center" id="'+o+'"> <p class="fee-id" style="display:none;">'+a+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+instantDiscountStructure.dataCache.feeMap[a].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col">Percentage</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(s),instantDiscountStructure.bindFeeContainerEvents(e,o,t,a,n),instantDiscountStructure.populateSelectFeesDropdown(e,t,n)},insertStandardDetailsCard:function(e){instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=instantDiscountStructure.dataCache.standardsMap[e].standardName.toUpperCase(),a=instantDiscountStructure.getStandardContainerId(e),n='<div id="'+a+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+t+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text pt-7 pb-7 black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container pt-3 pb-3" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right " style="max-width:fit-content; max-height: 300px ; "> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(n),instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},instantDiscountStructure.populateSelectFeesDropdown("#"+a,e,instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[e])},createEntityContainerId:function(e){return e+"-fee-structure-container"},createFeeContainerId:function(e,t){return e+"-"+t},bindFeeContainerEvents:function(e,t,a,n,i){$("#"+t).find(".add-fee-head-button").on("click",function(){instantDiscountStructure.insertFeeHeadRow(t,n,null,0,"")}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[n],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),instantDiscountStructure.populateSelectFeesDropdown(e,a,i)})},populateSelectFeesDropdown:function(e,t,a){var n=instantDiscountStructure.dataCache.feeMap,i="";for(feeId in n)feeId in a||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+n[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),instantDiscountStructure.bindSelectFeesEvent(e,t,a)},getStandardContainerId:function(e){return e+"-container"},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;")})},insertFeeHeadRow:function(e,t,a,n,i){var o="";if(null==a){o='<tr id=""> <td> '+instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"> <span class="slider round"></span> </label> </td><td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var s=instantDiscountStructure.dataCache.feeHeadConfigurationResponseList;o='<tr id=""> <td> '+instantDiscountStructure.createFeeHeadSelectMenu(s,a)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+n+'"> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"'+(i=i?"checked":"")+' /><span class="slider round"></span> </label> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(o),instantDiscountStructure.bindFeeHeadRowEvents(e)},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var a=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&a++}),a>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()}),$("#"+e).find(".fee-head-amount").change(function(){if($(this).parent().parent().find(".is-percentage-switch").is(":checked")){var e=$(this).val();if(e>100||e<=0)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).val("")}else{var t=$(this).val();if(t<=0&&""!=t)return showErrorDialogBox("Amount should be a positive number."),void $(this).val("")}}),$("#"+e).find(".is-percentage-switch").change(function(){if($(this).is(":checked")){var e=$(this).parent().parent().parent().find(".fee-head-amount").val();if((e>100||e<=0)&&""!=e)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).parent().parent().parent().find(".fee-head-amount").val("")}})},submitDiscountStructure:function(){var e=$("#discount-structure-name").val();if(""!=e){var t=academicSessionHandler.getSelectedSessionId(),a=$("#instant-discount-structure-id").text();null!=instantDiscountStructure.dataCache.selectedStructureId&&""!=instantDiscountStructure.dataCache.selectedStructureId.trim()&&(a=instantDiscountStructure.dataCache.selectedStructureId);var n=instantDiscountStructure.getFeeStructureData("#institute-fee-structure-container");if(!Array.isArray(n)||n.length){var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:n},o=[];o.push(i);var s=$("#fee-assign-view-student-id").text().trim(),d=[{structureId:a,discountStructureType:"SYSTEM",metadata:{title:e},entityFeeAssignmentPayloads:o}];$("#discount-structure-config-modal").modal("toggle");var l="/fees/add-student-discount-structure/"+t+"/"+s;null!=a&&""!=a&&null!=a&&(l="/fees/update-discount-structure/"+a+"/"+t),ajaxClient.post(l,{discountAssignmentStructurePayload:JSON.stringify(d)},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),loadStudentDiscountAssignment(s)})}}else alert("Discount Detail is mandatory!")},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),a=[],n={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in n)){var t=$(this).find(".fee-head-amount").val(),i=$(this).find(".toggle-switch").is(":checked");if(""===t||void 0===t)return alert("Amount cannot be empty"),[];a.push({feeHeadId:e,amount:t,isPercentage:i})}}),a.length>0&&t.push({feeId:e,feeHeadAmountList:a})}),Array.isArray(t)&&!t.length?(alert("Please select atleast one fees to create discount structure"),[]):t},fillFeeDiscountStructureModal:function(e,t){$("#discount-structure-name").attr("disabled",!1);var a=readJson($(e).parent().parent().find(".student-assigned-discount-structure"));instantDiscountStructure.resetDiscountStructureModal(),$("#discount-structure-name").val(a.feeDiscountMetadata.metadata.title),$("#discount-structure-config-modal").find(".modal-title").html("Update Instant Discounts"),$("#submit-discount-structure").html("Update Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),$("#instant-discount-structure-id").text(a.feeDiscountMetadata.discountStructureId),instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees={};for(var n={},i=0;i<a.feeIdDiscountAssignmentDetailsList.length;i++){var o=a.feeIdDiscountAssignmentDetailsList[i],s=o,d=o.feeConfigurationBasicInfo.feeId;n[d]=s}for(d in n){instantDiscountStructure.insertFeeDetailsCard(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,d,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);var l=instantDiscountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,d);for(i=0;i<n[d].feeHeadDiscountAssignmentDetailsList.length;i++){var r=n[d].feeHeadDiscountAssignmentDetailsList[i].feeHeadConfiguration.feeHeadId,c=n[d].feeHeadDiscountAssignmentDetailsList[i].amount,u=n[d].feeHeadDiscountAssignmentDetailsList[i].percent;instantDiscountStructure.insertFeeHeadRow(l,d,r,c,u)}}instantDiscountStructure.populateSelectFeesDropdown(".institute-structure",instantDiscountStructure.INSTITUTE_ENTITY,instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".toggle-switch").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#discount-structure-config-modal").find(".modal-title").html("View Instant Discounts"),$("#discount-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#discount-structure-config-modal").find(".modal-footer").css("display","none")),$("#discount-structure-config-modal").modal("toggle")}},CLASS_ASSIGNED_FEE_LIST="class-assigned-fees-list",STUDENT_ASSIGNED_FEE_LIST="student-assigned-fees-list",STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-student-assignment",BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-bulk-student-assignment",FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-fee-structure-assignment",CLASS_ENTITY="CLASS",STUDENT_ENTITY="STUDENT",feeAssignment={loadMainScreen:function(){ajaxClient.get("/fees/fee-assignment",function(e){$("#main-content").html(e),registerFeeAssignmentTabs(),academicSessionHandler.bindSessionChangeEvent(feeAssignment.changeSession),addStudentNewFeeAssignmentCallBack(),bindSearchStudentFeeAssignmentEvent(),bindAssignmentFeeSelectionEvents()})},doneStudentSearchTyping:function(e){var t=$("#fee-assign-search-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentFeesAssignment,"ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO")},changeSession:function(){var e=academicSessionHandler.getSelectedSession();$("#academic-year-display").text(e.displayName);var t=$(".fee-assign-options.active").attr("id");t==STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?loadFeeAssignment.loadStudentScreen():t==BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?loadFeeAssignment.loadBulkStudentScreen():t==FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID&&loadFeeAssignment.loadFeeStructureAssignmentStudentScreen()},resetToMainScreen:function(){feeAssignment.returnToClassMainScreen(),feeAssignment.returnToStudentMainScreen(),feeAssignment.returnToBulkStudentMainScreen()},returnToClassMainScreen:function(){$(".class-fee-assign-tab").attr("style","display:none"),$("#class-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()},returnToStudentMainScreen:function(){$(".student-fee-assign-tab").attr("style","display:none"),$("#student-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()},returnToBulkStudentMainScreen:function(){$("#searched-student-list").html(""),$("#bulk-fee-assign-class").val(""),$(".bulk-fee-assign-tab").attr("style","display:none"),$("#bulk-fee-assign-main-tab").attr("style","display:block"),clearMandatoryFieldsErrorDisplay()}};function populateUnassignedFees(e){for(var t=$("#assigned-fee-list").text().trim(),a=JSON.parse(t),n={},i=0;i<a.assigned_fees.length;i++)n[a.assigned_fees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId]=!0;var o=$("#session-authorized-fee-assignments").text().trim(),s=JSON.parse(o),d=[],l=[],r=[];for(i=0;i<s.authorized_fee_assignments.length;i++){if("ENROLMENT_PENDING"===e){var c=s.authorized_fee_assignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.allowPendingEnrollment;if(null==c||!c)continue}s.authorized_fee_assignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId in n||("REGULAR"==s.authorized_fee_assignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeType?d.push(s.authorized_fee_assignments[i]):"ONE_TIME"==s.authorized_fee_assignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeType?l.push(s.authorized_fee_assignments[i]):"SPECIAL"==s.authorized_fee_assignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeType&&r.push(s.authorized_fee_assignments[i]))}for(var u='<option selected value=""> </option>',f=0;f<d.length;f++)u+='<option value="'+d[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(d[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#regular-fee-select").empty().append(u),u='<option selected value=""> </option>';for(f=0;f<l.length;f++)u+='<option value="'+l[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(l[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#onetime-fee-select").empty().append(u),u='<option selected value=""> </option>';for(f=0;f<r.length;f++)u+='<option value="'+r[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(r[f].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+"</option>";$("#special-fee-select").empty().append(u)}function addClassNewFeeAssignmentCallBack(){$("#class-assign-new-fee-button").on("click",function(){var e=$("#fee-assign-class").find(":selected").val().trim();if(""!=e){var t=$("#fee-assign-class").find(":selected").text().trim();$("#assign-new-fee-class-name").val(t),$("#assign-new-fee-class-id").val(e),clearAuthorizedFeeHeads(),populateUnassignedFees(null),$(".class-fee-assign-tab").attr("style","display:none"),$("#class-new-fee-assignment-tab").attr("style","display:block")}else showErrorDialogBox("Please select class for which you want to assign fees")})}function addStudentNewFeeAssignmentCallBack(){$("#student-assign-new-fee-button").on("click",function(){var e=$("#fee-assign-view-student-id").text().trim(),t=$("#search-student-name").text().trim(),a=$("#search-admission-number").text().trim(),n=$("#search-student-class").text().trim(),i=$("#search-student-class-section").text().trim(),o=$("#fee-assign-view-student-status").text().trim();$("#assign-new-fee-student-id").val(e),$("#assign-new-fee-admission-number").val(a),$("#new-fee-view-student-name").text(t),$("#new-fee-view-admission-number").text(a),$("#new-fee-view-student-class").text(n),$("#new-fee-view-student-class-section").text(i),clearAuthorizedFeeHeads(),populateUnassignedFees(o),$(".student-fee-assign-tab").attr("style","display:none"),$("#student-new-fee-assignment-tab").attr("style","display:block")})}function getBulkFeesAssignmentScreen(){$("#bulk-assignment-confirmation-modal").modal("toggle");var e=[];$("input.bulk-student-assignment-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-fee-assign-student-id").first().text().trim();e.push(t)}});var t=academicSessionHandler.getSelectedSessionId(),a=$("#bulk-fee-assign-class option:selected").text();ajaxClient.post("/fees/bulk-fee-assignment/status/"+t,{studentIds:JSON.stringify(e)},function(e){$("#bulk-new-fee-assignment-tab").html(e),$(".bulk-fee-assign-tab").attr("style","display:none"),$("#bulk-new-fee-assignment-tab").attr("style","display:block"),bindShowSelectedStudentModal(),bindAssignmentFeeSelectionEvents();var t=$("#student-text-underline-id").text().trim();-1==a.indexOf("Class")&&(a="Class "+a);var n=t+" of "+a;$("#student-text-underline-id").text(n)})}function updateBulkAssignmentStudentConfirmModal(){var e=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assignment-confirmation-modal-text").text("You have selected "+e+" students to assign fees at student level. Do you want to proceed?")}function bulkAssignmentStudentSelectCheckbox(e){var t=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(t)}function selectBulkAssignmentStudentList(e){if(e.checked){$(".bulk-student-assignment-checkbox").prop("checked",!0);var t=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(t)}else $(".bulk-student-assignment-checkbox").prop("checked",!1),$("#bulk-assign-selected-count").html(0)}function bindAssignmentFeeSelectionEvents(){bindFeeTypeChangeEvent(),bindAssignmentFeeChangeEvent(),enableFeeAssignmentFeeHeadCallback()}var loadFeeAssignment={loadStudentScreen:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-assignment/"+e,function(e){$("#fee-assignment-screen").html(e),addStudentNewFeeAssignmentCallBack(),bindSearchStudentFeeAssignmentEvent(),bindAssignmentFeeSelectionEvents()})},loadBulkStudentScreen:function(){ajaxClient.get("/fees/bulk-student-assignment",function(e){$("#fee-assignment-screen").html(e),bindBulkFeeClassChangeEvent()})},loadFeeStructureAssignmentStudentScreen:function(){ajaxClient.get("/fees/fee-structure-change-home",function(e){$("#fee-assignment-screen").html(e),loadFeeAssignment.bindPaymentSearchStudentEvent()})},bindPaymentSearchStudentEvent:function(){var e="#student-payment-search-result";$("#student-payment-search").on("click",function(){loadFeeAssignment.doneStudentSearchTyping(e)}),$("#student-payment-search-text").on("keyup",function(t){13==t.keyCode&&loadFeeAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#student-payment-search-text",e,loadFeeAssignment.doneStudentSearchTyping)},doneStudentSearchTyping:function(e){var t=$("#student-payment-search-text").val().trim(),a=academicSessionHandler.getSelectedSessionId();loadFeeAssignment.studentLiveSearchEvent(a,t,e,loadFeeAssignment.loadStudentPaymentDetails,"ENROLLED")},studentLiveSearchEvent:function(e,t,a,n,i){ajaxClient.get("/fees/student-live-search/"+e+"?searchText="+t+"&status="+i,function(e){$(a).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(a,n)})},loadStudentPaymentDetails:function(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-details/"+t+"/"+e,function(e){$("#student-payment-screen").html(e)})},classChangeStudentPopUp:function(){$("#student-promotion-modal").modal("toggle"),loadFeeAssignment.bindPromotingStandardChange()},bindPromotingStandardChange:function(){var e=academicSessionHandler.getSelectedSessionId(),t=JSON.parse($("#student-details-json").text().trim()).studentId;ajaxClient.get("/fees/fee-structure-configuration/"+e+"/"+t,function(e){$("#promotion-configuration-div").html(e)})},changeStudentClass:function(){var e=academicSessionHandler.getSelectedSessionId(),t=JSON.parse($("#student-details-json").text().trim()).studentId,a=[];$("input.fee-structure-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find(".structure-id").text().trim();a.push(e)}});a={uuids:a};$("#student-promotion-modal").modal("toggle"),ajaxClient.post("/fees/fee-structure-change/"+e+"/"+t,{uuids:JSON.stringify(a)},function(e){$("#attendance-feed-status-modal-container").html(e),$("#fee-assignment-status-modal").modal({backdrop:"static",keyboard:!1})})}};function registerFeeAssignmentTabs(){$("#"+STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadStudentScreen()}),$("#"+BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadBulkStudentScreen()}),$("#"+FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){loadFeeAssignment.loadFeeStructureAssignmentStudentScreen()})}function bindFeeTypeChangeEvent(){$("#fee-type-select").change(function(){var e=$(this).find(":selected").val().trim();$(".fee-select").attr("style","display:none"),$(".fee-select").removeClass("active"),$(".fee-select").val(""),"regular"==e?($("#regular-fee-select").attr("style","display:block"),$("#regular-fee-select").addClass("active")):"onetime"==e?($("#onetime-fee-select").attr("style","display:block"),$("#onetime-fee-select").addClass("active")):"special"==e&&($("#special-fee-select").attr("style","display:block"),$("#special-fee-select").addClass("active")),clearAuthorizedFeeHeads()})}function clearAuthorizedFeeHeads(){$(".authorized-assignment-feeheads").attr("style","display:none"),$(".authorized-assignment-feeheads").removeClass("active"),resetAuthorizedFeeHeads()}function resetAuthorizedFeeHeads(){$(".add-assignment-fee-head-row").find(".assignment-fee-head").prop("checked",!1),$(".add-assignment-fee-head-row").find(".assignment-amount").val(""),$(".add-assignment-fee-head-row").find(".assignment-amount").prop("readonly",!0)}function displaySelectedAuthorizedFeeHeads(e){$("#"+e+"-feeheads").addClass("active"),$("#"+e+"-feeheads").attr("style","display:block")}function bindAssignmentFeeChangeEvent(){$(".fee-select").change(function(){var e=$(this).find(":selected").val().trim();clearAuthorizedFeeHeads(),displaySelectedAuthorizedFeeHeads(e)})}function bindClassChangeEvent(){$("#fee-assign-class").change(function(){var e=$(this).find(":selected").val().trim();loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),CLASS_ENTITY,e,CLASS_ASSIGNED_FEE_LIST)})}function bindBulkFeeClassChangeEvent(){$("#bulk-fee-assign-class").change(function(){var e=$(this).find(":selected").val().trim();searchStudentsInBulkAssignment(academicSessionHandler.getSelectedSessionId(),e)})}function bindShowSelectedStudentModal(){$("#selected-bulk-student-text").on("click",function(){$("#bulk-assignment-selected-students-modal").modal("toggle")})}function searchStudentsInBulkAssignment(e,t){ajaxClient.get("/fees/class-students/"+e+"/"+t,function(e){$("#searched-student-list").html(e)})}function bindSearchStudentFeeAssignmentEvent(){var e="#fee-assign-student-search-result";$("#fee-assign-search-student").on("click",function(){feeAssignment.doneStudentSearchTyping(e)}),$("#fee-assign-search-text").on("keyup",function(t){13==t.keyCode&&feeAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#fee-assign-search-text",e,feeAssignment.doneStudentSearchTyping)}function loadStudentFeesAssignment(e){loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),STUDENT_ENTITY,e,STUDENT_ASSIGNED_FEE_LIST)}function loadEntityFeesAssignment(e,t,a,n){t=t.trim(),a=a.trim(),""!=e&&""!=t?ajaxClient.get("/fees/v2/assigned-fees/"+e+"/"+t+"/"+a,function(e){$("#"+n).html(e),bindViewFeeAssignmentEvent(),bindUpdateFeeAssignmentEvent(),bindDeleteFeeAssignmentEvent(),t==STUDENT_ENTITY&&addStudentNewFeeAssignmentCallBack()}):showErrorDialogBox("Please select entity and session to assign fees.")}function enableFeeAssignmentFeeHeadCallback(){$(".assignment-fee-head").change(function(){$(this).is(":checked")?$(this).closest("tr").find("input.assignment-amount").prop("readonly",!1):$(this).closest("tr").find("input.assignment-amount").prop("readonly",!0)})}function addNewClassFeeAssignment(){if(!validateMandatoryFields($("#class-new-fee-assign-modal"))){$("#class-new-fee-assign-confirm-modal").modal("toggle");var e=$("#assign-new-fee-class-id").val(),t=getFeeAssignmentInfo(CLASS_ENTITY,[e]);null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(t){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(t),$("#fee-assignment-status-modal").modal("toggle"),loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),CLASS_ENTITY,e,CLASS_ASSIGNED_FEE_LIST)})}}function addNewStudentFeeAssignment(){$("#student-new-fee-assign-confirm-modal").modal("toggle");var e=$("#assign-new-fee-student-id").val(),t=($("#assign-new-fee-admission-number").val(),getFeeAssignmentInfo(STUDENT_ENTITY,[e]));null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(t){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(t),$("#fee-assignment-status-modal").modal("toggle"),loadEntityFeesAssignment(academicSessionHandler.getSelectedSessionId(),STUDENT_ENTITY,e,STUDENT_ASSIGNED_FEE_LIST)})}function generateFeesChart(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#fee-assign-view-student-id").text().trim(),a=$("#due-amount").is(":checked"),n=$("#collected-amount").is(":checked"),i=$("#discounted-amount").is(":checked"),o=$("#due-amount-till-today").is(":checked"),s=[];s.push(t);$("#generate-fee-chart-modal").modal("toggle"),window.open(baseURL+"/fees/generate-fee-chart?academic_session_id="+e+"&student_Ids="+s+"&student_per_page=1&due_amount="+a+"&collected_amount="+n+"&disounted_amount="+i+"&due_amount_till_today="+o,"_blank")}function generateBulkFeesCharts(){var e=academicSessionHandler.getSelectedSessionId(),t=[];if($("input.bulk-student-assignment-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-fee-assign-student-id").first().text().trim();t.push(e)}}),t.length<=0)alert("Please select at least one student to geenrate fee chart.");else{var a=$("#bulk-due-amount").is(":checked"),n=$("#bulk-collected-amount").is(":checked"),i=$("#bulk-discount-amount").is(":checked"),o=$("#bulk-due-amount-till-today").is(":checked"),s=$("#student-per-page").val();s<1?alert("Student Per Page Value Should Be greater than 0."):($("#generate-bulk-fee-chart-modal").modal("toggle"),window.open(baseURL+"/fees/generate-fee-chart?academic_session_id="+e+"&student_Ids="+t+"&student_per_page="+s+"&due_amount="+a+"&collected_amount="+n+"&disounted_amount="+i+"&due_amount_till_today="+o,"_blank"))}}function addBulkStudentFeeAssignment(){$("#bulk-student-new-fee-assign-confirm-modal").modal("toggle");var e=getSelectedBulkStudentIds(),t=getFeeAssignmentInfo(STUDENT_ENTITY,e);null!=t&&ajaxClient.post("/fees/add-fee-assignment",{feeAssignmentInfo:JSON.stringify(t)},function(e){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(e),$("#fee-assignment-status-modal").modal("toggle")})}function getSelectedBulkStudentIds(){var e=[];return $("tr.bulk-assignment-selected-student").each(function(){var t=$(this).attr("id");e.push(t)}),e}function getFeeAssignmentInfo(e,t){var a=$(".fee-select.active").first().find(":selected").val().trim();if(""==a.trim())return showErrorDialogBox("Please select one fees before proceeding for assignment."),null;var n=getAssignedFeeHeadAmounts();return 0==n.length?(showErrorDialogBox("Atleast one fee head must be assigned"),null):{entityIds:t,feeEntity:e,feeId:a,feeHeadAmounts:n}}function getAssignedFeeHeadAmounts(){var e=[];return $(".authorized-assignment-feeheads.active").find("tr.add-assignment-fee-head-row").each(function(){if(0!=$(this).find(".assignment-fee-head").length&&$(this).find(".assignment-fee-head").is(":checked")){var t=$(this).attr("id"),a=0;""!=$(this).find("input.assignment-amount").val()&&(a=parseFloat($(this).find("input.assignment-amount").val()));var n={};n.feeHeadId=t,n.amount=a,e.push(n)}}),e}function bindViewFeeAssignmentEvent(){$(".view-assigned-fee").on("click",function(){var e=$(this).parent().find(".assigned-fee-info").text().trim();populateViewFeeAssignmentContent(JSON.parse(e))})}function bindUpdateFeeAssignmentEvent(){$(".update-assigned-fee").off("click").on("click",function(){for(var e=$(this).parent().find(".assigned-fee-info").text().trim(),t=JSON.parse(e),a=$("#session-authorized-fee-assignments").text().trim(),n=JSON.parse(a).authorized_fee_assignments,i=null,o=0;o<n.length;o++)if(n[o].feeConfigurationResponse.feeConfigurationBasicInfo.feeId==t.feeConfigurationResponse.feeConfigurationBasicInfo.feeId){i=n[o];break}populateUpdateFeeAssignmentContent(t,i)}),$("#update-assigned-fee-amounts").off("click").on("click",function(){$("#update-assigned-fee-amounts-modal").modal("toggle")})}function updateAssignedFeeAmounts(){$("#update-assigned-fee-amounts-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),t=$("#fee-assign-view-student-id").text().trim(),a=getUpdateFeeAssignmentAmounts(),n={studentId:t,feeIdFeeHeadList:a};ajaxClient.post("/fees/update-fee-assignment-amounts/"+e,{payload:JSON.stringify(n)},function(a){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(a),$("#fee-assignment-status-modal").modal("toggle"),loadEntityFeesAssignment(e,STUDENT_ENTITY,t,STUDENT_ASSIGNED_FEE_LIST)})}function moduleExists(e,t){for(var a=0;a<e.length;a++)if(e[a].moduleId==t)return!0;return!1}function populateUpdateFeeAssignmentContent(e,t){var a=$("#entity-type").text().trim();$("#update\\.assigned-fee-id").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeId),$("#update\\.assigned-fee-name").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeName),$("#update\\.assigned-fee-academic-session").val(e.feeConfigurationResponse.academicSession.displayName),$("#update\\.assigned-fee-description").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.description),"ONE_TIME"!=e.feeConfigurationResponse.feeConfigurationBasicInfo.feeType?$("#update\\.assigned-fee-duration").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month+" to "+e.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month):$("#update\\.assigned-fee-duration-container").attr("style","display:none");var n={};for(o=0;o<e.feeHeadAmountDetailsList.length;o++)n[e.feeHeadAmountDetailsList[o].feeHeadConfiguration.feeHeadId]=e.feeHeadAmountDetailsList[o];for(var i={},o=0;o<t.authorizedFeeHeadAssignments.length;o++)t.authorizedFeeHeadAssignments[o].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId in n||(i[t.authorizedFeeHeadAssignments[o].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId]=t.authorizedFeeHeadAssignments[o]);var s="";o=0;if(a==STUDENT_ENTITY){var d=$("#fee-assign-view-student-id").text().trim(),l=$("#fee-assign-search-text").val().trim();for(var r in $("#update\\.assigned-entity-id").val(d),$("#update\\.assigned-admission-number").val(l),n)s=n[r].feeEntity!=CLASS_ENTITY&&moduleExists(n[r].authorizedModules,"FEES")?s+'<tr class="fee-head-row" id = "'+r+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" checked><label class="form-check-label"> '+n[r].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+n[r].amount+'"></td></tr>':s+'<tr class="fee-head-row non-editable-row" id = "'+r+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+n[r].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+n[r].amount+'" readonly></td></tr>'}else{d=$("#fee-assign-class").val();for(var r in $("#update\\.assigned-entity-id").val(d),n)s=moduleExists(n[r].authorizedModules,"FEES")?s+'<tr class="fee-head-row" id = "'+r+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" checked><label class="form-check-label"> '+n[r].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+n[r].amount+'"></td></tr>':s+'<tr class="fee-head-row non-editable-row" id = "'+r+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+n[r].feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" value="'+n[r].amount+'" readonly></td></tr>'}for(var c in i)s=i[c].module_ids.indexOf("FEES")<0?s+'<tr class="fee-head-row non-editable-row" id = "'+c+'"><td><div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head" onclick="return false;" checked disabled><label class="form-check-label"> '+i[c].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+' </label></div></td><td><input type="number" class="form-control form-control-sm assignment-amount" readonly></td></tr>':s+'<tr class = "fee-head-row" id = "'+c+'"><td> <div class="form-check"><input type="checkbox" class="form-check-input assignment-fee-head"><label class="form-check-label" for="fee-head"> '+i[c].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+' </label></div></td> <td> <input type="number" class="form-control form-control-sm assignment-amount" id="update.amount'+c+'" readonly></td></tr>';$("#update\\.assigned-fee-head-amounts").html(s),enableFeeAssignmentFeeHeadCallback(),$("#update-assigned-fee-modal").modal("toggle")}function updateFeeAssignment(){var e=$("#entity-type").text().trim();$("#update-assigned-fee-modal").modal("toggle");var t=$("#update\\.assigned-entity-id").val(),a=($("#update\\.assigned-admission-number").val(),getUpdateFeeAssignmentInfo(e,t));ajaxClient.post("/fees/update-fee-assignment",{updateFeeAssignmentInfo:JSON.stringify(a)},function(a){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(a),$("#fee-assignment-status-modal").modal("toggle");var n=academicSessionHandler.getSelectedSessionId();loadEntityFeesAssignment(n,e,t,e==STUDENT_ENTITY?STUDENT_ASSIGNED_FEE_LIST:CLASS_ASSIGNED_FEE_LIST)})}function getUpdateFeeAssignmentInfo(e,t){var a=$("#update\\.assigned-fee-id").val(),n=[];return $("tr.fee-head-row").each(function(){if($(this).find(".assignment-fee-head").is(":checked")&&!$(this).hasClass("non-editable-row")){var e=$(this).attr("id"),t=0;""!=$(this).find("input.assignment-amount").val()&&(t=parseFloat($(this).find("input.assignment-amount").val()));var a={};a.feeHeadId=e,a.amount=t,n.push(a)}}),{entityId:t,feeEntity:e,feeId:a,feeHeadAmounts:n}}function getUpdateFeeAssignmentAmounts(){var e={};$("tr.update-assigned-fee-amount-row").each(function(){if(!$(this).find("input.fee-row-assignment-amount").hasClass("read-only-value")){var t=$(this).attr("id").split("|"),a=t[0],n=t[1],i=0;""!=$(this).find("input.assignment-amount").val()&&(i=parseFloat($(this).find("input.fee-row-assignment-amount").val()));var o={};o.feeHeadId=n,o.amount=i;var s=[];a in e&&(s=e[a]),s.push(o),e[a]=s}});var t=[];return $.each(e,function(e,a){t.push({feeId:e,feeHeadAmountList:a})}),t}function bindDeleteFeeAssignmentEvent(){$(".delete-assigned-fee").on("click",function(){var e=$(this).parent().find(".assigned-fee-info").text().trim(),t=JSON.parse(e),a=$("#entity-type").text().trim();if($("#delete\\.assigned-fee-id").val(t.feeConfigurationResponse.feeConfigurationBasicInfo.feeId),a==STUDENT_ENTITY){var n=$("#fee-assign-view-student-id").text().trim(),i=$("#fee-assign-search-text").val().trim();$("#delete\\.assigned-entity-id").val(n),$("#delete\\.assigned-admission-number").val(i)}else{n=$("#fee-assign-class").val();$("#delete\\.assigned-entity-id").val(n)}$("#delete-assigned-fee-modal").modal("toggle")})}function deleteAssignedFees(){var e=$("#entity-type").text().trim();$("#delete-assigned-fee-modal").modal("toggle");var t=$("#delete\\.assigned-entity-id").val(),a=($("#delete\\.assigned-admission-number").val(),$("#delete\\.assigned-fee-id").val());ajaxClient.post("/fees/delete-fee-assignment/"+e+"/"+t+"/"+a,{},function(a){feeAssignment.resetToMainScreen(),$("#fee-assignment-status-modal-container").html(a),$("#fee-assignment-status-modal").modal("toggle");var n=academicSessionHandler.getSelectedSessionId();loadEntityFeesAssignment(n,e,t,e==STUDENT_ENTITY?STUDENT_ASSIGNED_FEE_LIST:CLASS_ASSIGNED_FEE_LIST)})}var dueDateMaxPastDays=365,REGULAR_FEE_LIST_TAB_ID="regular-fee-tab",ONETIME_FEE_LIST_TAB_ID="onetime-fee-tab",SPECIAL_FEE_LIST_TAB_ID="special-fee-tab",NO_STREAM_VALUE="NA";function loadFeeConfigurationPage(){ajaxClient.get("/fees/fee-config",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(initFeeConfigScreen),registerAddFeeConfigCallback(),reloadFeeConfigPageBindEvents(),bindRemoveErrorDisplayEvent()})}function reloadFeeConfigPageBindEvents(){bindViewFeeEvents(),bindUpdateFeeEvents(),initDateWithYearRange("-5:+5",!0)}function initFeeConfigScreen(){var e=academicSessionHandler.getSelectedSession();$(".fee-config-academic-session").html('<option value="'+e.academicSessionId+'">'+e.displayName+"</option>"),$("#regular-fee-start-month").val(e.startMonth);var t=$(".fee-config-list-type.active").first().attr("id").trim();loadFeelist(e.academicSessionId,t)}function openAddFeeConfigModal(){resetFeeConfigModal();var e=$(".fee-config-list-type.active").first().attr("id").trim();e==REGULAR_FEE_LIST_TAB_ID?$("a#regular-fee").trigger("click"):e==ONETIME_FEE_LIST_TAB_ID?$("a#one-time-fee").trigger("click"):e==SPECIAL_FEE_LIST_TAB_ID&&$("a#special-fee").trigger("click"),$("#add-fee-config-modal").modal({backdrop:"static",keyboard:!1}),$("#add-fee-config-modal").modal("toggle")}function resetFeeConfigModal(){$("#add-onetime-fee-form").find("input").val(""),$("#add-onetime-fee-form").find("select:not(.fee-config-academic-session)").val(""),$("#add-onetime-fee-form").find("textarea").val(""),$("#add-special-fee-form").find("input").val(""),$("#add-special-fee-form").find("select:not(.fee-config-academic-session)").val(""),$("#add-special-fee-form").find("textarea").val("")}function resetFeeHeadConfigModal(){$("#add-fee-head-modal").find("input").val(""),$("#add-fee-head-modal").find("select").val(""),$("#add-fee-head-modal").find("textarea").val("")}function refreshFeeConfigPage(e){loadFeelist(academicSessionHandler.getSelectedSessionId(),e)}function loadFeeHeadConfigurationPage(){ajaxClient.get("/fees/fee-head-config",function(e){$("#main-content").html(e),registerAddFeeHeadCallback(),registerUpdateFeeHeadCallBack(),registerDeleteFeeHeadCallBack(),bindRemoveErrorDisplayEvent()})}function registerAddFeeHeadCallback(){$("#add-fee-head").on("click",function(){if(!validateMandatoryFields($("#add-fee-head-modal"))){var e=$("#fee-head-name").val(),t=$("#fee-head-category option:selected").val(),a=$("#fee-head-description").val();$("#add-fee-head-modal").modal("toggle");var n={feeCategoryId:t,feeHead:e,description:a};ajaxClient.post("/fees/add-fee-head",{feeHeadData:JSON.stringify(n)},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}})}function registerAddFeeConfigCallback(){$("#add-fee-config-button").on("click",function(){var e=$(".fee-config-type.active").attr("id");if("regular-fee"==e){if(validateMandatoryFields($("#add-regular-fee-form")))return;var t=$("#regular-fee-academic-session option:selected").val(),a=$("#regular-fee-duration").val(),n=$("#regular-fee-applicable-fine:checkbox:checked").length>0,i=$("#regular-fee-transfer-to-wallet:checkbox:checked").length>0;$("#add-fee-config-modal").modal("toggle"),ajaxClient.post("/fees/add-regular-fee-config",{sessionId:t,duration:a,fineApplicable:n,transferToWallet:i},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}else if("one-time-fee"==e){if(validateMandatoryFields($("#add-onetime-fee-form")))return;t=$("#onetime-fee-academic-session option:selected").val();var o=$("#onetime-fee-name").val(),s=$("#onetime-fee-description").val(),d=$("#onetime-fee-allow-pending-enrollment:checkbox:checked").length>0,l=(n=$("#onetime-fee-applicable-fine:checkbox:checked").length>0,i=$("#onetime-fee-transfer-to-wallet:checkbox:checked").length>0,null);null!=(r=getDate($("#onetime-fee-due-date").val()))&&(l=r.getTime()/1e3),$("#add-fee-config-modal").modal("toggle"),oneTimeFeeData={feeName:o,academicSessionId:t,description:s,dueDate:l,allowPendingEnrollment:d,fineApplicable:n,transferToWallet:i},ajaxClient.post("/fees/add-onetime-fee",{oneTimeFeeData:JSON.stringify(oneTimeFeeData)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID)})}else if("special-fee"==e){if(validateMandatoryFields($("#add-special-fee-form")))return;t=$("#special-fee-academic-session option:selected").val(),o=$("#special-fee-name").val(),s=$("#special-fee-description").val();var r,c=$("#special-fee-start-month option:selected").val(),u=$("#special-fee-end-month option:selected").val(),f=(d=$("#special-fee-allow-pending-enrollment:checkbox:checked").length>0,n=$("#special-fee-applicable-fine:checkbox:checked").length>0,i=$("#special-fee-transfer-to-wallet:checkbox:checked").length>0,null);null!=(r=getDate($("#special-fee-due-date").val()))&&(f=r.getTime()/1e3),$("#add-fee-config-modal").modal("toggle"),specialFeeData={startMonth:c,endMonth:u,feeConfigurationBasicInfo:{feeName:o,academicSessionId:t,description:s,dueDate:f,allowPendingEnrollment:d,fineApplicable:n,transferToWallet:i}},ajaxClient.post("/fees/add-special-fee",{specialFeeData:JSON.stringify(specialFeeData)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID)})}})}function loadFeelist(e,t){ajaxClient.get("/fees/fee-list/"+e,function(e){$("#fees-list").html(e),reloadFeeConfigPageBindEvents(),null!=t&&$("#"+t).trigger("click")})}function loadFeeHeadlist(){ajaxClient.get("/fees/fee-head-list",function(e){$("#fee-head-list").html(e),registerUpdateFeeHeadCallBack(),registerDeleteFeeHeadCallBack()})}function bindViewFeeEvents(){bindViewRegularFeeEvent(),bindViewOneTimeFeeEvent(),bindViewSpecialFeeEvent(),bindDeleteOneTimeFeeEvent(),bindDeleteSpecialFeeEvent()}function bindViewRegularFeeEvent(){$(".view-regular-fee").on("click",function(){var e=$(this).parent().find(".regular-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.regular-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.regular-fee-academic-session").val(t.academicSession.displayName),$("#view\\.regular-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.regular-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view\\.regular-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#view\\.regular-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#view\\.regular-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#view-regular-fee-modal").modal("toggle")})}function bindViewOneTimeFeeEvent(){$(".view-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.onetime-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.onetime-fee-academic-session").val(t.academicSession.displayName),$("#view\\.onetime-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.onetime-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#view\\.onetime-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#view\\.onetime-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#view\\.onetime-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view-onetime-fee-modal").modal("toggle")})}function bindDeleteOneTimeFeeEvent(){$(".delete-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#delete-one-time-fee-id").text(t.feeConfigurationBasicInfo.feeId),$("#delete-one-time-fee-confirm-modal").modal("toggle")})}function bindViewSpecialFeeEvent(){$(".view-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#view\\.special-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#view\\.special-fee-academic-session").val(t.academicSession.displayName),$("#view\\.special-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#view\\.special-fee-description").val(t.feeConfigurationBasicInfo.description),$("#view\\.special-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#view\\.special-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#view\\.special-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#view\\.special-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#view-special-fee-modal").modal("toggle")})}function bindDeleteSpecialFeeEvent(){$(".delete-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#delete-special-fee-id").text(t.feeConfigurationBasicInfo.feeId),$("#delete-special-fee-confirm-modal").modal("toggle")})}function deleteRegularFees(){var e=academicSessionHandler.getSelectedSessionId();$("#delete-regular-fee-confirm-modal").modal("toggle"),ajaxClient.post("/fees/delete-regular-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}function deleteSpecialFee(){$("#delete-special-fee-confirm-modal").modal("toggle");var e=$("#delete-special-fee-id").text();ajaxClient.post("/fees/delete-special-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID)})}function deleteOneTimeFee(){$("#delete-one-time-fee-confirm-modal").modal("toggle");var e=$("#delete-one-time-fee-id").text();ajaxClient.post("/fees/delete-one-time-fees/"+e,{},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID)})}function bindUpdateFeeEvents(){bindUpdateRegularFeeEvent(),bindUpdateOneTimeFeeEvent(),bindUpdateSpecialFeeEvent()}function bindUpdateRegularFeeEvent(){$(".update-regular-fee").on("click",function(){var e=$(this).parent().find(".regular-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.regular-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.regular-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.regular-fee-academic-session").val(t.academicSession.displayName),$("#update\\.regular-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.regular-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update\\.regular-fee-duration").val(t.feeConfigurationBasicInfo.startMonthYear.month+" to "+t.feeConfigurationBasicInfo.endMonthYear.month),$("#update\\.regular-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#update\\.regular-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#update-regular-fee-modal").modal("toggle")})}function bindUpdateOneTimeFeeEvent(){$(".update-onetime-fee").on("click",function(){var e=$(this).parent().find(".onetime-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.onetime-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.one-time-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.one-time-fee-academic-session").val(t.academicSession.displayName),$("#update\\.one-time-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.one-time-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#update\\.onetime-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#update\\.onetime-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#update\\.one-time-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update-one-time-fee-modal").modal("toggle")})}function bindUpdateSpecialFeeEvent(){$(".update-special-fee").on("click",function(){var e=$(this).parent().find(".special-fee-info").text().trim(),t=JSON.parse(e);$("#update\\.special-fee-id").val(t.feeConfigurationBasicInfo.feeId),$("#update\\.special-fee-name").val(t.feeConfigurationBasicInfo.feeName),$("#update\\.special-fee-academic-session").val(t.academicSession.displayName),$("#update\\.special-fee-due-date").val(t.feeConfigurationBasicInfo.displayDueDate),$("#update\\.special-fee-description").val(t.feeConfigurationBasicInfo.description),$("#update\\.special-fee-start-month").val(t.feeConfigurationBasicInfo.startMonthYear.month),$("#update\\.special-fee-end-month").val(t.feeConfigurationBasicInfo.endMonthYear.month),$("#update\\.special-fee-allow-pending-enrollment").prop("checked",t.feeConfigurationBasicInfo.allowPendingEnrollment),$("#update\\.special-fee-applicable-fine").prop("checked",t.feeConfigurationBasicInfo.fineApplicable),$("#update\\.special-fee-transfer-to-wallet").prop("checked",t.feeConfigurationBasicInfo.transferToWallet),$("#update-special-fee-modal").modal("toggle")})}function updateRegularFeeConfiguration(){if(!validateMandatoryFields($("#update-regular-fee-modal"))){var e=$("#update\\.regular-fee-id").val(),t=$("#update\\.regular-fee-name").val(),a=$("#update\\.regular-fee-description").val(),n=getDate($("#update\\.regular-fee-due-date").val()),i=$("#update\\.regular-fee-applicable-fine:checkbox:checked").length>0,o=$("#update\\.regular-fee-transfer-to-wallet:checkbox:checked").length>0,s=null;null!=n&&(s=n.getTime()/1e3),$("#update-regular-fee-modal").modal("toggle");var d={feeId:e,feeName:t,dueDate:s,fineApplicable:i,transferToWallet:o,description:a};ajaxClient.post("/fees/update-fee-config",{updateFeeConfigData:JSON.stringify(d)},function(e){$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID)})}}function updateOneTimeFeeConfiguration(){if(!validateMandatoryFields($("#update-one-time-fee-modal"))){var e=$("#update\\.onetime-fee-id").val(),t=$("#update\\.one-time-fee-name").val(),a=$("#update\\.one-time-fee-description").val(),n=$("#update\\.one-time-fee-allow-pending-enrollment:checkbox:checked").length>0,i=$("#update\\.onetime-fee-applicable-fine:checkbox:checked").length>0,o=$("#update\\.onetime-fee-transfer-to-wallet:checkbox:checked").length>0,s=getDate($("#update\\.one-time-fee-due-date").val()),d=null;null!=s&&(d=s.getTime()/1e3),$("#update-one-time-fee-modal").modal("toggle");var l={feeId:e,feeName:t,dueDate:d,description:a,allowPendingEnrollment:n,fineApplicable:i,transferToWallet:o};ajaxClient.post("/fees/update-fee-config",{updateFeeConfigData:JSON.stringify(l)},function(e){refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID),$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle")})}}function updateSpecialFeeConfiguration(){if(!validateMandatoryFields($("#update-special-fee-modal"))){var e=$("#update\\.special-fee-id").val(),t=$("#update\\.special-fee-name").val(),a=$("#update\\.special-fee-start-month option:selected").val(),n=$("#update\\.special-fee-end-month option:selected").val(),i=$("#update\\.special-fee-allow-pending-enrollment:checkbox:checked").length>0,o=$("#update\\.special-fee-applicable-fine:checkbox:checked").length>0,s=$("#update\\.special-fee-transfer-to-wallet:checkbox:checked").length>0,d=$("#update\\.special-fee-description").val(),l=getDate($("#update\\.special-fee-due-date").val()),r=null;null!=l&&(r=l.getTime()/1e3),$("#update-special-fee-modal").modal("toggle");var c={feeId:e,feeName:t,dueDate:r,description:d,startMonth:a,endMonth:n,allowPendingEnrollment:i,fineApplicable:o,transferToWallet:s};ajaxClient.post("/fees/update-special-fee-config",{updateSpecialFeeConfigData:JSON.stringify(c)},function(e){refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID),$("#feeconfig\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle")})}}function registerUpdateFeeHeadCallBack(){$(".update-fee-head").on("click",function(){var e=$(this).parent().find(".fee-head-info").text().trim(),t=JSON.parse(e);$("#update\\.fee-head-id").val(t.feeHeadConfiguration.feeHeadId),$("#update\\.fee-head-name").val(t.feeHeadConfiguration.feeHead),$("#update\\.fee-category-id").val(t.feeCategory.feeCategoryId),$("#update\\.fee-category-name").val(t.feeCategory.feeCategoryName),$("#update\\.fee-head-description").val(t.feeHeadConfiguration.description),$("#update-fee-head-modal").modal("toggle")})}function updateFeeHead(){if(!validateMandatoryFields($("#update-fee-head-modal"))){var e=$("#update\\.fee-head-id").val(),t=$("#update\\.fee-head-name").val(),a=$("#update\\.fee-category-id").val(),n=$("#update\\.fee-head-description").val();$("#update-fee-head-modal").modal("toggle");var i={feeHeadId:e,feeHead:t,feeCategoryId:a,description:n};ajaxClient.post("/fees/update-fee-head",{updateFeeHeadData:JSON.stringify(i)},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}}function registerDeleteFeeHeadCallBack(){$(".delete-fee-head").on("click",function(){var e=$(this).parent().find(".fee-head-info").text().trim(),t=JSON.parse(e);$("#delete-fee-head-id").val(t.feeHeadConfiguration.feeHeadId),$("#delete-fee-head-confirm-modal").modal("toggle")})}function deleteFeeHead(){$("#delete-fee-head-confirm-modal").modal("toggle");var e=$("#delete-fee-head-id").val();ajaxClient.post("/fees/delete-fee-head-configuration/"+e,{},function(e){$("#feehead\\.status-modal-container").html(e),$("#feeconfig\\.status-modal").modal("toggle"),loadFeeHeadlist()})}var graphLoadTimer,feeStructure={dataCache:{},INSTITUTE_ENTITY:"institute",initDataCache:function(){for(var e=readJson("#session-authorized-fee-assignments"),t=readJson("#add-fee-standards"),a=readJson("#configured-fee-structure-info"),n={},i={},o={},s={},d={},l={},r=0;r<e.length;r++){var c=e[r].feeConfigurationResponse.feeConfigurationBasicInfo;o[c.feeId]=c;for(var u=[],f=0;f<e[r].authorizedFeeHeadAssignments.length;f++){var m=e[r].authorizedFeeHeadAssignments[f].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId;s[m]=e[r].authorizedFeeHeadAssignments[f].feeHeadConfigurationResponse.feeHeadConfiguration,e[r].authorizedFeeHeadAssignments[f].module_ids.includes("FEES")&&u.push(m)}d[c.feeId]=u}for(r=0;r<t.length;r++){var p=t[r];n[p.standardId]=p}for(r=0;r<a.length;r++){var g=a[r];i[g.structureId]=g}for(feeId in feeStructure.dataCache={},feeStructure.dataCache.feeStructureMap=i,feeStructure.dataCache.standards=t,feeStructure.dataCache.standardsMap=n,feeStructure.dataCache.feeMap=o,feeStructure.dataCache.feeHeadMap=s,feeStructure.dataCache.authorizedFeeIdFeeHeadMap=d,feeStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},d){u=d[feeId];var h=feeStructure.createFeeHeadSelectMenu(u,"");l[feeId]=h}feeStructure.dataCache.feeIdFeeHeadSelectOptionData=l},createFeeHeadSelectMenu:function(e,t){for(var a='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',n=0;n<e.length;n++){var i=e[n],o=feeStructure.dataCache.feeHeadMap[i].feeHead;a+=t==i?'<option value="'+i+'" selected>'+o+"</option>":'<option value="'+i+'">'+o+"</option>"}return a+="</select>"},loadFeeStructureConfigurationPageWithoutSession:function(){ajaxClient.get("/fees/fee-structure-config-without-session",function(e){$("#main-content").html(e),feeStructure.initDataCache(),academicSessionHandler.bindSessionChangeEvent(feeStructure.changeSession)})},loadFeeStructureConfigurationPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/fee-structure-config/"+e,function(e){$("#configured-fee-structure-list").html(e),feeStructure.initDataCache()})},changeSession:function(){feeStructure.loadFeeStructureConfigurationPage()},createFeeContainerId:function(e,t){return e+"-"+t},createEntityContainerId:function(e){return e+"-fee-structure-container"},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var a=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&a++}),a>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()})},insertFeeHeadRow:function(e,t,a,n){var i="";if(null==a){i='<tr id=""> <td> '+feeStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var o=feeStructure.dataCache.authorizedFeeIdFeeHeadMap[t];i='<tr id=""> <td> '+feeStructure.createFeeHeadSelectMenu(o,a)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+n+'"> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(i),feeStructure.bindFeeHeadRowEvents(e)},bindFeeContainerEvents:function(e,t,a,n,i){$("#"+t).find(".add-fee-head-button").on("click",function(){feeStructure.insertFeeHeadRow(t,n,null,0)}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[n],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),feeStructure.populateSelectFeesDropdown(e,a,i)})},insertFeeDetailsCard:function(e,t,a,n){n[a]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=feeStructure.createEntityContainerId(t),o=feeStructure.createFeeContainerId(t,a),s='<div class="fee-structure"> <div class="card card-border text-center" id="'+o+'"> <p class="fee-id" style="display:none;">'+a+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+feeStructure.dataCache.feeMap[a].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(s),feeStructure.bindFeeContainerEvents(e,o,t,a,n),feeStructure.populateSelectFeesDropdown(e,t,n)},bindSelectFeesEvent:function(e,t,a){$(e).find(".fee-select-option").on("click",function(t){t.stopPropagation();var n=$(this).attr("id").split("-select-")[0],i=$(this).attr("id").split("-select-")[1];feeStructure.insertFeeDetailsCard(e,i,n,a)})},populateSelectFeesDropdown:function(e,t,a){var n=feeStructure.dataCache.feeMap,i="";for(feeId in n)feeId in a||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+n[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),feeStructure.bindSelectFeesEvent(e,t,a)},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete feeStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(feeStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;"),feeStructure.populateStandardSelectDropdown()})},getStandardContainerId:function(e){return e+"-container"},insertStandardDetailsCard:function(e){feeStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=feeStructure.dataCache.standardsMap,a="";null!=t[e].stream&&t[e].stream!=NO_STREAM_VALUE&&(a=" ("+t[e].stream+")");var n=t[e].standardName.toUpperCase()+a,i=feeStructure.getStandardContainerId(e),o='<div id="'+i+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+n+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(o),feeStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},feeStructure.populateSelectFeesDropdown("#"+i,e,feeStructure.dataCache.configureNewStructure.standard.selectedFees[e]),feeStructure.bindStandardContainerEvents(i,e),feeStructure.populateStandardSelectDropdown()},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];feeStructure.insertStandardDetailsCard(e)})},populateStandardSelectDropdown:function(){var e=feeStructure.dataCache.standardsMap,t="";for(standardId in e){var a="";null!=e[standardId].stream&&e[standardId].stream!=NO_STREAM_VALUE&&(a=" ("+e[standardId].stream+")");var n=e[standardId].standardName.toUpperCase()+a;standardId in feeStructure.dataCache.configureNewStructure.standard.selectedStandards||(t+='<a class="dropdown-item standard-select-option" href="#" id="'+standardId+'-select">'+n+"</a>")}$(".class-fee-structure").find(".standard-select-dropdown").html(t),feeStructure.bindStandardSelectEvent()},resetFeeStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p>'),$("#class-fee-structure-config-accordion").html('<p class="standrad-add-structure-hint-text black-color text-center"> Select class to add new fee structure</p>'),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#fee-structure-name").val(""),$("#fee-structure-types").val(""),$("#fee-structure-name").attr("disabled",!1),$("#fee-structure-types").attr("disabled",!1),$("#fee-structure-config-modal").find(".modal-footer").css("display",""),$("#nav-structure-tab").find(".nav-item").removeClass("active"),$("#nav-institute-structure-config-tab").addClass("active"),$("#nav-structure-tabContent").find(".tab-pane").removeClass("show active"),$("#nav-institute-structure-config").addClass("show active")},loadNewFeeStructureConfigureModal:function(){feeStructure.resetFeeStructureModal(),$("#fee-structure-config-modal").find(".modal-title").html("Configure New Fee Structure"),$("#submit-fee-structure").html("Configure New Fee Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),feeStructure.dataCache.configureNewStructure.institute.selectedFees={},feeStructure.dataCache.configureNewStructure.standard.selectedStandards={},feeStructure.dataCache.configureNewStructure.standard.selectedFees={},feeStructure.dataCache.selectedStructureId=null,feeStructure.populateSelectFeesDropdown(".institute-structure",feeStructure.INSTITUTE_ENTITY,feeStructure.dataCache.configureNewStructure.institute.selectedFees),feeStructure.populateStandardSelectDropdown()},fillFeeStructureModal:function(e,t){feeStructure.dataCache.selectedStructureId=e;var a=feeStructure.dataCache.feeStructureMap;if(e in a){var n=a[e];feeStructure.resetFeeStructureModal(),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#fee-structure-name").val(n.structureName),$("#fee-structure-types").val(n.feeStructureType),$("#fee-structure-config-modal").find(".modal-title").html("Update Fee Structure"),$("#submit-fee-structure").html("Update Fee Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),feeStructure.dataCache.configureNewStructure.institute.selectedFees={},feeStructure.dataCache.configureNewStructure.standard.selectedStandards={},feeStructure.dataCache.configureNewStructure.standard.selectedFees={};for(var i={},o={},s=0;s<n.entityFeeAssignments.length;s++){var d=n.entityFeeAssignments[s];if("INSTITUTE"==d.feeEntity)for(var l=0;l<d.feeIdFeeHeadDetailsList.length;l++){i[u=(c=d.feeIdFeeHeadDetailsList[l]).feeConfigurationResponse.feeConfigurationBasicInfo.feeId]=c}else if("CLASS"==d.feeEntity){var r=d.entityId;o[r]={};for(l=0;l<d.feeIdFeeHeadDetailsList.length;l++){var c,u=(c=d.feeIdFeeHeadDetailsList[l]).feeConfigurationResponse.feeConfigurationBasicInfo.feeId;o[r][u]=c}}}for(u in i){feeStructure.insertFeeDetailsCard(".institute-structure",feeStructure.INSTITUTE_ENTITY,u,feeStructure.dataCache.configureNewStructure.institute.selectedFees);var f=feeStructure.createFeeContainerId(feeStructure.INSTITUTE_ENTITY,u);for(s=0;s<i[u].feeHeadAmountDetailsList.length;s++){var m=i[u].feeHeadAmountDetailsList[s].feeHeadId,p=i[u].feeHeadAmountDetailsList[s].amount;feeStructure.insertFeeHeadRow(f,u,m,p)}}for(r in o){feeStructure.insertStandardDetailsCard(r);var g=feeStructure.getStandardContainerId(r);for(u in o[r]){feeStructure.insertFeeDetailsCard("#"+g,r,u,feeStructure.dataCache.configureNewStructure.standard.selectedFees[r]);for(f=feeStructure.createFeeContainerId(r,u),s=0;s<o[r][u].feeHeadAmountDetailsList.length;s++){m=o[r][u].feeHeadAmountDetailsList[s].feeHeadId,p=o[r][u].feeHeadAmountDetailsList[s].amount;feeStructure.insertFeeHeadRow(f,u,m,p)}}}feeStructure.populateSelectFeesDropdown(".institute-structure",feeStructure.INSTITUTE_ENTITY,feeStructure.dataCache.configureNewStructure.institute.selectedFees),feeStructure.populateStandardSelectDropdown(),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#fee-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#fee-structure-config-modal").find(".modal-footer").css("display","none"))}else showErrorDialogBox("Invalid Fee Structure.")},populateDeleteStructureModal:function(e){var t=feeStructure.dataCache.feeStructureMap[e];feeStructure.dataCache.feeStructureID=e,$("#delete-fee-structure-modal-text").html("Do you want to delete fee Structure : "+t.structureName+"?")},submitFeeStructure:function(){if(validateMandatoryFields($("#fee-structure-config-modal")))showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.","#fee-structure-config-modal");else{$("#fee-structure-config-modal").modal("toggle");var e=$("#fee-structure-name").val(),t=academicSessionHandler.getSelectedSessionId(),a=$("#fee-structure-types option:selected").val(),n=null;null!=feeStructure.dataCache.selectedStructureId&&""!=feeStructure.dataCache.selectedStructureId.trim()&&(n=feeStructure.dataCache.selectedStructureId);var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:feeStructure.getFeeStructureData("#institute-fee-structure-container")},o=feeStructure.getStandardsFeeStructureData();o.push(i);var s=[{structureId:n,structureName:e,feeStructureType:a,entityFeeAssignmentPayloads:o}],d="/fees/add-fee-structure/"+t;null!=n&&(d="/fees/update-fee-structure/"+n+"/"+t),ajaxClient.post(d,{feeAssignmentStructurePayload:JSON.stringify(s)},function(e){$("#fee-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),feeStructure.loadFeeStructureConfigurationPage()})}},getStandardsFeeStructureData:function(){var e=[];return $("#class-fee-structure-config-accordion").find(".standard-fee-structure").each(function(){var t=$(this).find(".standard-id").text().trim(),a=feeStructure.getFeeStructureData(this);e.push({entityId:t,feeEntity:"CLASS",feeIdFeeHeadsList:a})}),e},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),a=[],n={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in n)){var t=$(this).find(".fee-head-amount").val();a.push({feeHeadId:e,amount:t})}}),a.length>0&&t.push({feeId:e,feeHeadAmountList:a})}),t},deleteFeeStructure:function(){$("#delete-fee-structure-modal").modal("toggle");var e=feeStructure.dataCache.feeStructureID,t=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/fees/delete-fee-structure/"+e+"/"+t,{},function(e){$("#fee-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),feeStructure.loadFeeStructureConfigurationPage()})}},discountStructure={dataCache:{},INSTITUTE_ENTITY:"institute",loadDiscountStructureConfigurationPageWithoutSession:function(){ajaxClient.get("/fees/discount-structure-without-session",function(e){$("#main-content").html(e),discountStructure.initDataCache(),academicSessionHandler.bindSessionChangeEvent(discountStructure.changeSession)})},loadDiscountStructureConfigurationPage:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/discount-structure/"+e,function(e){$("#configured-discount-structure-list").html(e),discountStructure.initDataCache()})},changeSession:function(){discountStructure.loadDiscountStructureConfigurationPage()},initDataCache:function(){for(var e=readJson("#fee-data"),t=readJson("#discount-standards"),a=readJson("#discount-structures"),n={},i={},o={},s={},d={},l=e.feeConfigurationBasicInfoList,r=e.feeHeadConfigurationResponseList,c=0;c<l.length;c++){var u=l[c];o[u.feeId]=u;var f=discountStructure.createFeeHeadSelectMenu(r,"");d[u.feeId]=f}for(c=0;c<r.length;c++){var m=r[c];s[m.feeHeadConfiguration.feeHeadId]=m}for(c=0;c<t.length;c++){var p=t[c];n[p.standardId]=p}for(c=0;c<a.length;c++){var g=a[c];i[g.feeDiscountMetadata.discountStructureId]=g}discountStructure.dataCache={},discountStructure.dataCache.discountStructureMap=i,discountStructure.dataCache.standards=t,discountStructure.dataCache.standardsMap=n,discountStructure.dataCache.feeMap=o,discountStructure.dataCache.feeHeadMap=s,discountStructure.dataCache.feeHeadConfigurationResponseList=r,discountStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},discountStructure.dataCache.feeIdFeeHeadSelectOptionData=d},createFeeHeadSelectMenu:function(e,t){for(var a='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',n=0;n<e.length;n++){var i=e[n].feeHeadConfiguration.feeHeadId,o=e[n].feeHeadConfiguration.feeHead;a+=t==i?'<option value="'+i+'" selected>'+o+"</option>":'<option value="'+i+'">'+o+"</option>"}return a+="</select>"},loadNewFeeStructureConfigureModal:function(){discountStructure.resetDiscountStructureModal(),$("#discount-structure-config-modal").find(".modal-title").html("Configure New Discount Structure"),$("#submit-fee-structure").html("Configure New Discount Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={},discountStructure.dataCache.configureNewStructure.standard.selectedStandards={},discountStructure.dataCache.configureNewStructure.standard.selectedFees={},discountStructure.dataCache.selectedStructureId=null,discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees),discountStructure.populateStandardSelectDropdown()},resetDiscountStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text pt-7 pb-7"> Select fees to add new discount structure</p>'),$("#discount-structure-name").val(""),$("#discount-structure-name").attr("disabled",!1),$("#discount-structure-config-modal").find(".modal-footer").css("display","")},populateSelectFeesDropdown:function(e,t,a){var n=discountStructure.dataCache.feeMap,i="";for(feeId in n)feeId in a||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+n[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),discountStructure.bindSelectFeesEvent(e,t,a)},populateStandardSelectDropdown:function(){var e=discountStructure.dataCache.standardsMap,t="";for(standardId in e)standardId in discountStructure.dataCache.configureNewStructure.standard.selectedStandards||(t+='<a class="dropdown-item standard-select-option" href="#" id="'+standardId+'-select">'+e[standardId].standardName+"</a>");$(".class-fee-structure").find(".standard-select-dropdown").html(t),discountStructure.bindStandardSelectEvent()},bindSelectFeesEvent:function(e,t,a){$(e).find(".fee-select-option").on("click",function(t){t.stopPropagation();var n=$(this).attr("id").split("-select-")[0],i=$(this).attr("id").split("-select-")[1];discountStructure.insertFeeDetailsCard(e,i,n,a)})},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var e=$(this).attr("id").split("-select")[0];discountStructure.insertStandardDetailsCard(e)})},insertFeeDetailsCard:function(e,t,a,n){n[a]=!0,$(e).find(".institute-add-structure-hint-text").attr("style","display:none;");var i=discountStructure.createEntityContainerId(t),o=discountStructure.createFeeContainerId(t,a),s='<div class="fee-structure"> <div class="card card-border text-center" id="'+o+'"> <p class="fee-id" style="display:none;">'+a+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+discountStructure.dataCache.feeMap[a].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col">Percentage</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+i).append(s),discountStructure.bindFeeContainerEvents(e,o,t,a,n),discountStructure.populateSelectFeesDropdown(e,t,n)},insertStandardDetailsCard:function(e){discountStructure.dataCache.configureNewStructure.standard.selectedStandards[e]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var t=discountStructure.dataCache.standardsMap[e].standardName.toUpperCase(),a=discountStructure.getStandardContainerId(e),n='<div id="'+a+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+e+'</p> <div class="card-header card-header-color" id="'+e+'-heading" data-toggle="collapse" data-target="#collapse-'+e+'" aria-expanded="true" aria-controls="collapse-'+e+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+t+'</strong> </button> </h2> </div> <div id="collapse-'+e+'" class="collapse" aria-labelledby="'+e+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+e+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(n),discountStructure.dataCache.configureNewStructure.standard.selectedFees[e]={},discountStructure.populateSelectFeesDropdown("#"+a,e,discountStructure.dataCache.configureNewStructure.standard.selectedFees[e]),discountStructure.bindStandardContainerEvents(a,e),discountStructure.populateStandardSelectDropdown()},createEntityContainerId:function(e){return e+"-fee-structure-container"},createFeeContainerId:function(e,t){return e+"-"+t},bindFeeContainerEvents:function(e,t,a,n,i){$("#"+t).find(".add-fee-head-button").on("click",function(){discountStructure.insertFeeHeadRow(t,n,null,0,"")}),$("#"+t).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete i[n],isEmpty(i)&&$(e).find(".institute-add-structure-hint-text").attr("style","display:block;"),discountStructure.populateSelectFeesDropdown(e,a,i)})},populateSelectFeesDropdown:function(e,t,a){var n=discountStructure.dataCache.feeMap,i="";for(feeId in n)feeId in a||(i+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+t+'">'+n[feeId].feeName+"</a>");$(e).find(".fee-select-dropdown").html(i),discountStructure.bindSelectFeesEvent(e,t,a)},getStandardContainerId:function(e){return e+"-container"},bindStandardContainerEvents:function(e,t){$("#"+e).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete discountStructure.dataCache.configureNewStructure.standard.selectedStandards[t],isEmpty(discountStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;"),discountStructure.populateStandardSelectDropdown()})},insertFeeHeadRow:function(e,t,a,n,i){var o="";if(null==a){o='<tr id=""> <td> '+discountStructure.dataCache.feeIdFeeHeadSelectOptionData[t]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"> <span class="slider round"></span> </label> </td><td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var s=discountStructure.dataCache.feeHeadConfigurationResponseList;o='<tr id=""> <td> '+discountStructure.createFeeHeadSelectMenu(s,a)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+n+'"> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"'+(i=i?"checked":"")+' /><span class="slider round"></span> </label> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+e).find(".fee-structure-body").append(o),discountStructure.bindFeeHeadRowEvents(e)},bindFeeHeadRowEvents:function(e){var t="";$("#"+e).find(".fee-head").on("focus",function(){t=$(this).find(":selected").val().trim()}).change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var a=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();""!=t&&e==t&&a++}),a>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(t))}}),$("#"+e).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()}),$("#"+e).find(".fee-head-amount").change(function(){if($(this).parent().parent().find(".is-percentage-switch").is(":checked")){var e=$(this).val();if(e>100||e<=0)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).val("")}else{var t=$(this).val();if(t<=0&&""!=t)return showErrorDialogBox("Amount should be a positive number."),void $(this).val("")}}),$("#"+e).find(".is-percentage-switch").change(function(){if($(this).is(":checked")){var e=$(this).parent().parent().parent().find(".fee-head-amount").val();if((e>100||e<=0)&&""!=e)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).parent().parent().parent().find(".fee-head-amount").val("")}})},submitDiscountStructure:function(){if(validateMandatoryFields($("#discount-structure-config-modal")))showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.","#discount-structure-config-modal");else{$("#discount-structure-config-modal").modal("toggle");var e=$("#discount-structure-name").val(),t=academicSessionHandler.getSelectedSessionId(),a=null;null!=discountStructure.dataCache.selectedStructureId&&""!=discountStructure.dataCache.selectedStructureId.trim()&&(a=discountStructure.dataCache.selectedStructureId);var n=discountStructure.getFeeStructureData("#institute-fee-structure-container");if(!Array.isArray(n)||n.length){var i={feeEntity:"INSTITUTE",feeIdFeeHeadsList:n},o=[];o.push(i);var s=[{structureId:a,structureName:e,discountStructureType:"CUSTOM",metadata:{title:e},entityFeeAssignmentPayloads:o}],d="/fees/add-discount-structure/"+t;null!=a&&(d="/fees/update-discount-structure/"+a+"/"+t),ajaxClient.post(d,{discountAssignmentStructurePayload:JSON.stringify(s)},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),discountStructure.loadDiscountStructureConfigurationPage()})}}},getFeeStructureData:function(e){var t=[];return $(e).find(".fee-structure").each(function(){var e=$(this).find(".fee-id").text().trim(),a=[],n={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();if(""!=e&&!(e in n)){var t=$(this).find(".fee-head-amount").val(),i=$(this).find(".toggle-switch").is(":checked");if(""===t||void 0===t)return showErrorDialogBoxWithExistingModalDetails("Amount cannot be empty","#discount-structure-config-modal"),[];a.push({feeHeadId:e,amount:t,isPercentage:i})}}),a.length>0&&t.push({feeId:e,feeHeadAmountList:a})}),Array.isArray(t)&&!t.length?(showErrorDialogBoxWithExistingModalDetails("Please select atleast one fees to create discount structure","#discount-structure-config-modal"),[]):t},fillFeeDiscountStructureModal:function(e,t){$("#discount-structure-name").attr("disabled",!1),discountStructure.dataCache.selectedStructureId=e;var a=discountStructure.dataCache.discountStructureMap;if(e in a){var n=a[e];discountStructure.resetDiscountStructureModal(),$("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName()),$("#discount-structure-name").val(n.feeDiscountMetadata.name),$("#discount-structure-config-modal").find(".modal-title").html("Update Discount Structure"),$("#submit-discount-structure").html("Update Discount Structure"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={},discountStructure.dataCache.configureNewStructure.standard.selectedStandards={},discountStructure.dataCache.configureNewStructure.standard.selectedFees={};for(var i={},o={},s=0;s<n.feeDiscountEntityStructures.length;s++){var d=n.feeDiscountEntityStructures[s];if("INSTITUTE"==d.feeEntity)for(var l=0;l<d.feeIdDiscountStructures.length;l++){i[u=(c=d.feeIdDiscountStructures[l]).feeConfigurationBasicInfo.feeId]=c}else if("CLASS"==d.feeEntity){var r=d.entityId;o[r]={};for(l=0;l<d.feeIdDiscountStructures.length;l++){var c,u=(c=d.feeIdDiscountStructures[l]).feeConfigurationBasicInfo.feeId;o[r][u]=c}}}for(u in i){discountStructure.insertFeeDetailsCard(".institute-structure",discountStructure.INSTITUTE_ENTITY,u,discountStructure.dataCache.configureNewStructure.institute.selectedFees);var f=discountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,u);for(s=0;s<i[u].feeHeadDiscountStructures.length;s++){var m=i[u].feeHeadDiscountStructures[s].feeHeadConfiguration.feeHeadId,p=i[u].feeHeadDiscountStructures[s].amount,g=i[u].feeHeadDiscountStructures[s].percent;discountStructure.insertFeeHeadRow(f,u,m,p,g)}}for(r in o){discountStructure.insertStandardDetailsCard(r);var h=discountStructure.getStandardContainerId(r);for(u in o[r]){discountStructure.insertFeeDetailsCard("#"+h,r,u,discountStructure.dataCache.configureNewStructure.standard.selectedFees[r]);for(f=discountStructure.createFeeContainerId(r,u),s=0;s<o[r][u].feeHeadDiscountStructures.length;s++){m=o[r][u].feeHeadDiscountStructures[s].feeHeadConfiguration.feeHeadId,p=o[r][u].feeHeadDiscountStructures[s].amount,g=i[u].feeHeadDiscountStructures[s].percent;discountStructure.insertFeeHeadRow(f,u,m,p,g)}}}discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees),discountStructure.populateStandardSelectDropdown(),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".toggle-switch").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#discount-structure-config-modal").find(".modal-title").html("View Discount Structure"),$("#discount-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#discount-structure-config-modal").find(".modal-footer").css("display","none"))}else showErrorDialogBox("Invalid Fee Discount Structure.")},populateDeleteStructureModal:function(e){var t=discountStructure.dataCache.discountStructureMap[e];discountStructure.dataCache.discountStructureID=e,$("#delete-fee-structure-modal-text").html("Do you want to delete discount Structure : "+t.feeDiscountMetadata.name+"?")},deleteFeeStructure:function(){$("#delete-fee-structure-modal").modal("toggle");var e=discountStructure.dataCache.discountStructureID,t=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/fees/delete-discount-structure/"+e+"/"+t,{},function(e){$("#discount-structure-status-modal-container").html(e),$("#fee-structure-status-modal").modal("toggle"),discountStructure.loadDiscountStructureConfigurationPage()})}},graphLoadInterval=100;function getFeesHomePageGraphDetails(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/home-graph/"+e,function(e){$("#home-page-graph-area").html(e),drawHomeCharts()})}function graphReadyToRender(){graphLibraryLoaded=!0}function drawHomeCharts(){displayLoader();drawPieChart(),hideLoader()}function drawPieChart(){if(!graphLibraryLoaded)return clearTimeout(graphLoadTimer),void(typingTimer=setTimeout(function(){drawPieChart()},graphLoadInterval));var e=$("#home-page-stats").text().trim(),t=JSON.parse(e),a=google.visualization.arrayToDataTable([["Amount Type","Value"],["Total Collected Fees",t.collectedAmount],["Total Discount Fees",t.discountAmount],["Total Due Fees",t.dueAmount]]);t.classFeePaymentAggregatedDatas.length>0&&new google.visualization.PieChart(document.getElementById("piechart_3d")).draw(a,{title:"Total Fees Summary",is3D:!0})}var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1,PAST_DAYS=1825;$(document).ready(function(){menuLoader.registerSidebarMenu();var e=new URLSearchParams(window.location.search);if(actionType=e.get(ACTION_TYPE),null!=actionType&&null!=actionType&&""!=actionType){"payments"===actionType?($("#paymentNav").parent().addClass("active"),loadPaymentPage()):"send-reminders"===actionType&&($("#sendReminderNav").parent().addClass("active"),reminders.loadSendReminderPage());var t=window.location.href;url=removeURLParameter(t,ACTION_TYPE);let e={id:"100"};window.history.replaceState(e,t,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerFeeConfigurationMenu(),menuLoader.registerFeeStructureConfigurationMenu(),menuLoader.registerDiscountStructureConfigurationMenu(),menuLoader.registerFeeHeadConfigurationMenu(),menuLoader.registerFeeAssignmentMenu(),menuLoader.registerDiscountAssignmentMenu(),menuLoader.registerPaymentMenu(),menuLoader.registerPaymentTransactionsMenu(),menuLoader.registerFollowUpMenu(),menuLoader.registerReportsMenu(),menuLoader.registerSendReminderMenu(),menuLoader.registerNotificationHistoryMenu()},registerHomeMenu:function(){$("#statisticsNav").on("click",function(){statistics.loadHomePage()})},registerFeeConfigurationMenu:function(){$("#feeConfigurationNav").on("click",function(){loadFeeConfigurationPage()})},registerFeeHeadConfigurationMenu:function(){$("#feeHeadConfigNav").on("click",function(){loadFeeHeadConfigurationPage()})},registerFeeAssignmentMenu:function(){$("#feeAssignmentNav").on("click",function(){feeAssignment.loadMainScreen()})},registerDiscountAssignmentMenu:function(){$("#discountAssignmentNav").on("click",function(){discountAssignment.loadDiscountAssignmentPage()})},registerPaymentMenu:function(){$("#paymentNav").on("click",function(){loadPaymentPage()})},registerSendReminderMenu:function(){$("#sendReminderNav").on("click",function(){reminders.loadSendReminderPage()})},registerNotificationHistoryMenu:function(){$("#notificationHistoryNav").on("click",function(){reminders.loadNotificationHistoryHomePage()})},registerPaymentTransactionsMenu:function(){$("#transactionsNav").on("click",function(){loadPaymentTransactionsMenu()})},registerFollowUpMenu:function(){$("#followupsNav").on("click",function(){loadfollowUpsMenu()})},registerReportsMenu:function(){$("#reportsNav").on("click",function(){feeReports.loadReportsMenu()})},registerFeeStructureConfigurationMenu:function(){$("#feeStructureConfigurationNav").on("click",function(){feeStructure.loadFeeStructureConfigurationPageWithoutSession()})},registerDiscountStructureConfigurationMenu:function(){$("#discountStructureConfigurationNav").on("click",function(){discountStructure.loadDiscountStructureConfigurationPageWithoutSession()})}},statistics={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(statistics.loadHomePageForSession),statistics.displayDashboardContent(),statistics.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip(),statistics.onClickFeeStatsType()},onClickFeeStatsType:function(){$("#fee-stats-type").change(function(){"TODAY"===$(this).val()?($("#fees-stats-date").attr("style","display:inline-block;"),statistics.loadHomePageForDate()):($("#fees-stats-date").attr("style","display:none;"),statistics.loadHomePageForSession())}),$("input#fees-stats-date").daterangepicker({autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},onSelect:function(e){$(this).change()}}).on("change",function(){statistics.loadHomePageForDate()})},onClickFeeStatsSwitchButton:function(){$("#fee-stats-switch-button").on("click",function(){$("#current-card-view-type").text()===TABULAR?($("#current-card-view-type").text(GRAPHICAL),$("#bar-graph-header-text").text("Class Fee Distribution (Graphical View)"),$(this).html("Tabular View"),statistics.loadStudentCountChart(),$("#tabularview-class-fee-distribution").attr("style","display:none;"),$("#chartjs-class-fee-distribution").attr("style","display:block;")):($("#current-card-view-type").text(TABULAR),$("#bar-graph-header-text").text("Class Fee Distribution (Tabular View)"),$(this).html("Graphical View"),statistics.loadBarGraphTabularView(),$("#chartjs-class-fee-distribution").attr("style","display:none;"),$("#tabularview-class-fee-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;"))})},loadHomePage:function(){ajaxClient.get("/fees/home",function(e){$("#main-content").html(e),statistics.initHomePage(),$("#fee-stats-type").val("SESSION")})},loadHomePageForSession:function(){$("#fees-stats-date").attr("style","display:none;");var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/session-home/"+e,function(e){$("#fees-dashboard-session-content").html(e),statistics.displayDashboardContent(),statistics.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip(),$("#fee-stats-type").val("SESSION")})},loadHomePageForDate:function(){var e=academicSessionHandler.getSelectedSessionId(),t=getDate($("input#fees-stats-date").val()).getTime()/1e3;ajaxClient.get("/fees/date-wise-fee-stats/"+e+"/"+t,function(e){$("#fees-dashboard-session-content").html(e),statistics.displayDashboardContent(),statistics.onClickFeeStatsSwitchButton(),$('[data-toggle="tooltip"]').tooltip()})},refreshHomePage:function(){statistics.loadHomePageForSession()},loadBarGraphTabularView:function(){var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){for(var t=[],a=[],n=[],i=[],o=[],s=0;s<e.classFeePaymentAggregatedDatas.length;s++){var d=e.classFeePaymentAggregatedDatas[s];t.push(d.standardName),a.push(d.assignedAmount),n.push(d.collectedAmount),i.push(d.discountAmount),o.push(d.dueAmount)}statistics.renderBarGraphTabularView(t,a,n,i,o)}},renderBarGraphTabularView:function(e,t,a,n,i){for(var o='<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Class</th><th scope="col">Total ('+RUPEE_SYMBOL+')</th><th scope="col">Collected ('+RUPEE_SYMBOL+')</th><th scope="col">Discounted ('+RUPEE_SYMBOL+')</th><th scope="col">Due ('+RUPEE_SYMBOL+")</th></tr></thead><tbody>",s=0,d=0,l=0,r=0,c=0;c<e.length;c++){var u=e[c],f=formatINRCurrency(parseFloat(t[c].toFixed(2)));s+=parseFloat(t[c].toFixed(2));var m=formatINRCurrency(parseFloat(a[c].toFixed(2)));d+=parseFloat(a[c].toFixed(2));var p=formatINRCurrency(parseFloat(n[c].toFixed(2)));l+=parseFloat(n[c].toFixed(2));var g=formatINRCurrency(parseFloat(i[c].toFixed(2)));r+=parseFloat(i[c].toFixed(2)),o+='<tr><th scope="row">'+u+"</th><td>"+f+"</td><td>"+m+"</td><td>"+p+"</td><td>"+g+"</td></tr>"}o+='<tr><th scope="row">Grand Total</th><td><b style="font-weight:bold">'+s+'</b></td><td><b style="font-weight:bold">'+d+'</b></td><td><b style="font-weight:bold">'+l+'</b></td><td><b style="font-weight:bold">'+r+"</b></td><tr>",o+="</tbody></table>",$("#tabularview-class-fee-distribution").html(o)},displayDashboardContent:function(){statistics.loadStudentCountChart();var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){e.assignedAmount;var t=e.collectedAmount/e.assignedAmount*100,a=e.discountAmount/e.assignedAmount*100,n=e.dueAmount/e.assignedAmount*100,i=[t.toFixed(2),a.toFixed(2),n.toFixed(2)];statistics.renderFeeCollectionPieChart(["","",""],i)}},loadStudentCountChart:function(){var e=readJson("#home-page-fees-stats");if(null!=e&&null!=e.classFeePaymentAggregatedDatas){for(var t=[],a=[],n=[],i=[],o=[],s=0;s<e.classFeePaymentAggregatedDatas.length;s++){var d=e.classFeePaymentAggregatedDatas[s];t.push(d.standardName),a.push(d.assignedAmount),n.push(d.collectedAmount),i.push(d.discountAmount),o.push(d.dueAmount)}statistics.renderStudentCountChart(t,a,n,i,o)}},renderStudentCountChart:function(e,t,a,n,i){new Chart($("#chartjs-class-fee-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:i,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeeCollectionPieChart:function(e,t){new Chart($("#chartjs-fee-collection-pie"),{type:"pie",data:{datasets:[{data:t,backgroundColor:[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,"#108F2B","#C08143 ","#DCDF69","#719E90","#A8B9DF ","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"],borderWidth:5,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cutoutPercentage:70,legend:{display:!1},tooltips:{enabled:!0,mode:"single",displayColors:!1,callbacks:{label:function(e,t){return" "+t.datasets[0].data[e.index]+"% "}}}}})}};function loadfollowUpsMenu(){ajaxClient.get("/fees/fee-followup-home",function(e){$("#main-content").html(e),getFeeFollowUpDetails(!0,"ACTIVE"),initDate(1825),initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3)})}function loadPaymentTransactionsMenu(){ajaxClient.get("/fees/all-payment-transactions-home",function(e){$("#main-content").html(e),getPaymentTransactions(!0,"ACTIVE"),feeTransaction.dataCache.status="ACTIVE",academicSessionHandler.bindSessionChangeEvent(loadPaymentTransactionsForSession),feeTransactionSearchCallback(),bindPaymentTransactionClickEvents(),initDate(1825)})}function feeTransactionSearchCallback(){$("#searchTransactions").on("click",function(){getPaymentTransactions(!0,"ACTIVE")}),$("#searchTransactionsInput").on("keyup",function(e){13==e.keyCode&&getPaymentTransactions(!0,"ACTIVE")})}function getPaymentTransactions(e,t){feeTransaction.dataCache.status=t;var a=$("#searchTransactionsInput").val(),n=$(".page-item.active").find(".page-number").text().trim();e&&(n=1);var i=$("#items-per-page").val();null!=n&&""!=n||(n=1);var o=(n-1)*i,s=academicSessionHandler.getSelectedSessionId();$("#searchTransactionsResult").html(""),ajaxClient.get("/fees/payment-transactions/session/"+s+"/"+t+"/"+o+"/"+i+"?text="+a,function(e){$("#searchTransactionsResult").html(e);var a=$("#sidebar").height()-$("#searchTransactionsResult").position().top-300;$("#fee-payment-transactions-fixed-height-list-wrapper").attr("style","height:"+a+"px;  overflow-y: scroll; cursor: pointer;");var i=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(i.itemsPerPage),$(".page-item").removeClass("active");i.offset,i.itemsPerPage;$("#page-number-"+n).addClass("active"),"CANCELLED"==t&&($("#nav-cancel-transaction-tab").addClass("active"),$("#nav-active-transaction-tab").removeClass("active"),$("#cancel-transaction-content").addClass("show active"),$("#active-transaction-content").removeClass("show active")),bindPaymentTransactionClickEvents(),feeTransaction.initPagination()})}function getFeeFollowUpDetails(e,t){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-fee-follow-up-details/"+a,function(e){$("#feeFollowUpResult").html(e),$(".datatables-reponsive").DataTable({searching:!0,bPaginate:!0,ordering:!1})})}function closeTransactionFeeFollowUpDetails(e){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Close All Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong style="color:red">Are you sure that you want to close the all followup trail for this student ?</strong>'),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="closeStudentTransactionFeeFollowupTrail(\'true\',\'INACTIVE\')">Yes, Close All</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("#fee-follow-up-close-modal").modal("toggle")}function closeStudentTransactionFeeFollowupTrail(e,t){var a=academicSessionHandler.getSelectedSessionId(),n=$("#delete-student-follow-up-id").text().trim();""!=n?($("#fee-follow-up-close-modal").modal("toggle"),ajaxClient.post("/fees/close-fee-followup-trail/"+a+"/"+e+"/"+t+"/"+n,{},function(e){$("#fee-followup-trail-transaction-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentFollowUpDetails(n)})):showErrorDialogBox("Invalid Follow Up to cancel")}function addFeeTransactionFollowUpDetails(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),$("p#student-follow-up-id").text(e),$("#next-transaction-fee-follow-up-date").val(""),$("#add-fee-transaction-follow-up-modal").modal("toggle")}function backButton(){getFeeFollowUpDetails()}function closeFeeTransactionFollowUpDetails(e,t,a){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Close Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html("<strong>Are you sure that you want to close this followup trail ?</strong>"),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="closeStudentTransactionSingleFeeFollowupTrail(\'false\',\'INACTIVE\')">Yes, Close It</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#fee-follow-up-close-modal").modal("toggle")}function closeStudentTransactionSingleFeeFollowupTrail(e,t){var a=academicSessionHandler.getSelectedSessionId(),n=$("#delete-student-follow-up-id").text().trim(),i=$("#student-follow-up-id").text().trim();""!=n?($("#fee-follow-up-close-modal").modal("toggle"),ajaxClient.post("/fees/close-fee-followup-trail/"+a+"/"+e+"/"+t+"/"+n,{},function(e){$("#fee-followup-trail-transaction-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentFollowUpDetails(i)})):showErrorDialogBox("Invalid Follow Up to cancel")}function updateStudentTransactionFeeFollowupTrail(e){if(!validateMandatoryFields($("#update-transaction-fee-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#update-transaction-entity-name").val(),n=getDate($("#update-transaction-fee-follow-up-date").val());if(null!=n){n=n.getTime()/1e3;var i=$("#update-transaction-contact-person-name").val(),o=$("select#update-transaction-follow-up-type").val(),s=$("#update-transaction-conversation").val(),d=$("#update-transaction-next-fee-follow-up-date").val();if(""!=d&&(d=(d=getDate($("#update-transaction-next-fee-follow-up-date").val())).getTime()/1e3),""==d&&(d=null),null!=d&&n>d)return $("#update-transaction-fee-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#update-transaction-amount").val(),r=e,c=$("#update-student-follow-up-id").text(),u=$("#student-id").text(),f={entityId:u,followUpId:c,entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#update-transaction-fee-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/update-fee-followup-trail/"+t,{updateFollowUpPayload:JSON.stringify(f)},function(e){$("#fee-followup-trail-transaction-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentFollowUpDetails(u)})}else showErrorDialogBox("Please fill follow up date.")}}function deleteTransactionFeeFollowUpDetails(e,t){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Delete Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong color="red">Are you sure that you want to Delete this followup trail ?</strong>'),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="deleteStudentTransactionFeeFollowupTrail()">Yes, Delete</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#fee-follow-up-close-modal").modal("toggle")}function deleteStudentTransactionFeeFollowupTrail(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete-student-follow-up-id").text().trim(),a=$("#student-follow-up-id").text().trim();""!=t?($("#fee-follow-up-close-modal").modal("toggle"),ajaxClient.post("/fees/delete-fee-followup-trail/"+e+"/"+t,{},function(e){returnToPaymentMainScreenDisplay(),$("#fee-followup-trail-transaction-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentFollowUpDetails(a)})):showErrorDialogBox("Invalid Follow Up to cancel")}function editTransactionFeeFollowUpDetails(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3);var t=JSON.parse($(e).parent().find("#follow-up-json").text().trim());$("#update-transaction-entity-name").html("<option>"+t.entityName+"</option>"),$("#update-transaction-fee-follow-up-date").val(getFormattedDate(t.followUpDate)),$("#update-transaction-follow-up-mode").html("<option>"+t.followUpMode+"</option>"),$("#update-transaction-contact-person-name").val(t.contactPersonName),$("#update-transaction-follow-up-type").html("<option>"+t.followUpType+"</option>"),$("#update-transaction-conversation").val(t.conversation),null!=t.nextFollowUpDate?$("#update-transaction-next-fee-follow-up-date").val(getFormattedDate(t.nextFollowUpDate)):$("#update-transaction-next-fee-follow-up-date").val(""),$("#update-transaction-amount").val(t.amount),$("#update-student-follow-up-id").text(t.followUpId),$("#student-id").text(t.entityId),$("#update-transaction-fee-follow-up-modal").modal("toggle")}function addTransactionStudentFeeFollowupTrail(e){if(!validateMandatoryFields($("#add-fee-transaction-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#entity-transaction-name").val(),n=getDate($("#fee-transaction-follow-up-date").val());if(null!=n){n=n.getTime()/1e3;var i=$("#contact-person-transaction-name").val(),o=$("select#follow-up-transaction-type").val(),s=$("#transaction-conversation").val(),d=getDate($("#next-transaction-fee-follow-up-date").val());if(null!=d&&(d=d.getTime()/1e3),""==d&&(d=null),null!=d&&n>d)return $("#add-fee-transaction-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#amount-transaction").val(),r=e,c=$("#student-follow-up-id").text(),u={entityId:c,entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#add-fee-transaction-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/add-fee-followup-trail/"+t,{followUpPayload:JSON.stringify(u)},function(e){$("#fee-followup-trail-transaction-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentFollowUpDetails(c)})}else showErrorDialogBox("Please fill follow up date.")}}function loadStudentFollowUpDetails(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-follow-up-details/"+t+"/"+e,function(e){$("#feeFollowUpResult").html(e)})}function loadPaymentTransactionsForSession(){getPaymentTransactions(!0,"ACTIVE")}function bindPaymentTransactionClickEvents(){$(".view-payment-transaction").on("click",function(){showPaymentDetails($(this).attr("id").trim())}),$(".invoice-payment-transaction").on("click",function(){generateFeePaymentInvoice($(this).attr("id").trim())}),$(".print-payment-transaction").on("click",function(){printFeePaymentInvoice($(this).attr("id").trim())}),$(".cancel-payment-transaction").on("click",function(){showCancelPaymentDetails($(this).attr("id").trim())})}function generateFeePaymentInvoice(e){window.open(baseURL+"/fees/invoice/"+e+"/pdf-summary","_blank")}function generateBulkFeePaymentInvoice(e){var t=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/fees/bulk-invoice/pdf-summary/"+t+"?transaction_ids_str="+e,"_blank")}function printFeePaymentInvoice(e){printJS({printable:baseURL+"/fees/invoice/"+e+"/pdf-summary",type:"pdf",showModal:!0})}function printBulkFeePaymentInvoice(e){var t=academicSessionHandler.getSelectedSessionId();printJS({printable:baseURL+"/fees/bulk-invoice/pdf-summary/"+t+"?transaction_ids_str="+e,type:"pdf",showModal:!0})}function generateWalletInvoice(e){window.open(baseURL+"/fees/wallet-invoice/"+e+"/pdf","_blank")}function showPaymentDetails(e){ajaxClient.get("/fees/payment-transaction-details/"+e,function(e){$("#fee-payment-transaction-detail-modal-container").html(e),$("#confirm-cancel-transaction-button").remove(),$("#fee-payment-transaction-view-modal").modal("toggle")})}function showCancelPaymentDetails(e){ajaxClient.get("/fees/payment-transaction-details/"+e,function(e){$("#fee-payment-transaction-detail-modal-container").html(e),$("#confirm-cancel-transaction-button").attr("style","display:block"),$("#fee-payment-transaction-view-modal").modal("toggle")})}function cancelTransaction(){var e=$("#cancel-transaction-id").text().trim();if(""!=e){var t={instituteId:null,transactionId:e,cancelBy:null,cancelAt:(new Date).getTime()/1e3,cancelRemarks:$("#payment-cancel-remarks").val().trim()};$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),ajaxClient.post("/fees/cancel-payment-transaction",{feeCancelledPayload:JSON.stringify(t)},function(e){$("#fee-payment-status-modal-container").html(e),$("#fee-payment-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Invalid Transaction to cancel")}function sendEmails(e){baseURL2=baseURL,"http://127.0.0.1:9123"==baseURL&&(baseURL2="http://127.0.0.1"),ajaxClient.get(baseURL2+":8080/data-server/2.0/fee-payment/due-payment-emails/"+e,function(e){})}function bindDiscountInfoViewEvents(){}function showViewDiscount(){for(var e=$("tbody tr td p").text(),t=JSON.parse(e),a="None",n=0;n<t.assigned_discounts.length;n++)"None"===a?(a="",a+=t.assigned_discounts[n].discountBasicInfo.discountName):a+=", "+t.assigned_discounts[n].discountBasicInfo.discountName;$("#search-student-class-section").html(a)}function bindDiscountViewEvents(){$(".view-discount").on("click",function(){for(var e=$(this).parent().parent().find(".discount-assign").text(),t=JSON.parse(e),a='<table class="table borderless"><thead><tr><th scope="col" class="w-25">Discount Name</th><th scope="col" >Fee Head</th><th scope="col" >Amount</th><th scope="col" >Percentage</th></tr></thead><tbody>',n=0;n<t.assigned_discounts.length;n++){a+='<tr class="row-discount"><td id="discount-name">'+t.assigned_discounts[n].discountBasicInfo.discountName+"</td>";for(var i=!0,o=0;o<t.assigned_discounts[n].discountAmountFeeHeadResponseList.length;o++){var s=t.assigned_discounts[n].discountAmountFeeHeadResponseList[o].feeHeadConfiguration.feeHead,d=t.assigned_discounts[n].discountAmountFeeHeadResponseList[o].amount,l="";1==t.assigned_discounts[n].discountAmountFeeHeadResponseList[o].percentage&&(l="checked"),a+=i?'<td id="fee-head-name">'+s+'</td><td id="amount">'+d+'</td><td id="percentage"><label class="switch"> <input type="checkbox" class="primary toggle-switch" '+l+' disabled ><span class="slider round"></span></label></td></tr>':'<tr class="row-discount"><td></td><td id="fee-head-name">'+s+'</td><td id="amount">'+d+'</td><td id="percentage"><label class="switch"> <input type="checkbox" class="primary toggle-switch" '+l+' disabled ><span class="slider round"></span></label></td></tr>',i=!1}}a+="</tbody></table>",$("#view-popup").html(a),$("#view-discount-modal").modal("toggle")})}function deleteDiscountCallBack(){$(".delete-discount-info").on("click",function(){var e=$(this).parent().find(".discount-info").text().trim(),t=JSON.parse(e);$("#delete\\.discount-id").val(t.discountBasicInfo.discountId),$("#delete\\.discount-name").val(t.discountBasicInfo.discountName),$("#delete-discount-info-modal").modal("toggle")})}function deleteDiscount(){$("#delete-discount-info-modal").modal("toggle");var e=$("#delete\\.discount-id").val();ajaxClient.post("/fees/delete-discount-configuration/"+e,{},function(e){returnToMainScreen(),$("#discount\\.status-modal-container").html(e),$("#discount\\.status-modal").modal("toggle"),getAllDiscounts()})}function populateViewFeeAssignmentContent(e){$("#view\\.assigned-fee-name").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.feeName),$("#view\\.assigned-fee-academic-session").val(e.feeConfigurationResponse.academicSession.displayName),$("#view\\.assigned-fee-description").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.description),"ONE_TIME"!=e.feeConfigurationResponse.feeConfigurationBasicInfo.feeType?$("#view\\.assigned-fee-duration").val(e.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month+" to "+e.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month):$("#assigned-fee-duration-container").attr("style","display:none");var t="",a=0,n=0;for(a=0;a<e.feeHeadAmountDetailsList.length;a++)n+=e.feeHeadAmountDetailsList[a].amount,t=t+"<tr> <td>"+e.feeHeadAmountDetailsList[a].feeHeadConfiguration.feeHead+"</td> <td>"+e.feeHeadAmountDetailsList[a].amount+'</td><td><span class="badge badge-logo-primary">'+e.feeHeadAmountDetailsList[a].feeEntity+"</span></td></tr>";t=t+"<tr> <td> <strong> TOTAL </strong></td> <td>"+n+"</td> <td></td></tr>",$("#assigned-fee-head-amounts").html(t),$("#view-assigned-fee-modal").modal("toggle")}function studentLiveSearchEvent(e,t,a,n,i){ajaxClient.get("/fees/student-live-search/"+e+"?searchText="+t+"&status="+i,function(e){$(a).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(a,n)})}var feeTransaction={dataCache:{},initPagination:function(){pagination.bindEvents(function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!1,feeTransaction.dataCache.status)},function(){getPaymentTransactions(!0,feeTransaction.dataCache.status)})},downloadBulkInvoice:function(){if(!validateMandatoryFields($("#download-bulk-invoice-modal"))){var e=$(".academic-session option:selected").val(),t=getDate($("#download-bulk-invoice-modal").find("input.start-date").val()).getTime()/1e3,a=getDate($("#download-bulk-invoice-modal").find("input.end-date").val()).getTime()/1e3,n=$("#download-bulk-invoice-modal").find("#fee-transaction-status").val(),i=(a-t)/86400;i<0||i>=65?alert("Total number of days cannot be greater than 65. Please reduce the range!"):($("#download-bulk-invoice-modal").modal("toggle"),window.open(baseURL+"/fees/bulk-invoice-pdf?academic_session_id="+e+"&start_date="+t+"&end_date="+a+"&fee_transaction_status="+n,"_blank"))}}};function formatINRCurrency(e){return null==e||null==e||""==e?0:e=e.toLocaleString("en-IN")}var pastFeePaymentTransactionDays=4e3,lastCollectedAmount=0,lastInstantDiscountAmount=0,lastTotalFineAmount=0,lastFeeLevelPaidFineAmount=0;function loadPaymentPage(){ajaxClient.get("/fees/payment",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(feePayment.changeSession),bindPaymentSearchStudentEvent()})}var feePayment={dataCache:{},initCache:function(){var e=readJson("#student-fee-payment-details-json");feePayment.dataCache.studentFeePaymentDetails=e;var t={},a={},n={},i=e.selected_session.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;t[i]=e.selected_session.due_fee_list,a[i]=e.selected_session.student.studentAcademicSessionInfoResponse.academicSession;for(var o=0;o<e.selected_session.due_fee_list.length;o++)n[e.selected_session.due_fee_list[o].feeConfigurationBasicInfo.feeId]=e.selected_session.due_fee_list[o];for(o=0;o<e.other_sessions.length;o++){var s=e.other_sessions[o],d=s.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;t[d]=s.due_fee_list,a[d]=s.student.studentAcademicSessionInfoResponse.academicSession;for(var l=0;l<s.due_fee_list.length;l++)n[s.due_fee_list[l].feeConfigurationBasicInfo.feeId]=s.due_fee_list[l]}feePayment.dataCache.sessionDueFees=t,feePayment.dataCache.sessionData=a,feePayment.dataCache.dueFeeDetailsMap=n},doneStudentSearchTyping:function(e){var t=$("#student-payment-search-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),t,e,loadStudentPaymentDetails,"ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO")},changeSession:function(){$("#student-payment-search-text").val(""),$("#student-payment-screen").html(""),feePayment.dataCache={}},showFeeHeadPaymentDetails:function(e){$(e).hasClass("closed")?($(e).find(".arrow").attr("style","display:none"),$(e).find(".arrow.up").attr("style","display:block"),$(e).removeClass("closed"),$(e).addClass("open")):($(e).find(".arrow").attr("style","display:none"),$(e).find(".arrow.down").attr("style","display:block"),$(e).removeClass("open"),$(e).addClass("closed"))},populateCollectFeesScreen:function(e){var t=feePayment.dataCache.sessionDueFees[e],a=feePayment.dataCache.sessionData[e];$("#collect-fee-session-display").html(a.displayName),$("#collect-fee-session-id").html(e);for(var n="",i=0;i<t.length;i++)n+=' <tr class="collect-fee-row" id="'+t[i].feeConfigurationBasicInfo.feeId+'"> <td>   <div class="form-check">  <input type="checkbox" class="form-check-input collect-fee-enable" >  <label class="form-check-label"> '+t[i].feeConfigurationBasicInfo.feeName+" </label>  </div> </td> </tr>";$("#due-fee-list-container").html(n),$(".select-all-fee-enable").prop("checked",!1),$("#selected-fees-end").siblings().remove(),feePayment.bindFeesForPaymentCheckboxes(),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},collectFeesDisplay:function(e){var t=$("#restrict-session-fees-payment-json").text().trim();if("None"!=t&&null!=t&&JSON.parse(t))return void showErrorDialogBox("Student past session fees are still due, please clear them then take current session payment!");$(".payment-main-screen-info").attr("style","display:none"),$(".fee-payment-tab").attr("style","display:none"),feePayment.populateCollectFeesScreen(e),$("#collect-fee-tab").attr("style","display:block")},bindFeesForPaymentCheckboxes:function(){$(".select-all-fee-enable").change(function(){$(this).is(":checked")?($(".collect-fee-enable").prop("checked",!0),$(".collect-fee-enable").trigger("change")):($(".collect-fee-enable").prop("checked",!1),$(".collect-fee-enable").trigger("change"))}),$(".collect-fee-row").on("click",function(){$(this).find(".collect-fee-enable").is(":checked")?($(this).find(".collect-fee-enable").prop("checked",!1),$(this).find(".collect-fee-enable").trigger("change")):($(this).find(".collect-fee-enable").prop("checked",!0),$(this).find(".collect-fee-enable").trigger("change"))}),$(".collect-fee-enable").on("click",function(){$(this).is(":checked")?($(this).prop("checked",!1),$(this).trigger("change")):($(this).prop("checked",!0),$(this).trigger("change"))}),$(".collect-fee-enable").change(function(){$(this).parent().find("label").text();var e=$(this).closest("tr").attr("id").trim(),t=feePayment.dataCache.dueFeeDetailsMap[e];if($(this).is(":checked"))0==$("#fee-card-"+e).length&&feePayment.insertFeesDetails(t);else{$(".select-all-fee-enable").prop("checked",!1);var a=feePayment.computeVariousTotalFeesAmounts();$("#fee-card-"+e).length>0&&$("#fee-card-"+e).remove(),feePayment.computeFeesPaymentAmounts(),feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay();var n=feePayment.computeVariousTotalFeesAmounts(),i=n.totalCollectedAmount+n.totalFineAmount,o=a.totalCollectedAmount+a.totalFineAmount;if(i<o){var s=o-i,d=feePayment.getPaidAmountValue();if(s<=d)$("#student-paid-amount").val(d-s);else{if(!feePayment.isWalletUsed())return void showErrorDialogBox("Invalid state. Fee amount was not included in paid amount");$("#student-paid-amount").val(0);var l=getUsedWalletAmount()-(s-d);if(l<0)return void showErrorDialogBox("Invalid state. Fee amount was not included in paid amount of wallet");$("#student-used-wallet-amount").val(l)}}}}),$("#student-used-wallet-amount").focus(function(){}).blur(function(){var e=feePayment.getStudentWalletAmount(),t=feePayment.getUsedWalletAmountValue();t>e&&(showErrorDialogBox("Used wallet amount "+t+" cannot be greater then student available wallet amount "+e+". Decreasing used wallet amount"),t=e,$("#student-used-wallet-amount").val(t));var a=feePayment.getPaidAmountValue(),n=t+a,i=feePayment.computeVariousTotalFeesAmounts(),o=i.totalCollectableAmount,s=i.totalFineAmount;n>o?(showErrorDialogBox("Total paid fee amount "+n+" cannot be greater then payble amount "+o+". Reducing the wallet amount."),t=o-a,$("#student-used-wallet-amount").val(t)):n<s&&(showErrorDialogBox("Total paid fee amount "+n+" cannot be less then fine payment amount "+s+". Increasing the wallet amount."),t=s-a,$("#student-used-wallet-amount").val(t)),feePayment.fillCollectedAmounts(n-s)}),$("#student-paid-amount").focus(function(){}).blur(function(){var e=feePayment.getPaidAmountValue(),t=getUsedWalletAmount(),a=t+e,n=feePayment.computeVariousTotalFeesAmounts(),i=n.totalCollectableAmount,o=n.totalFineAmount;a>i?(showErrorDialogBox("Total paid fee amount "+a+" cannot be greater then payble amount "+i+". Reducing the paid amount."),e=i-t,$("#student-paid-amount").val(e)):a<o&&(showErrorDialogBox("Total paid fee amount "+a+" cannot be less then fine payment amount "+o+". Increasing the paid amount."),e=o-t,$("#student-paid-amount").val(e)),feePayment.fillCollectedAmounts(a-o)}),$("#student-total-instant-discount-amount").focus(function(){}).blur(function(){var e=feePayment.computeVariousTotalFeesAmounts(),t=e.totalBalanceAmount-e.totalCollectedAmount,a=feePayment.getTotalInstantDiscountAmountValue();a>t&&(showErrorDialogBox("Discounted amount "+a+" cannot be greater then max discount amount "+t+". Reducing the discounted amount."),a=t),$("#student-total-instant-discount-amount").val(a),feePayment.fillInstantDiscountAmounts(a)}),$("#student-total-fine-collected-amount").focus(function(){lastTotalFineAmount=feePayment.getTotalFineAmountValue()}).blur(function(){var e=feePayment.getTotalFineAmountValue();feePayment.fillFineAmounts(e),feePayment.computeFineAmount(),feePayment.updatePaidAmountDelta(lastTotalFineAmount,e),feePayment.fillNetAmountDisplay()})},updatePaidAmountDelta:function(e,t){if(e<=t){var a=t-e,n=feePayment.getPaidAmountValue();$("#student-paid-amount").val(n+a)}else{var i=e-t;if(i<=(n=feePayment.getPaidAmountValue()))$("#student-paid-amount").val(n-i);else{if(!feePayment.isWalletUsed())return void showErrorDialogBox("Invalid state. Changed amount was not included in paid amount");$("#student-paid-amount").val(0);var o=getUsedWalletAmount()-(i-n);if(o<0)return void showErrorDialogBox("Invalid state. Changed amount was not included in paid amount of wallet");$("#student-used-wallet-amount").val(o)}}},isWalletUsed:function(){return $("#use-wallet-amount").is(":checked")},getStudentWalletAmount:function(){return parseFloat($("#student-wallet-amount-value").text().trim())},getUsedWalletAmountValue:function(){var e=$("#student-used-wallet-amount").val();return""!=e?parseFloat(e):0},insertFeesDetails:function(e){var t="readonly";"true"===$("#authorised-to-update-fine-assigned-amount").text()&&(t="");var a,n,i="",o=e.feeHeadPaymentDetails,s=e.feeConfigurationBasicInfo.fineApplicable,d=e.totalDueFineAmount,l=e.totalPaidFineAmount,r=0;s&&(r=d-l);for(var c=0;c<o.length;c++){var u=o[c].feeHeadConfiguration.feeHeadId,f=o[c].feeHeadConfiguration.feeHead,m=o[c].assignedAmount,p=o[c].paidAmount,g=o[c].instantDiscountAmount,h=o[c].assignedDiscountAmount;i+=feeHeadPaymentDetailRow(u,f,m,p,o[c].balanceAmount,g+h)}i+='<tr class="total-feehead-payment-row"> <td> <strong> <span>TOTAL</span> </strong> </td> <td class="total-feeHead-assigned-amount"><strong> <span>'+(0).toFixed(2)+'</span></td> <td class="total-feeHead-assigned-discount"><strong> <span>'+(0).toFixed(2)+'</span></td> <td class="total-feeHead-paid-amount"><strong> <span>'+(0).toFixed(2)+'</span></td> <td class="total-feeHead-payment-balance-amount"><strong> <span>'+(0).toFixed(2)+'</span></td> <td class="total-feeHead-collected-amount"><strong> <span>0</span></td> <td class="total-feeHead-instant-discount-amount"><strong> <span>0</span></td> <td class="total-feeHead-payment-due-amount"><strong> <span>'+(0).toFixed(2)+"</span></td> </tr>";var v="fee-card-"+(n=e.feeConfigurationBasicInfo.feeId);a=generateFeesPaymentCard(v,n,e.feeConfigurationBasicInfo.feeName+" ("+getFeeTypeDisplayName(e.feeConfigurationBasicInfo.feeType)+")",i,'<div class="table-responsive"> <table class="table table-hover fine-table"> <thead> <tr> <th scope="col" class="w-25"></th> <th scope="col" >Computed Fine</th> <th scope="col" >Collected Fine</th>  <th scope="col" >Paid Fine</th> </tr> </thead> <tbody> <tr class=""> <td> <strong>FINE</strong> </td> <td><input type="text" class="form-control form-control-sm computed-fine-amount" value="'+d+'" readonly></td> <td><input type="text" class="form-control form-control-sm collected-fine-amount" value="'+l+'" readonly></td>  <td><input type="text" class="form-control form-control-sm paid-fine-amount" value="'+r+'"'+t+"></td>  </tbody> </table> </div>"),$("#selected-fees-end").before(a),feePayment.computeFeesPaymentAmounts(),feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay(),feePayment.updatePaidAmountDelta(0,r),feePayment.bindPaymentDueComputation($("#"+v))},getFloatValue:function(e){return null==e||null==e?0:""==e?0:parseFloat(e)},bindPaymentDueComputation:function(e){$(e).find(".collected-amount").focus(function(){lastCollectedAmount=feePayment.getFloatValue($(this).val())}).blur(function(){feePayment.getFloatValue($(this).val())>=0&&feePayment.computePaymentDueAmount($(this).parent().parent())?(feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay(),feePayment.updatePaidAmountDelta(lastCollectedAmount,feePayment.getFloatValue($(this).val()))):$(this).val(lastCollectedAmount)}),$(e).find(".instant-discount-amount").focus(function(){lastInstantDiscountAmount=$(this).val()}).blur(function(){feePayment.getFloatValue($(this).val())>=0&&feePayment.computePaymentDueAmount($(this).parent().parent())?(feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()):$(this).val(lastInstantDiscountAmount)}),$(e).find(".paid-fine-amount").focus(function(){lastFeeLevelPaidFineAmount=feePayment.getFloatValue($(this).val())}).blur(function(){feePayment.getFloatValue($(this).val())<0?$(this).val(lastFeeLevelPaidFineAmount):(feePayment.computeFineAmount(),feePayment.fillNetAmountDisplay(),feePayment.updatePaidAmountDelta(lastFeeLevelPaidFineAmount,feePayment.getFloatValue($(this).val())))})},computeFineAmount:function(){var e=0,t=0,a=0;$("div.collected-fees-details").each(function(){e+=parseFloat($(this).find(".fine-table").find(".computed-fine-amount").val()),t+=parseFloat($(this).find(".fine-table").find(".collected-fine-amount").val()),a+=parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val())}),$("#net-total-fine-computed-amount").find("span").first().text(e),$("#net-total-fine-collected-amount").find("span").first().text(t),$("#net-total-fine-paid-amount").find("span").first().text(a)},getPaidAmountValue:function(){var e=$("#student-paid-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},getTotalInstantDiscountAmountValue:function(){var e=$("#student-total-instant-discount-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},getTotalFineAmountValue:function(){var e=$("#student-total-fine-collected-amount").val(),t=0;return""!=e&&(t=parseFloat(e)),t<0?0:t},fillCollectedAmounts:function(e){feePayment.clearCollectedAmounts(),$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){if(!(e<=0)){feePayment.computePaymentDueAmount($(this));var t=$(this).find("input.payment-due-amount").val(),a=0;""!=t&&(a=parseFloat(t));var n=0;e>=a?(n=a,e-=a):(n=e,e=0),$(this).find("input.collected-amount").first().val(n),feePayment.computePaymentDueAmount($(this))}})}),e>0&&(showErrorDialogBox(e+" Rs. amount is more than the assigned fees. Please select more fees or reduce the amount."),feePayment.clearCollectedAmounts(),$("#student-paid-amount").val(0)),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},fillInstantDiscountAmounts:function(e){feePayment.clearDiscountedAmounts(),$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){if(!(e<=0)){feePayment.computePaymentDueAmount($(this));var t=$(this).find("input.payment-due-amount").val(),a=0;""!=t&&(a=parseFloat(t));var n=0;e>=a?(n=a,e-=a):(n=e,e=0),$(this).find("input.instant-discount-amount").first().val(n),feePayment.computePaymentDueAmount($(this))}})}),e>0&&showErrorDialogBox(e+" Rs. amount is more than the max applicable discount. Please select more fees or reduce the amount."),feePayment.computeFeesPaymentAmounts(),feePayment.fillNetAmountDisplay()},fillFineAmounts:function(e){feePayment.clearFineAmounts();var t=(new Date).getTime()/1e3;$("div.collected-fees-details").each(function(){var a=$(this).find(".collected-fee-id").text().trim(),n=feePayment.dataCache.dueFeeDetailsMap[a];if(!(null==n.feeConfigurationBasicInfo.dueDate||n.feeConfigurationBasicInfo.dueDate<=0||n.feeConfigurationBasicInfo.dueDate>t)){var i=$(this).find(".fine-table").find(".computed-fine-amount").val(),o=0;""!=i&&(o=parseFloat(i));var s=$(this).find(".fine-table").find(".collected-fine-amount").val(),d=0;if(""!=s&&(d=parseFloat(s)),!(o<=0)){var l=o-d;e<l?($(this).find(".fine-table").find(".paid-fine-amount").val(e),e=0):($(this).find(".fine-table").find(".paid-fine-amount").val(l),e-=l)}}}),e<=0||($("div.collected-fees-details").each(function(){var a=$(this).find(".collected-fee-id").text().trim(),n=feePayment.dataCache.dueFeeDetailsMap[a];if(!(null==n.feeConfigurationBasicInfo.dueDate||n.feeConfigurationBasicInfo.dueDate<=0||n.feeConfigurationBasicInfo.dueDate>t)){var i=$(this).find(".fine-table").find(".computed-fine-amount").val(),o=0;return""!=i&&(o=parseFloat(i)),o<=0?($(this).find(".fine-table").find(".paid-fine-amount").val(e),void(e=0)):void 0}}),e<=0||$("div.collected-fees-details").each(function(){var a=$(this).find(".collected-fee-id").text().trim(),n=feePayment.dataCache.dueFeeDetailsMap[a];if(null==n.feeConfigurationBasicInfo.dueDate||n.feeConfigurationBasicInfo.dueDate<=0||n.feeConfigurationBasicInfo.dueDate>t)return $(this).find(".fine-table").find(".paid-fine-amount").val(e),void(e=0)}))},clearCollectedAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){$(this).find("input.collected-amount").val(0)})})},clearDiscountedAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find("tr.feehead-payment-row").each(function(){$(this).find("input.instant-discount-amount").val(0)})})},clearFineAmounts:function(){$("div.collected-fees-details").each(function(){$(this).find(".fine-table").find(".paid-fine-amount").val(0)})},computePaymentDueAmount:function(e){var t=$(e).find(".collected-amount").first().val(),a=0;t&&(a=parseFloat(t));var n=$(e).find(".instant-discount-amount").first().val(),i=0;n&&(i=parseFloat(n));var o=$(e).find(".payment-balance-amount").first().val(),s=0;o&&(s=parseFloat(o));var d=parseFloat(a.toFixed(2))+parseFloat(i.toFixed(2)),l=parseFloat(s.toFixed(2))-d;return l<0?(showErrorDialogBox("Collected amount = "+d+" is more than the Payable amount = "+s+". Please review the collected and instant discount amounts."),!1):($(e).find(".payment-due-amount").first().val(l.toFixed(0)),!0)},computeFeesPaymentAmounts:function(){var e=0,t=0,a=0,n=0,i=0,o=0,s=0;$("div.collected-fees-details").each(function(){var d=0,l=0,r=0,c=0,u=0,f=0,m=0;$(this).find("tr.feehead-payment-row").each(function(){feePayment.computePaymentDueAmount($(this)),d+=parseFloat($(this).find("input.assigned-amount").val()),l+=parseFloat($(this).find("input.assigned-discount").val()),r+=parseFloat($(this).find("input.paid-amount").val()),c+=parseFloat($(this).find("input.payment-balance-amount").val()),u+=parseFloat($(this).find("input.collected-amount").val()),f+=parseFloat($(this).find("input.instant-discount-amount").val()),m+=parseFloat($(this).find("input.payment-due-amount").val())}),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-assigned-amount").find("span").first().text(d),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-assigned-discount").find("span").first().text(l),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-paid-amount").find("span").first().text(r),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-payment-balance-amount").find("span").first().text(c),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-collected-amount").find("span").first().text(u),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-instant-discount-amount").find("span").first().text(f),$(this).find("tr.total-feehead-payment-row > td.total-feeHead-payment-due-amount").find("span").first().text(m),e+=d,t+=l,a+=r,n+=c,i+=u,o+=f,s+=m}),$("#net-total-fees-assigned-amount").find("span").first().text(e.toFixed(2)),$("#net-total-fees-assigned-discount").find("span").first().text(t.toFixed(2)),$("#net-total-fees-paid-amount").find("span").first().text(a.toFixed(2)),$("#net-total-fees-payment-balance-amount").find("span").first().text(n.toFixed(2)),$("#net-total-fees-collected-amount").find("span").first().text(i.toFixed(2)),$("#net-total-fees-instant-discount-amount").find("span").first().text(o.toFixed(2)),$("#net-total-fees-payment-due-amount").find("span").first().text(s.toFixed(2))},fillNetAmountDisplay:function(){var e=feePayment.computeVariousTotalFeesAmounts(),t=e.totalBalanceAmount,a=e.totalInstantDiscountAmount,n=e.totalFineAmount,i=t+n-a;$("#student-total-balance-amount-display").val(t.toFixed(2)),$("#student-total-fine-collected-amount").val(n.toFixed(2)),$("#student-total-instant-discount-amount").val(a.toFixed(2)),$("#student-net-payble-amount-display").val(i.toFixed(2))},computeVariousTotalFeesAmounts:function(){var e=0,t=0,a=0,n=0;return $("div.collected-fees-details").each(function(){var i=$(this).find(".fine-table").find(".paid-fine-amount").val();""!=i&&(n+=parseFloat(i)),$(this).find("tr.feehead-payment-row").each(function(){var n=$(this).find("input.payment-balance-amount").val();""!=n&&(e+=parseFloat(n));var i=$(this).find("input.instant-discount-amount").val();""!=i&&(t+=parseFloat(i));var o=$(this).find("input.collected-amount").val();""!=o&&(a+=parseFloat(o))})}),{totalBalanceAmount:e,totalInstantDiscountAmount:t,totalCollectedAmount:a,totalFineAmount:n,totalCollectableAmount:e+n-t}},collectSiblingFeesPage:function(e,t){ajaxClient.get("/fees/sibling-due-fee-details/"+t+"/"+e,function(a){$("#sibling-collect-fee-tab").html(a),$(".payment-main-screen-info").attr("style","display:none"),$(".fee-payment-tab").attr("style","display:none"),$("#sibling-collect-fee-tab").attr("style","display:block"),feePayment.populateSiblingCollectFeesScreen(e,t),feePayment.registerSummaryAmountInput(),feePayment.registerIndividualAmountInput()})},populateSiblingCollectFeesScreen:function(e,t){var a=feePayment.dataCache.sessionData[t];$("#collect-sibling-fee-session-display").html(a.displayName),$("#collect-sibling-fee-session-id").html(t),initDate(pastFeePaymentTransactionDays);for(var n=JSON.parse($("#sibling-due-fee-details").text()),i=0,o=0,s=0;s<n.length;s++){var d=n[s];d.feePaymentRestricted||(i+=d.dueAmount,o+=d.dueFineAmount)}var l=i+o-0;$("#sibling-total-balance-amount-display").val(i),$("#sibling-total-fine-collected-amount").val(o),$("#sibling-total-instant-discount-amount").val(0),$("#sibling-net-payble-amount-display").val(l),$("#sibling-student-paid-amount").val(o),$("tr.sibling-payment-details-tr").each(function(){var e=JSON.parse($(this).find(".sibling-due-fee-detail").text()),t=e.dueFineAmount;$(this).find(".sibling-student-fine-amount").val(t),$(this).find(".sibling-student-paid-amount").val(t);var a=e.dueAmount,n=$("#sibling-student-instant-discount-amount").val();""!==n&&null!=n||(n=0);var i=parseFloat(a)+parseFloat(t)-parseFloat(n);$(this).find(".sibling-student-net-payable-amount").val(roundOffNumber(i,2))})},registerSummaryAmountInput:function(){$("#sibling-student-paid-amount").focus(function(){}).blur(function(){JSON.parse($("#sibling-due-fee-details").text());var e=$(this).val();""!==e&&null!=e||(e=0),e=parseFloat(e);var t=parseFloat($("#sibling-net-payble-amount-display").val());""!==t&&null!=t||(t=0),t=parseFloat(t);var a=$("#sibling-total-fine-collected-amount").val();if(""!==a&&null!=a||(a=0),a=parseFloat(a),e>t)return showErrorDialogBox("Paid Amount cannot be greater than new payable amount!"),void $(this).val(0);e<a?showErrorDialogBox("Paid Amount cannot be lesser than total fine amount!"):feePayment.updateIndividualPaidAmount(!0)}),$("#sibling-total-fine-collected-amount").focus(function(){}).blur(function(){if(feePayment.setNetPayableAmount()){var e=$("#sibling-total-fine-collected-amount").val();""!==e&&null!=e||(e=0),e=parseFloat(e);var t=JSON.parse($("#sibling-due-fee-details").text()),a=feePayment.countSiblingsWithFeePaymentAllowed(t),n=0,i=0;n=e%a==0?e/a:(e-(i=e%a))/a;$("tr.sibling-payment-details-tr").each(function(){var e=$(this).find(".sibling-due-fee-detail").text();if(!JSON.parse(e).feePaymentRestricted){$(this).find(".sibling-student-fine-amount").val(n+i),i=0;feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-fine-amount"))}});var o=$("#sibling-student-paid-amount").val();""!==o&&null!=o||(o=0),(o=parseFloat(o))<e&&$("#sibling-student-paid-amount").val(e),feePayment.updateIndividualPaidAmount(!0)}}),$("#sibling-total-instant-discount-amount").focus(function(){}).blur(function(){if(feePayment.setNetPayableAmount()){var e=$("#sibling-total-instant-discount-amount").val();""!==e&&null!=e||(e=0),e=parseFloat(e);var t=JSON.parse($("#sibling-due-fee-details").text()),a=feePayment.countSiblingsWithFeePaymentAllowed(t),n=0,i=0;n=e%a==0?e/a:(e-(i=e%a))/a;$("tr.sibling-payment-details-tr").each(function(){var e=$(this).find(".sibling-due-fee-detail").text();if(!JSON.parse(e).feePaymentRestricted){$(this).find(".sibling-student-instant-discount-amount").val(n+i),i=0;feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-instant-discount-amount"))}}),feePayment.updateIndividualPaidAmount(!0)}})},countSiblingsWithFeePaymentAllowed:function(e){return e.filter(e=>!e.feePaymentRestricted).length},registerIndividualAmountInput:function(){$(".sibling-student-paid-amount").focus(function(){}).blur(function(){var e=0;$("tr.sibling-payment-details-tr").each(function(){var t=$(this).find(".sibling-student-paid-amount").val();null!=t&&null!=t&&""!=t&&(e+=parseFloat(t))});var t=$(this).closest("tr"),a=parseFloat(t.find(".sibling-student-fine-amount").val())||0,n=(parseFloat(t.find(".sibling-student-instant-discount-amount").val()),parseFloat($(this).val())||0),i=parseFloat(t.find(".sibling-student-net-payable-amount").val())||0,o=parseFloat($("#sibling-net-payble-amount-display").val());e>o?showErrorDialogBox("Paid Amount cannot be greater than new payable amount!"):n>i?showErrorDialogBox("Paid Amount cannot be greater than net payable amount!"):a>n&&i>a?showErrorDialogBox("Paid Amount cannot be lesser than fine amount!"):$("#sibling-student-paid-amount").val(e)}),$(".sibling-student-fine-amount").focus(function(){}).blur(function(){var e=0;$("tr.sibling-payment-details-tr").each(function(){var t=$(this).find(".sibling-student-fine-amount").val();null!=t&&null!=t&&""!=t&&(e+=parseFloat(t))}),$("#sibling-total-fine-collected-amount").val(e);var t=feePayment.setNetPayableAmount();if(t&&(t=feePayment.setIndividualNetPayableAmount(this))){var a=$("#sibling-student-paid-amount").val();""!==a&&null!=a||(a=0),a=parseFloat(a);var n=$(this).closest("tr"),i=parseFloat($(this).val())||0;(parseFloat(n.find(".sibling-student-paid-amount").val())||0)<i&&n.find(".sibling-student-paid-amount").val(i),feePayment.updateIndividualPaidAmount(!1),feePayment.updateTotalPaidAmount()}}),$(".sibling-student-instant-discount-amount").focus(function(){}).blur(function(){var e=0;$("tr.sibling-payment-details-tr").each(function(){var t=$(this).find(".sibling-student-instant-discount-amount").val();null!=t&&null!=t&&""!=t&&(e+=parseFloat(t))}),$("#sibling-total-instant-discount-amount").val(e);var t=feePayment.setNetPayableAmount();t&&(t=feePayment.setIndividualNetPayableAmount(this))&&feePayment.updateIndividualPaidAmount(!1)})},updateTotalPaidAmount:function(){var e=0;$("tr.sibling-payment-details-tr").each(function(){var t=$(this).find(".sibling-student-paid-amount").val();null!=t&&null!=t&&""!=t&&(e+=parseFloat(t))}),$("#sibling-student-paid-amount").val(e)},setNetPayableAmount:function(){var e=$("#sibling-total-fine-collected-amount").val();""!==e&&null!=e||(e=0),e=parseFloat(e);var t=$("#sibling-total-balance-amount-display").val();""!==t&&null!=t||(t=0),t=parseFloat(t);var a=$("#sibling-total-instant-discount-amount").val();""!==a&&null!=a||(a=0),a=parseFloat(a);var n=parseFloat(t)+parseFloat(e)-parseFloat(a);return n<0?(showErrorDialogBox("Net Payable amount cannot be negative!"),!1):($("#sibling-net-payble-amount-display").val(n),!0)},setIndividualNetPayableAmount:function(e){var t=$(e).parent().parent().find(".sibling-student-instant-discount-amount").val();""!==t&&null!=t||(t=0),t=parseFloat(t);var a=$(e).parent().parent().find(".sibling-student-fine-amount").val();""!==a&&null!=a||(a=0),a=parseFloat(a);var n=JSON.parse($(e).parent().parent().find(".sibling-due-fee-detail").text()).dueAmount,i=parseFloat(n)+parseFloat(a)-parseFloat(t);return i<0?(showErrorDialogBox("Net Payable amount cannot be negative!"),!1):($(e).parent().parent().find(".sibling-student-net-payable-amount").val(roundOffNumber(i,2)),!0)},updateIndividualPaidAmount:function(e){var t=JSON.parse($("#sibling-due-fee-details").text()),a=$("#sibling-student-paid-amount").val();""!==a&&null!=a||(a=0),a=parseFloat(a);var n={};e&&$("tr.sibling-payment-details-tr").each(function(){var e=JSON.parse($(this).find(".sibling-due-fee-detail").text());if(!e.feePaymentRestricted){var t=e.studentId,a=$(this).find(".sibling-student-net-payable-amount").val();n[t]=parseFloat(a)}});var i=feePayment.countSiblingsWithFeePaymentAllowed(t),o=a,s=0,d=sortMapByValue(n),l={},r={},c=0;$.each(d,function(e,t){o%(i-s);var a=(o-o%(i-s))/(i-s);a>t&&(a=t),l[e]=a,r[e]=t-a,r[e]>0&&(c+=1),o-=a,s+=1});var u=0,f=0;o>0&&(u=o%c==0?o/c:(o-(f=o%c))/c,$.each(l,function(e,t){l[e]+=u+f,f=0})),e&&$("tr.sibling-payment-details-tr").each(function(){var e=JSON.parse($(this).find(".sibling-due-fee-detail").text()).studentId,t=l[e];$(this).find(".sibling-student-paid-amount").val(t)})},collectSiblingFees:function(){$("#sibling-fee-payment-confirm-modal").modal("toggle");var e=JSON.parse($("#collect-sibling-fee-session-id").html()),t=$("#sibling-payment-mode-input").find(":selected").val().trim(),a=$("#sibling-payment-reference").val().trim(),n=getDate($("#sibling-payment-date").val()),i=$("#sibling-payment-remark").val();if(validateMandatoryFields($("#sibling-collect-fee-tab")))showErrorDialogBox("Please fill required fields before making payment.");else if(!validateSiblingPaymentAmounts()){if(null==n)return showErrorDialogBox("Please enter the date of payment"),null;var o=new Date;if(n.getTime()>o.getTime())return showErrorDialogBox("Invalid payment date. Future transactions not allowd"),null;var s=[];$("tr.sibling-payment-details-tr").each(function(){var o=JSON.parse($(this).find(".sibling-due-fee-detail").text());if(!o.feePaymentRestricted){var d=o.studentId,l=$(this).find(".sibling-student-fine-amount").val();""!==l&&null!=l||(l=0),l=parseFloat(l);var r=$(this).find(".sibling-student-instant-discount-amount").val();""!==r&&null!=r||(r=0),r=parseFloat(r);var c=$(this).find(".sibling-student-paid-amount").val();""!==c&&null!=c||(c=0),0===(c=parseFloat(c))&&0===r&&0===l||s.push({studentId:d,academicSessionId:e,transactionMode:t,transactionDate:n.getTime()/1e3,transactionReference:a,fineCollectionAmount:l,instantDiscountAmount:r,walletAmount:0,paidAmount:c,transactionRemarks:i})}}),s.length<=0?showErrorDialogBox("No payment Details added to make payment!"):ajaxClient.post("/fees/collect-sibling-fees/"+e,{feePaymentPayloadLiteList:JSON.stringify(s)},function(t){returnToPaymentMainScreenDisplay(),$("#fee-payment-status-modal-container").html(t),$("#collect-fee-payment-status-modal").modal({backdrop:"static",keyboard:!1});var a=$("#collect-fee-student-id").text().trim();feePayment.collectSiblingFeesPage(a,e),$("#marks-feed-status-modal-container").html(t),$("#exam-config\\.status-modal").modal({backdrop:"static",keyboard:!1})})}},prepareUpdateWalletTransaction:function(e,t,a){feePayment.currentTransactionData={transactionId:e,studentId:t,amount:a},$("#student-wallet-transactions-modal").modal("hide"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove(),$("#update-wallet-transaction-id").val(e),$("#update-wallet-student-id").val(t),$("#update-wallet-amount").val(a),$("#update-wallet-transaction-modal").modal("toggle"),$(".cancel-update-btn").off("click").on("click",function(){feePayment.cancelUpdateWalletTransaction()}),$("#confirm-update-btn").off("click").on("click",function(){feePayment.updateWalletTransactionAmount()})},cancelUpdateWalletTransaction:function(){$("#update-wallet-transaction-modal").modal("toggle"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove(),feePayment.reopenWalletTransactions()},updateWalletTransactionAmount:function(){var e=$("#update-wallet-transaction-id").val(),t=$("#update-wallet-student-id").val(),a=$("#update-wallet-amount").val();if(""!==a&&null!=a){var n={transactionId:e,studentId:t,amount:parseFloat(a)};$("#update-wallet-transaction-modal").modal("toggle"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove(),ajaxClient.post("/fees/update-wallet-transaction-amount",{updateWalletTransactionPayload:JSON.stringify(n)},function(e){$("#update-wallet-transaction-status-modal-container").html(e),$("#update-wallet-transaction-status-modal").modal("toggle"),$("#close-status-btn").off("click").on("click",function(){feePayment.closeStatusModal()}),loadStudentPaymentDetails(t)})}else showErrorDialogBox("Invalid Amount!")},closeStatusModal:function(){$("#update-wallet-transaction-status-modal").modal("toggle"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove()},reopenWalletTransactions:function(){var e=feePayment.currentTransactionData?feePayment.currentTransactionData.studentId:$("#wallet-recharge-student-id").text().trim();ajaxClient.get("/fees/wallet-transactions/"+e,function(e){$("#student-wallet-transactions-modal-container").html(e),$("#student-wallet-transactions-modal").modal("toggle")})},prepareDeleteWalletTransaction:function(e,t){feePayment.currentDeleteTransactionData={transactionId:e,studentId:t},$("#student-wallet-transactions-modal").modal("hide"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove(),$("#delete-wallet-transaction-id").val(e),$("#delete-wallet-student-id").val(t),$("#delete-wallet-transaction-modal").modal("toggle"),$(".cancel-update-btn").off("click").on("click",function(){feePayment.cancelUpdateWalletTransaction()}),$("#confirm-delete-btn").off("click").on("click",function(){feePayment.deleteWalletTransactionAmount()})},deleteWalletTransactionAmount:function(){var e=$("#delete-wallet-transaction-id").val(),t=$("#delete-wallet-student-id").val(),a={transactionId:e,studentId:t};$("#delete-wallet-transaction-modal").modal("toggle"),$("body").removeClass("modal-open"),$(".modal-backdrop").remove(),ajaxClient.post("/fees/delete-wallet-transaction",{deleteWalletTransactionPayload:JSON.stringify(a)},function(e){$("#update-wallet-transaction-status-modal-container").html(e),$("#update-wallet-transaction-status-modal").modal("toggle"),$("#close-status-btn").off("click").on("click",function(){feePayment.closeStatusModal()}),loadStudentPaymentDetails(t)})}};function generateFeeChallan(e){var t=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/fees/generate-student-fee-challan-pdf/"+t+"/"+e,"_blank")}function useWalletAmountCallBack(){$("#use-wallet-amount").change(function(){if($(this).is(":checked")){if(parseFloat($("#student-wallet-amount-value").text().trim())<=0)return showErrorDialogBox("You donot have required credits in your wallet. Please recharge it to use wallet amount."),void $(this).prop("checked",!1);$("#student-used-wallet-amount").val(0),$("#wallet-amount-input-container").attr("style","display:block")}else{var e=getUsedWalletAmount();$("#student-used-wallet-amount").val(0),$("#student-paid-amount").val(feePayment.getPaidAmountValue()+e),$("#wallet-amount-input-container").attr("style","display:none")}})}function bindPaymentSearchStudentEvent(){var e="#student-payment-search-result";$("#student-payment-search").on("click",function(){feePayment.doneStudentSearchTyping(e)}),$("#student-payment-search-text").on("keyup",function(t){13==t.keyCode&&feePayment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#student-payment-search-text",e,feePayment.doneStudentSearchTyping)}function confirmCancelTransaction(e,t){$("#fee-payment-transaction-view-modal").modal("toggle"),$("#cancel-transaction-student-id").text(t),$("#cancel-transaction-id").text(e),$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle")}function reviewCancelTransaction(){$("#cancel-transaction-student-id").text(""),$("#cancel-transaction-id").text(""),$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),$("#fee-payment-transaction-view-modal").modal("toggle")}function cancelStudentTransaction(){var e=$("#cancel-transaction-student-id").text().trim(),t=$("#cancel-transaction-id").text().trim();if(""!=t){var a={instituteId:null,transactionId:t,cancelBy:null,cancelAt:(new Date).getTime()/1e3,cancelRemarks:$("#payment-cancel-remarks").val().trim()};$("#fee-payment-cancel-transaction-confirm-modal").modal("toggle"),ajaxClient.post("/fees/cancel-payment-transaction",{feeCancelledPayload:JSON.stringify(a)},function(t){returnToPaymentMainScreenDisplay(),$("#fee-payment-status-modal-container").html(t),$("#fee-payment-status-modal").modal("toggle"),loadStudentPaymentDetails(e)})}else showErrorDialogBox("Invalid Transaction to cancel")}function bindPaymentActionEvents(){$("#past-payment-button").on("click",function(){pastTransactionsDisplay()})}function pastTransactionsDisplay(){$(".payment-main-screen-info").attr("style","display:none"),$(".fee-payment-tab").attr("style","display:none"),$("#fee-transactions-tab").attr("style","display:block")}function returnToPaymentMainScreenDisplay(){$(".payment-main-screen-info").attr("style","display:block"),$(".fee-payment-tab").attr("style","display:none"),$("#fee-payment-view-tab").attr("style","display:block")}function generateFeesPaymentCard(e,t,a,n,i){return'<div class="card card-border collected-fees-details" id="'+e+'"> <p style="display:none" class="collected-fee-id">'+t+'</p>  <div class="card-header" id="fee-heading-'+t+'"> <h5 class="mb-0"> <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-fee-'+t+'" aria-expanded="true" aria-controls="collapse-fee-'+t+'"> <strong>'+a.toUpperCase()+' </strong> </button> </h5> </div> <div id="collapse-fee-'+t+'" class="collapse show" aria-labelledby="fee-heading-'+t+'" data-parent="#selected-fees-payment-list"> <div class="card-body"> <div class="table-responsive"> <table class="table table-hover"> <thead> <tr> <th scope="col" class="w-25">Fee Head</th> \x3c!-- <th scope="col" >Fee Head</th> --\x3e <th scope="col" >Assigned Amount</th> <th scope="col" >Given Discount</th> <th scope="col" >Collected Amount</th> <th scope="col" >Payable Amount</th> <th scope="col" >Amount Paid</th> <th scope="col" >Instant Discount</th> <th scope="col" >Due Amount</th> </tr> </thead> <tbody> '+n+" </tbody> </table> </div> "+i+" </div> </div> </div>"}function feeHeadPaymentDetailRow(e,t,a,n,i,o){return'<tr class="feehead-payment-row"> <td class="payment-feehead-id" style="display:none" >'+e+"</td> <td>"+t+'</td> <td><input type="text" class="form-control form-control-sm assigned-amount" value="'+a.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm assigned-discount" value="'+o.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm paid-amount" value="'+n.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm payment-balance-amount" value="'+i.toFixed(2)+'" readonly></td> <td><input type="number" class="form-control form-control-sm collected-amount" value="0" ></td> <td><input type="number" class="form-control form-control-sm instant-discount-amount" value="0" ></td> <td><input type="text" class="form-control form-control-sm payment-due-amount" value="'+i.toFixed(2)+'" readonly></td> </tr>'}function getUsedWalletAmount(){var e=parseFloat($("#student-wallet-amount-value").text().trim()),t=0;return $("#use-wallet-amount").is(":checked")&&((t=parseFloat($("#student-used-wallet-amount").val()))<=0&&(t=0),t>e&&(t=e)),t}function getFeeTypeDisplayName(e){var t="";return"REGULAR"==e?t="Regular Fees":"SPECIAL"==e?t="Special Fees":"ONE_TIME"==e&&(t="One Time Fees"),t}function loadStudentPaymentDetails(e){loadStudentPaymentDetails(e,!1)}function deleteFeeFollowUpDetails(e,t){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Delete Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong> <font color="red">Are you sure that you want to delete this followup trail ? </font> </strong>'),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button  type="button" class="btn btn-danger" onclick="deleteStudentFeeFollowupTrail()">Yes, Delete</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#fee-follow-up-close-modal").modal("toggle")}function editFeeFollowUpDetails(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3);var t=JSON.parse($(e).parent().find("#follow-up-json").text().trim());$("#update-entity-name").html("<option>"+t.entityName+"</option>"),$("#update-fee-follow-up-date").val(getFormattedDate(t.followUpDate)),$("#update-follow-up-mode").html("<option>"+t.followUpMode+"</option>"),$("#update-contact-person-name").val(t.contactPersonName),$("#update-follow-up-type").html("<option>"+t.followUpType+"</option>"),$("#update-conversation").val(t.conversation),null!=t.nextFollowUpDate?$("#update-next-fee-follow-up-date").val(getFormattedDate(t.nextFollowUpDate)):$("#update-next-fee-follow-up-date").val(""),$("#update-amount").val(t.amount),$("#update-student-follow-up-id").text(t.followUpId),$("#student-id").text(t.entityId),$("#update-fee-follow-up-modal").modal("toggle")}function closeFeeFollowUpDetails(e,t,a){if(a)n='<h5 class="modal-title" id="exampleModalLongTitle">Close All Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>',i='<strong style="color:red">Are you sure that you want to close the all followup trail for this student ?</strong>',o='<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="closeStudentFeeFollowupTrail(\'true\',\'INACTIVE\')">Yes, Close All</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>';else var n='<h5 class="modal-title" id="exampleModalLongTitle">Close Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>',i="<strong>Are you sure that you want to close this followup trail ?</strong>",o='<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="closeStudentFeeFollowupTrail(\'false\',\'INACTIVE\')">Yes, Close It</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>';n='<h5 class="modal-title" id="exampleModalLongTitle">Close Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>';$("#modal-header").html(n),$("#modal-body").html(i),$("#modal-footer").html(o),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#fee-follow-up-close-modal").modal("toggle")}function addFeeFollowUpDetails(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3),$("p#student-follow-up-id").text(e),$("#next-fee-follow-up-date").val(""),$("#add-fee-follow-up-modal").modal("toggle")}function updateStudentFeeFollowupTrail(e){if(!validateMandatoryFields($("#update-fee-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#update-entity-name").val();if(null!=(n=getDate($("#update-fee-follow-up-date").val()))){var n=n.getTime()/1e3,i=$("#update-contact-person-name").val(),o=$("select#update-follow-up-type").val(),s=$("#update-conversation").val(),d=$("#update-next-fee-follow-up-date").val();if(""!=d&&(d=(d=getDate($("#update-next-fee-follow-up-date").val())).getTime()/1e3),null!=d&&""!=d||(d=null),null!=d&&n>d)return $("#update-fee-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#update-amount").val(),r=e,c=$("#update-student-follow-up-id").text(),u={entityId:$("#student-id").text(),followUpId:c,entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#update-fee-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/update-fee-followup-trail/"+t,{updateFollowUpPayload:JSON.stringify(u)},function(e){returnToPaymentMainScreenDisplay(),$("#fee-followup-trail-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),getFollowUpDetails()})}else showErrorDialogBox("Please fill follow up date.")}}function addStudentFeeFollowupTrail(e){if(!validateMandatoryFields($("#add-fee-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#entity-name").val();if(null!=(n=getDate($("#fee-follow-up-date").val()))){var n=n.getTime()/1e3,i=$("#contact-person-name").val(),o=$("select#follow-up-type").val(),s=$("#conversation").val(),d=getDate($("#next-fee-follow-up-date").val());if(null!=d&&(d=d.getTime()/1e3),null!=d&&""!=d||(d=null),null!=d&&n>d)return $("#add-fee-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#amount").val(),r=e,c={entityId:$("#student-follow-up-id").text(),entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#add-fee-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/add-fee-followup-trail/"+t,{followUpPayload:JSON.stringify(c)},function(e){returnToPaymentMainScreenDisplay(),$("#fee-followup-trail-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),getFollowUpDetails()})}else showErrorDialogBox("Please fill follow up date.")}}function getFollowUpDetails(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#student-follow-up-id").text().trim();ajaxClient.get("/fees/follow-up-details/"+e+"/"+t,function(e){$("#follow-up-details-container-div").html(e),$(".tab-pane").removeClass("fade")})}function deleteStudentFeeFollowupTrail(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete-student-follow-up-id").text().trim();$("#student-follow-up-id").text().trim();""!=t?($("#fee-follow-up-close-modal").modal("toggle"),ajaxClient.post("/fees/delete-fee-followup-trail/"+e+"/"+t,{},function(e){returnToPaymentMainScreenDisplay(),$("#fee-followup-trail-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),getFollowUpDetails()})):showErrorDialogBox("Invalid Follow Up to cancel")}function closeStudentFeeFollowupTrail(e,t){var a=academicSessionHandler.getSelectedSessionId(),n=$("#delete-student-follow-up-id").text().trim();$("#student-follow-up-id").text().trim();""!=n?($("#fee-follow-up-close-modal").modal("toggle"),ajaxClient.post("/fees/close-fee-followup-trail/"+a+"/"+e+"/"+t+"/"+n,{},function(e){returnToPaymentMainScreenDisplay(),$("#fee-followup-trail-status-modal-container").html(e),$("#fee-followup-trail-status-modal").modal({backdrop:"static",keyboard:!1}),getFollowUpDetails()})):showErrorDialogBox("Invalid Follow Up to cancel")}function loadStudentPaymentDetails(e,t){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/student-payment-details/"+a+"/"+e,function(e){$("#student-payment-screen").html(e),feePayment.initCache(),bindPaymentActionEvents(),bindPaymentTransactionClickEvents(),bindCollectFeesPaymentEvent(),initDate(pastFeePaymentTransactionDays),bindViewPaymentFeesEvent(),useWalletAmountCallBack(),feePayment.fillNetAmountDisplay(),setOtherSessionTotalDueAmount(),t&&feePayment.collectFeesDisplay(a),getFeeFollowUpDetails()})}function bindCollectFeesPaymentEvent(){$("#fee-payment-confirm").on("click",function(){$("#fee-payment-confirm-modal").modal("toggle");var e=collectFeesInformation();null!=e&&ajaxClient.post("/fees/collect-fee-payment",{feePaymentPayload:JSON.stringify(e)},function(t){returnToPaymentMainScreenDisplay(),$("#fee-payment-status-modal-container").html(t),$("#collect-fee-payment-status-modal").modal({backdrop:"static",keyboard:!1}),loadStudentPaymentDetails(e.feePaymentTransactionMetaData.studentId)})})}function collectFeesInformation(){var e=$("#collect-fee-session-id").text().trim(),t=$("#collect-fee-student-id").text().trim(),a=$("#bank-account-input"),n="";a.length>0&&a.find(":selected").length>0&&(n=a.find(":selected").val().trim());var i=$("#payment-mode-input").find(":selected").val().trim(),o=$("#payment-reference").val().trim(),s=getDate($("#payment-date").val()),d=$("#payment-remark").val();if(validateMandatoryFields($("#collect-fee-tab")))showErrorDialogBox("Please fill required fields before making payment.");else if(!validateAmounts()){if(null==s)return showErrorDialogBox("Please enter the date of payment"),null;var l=new Date;if(s.getTime()>l.getTime())return showErrorDialogBox("Invalid payment date. Future transactions not allowd"),null;$("div.collected-fees-details").each(function(){});var r=[];$("div.collected-fees-details").each(function(){var e=$(this).find("p.collected-fee-id").text().trim(),t=parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val()),a=[];if($(this).find("tr.feehead-payment-row").each(function(){var e={feeHeadId:$(this).find("td.payment-feehead-id").text().trim(),paidAmount:$(this).find("input.collected-amount").val(),instantDiscount:$(this).find("input.instant-discount-amount").val()};a.push(e)}),a.length>0)for(var n=t/a.length,i=0;i<a.length;i++)a[i].fineAmount=n;var o={feeId:e,feeHeadTransactionAmounts:a};r.push(o)});var c=getUsedWalletAmount();return{feePaymentTransactionMetaData:{studentId:t,bankAccountId:n,transactionMode:i,transactionReference:o,transactionDate:s.getTime()/1e3,academicSessionId:e,debitWalletAmount:c,remark:d},feeIdFeeHeadTransactionList:r}}}function validateAmounts(){var e=parseFloat($("#net-total-fees-assigned-amount").text()),t=parseFloat($("#net-total-fees-collected-amount").text());parseFloat($("#net-total-fees-instant-discount-amount").text());return""==e||0==e?(showErrorDialogBox("No amount added to collect fees."),!0):(""==t||0==t)&&!confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")}function bindViewPaymentFeesEvent(){$(".view-payment-fee-info").on("click",function(){var e=$(this).parent().parent().find("p.payment-fee-info").text().trim();populateViewFeePaymentContent(JSON.parse(e))})}function setOtherSessionTotalDueAmount(){$(".totalDueAmount").each(function(){var e=0,t=0,a=0;$(this).parent().parent().parent().parent().find(".payment-fee-info").each(function(){var n=$(this).text().trim(),i=JSON.parse(n);for(e=0;e<i.feeHeadPaymentDetails.length;e++){i.feeHeadPaymentDetails[e].feeHeadConfiguration.feeHead;var o=i.feeHeadPaymentDetails[e].assignedAmount,s=i.feeHeadPaymentDetails[e].assignedDiscountAmount,d=i.feeHeadPaymentDetails[e].instantDiscountAmount,l=i.feeHeadPaymentDetails[e].paidAmount,r=i.feeHeadPaymentDetails[e].balanceAmount,c=i.feeHeadPaymentDetails[e].computedDueFineAmount;console.log(c),o,s+d,l,t+=r,a+=c}}),totalDueAmounts=t+a,$(this).text(totalDueAmounts.toFixed(2)+"/-"),$(this).css("font-weight","Bold")})}function populateViewFeePaymentContent(e){$("#view-payment-fee-name").text(e.feeConfigurationBasicInfo.feeName);var t="",a=0,n=0,i=0,o=0,s=0;for(a=0;a<e.feeHeadPaymentDetails.length;a++){var d=e.feeHeadPaymentDetails[a].feeHeadConfiguration.feeHead,l=e.feeHeadPaymentDetails[a].assignedAmount,r=e.feeHeadPaymentDetails[a].assignedDiscountAmount,c=e.feeHeadPaymentDetails[a].instantDiscountAmount,u=e.feeHeadPaymentDetails[a].paidAmount,f=e.feeHeadPaymentDetails[a].balanceAmount,m=r+c;n+=l,i+=m,o+=u,s+=f,t=t+"<tr> <td>"+d+'</td> <td><input type="text" class="form-control form-control-sm" value="'+l.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+m.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+u.toFixed(2)+'" readonly></td> <td><input type="text" class="form-control form-control-sm" value="'+f.toFixed(2)+'" readonly></td> </tr>'}t=t+"<tr> <td> <strong> TOTAL </strong></td> <td>"+n.toFixed(2)+"</td> <td>"+i.toFixed(2)+"</td> <td>"+o.toFixed(2)+"</td> <td>"+s.toFixed(2)+"</td> </tr>",$("#view-payment-feehead-details").html(t),$("#fee-payment-details-view-modal").modal("toggle")}var reminders={dataCache:{},loadSendReminderPage:function(){ajaxClient.get("/fees/due-fees-reminder-screen",function(e){$("#main-content").html(e),initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3),channelCreditsHandler.initDataCache(),channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits"),reminders.bindSelectClassCheckboxEvent(),reminders.dataCache.sendReminders={},reminders.dataCache.selectedStudentCount=0,reminders.dataCache.templateLoaded=!1,initSelect2(),academicSessionHandler.bindSessionChangeEvent(reminders.loadBulkStudentScreen)})},loadBulkStudentScreen:function(e,t,a,n,i,o){var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/load-bulk-student-screen/"+s,function(s){$("#fee-reminder-screen").html(s),initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3),channelCreditsHandler.initDataCache(),channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits"),reminders.bindSelectClassCheckboxEvent(),reminders.dataCache.sendReminders={},reminders.dataCache.selectedStudentCount=0,reminders.dataCache.templateLoaded=!1,initSelect2(),e&&ajaxClient.get("/fees/due-fees-students/"+t+"/"+a.getTime()/1e3+"/"+n+"?requiredStandards="+o+"&includePastSessions="+i,function(e){$("#due-fees-student-list").html(e),reminders.dataCache.sendReminders.dueFeesDate=a.getTime()/1e3,reminders.dataCache.sendReminders.requiredStandards=o,reminders.dataCache.selectedStudentCount=0})})},loadDueFeeStudentsForInstitute:function(){var e=academicSessionHandler.getSelectedSessionId(),t=getDate($("#reminder-fee-due-date").val());if(null!=t){reminders.dataCache.dueDate=getDate($("#reminder-fee-due-date").val());var a=!1;$("#include-fee-fine").is(":checked")&&(a=!0);var n=!1;$("#include-past-sessions").is(":checked")&&(n=!0),reminders.dataCache.includeFine=a,reminders.dataCache.includePastSessions=n;var i=$(".select-reminder-class").val();i=null!=i&&null!=i?i.join(","):"",reminders.dataCache.requiredStandards=i,ajaxClient.get("/fees/due-fees-students/"+e+"/"+t.getTime()/1e3+"/"+a+"?requiredStandards="+i+"&includePastSessions="+n,function(e){$("#due-fees-student-list").html(e),reminders.dataCache.sendReminders.dueFeesDate=t.getTime()/1e3,reminders.dataCache.sendReminders.requiredStandards=i,reminders.dataCache.selectedStudentCount=0})}else showErrorDialogBox("Please fill due date.")},selectBulkStudentList:function(e){if(e.checked){$(".bulk-student-select-checkbox").prop("checked",!0);var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),reminders.dataCache.selectedStudentCount=t}else $(".bulk-student-select-checkbox").prop("checked",!1),$("#bulk-students-selected-count").html(0),reminders.dataCache.selectedStudentCount=0},bindSelectClassCheckboxEvent:function(){$("#reminder-select-all-class").change(function(){$(this).is(":checked")?$(".select-reminder-student-class-menu").attr("style","display:none"):$(".select-reminder-student-class-menu").attr("style","display:block")})},getRequiredStandards:function(){var e=[];return $("#reminder-select-all-class").is(":checked")?e:($(".select-reminder-class").each(function(){$(this).prop("checked")&&e.push($(this).attr("id"))}),e)},getRequiredStandardsCSV:function(){for(var e="",t=reminders.getRequiredStandards(),a=0;a<t.length;a++)e=0==a?t[a]:e+","+t[a];return e},bulkStudentSelectCheckbox:function(e){var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),reminders.dataCache.selectedStudentCount=t},bulkReminderConfirmModal:function(){var e=$("input.bulk-student-select-checkbox:checkbox:checked").length;if(0!=e){reminders.dataCache.selectedStudentCount=e;var t=$("#include-fee-fine").is(":checked"),a=$("#include-past-sessions").is(":checked");reminders.dataCache.templateLoaded?(reminders.updateReminderModalDisplay(t,a),$("#bulk-reminder-confirmation-modal").modal({backdrop:"static",keyboard:!1}),$(".selected-student-count").text(e)):ajaxClient.get("/fees/reminder-templates",function(n){$("#bulk-reminder-template-modal-container").html(n),reminders.updateReminderModalDisplay(t,a),$("#bulk-reminder-confirmation-modal").modal({backdrop:"static",keyboard:!1});var i=readJson("#audio-templates-json");reminders.bindAudioTemplateClickAction(i),$(".selected-student-count").text(e),reminders.dataCache.templateLoaded=!0})}else showErrorDialogBox("Please select atleast one student for sending reminders.")},updateReminderModalDisplay:function(e,t){e?$(".fine-included").text("Yes"):$(".fine-included").text("No"),t?$(".past-session-included").text("Yes"):$(".past-session-included").text("No"),$("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count),$("#audio-voice-call-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count),$("#audio-template-selection-display").attr("style","display:none"),$("#audio-template-select").val(""),$("#send-bulk-voice-calls-button").prop("disabled",!0),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:none;"),reminders.dataCache.selectedStudentCount>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count?($("#send-bulk-sms-button").prop("disabled",!0),$("#insufficient-sms-credits-message").attr("style","color:red; display:block;")):($("#send-bulk-sms-button").prop("disabled",!1),$("#insufficient-sms-credits-message").attr("style","color:red; display:none;"))},bindAudioTemplateClickAction:function(e){$("#audio-template-select").change(function(){var t=$(this).val();if(""==t)return $("#audio-template-selection-display").attr("style","display:none"),void $("#send-bulk-voice-calls-button").prop("disabled",!0);$("#audio-template-selection-display").attr("style","display:block");for(var a=0;a<e.length;a++){var n=e[a];if(t==n.templateId){$("#template-name").text(n.templateName),$("#template-duration").text(round(n.metadata.audio_metadata.duration,0)),$("#template-credits").text(n.credits);var i=n.credits*reminders.dataCache.selectedStudentCount;$("#total-student-credits").text(i),reminders.dataCache.selectedAudioCredits=n.credits,i>channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count?($("#send-bulk-voice-calls-button").prop("disabled",!0),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:block;")):($("#send-bulk-voice-calls-button").prop("disabled",!1),$("#insufficient-voice-call-credits-message").attr("style","color:red; display:none;"));break}}})},bulkDemandNoticeConfirmModal:function(){var e=$("input.bulk-student-select-checkbox:checkbox:checked").length;if(0!=e){var t='<span style="color:red">(FINE AMOUNT NOT INCLUDED)</span>',a="Yes, Fee Notice Without Fine";$("#include-fee-fine").is(":checked")&&(t='<span style="color:green">(FINE AMOUNT INCLUDED)</span>',a="Yes, Fee Notice With Fine"),$("#demand-notice-confirm-button").html(a),$("#bulk-demand-notice-confirmation-modal-text").html("You have selected "+e+" students to generate fee notice. Do you want to proceed? <br>"+t),$("#bulk-demand-notice-confirmation-modal").modal({backdrop:"static",keyboard:!1})}else showErrorDialogBox("Please select atleast one student for generating fee notice.")},sendBulkSMSReminders:function(){if(reminders.dataCache.selectedStudentCount>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count)showErrorDialogBox("You do not have sufficient credits to send "+reminders.dataCache.selectedStudentCount+" SMS. Please recharge.");else{var e=$("#sms-reminder-fee-batch-name").val();if(null!=e&&""!=e){$("#bulk-reminder-confirmation-modal").modal("toggle");var t=!1;$("#include-fee-fine").is(":checked")&&(t=!0);var a=!1;$("#include-past-sessions").is(":checked")&&(a=!0);var n=reminders.getSelectedBulkStudentIds(),i={batchName:e,dueDate:reminders.dataCache.sendReminders.dueFeesDate,computeFine:t,academicSessionId:null,deliveryMode:"SMS",requiredStanardsCSV:reminders.dataCache.sendReminders.requiredStandards,studentIds:n,includePastSessions:a};ajaxClient.post("/fees/send-due-payment-reminders",{sendRemindersPayload:JSON.stringify(i)},function(e){$("#fee-reminder-status-modal-container").html(e),$("#fee-reminder-status-modal").modal("toggle")})}else showErrorDialogBox("Please give batch name to identify the sent reminders.")}},sendAudioVoiceCall:function(){if(reminders.dataCache.selectedStudentCount*reminders.dataCache.selectedAudioCredits>channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count)showErrorDialogBox("You do not have sufficient credits to send "+reminders.dataCache.selectedStudentCount+" voice calls. Please recharge.");else{var e=$("#voice-reminder-fee-batch-name").val();if(null!=e&&""!=e){var t=!1;$("#include-fee-fine").is(":checked")&&(t=!0);var a=!1;$("#include-past-sessions").is(":checked")&&(a=!0);var n=$("#audio-template-select").find(":selected").val().trim();if(""!=n){$("#bulk-reminder-confirmation-modal").modal("toggle");var i=reminders.getSelectedBulkStudentIds(),o={batchName:e,dueDate:reminders.dataCache.sendReminders.dueFeesDate,computeFine:t,academicSessionId:null,deliveryMode:"CALL",requiredStanardsCSV:reminders.dataCache.sendReminders.requiredStandards,studentIds:i,audioTemplateId:n,includePastSessions:a};ajaxClient.post("/fees/send-due-payment-reminders",{sendRemindersPayload:JSON.stringify(o)},function(e){$("#fee-reminder-status-modal-container").html(e),$("#fee-reminder-status-modal").modal("toggle")})}else showErrorDialogBox("Please select audio clip for voice call")}else showErrorDialogBox("Please give batch name to identify the sent reminders.")}},sendMobileAppNotifications:function(){var e=!1;$("#include-fee-fine").is(":checked")&&(e=!0);var t=!1;$("#include-past-sessions").is(":checked")&&(t=!0);$("#bulk-reminder-confirmation-modal").modal("toggle");var a=reminders.getSelectedBulkStudentIds(),n={batchName:null,dueDate:reminders.dataCache.sendReminders.dueFeesDate,computeFine:e,academicSessionId:null,deliveryMode:"APP",requiredStanardsCSV:reminders.dataCache.sendReminders.requiredStandards,studentIds:a,audioTemplateId:null,includePastSessions:t};ajaxClient.post("/fees/send-due-payment-reminders",{sendRemindersPayload:JSON.stringify(n)},function(e){$("#fee-reminder-status-modal-container").html(e),$("#fee-reminder-status-modal").modal("toggle")})},generateDemandNotice:function(){$("#bulk-demand-notice-confirmation-modal").modal("toggle");var e=!1;$("#include-fee-fine").is(":checked")&&(e=!0);var t=!1;$("#include-past-sessions").is(":checked")&&(t=!0);for(var a=reminders.getSelectedBulkStudentIds(),n=reminders.dataCache.sendReminders.dueFeesDate,i=reminders.dataCache.sendReminders.requiredStandards,o=academicSessionHandler.getSelectedSessionId(),s="",d=0;d<a.length;d++)0==d?s+=a[d]:s=s+","+a[d];demandNoticePayload={academicSessionId:o,dueFeesDate:n,includeFine:e,requiredStanardsCSV:i,students:a,includePastSessions:t},ajaxClient.post("/fees/submit-demand-notice-students",{demandNoticePayload:JSON.stringify(demandNoticePayload)},function(e){window.open(baseURL+"/fees/generate-demand-notice/"+e,"_blank")})},getSelectedBulkStudentIds:function(){var e=[];return $("input.bulk-student-select-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-reminder-student-id").first().text().trim();e.push(t)}}),e},loadNotificationHistoryHomePage:function(){ajaxClient.get("/fees/notification-history-homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(reminders.changeSession),reminders.getNotificationHistory(!0,"SMS","BATCH"),reminders.dataCache.notificationHistoryTab="BATCH"})},changeSession:function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!0,t,e)},getNotificationHistory:function(e,t,a){reminders.dataCache.notificationHistoryTab=a,reminders.dataCache.deliveryModeTab=t;var n=$(".page-item.active").find(".page-number").text().trim();e&&(n=1);var i=$("#items-per-page").val();null!=n&&""!=n||(n=1);var o=(n-1)*i,s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/notification-history/"+s+"/"+t+"/"+a+"/"+o+"/"+i,function(e){var i=0;"SMS"==t?($("#smsNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50):"CALL"==t?($("#voiceNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50):"APP"==t&&($("#mobileNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50),$(".notification-history-fixed-height-list-wrapper").attr("style","height:"+i+"px;  overflow-y: scroll; cursor: pointer;");var o=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(o.itemsPerPage),$(".page-item").removeClass("active");o.offset,o.itemsPerPage;$("#page-number-"+n).addClass("active"),"INDIVIDUAL"==a&&($("#nav-individual-history-tab").addClass("active"),$("#nav-batch-history-tab").removeClass("active"),$("#individual-history-content").addClass("show active"),$("#batch-history-content").removeClass("show active")),reminders.bindIndividualNotificationsContentView(),reminders.initPagination()})},initPagination:function(){pagination.bindEvents(function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!1,t,e)},function(){var e=reminders.dataCache.notificationHistoryTab,t=reminders.getDeliveryModeTab();reminders.getNotificationHistory(!0,t,e)})},getDeliveryModeTab:function(){var e=reminders.dataCache.deliveryModeTab;return null==e||""==e?"SMS":e},loadBatchNotificationsDetails:function(e){ajaxClient.get("/fees/batch-notification-details/"+e,function(e){$("#batch-notification-detail-modal-container").html(e),$("#view-batch-notification-modal").modal("toggle")})},bindIndividualNotificationsContentView:function(){$(".view-individual-notification").click(function(){var e=$(this).parent().find("p.notification-content").text().trim();$("#individual-notification-detail-modal").find(".modal-body").html('<span style="white-space: pre-line;">'+e+"</span>"),$("#individual-notification-detail-modal").modal("toggle")})},loadReminderStudentFollowUpDetails:function(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/fees/get-follow-up-remainder-details/"+t+"/"+e,function(e){$("#fee-reminder-screen").html(e),$("#academic-session").attr("style","display:none;")})},closeReminderFeeFollowUpDetails:function(e){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Close All Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong style="color:red">Are you sure that you want to close the all followup trail for this student ?</strong>'),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="reminders.closeStudentRemainderFeeFollowupTrail(\'true\',\'INACTIVE\')">Yes, Close All</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("#all-fee-follow-reminder-up-close-modal").modal("toggle")},closeStudentRemainderFeeFollowupTrail:function(e,t){$("#all-fee-follow-reminder-up-close-modal").modal("toggle");var a=academicSessionHandler.getSelectedSessionId(),n=$("#delete-student-follow-up-id").text().trim(),i=n;""!=n?ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+a+"/"+e+"/"+t+"/"+n+"/"+i,{},function(e){$("#fee-payment-reminder-student-detail-modal-container").html(e),$("#fee-reminder-follow-up-student-status-modal").modal({backdrop:"static",keyboard:!1}),reminders.loadReminderStudentFollowUpDetails()}):showErrorDialogBox("Invalid Follow Up to cancel")},closeFeeReminderFollowUpDetails:function(e,t,a){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Close Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html("<strong>Are you sure that you want to close this followup trail ?</strong>"),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="reminders.closeStudentReminderSingleFeeFollowupTrail(\'false\',\'INACTIVE\')">Yes, Close It</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#all-fee-follow-reminder-up-close-modal").modal("toggle")},closeStudentReminderSingleFeeFollowupTrail:function(e,t){var a=academicSessionHandler.getSelectedSessionId(),n=$("#delete-student-follow-up-id").text().trim(),i=$("#student-follow-up-id").text().trim();""!=n?($("#all-fee-follow-reminder-up-close-modal").modal("toggle"),ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+a+"/"+e+"/"+t+"/"+n+"/"+i,{},function(e){$("#fee-payment-reminder-student-detail-modal-container").html(e),$("#fee-reminder-follow-up-student-status-modal").modal({backdrop:"static",keyboard:!1}),reminders.loadReminderStudentFollowUpDetails()})):showErrorDialogBox("Invalid Follow Up to cancel")},addFeeReminderFollowUpDetails:function(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3),$("p#student-follow-up-id").text(e),$("#next-transaction-fee-follow-up-date").val(""),$("#add-fee-reminder-follow-up-modal").modal("toggle")},backButton:function(){var e=academicSessionHandler.getSelectedSessionId(),t=reminders.dataCache.dueDate,a=reminders.dataCache.includeFine,n=reminders.dataCache.includePastSessions,i=reminders.dataCache.requiredStandards;reminders.loadBulkStudentScreen(!0,e,t,a,n,i)},addReminderStudentFeeFollowupTrail:function(e){if(!validateMandatoryFields($("#add-fee-reminder-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#entity-transaction-name").val();if(null!=(n=getDate($("#fee-transaction-follow-up-date").val()))){var n=n.getTime()/1e3,i=$("#contact-person-transaction-name").val(),o=$("select#follow-up-transaction-type").val(),s=$("#transaction-conversation").val(),d=$("#next-transaction-fee-follow-up-date").val();if(""!=d&&(d=(d=getDate($("#next-transaction-fee-follow-up-date").val())).getTime()/1e3),null!=d&&""!=d||(d=null),null!=d&&n>d)return $("#add-fee-reminder-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#amount-transaction").val(),r=e,c=$("#student-follow-up-id").text(),u={entityId:c,entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#add-fee-reminder-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/add-fee-reminder-trail/"+t+"/"+c,{followUpPayload:JSON.stringify(u)},function(e){$("#fee-payment-reminder-student-detail-modal-container").html(e),$("#fee-reminder-follow-up-student-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Please fill follow up date.")}},editReminderFeeFollowUpDetails:function(e){initDateWithYearRange("-10:+10",!0),initPastDateById("fee-transaction-follow-up-date",4e3),initPastDateById("fee-follow-up-date",4e3);var t=JSON.parse($(e).parent().find("#follow-up-json").text().trim());$("#update-transaction-entity-name").html("<option>"+t.entityName+"</option>"),$("#update-transaction-fee-follow-up-date").val(getFormattedDate(t.followUpDate)),$("#update-transaction-contact-person-name").val(t.contactPersonName),$("#update-transaction-follow-up-type").html("<option>"+t.followUpType+"</option>"),$("#update-transaction-conversation").val(t.conversation),null!=t.nextFollowUpDate?$("#update-transaction-next-fee-follow-up-date").val(getFormattedDate(t.nextFollowUpDate)):$("#update-transaction-next-fee-follow-up-date").val(""),$("#update-transaction-amount").val(t.amount),$("#update-student-follow-up-id").text(t.followUpId),$("#student-id").text(t.entityId),$("#update-reminder-fee-follow-up-modal").modal("toggle")},updateStudentReminderFeeFollowupTrail:function(e){if(!validateMandatoryFields($("#update-reminder-fee-follow-up-modal"))){var t=academicSessionHandler.getSelectedSessionId(),a=$("select#update-transaction-entity-name").val();if(null!=(n=getDate($("#update-transaction-fee-follow-up-date").val()))){var n=n.getTime()/1e3,i=($("select#update-transaction-follow-up-mode").val(),$("#update-transaction-contact-person-name").val()),o=$("select#update-transaction-follow-up-type").val(),s=$("#update-transaction-conversation").val(),d=$("#update-transaction-next-fee-follow-up-date").val();if(""!=d&&(d=(d=getDate($("#update-transaction-next-fee-follow-up-date").val())).getTime()/1e3),null!=d&&""!=d||(d=null),null!=d&&n>d)return $("#update-reminder-fee-follow-up-modal").modal("toggle"),void showErrorDialogBox("Follow up date cannot exceed the next follow up date");var l=$("#update-transaction-amount").val(),r=e,c=$("#update-student-follow-up-id").text(),u=$("#student-id").text(),f={entityId:u,followUpId:c,entityName:a,followUpDate:n,followUpMode:"MANUAL",contactPersonName:i,conversation:s,nextFollowUpDate:d,amount:l,followUpType:o,followUpStatus:r};$("#update-reminder-fee-follow-up-modal").modal("toggle"),ajaxClient.post("/fees/update-reminder-followup-trail/"+t+"/"+u,{updateFollowUpPayload:JSON.stringify(f)},function(e){$("#fee-payment-reminder-student-detail-modal-container").html(e),$("#fee-reminder-follow-up-student-status-modal").modal({backdrop:"static",keyboard:!1}),reminders.loadReminderStudentFollowUpDetails()})}else showErrorDialogBox("Please fill follow up date.")}},deleteReminderFeeFollowUpDetails:function(e,t){$("#modal-header").html('<h5 class="modal-title" id="exampleModalLongTitle">Delete Fee Follow Up Trail</h5><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'),$("#modal-body").html('<strong color="red">Are you sure that you want to Delete this followup trail ?</strong>'),$("#modal-footer").html('<p style="display:none" id="delete-student-follow-up-id"></p><p style="display:none" id="student-follow-up-id"></p><button type="button" class="btn btn-danger" onclick="reminders.deleteStudentReminderFeeFollowupTrail()">Yes, Delete</button><button type="button" class="btn btn-secondary" data-dismiss="modal"> Cancel</button>'),$("p#delete-student-follow-up-id").text(e),$("p#student-follow-up-id").text(t),$("#all-fee-follow-reminder-up-close-modal").modal("toggle")},deleteStudentReminderFeeFollowupTrail:function(){var e=academicSessionHandler.getSelectedSessionId(),t=$("#delete-student-follow-up-id").text().trim(),a=$("#student-follow-up-id").text().trim();""!=t?($("#all-fee-follow-reminder-up-close-modal").modal("toggle"),ajaxClient.post("/fees/delete-fee-reminder-followup-trail/"+e+"/"+t+"/"+a,{},function(e){$("#fee-payment-reminder-student-detail-modal-container").html(e),$("#fee-reminder-follow-up-student-status-modal").modal({backdrop:"static",keyboard:!1}),reminders.loadReminderStudentFollowUpDetails()})):showErrorDialogBox("Invalid Follow Up to cancel")}},studentWalletHandler={dataCache:{},confirmWalletRecharge:function(){var e=$("#wallet-recharge-student-id").text().trim(),t=$("#wallet-recharge-student-name-identifier").text().trim();if(!validateMandatoryFields($("#student-wallet-recharge-modal").find(".modal-body"))){var a=getFloatValue($("#student-recharge-wallet-amount").val());if(a<=0)return $("#student-recharge-wallet-amount").css("border","1px solid #ff8795"),void $("#student-recharge-wallet-amount").after('<p class="mandatory-field-text"> <span style="color:#e65f76;">Wallet amount should be > 0</span></p>');var n=$("#student-recharge-wallet-description").val(),i=$("#student-recharge-payment-mode").find(":selected").val().trim(),o=getDate($("#student-rechage-wallet-date").val());studentWalletHandler.dataCache.studentId=e,studentWalletHandler.dataCache.studentNameIdentifier=t,studentWalletHandler.dataCache.amount=a,studentWalletHandler.dataCache.description=n,studentWalletHandler.dataCache.paymentMode=i,studentWalletHandler.dataCache.transactionDate=o.getTime()/1e3,$("#student-wallet-recharge-modal").modal("toggle");var s="Do you want to recharge wallet of student "+t+" with amount = "+a+"/- ?";$("#student-wallet-recharge-confirm-modal").find(".student-wallet-recharge-confirm-text").text(s),$("#student-wallet-recharge-confirm-modal").modal({backdrop:"static",keyboard:!1})}},rechargeWallet:function(){$("#student-wallet-recharge-confirm-modal").modal("toggle");var e={studentId:studentWalletHandler.dataCache.studentId,amount:studentWalletHandler.dataCache.amount,description:studentWalletHandler.dataCache.description,transactionMode:studentWalletHandler.dataCache.paymentMode,transactionDate:studentWalletHandler.dataCache.transactionDate};ajaxClient.post("/fees/recharge-student-wallet",{walletRechargePayload:JSON.stringify(e)},function(e){$("#student-wallet-recharge-status-modal-container").html(e),$("#student-wallet-recharge-status-modal").modal("toggle"),loadStudentPaymentDetails(studentWalletHandler.dataCache.studentId)})},loadWalletTransactions:function(){var e=$("#wallet-recharge-student-id").text().trim();ajaxClient.get("/fees/wallet-transactions/"+e,function(e){$("#student-wallet-transactions-modal-container").html(e),$("#student-wallet-transactions-modal").modal({backdrop:"static",keyboard:!1})})}};function roundOffNumber(e,t){return parseFloat(e.toFixed(t))}function sortMapByValue(e){var t=Object.keys(e).map(function(t){return[t,e[t]]});t.sort(function(e,t){return e[1]-t[1]});var a={};return t.forEach(function(e){a[e[0]]=e[1]}),a}function validateSiblingPaymentAmounts(){var e=parseFloat($("#sibling-total-balance-amount-display").text()),t=parseFloat($("#sibling-student-paid-amount").text());parseFloat($("#sibling-total-instant-discount-amount").text());if(""==e||0==e)return showErrorDialogBox("No amount added to collect fees."),!0;if(""==t||0==t)return!confirm("You are about to make payment with 0 paid amount. Press cancel to go back.");var a=!1,n="";return $("tr.sibling-payment-details-tr").each(function(){var e=JSON.parse($(this).find(".sibling-due-fee-detail").text()).studentFullName,t=$(this).find(".sibling-student-paid-amount").val(),i=$(this).find(".sibling-student-net-payable-amount").val();parseFloat(t)>parseFloat(i)&&(""!==n&&(n+="<br/>"),n+="For "+e+" paid amount cannot be greater than net payable amount!",a=!0)}),a&&showErrorDialogBox(n),a}pastReportAllowedDays=365;var ALL="ALL",ONLY_DUE_STUDENTS="ONLY_DUE_STUDENTS",ONLY_NON_DUE_STUDENTS="ONLY_NON_DUE_STUDENTS",feeReports={dataCache:{},loadReportsMenu:function(){ajaxClient.get("/fees/reports",function(e){if($("#main-content").html(e),initDateWithYearRange("-5:+5",!0),initSelect2("All"),feeReports.initDataCache(),feeReports.initMultiSelectAcademicSession(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),feeReports.bindGenerateReportEvent(),feeReports.checkboxEvents(),reportUtils.bindSelectClassCheckboxEvent(),feeReports.dataCache.allSessions.length>0){var t=feeReports.getFeesDropDown(feeReports.dataCache.allSessions[0].academicSessionId);$(".reports-student-fees").html(t)}})},initMultiSelectAcademicSession:function(e=""){$(".select2-multi-academic-session").each(function(){$(this).wrap('<div class="position-relative"></div>').select2({placeholder:e,dropdownParent:$(this).parent(),closeOnSelect:!1})})},initDataCache:function(){var e=readJson("#all-session-fees"),t=readJson("#all-sessions"),a=readJson("#selected-academic-session-json");feeReports.dataCache.allSessionFees=e,feeReports.dataCache.allSessions=t,$(".report-academic-session").val(a.academicSessionId)},getRequiredFeesCSV:function(e){$(".reports-student-fees").val().join()},getFeesDropDown:function(e){var t=feeReports.dataCache.allSessionFees[e],a="";if(null==t||null==t)return a;for(var n=0;n<t.length;n++)a+='<option value="'+t[n].feeConfigurationBasicInfo.feeId+'">'+t[n].feeConfigurationBasicInfo.feeName+"</option>";return a},checkboxEvents:function(){$(".all-column-select").on("change",function(){const e=$(this).closest(".report-header-container").find(".select-report-column");$(this).is(":checked")?e.prop("checked",!0):e.each(function(){$(this).is(":disabled")||$(this).prop("checked",!1)})})},bindGenerateReportEvent:function(){$(".report-academic-session").change(function(){var e=$(this).find(":selected").val().trim();if(void 0!==e&&""!==e&&null!==e){var t=feeReports.getFeesDropDown(e);$(this).closest(".modal-body").find(".reports-student-fees").html(t),ajaxClient.get("/fees/standards/"+e,function(e){$(".standard-with-section-options-wrapper").html(e),initSelect2("All")})}}),$(".reports-fee-report-data-type").on("change",function(){var e=$(this).find(":selected").val().trim();e===ALL?$(this).parent().parent().parent().find("#fee-due-date-div").attr("style","display:none;"):e===ONLY_DUE_STUDENTS?$(this).parent().parent().parent().find("#fee-due-date-div").attr("style","display:inline-block;"):e===ONLY_NON_DUE_STUDENTS&&$(this).parent().parent().parent().find("#fee-due-date-div").attr("style","display:inline-block;")}),$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container"),t=reportUtils.getReportHeadersCSV(e);if(!validateMandatoryFields($(e))){var a="";$(e).find(".report-academic-session option:selected").length>0&&(a=$(e).find(".report-academic-session option:selected").val()),a=""===a?0:a;var n="";$(e).find(".report-multi-academic-sessions").length>0&&(n=$(e).find(".report-multi-academic-sessions").val().join());var i="";$(e).find(".reports-transaction-mode ").length>0&&(i=$(e).find(".reports-transaction-mode ").val());var o=i.toString(),s=$(e).find("p.report-type").text().trim(),d=-1,l=-1,r=-1;$(e).find("input.report-start-date").length>0&&(d=getDate($(e).find("input.report-start-date").val()).getTime()/1e3),$(e).find("input.report-end-date").length>0&&(l=getDate($(e).find("input.report-end-date").val()).getTime()/1e3);var c="";"STUDENT_LEDGER"===s?c=$(e).find(".reports-student-class").val():$(e).find(".reports-student-class").length>0&&(c=$(e).find(".reports-student-class").val().join());var u="";$(e).find(".reports-student-fees").length>0&&(u=$(e).find(".reports-student-fees").val().join());var f="";$(e).find(".reports-student-status").length>0&&(f=$(e).find(".reports-student-status").val().join());var m="";$(e).find(".reports-student-fee-heads").length>0&&(m=$(e).find(".reports-student-fee-heads").val().join());var p=$(e).find(".transaction-type").val();p=void 0===p?"":p.trim(),$(e).find("input.fee-due-date").length>0&&(feeDueDateObj=getDate($(e).find("input.fee-due-date").val()),null!=feeDueDateObj&&(r=feeDueDateObj.getTime()/1e3));var g="";if($(e).find(".reports-fee-report-data-type option:selected").length>0)if((g=$(e).find(".reports-fee-report-data-type option:selected").val())===ALL)r=-1;else if(g===ONLY_DUE_STUDENTS){if(r<=0)return void $(e).find("input.fee-due-date").css("border","1px solid #ff8795")}else if(g===ONLY_NON_DUE_STUDENTS&&r<=0)return void $(e).find("input.fee-due-date").css("border","1px solid #ff8795");var h="";$(e).find(".reports-show-fee-head option:selected").length>0&&(h=$(e).find(".reports-show-fee-head option:selected").val());var v=$(e).find("input.students-with-zero-fees-assignment").is(":checked"),S=$(e).find("input.student-with-zero-wallet-balance").is(":checked");$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/fees/generate-report/"+s+"?academic_session_id="+a+"&requiredHeaders="+t+"&requiredMultiSession="+n+"&reportStartDate="+d+"&reportEndDate="+l+"&fee_ids="+u+"&requiredStandards="+c+"&feeDueDate="+r+"&studentStatus="+f+"&fee_head_ids="+m+"&transactionType="+p+"&feeReportDataType="+g+"&transactionMode="+o+"&hideStudentWithZeroFeesAssignment="+v+"&showStudentWithZeroWalletBalance="+S+"&reportDataVisibility="+h,"_blank")}})}};