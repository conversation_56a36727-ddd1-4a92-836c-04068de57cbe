SCHOLASTIC_COURSE_TYPE="SCHOLASTIC",COSCHOLASTIC_COURSE_TYPE="COSCHOLASTIC",GRAPH_VIEW="GRAPH_VIEW",TREE_VIEW="TREE_VIEW",REPORT_CARD_VARIABLES="REPORT_CARD_VARIABLES",PERSONALITY_TRAITS="PERSONALITY_TRAITS";var ONE_KB=1024,FILE_SIZE_LIMIT=50,examinationConfig={dataCache:{},loadExamConfigurationPage:function(){ajaxClient.get("/examination/configure-exam",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(examinationConfig.resetPage),examinationConfig.bindClassChangeEvent(),examinationConfig.bindExamViewChangeEvent()})},resetPage:function(){var e=$("#exam-config-class").find(":selected").val().trim();""!=e&&examinationConfig.loadClassExamConfigurationPage(e)},loadExamConfigurationPageForStandard:function(e){var a=$("#exam-list-view-p").text();ajaxClient.get("/examination/configure-exam",function(t){$("#main-content").html(t),$("#exam-config-class").val(e),$("#exam-list-view").val(a),academicSessionHandler.bindSessionChangeEvent(examinationConfig.resetPage),examinationConfig.bindClassChangeEvent(),examinationConfig.bindExamViewChangeEvent(),examinationConfig.loadClassExamConfigurationWithViewTypePage(e,a)})},bindClassChangeEvent:function(){$("#exam-config-class").change(function(){var e=$(this).find(":selected").val().trim();""!=e&&examinationConfig.loadClassExamConfigurationPage(e)})},bindExamViewChangeEvent:function(){$("#exam-list-view").change(function(){var e=$("#exam-config-class").find(":selected").val().trim();""!=e&&examinationConfig.loadClassExamConfigurationPage(e)})},loadClassExamConfigurationPage:function(e){var a=$("#exam-list-view").val();examinationConfig.loadClassExamConfigurationWithViewTypePage(e,a)},loadClassExamConfigurationWithViewTypePage:function(e,a){a===GRAPH_VIEW?examinationConfig.loadClassExamGraphConfigurationPage(e):examinationConfig.loadClassExamTreeConfigurationPage(e)},loadClassExamTreeConfigurationPage:function(e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/exam-forest/"+e+"/"+a,function(e){if($("#tree-container").html(e),0!=readJson("#class-exam-tree-view").length){var a=0;$("div.tree-view-exam-name-div").each(function(){if(1==++a)return $(this).attr("style","padding:.75rem!important"),$(this).parent().attr("style","padding-left:0px!important;"),$(this).find(".arrow-icon").remove(),void $(this).find(".tree-view-exam-name").attr("style","display:inline-block;font-size:16px;font-weight:bold;");a%2==0?$(this).attr("style","background-color:#ecf5f6;padding:.75rem!important;display: table;width:100%;padding-top:0rem!important;"):$(this).attr("style","background-color:#ffffff;padding:.75rem!important;display: table;width:100%;padding-top:0rem!important;")}),initDateWithYearRange("-5:+5")}else showErrorDialogBox("No exam configured. Please contact support to create initial exam structure.")})},loadClassExamGraphConfigurationPage:function(e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/exam-forest-graph/"+e+"/"+a,function(e){$("#tree-container").html(e);var a=readJson("#class-exam-forest");0!=a.length?examTree.render(a[0]):showErrorDialogBox("No exam configured. Please contact support to create initial exam structure.")})}},examConfig={dataCache:{},addExamLitePopUp:function(e){var a=$(e).parent().parent().find(".exam-metadata-details-json").text();JSON.parse(a);$("#add-class-child-exam-name").val(""),$("#add-exam-lite-modal").modal("toggle"),$("#exam-metadata-details").text(a)},addExamLite:function(e){var a=JSON.parse($("#exam-metadata-details").text()),t=a.academicSessionId,n=a.standardId,i=$("#add-class-child-exam-name").val().trim(),s=a.examId,o={academicSessionId:t,standardId:n,examName:i,parentExamId:s,examType:"REGULAR",courseTypeDimensions:null};$("#add-exam-lite-modal").modal("toggle"),ajaxClient.post("/examination/create-exam/true",{examCreationPayload:JSON.stringify(o)},function(e){$("#global-modal-container").html(e),$("#exam-creation-status-modal").modal({backdrop:"static",keyboard:!1}),examinationConfig.loadClassExamConfigurationPage(n)})},updateExamLitePopUp:function(e){var a=$(e).parent().parent().find(".exam-metadata-details-json").text(),t=JSON.parse(a);$("#update-exam-lite-modal").find("#update-class-exam-name-lite").val(t.examName),$("#update-exam-lite-modal").find("#update-exam-operation-lite").val(t.operation),$("#update-exam-lite-modal").find("#attendance-start-date-lite").val(getFormattedDate(t.attendanceStartDate)),$("#update-exam-lite-modal").find("#attendance-end-date-lite").val(getFormattedDate(t.attendanceEndDate)),$("#update-exam-lite-modal").find("#exam-start-date-lite").val(getFormattedDate(t.examStartDate)),$("#update-exam-lite-modal").find("#date-of-result-declaration-lite").val(getFormattedDate(t.dateOfResultDeclaration)),$("#update-exam-lite-modal").modal("toggle"),$("#update-exam-metadata-details").text(a)},updateExamLite:function(){$("#update-exam-lite-modal").modal("toggle");var e=JSON.parse($("#update-exam-metadata-details").text()),a=e.examId,t=e.standardId,n=$("#update-exam-lite-modal").find("#update-class-exam-name-lite").val();if(null!=n&&""!=n.trim()){var i=$("#update-exam-lite-modal").find("#update-exam-operation-lite").val();if(null!=i&&""!=i.trim()&&void 0!==i){var s=getDate($("#attendance-start-date-lite").val()),o=null;null!=s&&(o=s.getTime()/1e3);var r=getDate($("#attendance-end-date-lite").val()),d=null;null!=r&&(d=r.getTime()/1e3);var l=getDate($("#exam-start-date-lite").val()),c=null;null!=l&&(c=l.getTime()/1e3);var m=getDate($("#date-of-result-declaration-lite").val()),u=null;null!=m&&(u=m.getTime()/1e3);var p={examName:n,operation:i,attendanceStartDate:o,attendanceEndDate:d,examStartDate:c,dateOfResultDeclaration:u};ajaxClient.post("/examination/update-exam-metadata/"+a+"/true",{updateExamMetadataPayload:JSON.stringify(p)},function(e){$("#global-modal-container").html(e),$("#exam-update-status-modal").modal({backdrop:"static",keyboard:!1}),examinationConfig.loadClassExamConfigurationPage(t)})}else showErrorDialogBox("Invalid operation selected.")}else showErrorDialogBox("Invalid exam name provided.")},deleteExamLitePopUp:function(e){var a=$(e).parent().parent().find(".exam-metadata-details-json").text();JSON.parse(a);$("#delete-exam-lite-modal").modal("toggle"),$("#delete-exam-metadata-details").text(a)},deleteExamLite:function(){$("#delete-exam-lite-modal").modal("toggle");var e=JSON.parse($("#delete-exam-metadata-details").text()),a=e.examId,t=e.standardId;ajaxClient.post("/examination/delete-exam/"+a+"/"+t+"/true",{},function(e){$("#global-modal-container").html(e),$("#exam-delete-status-modal").modal({backdrop:"static",keyboard:!1}),examinationConfig.loadClassExamConfigurationPage(t)})},expandCollaspeChildExams:function(e){$(e).hasClass("bi-chevron-right")?($(e).removeClass("bi-chevron-right"),$(e).addClass("bi-chevron-down"),$(e).parent().parent().find("ul").attr("style","list-style-type: none;display:block;")):($(e).removeClass("bi-chevron-down"),$(e).addClass("bi-chevron-right"),$(e).parent().parent().find("ul").attr("style","list-style-type: none;display:none;"))}},examDetailsNamespace={dataCache:{},loadExamDetailsPage:function(e){var a=$("#exam-list-view").val();""!==a&&null!=a||(a=$("#exam-list-view-p").text()),ajaxClient.get("/examination/exam-details/"+e,function(e){$("#main-content").html(e),examDetailsNamespace.dataCache={};var t=readJson("#exam-details-json"),n=readJson("#dimensions-json"),i=readJson("#class-courses-json");examDetailsNamespace.dataCache.examDetails=t,examDetailsNamespace.dataCache.dimensions=n,examDetailsNamespace.dataCache.classCourses=i,$("#exam-list-view-p").text(a),initDateWithYearRange("-5:+5")})},populateCreateExamModal:function(){var e=examDetailsNamespace.dataCache.examDetails,a=e.academicSession.yearDisplayName;$("#add-class-exam-session-display").val(a);var t=e.standard.displayName;$("#add-class-exam-class-name").val(t);for(var n=examDetailsNamespace.dataCache.dimensions,i="",s=0;s<n.length;s++)if(e.standardsMetaData.scholasticGradingEnabled){if("NUMBER"==n[s].examEvaluationType)continue;if(n[s].total){i+='<tr id="'+n[s].dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td>  <td> "+n[s].examEvaluationType+" </td> </tr>";break}}else{if("GRADE"==n[s].examEvaluationType)continue;if(n[s].total){i+='<tr id="'+n[s].dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td> <td> "+n[s].examEvaluationType+" </td> </tr>";continue}i+='<tr id="'+n[s].dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch"> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td> <td> "+n[s].examEvaluationType+" </td> </tr>"}$("#add-class-exam-scholastic-dimensions-input-div").find("tbody").html(i),i="";for(s=0;s<n.length;s++)if(e.standardsMetaData.coScholasticGradingEnabled){if("NUMBER"==n[s].examEvaluationType)continue;if(n[s].total){i+='<tr id="'+n[s].dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td>  <td> "+n[s].examEvaluationType+" </td> </tr>";break}}else{if("GRADE"==n[s].examEvaluationType)continue;if(n[s].total){i+='<tr id="'+n[s].dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td> <td> "+n[s].examEvaluationType+" </td> </tr>";continue}i+='<tr id="'+n[s].dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch"> <span class="slider round"></span> </label> </td> <td> '+n[s].dimensionName+" </td> <td> "+n[s].examEvaluationType+" </td> </tr>"}$("#add-class-exam-coscholastic-dimensions-input-div").find("tbody").html(i)},createExam:function(){$("#add-class-exam-modal").modal("toggle");var e=examDetailsNamespace.dataCache.examDetails,a=e.academicSession.academicSessionId,t=e.standard.standardId,n=$("#add-class-exam-name").val().trim(),i=e.examMetaData.examId,s=$("#add-exam-operation").val();if(null!=s&&""!=s.trim()&&void 0!==s){var o=$("#add-exam-sch-display-type").val(),r=$("#add-exam-cosch-display-type").val(),d=getDate($("#add-attendance-start-date").val()),l=null;null!=d&&(l=d.getTime()/1e3);var c=getDate($("#add-attendance-end-date").val()),m=null;null!=c&&(m=c.getTime()/1e3);var u=getDate($("#add-exam-start-date").val()),p=null;null!=u&&(p=u.getTime()/1e3);var h=getDate($("#add-date-of-result-declaration").val()),f=null;null!=h&&(f=h.getTime()/1e3);var g=[];$("tr.scholastic-dimension-row").each(function(){if($(this).find(".toggle-switch:checkbox:checked").length>0){var e=$(this).attr("id").split("-")[0],a=$(this).find(".max-default-value").val(),t=$(this).find(".min-default-value").val();g.push({dimensionId:e,maxMarks:a,minMarks:t})}});var v=[];$("tr.coscholastic-dimension-row").each(function(){if($(this).find(".toggle-switch:checkbox:checked").length>0){var e=$(this).attr("id").split("-")[0],a=$(this).find(".max-default-value").val(),t=$(this).find(".min-default-value").val();v.push({dimensionId:e,maxMarks:a,minMarks:t})}});var x={academicSessionId:a,standardId:t,examName:n,parentExamId:i,examType:"REGULAR",operation:s,attendanceStartDate:l,attendanceEndDate:m,examStartDate:p,dateOfResultDeclaration:f,scholasticExamMarksDisplayType:o,coScholasticExamMarksDisplayType:r,courseTypeDimensions:{SCHOLASTIC:g,COSCHOLASTIC:v}};ajaxClient.post("/examination/create-exam/false",{examCreationPayload:JSON.stringify(x)},function(e){$("#global-modal-container").html(e),$("#exam-creation-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Invalid operation selected.")},getCourseMaxGradeRow:function(e,a,t){var n="";t&&(n="checked"),courseMarksRow=' <tr class="exam-course-update-row" id="'+e.courseId+'"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch child" '+n+'> <span class="slider round"></span> </label> </td> <td>'+e.courseName+"</td>";for(var i=0;i<a.length;i++)if("NUMBER"!=a[i].examDimension.examEvaluationType&&a[i].examDimension.total){courseMarksRow+='<td class="exam-dimension" id="'+a[i].examDimension.dimensionId+'"> Grade</td>';break}return courseMarksRow+="</tr>",courseMarksRow},getCourseMaxMarksRow:function(e,a,t){var n="";t&&(n="checked"),courseMarksRow=' <tr class="exam-course-update-row" id="'+e.courseId+'"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch child" '+n+'> <span class="slider round"></span> </label> </td> <td>'+e.courseName+"</td>";for(var i=0;i<a.length;i++)if("GRADE"!=a[i].examDimension.examEvaluationType){var s="",o="",r="readonly";t&&(r="",s="value="+a[i].maxMarks,o="value="+a[i].minMarks),a[i].examDimension.total?courseMarksRow+='<td class="exam-dimension" id="'+a[i].examDimension.dimensionId+'"> <input type="number" class="form-control form-control-sm max-marks-value input-max-marks-'+a[i].examDimension.dimensionId+'-value total-marks" placeholder="Max Marks..." '+s+' readonly></td>         <td class="exam-dimension" id="'+a[i].examDimension.dimensionId+'"> <input type="number" class="form-control form-control-sm min-marks-value input-min-marks-'+a[i].examDimension.dimensionId+'-value total-marks" placeholder="Min Marks..." '+o+" readonly></td>":courseMarksRow+='<td class="exam-dimension" id="'+a[i].examDimension.dimensionId+'"> <input type="number" class="form-control form-control-sm max-marks-value input-max-marks-'+a[i].examDimension.dimensionId+'-value dimension-marks" placeholder="Max Marks..." '+s+" "+r+' ></td>    <td class="exam-dimension" id="'+a[i].examDimension.dimensionId+'"> <input type="number" class="form-control form-control-sm min-marks-value input-min-marks-'+a[i].examDimension.dimensionId+'-value dimension-marks" placeholder="Min Marks..."    onChange="examDetailsNamespace.checkMinMarks(this)"  '+o+" "+r+" ></td>"}return courseMarksRow+="</tr>",courseMarksRow},checkMinMarks:function(e){val=$(e).val(),maxMarks=$(e).parent().prev().find(".max-marks-value").val(),val<0&&($("#update-class-exam-courses-modal").modal("toggle"),showErrorDialogBox("Please Enter a number which is greater than zero"),$(e).val(""),$("#update-class-exam-courses-modal").modal("toggle")),parseInt(val)>parseInt(maxMarks)&&($("#update-class-exam-courses-modal").modal("toggle"),showErrorDialogBox("Min Marks cannot be greater than the Max Marks"),$(e).val(""),$("#update-class-exam-courses-modal").modal("toggle")),val=""},checkInputMinMarks:function(e,a){val=$(e).val();var t=$(e).parent().prev().find(".input-button-max-marks-"+a+"-value").val();return val<0?($("#update-class-exam-courses-modal").modal("toggle"),showErrorDialogBox("Please Enter a number which is greater than zero"),void $(e).val("")):parseInt(val)>parseInt(t)?($("#update-class-exam-courses-modal").modal("toggle"),showErrorDialogBox("Min Marks cannot be greater than the Max Marks"),void $(e).val("")):void 0},populateUpdateExamCoursesModal:function(e){var a=e?"SCHOLASTIC":"COSCHOLASTIC";examDetailsNamespace.dataCache.updateCourseType=a;var t=examDetailsNamespace.dataCache.examDetails,n=examDetailsNamespace.dataCache.classCourses,i=t.academicSession.yearDisplayName,s=t.standard.displayName;$("#update\\.class-exam-name").val(t.examMetaData.examName),$("#update\\.class-exam-class-name").val(s),$("#update\\.class-exam-session-display").val(i);var o="";e?$("#update-class-exam-courses-modal").find(".modal-title").html("Update Exam Scholastic Courses"):$("#update-class-exam-courses-modal").find(".modal-title").html("Update Exam Co-Scholastic Courses"),o+='<table class="table table-bordered table-hover"> <thead> <tr> <th scope="col" style="width:5%;" rowspan="2"> <label class="switch"> <input type="checkbox" class="primary toggle-switch parent" checked > <span class="slider round"></span> </label>  </th> <th scope="col" rowspan="2" class="p-4">Name</th>';for(var r=0;r<t.examDimensionValues[a].length;r++)"SCHOLASTIC"==a?t.standardsMetaData.scholasticGradingEnabled?"GRADE"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&t.examDimensionValues[a][r].examDimension.total&&(o+='<th scope="col" rowspan="2" class="p-4">Evaluation Type</th>'):"NUMBER"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&(t.examDimensionValues[a][r].examDimension.total?o+='<th scope="col" colspan="2" class="text-center ">'+t.examDimensionValues[a][r].examDimension.dimensionName+"</th>":o+='<th scope="col" colspan="2" class="text-center ">'+t.examDimensionValues[a][r].examDimension.dimensionName+'<button type="button" class="btn btn-outline-success btn-sm mb-1 ml-3" onclick="examDetailsNamespace.autofillData(\''+t.examDimensionValues[a][r].examDimension.dimensionId+"');\">Autofill</button></th>"):"COSCHOLASTIC"==a&&(t.standardsMetaData.coScholasticGradingEnabled?"GRADE"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&t.examDimensionValues[a][r].examDimension.total&&(o+='<th scope="col" rowspan="2" class="p-4">Evaluation Type</th>'):"NUMBER"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&(t.examDimensionValues[a][r].examDimension.total?o+='<th scope="col" colspan="2" class="text-center ">'+t.examDimensionValues[a][r].examDimension.dimensionName+"</th>":o+='<th scope="col" colspan="2" class="text-center">'+t.examDimensionValues[a][r].examDimension.dimensionName+'<button type="button" class="btn btn-outline-success btn-sm mb-1 ml-3" onclick="examDetailsNamespace.autofillData(\''+t.examDimensionValues[a][r].examDimension.dimensionId+"');\">Autofill</button></th>"));o+="</tr><tr>";for(r=0;r<t.examDimensionValues[a].length;r++)"SCHOLASTIC"==a?t.standardsMetaData.scholasticGradingEnabled?"GRADE"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&t.examDimensionValues[a][r].examDimension.total:"NUMBER"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&(t.examDimensionValues[a][r].examDimension.total?o+='<th scope="col" colspan="1"></th><th scope="col" colspan="1"></th>':o+='<th scope="col" colspan="1">Max Marks<input type="number" class="form-control form-control-sm input-button-max-marks-'+t.examDimensionValues[a][r].examDimension.dimensionId+'-value dimension-marks" placeholder="Max Marks..."></th><th scope="col" colspan="1">Min Marks<input type="number" class="form-control form-control-sm input-button-min-marks-'+t.examDimensionValues[a][r].examDimension.dimensionId+'-value dimension-marks" placeholder="Min Marks..." onChange="examDetailsNamespace.checkInputMinMarks(this,\''+t.examDimensionValues[a][r].examDimension.dimensionId+"')\"></th>"):"COSCHOLASTIC"==a&&(t.standardsMetaData.coScholasticGradingEnabled?"GRADE"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&t.examDimensionValues[a][r].examDimension.total:"NUMBER"==t.examDimensionValues[a][r].examDimension.examEvaluationType&&(t.examDimensionValues[a][r].examDimension.total?o+='<th scope="col" colspan="1"></th><th scope="col" colspan="1"></th>':o+='<th scope="col" colspan="1">Max Marks<input type="number" class="form-control form-control-sm input-button-max-marks-'+t.examDimensionValues[a][r].examDimension.dimensionId+'-value dimension-marks" placeholder="Max Marks..."></th><th scope="col" colspan="1">Min Marks<input type="number" class="form-control form-control-sm input-button-min-marks-'+t.examDimensionValues[a][r].examDimension.dimensionId+'-value dimension-marks" placeholder="Min Marks..." onChange="examDetailsNamespace.checkInputMinMarks(this,\''+t.examDimensionValues[a][r].examDimension.dimensionId+"')\"></th>"));var d={};if(o+="</tr> </thead> <tbody>",a in t.courseMarksMatrix)for(r=0;r<t.courseMarksMatrix[a].length;r++){var l=t.courseMarksMatrix[a][r];d[l.course.courseId]=l,"SCHOLASTIC"==a?t.standardsMetaData.scholasticGradingEnabled?o+=examDetailsNamespace.getCourseMaxGradeRow(l.course,l.examDimensionValues,!0):o+=examDetailsNamespace.getCourseMaxMarksRow(l.course,l.examDimensionValues,!0):"COSCHOLASTIC"==a&&(t.standardsMetaData.coScholasticGradingEnabled?o+=examDetailsNamespace.getCourseMaxGradeRow(l.course,l.examDimensionValues,!0):o+=examDetailsNamespace.getCourseMaxMarksRow(l.course,l.examDimensionValues,!0))}var c=!1;for(r=0;r<n.length;r++)n[r].courseId in d||n[r].courseType!=a||("SCHOLASTIC"==a?t.standardsMetaData.scholasticGradingEnabled?o+=examDetailsNamespace.getCourseMaxGradeRow(n[r],t.examDimensionValues[a],!1):o+=examDetailsNamespace.getCourseMaxMarksRow(n[r],t.examDimensionValues[a],!1):"COSCHOLASTIC"==a&&(t.standardsMetaData.coScholasticGradingEnabled?o+=examDetailsNamespace.getCourseMaxGradeRow(n[r],t.examDimensionValues[a],!1):o+=examDetailsNamespace.getCourseMaxMarksRow(n[r],t.examDimensionValues[a],!1)),c=!0);o+="</tbody> </table>",$("#update-class-exam-courses-modal").find(".exam-course-assignment").html(o),c&&$("input.parent").prop("checked",!1),examDetailsNamespace.bindComputeCourseMarksTotal(),$("#update-class-exam-courses-modal").modal({backdrop:"static",keyboard:!1})},autofillData:function(e){var a=$(".input-button-max-marks-"+e+"-value").val(),t=$(".input-button-min-marks-"+e+"-value").val();$("tr.exam-course-update-row").each(function(){$(this).find("input.input-max-marks-"+e+"-value").each(function(){""!=a&&$(this).val(a)}),$(this).find("input.input-min-marks-"+e+"-value").each(function(){""!=t&&$(this).val(t)})}),examDetailsNamespace.computeCourseMarksTotal()},bindComputeCourseMarksTotal:function(){$("input.dimension-marks").focus(function(){}).blur(function(){examDetailsNamespace.computeCourseMarksTotal()}),$(".exam-course-update-row").find(".toggle-switch").change(function(){var e=0,a=0;this.checked?($(this).parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",!1),$(this).parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",!1),$(this).parent().parent().parent().parent().find("input.child").each(function(){e++,this.checked&&a++}),a==e&&$(this).parent().parent().parent().parent().parent().find("input.parent").prop("checked",!0)):($(this).parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",!0),$(this).parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",!0))}),$(".exam-course-update-row").parent().parent().find(".toggle-switch.parent").change(function(){this.checked?($(this).parent().parent().parent().parent().parent().find("input.child").prop("checked",!0),$(this).parent().parent().parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",!1),$(this).parent().parent().parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",!1)):($(this).parent().parent().parent().parent().parent().find("input.child").prop("checked",!1),$(this).parent().parent().parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",!0),$(this).parent().parent().parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",!0))})},computeCourseMarksTotal:function(){$("tr.exam-course-update-row").each(function(){var e=0,a=!1;$(this).find("input.dimension-marks.max-marks-value").each(function(){""!=$(this).val().trim()&&(a=!0,e+=parseFloat($(this).val()))}),a&&$(this).find("input.total-marks.max-marks-value").val(e),e=0,a=!1,$(this).find("input.dimension-marks.min-marks-value").each(function(){""!=$(this).val().trim()&&(a=!0,e+=parseFloat($(this).val()))}),a&&$(this).find("input.total-marks.min-marks-value").val(e<0?0:e)})},submitExamCourseUpdate:function(){$("#update-class-exam-courses-modal").modal("toggle");var e=examDetailsNamespace.dataCache.examDetails.examMetaData.examId,a=examDetailsNamespace.dataCache.updateCourseType,t=[];$("tr.exam-course-update-row").each(function(){var e,a;if($(this).find(".toggle-switch:checkbox:checked").length>0){var n=$(this).attr("id"),i={};$(this).find("td.exam-dimension").each(function(){var t=$(this).attr("id"),n=$(this).find(".max-marks-value").val(),s=$(this).find(".min-marks-value").val();null!=s&&(e=s),null!=n&&(a=n);var o=$(this).find(".max-grade-value").val();i[t]={dimensionId:t,maxMarks:a,minMarks:e,maxGrade:o}});var s=[];$.each(i,function(e,a){s.push(a)}),t.push({courseId:n,dimensionValuesPayloads:s})}}),ajaxClient.post("/examination/update-exam-courses/"+e+"/"+a,{examCoursesPayloads:JSON.stringify(t)},function(e){$("#global-modal-container").html(e),$("#exam-update-status-modal").modal({backdrop:"static",keyboard:!1})})},populateUpdateExamDimensionModal:function(){var e=examDetailsNamespace.dataCache.examDetails,a=e.academicSession.yearDisplayName;$("#update-dimension\\.class-exam-session-display").val(a);var t=e.standard.displayName;$("#update-dimension\\.class-exam-class-name").val(t),$("#update-dimension\\.class-exam-name").val(e.examMetaData.examName);var n=examDetailsNamespace.dataCache.dimensions,i={},s={},o=e.examDimensionValues,r="",d="";if(null!=o){if(SCHOLASTIC_COURSE_TYPE in o&&o[SCHOLASTIC_COURSE_TYPE].length>0)for(var l=0;l<o[SCHOLASTIC_COURSE_TYPE].length;l++){i[o[SCHOLASTIC_COURSE_TYPE][l].examDimension.dimensionId]=o[SCHOLASTIC_COURSE_TYPE][l].examDimension;var c=o[SCHOLASTIC_COURSE_TYPE][l];if(e.standardsMetaData.scholasticGradingEnabled){if("NUMBER"==c.examDimension.examEvaluationType)continue;if(c.examDimension.total){r+='<tr id="'+c.examDimension.dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>";break}}else{if("GRADE"==c.examDimension.examEvaluationType)continue;if(c.examDimension.total){r+='<tr id="'+c.examDimension.dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>";continue}r+='<tr id="'+c.examDimension.dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>"}}if(COSCHOLASTIC_COURSE_TYPE in o&&o[COSCHOLASTIC_COURSE_TYPE].length>0)for(l=0;l<o[COSCHOLASTIC_COURSE_TYPE].length;l++){s[o[COSCHOLASTIC_COURSE_TYPE][l].examDimension.dimensionId]=o[COSCHOLASTIC_COURSE_TYPE][l].examDimension;c=o[COSCHOLASTIC_COURSE_TYPE][l];if(e.standardsMetaData.coScholasticGradingEnabled){if("NUMBER"==c.examDimension.examEvaluationType)continue;if(c.examDimension.total){d+='<tr id="'+c.examDimension.dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>";break}}else{if("GRADE"==c.examDimension.examEvaluationType)continue;if(c.examDimension.total){d+='<tr id="'+c.examDimension.dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked disabled> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>";continue}d+='<tr id="'+c.examDimension.dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch" checked> <span class="slider round"></span> </label> </td> <td> '+c.examDimension.dimensionName+" </td> <td> "+c.examDimension.examEvaluationType+" </td>"}}}var m=[];for(l=0;l<n.length;l++)n[l].dimensionId in i||m.push(n[l]);var u=[];for(l=0;l<n.length;l++)n[l].dimensionId in s||u.push(n[l]);for(l=0;l<m.length;l++)if(!e.standardsMetaData.scholasticGradingEnabled){if("GRADE"==m[l].examEvaluationType)continue;r+='<tr id="'+m[l].dimensionId+'-scholastic" class="scholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch"> <span class="slider round"></span> </label> </td> <td> '+m[l].dimensionName+" </td> <td> "+m[l].examEvaluationType+" </td></tr>"}$("#update-dimension\\.class-exam-scholastic-dimensions-input-div").find("tbody").html(r);for(l=0;l<u.length;l++)if(!e.standardsMetaData.coScholasticGradingEnabled){if("GRADE"==u[l].examEvaluationType)continue;d+='<tr id="'+u[l].dimensionId+'-coscholastic" class="coscholastic-dimension-row"> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch"> <span class="slider round"></span> </label> </td> <td> '+u[l].dimensionName+" </td>  <td> "+u[l].examEvaluationType+" </td> </tr>"}$("#update-dimension\\.class-exam-coscholastic-dimensions-input-div").find("tbody").html(d)},updateExamDimension:function(){$("#update-exam-dimension-modal").modal("toggle");var e=examDetailsNamespace.dataCache.examDetails,a=(e.academicSession.academicSessionId,e.standard.standardId,e.examMetaData.examId),t=[];$("tr.scholastic-dimension-row").each(function(){if($(this).find(".toggle-switch:checkbox:checked").length>0){var e=$(this).attr("id").split("-")[0],a=$(this).find(".max-default-value").val(),n=$(this).find(".min-default-value").val();t.push({dimensionId:e,maxMarks:a,minMarks:n})}});var n=[];$("tr.coscholastic-dimension-row").each(function(){if($(this).find(".toggle-switch:checkbox:checked").length>0){var e=$(this).attr("id").split("-")[0],a=$(this).find(".max-default-value").val(),t=$(this).find(".min-default-value").val();n.push({dimensionId:e,maxMarks:a,minMarks:t})}});var i={SCHOLASTIC:t,COSCHOLASTIC:n};ajaxClient.post("/examination/update-exam-dimension/"+a,{updateExamDimensionPayload:JSON.stringify(i)},function(e){$("#global-modal-container").html(e),$("#exam-update-status-modal").modal({backdrop:"static",keyboard:!1})})},updateExamMetaDataPopUp:function(){var e=examDetailsNamespace.dataCache.examDetails;$("#update-exam-metadata-modal").find("#update-class-exam-name").val(e.examMetaData.examName),$("#update-exam-metadata-modal").find("#update-exam-operation").val(e.examMetaData.operation),$("#update-exam-metadata-modal").find("#update-exam-sch-display-type").val(e.examMetaData.scholasticExamMarksDisplayType),$("#update-exam-metadata-modal").find("#update-exam-cosch-display-type").val(e.examMetaData.coScholasticExamMarksDisplayType),$("#update-exam-metadata-modal").find("#attendance-start-date").val(getFormattedDate(e.examMetaData.attendanceStartDate)),$("#update-exam-metadata-modal").find("#attendance-end-date").val(getFormattedDate(e.examMetaData.attendanceEndDate)),$("#update-exam-metadata-modal").find("#exam-start-date").val(getFormattedDate(e.examMetaData.examStartDate)),$("#update-exam-metadata-modal").find("#date-of-result-declaration").val(getFormattedDate(e.examMetaData.dateOfResultDeclaration)),$("#update-exam-metadata-modal").modal({backdrop:"static",keyboard:!1})},updateExamMetadata:function(){$("#update-exam-metadata-modal").modal("toggle");var e=examDetailsNamespace.dataCache.examDetails.examMetaData.examId,a=$("#update-exam-metadata-modal").find("#update-class-exam-name").val();if(null!=a&&""!=a.trim()){var t=$("#update-exam-metadata-modal").find("#update-exam-operation").val();if(null!=t&&""!=t.trim()&&void 0!==t){var n=$("#update-exam-sch-display-type").val(),i=$("#update-exam-cosch-display-type").val(),s=getDate($("#attendance-start-date").val()),o=null;null!=s&&(o=s.getTime()/1e3);var r=getDate($("#attendance-end-date").val()),d=null;null!=r&&(d=r.getTime()/1e3);var l=getDate($("#exam-start-date").val()),c=null;null!=l&&(c=l.getTime()/1e3);var m=getDate($("#date-of-result-declaration").val()),u=null;null!=m&&(u=m.getTime()/1e3);var p={examName:a,operation:t,attendanceStartDate:o,attendanceEndDate:d,examStartDate:c,dateOfResultDeclaration:u,scholasticExamMarksDisplayType:n,coScholasticExamMarksDisplayType:i};ajaxClient.post("/examination/update-exam-metadata/"+e+"/false",{updateExamMetadataPayload:JSON.stringify(p)},function(e){$("#global-modal-container").html(e),$("#exam-update-status-modal").modal({backdrop:"static",keyboard:!1})})}else showErrorDialogBox("Invalid operation selected.")}else showErrorDialogBox("Invalid exam name provided.")},deleteExamPopUp:function(){var e=examDetailsNamespace.dataCache.examDetails;e.academicSession.yearDisplayName,e.standard.displayName;$("#delete-class-exam-courses-modal").find(".modal-title").html("Delete Exam"),$("#delete-class-exam-courses-modal").modal({backdrop:"static",keyboard:!1})},submitDeleteExam:function(){$("#delete-class-exam-courses-modal").modal("toggle");var e=examDetailsNamespace.dataCache.examDetails.examMetaData.examId,a=examDetailsNamespace.dataCache.examDetails.standard.standardId;ajaxClient.post("/examination/delete-exam/"+e+"/"+a+"/false",{},function(e){$("#global-modal-container").html(e),$("#exam-delete-status-modal").modal({backdrop:"static",keyboard:!1})})}},dimensions={loadDimensionsPage:function(){ajaxClient.get("/examination/dimension-config",function(e){$("#main-content").html(e),dimensions.registerAddDimensionCallback(),dimensions.registerUpdateDimensionCallBack(),dimensions.registerDeleteDimensionCallBack(),bindRemoveErrorDisplayEvent()})},loadDimensionlist:function(){ajaxClient.get("/examination/dimension-list",function(e){$("#dimension-list").html(e),dimensions.registerUpdateDimensionCallBack(),dimensions.registerDeleteDimensionCallBack()})},resetDimensionConfigModal:function(){$("#add-dimension-modal").find("input").val(""),$("#add-dimension-modal").find("select").val("")},registerAddDimensionCallback:function(){$("#add-dimension").on("click",function(){if(!validateMandatoryFields($("#add-dimension-modal"))){var e=$("#dimension-name").val().trim(),a=$("#exam-evaluation-type option:selected").val();$("#add-dimension-modal").modal("toggle");var t={dimensionName:e,examEvaluationType:a};ajaxClient.post("/examination/add-dimension",{dimensionData:JSON.stringify(t)},function(e){$("#dimension\\.status-modal-container").html(e),$("#exam-config\\.status-modal").modal("toggle"),dimensions.loadDimensionlist()})}})},registerUpdateDimensionCallBack:function(){$(".update-dimension").on("click",function(){var e=$(this).parent().find(".dimension-info").text().trim(),a=JSON.parse(e);$("#update\\.dimension-id").val(a.dimensionId),$("#update\\.dimension-name").val(a.dimensionName),$("#update\\.exam-evaluation-type").val(a.examEvaluationType),$("#update-dimension-modal").modal("toggle")})},updateDimension:function(){if(!validateMandatoryFields($("#update-dimension-modal"))){var e=$("#update\\.dimension-id").val(),a=$("#update\\.dimension-name").val().trim(),t=$("#update\\.exam-evaluation-type").val();$("#update-dimension-modal").modal("toggle");var n={dimensionId:e,dimensionName:a,examEvaluationType:t};ajaxClient.post("/examination/update-dimension",{updateDimensionData:JSON.stringify(n)},function(e){$("#dimension\\.status-modal-container").html(e),$("#exam-config\\.status-modal").modal("toggle"),dimensions.loadDimensionlist()})}},registerDeleteDimensionCallBack:function(){$(".delete-dimension").on("click",function(){var e=$(this).parent().find(".dimension-info").text().trim(),a=JSON.parse(e);$("#delete-dimension-id").val(a.dimensionId),$("#delete-dimension-confirm-modal").modal("toggle")})},deleteDimension:function(){$("#delete-dimension-confirm-modal").modal("toggle");var e=$("#delete-dimension-id").val();ajaxClient.post("/examination/delete-dimension/"+e,{},function(e){$("#dimension\\.status-modal-container").html(e),$("#exam-config\\.status-modal").modal("toggle"),dimensions.loadDimensionlist()})}},datesheet={dataCache:{},initDataCache:function(){for(var e=JSON.parse($("#exam-courses-json").text().trim()),a={},t=0;t<e.length;t++){var n=e[t];a[n.examMetaData.examId]=n}datesheet.dataCache={},datesheet.dataCache.examCoursesMap=a,datesheet.dataCache.selectedCourses={}},loadDatesheetPage:function(){ajaxClient.get("/examination/datesheet-config",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(datesheet.changeSession),datesheet.bindClassChangeEvent()})},changeSession:function(){academicSessionHandler.getSelectedSessionId();datesheet.loadDatesheetPage()},bindClassChangeEvent:function(){$("#datesheet-config-class").change(function(){""!=$(this).find(":selected").val().trim()&&datesheet.loadDatesheetList()})},loadDatesheetList:function(){var e=$("#datesheet-config-class").find(":selected").val();if(""!=e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/datesheet-list/"+a+"/"+e,function(e){$("#datesheet-list").html(e),datesheet.initDataCache(),datesheet.registerDeleteDatesheetCallback(),datesheet.addDatesheetPopup(),bindRemoveErrorDisplayEvent()})}},resetAddDatesheetModal:function(){$("#class-level-datesheet-container").html('<p class="class-add-datesheet-hint-text black-color"> Select course to add new Datesheet</p>'),$("#datesheet-modal-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName());var e=$("#datesheet-config-class").find(":selected").text();$("#datesheet-modal-standard").val(e),$("#datesheet-exam").val(""),$("#datesheet-exam-start-date").val(""),$("#datesheet-exam-end-date").val(""),$("#datesheet-notes").val(""),$("#datesheet-exam").attr("disabled",!1),$("#datesheet-notes").attr("disabled",!1),$("#datesheet-config-modal").find(".modal-footer").css("display",""),$("#nav-structure-tab").find(".nav-item").removeClass("active"),$("#nav-class-datesheet-config-tab").addClass("active"),$("#nav-structure-tabContent").find(".tab-pane").removeClass("show active"),$("#nav-class-datesheet-config").addClass("show active")},addDatesheetPopup:function(){initFutureDate(1800),datesheet.resetAddDatesheetModal(),$("#datesheet-config-modal").find(".modal-title").html("Add New Datesheet"),$(".course-dropdown-container").css("display","block"),datesheet.dataCache.selectedCourses={},datesheet.dataCache.datesheetId=null,datesheet.bindSelectCourseDropdown(datesheet.dataCache.selectedCourses),$("#datesheet-exam-start-date").closest("div.form-group").css("display","none"),$("#datesheet-exam-end-date").closest("div.form-group").css("display","none")},bindDimensionRowEvents:function(e){$("#"+e).find(".delete-dimension-row").on("click",function(){$(this).closest(".class-dimension").remove()})},insertDimensionRow:function(e,a,t,n){for(var i=datesheet.dataCache.examCoursesMap[a],s=0;s<i.examCourses.length;s++)if(i.examCourses[s].course.courseId===t)var o=i.examCourses[s].examDimensionValues;var r="";for(s=0;s<o.length;s++)null==n||o[s].examDimension.dimensionId!==n.examDimension.dimensionId?r+=" <option value="+o[s].examDimension.dimensionId+">"+o[s].examDimension.dimensionName+"</option>":r+=" <option value="+o[s].examDimension.dimensionId+" selected >"+o[s].examDimension.dimensionName+"</option>";if(null===n){var d='<div class="card card-border text-center class-dimension" id="class-dimension"><div class = "card-body"><div style="float:right;"> <button type="button" class="close delete-dimension-row" aria-label="Close"> <span aria-hidden="true">×</span> </button> </div><div class="form-row"> <div class="form-group col-md-3"> <label >Dimension*</label><select class="form-control mandatory-field class-datesheet-dimension"> <option value="">-choose-</option>'+r+' </select> </div> <div class="form-group col-md-3"> <label>Course Exam Date*</label><input type="text" class="form-control mandatory-field select-date class-datesheet-exam-date" placeholder="Exam Date"></div><div class="form-group col-md-3  clockpicker"><label>Start Time*</label> <input type="text" class="form-control mandatory-field in-time clockpicker-input class-datesheet-start-time" placeholder="Exam Start Time"></div> <div class="form-group col-md-3 clockpicker"><label>End Time*</label><input type="text" class="form-control mandatory-field clockpicker-input class-datesheet-end-time" placeholder="Exam End Time"></div> </div><div class="form-group"><label >Syllabus</label><textarea class="form-control class-datesheet-syllabus" rows="3" placeholder="Syllabus"></textarea></div></div></div><br>';$("#"+e).find(".class-dimension-container").append(d),initFutureDate(1800),$(".clockpicker").clockpicker({autoclose:!0,default:"now"})}else{var l=getFormattedTime(n.startTime.hour)+":"+getFormattedTime(n.startTime.minute),c=getFormattedTime(n.endTime.hour)+":"+getFormattedTime(n.endTime.minute);d='<div class="card card-border text-center class-dimension" id="class-dimension"><div class = "card-body"><div style="float:right;"> <button type="button" class="close delete-dimension-row" aria-label="Close"> <span aria-hidden="true">×</span> </button> </div><div class="form-row"> <div class="form-group col-md-3"> <label >Dimension*</label><select class="form-control mandatory-field class-datesheet-dimension"> <option value="">-choose-</option>'+r+' </select> </div> <div class="form-group col-md-3"> <label>Course Exam Date*</label><input type="text" class="form-control mandatory-field select-date class-datesheet-exam-date" placeholder="Exam Date" value='+getFormattedDate(n.courseExamDate)+'></div><div class="form-group col-md-3  clockpicker"><label>Start Time*</label> <input type="text" class="form-control mandatory-field in-time clockpicker-input class-datesheet-start-time" placeholder="Exam Start Time" value='+l+'></div> <div class="form-group col-md-3 clockpicker"><label>End Time*</label><input type="text" class="form-control mandatory-field clockpicker-input class-datesheet-end-time" placeholder="Exam End Time"value='+c+'></div> </div><div class="form-group"><label >Syllabus</label><textarea class="form-control class-datesheet-syllabus" rows="3" placeholder="Syllabus">'+n.syllabus+"</textarea></div></div></div><br>";$("#"+e).find(".class-dimension-container").append(d),initFutureDate(1800),$(".clockpicker").clockpicker({autoclose:!0,default:"now"})}datesheet.bindDimensionRowEvents(e)},bindCourseContainerEvents:function(e,a,t,n,i){$("#"+a).find(".add-dimension-button").on("click",function(){datesheet.insertDimensionRow(a,t,n,null)}),$("#"+a).find(".delete-course-row").on("click",function(){$(this).closest(".course-datesheet").remove(),delete i[n],isEmpty(i)&&$(e).find(".class-add-datesheet-hint-text").attr("style","display:block;"),datesheet.populateSelectCourseDropdown(e,t,i)})},insertCourseDatesheetCard:function(e,a,t,n){n[a]=!0,$(e).find(".class-add-datesheet-hint-text").attr("style","display:none;");for(var i=datesheet.dataCache.examCoursesMap[t],s=0;s<i.examCourses.length;s++)if(i.examCourses[s].course.courseId===a)var o=i.examCourses[s].course.courseName;var r=a+"-select-"+t,d='<div class="course-datesheet"> <div class="card card-border text-center" id="'+r+'"><p class="course-id" style="display:none;">'+a+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-course-row" aria-label="Close"> <span aria-hidden="true">×</span> </button> </div> <h5 class="card-title"> <strong>'+o+'</strong> </h5> <div class = "class-dimension-container"></div> <button type="button" class="btn btn-outline-secondary btn-sm add-dimension-button"> + Add Dimension</button></div></div></div>';$("#class-level-datesheet-container").append(d),datesheet.bindCourseContainerEvents(e,r,t,a,n),datesheet.populateSelectCourseDropdown(".class-datesheet",t,n)},bindCourseSelectEvent:function(e,a){$(e).find(".course-select-option").on("click",function(){var t=$(this).attr("id").split("-select-")[0],n=$(this).attr("id").split("-select-")[1];datesheet.insertCourseDatesheetCard(e,t,n,a)})},bindSelectCourseDropdown:function(e){$("#datesheet-exam").change(function(){var a=$(this).find(":selected").val().trim();""!=a&&datesheet.populateAddDateSheetPopUp(".class-datesheet",a,e)})},populateSelectCourseDropdown:function(e,a,t){var n=datesheet.dataCache.examCoursesMap,i="";examCourseData=n[a];for(var s=0;s<examCourseData.examCourses.length;s++)examCourseData.examCourses[s].course.courseId in t||(i+='<a class="dropdown-item course-select-option" href="#" id="'+examCourseData.examCourses[s].course.courseId+"-select-"+a+'">'+examCourseData.examCourses[s].course.courseName+"</a>");$(e).find(".course-select-dropdown").html(i),datesheet.bindCourseSelectEvent(".class-datesheet",t),$("#autofill-button").click(datesheet.autofillTimes)},generateTableHeader:function(e){return'<table id="exam-courses-table" class="table"><thead><tr><th style="width: 5%; text-align: center;"><label class="switch" style="display: inline-block; position: relative; top: 13px;"><input type="checkbox" class="primary toggle-switch parent" id="parent-toggle" checked '+(e?"disabled":"")+'><span class="slider round"></span></label></th><th style="width: 15%;">Subject</th><th style="width: 10%;">Dimension</th><th style="width: 12%;">Date</th><th style="width: 10%;">Start Time</th><th style="width: 10%;">End Time</th><th style="width: 38%;">Syllabus</th></tr>'},generateTableFooter:function(){return"</thead><tbody>"},generateAutofillRow:function(){return'<tr><th colspan="4" style="text-align: right;"></th><th class="form-group clockpicker" >  <input type="text"  class="form-control clockpicker-input default-start-time" placeholder="Start Time"></th><th class="form-group clockpicker">  <input type="text"  class="form-control clockpicker-input default-end-time" placeholder="End Time" onchange="datesheet.validateExamTime(this, \'default-start-time\', \'default-end-time\')"></th><th>  <button id="autofill-button" class="btn btn-outline-success btn-sm mb-1"  style="position: relative; float: left; margin-left: 0;"; onclick="datesheet.autofillTimes()">Autofill</button></th></tr>'},populateAddDateSheetPopUp:function(e,a,t){var n=datesheet.dataCache.examCoursesMap;examCourseData=n[a];var i=datesheet.generateTableHeader(!1);i+=datesheet.generateAutofillRow(),i+=datesheet.generateTableFooter();for(var s=0;s<examCourseData.examCourses.length;s++)for(var o=examCourseData.examCourses[s].course,r=examCourseData.examCourses[s].examDimensionValues,d=0;d<r.length;d++){var l=r[d].examDimension;l.total&&"NUMBER"===l.examEvaluationType||(i+=datesheet.generateRow(o,l,"","","","",!0,!1))}i+="</tbody></table>",$(e).find("#class-level-datesheet-container").html(i),initFutureDate(1800),$(".clockpicker").clockpicker({autoclose:!0,default:"now"}),datesheet.bulkToggleChange(),datesheet.childToggleChange()},autofillTimes:function(){var e=$(".default-start-time").val(),a=$(".default-end-time").val();$(".class-datesheet-start-time").each(function(){$(this).prop("disabled")||$(this).val(e)}),$(".class-datesheet-end-time").each(function(){$(this).prop("disabled")||$(this).val(a)})},bulkToggleChange:function(){$("#parent-toggle").change(function(){var e=$(this).is(":checked");$(".toggle-switch.child").prop("checked",e),$(".toggle-switch.child").each(function(){var a=$(this).closest("tr");a.find(".class-datesheet-exam-date").prop("disabled",!e).toggleClass("mandatory-field",e).css("border",""),a.find(".class-datesheet-start-time").prop("disabled",!e).toggleClass("mandatory-field",e).css("border",""),a.find(".class-datesheet-end-time").prop("disabled",!e).toggleClass("mandatory-field",e).css("border",""),a.find(".class-datesheet-syllabus").prop("disabled",!e)})})},childToggleChange:function(){$(".child").change(function(){var e=$(this).closest("tr"),a=!$(this).is(":checked");e.find(".class-datesheet-exam-date").prop("disabled",a).toggleClass("mandatory-field",!a).css("border",""),e.find(".class-datesheet-start-time").prop("disabled",a).toggleClass("mandatory-field",!a).css("border",""),e.find(".class-datesheet-end-time").prop("disabled",a).toggleClass("mandatory-field",!a).css("border",""),e.find(".class-datesheet-syllabus").prop("disabled",a)})},validateExamTime:function(e,a,t){var n=$(e).closest("tr"),i=n.find("."+a),s=$(e).val(),o=i.val(),r=(o.hour,n.closest("div").find("table").parent()),d=r.next(".error-message");if(o>=s)return n.find("."+t).css("border","1px solid #ff8795"),void(0===d.length&&r.after('<div class="error-message" style="color: red; margin-top: 10px; text-align: center;">Start time should not be greater than or equal to end time.</div>'));n.find("."+t).css("border",""),d.remove()},addDatesheet:function(e){var a=$("#datesheet-config-class").find(":selected").val(),t=academicSessionHandler.getSelectedSessionId();$("#datesheet-config-modal").modal("toggle");var n=$("#datesheet-exam option:selected").val();if(validateMandatoryFields($("#datesheet-config-modal")))showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.","#datesheet-config-modal");else{var i=null,s=$("#datesheet-notes").val().trim();null!=datesheet.dataCache.datesheetId&&""!=datesheet.dataCache.datesheetId.trim()&&(i=datesheet.dataCache.datesheetId);var o=datesheet.getCourseWiseData("#class-level-datesheet-container");if(0!==o.courseWiseDatesheetList.length){var r={instituteId:null,datesheetId:i,academicSessionId:t,standardId:a,examId:n,examStartDate:o.examStartDateInt,examEndDate:o.examEndDateInt,datesheetStatus:e,notes:s,courseWiseDatesheetList:o.courseWiseDatesheetList},d="/examination/add-datesheet";null!=i&&(d="/examination/update-datesheet"),ajaxClient.post(d,{datesheetAndSyllabusPayload:JSON.stringify(r)},function(e){$("#datesheet-status-modal-container").html(e),$(".modal-backdrop").remove(),$("#datesheet-status-modal").modal("toggle"),datesheet.loadDatesheetList()})}else showErrorDialogBox("Please select course to add datesheet.")}},registerDeleteDatesheetCallback:function(){$(".delete-datesheet-button").on("click",function(){var e=$(this).parent().find(".datesheet-info").html();$("#delete-datesheet-id").text(e),$("#delete-datesheet-confirm-modal").modal("toggle")})},deleteDatesheet:function(){$("#delete-datesheet-confirm-modal").modal("toggle");var e=$("#delete-datesheet-id").text().trim();ajaxClient.post("/examination/delete-datesheet/"+e,{},function(e){$("#datesheet-status-modal-container").html(e),$("#datesheet-status-modal").modal("toggle"),datesheet.loadDatesheetList()})},getDatesheetDetails:function(e,a,t){datesheet.dataCache.datesheetId=e;var n=academicSessionHandler.getSelectedSessionId(),i=$("#datesheet-config-class").find(":selected").val().trim();ajaxClient.get("/examination/view-datesheet-detail/"+n+"/"+i+"/"+e,function(t){$("#detailed-datesheet-json-container").html(t),datesheet.fillDatesheetModal(e,a)})},fillDatesheetModal:function(e,a){datesheet.resetAddDatesheetModal();academicSessionHandler.getSelectedSessionId(),$("#datesheet-config-class").find(":selected").val().trim();var t=JSON.parse($("#detailed-datesheet-json").text().trim());$("#datesheet-exam").val(t.examMetaData.examId),$("#datesheet-exam").attr("disabled",!0),$("#datesheet-exam-start-date").closest("div.form-group").css("display",""),$("#datesheet-exam-end-date").closest("div.form-group").css("display",""),$("#datesheet-exam-start-date").val(getFormattedDate(t.examStartDate)),$("#datesheet-exam-end-date").val(getFormattedDate(t.examEndDate)),$("#datesheet-notes").val(t.notes),a?($("#datesheet-config-modal").find(".modal-title").html("View Datesheet"),$("#datesheet-notes").attr("disabled",!0)):$("#datesheet-config-modal").find(".modal-title").html("Update Datesheet"),$(".course-dropdown-container").css("display","block"),$("#datesheet-config-modal").find(".modal-footer").css("display","");var n={};datesheet.dataCache.selectedCourses={};for(var i=0;i<t.courseWiseDatesheetDetailsList.length;i++)n[t.courseWiseDatesheetDetailsList[i].course.courseId]=t.courseWiseDatesheetDetailsList[i];var s=datesheet.dataCache.examCoursesMap;examCourseData=s[t.examMetaData.examId],datesheet.populateViewUpdateTable(".class-datesheet",n,examCourseData.examCourses,a),a&&($(".course-dropdown-container").css("display","none"),$(".add-dimension-button").css("display","none"),$(".delete-dimension-row").remove(),$(".delete-course-row").remove(),$("#fee-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#datesheet-config-modal").find(".modal-footer").css("display","none"),$(".class-datesheet-dimension").attr("disabled","true"),$(".class-datesheet-exam-date").attr("disabled","true"),$(".class-datesheet-start-time").attr("disabled","true"),$(".class-datesheet-end-time").attr("disabled","true"),$(".class-datesheet-syllabus").attr("disabled","true")),datesheet.bindSelectCourseDropdown(datesheet.dataCache.selectedCourses)},populateViewUpdateTable:function(e,a,t,n){var i=datesheet.generateTableHeader(n);n||(i+=datesheet.generateAutofillRow()),i+=datesheet.generateTableFooter();var s=new Map;for(var o in a)if(a.hasOwnProperty(o))for(var r=a[o],d=r.course,l=r.dimensionWiseDatesheetDetailsList,c=0;c<l.length;c++){var m=l[c].examDimension;if(!m.total||"NUMBER"!==m.examEvaluationType){var u=o+"-"+m.dimensionId;if(!s.has(u)){s.set(u,!0);var p=getFormattedTime(l[c].startTime.hour)+":"+getFormattedTime(l[c].startTime.minute),h=getFormattedTime(l[c].endTime.hour)+":"+getFormattedTime(l[c].endTime.minute),f=getFormattedDate(l[c].courseExamDate),g=l[c].syllabus||"";i+=datesheet.generateRow(d,m,f,p,h,g,!0,n)}}}n||t.forEach(function(e){var a=e.course;e.examDimensionValues.forEach(function(e){var t=e.examDimension;if(!t.total||"NUMBER"!==t.examEvaluationType){var o=a.courseId+"-"+t.dimensionId;s.has(o)||(s.set(o,!0),i+=datesheet.generateRow(a,t,"","","","",!1,n))}})}),i+="</tbody></table>",$(e).find("#class-level-datesheet-container").html(i),initFutureDate(1800),$(".clockpicker").clockpicker({autoclose:!0,default:"now"}),datesheet.bulkToggleChange(),datesheet.childToggleChange()},generateRow:function(e,a,t,n,i,s,o,r){var d=o?"":"disabled",l=o?"mandatory-field":"";return'<tr class="course-datesheet">    <td>        <label class="switch" style="display: inline-block; position: relative; top: 9px;">            <input type="checkbox" class="primary toggle-switch child" '+(o?"checked":"")+" "+(r?"disabled":"")+'>            <span class="slider round"></span>        </label>    </td>    <td class="course-id" style="display:none;">'+e.courseId+"</td>    <td>"+e.courseName+'</td>    <td style="display:none;" class="class-datesheet-dimension">'+a.dimensionId+"</td>    <td>"+a.dimensionName+'</td>    <td>        <div class="form-group" style="padding-top: 15px;">            <input type="text" class="form-control '+l+' select-date class-datesheet-exam-date"                value="'+t+'" placeholder="Exam Date" '+d+'>        </div>    </td>    <td>        <div class="form-group clockpicker" style="padding-top: 15px;">            <input type="text" class="form-control '+l+' in-time clockpicker-input class-datesheet-start-time"                value="'+n+'" placeholder="Start Time" '+d+'>        </div>    </td>    <td>        <div class="form-group clockpicker" style="padding-top: 15px;">            <input type="text" class="form-control '+l+' clockpicker-input class-datesheet-end-time"                value="'+i+'" placeholder="End Time" '+d+' onchange="datesheet.validateExamTime(this, \'class-datesheet-start-time\', \'class-datesheet-end-time\')">        </div>    </td>    <td>         <textarea  class="form-control class-datesheet-syllabus"             placeholder="Syllabus" '+d+">"+s+"</textarea>    </td></tr>"},getCourseWiseData:function(e){var a=[],t=[];$(e).find("tr.course-datesheet").each(function(){if($(this).find(".toggle-switch.child").is(":checked")){var e=$(this).find(".course-id").text().trim();if(e){var n=[],i=$(this).find(".class-datesheet-dimension").text().trim();if(i){var s=$(this).find(".class-datesheet-exam-date").val();if(s){var o=getDate(s).getTime()/1e3;t.push(o);var r=$(this).find(".class-datesheet-start-time").val();if(r){var d=r.split(":"),l={hour:d[0],minute:d[1],second:"00"},c=$(this).find(".class-datesheet-end-time").val();if(c){var m=c.split(":"),u={hour:m[0],minute:m[1],second:"00"},p=$(this).find(".class-datesheet-syllabus").val().trim();n.push({dimensionId:i,courseExamDate:o,startTime:l,endTime:u,syllabus:p}),n.length>0&&a.push({courseId:e,dimensionWiseDatesheetList:n})}}}}}}});var n=Math.min.apply(null,t),i=Math.max.apply(null,t);return{courseWiseDatesheetList:a,examStartDateInt:n,examEndDateInt:i}},generateDatesheetPdf:function(e){var a=academicSessionHandler.getSelectedSessionId(),t=$("#datesheet-config-class").find(":selected").val().trim();window.open(baseURL+"/examination/pdf-datesheet/"+e+"/"+t+"?academic_session_id="+a,"_blank")}},publishMarks={dataCache:{},loadPublishMarksPage:function(){ajaxClient.get("/examination/publish-exam-config",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(publishMarks.changeSession),publishMarks.bindClassChangeEvent()})},changeSession:function(){academicSessionHandler.getSelectedSessionId();publishMarks.loadPublishMarksPage()},bindClassChangeEvent:function(){$("#publish-exam-config-class").change(function(){""!=$(this).find(":selected").val().trim()&&publishMarks.loadPublishMarksList()})},loadPublishMarksList:function(){var e=$("#publish-exam-config-class").find(":selected").val();if(""!=e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/publish-exam-list/"+a+"/"+e,function(e){$("#publish-exam-list").html(e)})}},updateMarksStatusPopup:function(e,a){var t=JSON.parse($(e).parent().parent().find(".exam-course-json").text().trim()).examMetaData.examId;$("#update-exam-id").text(t),$("#update-exam-status").text(a);var n="Do you want to unpublish exam's marks?";"PUBLISHED"===a&&(n="Do you want to publish exam's marks?"),$("#update-exam-status-config-modal-text").text(n)},updateMarksStatus:function(){$("#update-exam-status-config-modal").modal("toggle");var e=$("#update-exam-id").text(),a=$("#update-exam-status").text();ajaxClient.post("/examination/update-exam-status/"+e+"/"+a,{},function(e){$("#publish-exam-status-modal-container").html(e),$("#publish-exam-status-modal").modal("toggle"),publishMarks.loadPublishMarksList()})},marksStatusDetails:function(e){if("true"===$(e).parent().find(".courses-details-visibility").text())return $(e).parent().find(".courses-details-visibility").text(!1),void $(".courses-marks-status-details").remove();var a=JSON.parse($(e).parent().parent().find(".exam-course-json").text().trim()).examMetaData.examId,t=academicSessionHandler.getSelectedSessionId();$(".courses-marks-status-details").remove(),ajaxClient.get("/examination/courses-marks-status-details/"+t+"/"+a,function(a){$(e).parent().parent().after(a),$(e).parent().find(".courses-details-visibility").text(!0)})},updateCourseMarksStatusPopup:function(e,a){var t=$(e).parent().find(".exam-course-dimension-id").text(),n=$(e).parent().find(".exam-course-id").text(),i=$(e).parent().find(".exam-id").text(),s=$(e).parent().find(".exam-section-id").text();$("#update-dimension-id").text(t),$("#update-course-id").text(n),$("#update-exam-id").text(i),$("#update-exam-section-id").text(s),$("#update-exam-status").text(a);$("#update-course-exam-status-config-modal-text").text("Do you want to submit course exam's marks?")},updateCourseMarksStatus:function(){$("#update-course-exam-status-config-modal").modal("toggle");var e=$("#update-dimension-id").text(),a=$("#update-course-id").text(),t=$("#update-exam-id").text(),n=$("#update-exam-section-id").text(),i=$("#update-exam-status").text(),s=academicSessionHandler.getSelectedSessionId(),o=$("#publish-exam-config-class").find(":selected").val();ajaxClient.post("/examination/update-course-exam-status/"+e+"/"+a+"/"+t+"/"+i+"/"+s+"/"+o+"?sectionId="+n,{},function(e){$("#publish-exam-status-modal-container").html(e),$("#publish-exam-status-modal").modal("toggle"),publishMarks.loadPublishMarksList()})}},marksFeeding={dataCache:{},loadMainPage:function(){ajaxClient.get("/examination/marks-feed-input-page",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(marksFeeding.changeSession),marksFeeding.bindClassChangeEvent(),marksFeeding.dataCache={}})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$("#standard-with-section-options-wrapper").html(e),marksFeeding.bindClassChangeEvent(),$("#marks-feed-main-container").html('<br><br><br><br> <br> <br> <br><br><br><br> <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to see course exams for marks updation !</h5><br><br><br><br> <br> <br> <br><br><br><br>'),marksFeeding.dataCache={}})},clearMarks:function(){$("tr.student-course-marks").each(function(){$(this).find("td.exam-marks-dimension").each(function(){$(this).find("input").val(""),$(this).find("input").removeAttr("readonly")}),$(this).find("td.exam-grade-dimension").each(function(){$(this).find("select").val(""),$(this).find("select").removeAttr("readonly")})}),marksFeeding.computeStudentMarksTotal()},bindClassChangeEvent:function(){$("#examination-standard-section-select").change(function(){marksFeeding.loadMarksFeedStructure($(this).find(":selected").val().trim())})},loadMarksFeedStructure:function(e){if(""!=e){var a=e.split(":"),t=a[0];if(""!=t){var n=null;2==a.length&&(n=a[1]);var i=academicSessionHandler.getSelectedSessionId();marksFeeding.dataCache.classMarksStructures=[],marksFeeding.dataCache.selectedStandardSectionMapId=e,marksFeeding.dataCache.selectedStandard=t,marksFeeding.dataCache.selectedSection=n,ajaxClient.get("/examination/marks-feed-structure/"+t+"/"+i,function(e){$("#marks-feed-main-container").html(e),marksFeeding.bindCourseChangeEvent();var a=readJson("#class-marks-structures-json");marksFeeding.dataCache.classMarksStructures=a,initSelect2("Select dimension")})}}},bindCourseChangeEvent:function(){$("#marks-structures-courses").change(function(){var e=$(this).find(":selected").val().trim(),a=marksFeeding.dataCache.classMarksStructures;marksFeeding.bindExamChangeEvent();for(var t=0;t<a.length;t++){var n=a[t];if(n.course.courseId==e){for(var i='<option value="" selected>Choose Exam ...</option>',s=0;s<n.examDimensionCoursesDataList.length;s++){var o=n.examDimensionCoursesDataList[s].examMetaData;o&&(i+='<option value="'+o.examId+'">'+o.examName+"</option>")}$("#marks-structures-exams").html(i);i='<option value="" selected>Choose Dimension ...</option>';$("#marks-structures-exams-dimension").html(i);break}}})},bindExamChangeEvent:function(){$("#marks-structures-exams").change(function(){for(var e=$(this).find(":selected").val().trim(),a=0,t=$("#marks-structures-courses").find(":selected").val().trim(),n=marksFeeding.dataCache.classMarksStructures,i=0;i<n.length;i++){var s=n[i];if(s.course.courseId==t){for(var o=0;o<s.examDimensionCoursesDataList.length;o++){if(s.examDimensionCoursesDataList[o].examMetaData.examId==e){for(var r="",d=s.examDimensionCoursesDataList[o].examDimensionValues,l=0;l<d.length;l++){var c=d[l].examDimension;"SCHOLASTIC"==s.course.courseType&&s.scholasticGradingEnabled?c.total&&"NUMBER"==c.examEvaluationType?marksFeeding.dataCache.totalDimensionId=c.dimensionId:(r+='<option value="'+c.dimensionId+'" selected>'+c.dimensionName+"</option>",a+=1):"COSCHOLASTIC"==s.course.courseType&&s.coscholasticGradingEnabled?c.total&&"NUMBER"==c.examEvaluationType?marksFeeding.dataCache.totalDimensionId=c.dimensionId:(r+='<option value="'+c.dimensionId+'" selected>'+c.dimensionName+"</option>",a+=1):c.total&&"GRADE"==c.examEvaluationType?marksFeeding.dataCache.gradeDimensionId=c.dimensionId:c.total&&"NUMBER"==c.examEvaluationType?marksFeeding.dataCache.totalDimensionId=c.dimensionId:(r+='<option value="'+c.dimensionId+'" selected>'+c.dimensionName+"</option>",a+=1)}marksFeeding.dataCache.dimensionDisplayCount=a,$("#marks-structures-exams-dimension").html(r);break}}break}}})},loadMarksFeedSheet:function(){var e,a=academicSessionHandler.getSelectedSessionId(),t=$("#marks-structures-courses").find(":selected").val().trim(),n=$("#marks-structures-exams").find(":selected").val().trim(),i=$("#marks-structures-exams-dimension").val(),s=$("#examination-standard-section-select").val().trim().split(":")[0].trim();marksFeeding.dataCache.selectedCourseId=t,marksFeeding.dataCache.selectedExamId=n;for(var o=marksFeeding.dataCache.classMarksStructures,r=0;r<o.length;r++){var d=o[r];d.course.courseId==t&&(e=d.course.courseType)}if(""!=t)if(""!=n)if(""!=i){var l=marksFeeding.dataCache.selectedSection,c=$("#relieved-student-checkbox").is(":checked"),m=[];m.push(marksFeeding.dataCache.totalDimensionId),"gradeDimensionId"in marksFeeding.dataCache&&m.push(marksFeeding.dataCache.gradeDimensionId);var u=(i.length===marksFeeding.dataCache.dimensionDisplayCount?[i].concat(m):[i]).join(",");ajaxClient.get("/examination/marks-feed-sheet/"+a+"/"+s+"/"+n+"/"+t+"?sectionId="+l+"&add_relieved_students="+c+"&dimension_id_str="+u+"&course_type="+e,function(e){$("#student-marks-feed-container").html(e),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[2,"asc"]]}),marksFeeding.bindComputeTotalMarks();var a=JSON.parse($("#exam-dimension-obtained-value-json").text().trim());marksFeeding.initExamDimensionObtainedValue(a)})}else showErrorDialogBox("please select Dimension");else showErrorDialogBox("Please select exam");else showErrorDialogBox("Please select course")},initExamDimensionObtainedValue:function(e){examDimensionMaxMarksValue={},examDimensionMinMarksValue={},$.each(e,function(e,a){examDimensionMaxMarksValue[a.examDimension.dimensionId]=a.originalMaxMarks,examDimensionMinMarksValue[a.examDimension.dimensionId]=a.minMarks}),marksFeeding.dataCache.examDimensionMaxMarksValue=examDimensionMaxMarksValue,marksFeeding.dataCache.examDimensionMinMarksValue=examDimensionMinMarksValue},bindComputeTotalMarks:function(){$("input.dimension-marks").focus(function(){}).blur(function(){marksFeeding.computeStudentMarksTotal()}),$(".fill-mark-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","number"),a.val(""),a.prop("readonly",!1),marksFeeding.computeStudentMarksTotal()}),$(".medical-leave-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("ML"),a.prop("readonly",!0),marksFeeding.computeStudentMarksTotal()}),$(".not-joined-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("NJ"),a.prop("readonly",!0),marksFeeding.computeStudentMarksTotal()}),$(".absent-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("AB"),a.prop("readonly",!0),marksFeeding.computeStudentMarksTotal()}),$(".exempted-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("EX"),a.prop("readonly",!0),advanceMarksFeeding.computeStudentMarksTotal()}),$("input.dimension-marks").keydown(function(e){13===e.which&&($(this).parent().next().find("input.dimension-marks").length>0?$(this).parent().next().find("input.dimension-marks").focus():$(this).closest("tr").next().find("input.dimension-marks").eq(0).focus())})},checkMaxMarks:function(e){var a=$(e).parent().parent().attr("id").split(":"),t=marksFeeding.dataCache.examDimensionMaxMarksValue[a[0]],n=marksFeeding.dataCache.examDimensionMinMarksValue[a[0]];return t<parseFloat($(e).val())?($(e).css("border-color","red"),$(e).val(""),!1):(n>parseFloat($(e).val())?$(e).attr("style","color:red"):$(e).removeAttr("style"),!0)},computeStudentMarksTotal:function(){var e=!0;if($("tr.student-course-marks").each(function(){var a=0,t=!1;$(this).find("input.dimension-marks").each(function(){"number"==$(this).attr("type")&&""!=$(this).val().trim()&&(t=!0,currentFlag=marksFeeding.checkMaxMarks(this),e&=currentFlag,currentFlag&&(a+=parseFloat($(this).val())))}),t?$(this).find("input.total-marks").val(a):$(this).find("input.total-marks").val("")}),!e)return showErrorDialogBox("Obtained marks cannot be greater than Max Marks"),void $(this).val("")},submitExamMarks:function(e){if("SUBMITTED"==e&&!confirm("You are submitting the marks. Once submitted you cannot update the marks of this course.\n Click on save instead if you want alter marks at later stage. \n Are you sure to submit the marks?"))return;var a=[],t=marksFeeding.dataCache.selectedCourseId,n=marksFeeding.dataCache.selectedExamId,i=marksFeeding.dataCache.selectedSection;$("tr.student-course-marks").each(function(){var e=$(this).attr("id"),i=[];$(this).find("td.exam-marks-dimension").each(function(){var e=$(this).attr("id").split(":")[0],a=$(this).find("input"),t=null,n=null;if("number"==a.attr("type"))t=a.val();else{var s=a.val().trim();"ML"==s?n="MEDICAL_LEAVE":"NJ"==s?n="NOT_JOINED":"AB"==s?n="ABSENT":"EX"==s&&(n="EXEMPTED")}var o={examDimension:{dimensionId:e},obtainedMarks:t,attendanceStatus:n};i.push(o)}),$(this).find("td.exam-grade-dimension").each(function(){var e=$(this).attr("id").split(":")[0],a=$(this).find("select").val(),t=null,n=null;"ML"==a?n="MEDICAL_LEAVE":"NJ"==a?n="NOT_JOINED":"AB"==a?n="ABSENT":"EX"==a?n="EXEMPTED":t={gradeId:a};var s={examDimension:{dimensionId:e},obtainedGrade:t,attendanceStatus:n};i.push(s)}),a.push({studentId:e,examId:n,courseId:t,examDimensionObtainedValues:i})});var s=academicSessionHandler.getSelectedSessionId(),o=$("#examination-standard-section-select").val().trim().split(":")[0].trim();ajaxClient.post("/examination/submit-marks/"+e+"/"+s+"/"+o+"?sectionId="+i,{studentsMarksPayload:JSON.stringify(a)},function(e){$("#marks-feed-status-modal-container").html(e),$("#exam-config\\.status-modal").modal({backdrop:"static",keyboard:!1})})}},advanceMarksFeeding={dataCache:{},loadMainPage:function(){ajaxClient.get("/examination/advance-marks-feed-input-page",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(advanceMarksFeeding.changeSession),advanceMarksFeeding.bindClassChangeEvent(),advanceMarksFeeding.dataCache={}})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$("#standard-with-section-options-wrapper").html(e),advanceMarksFeeding.bindClassChangeEvent(),$("#marks-feed-main-container").html('<br><br><br><br> <br> <br> <br><br><br><br> <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to feed marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> '),advanceMarksFeeding.dataCache={}})},bindClassChangeEvent:function(){$("#examination-standard-section-select").change(function(){var e=$(this).find(":selected").val().trim();advanceMarksFeeding.loadExamCourseStructure(e)})},loadExamCourseStructure:function(e){if(""!=e){var a=e.split(":"),t=a[0];if(""!=t){var n=null;2==a.length&&(n=a[1]);var i=academicSessionHandler.getSelectedSessionId();advanceMarksFeeding.dataCache.classMarksStructures=[],advanceMarksFeeding.dataCache.selectedStandardSectionMapId=e,advanceMarksFeeding.dataCache.selectedStandard=t,advanceMarksFeeding.dataCache.selectedSection=n;ajaxClient.get("/examination/exam-course-structure/"+t+"/"+i+"?onlyLeaveNode="+!0,function(e){$("#marks-feed-main-container").html(e),advanceMarksFeeding.bindExamChangeEvent();var a=$("#class-exam-json").text().trim();a="None"===a||void 0===a?"":JSON.parse(a),advanceMarksFeeding.dataCache.classExamsCourses=a,initSelect2("Select Course")})}}},clearMarks:function(){$("tr.student-course-marks").each(function(){$(this).find("td.exam-marks-dimension").each(function(){$(this).find("input").val(""),$(this).find("input").removeAttr("readonly")}),$(this).find("td.exam-grade-dimension").each(function(){$(this).find("select").val(""),$(this).find("select").removeAttr("readonly")})}),marksFeeding.computeStudentMarksTotal()},bindExamChangeEvent:function(){$("#marks-structures-exams").change(function(){for(var e=$(this).find(":selected").val().trim(),a=advanceMarksFeeding.dataCache.classExamsCourses,t=0;t<a.length;t++){var n=a[t];if(n.examMetaData.examId==e){for(var i="",s=0;s<n.examCourses.length;s++){var o=n.examCourses[s];i+='<option value="'+o.course.courseId+'">'+o.course.courseName+"</option>"}$("#marks-structures-courses").html(i)}}})},loadMarksFeedSheet:function(){var e=$("#marks-structures-courses").val(),a=$("#marks-structures-exams").find(":selected").val().trim();if(advanceMarksFeeding.dataCache.selectedCourseId=e,advanceMarksFeeding.dataCache.selectedExamId=a,""!=e)if(""!=a){var t=advanceMarksFeeding.dataCache.selectedSection,n=$("#relieved-student-checkbox").is(":checked"),i=(e=e.join(),academicSessionHandler.getSelectedSessionId()),s=$("#examination-standard-section-select").val().trim().split(":")[0].trim();ajaxClient.get("/examination/advance-marks-feed-sheet/"+a+"/"+s+"/"+i+"?sectionId="+t+"&courseId="+e+"&add_relieved_students="+n,function(e){$("#student-marks-feed-container").html(e),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[2,"asc"]]}),advanceMarksFeeding.bindComputeTotalMarks();var a=JSON.parse($("#exam-dimension-obtained-value-json").text().trim());advanceMarksFeeding.initExamCourseDimensionObtainedValue(a)})}else showErrorDialogBox("please select exam");else showErrorDialogBox("Please select course")},initExamCourseDimensionObtainedValue:function(e){examCourseDimensionMaxMarksValue={},examCourseDimensionMinMarksValue={},$.each(e,function(e,a){examCourseDimensionMaxMarksValue[e]=examCourseDimensionMaxMarksValue[e]||{},examCourseDimensionMinMarksValue[e]=examCourseDimensionMinMarksValue[e]||{},$.each(a.examDimensionObtainedValues,function(a,t){examCourseDimensionMaxMarksValue[e][t.examDimension.dimensionId]=t.originalMaxMarks,examCourseDimensionMinMarksValue[e][t.examDimension.dimensionId]=t.minMarks})}),advanceMarksFeeding.dataCache.examCourseDimensionMaxMarksValue=examCourseDimensionMaxMarksValue,advanceMarksFeeding.dataCache.examCourseDimensionMinMarksValue=examCourseDimensionMinMarksValue},bindComputeTotalMarks:function(){$("input.dimension-marks").focus(function(){}).blur(function(){advanceMarksFeeding.computeStudentMarksTotal()}),$(".fill-mark-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","number"),a.val(""),a.prop("readonly",!1),advanceMarksFeeding.computeStudentMarksTotal()}),$(".medical-leave-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("ML"),a.prop("readonly",!0),advanceMarksFeeding.computeStudentMarksTotal()}),$(".not-joined-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("NJ"),a.prop("readonly",!0),advanceMarksFeeding.computeStudentMarksTotal()}),$(".absent-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("AB"),a.prop("readonly",!0),advanceMarksFeeding.computeStudentMarksTotal()}),$(".exempted-option").off("click").on("click",function(e){var a=$(this).closest(".exam-marks-dimension").find("input.dimension-marks");a.attr("type","text"),a.val("EX"),a.prop("readonly",!0),advanceMarksFeeding.computeStudentMarksTotal()}),$("input.dimension-marks").keydown(function(e){13===e.which&&($(this).parent().next().find("input.dimension-marks").length>0?$(this).parent().next().find("input.dimension-marks").focus():$(this).closest("tr").next().find("input.dimension-marks").eq(0).focus())})},checkMaxMarks:function(e){var a=$(e).parent().parent().attr("id").split(":"),t=advanceMarksFeeding.dataCache.examCourseDimensionMaxMarksValue[a[2]][a[0]],n=advanceMarksFeeding.dataCache.examCourseDimensionMinMarksValue[a[2]][a[0]];return t<parseFloat($(e).val())?($(e).css("border-color","red"),$(e).val(""),!1):(n>parseFloat($(e).val())?$(e).attr("style","color:red"):$(e).removeAttr("style"),!0)},computeStudentMarksTotal:function(){var e=!0;if($("tr.student-course-marks").each(function(){var a=!1,t={};$(this).find("input.dimension-marks").each(function(){if("number"==$(this).attr("type")&&""!=$(this).val().trim()&&(a=!0,currentFlag=advanceMarksFeeding.checkMaxMarks(this),e&=currentFlag,currentFlag)){var n=$(this).parent().parent().attr("id").split(":")[2],i=parseFloat($(this).val());t[n]=t[n]||0,t[n]=t[n]+i}}),a?$(this).find("input.total-marks").each(function(){var e=$(this).parent().attr("id").split(":"),a=(e[0].trim(),e[1].trim(),e[2].trim()),n=t[a];$(this).val(n)}):$(this).find("input.total-marks").each(function(){$(this).val("")})}),!e)return showErrorDialogBox("Obtained marks cannot be greater than Max Marks"),void $(this).val("")},submitExamMarks:function(e){if("SUBMITTED"==e&&!confirm("You are submitting the marks. Once submitted you cannot update the marks of this course.\n Click on save instead if you want alter marks at later stage. \n Are you sure to submit the marks?"))return;var a=[],t=advanceMarksFeeding.dataCache.selectedExamId,n=advanceMarksFeeding.dataCache.selectedSection;$("tr.student-course-marks").each(function(){var e=$(this).attr("id"),n={};$(this).find("td.exam-marks-dimension").each(function(){var e=$(this).attr("id").split(":")[0],a=$(this).attr("id").split(":")[2],t=$(this).find("input"),i=null,s=null;if("number"==t.attr("type"))i=t.val();else{var o=t.val().trim();"ML"==o?s="MEDICAL_LEAVE":"NJ"==o?s="NOT_JOINED":"AB"==o?s="ABSENT":"EX"==o&&(s="EXEMPTED")}var r={examDimension:{dimensionId:e},obtainedMarks:i,attendanceStatus:s};n[a]=n[a]||[],n[a].push(r)}),$(this).find("td.exam-grade-dimension").each(function(){var e=$(this).attr("id").split(":")[0],a=$(this).attr("id").split(":")[2],t=$(this).find("select").val(),i=null,s=null;"ML"==t?s="MEDICAL_LEAVE":"NJ"==t?s="NOT_JOINED":"AB"==t?s="ABSENT":"EX"==t?s="EXEMPTED":i={gradeId:t};var o={examDimension:{dimensionId:e},obtainedGrade:i,attendanceStatus:s};n[a]=n[a]||[],n[a].push(o)}),$.each(n,function(n,i){a.push({studentId:e,examId:t,courseId:n,examDimensionObtainedValues:i})})});var i=academicSessionHandler.getSelectedSessionId(),s=$("#examination-standard-section-select").val().trim().split(":")[0].trim();ajaxClient.post("/examination/submit-marks/"+e+"/"+i+"/"+s+"?sectionId="+n,{studentsMarksPayload:JSON.stringify(a)},function(e){$("#marks-feed-status-modal-container").html(e),$("#exam-config\\.status-modal").modal({backdrop:"static",keyboard:!1})})}},marksView={dataCache:{},loadMainPage:function(){ajaxClient.get("/examination/marks-view-input-page",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(marksView.changeSession),marksView.bindClassChangeEvent(),marksView.dataCache={}})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$("#standard-with-section-options-wrapper").html(e),marksView.bindClassChangeEvent(),$("#marks-view-main-container").html('<br><br><br><br> <br> <br> <br><br><br><br> <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to view marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> '),marksView.dataCache={}})},bindClassChangeEvent:function(){$("#examination-standard-section-select").change(function(){var e=$(this).find(":selected").val().trim();marksView.loadExamCourseStructure(e)})},loadExamCourseStructure:function(e){if(""!=e){var a=e.split(":"),t=a[0];if(""!=t){var n=null;2==a.length&&(n=a[1]);var i=academicSessionHandler.getSelectedSessionId();marksView.dataCache.classMarksStructures=[],marksView.dataCache.selectedStandardSectionMapId=e,marksView.dataCache.selectedStandard=t,marksView.dataCache.selectedSection=n,ajaxClient.get("/examination/exam-course-structure/"+t+"/"+i,function(e){$("#marks-view-main-container").html(e),marksView.bindExamChangeEvent();var a=readJson("#class-exam-courses-json");marksView.dataCache.classExamsCourses=a})}}},bindExamChangeEvent:function(){$("#class-exams").change(function(){for(var e=$(this).find(":selected").val().trim(),a=marksView.dataCache.classExamsCourses,t=0;t<a.length;t++){var n=a[t];if(n.examMetaData.examId==e){for(var i='<option value="" selected>Choose Course ...</option>',s=0;s<n.examCourses.length;s++){var o=n.examCourses[s];i+='<option value="'+o.course.courseId+'">'+o.course.courseName+"</option>"}$("#class-exam-courses").html(i)}}})},loadMarksViewSheet:function(){var e=$("#class-exams").find(":selected").val().trim(),a=$("#class-exam-courses").find(":selected").val().trim();if(marksView.dataCache.selectedCourseId=a,marksView.dataCache.selectedExamId=e,""!=a)if(""!=e){var t=$("#relieved-student-checkbox").is(":checked"),n=marksView.dataCache.selectedSection,i=academicSessionHandler.getSelectedSessionId(),s=$("#examination-standard-section-select").val().trim().split(":")[0].trim();ajaxClient.get("/examination/marks-view-sheet/"+i+"/"+s+"/"+e+"/"+a+"?sectionId="+n+"&add_relieved_students="+t,function(e){$("#student-marks-view-container").html(e),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[2,"asc"]]})})}else showErrorDialogBox("please select exam");else showErrorDialogBox("Please select course")},generateViewMarksReport:function(){$(this).closest("div.modal").modal("toggle");var e=$("#class-exams").find(":selected").val().trim(),a=$("#class-exam-courses").find(":selected").val().trim();if(marksView.dataCache.selectedCourseId=a,marksView.dataCache.selectedExamId=e,""!=a)if(""!=e){var t=$("#relieved-student-checkbox").is(":checked"),n=marksView.dataCache.selectedSection,i=academicSessionHandler.getSelectedSessionId(),s=$("#examination-standard-section-select").val().trim().split(":")[0].trim();n=null==n?"":n.trim();var o=$("#student-per-page").val(),r=$("#student-sorting-parameters").val();window.open(baseURL+"/examination/generate-feed-marks-sheet/"+i+"/"+s+"/"+e+"?section_id="+n+"&student_count="+o+"&add_relieved_students="+t+"&course_id="+a+"&student_sorting_parameters="+r,"_blank")}else showErrorDialogBox("please select exam");else showErrorDialogBox("Please select course")}},personalityTraits={dataCache:{},loadMainPage:function(){ajaxClient.get("/examination/personality-traits-home",function(e){$("#main-content").html(e),personalityTraits.initPage(),academicSessionHandler.bindSessionChangeEvent(personalityTraits.changeSession)})},initPage:function(){personalityTraits.registerUpdateSectionsModal(),personalityTraits.registerAddSectionsModal(),personalityTraits.registerDeleteSectionsModal()},clearCache:function(){personalityTraits.dataCache={}},changeSession:function(){personalityTraits.loadManageSectionslist()},loadManageSectionslist:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/personality-traits/"+e,function(e){$("#personality-traits-list").html(e),personalityTraits.initPage(),personalityTraits.clearCache()})},registerUpdateSectionsModal:function(){$(".update-personality-traits").on("click",function(){var e=$(this).parent().find(".personality-traits-info").text().trim(),a=JSON.parse(e);personalityTraits.dataCache.sectionDetails=a,$("#update-personality-standard-id").val(a.standard.standardId);for(var t="",n=0;n<a.personalityTraitsDetailsList.length;n++){var i=a.personalityTraitsDetailsList[n].personalityTraitName;html='<div class="form-group  col-md-8"><input  type="text" class="form-control update-rename-manage-sections add-label-input mandatory-field update.section-name" placeholder="Enter Personality Trait"   value="'+i+'"></div>',t+=html}$("#update-personality-traits-div").html(t),$("#update-manage-sections-modal").modal("toggle")})},registerAddSectionsModal:function(){$(".add-personality-traits").on("click",function(){var e=$(this).parent().find(".personality-traits-info").text().trim(),a=JSON.parse(e),t=(academicSessionHandler.getSelectedSessionId(),a.standard.standardId);$("#add-personality-standard-id").val(t);$("#add-personality-traits-div").html('<div class="form-group row sections-remove col-md-8"><input id="sectionInput"  type="text" class="form-control add-rename-manage-sections col-md-9 add-label-input mandatory-field add.section-name" placeholder="Enter Personality Trait" ></div>'),$("#add-manage-sections-modal").modal("toggle"),personalityTraits.clearCache()})},addMoreClass:function(e){html=$("#add-personality-traits-div").html(),newHtml='<div class="form-group row sections-remove col-md-8"><input id="sectionInput"  type="text" class="form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name" placeholder="Enter Personality Trait"  value=""><button type="button" class="col-md-2 close" data-dismiss="modal" aria-label="Close" onclick="personalityTraits.removeSectionDetailsRow(this)"><span aria-hidden="true"> × </span></button></div>',$("#add-personality-traits-div").append(newHtml)},removeSectionDetailsRow:function(e){$(e).parent().remove()},addSectionsDetails:function(){if(!validations($("#add-manage-sections-modal"))){var e=getAddSectionsName(),a=Array.from(new Set(e)),t=getHiddenSectionNames();if(e.length!=a.length)return $("#add-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Duplicate Personality Traits");for(var n=commonBetweenTwoNames(e,t),i="",s=0;s<t.length;s++)i+=t[s],t.length>s+1&&(i+=",");if(0!=n.length)return $("#add-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Personality Trait "+i+" are already present. Please write a different Personality Traits");var o=[],r=($("input.standard:checked").parent().parent().find(".add-rename-manage-sections").val(),academicSessionHandler.getSelectedSessionId()),d=$("#add-personality-standard-id").val();for(s=0;s<e.length;s++)section_dict={academicSessionId:r,personalityTraitId:null,personalityTraitName:e[s],standardId:d},o.push(section_dict);$("#add-manage-sections-modal").modal("toggle"),ajaxClient.post("/examination/bulk-add-personality-trait/"+r,{personalityTraitsDetailList:JSON.stringify(o)},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),personalityTraits.loadManageSectionslist()})}},registerDeleteSectionsModal:function(){$(".delete-personality-traits").on("click",function(){var e=$(this).parent().find(".personality-traits-info").text().trim(),a=JSON.parse(e),t=(academicSessionHandler.getSelectedSessionId(),a.standard.standardId);$("#delete-personality-standard-id").val(t);for(var n="",i=0;i<a.personalityTraitsDetailsList.length;i++){var s=a.personalityTraitsDetailsList[i].personalityTraitName,o=a.personalityTraitsDetailsList[i].personalityTraitId;html='<div class="form-group row sections-remove col-md-8"><input type="checkbox" class="section-id" name="" value=""><p class="bulk-section-id" style="display:none;">'+o+'</p><input id="sectionInput"  type="text" class="form-control ml-2 delete-rename-manage-sections add-label-input mandatory-field delete.section-name col-md-8" placeholder="Enter Personality Trait"  value="'+s+'"readonly></div>',n+=html}$("#delete-personality-traits-div").html(n),$("#delete-manage-sections-modal").modal("toggle"),personalityTraits.clearCache()})},deleteSectionDetails:function(){if(!validations($("#delete-manage-sections-modal"))){var e=personalityTraits.getDictionarySectionInfos(),a=(academicSessionHandler.getSelectedSessionId(),$("#delete-personality-standard-id").val());if(options="",checkedSection=e[0],unCheckedSection=e[1],0==checkedSection.length)return $("#delete-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Please Select atleast one personality trait");if(0!=unCheckedSection.length){html1="";for(var t=0;t<unCheckedSection.length;t++)html1="<option value="+unCheckedSection[t].personalityTraitId+">"+unCheckedSection[t].personalityTraitName+"</option>",options+=html1;mainhtml='<p>Where do you wish to move them?</p><select class="col-md-6 form-control which-section mandatory-field" name="">'+options+"</select>"}$("#delete-manage-sections-modal").modal("toggle");var n=[],i=[];for(t=0;t<checkedSection.length;t++)sectionId=checkedSection[t].personalityTraitId,sectionName=checkedSection[t].personalityTraitName,n.push(sectionId),i.push(sectionName);n=n.join(","),personalityTraits.dataCache.sectionIds=n,html='<p style="display:none;" class="std-id">'+a+"</p>Do you wish to delete the Personality Traits?",$("#delete\\.standard-sections-manage-id").html(html),$("#delete-id-manage-sections-modal").modal("toggle")}},deleteStudentSectionsDetails:function(){if($("#delete-id-manage-sections-modal").modal("toggle"),!validations($("#delete-id-manage-sections-modal"))){section_ids=personalityTraits.dataCache.sectionIds;var e=academicSessionHandler.getSelectedSessionId(),a=$(".std-id").html();ajaxClient.post("/examination/bulk-delete-personality-trait/"+e+"/"+a+"/"+section_ids,{},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),personalityTraits.loadManageSectionslist()})}},getDictionarySectionInfos:function(){var e=[],a=[],t=[];return $("input.section-id").each(function(){$(this).is(":checked")?(sectionName=$(this).parent().find("input.delete-rename-manage-sections").val(),sectionId=$(this).parent().find("p.bulk-section-id").first().text().trim(),section_dict={personalityTraitId:sectionId,personalityTraitName:sectionName},t.push(section_dict)):(sectionName=$(this).parent().find("input.delete-rename-manage-sections").val(),sectionId=$(this).parent().find("p.bulk-section-id").first().text().trim(),section_dict={personalityTraitId:sectionId,personalityTraitName:sectionName},a.push(section_dict))}),e.push(t),e.push(a),e},updateSectionsDetails:function(){if(!validations($("#update-manage-sections-modal"))){var e;e=getUpdateSectionsName();var a=Array.from(new Set(e));if(e.length!=a.length)return $("#update-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Duplicate Personality Traits");for(var t=[],n=academicSessionHandler.getSelectedSessionId(),i=$("#update-personality-standard-id").val(),s=personalityTraits.dataCache.sectionDetails.personalityTraitsDetailsList,o=0;o<s.length;o++)section_dict={academicSessionId:n,personalityTraitId:s[o].personalityTraitId,personalityTraitName:e[o],standardId:i},t.push(section_dict);$("#update-manage-sections-modal").modal("toggle"),ajaxClient.post("/examination/bulk-update-personality-trait/"+n,{personalityTraitsDetailList:JSON.stringify(t)},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),personalityTraits.loadManageSectionslist()})}}},reportCardVariables={dataCache:{},loadMainPage:function(){ajaxClient.get("/examination/report-card-variables-input-view",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(reportCardVariables.changeSession),reportCardVariables.bindClassChangeEvent(),reportCardVariables.dataCache={}})},changeSession:function(){reportCardVariables.loadMainPageWithSessionDetails()},loadMainPageWithSessionDetails:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/report-card-variables/session-details/"+e,function(e){$("#marks-view-main-container").html(e),reportCardVariables.bindClassChangeEvent(),reportCardVariables.dataCache={}})},bindClassChangeEvent:function(){$("#marks-view-class").change(function(){var e=$(this).find(":selected").val().trim();if(reportCardVariables.dataCache.standardSelected=e,""!=e){var a=e.split(":")[0];if(""!=a){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/report-card-variable-types/"+t+"/"+a,function(e){$("#report-card-type").html(e),$("#student-marks-feed-container").empty()})}}}),$("#report-card-type").change(function(){$("#student-marks-feed-container").empty()})},loadViewReportCardVariablesScreen:function(){var e=$("#report-card-details-option").val();e===REPORT_CARD_VARIABLES?reportCardVariables.loadViewReportCardVariablesSheet():e===PERSONALITY_TRAITS&&reportCardVariables.loadViewPersonalityTraitsSheet()},loadViewPersonalityTraitsSheet:function(){if(validateMandatoryFields($("#page-top-class-report-type")))showErrorDialogBox("Please fill mandatory fields.");else if(standardSectionMapId=reportCardVariables.dataCache.standardSelected,""!=standardSectionMapId){var e=standardSectionMapId.split(":"),a=e[0];if(""!=a){var t=null;2==e.length&&(t=e[1]);var n=academicSessionHandler.getSelectedSessionId(),i=$("#report-card-type").val();ajaxClient.get("/examination/view-personality-trait/"+a+"/"+n+"?sectionId="+t+"&reportType="+i,function(e){$("#student-marks-feed-container").html(e),initDateWithYearRange("-5:+5"),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[2,"asc"]]}),reportCardVariables.populatePersonalityTraitSheetData()})}}},populatePersonalityTraitSheetData:function(){$("tr.personality-traits-student-row").each(function(){var e=JSON.parse($(this).find(".student_personality_trait_details").text()).studentPersonalityTraitsResponseList,a={};if(null!=e&&null!=e)for(var t=0;t<e.length;t++){var n=e[t],i=n.response,s=n.personalityTraitsDetails.personalityTraitId;a[s]=i}$(this).find("input.personality-trait-input").each(function(){var e=$(this).attr("id"),t=a[e];null==t&&(t=""),$(this).val(t)})})},loadViewReportCardVariablesSheet:function(){if(validateMandatoryFields($("#page-top-class-report-type")))showErrorDialogBox("Please fill mandatory fields.");else if(standardSectionMapId=reportCardVariables.dataCache.standardSelected,""!=standardSectionMapId){var e=standardSectionMapId.split(":"),a=e[0];if(""!=a){var t=null;2==e.length&&(t=e[1]);var n=academicSessionHandler.getSelectedSessionId(),i=$("#report-card-type").val();ajaxClient.get("/examination/view-report-card-variables/"+a+"/"+n+"?sectionId="+t+"&reportType="+i,function(e){$("#student-marks-feed-container").html(e),initDateWithYearRange("-5:+5"),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[1,"asc"]]})})}}},autofillTotalDays:function(){var e=$("#report-variable-total-days").val();$(".total-days").val(e)},autofillDateOfResultDeclaration:function(){var e=$("#report-variable-date-of-result-declaration").val();$(".date-of-result-declaration").val(e)},submitPersonalityTraits:function(e,a){if(validateMandatoryFields($("#marks-view-main-container")))showErrorDialogBox("Please fill mandatory fields.");else{var t=[];$("tr.personality-traits-student-row").each(function(){var e=$(this).attr("id");$(this).find("input.personality-trait-input").each(function(){var a=$(this).attr("id"),n=$(this).val(),i={studentId:e,personalityTraitId:a,remarks:n};t.push(i)})});var n=a;null!=n&&""!==n||(n=$("#report-card-type").find(":selected").val());var i=e;null!=i&&""!==i||(i=academicSessionHandler.getSelectedSessionId()),ajaxClient.post("/examination/submit-personality-traits/"+i+"/"+n,{studentPersonalityTraitsPayloadList:JSON.stringify(t)},function(e){$("#report-card-variables-view-container").html(e),$("#report-card-status-modal").modal({backdrop:"static",keyboard:!1})})}},submitReportCardVariables:function(e,a){if(validateMandatoryFields($("#marks-view-main-container")))showErrorDialogBox("Please fill mandatory fields.");else{var t=[];$("tr.student-row").each(function(){var e=$(this).attr("id"),a=$(this).find(".height").val(),n=$(this).find(".weight").val(),i=$(this).find(".attended-days").val(),s=$(this).find(".total-days").val(),o=getDate($(this).find(".date-of-result-declaration").val()),r=null;null!=o&&(r=o.getTime()/1e3);var d=$(this).find(".remarks").val();"true"===$(this).find(".remark-dropdown").text()&&(d=$(this).find(".remarks option:selected").text());var l=$(this).find(".principal-remarks").val();"true"===$(this).find(".principal-remark-dropdown").text()&&(l=$(this).find(".principal-remarks option:selected").text());var c={studentId:e,attendedDays:i,remarks:d,principalRemarks:l,height:a,weight:n,totalDays:s,dateOfResultDeclaration:r};t.push(c)});var n=a;null!=n&&""!==n||(n=$("#report-card-type").find(":selected").val());var i=e;null!=i&&""!==i||(i=academicSessionHandler.getSelectedSessionId());var s={instituteId:null,reportType:n,academicSessionId:i,studentReportCardVariableDetailsPayload:t};ajaxClient.post("/examination/submit-report-card-variables",{reportCardVariableDetails:JSON.stringify(s)},function(e){$("#report-card-variables-view-container").html(e),$("#report-card-status-modal").modal({backdrop:"static",keyboard:!1})})}},openReportCard:function(e){var a=$("#report-card-type").find(":selected").val(),t=academicSessionHandler.getSelectedSessionId();if(standardSectionMapId=reportCardVariables.dataCache.standardSelected,""!=standardSectionMapId){var n=standardSectionMapId.split(":"),i=n[0];if(""!=i){var s=null;2==n.length&&(s=n[1]);var o=window.open(baseURL+"/examination/report-card-variable-page/"+t+"/"+e+"/"+a+"/"+i+"?sectionId="+s+"&reportType="+a,"_blank");reportCardVariables.loadIFrame(t,e,a,o),hideLoader()}}},loadIFrame:function(e,a,t,n){ajaxClient.get("/examination/student-report-variable-pdf/"+e+"/"+a+"/"+t,function(e){$("#report-card-byte-array-div").html(e);var a=JSON.parse($("#report-card-byte-array").text()),t=new Uint8Array(a),i="data:application/pdf;base64,"+reportCardVariables.uint8ArrayToBase64(t)+"#toolbar=0&navpanes=0&scrollbar=0";n.document.getElementById("report-card-iframe").src=i,$("#report-card-byte-array-div").html(""),hideLoader()})},uint8ArrayToBase64:function(e){for(var a="",t=e.byteLength,n=0;n<t;n++)a+=String.fromCharCode(e[n]);return window.btoa(a)}},gradeRowConfig={gradeId:"",gradeValue:"",marksRangeStart:"",marksRangeEnd:"",gradeName:"",rangeDisplayName:"",remark:"",creditScore:"",newTd:"",marksRangeStartDisabled:"",marksRangeEndDisabled:"",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScoreDisabled:""},gradeScheme={dataCache:{},loadGradeSchemeListPage:function(){ajaxClient.get("/examination/exam-grade-details",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(gradeScheme.changeSession),$("#grade-screen-back-button").hide(),initSelect2("Select Class"),gradeScheme.populateGradeSchemeModal()})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/exam-grade-details-with-session/"+e,function(e){$("#grade-scheme-div").html(e),initSelect2("Select Class"),$("#grade-screen-back-button").hide(),gradeScheme.populateGradeSchemeModal()})},populateGlobalHtmlCode:function(e,a,t,n){if(e){if(a||t)return`\n                <div class="row">\n                    <div class="col-md-12">\n                        <table class="table table-bordered">\n                            <thead>\n                                <tr>\n                                    <th scope="col" colspan="6">\n                                        <div class="d-flex justify-content-center align-items-center">\n                                            <span style="font-size: 1rem;">\n                                                ${n}\n                                            </span>\n                                            ${t?'\n                                                <div style="position: absolute; right: 20%;">\n                                                    <button type="button" class="btn btn-outline-primary btn-sm copyGrade"  onclick="gradeScheme.copyGrade()">\n                                                        Same As Scholastic Grade\n                                                    </button>\n                                                </div>\n                                            ':""}\n                                        </div>\n                                    </th>\n                                </tr>\n                                <tr class="text-center">\n                                    <th scope="col">Start Range</th>\n                                    <th scope="col">End Range</th>\n                                    <th scope="col">Grade Name</th>\n                                    <th scope="col">Display Range Name</th>\n                                    <th scope="col">Remarks</th>\n                                    <th scope="col">Credit Score</th>\n                                </tr>\n                            </thead>\n                            <tbody>\n            `}else if(a||t){return`\n            <tr id="${a?"add-scholastic-grade-add-row-bottom":"add-coscholastic-grade-add-row-bottom"}" style="display:none;"></tr>\n            </tbody>\n            </table>\n            </div>\n            </div>\n        `}},populateGlobalTableRow:function(e){return`\n      <tr class="${e.trClass}">\n        <td>\n          <p class="grade-id" style="display:none">${e.gradeId}</p>\n          <p class="grade-value" style="display:none">${e.gradeValue}</p>\n          <p class="grade-name" style="display:none">${e.gradeName}</p>\n          <input type="number" class="form-control form-control-sm add-start-range" placeholder="Start Range..." value="${e.marksRangeStart}" ${e.marksRangeStartDisabled}>\n        </td>\n        <td>\n          <input type="number" class="form-control form-control-sm add-end-range" placeholder="End Range..." value="${e.marksRangeEnd}" ${e.marksRangeEndDisabled}>\n        </td>\n        <td>\n          <input type="text" class="form-control form-control-sm add-grade-name" placeholder="Grade Name..."  value="${e.gradeName}" ${e.gradeNameDisabled}>\n        </td>\n        <td>\n          <input type="text" class="form-control form-control-sm add-display-range-name" placeholder="Display Range Name..." value="${e.rangeDisplayName}" ${e.rangeDisplayNameDisabled}>\n        </td>\n        <td>\n          <input type="text" class="form-control form-control-sm add-remark" placeholder="Remarks..." value="${e.remark}" ${e.remarkDisabled}>\n        </td>\n        <td>\n          <input type="text" class="form-control form-control-sm add-credit-score" placeholder="Credit Score..." value="${e.creditScore}" ${e.creditScoreDisabled}>\n        </td>\n        ${e.newTd}\n      </tr>\n    `},populateAddGradeSchemeModal:function(){$(".select2-search__field").css("width","100%"),$(".screen-name").text("ADD"),$("#action-grade-detail-button").text("Add Grade Details");var e=gradeScheme.populateGlobalHtmlCode(!0,!0,!1,"Scholastic Grade");e+=gradeScheme.populateGlobalHtmlCode(!1,!0,!1,"Scholastic Grade"),$("#add-scholastic-grade-input-div").html(e);var a=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");a+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0,"Co-Scholastic Grade"),$("#add-coscholastic-grade-input-div").html(a),$("#grade-details-div").hide(),$("#grade-screen-back-button").show(),$(".add-grade-div").hide(),$("#update-grade-scheme-screen").show()},gradeInfoDataFormating:function(e,a,t,n){if(void 0===a){if("RENAME"==n||"VIEW"==n){e+='<tr class="text-center"><td colspan=5 style="text-align: center;">No Grade</td></tr>'}return e}for(var i=0;i<a.length;i++){var s=a[i],o=Object.assign({},gradeRowConfig,{gradeId:s.gradeId,gradeValue:s.gradeValue,marksRangeStart:100*s.marksRangeStart,marksRangeEnd:1.1==s.marksRangeEnd?100:100*s.marksRangeEnd>=100?100:100*s.marksRangeEnd,gradeName:s.gradeName,rangeDisplayName:s.rangeDisplayName,remark:s.remarks||"",creditScore:0===s.creditScore?0:s.creditScore||"",trClass:t,marksRangeStartDisabled:"disabled",marksRangeEndDisabled:"disabled",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScoreDisabled:"",newTd:""});"RENAME"==n&&(e+=gradeScheme.populateGlobalTableRow(o)),"UPDATE"==n&&(o.gradeId="",o.gradeValue="",o.marksRangeStartDisabled="",o.marksRangeEndDisabled="",o.gradeNameDisabled="",o.rangeDisplayNameDisabled="",o.remarkDisabled="",o.creditScoreDisabled="",o.newTd='<td><button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"><span aria-hidden="true">&times;</span></button></td>',e+=gradeScheme.populateGlobalTableRow(o)),"VIEW"==n&&(o.marksRangeStartDisabled="disabled",o.marksRangeEndDisabled="disabled",o.gradeNameDisabled="disabled",o.rangeDisplayNameDisabled="disabled",o.remarkDisabled="disabled",o.creditScoreDisabled="disabled",o.newTd="",e+=gradeScheme.populateGlobalTableRow(o))}return e},populateGradeSchemeModal:function(){$(".rename-grade-details").on("click",function(){var e=$(this).parent().parent().parent().find(".grade-scheme-info").text().trim(),a=$(this).parent().parent().parent().find(".grade-standard-info").text().trim(),t=JSON.parse(e),n=JSON.parse(a);$("#standard-selected-details").val(n.standardId).trigger("change"),$(".screen-name").text("RENAME"),$("#action-grade-detail-button").text("Update Grade Details");var i=gradeScheme.populateGlobalHtmlCode(!0,!0,!1,"Scholastic Grade");i=gradeScheme.gradeInfoDataFormating(i,t.SCHOLASTIC,"new-scholastic-grade-row","RENAME"),i+=gradeScheme.populateGlobalHtmlCode(!1,!0,!1,"Scholastic Grade"),$("#add-scholastic-grade-input-div").html(i);var s=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");s=gradeScheme.gradeInfoDataFormating(s,t.COSCHOLASTIC,"new-co-scholastic-grade-row","RENAME"),s+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0,"Co-Scholastic Grade"),$("#add-coscholastic-grade-input-div").html(s),null==t.COSCHOLASTIC&&$(".copyGrade").hide(),$("#grade-screen-back-button").show(),$("#grade-details-div").hide(),$(".ranges").hide(),$("#add-scholastic-grade-button").hide(),$("#add-coscholastic-grade-button").hide(),$(".add-grade-div").hide(),$("#update-grade-scheme-screen").show()}),$(".update-grade-details").on("click",function(){var e=$(this).parent().parent().parent().find(".grade-scheme-info").text().trim(),a=$(this).parent().parent().parent().find(".grade-standard-info").text().trim(),t=JSON.parse(e),n=JSON.parse(a);$("#standard-selected-details").val(n.standardId).trigger("change"),$("#startRange").prop("checked",!0),$(".screen-name").text("UPDATE"),$("#action-grade-detail-button").text("Update Grade Details");var i=gradeScheme.populateGlobalHtmlCode(!0,!0,!1,"Scholastic Grade");i=gradeScheme.gradeInfoDataFormating(i,t.SCHOLASTIC,"new-scholastic-grade-row","UPDATE"),i+=gradeScheme.populateGlobalHtmlCode(!1,!0,!1,"Scholastic Grade"),$("#add-scholastic-grade-input-div").html(i);var s=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");s=gradeScheme.gradeInfoDataFormating(s,t.COSCHOLASTIC,"new-co-scholastic-grade-row","UPDATE"),s+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0,"Co-Scholastic Grade"),$("#add-coscholastic-grade-input-div").html(s),$("#grade-details-div").hide(),$("#grade-screen-back-button").show(),$(".add-grade-div").hide(),$("#update-grade-scheme-screen").show()}),$(".view-grade").on("click",function(){var e=$(this).parent().parent().find(".grade-scheme-info").text().trim(),a=$(this).parent().parent().find(".grade-standard-info").text().trim(),t=JSON.parse(e),n=JSON.parse(a);$("#standard-selected-details").val(n.standardId).trigger("change").prop("disabled",!0);var i=gradeScheme.populateGlobalHtmlCode(!0,!0,!1,"Scholastic Grade");i=gradeScheme.gradeInfoDataFormating(i,t.SCHOLASTIC,"new-scholastic-grade-row","VIEW"),i+=gradeScheme.populateGlobalHtmlCode(!1,!0,!1,"Scholastic Grade"),$("#add-scholastic-grade-input-div").html(i);var s=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");s=gradeScheme.gradeInfoDataFormating(s,t.COSCHOLASTIC,"new-co-scholastic-grade-row","VIEW"),s+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0,"Co-Scholastic Grade"),$("#add-coscholastic-grade-input-div").html(s),$("#grade-details-div").hide(),$(".ranges").hide(),$("#grade-screen-back-button").show(),$(".add-grade-div").hide(),$(".copyGrade").hide(),$("#close-grade-detail-button").hide(),$("#action-grade-detail-button").hide(),$("#add-scholastic-grade-button").hide(),$("#add-coscholastic-grade-button").hide(),$("#update-grade-scheme-screen").show()}),$(".delete-grade").on("click",function(){var e=$(this).parent().parent().find(".grade-standard-info").text().trim(),a=JSON.parse(e);$("#delete-grade-standard-id").text(a.standardId),$("#delete-manage-grade-modal").modal("toggle")}),$("#close-grade-detail-button").on("click",function(){$(".update-grade-scheme-screen").hide(),$("#grade-details-div").show(),$(".ranges").show(),$(".add-grade-div").show(),$(".copyGrade").show(),$("#add-scholastic-grade-button").show(),$("#add-coscholastic-grade-button").show(),$("#grade-screen-back-button").hide(),gradeScheme.loadGradeSchemeListPage()}),$("#grade-screen-back-button").on("click",function(){$(".update-grade-scheme-screen").hide(),$("#grade-details-div").show(),$(".ranges").show(),$(".add-grade-div").show(),$(".copyGrade").show(),$("#add-scholastic-grade-button").show(),$("#add-coscholastic-grade-button").show(),$("#grade-screen-back-button").hide(),$("#close-grade-detail-button").show(),$("#action-grade-detail-button").show(),gradeScheme.loadGradeSchemeListPage()}),$("#action-grade-detail-button").on("click",function(){var e=$(".screen-name").text();"RENAME"==e?gradeScheme.renameGradeDetail():"UPDATE"==e?gradeScheme.gradeDetailInputData("UPDATE"):"ADD"==e&&gradeScheme.gradeDetailInputData("ADD")})},copyGrade:function(){var e=$(".screen-name").text();"RENAME"==e?gradeScheme.copyRenameScholasticGradeDetail():"UPDATE"==e?gradeScheme.copyAddOrUpdateScholasticGradeDetail():"ADD"==e&&gradeScheme.copyAddOrUpdateScholasticGradeDetail()},getBlankGradeRow:function(e){return'<tr class="new-grade-row"> <td><input type="number" class="form-control form-control-sm add-start-range" style="width: 75px;" placeholder="Start Range..."></td> <td><input type="number" class="form-control form-control-sm add-end-range" style="width: 75px;" placeholder="End Range..."></td>  <td><input type="text" class="form-control form-control-sm add-grade-name" placeholder="Grade Name..." style="width: 75px;"></td> <td><input type="text" class="form-control form-control-sm add-display-range-name" style="width: 150px;" placeholder="Display Range Name..."></td> <td><input type="text" class="form-control form-control-sm add-remark" placeholder="Remarks..."></td> <td><input type="text" class="form-control form-control-sm add-credit-score" placeholder="Credit Score..."></td><td> <button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'},addNewScholasticGradeRow:function(){var e="add-scholastic-grade-add-row-bottom";var a=Object.assign({},gradeRowConfig,{gradeId:"",gradeValue:"",marksRangeStart:"",marksRangeEnd:"",gradeName:"",rangeDisplayName:"",remark:"",creditScore:"",trClass:"new-scholastic-grade-row",marksRangeStartDisabled:"",marksRangeEndDisabled:"",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScoreDisabled:"",newTd:'<td> <button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td>'});$("#"+e).before(gradeScheme.populateGlobalTableRow(a))},addNewCoScholasticGradeRow:function(){var e="add-coscholastic-grade-add-row-bottom";var a=Object.assign({},gradeRowConfig,{gradeId:"",gradeValue:"",marksRangeStart:"",marksRangeEnd:"",gradeName:"",rangeDisplayName:"",remark:"",creditScore:"",trClass:"new-co-scholastic-grade-row",marksRangeStartDisabled:"",marksRangeEndDisabled:"",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScoreDisabled:"",newTd:'<td> <button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td>'});$("#"+e).before(gradeScheme.populateGlobalTableRow(a))},deleteGradeRow(e){$(e).parent().parent().remove()},deleteGradeDetails:function(){var e=academicSessionHandler.getSelectedSessionId(),a=[$("#delete-grade-standard-id").text().trim()];$("#delete-manage-grade-modal").modal("toggle"),ajaxClient.post("/examination/delete-grades/"+e+"?standardIdList="+a,{},function(e){$("#grade-scheme-generation-status-modal-container").html(e),$("#grade-status-modal").modal("toggle"),$("#grade-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){gradeScheme.loadGradeSchemeListPage()})})},copyAddOrUpdateScholasticGradeDetail:function(){var e=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");$("tr.new-scholastic-grade-row").each(function(){var a=$(this).find(".add-start-range").val().trim(),t=$(this).find(".add-end-range").val().trim(),n=$(this).find(".add-grade-name").val().trim(),i=$(this).find(".add-display-range-name").val().trim(),s=$(this).find(".add-remark").val().trim(),o=$(this).find(".add-credit-score").val().trim(),r=Object.assign({},gradeRowConfig,{gradeId:"",gradeValue:"",marksRangeStart:a,marksRangeEnd:t,gradeName:n,rangeDisplayName:i,remark:s,creditScore:o,trClass:"new-co-scholastic-grade-row",marksRangeStartDisabled:"",marksRangeEndDisabled:"",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScore:"",newTd:'<td> <button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td>'}),d=gradeScheme.populateGlobalTableRow(r);e+=d}),e+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0),e+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0),$("#add-coscholastic-grade-input-div").html(e)},copyRenameScholasticGradeDetail:function(){var e=$("tr.new-co-scholastic-grade-row"),a=$("tr.new-scholastic-grade-row"),t=gradeScheme.populateGlobalHtmlCode(!0,!1,!0,"Co-Scholastic Grade");e.each(function(e){var n=$(this).find(".grade-id").text().trim(),i=$(this).find(".grade-value").text().trim(),s=$(this).find(".add-start-range").val().trim(),o=$(this).find(".add-end-range").val().trim(),r=a.eq(e),d=r.find(".add-grade-name").val().trim(),l=r.find(".add-display-range-name").val().trim(),c=r.find(".add-remark").val().trim(),m=r.find(".add-credit-score").val().trim(),u=Object.assign({},gradeRowConfig,{gradeId:n,gradeValue:i,marksRangeStart:s,marksRangeEnd:o,gradeName:d,rangeDisplayName:l,remark:c,creditScore:m,trClass:"new-co-scholastic-grade-row",marksRangeStartDisabled:"disabled",marksRangeEndDisabled:"disabled",gradeNameDisabled:"",rangeDisplayNameDisabled:"",remarkDisabled:"",creditScores:"",newTd:""}),p=gradeScheme.populateGlobalTableRow(u);t+=p}),t+=gradeScheme.populateGlobalHtmlCode(!1,!1,!0,"Co-Scholastic Grade"),$("#add-coscholastic-grade-input-div").html(t)},renameGradeDetail:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#standard-selected-details").val();if(!a||a.length<=0)alert("Please select at least one class to add/update grades.");else{var t={},n={},i=!0;if($("tr.new-scholastic-grade-row").each(function(){var e=$(this).find(".grade-id").text().trim(),a=$(this).find(".grade-value").text().trim(),n=$(this).find(".grade-name").text().trim(),s=$(this).find(".add-start-range").val().trim(),o=$(this).find(".add-end-range").val().trim(),r=$(this).find(".add-grade-name").val().trim(),d=$(this).find(".add-display-range-name").val().trim(),l=$(this).find(".add-remark").val().trim(),c=$(this).find(".add-credit-score").val().trim();if(""==r||""==d)return alert("Field Can't be Empty for Grade Name or Display Range Name"),i=!1,!1;t[n]={gradeId:e,gradeName:r,gradeValue:a,marksRangeStart:s,marksRangeEnd:o,rangeDisplayName:d,remarks:l,creditScore:c}}),i){n.SCHOLASTIC=t,t={};i=!0;if($("tr.new-co-scholastic-grade-row").each(function(){var e=$(this).find(".grade-id").text().trim(),a=$(this).find(".grade-value").text().trim(),n=$(this).find(".grade-name").text().trim(),s=$(this).find(".add-start-range").val().trim(),o=$(this).find(".add-end-range").val().trim(),r=$(this).find(".add-grade-name").val().trim(),d=$(this).find(".add-display-range-name").val().trim(),l=$(this).find(".add-remark").val().trim(),c=$(this).find(".add-credit-score").val().trim();if(""==r||""==d)return alert("Field Can't be Empty for Grade Name or Display Range Name"),i=!1,!1;t[n]={gradeId:e,gradeName:r,gradeValue:a,marksRangeStart:s,marksRangeEnd:o,rangeDisplayName:d,remarks:l,creditScore:c}}),i){n.COSCHOLASTIC=t;var s={standardIdList:a,courseTypeExaminationGradesMap:n};ajaxClient.post("/examination/rename-grades/"+e,{gradeRenamePayload:JSON.stringify(s)},function(e){$("#grade-scheme-generation-status-modal-container").html(e),$("#grade-status-modal").modal("toggle")})}}}},gradeDetailInputData:function(e){var a=academicSessionHandler.getSelectedSessionId(),t=$("#standard-selected-details").val(),n=$("#startRange").prop("checked"),i=$("#endRange").prop("checked");if(!t||t.length<=0)alert("Please select at least one class to add/update grades.");else if(n||i){var s=[],o={},r=!0;if($("tr.new-scholastic-grade-row").each(function(){var e=$(this).find(".add-start-range").val().trim(),a=$(this).find(".add-end-range").val().trim(),t=$(this).find(".add-grade-name").val().trim(),n=$(this).find(".add-display-range-name").val().trim(),i=$(this).find(".add-remark").val().trim(),o=$(this).find(".add-credit-score").val().trim();if(""===e||""===a||""===t||""===n)return alert("Field Can't be Empty for Start Range or End Range or Grade Name or Display Range Name"),r=!1,!1;s.push({gradeName:t,marksRangeStart:e,marksRangeEnd:a,rangeDisplayName:n,remarks:i,creditScore:o})}),r&&(o.SCHOLASTIC=s,s=[],r=!0,$("tr.new-co-scholastic-grade-row").each(function(){var e=$(this).find(".add-start-range").val().trim(),a=$(this).find(".add-end-range").val().trim(),t=$(this).find(".add-grade-name").val().trim(),n=$(this).find(".add-display-range-name").val().trim(),i=$(this).find(".add-remark").val().trim(),o=$(this).find(".add-credit-score").val().trim();if(""===e||""===a||""===t||""===n)return alert("Field Can't be Empty for Start Range or End Range or Grade Name or Display Range Name"),r=!1,!1;s.push({gradeName:t,marksRangeStart:e,marksRangeEnd:a,rangeDisplayName:n,remarks:i,creditScore:o})}),r))if(o.COSCHOLASTIC=s,o.SCHOLASTIC&&0!==o.SCHOLASTIC.length||o.COSCHOLASTIC&&0!==o.COSCHOLASTIC.length){var d={standardIdList:t,marksRangeStartInclusive:n,marksRangeEndInclusive:i,courseTypeExaminationGradesMap:o,dataUpdationAction:e};ajaxClient.post("/examination/add-update-grades/"+a,{gradeAddUpdatePayload:JSON.stringify(d)},function(e){$("#grade-scheme-generation-status-modal-container").html(e),$("#grade-status-modal").modal("toggle")})}else alert("Both SCHOLASTIC and COSCHOLASTIC grade schemes are empty. Please provide valid entries.")}else alert("At least one should be checked from 'Marks Starting Range Inclusive' and 'Marks Ending Range Inclusive'")}},reportCard={dataCache:{},loadReportCardPage:function(){ajaxClient.get("/examination/report-card-generation-homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(reportCard.changeSession),reportCard.bindSearchStudentEvent(),reportCard.bindReportCardGenerationOptions(),reportCard.bindClassChangeEvent()})},changeSession:function(){$("#report-card-generation-search-student-text").val(""),$("#student-report-card-view").html(""),$(".report-card-types").html("");var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$(".standard-with-section-options-wrapper").html(e),reportCard.bindClassChangeEvent()})},bindReportCardGenerationOptions:function(){$("#student-report-card-generation-option").on("click",function(){$(".report-card-screen").attr("style","display:none;"),$("#student-report-card-screen").attr("style","display:block;"),reportCard.loadReportCardPage()}),$("#bulk-student-report-card-generation-option").on("click",function(){$(".report-card-screen").attr("style","display:none;"),$("#bulk-student-report-card-screen").attr("style","display:block;")}),$("#class-report-card-generation-option").on("click",function(){$(".report-card-screen").attr("style","display:none;"),$("#class-report-card-screen").attr("style","display:block;")})},loadClassStudents:function(){var e=$(".report-card-types").val(),a=$("#examination-standard-section-select").val();if(""!=a&&null!=e){$("#send-notifications-class-student-list").attr("style","display:block");var t=a.split(":"),n=t[0].trim(),i="";t.length>1&&(i=t[1].trim());var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/class-students/"+s+"?standards="+n+"&sectionId="+i,function(e){$("#send-notifications-class-student-list").html(e)})}else showErrorDialogBox("Please select standard and report type")},bindClassChangeEvent:function(){$(".report-card-change").change(function(){$("#send-notifications-class-student-list").attr("style","display:none;");var e=$(this).find(":selected").val().trim().split(":"),a=e[0].trim();e[1];e[1];if(""!=a){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/report-card-types/"+t+"/"+a,function(e){$(".class-report").html(e)})}})},bindSearchStudentEvent:function(){var e="#report-card-generation-student-search-result";$("#report-card-generation-search-student").on("click",function(){reportCard.doneStudentSearchTyping(e)}),$("#report-card-generation-search-student-text").on("keyup",function(a){13==a.keyCode&&reportCard.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#report-card-generation-search-student-text",e,reportCard.doneStudentSearchTyping)},doneStudentSearchTyping:function(e){var a=$("#report-card-generation-search-student-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),a,e,reportCard.loadStudentReportCard,"ENROLLED,RELIEVED")},loadStudentReportCard:function(e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/report-card-student-details/"+a+"/"+e,function(e){$("#student-report-card-view").html(e),reportCard.registerReportCardGenerationCallBack()})},registerReportCardGenerationCallBack:function(){$("#student-generate-report-card-button").on("click",function(){var e=$("#report-card-view-student-id").text().trim(),a=$("#report-card-type").val().trim();reportCard.generateReportCard(e,a)})},generateReportCard:function(e,a){var t=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/examination/student-report-card/"+t+"/"+e+"/"+a,"_blank")},studentSelectCheckbox:function(e){var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)},selectStudentList:function(e){if(e.checked){$(".student-select-checkbox").prop("checked",!0);var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)}else $(".student-select-checkbox").prop("checked",!1),$("#students-selected-count").html(0)},generateStudentsBulkReportCard:function(){var e=$("#examination-standard-section-select").val().trim(),a=$("#class-report-type").find(".report-card-types").find(":selected").val().trim(),t=academicSessionHandler.getSelectedSessionId(),n=[];if($("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();n.push(e)}}),n=n.join(","),""!=e)if(""!=a){var i=e.split(":"),s=i[0].trim(),o="";i.length>1&&(o=i[1].trim()),window.open(baseURL+"/examination/bulk-student-report-card/"+t+"/"+n+"/"+s+"/"+a+"?sectionId="+o,"_blank")}else showErrorDialogBox("Please select report type for generating report");else showErrorDialogBox("Please select class for generating report")},generateClassReportCard:function(e){var a=$(".class-standard-filter").find(".report-card-change").val(),t=academicSessionHandler.getSelectedSessionId();if(""!=a){var n=$("#only-class-report-type").find(".report-card-types").find(":selected").val().trim();if(""!=n){var i=a.split(":"),s=i[0].trim(),o="";i.length>1&&(o=i[1].trim());var r="";e&&(r=$("#student-per-page").val(),$("#generate-class-report-data-modal").modal("toggle")),window.open(baseURL+"/examination/class-report-card/"+t+"/"+s+"/"+n+"?sectionId="+o+"&studentPerPage="+r,"_blank")}else showErrorDialogBox("Please select report type for generating report")}else showErrorDialogBox("Please select class for generating report")}},hpc={dataCache:{},loadHPCFormPage:function(){ajaxClient.get("/examination/hpc/form/homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(hpc.changeSession),hpc.bindSearchStudentEvent(),hpc.dataCache={}})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$("#standard-with-section-options-wrapper").html(e),$("#student-hpc-form-view").html(""),$("#relieved-student-checkbox").prop("checked",!1),hpc.dataCache={}})},bindSearchStudentEvent:function(){var e="#hpc-form-generation-student-search-result";$("#hpc-form-generation-search-student").on("click",function(){hpc.doneStudentSearchTyping(e)}),$("#hpc-form-generation-search-student-text").on("keyup",function(a){13==a.keyCode&&hpc.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#hpc-form-generation-search-student-text",e,hpc.doneStudentSearchTyping)},doneStudentSearchTyping:function(e){var a=$("#hpc-form-generation-search-student-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),a,e,hpc.loadStudentHPCForm,"ENROLLED")},loadStudentHPCForm:function(e,a){hpc.dataCache.selectedStudentId=e,hpc.dataCache.selectedStudentInfo=a;var t=academicSessionHandler.getSelectedSessionId(),n=$("#hpc-exam-type").val().trim();$("#hpc-form-student-name-adm-no").text(a),$("#student-hpc-form-generation-search-tab").attr("style","display:none"),$("#hpc-academic-session-div").attr("style","display:none"),$("#fill-hpc-form-navbar").attr("style","display: "),ajaxClient.get("/examination/hpc/student-form/"+t+"/"+e+"/"+n,function(e){$("#student-hpc-form-view").html(e),hpc.registerUploadFileCallback()})},submitForm:async function(e,a,t){if("SUBMITTED"==e&&!confirm("You are submitting the form. Once submitted you cannot update the form of this student.\n Click on save instead if you want alter form at later stage. \n Are you sure to submit the form?"))return;var n=academicSessionHandler.getSelectedSessionId(),i=$("#hpc-exam-type").val().trim(),s=hpc.dataCache.selectedStudentId,o=[];$(t).closest(".student-hpc-form").find("input.hpc-form-value").each(function(){var e=$(this).attr("id"),a=$(this).val().trim();o.push({id:e,value:a})}),$(t).closest(".student-hpc-form").find("select.hpc-form-dropdown-value").each(function(){var e=$(this).attr("id"),a=$(this).find("option:selected").text().trim();o.push({id:e,value:a})});var r=[],d=[],l=!1;if($(t).closest(".student-hpc-form").find("input.hpc-form-document").each(function(){var e=$(this)[0].files;if(!(e.length<=0))for(var a=0;a<e.length;a++){var t=e[a];if(t.size/ONE_KB>FILE_SIZE_LIMIT)return l=!0,void showErrorDialogBox("Size Of document cannot be greater than "+FILE_SIZE_LIMIT+" kb");if(l)return;d.push(t);var n=$(this).attr("id"),i=$(this).parent().find(".image-type").text(),o={documentType:i,documentName:s+"-"+i};r.push({id:n,document:o,dataUpdationAction:"ADD"})}}),console.log(r),console.log(d),console.log(l),!l){var c={academicSessionId:n,studentId:s,examType:i,formUserType:a,status:e,fieldValues:o,hpcDocumentPayloadList:r},m=new FormData;Array.from(d).forEach(e=>m.append("document",e)),m.append("payload",JSON.stringify(c)),ajaxClient.uploadFile("/examination/hpc/form-data",m,function(e){$("#hpc-status-modal-container").html(e),$("#hpc-form-status-modal").modal({backdrop:"static",keyboard:!1}),hpc.loadStudentHPCForm(s,hpc.dataCache.selectedStudentInfo)})}},loadClassStudents:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#examination-standard-section-select").val();if(""!=a){var t="ENROLLED";$("#relieved-student-checkbox").is(":checked")&&(t+=",RELIEVED"),ajaxClient.get("/examination/hpc/class-students-list/"+e+"?status="+t+"&standardSection="+a,function(e){$("#student-hpc-form-view").html(e),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[3,"asc"]]})})}else showErrorDialogBox("Please select standard ")},back:function(){hpc.loadClassStudents(),$("#student-hpc-form-generation-search-tab").attr("style","display: "),$("#hpc-academic-session-div").attr("style","display: "),$("#fill-hpc-form-navbar").attr("style","display:none")},downloadDocument:function(e,a){var t=academicSessionHandler.getSelectedSessionId(),n=$("#hpc-exam-type").val().trim(),i=hpc.dataCache.selectedStudentId;window.open(baseURL+"/examination/hpc/download-document/"+t+"/"+n+"/"+a+"/"+i+"/"+e,"_blank")},deleteDocument:function(e,a){var t=academicSessionHandler.getSelectedSessionId(),n=$("#hpc-exam-type").val().trim(),i=hpc.dataCache.selectedStudentId;ajaxClient.post("/examination/hpc/delete-document/"+t+"/"+n+"/"+a+"/"+i+"/"+e,{},function(e){$("#hpc-status-modal-container").html(e),$("#hpc-form-status-modal").modal({backdrop:"static",keyboard:!1}),hpc.loadStudentHPCForm(i)})},registerUploadFileCallback:function(){$(".hpc-form-document").on("change",function(){var e=$(this).val().split("\\");e.length<=0?showErrorDialogBox("Invalid file"):$(this).next(".custom-file-label").html(e[e.length-1])})}},hpcReportCard={dataCache:{},loadHPCReportCardPage:function(){ajaxClient.get("/examination/hpc-report-card-generation-homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(hpcReportCard.changeSession),hpcReportCard.bindSearchStudentEvent(),hpcReportCard.bindReportCardGenerationOptions()})},changeSession:function(){$("#hpc-report-card-generation-search-student-text").val(""),$("#student-hpc-report-card-view").html(""),$(".hpc-report-card-types").html("");var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$(".hpc-standard-with-section-options-wrapper").html(e)})},bindReportCardGenerationOptions:function(){$("#student-hpc-report-card-generation-option").on("click",function(){$(".hpc-report-card-screen").attr("style","display:none;"),$("#student-hpc-report-card-screen").attr("style","display:block;"),hpcReportCard.loadHPCReportCardPage()}),$("#bulk-student-hpc-report-card-generation-option").on("click",function(){$(".hpc-report-card-screen").attr("style","display:none;"),$("#bulk-student-hpc-report-card-screen").attr("style","display:block;")}),$("#class-hpc-report-card-generation-option").on("click",function(){$(".hpc-report-card-screen").attr("style","display:none;"),$("#class-hpc-report-card-screen").attr("style","display:block;")})},loadClassStudents:function(){var e=$("#class-hpc-report-card-type").val(),a=$("#examination-standard-section-select").val();if(""!=a&&null!=e){$("#send-notifications-hpc-class-student-list").attr("style","display:block");var t=a.split(":"),n=t[0].trim(),i="";t.length>1&&(i=t[1].trim());var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/hpc-class-students/"+s+"?standards="+n+"&sectionId="+i,function(e){$("#send-notifications-hpc-class-student-list").html(e)})}else showErrorDialogBox("Please select standard and report type")},bindSearchStudentEvent:function(){var e="#hpc-report-card-generation-student-search-result";$("#hpc-report-card-generation-search-student").on("click",function(){hpcReportCard.doneStudentSearchTyping(e)}),$("#hpc-report-card-generation-search-student-text").on("keyup",function(a){13==a.keyCode&&hpcReportCard.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#hpc-report-card-generation-search-student-text",e,hpcReportCard.doneStudentSearchTyping)},doneStudentSearchTyping:function(e){var a=$("#hpc-report-card-generation-search-student-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),a,e,hpcReportCard.loadStudentReportCard,"ENROLLED,RELIEVED")},loadStudentReportCard:function(e){var a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/hpc-report-card-student-details/"+a+"/"+e,function(e){$("#student-hpc-report-card-view").html(e),hpcReportCard.registerReportCardGenerationCallBack()})},registerReportCardGenerationCallBack:function(){$("#student-generate-hpc-report-card-button").on("click",function(){var e=$("#hpc-report-card-view-student-id").text().trim(),a=$("#hpc-report-card-type").val().trim();hpcReportCard.generateReportCard(e,a)})},generateReportCard:function(e,a){var t=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/examination/student-hpc-report-card/"+t+"/"+e+"/"+a,"_blank")},studentSelectCheckbox:function(e){var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)},selectStudentList:function(e){if(e.checked){$(".student-select-checkbox").prop("checked",!0);var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)}else $(".student-select-checkbox").prop("checked",!1),$("#students-selected-count").html(0)},generateStudentsBulkReportCard:function(){var e=$("#examination-standard-section-select").val().trim(),a=$("#class-hpc-report-card-type").val(),t=academicSessionHandler.getSelectedSessionId(),n=[];if($("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();n.push(e)}}),n=n.join(","),""!=e)if(""!=a){var i=e.split(":"),s=i[0].trim(),o="";i.length>1&&(o=i[1].trim()),window.open(baseURL+"/examination/bulk-student-hpc-report-card/"+t+"/"+n+"/"+s+"/"+a+"?sectionId="+o,"_blank")}else showErrorDialogBox("Please select report type for generating report");else showErrorDialogBox("Please select class for generating report")},generateClassReportCard:function(e){var a=$(".class-standard-filter").find(".report-card-change").val(),t=academicSessionHandler.getSelectedSessionId();if(""!=a){var n=$("#only-class-hpc-report-type").val();if(""!=n){var i=a.split(":"),s=i[0].trim(),o="";i.length>1&&(o=i[1].trim());var r="";e&&(r=$("#student-per-page").val(),$("#generate-class-report-data-modal").modal("toggle")),window.open(baseURL+"/examination/class-hpc-report-card/"+t+"/"+s+"/"+n+"?sectionId="+o+"&studentPerPage="+r,"_blank")}else showErrorDialogBox("Please select report type for generating report")}else showErrorDialogBox("Please select class for generating report")}},examinationReports={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/reports",function(e){$("#main-content").html(e);var a=readJson("#session-class-exams");examinationReports.dataCache.sessionClassExams=a;readJson("#session-class-courses");examinationReports.dataCache.sessionClassCourses=a;var t=readJson("#current-session");initSelect2(),initSelect2("None","report-cumulative-exams"),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),examinationReports.bindSessionDropDown(),examinationReports.bindStandardDropDown(),examinationReports.bindLeafExamNodeStandardDropDown(),examinationReports.bindGenerateReportEvent(),examinationReports.checkboxEvents(),$(".report-academic-session").val(t.academicSessionId),$(".rank-till").val(1)})},getStandardMap:function(e){for(var a={},t=0;t<e.length;t++){var n=e[t];a[n.standardId]=n}return a},bindStandardDropDown:function(){$(".report-standards").on("change",function(){var e=$(this).closest(".modal-body").find(".report-academic-session").find(":selected").val().trim(),a=$(this).find(":selected").val().trim();$(this).closest(".modal-body").find(".report-exams").html(""),$(this).closest(".modal-body").find(".report-courses").html(""),examinationReports.fillStudentSections(this,e,a),examinationReports.fillExamsDropDown(this,e,a),examinationReports.fillCumulativeExamsDropDown(this,e,a),$("input.show-dimensions").prop("checked",!1),$("input.exclude-coscholastic-subjects").prop("checked",!1),$("input.show-scholastic-grade").prop("checked",!1),$("input.show-co-scholastic-grade").prop("checked",!1),$("input.relieved-student-checkbox").prop("checked",!1),$(a).closest(".modal-body").find(".report-standard-section").html(""),$(this).closest(".modal-body").find(".report-standard-section").html(""),$(a).closest(".modal-body").find(".section-ids-multiple").html(""),$(this).closest(".modal-body").find(".section-ids-multiple").html(""),$(this).closest(".modal-body").find(".report-courses").html("")})},bindLeafExamNodeStandardDropDown:function(){$("#select-standards").on("change",function(){var e=$(this).closest(".modal-body").find(".report-academic-session").find(":selected").val().trim(),a=$(this).find(":selected").val().trim();examinationReports.fillStudentSections(this,e,a),ajaxClient.get("/examination/reports-only-leaf-exams/"+a+"/"+e,function(e){$("#leaf-exams").html(e)})})},fillStudentSections:function(e,a,t){ajaxClient.get("/examination/section-details/"+t+"/"+a,function(e){$(".section-json-wrapper").html(e);for(var n=readJson("#standard-details-p"),i={},s=0;s<n.length;s++){i[(r=n[s]).standardId]=r}var o="";if(t in i){var r=i[t];for(s=0;s<r.standardSectionList.length;s++)o+='<option value="'+r.standardSectionList[s].sectionId+'">'+r.standardSectionList[s].sectionName+"</option>";$(".report-standard-section").html(o),$(".section-ids-multiple").html(o),examinationReports.fillCoursesDropDown(this,a,t)}else $("select.student-section").html(o)})},populateReportCardTypeSelectOptions:function(e){for(var a='<option value=""></option>',t=examinationReports.getReportCardType(e),n=0;n<t.length;n++){var i=t[n];a+='<option value="'+i.reportCardType+'">'+i.reportCardName+"</option>"}$(e).closest(".modal-body").find(".report-type-name").html(a)},getReportStandards:function(e){return JSON.parse($(e).closest(".modal-body").find(".standards-json").text().trim())},getReportCardType:function(e){if($(e).closest(".modal-body").find(".report-card-type-json").length>0)return JSON.parse($(e).closest(".modal-body").find(".report-card-type-json").text().trim())},bindSessionDropDown:function(){$(".report-academic-session").on("change",function(){var e=$(this).find(":selected").val().trim(),a=this;ajaxClient.get("/examination/standards-json/"+e,function(e){$(a).closest(".modal-body").find(".different-value-wrapper").html(e),examinationReports.bindStandardDropDown(),$(a).closest(".modal-body").find(".report-card-type-json").length>0&&examinationReports.populateReportCardTypeSelectOptions(a),$(a).closest(".modal-body").find(".report-standard-section").html(""),$(a).closest(".modal-body").find(".section-ids-multiple").html(""),$(a).closest(".modal-body").find(".report-exams").html(""),$(a).closest(".modal-body").find(".report-courses").html(""),$(a).closest(".modal-body").find(".report-standards").val("")})})},fillExamsDropDown:function(e,a,t){if(""!=a&&""!=t){var n='<option value = "" ></option>';if($(e).closest(".modal-body").find(".report-exams").html(n),a in examinationReports.dataCache.sessionClassExams){var i=examinationReports.dataCache.sessionClassExams[a];if(t in i){for(var s=i[t],o=0;o<s.length;o++)n+='<option value="'+s[o].examId+'">'+s[o].examName+"</option>";$(e).closest(".modal-body").find(".report-exams").html(n)}}}},fillCumulativeExamsDropDown:function(e,a,t){if(""!=a&&""!=t){var n='<option value = "" ></option>';if($(e).closest(".modal-body").find(".report-cumulative-exams").html(n),a in examinationReports.dataCache.sessionClassExams){var i=examinationReports.dataCache.sessionClassExams[a];if(t in i){for(var s=i[t],o=0;o<s.length;o++)n+='<option value="'+s[o].examId+'">'+s[o].examName+"</option>";$(e).closest(".modal-body").find(".report-cumulative-exams").html(n)}}}},fillCoursesDropDown:function(e,a,t){ajaxClient.get("/examination/exam-course-details/"+t+"/"+a,function(e){$(".report-courses").html(e)})},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){$(this).closest("div.modal").modal("toggle");var e=$(this).closest("div.report-field-container"),a=reportUtils.getReportHeadersCSV(e),t=$(e).find("p.report-type").text().trim(),n=$(e).find(".report-academic-session option:selected").val(),i=$(e).find(".report-standards option:selected").val(),s=$(e).find(".report-standard-section").val(),o=$(e).find(".report-exams option:selected").val();if(""!=n&&""!=i&&""!=o){var r=!1;$(e).find("input.show-scholastic-grade").is(":checked")&&(r=!0);var d=!1;$(e).find("input.show-co-scholastic-grade").is(":checked")&&(d=!0),excludeCoscholasticSubjects=$(e).find("input.exclude-coscholastic-subjects").is(":checked"),showDimensions=$(e).find("input.show-dimensions").is(":checked"),showTotalColumnDimension=!1,s=void 0===s?"":s.join(",").trim(),window.open(baseURL+"/examination/generate-report/"+o+"?section_id_str="+s+"&exclude_coscholastic_subjects="+excludeCoscholasticSubjects+"reportType="+t+"&show_dimensions="+showDimensions+"&show_total_column_dimension="+showTotalColumnDimension+"&show_coscholastic_grade="+d+"&show_scholastic_grade="+r+"&requiredHeaders="+a,"_blank")}else showErrorDialogBox("Please select all the required fields to generate report")}),$(".generate-feed-marks-sheet").on("click",function(){$(this).closest("div.modal").modal("toggle");var e=$(this).closest("div.report-field-container"),a=$(e).find(".report-academic-session option:selected").val(),t=$(e).find(".report-standards option:selected").val(),n=$(e).find(".report-standard-section option:selected").val(),i=$(e).find(".report-exams option:selected").val(),s=$(e).find(".relieved-student-checkbox").is(":checked");if(""!=a&&""!=t&&""!=i){n=void 0===n?"":n.trim();var o=$(e).find(".student-per-page").val(),r=$(e).find("#student-sorting-parameters").val();window.open(baseURL+"/examination/generate-feed-marks-sheet/"+a+"/"+t+"/"+i+"?section_id="+n+"&student_count="+o+"&add_relieved_students="+s+"&course_id=&student_sorting_parameters="+r,"_blank")}else showErrorDialogBox("Please select all the required fields to generate report")}),$(".course-report").on("click",function(){$(this).closest("div.modal").modal("toggle");var e=$(this).closest("div.report-field-container"),a=$(e).find(".report-academic-session option:selected").val(),t=$(e).find(".report-standards option:selected").val(),n=$(e).find(".report-standard-section option:selected").val(),i=$(e).find(".report-exams option:selected").val();if(""!=a&&""!=t&&""!=i){$(e).find("input.compute-rank").is(":checked")&&!0,excludeCoscholasticSubjects=$(e).find("input.exclude-coscholastic-subjects").is(":checked"),showDimensions=!1,showTotalColumnDimension=!1,n=void 0===n?"":n.trim(),window.open(baseURL+"/examination/generate-course-report/"+t+"/"+i+"?section_id="+n+"&exclude_coscholastic_subjects="+excludeCoscholasticSubjects+"&show_dimensions="+showDimensions+"&show_total_column_dimension="+showTotalColumnDimension+"&academicSessionId="+a,"_blank")}else showErrorDialogBox("Please select all the required fields to generate report")}),$(".generate-reports").on("click",function(){var e=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(e))){var a=$(e).find(".report-academic-session option:selected").val(),t=$(e).find("p.report-type").text().trim(),n=$(e).find(".report-standards option:selected").val();n=void 0===n?"":n.trim();var i=$(e).find(".report-standard-section option:selected").val();i=void 0===i?"":i.trim();var s=$(e).find(".report-courses option:selected").val();s=void 0===s?"":s.trim();var o=$(e).find(".report-course-type option:selected").val();o=void 0===o?"":o.trim();var r=$(e).find(".report-exams option:selected").val();r=void 0===r?"":r.trim();var d=$(e).find(".report-staff option:selected").val();d=void 0===d?"":d.trim();var l=$(e).find(".report-type-name").val();l=void 0===l?"":l.trim();var c=$(e).find(".section-ids-multiple").val();c=void 0===c?"":c.join(",");var m=$(e).find(".exam-ids-multiple").val();m=void 0===m?"":m.join(",");var u=$(e).find(".course-ids-multiple").val();u=void 0===u?"":u.join(",");var p="";p=void 0===(p=$(e).find(".report-cumulative-exams").val())?"":p.join(",");var h=$(e).find(".rank-till").val();void 0===h&&(h="");var f=reportUtils.getReportHeadersCSV(e);$(e).find("input.show-scholastic-grade").is(":checked")&&!0;$(e).find("input.show-co-scholastic-grade").is(":checked")&&!0;var g=$(e).find("input.exclude-coscholastic-subjects").is(":checked"),v=$(e).find("input.sort-student-on-rank").is(":checked"),x=$(e).find(".additional-course-ids-multiple").val();x=void 0===x?"":x.join(","),$(this).closest("div.modal").modal("toggle");var b=$(e).find(".scholastic-marks-display-type-multiple").val();b=void 0===b?"":b.join(",");var C=$(e).find(".coscholastic-marks-display-type-multiple").val();C=void 0===C?"":C.join(",");var S=!1;$(e).find("input.show-class-average-details").is(":checked")&&(S=!0);var k=!1;$(e).find("input.show-staff-details").is(":checked")&&(k=!0),window.open(baseURL+"/examination/generate-reports/"+t+"?academic_session_id="+a+"&standard_id="+n+"&section_id="+i+"&exam_id="+r+"&course_type="+o+"&course_id="+s+"&staffId="+d+"&reportTypeName="+l+"&sectionIdsStr="+c+"&examIdsStr="+m+"&courseIdsStr="+u+"&compareCumulativeExamIdsStr="+p+"&rankTill="+h+"&exclude_coscholastic_subjects="+g+"&requiredHeaders="+f+"&sort_student_on_rank="+v+"&additionalCourseIdsStr="+x+"&scholasticMarksDisplayTypeStr="+b+"&coScholasticMarksDisplayTypeStr="+C+"&show_class_average_details="+S+"&show_staff_details="+k,"_blank")}}),$(".generate-graphical-report").on("click",function(){var e=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(e))){var a=$(e).find(".report-academic-session option:selected").val(),t=$(e).find("p.report-type").text().trim(),n=$(e).find(".report-standards option:selected").val();n=void 0===n?"":n.trim();var i=$(e).find(".report-standard-section option:selected").val();i=void 0===i?"":i.trim();var s=$(e).find(".report-exams option:selected").val();s=void 0===s?"":s.trim();var o=$(e).find(".section-ids-multiple").val();o=void 0===o?"":o.join(",");var r=$(e).find(".exam-ids-multiple").val();r=void 0===r?"":r.join(","),$(this).closest("div.modal").modal("toggle");var d="/examination/generate-graphical-reports/"+t+"?academic_session_id="+a+"&standard_id="+n+"&section_id="+i+"&exam_id="+s+"&sectionIdsStr="+o+"&examIdsStr="+r+"&courseIdsStr=";ajaxClient.get(d,function(e){var a=window.open(baseURL+d,"_blank");a.document.open(),a.history.pushState({},"",d),a.document.write(e),a.document.close(),a.onload=function(){graphicalReportGenerator.loadGraphicalData(a.document,a)}})}})},checkboxEvents:function(){$(".all-column-select").on("change",function(){const e=$(this).closest(".report-header-container").find(".select-report-column");$(this).is(":checked")?e.prop("checked",!0):e.each(function(){$(this).is(":disabled")||$(this).prop("checked",!1)})})},bindClassChangeEvent:function(){$("#examination-standard-section-select").change(function(){var e=$(this).find(":selected").val().trim();marksView.loadExamCourseStructure(e)})},loadExamCourseStructure:function(e){if(""!=e){var a=e.split(":"),t=a[0];if(""!=t){var n=null;2==a.length&&(n=a[1]);var i=academicSessionHandler.getSelectedSessionId();marksView.dataCache.classMarksStructures=[],marksView.dataCache.selectedStandardSectionMapId=e,marksView.dataCache.selectedStandard=t,marksView.dataCache.selectedSection=n,ajaxClient.get("/examination/exam-course-structure/"+t+"/"+i,function(e){$("#marks-view-main-container").html(e),marksView.bindExamChangeEvent();var a=readJson("#class-exam-courses-json");marksView.dataCache.classExamsCourses=a})}}}},admitCards={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/admitcard",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(admitCards.changeSession);var a=readJson("#session-class-exams");admitCards.dataCache.sessionClassExams=a,admitCards.bindStandardDropDown(),admitCards.onClickBulkAdmitCardTab(),admitCards.onClickClassAdmitCardTab()})},changeAdmitCardType:function(){$(".admitcard-type").change(function(){var e=$(this).val();"ADMIT_CARD_WITH_DATESHEET"===e?($(".count-per-page-row").attr("style","display:none"),$(".count-per-page").removeClass("mandatory-field")):"ADMIT_CARD_WITHOUT_DATESHEET"===e&&($(".count-per-page-row").attr("style","display:block"),$(".count-per-page").addClass("mandatory-field"))})},changeSession:function(){var e="none"==$("#class-admit-card-screen").css("display"),a=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/admitcard-session-change/"+a,function(a){$("#admit-card-div").html(a),admitCards.bindStandardDropDown(),e||($("#class-admit-card-screen").attr("style","display:block"),$("#bulk-admit-card-screen").attr("style","display:none;"))})},bindStandardDropDown:function(){$(".admitcard-standards").on("change",function(){var e="class-admit-card-screen";$("#admit-card-student-list-div").attr("style","display:none;"),"none"==$("#class-admit-card-screen").css("display")&&(e="bulk-admit-card-screen");var a=$("#"+e).find(".admitcard-standards").find(":selected").val().trim(),t=academicSessionHandler.getSelectedSessionId();admitCards.fillExamsDropDown(e,t,a)})},fillExamsDropDown:function(e,a,t){if(""!=a&&""!=n){var n=t.split(":")[0],i='<option value = "" ></option>';if(a in admitCards.dataCache.sessionClassExams){var s=admitCards.dataCache.sessionClassExams[a];if(n in s)for(var o=s[n],r=0;r<o.length;r++)i+='<option value="'+o[r].examId+'">'+o[r].examName+"</option>"}$("#"+e).find(".admitcard-exams").html(i)}},loadStandardStudents:function(){var e=$("#examination-standard-section-select").val();if(""!=e){$("#send-notifications-class-student-list").attr("style","display:block");var a=e.split(":"),t=a[0].trim(),n="";a.length>1&&(n=a[1].trim());var i=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/class-students-with-sections/"+i+"?standards="+t+"&sectionId="+n,function(e){$("#admit-card-student-list-div").html(e),$("#admit-card-student-list-div").attr("style","display:block;")})}else showErrorDialogBox("Please select standard")},studentSelectCheckbox:function(e){var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)},selectStudentList:function(e){if(e.checked){$(".student-select-checkbox").prop("checked",!0);var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a)}else $(".student-select-checkbox").prop("checked",!1),$("#students-selected-count").html(0)},onClickGenerateBulkAdmitCard:function(){var e=admitCards.getSelectedBulkStudentIds();if(Array.isArray(e)&&e.length>0){$("#generate-admit-card-popup-modal").closest("div.modal").modal("toggle"),admitCards.changeAdmitCardType();var a=$("#bulk-admit-card-screen").find(".admitcard-standards").find(":selected").val().trim(),t=academicSessionHandler.getSelectedSessionId();admitCards.fillExamsDropDown("bulk-admit-card-screen",t,a)}else showErrorDialogBox("Please select atleast one student to generate admit card. ")},generateBulkAdmitCard:function(){if(!validateMandatoryFields($("#generate-admit-card-popup-modal"))){var e="generate-admit-card-popup-modal",a=$("#"+e).find(".admitcard-exams option:selected").val(),t=$("#"+e).find(".admitcard-type option:selected").val(),n=$("#"+e).find(".count-per-page option:selected").val(),i=admitCards.getSelectedBulkStudentIds();i=i.join(","),$("#generate-admit-card-popup-modal").closest("div.modal").modal("toggle"),window.open(baseURL+"/examination/generate-admitcard/"+a+"/"+t+"?studentIds="+i+"&countPerPage="+n,"_blank")}},getSelectedBulkStudentIds:function(){var e=[];return $("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var a=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();e.push(a)}}),e},onClickBulkAdmitCardTab:function(){$("#bulk-admit-card-generation-option").on("click",function(){$("#class-admit-card-screen").attr("style","display:none"),$("#bulk-admit-card-screen").attr("style","display:block"),admitCards.loadMainScreen()})},onClickClassAdmitCardTab:function(){$("#class-admit-card-generation-option").on("click",function(){$("#bulk-admit-card-screen").attr("style","display:none"),$("#class-admit-card-screen").attr("style","display:block"),admitCards.changeAdmitCardType(),$(".count-per-page-row").attr("style","display:block"),$(".count-per-page").addClass("mandatory-field")})},generateClassAdmitCard:function(){if(!validateMandatoryFields($("#class-admit-card-screen"))){var e="class-admit-card-screen",a=$("#"+e).find(".admitcard-exams option:selected").val(),t=$("#"+e).find(".admitcard-type option:selected").val(),n=$("#"+e).find(".count-per-page option:selected").val();window.open(baseURL+"/examination/generate-admitcard/"+a+"/"+t+"?studentIds=&countPerPage="+n,"_blank")}}},greenSheet={loadGreenSheetPage:function(){ajaxClient.get("/examination/green-sheet",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(greenSheet.changeSession)})},changeSession:function(){$("#report-card-generation-class").val("")},generateGreenSheet:function(){var e=$("#report-card-generation-class").val().trim(),a=academicSessionHandler.getSelectedSessionId();""!=e?window.open(baseURL+"/examination/generate-green-sheet/"+a+"/"+e,"_blank"):showErrorDialogBox("Please select class for generating green sheet")}},notification={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/notification-input-page",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(notification.changeSession),channelCreditsHandler.initDataCache(),channelCreditsHandler.loadChannelCredits("/examination/get-channel-credits"),notification.bindClassChangeEvent(),notification.dataCache={},notification.dataCache.sendNotifications={},notification.dataCache.selectedUserCount=0})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/standards/"+e,function(e){$("#standard-with-section-options-wrapper").html(e),notification.bindClassChangeEvent(),$("#marks-view-main-container").html('<br><br><br><br> <br> <br> <br><br><br><br> <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to view marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> '),notification.dataCache={}})},bindClassChangeEvent:function(){$("#examination-standard-section-select").change(function(){var e=$(this).find(":selected").val().trim();notification.loadExamCourseStructure(e)})},loadExamCourseStructure:function(e){if(""!=e){var a=e.split(":"),t=a[0];if(""!=t){var n=null;2==a.length&&(n=a[1]);var i=academicSessionHandler.getSelectedSessionId();notification.dataCache.classMarksStructures=[],notification.dataCache.selectedStandardSectionMapId=e,notification.dataCache.selectedStandard=t,notification.dataCache.selectedSection=n,ajaxClient.get("/examination/exam-notification-detail/"+t+"/"+i,function(e){$("#marks-view-main-container").html(e),notification.bindExamChangeEvent();var a=readJson("#class-exam-courses-json");notification.dataCache.classExamsCourses=a})}}},bindExamChangeEvent:function(){$("#class-exams").change(function(){for(var e=$(this).find(":selected").val().trim(),a=notification.dataCache.classExamsCourses,t=0;t<a.length;t++){var n=a[t];if(n.examMetaData.examId==e){for(var i='<option value="" selected>Choose Course ...</option>',s=0;s<n.examCourses.length;s++){var o=n.examCourses[s];i+='<option value="'+o.course.courseId+'">'+o.course.courseName+"</option>"}$("#class-exam-courses").html(i)}}})},loadMarksViewSheet:function(){var e=$("#class-exams").find(":selected").val().trim(),a=$("#class-exam-courses").find(":selected").val().trim();if(notification.dataCache.selectedCourseId=a,notification.dataCache.selectedExamId=e,""!=a)if(""!=e){var t=notification.dataCache.selectedSection,n=academicSessionHandler.getSelectedSessionId(),i=$("#examination-standard-section-select").val().trim().split(":")[0].trim();ajaxClient.get("/examination/student-details-sheet/"+n+"/"+i+"/"+e+"/"+a+"?sectionId="+t,function(e){$("#student-marks-view-container").html(e),$("#total-student-count").text($(".student-course-marks").length)})}else showErrorDialogBox("please select exam");else showErrorDialogBox("Please select course")},selectStudentList:function(e){if(e.checked){$(".student-select-checkbox").prop("checked",!0);var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a),notification.dataCache.selectedUserCount=a}else $(".student-select-checkbox").prop("checked",!1),$("#students-selected-count").html(0),notification.dataCache.selectedUserCount=0},studentSelectCheckbox:function(e){var a=$("input.student-select-checkbox:checkbox:checked").length;$("#students-selected-count").html(a),notification.dataCache.selectedUserCount=a},bulkReminderConfirmModal:function(){var e=$("input.student-select-checkbox:checkbox:checked").length;if(0!=e){notification.dataCache.selectedStudentCount=e;var a=$("#include-fee-fine").is(":checked");ajaxClient.get("/examination/reminder-templates",function(t){$("#bulk-reminder-template-modal-container").html(t),notification.updateReminderModalDisplay(a),$("#bulk-reminder-confirmation-modal").modal({backdrop:"static",keyboard:!1});var n=readJson("#sms-templates-json");notification.bindSMSTemplateClickAction(n),$(".selected-user-count").text(e)})}else showErrorDialogBox("Please select atleast one student for sending sms.")},updateReminderModalDisplay:function(e){e?$(".fine-included").text("Yes"):$(".fine-included").text("No"),$("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count),notification.dataCache.selectedStudentCount>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count?($("#notification-send-confirm-button").prop("disabled",!0),$("#insufficient-sms-credits-message").attr("style","color:red; display:block;")):($("#notification-send-confirm-button").prop("disabled",!1),$("#insufficient-sms-credits-message").attr("style","color:red; display:none;"))},bindSMSTemplateClickAction:function(e){$("#sms-template-select").change(function(){var a=$(this).val();if(""==a)return $("#sms-template-selection-display").attr("style","display:none"),void $("#notification-send-confirm-button").prop("disabled",!0);$("#sms-template-selection-display").attr("style","display:block");for(var t=0;t<e.length;t++){var n=e[t];if(a==n.templateId){if($("#selected-sms-template-header").text("Template Message ("+n.templateName+")"),$("#notification-template-content").text(n.templateValue),null!=n.templateVariableDetails&&null!=n.templateVariableDetails.templateVariableList)for(var i=1,s="",o=0;o<n.templateVariableDetails.templateVariableList.length;o++){var r=n.templateVariableDetails.templateVariableList[o],d="",l="";null!=r.defaultValue&&(l=r.defaultValue);var c="";"CUSTOM"!=r.templateVariableType?(c="template-var-row system-var",d="System Derived"):(c="template-var-row custom-var",d='<input type="text" class="variable-value" value="'+l+'"/>'),s+='<tr class="'+c+"\" id='"+r.varName+"'> <td class='template-var-sr-no'>"+i+"</td><td class='template-var-name'>"+r.varName+"</td><td class='template-var-type'>"+r.templateVariableTypeDisplayName+"</td><td class='template-var-value'>"+d+"</td></tr>",i+=1}$("#template-var-content").html(s);break}}notification.bindSMSTemplateVarInput(),notification.setVariableValues(),notification.updateSMSTemplatePreview()})},setVariableValues:function(){$(".template-var-row").each(function(){var e=$(this).find(".template-var-name").first().text();if($(this).hasClass("custom-var"))if("standard_name"===e)$(this).find(".variable-value").val($("#examination-standard-section-select option:selected").text());else if("exam_name"===e){var a=$("#class-exams option:selected").text()+" of "+$("#class-exam-courses option:selected").text();a.length>30&&(a=a.substring(0,30)),$(this).find(".variable-value").val(a)}})},bindSMSTemplateVarInput:function(){$(".variable-value").on("keyup",function(e){notification.updateSMSTemplatePreview()})},updateSMSTemplatePreview:function(){var e=$("#notification-template-content").text();$(".template-var-row").each(function(){var a=$(this).find(".template-var-name").first().text(),t=a;$(this).hasClass("custom-var")&&null!=(t=$(this).find(".variable-value").val())&&""!=t.trim()&&(e=e.replace("${"+a+"}",t))}),$("#preview-notification-template-content").text(e),notification.updateSMSCreditDisplay()},updateSMSCreditDisplay:function(){var e=$("#preview-notification-template-content").text(),a=getSmsCredits(e);$("#notification-content-char-count").html("Total characters : "+e.length+" / Credit(s) Used : "+a+" (Actual credits may vary depending on the variables values)");var t="Yes, Send SMS",n="btn-danger",i="btn-primary";a>1&&(t="Yes, Send SMS With "+a+" Credits",n="btn-primary",i="btn-danger"),$("#notification-send-confirm-button").html(t),$("#notification-send-confirm-button").removeClass(n),$("#notification-send-confirm-button").addClass(i),0==a?($("#notification-send-confirm-button").prop("disabled",!0),$("#insufficient-sms-credits-message").attr("style","color:red; display:none;")):notification.dataCache.selectedUserCount*a>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count?($("#notification-send-confirm-button").prop("disabled",!0),$("#insufficient-sms-credits-message").attr("style","color:red; display:block;")):($("#notification-send-confirm-button").prop("disabled",!1),$("#insufficient-sms-credits-message").attr("style","color:red; display:none;"))},getSMSTemplateVariables:function(){var e={};return $(".template-var-row").each(function(){var a=$(this).find(".template-var-name").first().text(),t=null;$(this).hasClass("custom-var")&&(t=$(this).find(".variable-value").val()),e[a]=t}),e},sendSMSNotifications:function(){var e=$("#notification-batch-name").val();if(null!=e&&""!=e){var a=$("#sms-template-select").find(":selected").val().trim();if(""!=a){var t=$("#preview-notification-template-content").text();if(null!=t&&""!=t){var n=!1;$("#include-fee-fine").is(":checked")&&(n=!0);var i=$("#examination-standard-section-select").find(":selected").val().trim(),s=$("#class-exam-courses").find(":selected").val().trim(),o=(i=$("#class-exams").find(":selected").val().trim()).split(":");if(""!=o[0]){var r=null;2==o.length&&(r=o[1]);var d;d=notification.getSelectedBulkStudentIds();var l=getSmsCredits(t);if(notification.dataCache.selectedUserCount*l>channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count)alert("You do not have sufficient credits to send "+notification.dataCache.selectedUserCount+" SMS. Please recharge.");else{var c=notification.getSMSTemplateVariables();if(null!=c)for(var m in c)if(null!=c[m]){if(0==c[m].length)return void alert("Invalid value for variable "+m);if(c[m].length>30)return void alert("Value of variable "+m+" exceeds limit of 30 characters.")}$("#bulk-reminder-confirmation-modal").modal("toggle");var u={batchName:e,academicSessionId:academicSessionHandler.getSelectedSessionId(),deliveryMode:"SMS",userType:"STUDENT",userIds:d,smsTemplateId:a,customVariables:c};ajaxClient.post("/examination/send-notifications/"+n+"/"+i+"/"+s+"?sectionId="+r,{sendNotificationsPayload:JSON.stringify(u)},function(e){$("#marks-view-status-modal-container").html(e),$("#bulk-notification-status-modal").modal("toggle"),channelCreditsHandler.loadChannelCredits("/examination/get-channel-credits")})}}}else alert("Please select sms template to send notifications.")}else alert("Please select sms template to send notifications.")}else alert("Please give batch name to identify the sent notifications.")},getSelectedBulkStudentIds:function(){var e=[];return $("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var a=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();e.push(a)}}),e}},notificationHistory={dataCache:{},loadNotificationHistoryHomePage:function(){ajaxClient.get("/examination/notification-history-homepage",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(notificationHistory.changeSession),notificationHistory.getNotificationHistory(!0,"SMS","BATCH"),notificationHistory.dataCache.notificationHistoryTab="BATCH"})},changeSession:function(){var e=notificationHistory.dataCache.notificationHistoryTab,a=notificationHistory.getDeliveryModeTab();notificationHistory.getNotificationHistory(!0,a,e)},getNotificationHistory:function(e,a,t){notificationHistory.dataCache.notificationHistoryTab=t,notificationHistory.dataCache.deliveryModeTab=a;var n=$(".page-item.active").find(".page-number").text().trim();e&&(n=1);var i=$("#items-per-page").val();null!=n&&""!=n||(n=1);var s=(n-1)*i,o=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/notification-history/"+o+"/"+a+"/"+t+"/"+s+"/"+i,function(e){var i=0;"SMS"==a?($("#smsNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50):"CALL"==a&&($("#voiceNotificationHistoryResult").html(e),i=$("#sidebar").height()-$("#channel-tabContent").position().top-50),$(".notification-history-fixed-height-list-wrapper").attr("style","height:"+i+"px;  overflow-y: scroll; cursor: pointer;");var s=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(s.itemsPerPage),$(".page-item").removeClass("active");s.offset,s.itemsPerPage;$("#page-number-"+n).addClass("active"),"INDIVIDUAL"==t&&($("#nav-individual-history-tab").addClass("active"),$("#nav-batch-history-tab").removeClass("active"),$("#individual-history-content").addClass("show active"),$("#batch-history-content").removeClass("show active")),notificationHistory.bindIndividualNotificationsContentView(),notificationHistory.initPagination()})},initPagination:function(){pagination.bindEvents(function(){var e=notificationHistory.dataCache.notificationHistoryTab,a=notificationHistory.getDeliveryModeTab();notificationHistory.getNotificationHistory(!1,a,e)},function(){var e=notificationHistory.dataCache.notificationHistoryTab,a=notificationHistory.getDeliveryModeTab();notificationHistory.getNotificationHistory(!1,a,e)},function(){var e=notificationHistory.dataCache.notificationHistoryTab,a=notificationHistory.getDeliveryModeTab();notificationHistory.getNotificationHistory(!1,a,e)},function(){var e=notificationHistory.dataCache.notificationHistoryTab,a=notificationHistory.getDeliveryModeTab();notificationHistory.getNotificationHistory(!0,a,e)})},getDeliveryModeTab:function(){var e=notificationHistory.dataCache.deliveryModeTab;return null==e||""==e?"SMS":e},loadBatchNotificationsDetails:function(e){ajaxClient.get("/examination/batch-notification-details/"+e,function(e){$("#batch-notification-detail-modal-container").html(e),$("#view-batch-notification-modal").modal("toggle")})},bindIndividualNotificationsContentView:function(){$(".view-individual-notification").click(function(){var e=$(this).parent().find("p.notification-content").text().trim();$("#individual-notification-detail-modal").find(".modal-body").html('<span style="white-space: pre-line;">'+e+"</span>"),$("#individual-notification-detail-modal").modal("toggle")})}};function getUpdateSectionsName(){var e=[];return $(".update-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function getAddSectionsName(){var e=[];return $(".add-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function getHiddenSectionNames(){var e=[];return $(".hidden-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function commonBetweenTwoNames(e,a){for(var t=[],n=0;n<e.length;n++)for(var i=0;i<a.length;i++)e[n].trim()==a[i].trim()&&t.push(e[n]);return t}function validations(e){clearValidationErrorDisplay();var a=!1;return $(e).find("input.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),a=!0)}),$(e).find("select.mandatory-field").each(function(){0!=$(this).find(":selected").length&&""!=$(this).find(":selected").val().trim()||($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),a=!0)}),$(e).find("input.mandatory-table-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-table-field-text"> <span style="color:#e65f76;">Mandatory</span></p>'),a=!0)}),$(e).find("input.hour-range").each(function(){($(this).val()<0||$(this).val()>23)&&($(this).css("border","1px solid #ff8795"),a=!0)}),$(e).find("input.minute-range").each(function(){($(this).val()<0||$(this).val()>59)&&($(this).css("border","1px solid #ff8795"),a=!0)}),$(e).find("input.positive-number").each(function(){$(this).val()<0&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-text"> <span style="color:#e65f76;">Invalid</span></p>'),a=!0)}),a}function clearValidationErrorDisplay(){$(".hour-range").css("border",""),$(".minute-range").css("border",""),$(".positive-number").css("border",""),$(".invalid-input-text").remove(),$(".mandatory-field").css("border",""),$(".mandatory-field-text").remove(),$(".mandatory-table-field").css("border",""),$(".mandatory-table-field-text").remove()}function closeModal(){clearValidationErrorDisplay()}var updateStudentInfo={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/student-details-home-v2",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(updateStudentInfo.changeSession),updateStudentInfo.initPage()})},initPage:function(){updateStudentInfo.dataCache={};var e=readJson("#standards-json");updateStudentInfo.dataCache.standards=e;var a=readJson("#session-class-exams");updateStudentInfo.dataCache.sessionClassExams=a,updateStudentInfo.bindStandardChangeEvent(),initSelect2("None")},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/update-page-session-change-v2/"+e,function(e){$("#student-details-main-container").html(e),updateStudentInfo.initPage()})},bindStandardChangeEvent:function(){$("#student-feed-class").change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){$("#exam-ids-multiple").html(""),$("#additional-course-ids-multiple").html("");var a=academicSessionHandler.getSelectedSessionId();updateStudentInfo.updateStandardSections(e),updateStudentInfo.fillExamsDropDown(this,a,e),updateStudentInfo.fillCoursesDropDown(this,a,e)}})},fillCoursesDropDown:function(e,a,t){ajaxClient.get("/examination/exam-course-details/"+t+"/"+a,function(e){$("#additional-course-ids-multiple").html(e),initSelect2("None")})},fillExamsDropDown:function(e,a,t){if(""!=a&&""!=t){var n='<option value = "" ></option>';if($(e).closest(".modal-body").find("#exam-ids-multiple").html(n),a in updateStudentInfo.dataCache.sessionClassExams){var i=updateStudentInfo.dataCache.sessionClassExams[a];if(t in i){for(var s=i[t],o=0;o<s.length;o++)n+='<option value="'+s[o].examId+'">'+s[o].examName+"</option>";$(e).closest(".form-row").find("#exam-ids-multiple").html(n)}}initSelect2("None")}},updateStandardSections:function(e){for(var a=updateStudentInfo.dataCache.standards,t={},n=0;n<a.length;n++){t[(s=a[n]).standardId]=s}var i='<option value=""></option>';if(e in t){var s=t[e];for(n=0;n<s.standardSectionList.length;n++)i+='<option value="'+s.standardSectionList[n].sectionId+'">'+s.standardSectionList[n].sectionName+"</option>";$("select.student-section").html(i),initSelect2("None")}else $("select.student-section").html(i)},loadStudentList:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#student-feed-class").find(":selected").val().trim();if(""!=a){for(var t="",n=updateStudentInfo.dataCache.standards,i={},s=0;s<n.length;s++){var o;i[(o=n[s]).standardId]=o}if(a in i)if((o=i[a]).standardSectionList.length>0&&""==(t=$("#student-feed-section").val()))return void showErrorDialogBox("Please select section.");var r=$("#exam-ids-multiple").val();if(void 0===r||r.length<=0)showErrorDialogBox("Please select exam.");else{var d=r.join(","),l=$("#additional-course-ids-multiple").val();l=void 0===l?"":l.join(","),""!=t&&(t=t.join(",")),ajaxClient.get("/examination/student-list-v2/"+e+"/"+a+"?sectionId="+t+"&examIdStr="+d+"&additionalCourseIdsStr="+l,function(e){$("#student-feed-container").html(e);var a=readJson("#student-field-details-json");updateStudentInfo.dataCache.studentFieldDetails=a,$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[7,"desc"],[8,"asc"]]}),initDate(36500)})}}else showErrorDialogBox("Please select class.")},saveStudentDetails:function(){for(var e=$("#selected-standard-id").text().trim(),a=$("#selected-section-id").text().trim(),t=[],n=academicSessionHandler.getSelectedSessionId(),i=updateStudentInfo.dataCache.studentFieldDetails,s=i.fieldMetadata.fieldInfoList,o={},r=[],d=0;d<s.length;d++){var l=s[d];o[l.field]=l,r.push(l.field)}var c=!1,m="";if(updateStudentInfo.dataCache.studentFieldDetails=i,$("tr.student-field-row").each(function(){var e=$(this).attr("id"),a=[];$(this).find("td.student-field-column").each(function(){var e=$(this).attr("id");if(e in o){var t=o[e],n="";"TEXT"==t.fieldType?n=$(this).find("input.student-field-value").val().trim():"LONGTEXT"==t.fieldType?n=$(this).find(".student-field-value").val().trim():"NUMBER"==t.fieldType?n=$(this).find("input.student-field-value").val():"DATE"==t.fieldType?n=$(this).find("input.student-field-value").val():"DROPDOWN"==t.fieldType&&(n=$(this).find("select.student-field-value").find(":selected").val().trim()),"ADMISSION_NUMBER"==e&&""==n.trim()&&(c=!0,m="Admission number cannot be blank."),a.push({field:e,value:n})}}),t.push({studentId:e,fieldValueList:a})}),c)showErrorDialogBox(m);else{var u={dataType:"SESSION_DATA",requiredFields:r,studentDataList:t};ajaxClient.post("/examination/update-student-details-v2/"+n+"/"+e+"?sectionId="+a,{updateFieldPayload:JSON.stringify(u)},function(e){$("#student-details-status-modal-container").html(e),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),updateStudentInfo.loadStudentList()})}}},publishReportCards={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/publish-report-card-home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(publishReportCards.changeSession),publishReportCards.initPage()})},initPage:function(){publishReportCards.dataCache={};var e=readJson("#standards-json");publishReportCards.dataCache.standards=e,publishReportCards.bindStandardChangeEvent(),initSelect2("None")},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/publish-report-card-session-home/"+e,function(e){$("#student-details-main-container").html(e),publishReportCards.initPage()})},bindStandardChangeEvent:function(){$("#student-feed-class").change(function(){var e=$(this).find(":selected").val().trim();if(""!=e){var a=academicSessionHandler.getSelectedSessionId();publishReportCards.updateStandardSections(e),publishReportCards.fillReportTypeDropdown(this,a,e)}})},updateStandardSections:function(e){for(var a=publishReportCards.dataCache.standards,t={},n=0;n<a.length;n++){t[(s=a[n]).standardId]=s}var i='<option value=""></option>';if(e in t){var s=t[e];for(n=0;n<s.standardSectionList.length;n++)i+='<option value="'+s.standardSectionList[n].sectionId+'">'+s.standardSectionList[n].sectionName+"</option>";$("select.student-section").html(i),initSelect2("None")}else $("select.student-section").html(i)},fillReportTypeDropdown:function(e,a,t){ajaxClient.get("/examination/report-card-variable-types-with-id/"+a+"/"+t,function(e){$("#class-report-type-div").html(e),$("#student-feed-container").empty()})},loadStudentList:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#student-feed-class").find(":selected").val().trim();if(""!=a){for(var t="",n=publishReportCards.dataCache.standards,i={},s=0;s<n.length;s++){var o;i[(o=n[s]).standardId]=o}if(a in i)if((o=i[a]).standardSectionList.length>0&&(""==(t=$("#student-feed-section").val())||t.length<=0))return void showErrorDialogBox("Please select section.");""!=t&&(t=t.join(","));var r=$("#report-card-type").val();""!=r?ajaxClient.get("/examination/student-report-card-status/"+e+"/"+a+"/"+r+"?sectionId="+t,function(e){$("#student-feed-container").html(e)}):showErrorDialogBox("Please select report type")}else showErrorDialogBox("Please select class.")},updateReportCardStatusPopup:function(e){$("#update-student-report-card-status-config-modal").modal("toggle"),$("#update-exam-status").text(e);var a="Do you want to unpublish student's report cards?";"PUBLISHED"===e&&(a="Do you want to publish student's report cards?"),$("#update-exam-status-config-modal-text").text(a)},updateReportCardStatus:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#selected-report-card-id").text().trim(),t=$("#update-exam-status").text().trim(),n=[];if($("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();n.push(e)}}),n.length<=0)return $("#update-student-report-card-status-config-modal").modal("toggle"),void showErrorDialogBox("Please select atleast one student to update status");var i={instituteId:null,academicSessionId:e,reportCardId:a,studentExamDisplayDataStatus:t,studentIdSet:n};$("#update-student-report-card-status-config-modal").modal("toggle"),ajaxClient.post("/examination/update-student-report-card-status",{studentReportCardStatusPayload:JSON.stringify(i)},function(e){$("#student-details-status-modal-container").html(e),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),publishReportCards.loadStudentList()})}},publishHPCReportCards={dataCache:{},loadMainScreen:function(){ajaxClient.get("/examination/publish-hpc-report-card-home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(publishHPCReportCards.changeSession),publishHPCReportCards.initPage()})},initPage:function(){publishHPCReportCards.dataCache={};var e=readJson("#standards-json");publishHPCReportCards.dataCache.standards=e,publishHPCReportCards.bindStandardChangeEvent(),initSelect2("None")},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/publish-hpc-report-card-session-home/"+e,function(e){$("#student-details-main-container").html(e),publishHPCReportCards.initPage()})},bindStandardChangeEvent:function(){$("#student-feed-class").change(function(){var e=$(this).find(":selected").val().trim();""!=e&&publishHPCReportCards.updateStandardSections(e)})},updateStandardSections:function(e){for(var a=publishHPCReportCards.dataCache.standards,t={},n=0;n<a.length;n++){t[(s=a[n]).standardId]=s}var i='<option value=""></option>';if(e in t){var s=t[e];for(n=0;n<s.standardSectionList.length;n++)i+='<option value="'+s.standardSectionList[n].sectionId+'">'+s.standardSectionList[n].sectionName+"</option>";$("select.student-section").html(i),initSelect2("None")}else $("select.student-section").html(i)},loadStudentList:function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#student-feed-class").find(":selected").val().trim();if(""!=a){for(var t="",n=publishHPCReportCards.dataCache.standards,i={},s=0;s<n.length;s++){var o;i[(o=n[s]).standardId]=o}if(a in i)if((o=i[a]).standardSectionList.length>0&&(""==(t=$("#student-feed-section").val())||t.length<=0))return void showErrorDialogBox("Please select section.");""!=t&&(t=t.join(","));var r=$("#exam-card-type").val();""!=r?ajaxClient.get("/examination/student-hpc-report-card-status/"+e+"/"+a+"/"+r+"?sectionId="+t,function(e){$("#student-feed-container").html(e)}):showErrorDialogBox("Please select exam type")}else showErrorDialogBox("Please select class.")},updateReportCardStatusPopup:function(e){$("#update-student-report-card-status-config-modal").modal("toggle"),$("#update-exam-status").text(e);var a="Do you want to unpublish student's report cards?";"PUBLISHED"===e&&(a="Do you want to publish student's report cards?"),$("#update-exam-status-config-modal-text").text(a)},updateReportCardStatus:function(){var e=$("#selected-exam-type").text().trim(),a=$("#update-exam-status").text().trim(),t=[];if($("input.student-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-notification-student-id").first().text().trim();t.push(e)}}),t.length<=0)return $("#update-student-report-card-status-config-modal").modal("toggle"),void showErrorDialogBox("Please select atleast one student to update status");var n={hpcExamType:e,studentExamDisplayDataStatus:a,studentIdSet:t};$("#update-student-report-card-status-config-modal").modal("toggle"),ajaxClient.post("/examination/update-hpc-student-report-card-status",{studentHPCReportCardStatusPayload:JSON.stringify(n)},function(e){$("#student-details-status-modal-container").html(e),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),publishHPCReportCards.loadStudentList()})}},examTree={rectNode:{width:150,height:60,textMargin:5},sidebarWidth:270,margin:{top:20,right:0,bottom:30,left:0},width:function(){return screen.width-examTree.sidebarWidth-examTree.margin.left-examTree.margin.right},height:function(){return 1e3-examTree.margin.top-examTree.margin.bottom},nodeId:0,duration:750,initSVG:function(){return d3.select("#tree-container").append("svg").attr("id","render-svg").attr("width",examTree.width()+examTree.margin.right+examTree.margin.left).attr("height",examTree.height()+examTree.margin.top+examTree.margin.bottom).append("g").attr("transform","translate("+examTree.margin.left+","+examTree.margin.top+")")},intiArrowMarker:function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("viewBox","-0 -5 10 10").attr("refX",9).attr("refY",0).attr("orient","auto").attr("markerWidth",4).attr("markerHeight",4).attr("xoverflow","visible").append("svg:path").attr("d","M 0,-5 L 10 ,0 L 0,5").attr("fill","darkgrey").style("stroke","none")},initShadow:function(e){var a=e.append("defs").append("filter").attr("id","drop-shadow").attr("height","130%");a.append("feGaussianBlur").attr("in","SourceAlpha").attr("stdDeviation",5).attr("result","blur"),a.append("feOffset").attr("in","blur").attr("dx",5).attr("dy",5).attr("result","offsetBlur");var t=a.append("feMerge");t.append("feMergeNode").attr("in","offsetBlur"),t.append("feMergeNode").attr("in","SourceGraphic")},breadthFirstTraversal:function(e){var a=[];a.push(e),a.push(null);for(var t=0,n=0;0!==a.length;){var i=a.shift();if(null!=i){if(n++,1,null!=i.children)for(var s=0;s<i.children.length;s++)a.push(i.children[s])}else t<n&&(t=n),n=0,0!==a.length&&a.push(null)}},initTreeLayout:function(){return d3.tree()},createTreeHierarchy:function(e){var a=d3.hierarchy(e,function(e){return e.children});return a.x0=0,a.y0=0,a},updateTree:function(e,a,t,n){var i=a(t),s=i.leaves().length,o=t.height,r=s*examTree.rectNode.width*2<examTree.width()?examTree.width():s*examTree.rectNode.width*2,d=80*o<examTree.height()?examTree.height():80*(o+1);d3.select("#render-svg").attr("width",r+examTree.margin.right+examTree.margin.left).attr("height",d+examTree.margin.top+examTree.margin.bottom),a.size([r,d]);var l=(i=a(t)).descendants(),c=i.descendants().slice(1);l.forEach(function(e){e.y=140*e.depth});var m=e.selectAll("g.node").data(l,function(e){return e.id||(e.id=++examTree.nodeId)}),u=examTree.createNodes(e,m,a,t,n);examTree.updateNodes(m,u,e,n),examTree.bindExitNodes(m,e,n);var p=e.selectAll("path.link").data(c,function(e){return e.id}),h=examTree.createLinks(e,p,n);examTree.updateLinks(e,h,p),examTree.bindLinkExit(p,n),l.forEach(function(e){e.x0=e.x,e.y0=e.y})},createNodes:function(e,a,t,n,i){var s=a.enter().append("g").attr("class","node").attr("transform",function(e){return"translate("+i.x0+","+i.y0+")"});return s.append("rect").attr("class","node first").attr("width",examTree.rectNode.width).attr("height",examTree.rectNode.height).style("filter","url(#drop-shadow)").style("fill",function(e){return e._children?"lightsteelblue":"#fff"}).on("click",function(e){return examTree.nodeClick(e)}),s.append("text").attr("y",examTree.rectNode.height/1.8).attr("x",function(e){return examTree.rectNode.width/2}).attr("text-anchor",function(e){return"middle"}).attr("cursor","pointer").attr("fill","white").style("font-size",function(e){return"1em"}).style("font-weight","bold").text(function(e){return e.data.examMetaData.examName.length>16?e.data.examMetaData.examName.substring(0,14)+"...":e.data.examMetaData.examName}).on("mouseover",function(){$(this).css("font-size","1.1em")}).on("mouseout",function(){$(this).css("font-size","1em")}).on("click",function(e){return examTree.nodeClick(e)}),s.append("rect").attr("class","maximize").attr("x",examTree.rectNode.width-15).attr("y",i.y0+5).attr("width",10).attr("height",10).style("stroke","#C8C8C8").style("stroke-width","1.5").style("fill",function(e){return e._children?"#da5353":"#2984C7"}).style("display",function(e){return e._children?"":"none"}).attr("cursor","pointer").on("click",function(a){return examTree.click(e,t,n,a)}),s.append("text").attr("class","minimize").attr("y",.3*examTree.rectNode.height).attr("x",function(e){return.95*examTree.rectNode.width}).attr("text-anchor",function(e){return"end"}).attr("cursor","pointer").attr("fill","#C8C8C8").style("font-size","2em").style("display",function(e){return e.children?"":"none"}).text(function(e){return"-"}).on("click",function(a){return examTree.click(e,t,n,a)}),s},updateNodes:function(e,a,t,n){var i=a.merge(e);i.transition().duration(examTree.duration).attr("transform",function(e){return"translate("+(e.x-examTree.rectNode.width/2)+","+e.y+")"}),i.selectAll("rect.node").attr("width",examTree.rectNode.width).attr("height",examTree.rectNode.height).attr("rx",2).attr("ry",2).style("filter","url(#drop-shadow)").style("fill",function(e){return e._children?"#da5353":"#2984C7"}).attr("cursor","pointer"),i.selectAll("text.minimize").attr("y",.3*examTree.rectNode.height).attr("x",function(e){return.95*examTree.rectNode.width}).attr("text-anchor",function(e){return"end"}).attr("cursor","pointer").attr("fill","#C8C8C8").style("font-size","2em").style("display",function(e){return e.children?"":"none"}).text(function(e){return"-"}),i.selectAll("rect.maximize").attr("x",examTree.rectNode.width-15).attr("y",5).attr("width",10).attr("height",10).style("stroke","#C8C8C8").style("stroke-width","1.5").style("fill",function(e){return e._children?"#da5353":"#2984C7"}).style("display",function(e){return e._children?"":"none"}).attr("cursor","pointer")},bindExitNodes:function(e,a,t){var n=e.exit().transition().duration(examTree.duration).attr("transform",function(e){return"translate("+t.x+","+t.y+")"}).remove();n.select("rect").attr("width",1e-6).attr("height",1e-6),n.select("text").style("fill-opacity",1e-6)},createLinks:function(e,a,t){return a.enter().insert("path","g").attr("class","link").attr("marker-end","url(#arrowhead)").attr("d",function(e){var a={x:t.x0,y:t.y0};return examTree.diagonal(a,a)})},updateLinks:function(e,a,t){a.merge(t).transition().duration(examTree.duration).attr("d",function(e){return examTree.diagonal(e,e.parent)})},bindLinkExit:function(e,a){e.exit().transition().duration(examTree.duration).attr("d",function(e){var t={x:a.x,y:a.y};return examTree.diagonal(t,t)}).remove()},diagonal:function(e,a){return path=`M ${a.x} ${a.y}\n            V ${(e.y+a.y)/2+examTree.rectNode.height/2}\n            H ${e.x}\n            V ${e.y}`,path},click:function(e,a,t,n){n.children?(n._children=n.children,n.children=null):(n.children=n._children,n._children=null),examTree.updateTree(e,a,t,n)},nodeClick:function(e){examDetailsNamespace.loadExamDetailsPage(e.data.examMetaData.examId)},render:function(e){var a=examTree.initSVG();examTree.intiArrowMarker(a),examTree.initShadow(a);var t=examTree.initTreeLayout(),n=examTree.createTreeHierarchy(e);examTree.updateTree(a,t,n,n)}},sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1;$(document).ready(function(){menuLoader.registerSidebarMenu();var e=new URLSearchParams(window.location.search);if(actionType=e.get(ACTION_TYPE),""===actionType||null===actionType)homePage.initHomePage();else if("fill-marks"===actionType){$(".sidebar-nav").find(".sidebar-item").each(function(){$(this).removeClass("active")}),$("#feedMarksNav").parent().addClass("active"),marksFeeding.loadMainPage();var a=window.location.href;url=removeURLParameter(a,ACTION_TYPE);let e={id:"100"};window.history.replaceState(e,a,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerExamConfigurationMenu(),menuLoader.registerFeedMarksMenu(),menuLoader.registerAdvanceFeedMarksMenu(),menuLoader.registerViewMarksMenu(),menuLoader.registerDimensionsMenu(),menuLoader.registerDatesheetMenu(),menuLoader.registerPublishMarksMenu(),menuLoader.registerPublishReportCardsMenu(),menuLoader.registerUpdateStudentInfoMenu(),menuLoader.registerPersonalityTraits(),menuLoader.registerReportCardVariables(),menuLoader.registerReportCardMenu(),menuLoader.registerGreenSheetMenu(),menuLoader.registerReportsDetailsMenu(),menuLoader.registerAdmitCardsMenu(),menuLoader.registerNotificationMenu(),menuLoader.registerNotificationHistoryMenu(),menuLoader.registerGradeSchemeMenu(),menuLoader.registerStudentHPCFormMenu(),menuLoader.registerHPCReportCardMenu(),menuLoader.registerHPCPublishReportCardsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerExamConfigurationMenu:function(){$("#configureExamNav").on("click",function(){examinationConfig.loadExamConfigurationPage()})},registerFeedMarksMenu:function(){$("#feedMarksNav").on("click",function(){marksFeeding.loadMainPage()})},registerAdvanceFeedMarksMenu:function(){$("#advanceFeedMarksNav").on("click",function(){advanceMarksFeeding.loadMainPage()})},registerViewMarksMenu:function(){$("#viewMarksNav").on("click",function(){marksView.loadMainPage()})},registerDimensionsMenu:function(){$("#dimensionsNav").on("click",function(){dimensions.loadDimensionsPage()})},registerPersonalityTraits:function(){$("#personalityTraits").on("click",function(){personalityTraits.loadMainPage()})},registerReportCardVariables:function(){$("#reportCardVariables").on("click",function(){reportCardVariables.loadMainPage()})},registerReportCardMenu:function(){$("#reportCardNav").on("click",function(){reportCard.loadReportCardPage()})},registerDatesheetMenu:function(){$("#datesheetNav").on("click",function(){datesheet.loadDatesheetPage()})},registerPublishMarksMenu:function(){$("#publishMarksNav").on("click",function(){publishMarks.loadPublishMarksPage()})},registerPublishReportCardsMenu:function(){$("#publishReportCardsNav").on("click",function(){publishReportCards.loadMainScreen()})},registerUpdateStudentInfoMenu:function(){$("#updateStudentInfoNav").on("click",function(){updateStudentInfo.loadMainScreen()})},registerStudentHPCFormMenu:function(){$("#hpcStudentForm").on("click",function(){hpc.loadHPCFormPage()})},registerHPCPublishReportCardsMenu:function(){$("#publishHPCReportCardsNav").on("click",function(){publishHPCReportCards.loadMainScreen()})},registerHPCReportCardMenu:function(){$("#generateHPC").on("click",function(){hpcReportCard.loadHPCReportCardPage()})},registerGreenSheetMenu:function(){$("#greenSheetNav").on("click",function(){greenSheet.loadGreenSheetPage()})},registerAdmitCardsMenu:function(){$("#admitCardsNav").on("click",function(){admitCards.loadMainScreen()})},registerNotificationMenu:function(){$("#notificationNav").on("click",function(){notification.loadMainScreen()})},registerNotificationHistoryMenu:function(){$("#notificationHistoryNav").on("click",function(){notificationHistory.loadNotificationHistoryHomePage()})},registerReportsDetailsMenu:function(){$("#reportsNav").on("click",function(){examinationReports.loadMainScreen()})},registerGradeSchemeMenu:function(){$("#gradeNav").on("click",function(){gradeScheme.loadGradeSchemeListPage()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/examination/home",function(e){$("#main-content").html(e),homePage.initHomePage()})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/examination/session-home/"+e,function(e){$("#examination-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()},displayDashboardContent:function(){var e=readJson("#home-page-stats"),a=[],t=[],n=0;for(i=0;i<e.length;i++)a.push(e[i].standard.displayName),t.push(e[i].standard.studentCount),n+=e[i].standard.studentCount;$("#total-student-count").text(n),homePage.renderStudentCountChart(a,t),homePage.renderFeeCollectionPieChart(a,t)},renderStudentCountChart:function(e,a){new Chart($("#chartjs-student-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Last year",backgroundColor:window.theme.primary,borderColor:window.theme.primary,hoverBackgroundColor:window.theme.primary,hoverBorderColor:window.theme.primary,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{gridLines:{display:!1},stacked:!1,ticks:{stepSize:20},stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeeCollectionPieChart:function(e,a){new Chart($("#chartjs-fee-collection-pie"),{type:"pie",data:{labels:e,datasets:[{data:a,backgroundColor:[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,"#108F2B","#C08143 ","#DCDF69","#719E90","#A8B9DF ","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"],borderWidth:5,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cutoutPercentage:70,legend:{display:!1}}})}};function studentLiveSearchEvent(e,a,t,n,i){ajaxClient.get("/examination/student-live-search/"+e+"?searchText="+a+"&status="+i,function(e){$(t).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(t,n)})}