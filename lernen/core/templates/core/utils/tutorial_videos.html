{% if tutorial_videos %}
    {% block right-side-drawer-css %}
    <style>
        /* Drawer Styling */
        #rightDrawer {
            position: fixed;
            top: 0;
            right: -350px; /* Initially hidden */
            width: 350px;
            height: 100%;
            background-color: #fff;
            overflow-y: auto;
            transition: all 0.3s ease-in-out;
            z-index: 1050;
        }
        #rightDrawer.open {
            right: 0; /* Slide in */
        }
        #closeDrawerBtn{
            cursor: pointer;
        }

        /* Overlay Styling */
        #drawerOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1040;
        }

        /* Fixed Button Styling */
        #openDrawerBtn {
            position: fixed;
            top: 40%; /* Adjust vertically */
            right: 0; /* Stick to the edge */
            margin-right: 1.15%;
            transform: translateY(-50%) rotate(90deg); /* Rotate 90 degrees */
            transform-origin: center right; /* Align the rotation point */
            z-index: 1060;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            box-shadow: -2px 0 5px rgba(67, 162, 173, 0.3);
        }
        #openDrawerBtn.hidden {
            display: none;
        }

        /* Header Text Styling (Bold Effect) */
        #rightDrawer h3 {
            font-size: 1.2rem;
            text-shadow: 0.5px 0.5px 0 rgba(0, 0, 0, 0.2); /* Create bold effect */
            color: #000;
        }

        /* Collapsible Button Styling */
        .collapsible-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            background: none;
            border: none;
            text-align: left;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #ddd; /* Light border under each section */
        }
        .collapsible-btn:focus {
            outline: none;
        }
        .collapsible-btn .arrow {
            font-size: 16px;
            transform: rotate(270deg); /* Default arrow direction */
            transition: transform 0.3s ease; /* Smooth rotation */
        }
        .collapsible-btn.collapsed .arrow {
            transform: rotate(90deg); /* Rotate down when expanded */
        }

        /* Collapsible Content Styling */
        .video1 {
            border-bottom: 1px solid #ddd; /* Add bottom border */
            padding-bottom: 10px; /* Space below the content */
        }

        .video1.show {
            padding-bottom: 15px; /* More padding when the collapsible is open */
        }

        /* Video Placeholder Styling */
        .video-placeholder {
            position: relative;
            width: 100%;
            height: 200px;
            cursor: pointer;
        }
        .video-placeholder iframe {
            width: 100%;
            height: 100%;
            pointer-events: none; /* Prevent interaction */
        }
        .video-placeholder .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 18px;
            font-weight: bold;
        }

        /* Mobile Responsiveness */
        @media (max-width: 576px) {
            #rightDrawer {
                width: 100%; /* Full width for mobile screens */
            }
        }
    </style>
    {% endblock %}

    <!-- Button to Trigger Drawer -->
    {% block right-drawer-content %}
    <button id="openDrawerBtn" class="btn btn-primary">Tutorial Videos</button>

    <!-- Drawer (Right Sidebar) -->
    <div id="rightDrawer">
        <div class="p-3">
            <h3 class="d-flex justify-content-between align-items-center">
                Embrate Tutorial Videos
                <button type="button" class="close" id="closeDrawerBtn">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </h3>
            <hr>
            <!-- Video Links -->
            <div id="videoAccordion">
                {% for tutorial_video in tutorial_videos %}
                    <div>
                        <button
                            class="collapsible-btn collapsed"
                            type="button"
                            data-toggle="collapse"
                            data-target="#collapse{{ forloop.counter }}"
                            aria-expanded="false"
                            aria-controls="collapse{{ forloop.counter }}"
                        >
                            {{ tutorial_video.title }}
                            <span class="arrow">&gt;</span>
                        </button>
                        <div
                            id="collapse{{ forloop.counter }}"
                            class="collapse video1"
                            aria-labelledby="heading{{ forloop.counter }}"
                            data-parent="#videoAccordion"
                        >
                            <div class="p-2">
                                <!-- Description with Show More -->
                                <div class="video-description mb-2" id="desc-{{ forloop.counter }}">
                                    <span class="short-desc">
                                        {{ tutorial_video.description|truncatechars:150 }}
                                    </span>
                                    <span class="full-desc d-none">
                                        {{ tutorial_video.description }}
                                    </span>
                                    {% if tutorial_video.description|length > 150 %}
                                        <a class="toggle-desc text-primary" data-target="{{ forloop.counter }}">Show more</a>
                                    {% endif %}
                                </div>
                                <!-- Embedded YouTube Video -->
                                <div
                                    class="video-placeholder rounded"
                                    data-video="{{ tutorial_video.url }}"
                                >
                                    <iframe
                                        src="{{ tutorial_video.url }}"
                                        frameborder="0"
                                        allow="autoplay; fullscreen"
                                        allowfullscreen
                                    ></iframe>
                                    <div class="overlay">Click to Play</div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div id="drawerOverlay"></div>

    <!-- Fullscreen Video Modal -->
    <div id="videoModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="videoModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body p-0">
                    <iframe id="videoFrame" src="" style="width: 100%; height: 450px;" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
    {% endblock %}

    <!-- jQuery and Bootstrap 4 JS -->
    {% block right-drawer-script %}
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script>
        $(document).ready(function () {
            const $openDrawerBtn = $('#openDrawerBtn');
            const $rightDrawer = $('#rightDrawer');
            const $drawerOverlay = $('#drawerOverlay');

            // Open Drawer
            $openDrawerBtn.click(function () {
                $rightDrawer.addClass('open card-border');
                $drawerOverlay.fadeIn();
                $openDrawerBtn.addClass('hidden'); // Hide the button
            });

            // Close Drawer
            $('#closeDrawerBtn, #drawerOverlay').click(function () {
                $rightDrawer.removeClass('open card-border');
                $drawerOverlay.fadeOut();
                $openDrawerBtn.removeClass('hidden'); // Show the button again
            });

            // Open Video in Fullscreen Modal
            $('#videoAccordion').on('click', '.video-placeholder', function () {
                var videoSrc = $(this).data('video') + "?autoplay=1"; // Append autoplay
                $('#videoFrame').attr('src', videoSrc);
                $('#videoModal').modal('show');
                $rightDrawer.removeClass('open card-border');
                $drawerOverlay.fadeOut();
            });

            // Stop Video Playback on Modal Close
            $('#videoModal').on('hidden.bs.modal', function () {
                $('#videoFrame').attr('src', ''); // Reset src to stop video
                $('#videoModal').modal('hide');
                $rightDrawer.addClass('open card-border');
                $drawerOverlay.fadeIn();
            });

            $(".toggle-desc").on("click", function () {
                const targetId = $(this).data("target");
                const $descContainer = $("#desc-" + targetId);
                const $shortDesc = $descContainer.find(".short-desc");
                const $fullDesc = $descContainer.find(".full-desc");

                if ($fullDesc.hasClass("d-none")) {
                    $shortDesc.addClass("d-none");
                    $fullDesc.removeClass("d-none");
                    $(this).text("Show less");
                } else {
                    $shortDesc.removeClass("d-none");
                    $fullDesc.addClass("d-none");
                    $(this).text("Show more");
                }
            });
        });
    </script>
    {% endblock %}
{% endif %}