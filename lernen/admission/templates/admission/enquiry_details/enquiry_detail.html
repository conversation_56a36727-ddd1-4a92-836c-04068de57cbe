{% load displaytime %}
{% load json %}
<p id="enquiry-details-p" style="display:none">{{ enquiry_details|jsonstr }}</p>
<div>
    {% if enquiry_details %}
    <h4 class="card-title" style="color:#43a2ad;">
        <img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/back-button-20.png" onclick="admissionEnquiry.backButton()" style="cursor:pointer;">
        Enquiry Details
    </h4>

    <div>

        <div class="row">
            <div class="col-md-6 d-flex">
                <div class="card border  flex-grow-1">
                    <div class="card-body d-flex pb-0">
                        <div class="w-50 ">
                            <p><strong>Parent Name:</strong> {{ enquiry_details.guardianName }}</p>
                            <p><strong>Child Name:</strong> {{ enquiry_details.childName }}</p>
                            <p><strong>Class:</strong> {{ enquiry_details.standardName }}</p>
                            <p><strong>Phone:</strong> {{ enquiry_details.guardianContactInfo }}</p>
                        </div>
                        <div class="w-50 pl-40">
                            <p><strong>Email:</strong> {% if enquiry_details.guardianEmailId %}{{ enquiry_details.guardianEmailId }}{% else %}-{% endif %}</p>
                            <p><strong>Tracking ID:</strong> {{ enquiry_details.trackingId }}</p>
                            <p><strong>Status:</strong> {{ enquiry_details.enquiryStatus }}</p>

                        </div>
                    </div>
                    <p class="w-100 pr-2" style="padding-left : 20px;"><strong>Message:</strong> {{ enquiry_details.message }}</p>
                </div>
            </div>

            <div class="col-md-6 d-flex">
                <div class="card border flex-grow-1" style="width: 55%">
                    <div class="card-body d-flex pb-0" >
                        <div class="w-50">
                            <p><strong>Raised On:</strong> {{ enquiry_details.enquiryDate|print_date_with_month_text }}</p>
                            <p><strong>Total Follow-Ups:</strong> {{ enquiry_details.followUpPayloadList|length|default:0 }}</p>
                            {% if enquiry_details.acceptedUserLite %}
                            <p><strong>Accepted By:</strong> {{ enquiry_details.acceptedUserLite.name }}</p>
                            <p><strong>Accepted On:</strong> {{ enquiry_details.acceptedDate|print_date_with_month_text }}</p>
                            {% endif %}
                            {% if enquiry_details.rejectedUserLite %}
                            <p><strong>Rejected By:</strong> {{ enquiry_details.rejectedUserLite.name }}</p>
                            <p><strong>Rejected On:</strong> {{ enquiry_details.rejectedDate|print_date_with_month_text }}</p>
                            {% endif %}
                        </div>
                        <div class="w-50 pl-40">
                            {% if enquiry_details.acceptedUserLite and enquiry_details.reason %}
                            <p><strong>Accepted Reason:</strong> {{ enquiry_details.reason }}</p>
                            {% endif %}
                            {% if enquiry_details.rejectedUserLite and enquiry_details.reason %}
                            <p><strong>Rejected Reason:</strong> {{ enquiry_details.reason }}</p>
                            {% endif %}
                            {% if enquiry_details.closedUserLite %}
                            <p><strong>Closed By:</strong> {{ enquiry_details.closedUserLite.name }}</p>
                            <p><strong>Closed On:</strong> {{ enquiry_details.closedDate|print_date_with_month_text }}</p>
                            {% if enquiry_details.outcome %}
                            <p><strong>Outcome:</strong> {{ enquiry_details.outcome }}</p>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div>
    </div>
    <div class="card">
        <div class="card-header mt-3">

                <h3 class="align-left" style="display:inline-block;">FOLLOW UP DETAILS</h3>
            {% if enquiry_details.enquiryStatus == 'ACCEPTED' %}
            <div style="display:inline-block;float:right;">
                <button class="btn btn-outline-primary mr-2" onclick="followup.addFollowUpDetails('{{enquiry_details.enquiryId}}', 'DETAILS')">Add Follow up</button>
            </div>
            {% endif %}
        </div>
        {% if enquiry_details.followUpPayloadList|length > 0  %}
        <div class="card-body">
            <div>
                {% for follow_up_detail in enquiry_details.followUpPayloadList %}
                <div class="card-border p-3" style="border:1px solid #dee6ed">
                    <div>
                        <div>
                            <p style="font-size:16px;display:inline-block;">
                                {% if follow_up_detail.followUpMode %}
                                {% if follow_up_detail.followUpMode == "SMS" %}
                                SMS Sent
                                {% elif follow_up_detail.followUpMode == "CALL" %}
                                Voice Call Sent
                                {% elif follow_up_detail.followUpMode == "MANUAL" %}
                                Manual Conversation
                                {% elif follow_up_detail.followUpMode == "APP" %}
                                Mobile App Notification
                                {% endif %}
                                {% else %}
                                -
                                {% endif %}
                            </p>
                        </div>
                        <div>
                            <div class="mt-0">
                                <p style="color:#43a2ad;">{{follow_up_detail.followUpDate|print_date_with_month_text}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="pt-0">
                        <p class="pl-3 pr-3 pt-2 pb-2" style="text-align:justify;background-color:#f4f9f9;">
                            {% if follow_up_detail.contactPersonName %} {{follow_up_detail.contactPersonName}} <br/>{% endif %}
                            {{follow_up_detail.conversation}}
                        </p>

                        <div>
                            {% if follow_up_detail.nextFollowUpDate %}
                            <p style="color:#43a2ad;display:inline-block;float:right;">
                                Next Follow up Date : {{follow_up_detail.nextFollowUpDate|print_date_with_month_text}}
                            </p>
                            {%endif%}
                        </div>

                        {% if follow_up_detail.followUpMode == 'MANUAL' %}
                        <div class="pl-0">
                            {% if follow_up_detail.followUpStatus == 'ACTIVE' and enquiry_details.enquiryStatus == 'ACCEPTED' %}
                            <p style="display:none;" id="follow-up-json">{{follow_up_detail|jsonstr}}</p>
                            <button class="btn btn-outline-warning pl-4 pr-4" onclick="admissionEnquiry.editFollowUpDetails(this)">Edit</button>
                            <button class="btn btn-outline-danger ml-2 pl-3 pr-3" onclick="followup.deleteFollowUpDetails('{{follow_up_detail.followUpId}}','{{follow_up_detail.entityId}}')">Delete</button>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <br/>

                {% endfor %}
            </div>
            <br />
        </div>
    </div>
        {% else %}
        <div>
            <br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>
            <h5 style="color:#43a2ad" class="text-center">No followup details added yet!</h5>
            <br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>
        </div>
        {% endif %}
    </div>
        {% else %}
        <h1 class="text-center" style="color: #43a2ad;">No Enquiry Details Found</h1>
        {% endif %}


