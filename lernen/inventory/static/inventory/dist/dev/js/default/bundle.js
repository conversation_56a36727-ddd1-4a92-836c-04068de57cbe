var sidebarFull = 200;
var sidebarCollapsed = 80;
var baseURL = getBaseURL();
var graphLibraryLoaded = false;

$(document).ready(function() {
  menuLoader.registerSidebarMenu();
  homePage.initHomePage();
  paymentReminder.readPaymentState();
});


var menuLoader = {

  registerSidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.registerHomeMenu();
      menuLoader.registerStockDetailsMenu();
      menuLoader.registerPurchaseOrderMenu();
      menuLoader.registerSellItemsMenu();
      menuLoader.registerExchangeItemsMenu();
      menuLoader.registerIssueItemsMenu();
      menuLoader.registerReturnItemsMenu();
      menuLoader.registerSaleReturnItemsMenu();
      menuLoader.registerSupplierMenu();
      menuLoader.registerReportsMenu();
      menuLoader.registerTransactionsMenu();
      menuLoader.registerProductGroupsMenu();
      menuLoader.registerBrandsMenu();
  },

  registerHomeMenu : function() {
      $('#homeNav').on('click', function() {
        homePage.loadHomePage();
      });
  },

  registerStockDetailsMenu : function (){
    $('#stockDetailsNav').on('click', function () {
      inventoryStock.loadStockDetailsMenu();
    });
  },

  registerPurchaseOrderMenu : function() {
      $('#purchaseOrderNav').on('click', function() {
        inventoryPurchase.loadNewPurchaseMenu();
      });
  },

  registerSellItemsMenu : function () {
      $('#sellItemsNav').on('click', function() {
          inventorySale.loadNewSaleMenu(inventorySale.saleType);
      });
  },

  registerIssueItemsMenu : function () {
      $('#issueItemsNav').on('click', function() {
          inventoryIssueProduct.loadIssueProductMenu();
      });
  },

  registerExchangeItemsMenu : function () {
    $('#exchangeItemsNav').on('click', function() {
      inventoryExchangeProduct.loadExchangeProductMenu();
    });
},

  registerReturnItemsMenu : function () {
      $('#returnItemsNav').on('click', function() {
          inventorySale.loadNewSaleMenu(inventorySale.returnType);
      });
  },

  registerSaleReturnItemsMenu : function () {
      $('#saleReturnItemsNav').on('click', function() {
          inventorySale.loadNewSaleMenu(inventorySale.saleReturnType);
      });
  },

  registerSupplierMenu : function () {
      $('#supplierDetailsNav').on('click', function() {
          inventorySupplier.loadSupplierMenu();
      });
  },

  registerReportsMenu : function () {
    $('#reportsNav').on('click', function() {
      inventoryReports.loadReportsMenu();
    });
  },

  registerTransactionsMenu : function () {
      $('#transactionsNav').on('click', function() {
          inventoryTransactions.loadTransactionMenu();
      });
  },

  registerProductGroupsMenu : function () {
      $('#productGroupNav').on('click', function() {
          inventoryProductGroup.loadProductGroupsMenu();
      });
  },

  registerBrandsMenu : function () {
      $('#brandNav').on('click', function() {
          inventoryBrand.loadBrandsMenu();
      });
  },

};


var homePage =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        // homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/inventory/home", function(data) {
          $("#main-content").html(data);
          // homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      ajaxClient.get("/inventory/session-home", function(data) {
          $("#store-dashboard-session-content").html(data);
          // homePage.displayDashboardContent();
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

   displayDashboardContent : function () {
     var transationStats = readJson("#home-page-transaction-stats");
     var saleAmtlabelArr = [];
     var saleAmtDataArr = []
     for( day in transationStats.perDaySaleAmount){
       var value = transationStats.perDaySaleAmount[day];
       //
       saleAmtlabelArr.push(getShortWeekDay(day));
       saleAmtDataArr.push(value);
     }
     homePage.renderSalesAmountChart(saleAmtlabelArr, saleAmtDataArr);

     var purchaseAmtlabelArr = [];
     var purchaseAmtDataArr = []
     for( day in transationStats.perDayPurchaseAmount){
       var value = transationStats.perDayPurchaseAmount[day];
       //
       purchaseAmtlabelArr.push(getShortWeekDay(day));
       purchaseAmtDataArr.push(value);
     }

     homePage.renderPurchaseAmountChart(purchaseAmtlabelArr, purchaseAmtDataArr);

     var saleQtylabelArr = [];
     var saleQtyDataArr = []
     for( day in transationStats.perDaySaleQuanitity){
       var value = transationStats.perDaySaleQuanitity[day];
       //
       saleQtylabelArr.push(getShortWeekDay(day));
       saleQtyDataArr.push(value);
     }
     homePage.renderSalesQuantityChart(saleQtylabelArr, saleQtyDataArr);

     var purchaseQtylabelArr = [];
     var purchaseQtyDataArr = []
     for( day in transationStats.perDayPurchaseQuanitity){
       var value = transationStats.perDayPurchaseQuanitity[day];
       //
       purchaseQtylabelArr.push(getShortWeekDay(day));
       purchaseQtyDataArr.push(value);
     }
     homePage.renderPurchaseQuantityChart(purchaseQtylabelArr, purchaseQtyDataArr);

     // homePage.renderFeeCollectionPieChart(labelArr, dataArr);
   },

   renderSalesAmountChart : function (labelArr, dataArr) {
     // Line chart
     new Chart($("#chartjs-sales-amount-distribution"), {
       type: "line",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Sales Amount",
           fill: true,
           backgroundColor: "transparent",
           borderColor: window.theme.primary,
           data: dataArr
         }]
       },
       options: {
         maintainAspectRatio: false,
         legend: {
           display: false
         },
         tooltips: {
           intersect: false
         },
         hover: {
           intersect: true
         },
         plugins: {
           filler: {
             propagate: false
           }
         },
         scales: {
           xAxes: [{
             reverse: true,
             gridLines: {
               color: "rgba(0,0,0,0.05)"
             }
           }],
           yAxes: [{
             ticks: {
               stepSize: 500
             },
             display: true,
             borderDash: [5, 5],
             gridLines: {
               color: "rgba(0,0,0,0)",
               fontColor: "#fff"
             }
           }]
         }
       }
     });
   },

   renderPurchaseAmountChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-purchase-amount-distribution"), {
       type: "line",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Purchase Amount",
           fill: true,
           backgroundColor: "transparent",
           borderColor: window.theme.warning,
           data: dataArr
         }]
       },
       options: {
         maintainAspectRatio: false,
         legend: {
           display: false
         },
         tooltips: {
           intersect: false
         },
         hover: {
           intersect: true
         },
         plugins: {
           filler: {
             propagate: false
           }
         },
         scales: {
           xAxes: [{
             reverse: true,
             gridLines: {
               color: "rgba(0,0,0,0.05)"
             }
           }],
           yAxes: [{
             ticks: {
               stepSize: 500
             },
             display: true,
             borderDash: [5, 5],
             gridLines: {
               color: "rgba(0,0,0,0)",
               fontColor: "#fff"
             }
           }]
         }
       }
     });
   },

   renderSalesQuantityChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-sales-quantity-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Sales Quantity",
           backgroundColor: window.theme.primary,
           borderColor: window.theme.primary,
           hoverBackgroundColor: window.theme.primary,
           hoverBorderColor: window.theme.primary,
           data: dataArr,
           barPercentage: .325,
           categoryPercentage: .5
         }]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

   renderPurchaseQuantityChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-purchase-quantity-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Purchase Quantity",
           backgroundColor: window.theme.primary,
           borderColor: window.theme.primary,
           hoverBackgroundColor: window.theme.primary,
           hoverBorderColor: window.theme.primary,
           data: dataArr,
           barPercentage: .325,
           categoryPercentage: .5
         }]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

   renderFeeCollectionPieChart : function (labelArr, dataArr) {
     new Chart($("#chartjs-fee-collection-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: [
             window.theme.success,
             window.theme.warning,
             window.theme.danger,
             window.theme.info,
             "#108F2B",
             "#C08143 ",
             "#DCDF69",
             "#719E90",
             "#A8B9DF ",
             "#B74034",
             "#F196CD",
             "#6486B9",
             "#5551FA",
             "#E8F697",
             "#0591F6",
             "#4C6C42",
             "#442BC6",
           ],
           borderWidth: 5,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: true,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       }
     });
   },
};

var inventoryStock = {

  dataCache : {},

  loadStockDetailsMenu : function (){
      ajaxClient.get("/inventory/stock-home", function(data){
        $("#main-content").html(data);

        inventoryStock.bindAddInitialProductCheckbox("#add-stock-form");
        initDate(365);
        inventoryStock.bindStockSearchCallback();
        inventoryStock.registerInputEnterEvent();
        registerLableCloseActions();
        registerDropDownSelectEvent();
        $('#add-stock-modal').on('shown.bs.modal', function () {
          function validateDecimalInput(input) {
            let val = $(input).val();
            
            const valid = /^(\d+(\.\d{0,2})?)?$/.test(val);
            if (!valid) {
              alert('Please enter a valid price with no more than 2 decimal places.');
              
              let corrected = val.match(/^(\d+(\.\d{0,2})?)/);
              $(input).val(corrected ? corrected[0] : '');
            }
          }

          $('.product-batch-selling-price').off('change').on('change', function () {
            validateDecimalInput(this);
          });
      
          $('.product-batch-purchase-price').off('change').on('change', function () {
            validateDecimalInput(this);
          });
        });

        // searchStock();
        // registerStockSearchCallback();
        // registerAddStockCallback();
        // registerUpdateStockCallback();
        // registerInputEnterEvent();
        // registerLableCloseActions();
        // registerCategoryEvents();
        // bindRemoveErrorDisplayEvent();
     });
  },

  expandBatchDetails : function (divId) {
    $("#"+divId).collapse('toggle');
  },

  bindAddInitialProductCheckbox : function (parentSelector) {
    $(parentSelector).find('.add-initial-batch').click(function(){
        if($(this).is(":checked")){
          $(this).closest('.product-container').find('.product-batch-details').attr('style','display:block');
        }else{
          $(this).closest('.product-container').find('.product-batch-details').attr('style','display:none');
        }
    });
  },

  bindDeleteProductCheckbox : function (parentSelector) {
    $(parentSelector).find('.delete-product').click(function(){
        $(this).closest('.product-container').remove();
        inventoryStock.computeTotalProductsForASB(parentSelector);
    });
  },

  computeTotalProductsForASB : function (parentSelector) {
    $("#total-product-count").val($(parentSelector).find(".product-container").length);
  },

  addProducts : function () {
    var productPayloadList = [];
    //TODO : add validations
    $('#products').children('.product-container').each(function () {
      var name = $(this).find(".product-name").val();
      var categoryId = $(this).find(".product-category").find(':selected').val().trim();
      var brandId = $(this).find(".product-brand").find(':selected').val().trim();
      var description = $(this).find(".product-description").val();

      var productBatchPayloadList = inventoryStock.getProductInitialBatchPayloadList(this);
      productPayloadList.push({'productMetadataPayload' : {'name' : name, 'categoryId' : categoryId, "brandId" : brandId, "description" : description}, 'productBatchPayloadList' : productBatchPayloadList});
    });

    $("#add-stock-modal").modal('toggle');
    inventoryStock.addStock(productPayloadList);

  },

  updateProduct : function () {
    var selectedSkuId = inventoryStock.dataCache['selectedSkuId'];
    var name = $("#update-product-name").val();
    var categoryId = $("#update-product-category").find(':selected').val().trim();
    var brandId = $("#update-product-brand").find(':selected').val().trim();
    var description = $("#update-product-description").val();

    var productBatchUpdatePayloadList = [];
    //TODO : add validations
    $('#update-product-batches').children('.update-product-batch-container').each(function () {
      var batchId = $(this).attr('id');
      if(batchId == null || batchId.trim() == "" ||batchId == undefined){
        batchId = null;
      }
      var batchName = $(this).find(".update-product-batch-name").val();
      var sellingPrice = $(this).find(".update-product-batch-selling-price").val();
      var discount = $(this).find(".update-product-batch-discount").val();
      var discountInPercent = $(this).find(".update-discount-in-percent").is(":checked");
      var initialQuantity = $(this).find(".update-product-batch-initial-quantity").val();
      var purchasePrice = $(this).find(".update-product-batch-purchase-price").val();
      var mrp = $(this).find(".update-product-batch-mrp").val();
      var batchDescription = $(this).find(".update-product-batch-description").val();

      var active = $(this).find(".update-active-batch").is(":checked");

      var defaultBatch = false;
      if($(this).find('.update-default-batch').is(":checked")) {
          defaultBatch = true;
      }
      $(this).find('input[name=radioName]:checked').val()

      productBatchUpdatePayloadList.push({'batchId' : batchId, 'batchName' : batchName, 'description' : batchDescription, "initialQuantity" : initialQuantity, "sellingPrice" : sellingPrice, 'discount' : discount, 'discountInPercent' : discountInPercent, 'purchasePrice' : purchasePrice, 'mrp' : mrp, 'defaultBatch' : defaultBatch, 'active' : active});
    });

    var updatePayload = {'skuId' : selectedSkuId, 'productMetadataPayload' : {'name' : name, 'categoryId' : categoryId, "brandId" : brandId, "description" : description}, 'productBatchUpdatePayloadList' : productBatchUpdatePayloadList}

    $("#update-product-modal").modal('toggle');

    ajaxClient.post("/inventory/update-product", {'updatePayload' : JSON.stringify(updatePayload)}, function(data){
      $("#stock-status-modal-container").html(data);
      $("#stock-status-modal").modal('toggle');
      inventoryStock.searchStock(false);
    });

  },

  getProductInitialBatchPayloadList : function (parentSelector) {
    var productBatchPayloadList = [];
    var addInitialBatch = $(parentSelector).find('.add-initial-batch').is(":checked");
    if(!addInitialBatch){
      return [];
    }

    var batchName = $(parentSelector).find(".product-batch-name").val();
    var batchDescription = $(parentSelector).find(".product-batch-description").val();
    var initialQuantity = $(parentSelector).find(".product-batch-initial-quantity").val();
    // var initialQuantityAsOf = getDate($(parentSelector).find('.product-batch-initial-quantity-date').val());
    var sellingPrice = $(parentSelector).find(".product-batch-selling-price").val();
    var discount = $(parentSelector).find(".product-batch-discount").val();
    var discountInPercent = $(parentSelector).find('.discount-in-percent').is(":checked");
    var purchasePrice = $(parentSelector).find(".product-batch-purchase-price").val();
    var mrp = $(parentSelector).find(".product-batch-mrp").val();

    return [{'batchName' : batchName, 'description' : batchDescription,
    'initialQuantity' : initialQuantity, 'sellingPrice' : sellingPrice,
    'discount' : discount, 'discountInPercent' : discountInPercent, 'purchasePrice' : purchasePrice, 'mrp' : mrp}];

  },

  addASBProducts : function (categoryId, brandId) {
    var productPayloadList = [];
    $('#asb-products').children('.product-container').each(function () {
      var name = $(this).find(".product-name").val();
      var description = $(this).find(".product-description").val();
      var productBatchPayloadList = inventoryStock.getProductInitialBatchPayloadList(this);
      productPayloadList.push({'productMetadataPayload' : {'name' : name, 'categoryId' : categoryId, "brandId" : brandId, "description" : description}, 'productBatchPayloadList' : productBatchPayloadList});
    });

    $("#advance-stock-products-modal").modal('toggle');
    inventoryStock.addStock(productPayloadList);
  },

  addStock : function (productPayloadList) {
    var newProductRequest = {'productPayloadList' : productPayloadList};
    ajaxClient.post("/inventory/add-stock", {'newProductRequest' : JSON.stringify(newProductRequest)}, function(data){
      $("#stock-status-modal-container").html(data);
      $("#stock-status-modal").modal('toggle');
      inventoryStock.searchStock(true);
    });
  },

  bindStockSearchCallback : function(){
      $('#search-stock').on('click', function () {
            inventoryStock.searchStock(true);
      });
      $("#stock-search-text").on('keyup', function (e) {
        if (e.keyCode == 13) {
            inventoryStock.searchStock(true);
        }
      });
  },

  searchStock : function(freshSearch){
      var stockSearchResultDivId = "#stock-search-result";
      searchText = $("#stock-search-text").val();
      var page_number = $('.page-item.active').find('.page-number').text().trim();
      if(freshSearch){
          page_number = 1;
      }
      var itemsPerPage = $('#items-per-page').val();
      if(page_number == null || page_number == ''){
        page_number = 1;
      }
      var offset = (page_number - 1)*itemsPerPage;
      ajaxClient.get("/inventory/stock-search?text="+searchText+"&itemsPerPage="+itemsPerPage+"&offset="+offset, function(data){
          $(stockSearchResultDivId).html(data);
          var screenHeight = $("#sidebar").height() - $(stockSearchResultDivId).position().top - 300;
          $('#stock-search-result-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
          var response = JSON.parse($("#pagination-info").text().trim());
          $('#items-per-page').val(response.itemsPerPage);
          $('.page-item').removeClass('active');
          var pageNumber = (response.offset/response.itemsPerPage) + 1;
          $('#page-number-'+page_number).addClass('active');
          inventoryStock.initPagination();

          var products = readJson("#product-data-json");
          var productsMap = {};
          for(i = 0; i < products.length; i++){
            var product = products[i];
            productsMap[product.productMetadata.skuId] = product;
          }

          inventoryStock.dataCache['productsMap'] = productsMap;
          // bindUpdateProductCallback();
     });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        inventoryStock.searchStock(false);
      },
      function () {
        inventoryStock.searchStock(false);
      },
      function () {
        inventoryStock.searchStock(false);
      },
      function () {
        inventoryStock.searchStock(true);
      }
    );
  },

  registerInputEnterEvent : function(){
        $(".add-label-input").on('keyup', function (e) {
          if (e.keyCode == 13) {
              inventoryStock.addInputLables(this);
          }
        });

        $(".add-label-input").focus(function() {}).blur(function() {
           inventoryStock.addInputLables(this);
        });
  },

  addInputLables : function(labelInput){
      var enteredVal = $(labelInput).val();
      if(enteredVal == ""){
        return;
      }
      var values = enteredVal.trim().split(",")
      $(labelInput).val("");
      var label =  $(labelInput).parent().find("p");
      var existingContent = label.html();
      var newValues = []
      values.forEach(function(entry) {
          var exists = false;
          label.children('span').each(function(i) {
              if($(this).find(".label-text").length > 0){
                var labelVal = $(this).find(".label-text").text().trim();
                if(labelVal.toLowerCase() == entry.trim().toLowerCase()){
                   exists = true;
                   return;
                }
              }
          });
          if(!exists){
             newValues.push(entry);
          }
      });

      if(newValues.length > 0){
        newValues.forEach(function(entry) {
          if(entry.trim() == ""){
              return;
          }
          existingContent = existingContent + " <span class=\"badge badge-info label-area\" style=\"padding: .3em .4em .4em; margin: 0 .1em; font-size: 14px;\" \"> <span class=\"label-text\">"+ entry.trim() +"</span> <button type=\"button\" class=\"close label-close\" ><span aria-hidden=\"true\">&times;</span></button></span> "
        });
        label.html(existingContent);
        registerLableCloseActions();
      }
  },

  previewStock : function(){
    var productName = $("#asb-product-name").val();
    var categoryInput = $("#asb-product-category").find(':selected');
    var categoryId = categoryInput.val().trim();
    var categoryText = categoryInput.text().trim();
    var brandInput = $("#asb-product-brand").find(':selected');
    var brandId = brandInput.val().trim();
    var brandText = brandInput.text().trim();
    var sizes = inventoryStock.getSelectedAttributes("#asb-product-sizes");
    var colors = inventoryStock.getSelectedAttributes("#asb-product-colors");
    var genders = getDropdownSelectedAttributes("#asb-product-genders");
    var description = $("#asb-product-description").val();
    $("#advance-stock-builder-modal").modal('toggle');

    var productVariationsRequest = {"name":productName, "sizes":sizes,"colors":colors, "genders":genders, 'categoryId' : categoryId, 'categoryText' : categoryText, 'brandId' : brandId, 'brandText' : brandText, 'description' : description};
    ajaxClient.post("/inventory/product/variations",{'productVariationsRequest':JSON.stringify(productVariationsRequest)}, function(data){
        $("#advance-stock-builder-modal-container").html(data);
        inventoryStock.bindAddInitialProductCheckbox("#add-advance-stock-form");
        inventoryStock.bindDeleteProductCheckbox("#add-advance-stock-form");
        $("#advance-stock-products-modal").modal({backdrop: 'static', keyboard: false});
   });
 },

  backToASBBuilder : function () {
   $("#advance-stock-products-modal").modal('toggle');
   $("#advance-stock-builder-modal").modal('toggle');
  },

  getSelectedAttributes : function (id){
     var label =  $(id).parent().find("p");
     var attributes = []
     label.children('span').each(function(i) {
       if($(this).find(".label-text").length > 0){
         var labelVal = $(this).find(".label-text").text().trim();
         attributes.push(labelVal);
       }
     });
     return attributes;
  },

  editProduct : function (skuId) {
    var productMap = inventoryStock.dataCache['productsMap'];
    if(!(skuId in productMap)){
      return;
    }
    var product = productMap[skuId];
    inventoryStock.dataCache['selectedSkuId'] = skuId;
    $("#update-product-name").val(product.productMetadata.name);
    $("#update-product-category").val(product.productMetadata.categoryId);
    $("#update-product-brand").val(product.productMetadata.brandId);
    $("#update-product-description").val(product.productMetadata.description);
    $("#update-product-batches").html(inventoryStock.buildProductBatches(product));
    $("#update-product-modal").modal({backdrop: 'static', keyboard: false});
  },

  deleteProductPreview : function (skuId) {
    var productMap = inventoryStock.dataCache['productsMap'];
    if(!(skuId in productMap)){
      return;
    }
    inventoryStock.dataCache['deleteSelectedSkuId'] = skuId;
    $("#delete-product-modal").modal({backdrop: 'static', keyboard: false});
  },

  deleteProduct : function () {
    $("#delete-product-modal").modal('toggle');
    var skuId = inventoryStock.dataCache['deleteSelectedSkuId'];
    ajaxClient.post("/inventory/delete-product/"+skuId, {}, function(data){
      $("#stock-status-modal-container").html(data);
      $("#stock-status-modal").modal('toggle');
      inventoryStock.searchStock(true);
    });
  },

  addBatch : function () {
   var productBatch = {'batchId' : "", 'batchName' : "", 'description' : "", 'discount' : "", 'discountInPercent' : false, 'initialQuantity' : "", 'sellingPrice' : "", 'totalQuantity' : ""};
   $("#update-product-batches").append(inventoryStock.buildProductBatchRow(productBatch));
  },

  removeBatch : function (context) {
   $(context).closest('.update-product-batch-container').remove();
  },

  buildProductBatches : function (product) {
    var productBatchDataList = product.productBatchDataList;
    if(productBatchDataList == null || productBatchDataList.length == 0){
        return "";
    }

    var batchProductRows = [];
    for(i = 0; i < productBatchDataList.length; i++){
      var productBatch = productBatchDataList[i];

      var batchProductRow = inventoryStock.buildProductBatchRow(productBatch);
      batchProductRows.push(batchProductRow)
    }

    return batchProductRows;
  },

  buildProductBatchRow : function (productBatch) {

    var batchId = productBatch.batchId;
    var batchName = productBatch.batchName;
    var description = productBatch.description;
    var discount = productBatch.discount;
    var discountInPercent = productBatch.discountInPercent;
    var initialQuantity = productBatch.initialQuantity;
    var sellingPrice = productBatch.sellingPrice;
    var purchasePrice = productBatch.purchasePrice;
    var mrp = productBatch.mrp;
    var totalQuantity = productBatch.totalQuantity;
    var inUse = productBatch.inUse;
    var defaultBatch = productBatch.defaultBatch;
    var active = productBatch.active;

    var checkedVal = ""
    if(discountInPercent){
      checkedVal = "checked";
    }

    var activeCheckedVal = ""
    if(active){
      activeCheckedVal = "checked";
    }

    if(description == null){
      description = "";
    }

    var inUseText = "Batch has no transactions yet"
    var inUseTextColor = "#18A558";
    var removeBathDiv = "<div class=\"form-group col-md-1\"> <button type=\"button\" class=\"close remove-product-batch\" aria-label=\"Close\" onclick=\"inventoryStock.removeBatch(this)\"> <span aria-hidden=\"true\"> &times; </span> </button> </div>";
    if(inUse){
      inUseText = "Batch has been used in transactions"
      inUseTextColor = "#B8390E";
      removeBathDiv = "";
    }

    var radioChecked = "";
    if(defaultBatch){
      radioChecked = "checked"
    }

    var radioId = "batch-"+batchId;

    return "<div class=\"card update-product-batch-container\" id=\""+batchId+"\"> <div class=\"card-body\"> <div class=\"update-product-batch-details\"> <div class=\"form-row\"> <div class=\"form-group col-md-3\"> <div class=\"form-check\"> <input class=\"form-check-input update-default-batch\" type=\"radio\" name=\"defaultBatchRadio\" id=\""+radioId+"\" value=\""+radioId+"\" "+radioChecked+"> <label class=\"form-check-label\" for=\""+radioId+"\"> Default Batch </label> </div> </div> <div class=\"form-group col-md-3\"> <div class=\"form-check\"> <input type=\"checkbox\" class=\"form-check-input update-active-batch\" "+activeCheckedVal+"> <label class=\"form-check-label\"> <strong>Active Batch</strong> </label> </div> </div> <div class=\"form-group col-md-5\"> <p> <span style=\"color:"+inUseTextColor+";font-weight:bold;\" >"+inUseText+"</span></p> </div> "+removeBathDiv+" </div> <div class=\"form-row\"> <div class=\"form-group col-md-3\"> <label>Batch Name*</label> <input type=\"text\" class=\"form-control update-product-batch-name mandatory-field\" value=\""+batchName+"\"> </div> <div class=\"form-group col-md-3\"> <label>Available Quantity</label> <input type=\"number\" class=\"form-control update-product-batch-available-quantity\" placeholder=\"0\" value=\""+totalQuantity+"\" readonly> </div> <div class=\"form-group col-md-2\"> <label>Selling Price</label> <input type=\"number\" class=\"form-control update-product-batch-selling-price mandatory-field\" value=\""+sellingPrice+"\"> </div> <div class=\"form-group col-md-2\"> <label>Discount</label> <input type=\"number\" class=\"form-control update-product-batch-discount\" placeholder=\"0\" value=\""+discount+"\"> </div> <div class=\"form-group col-md-2\"> <div class=\"form-check\" style=\"margin-top:20%;\"> <input type=\"checkbox\" class=\"form-check-input update-discount-in-percent\" "+checkedVal+"> <label class=\"form-check-label\"> <strong>Discount in % </strong> </label> </div> </div> </div> <div class=\"form-row\"> <div class=\"form-group col-md-2\"> <label>Initial Quantity</label> <input type=\"number\" class=\"form-control update-product-batch-initial-quantity\" placeholder=\"Quanity\" value=\""+initialQuantity+"\"> </div> <div class=\"form-group col-md-2\"> <label>Purchase Price</label> <input type=\"number\" class=\"form-control update-product-batch-purchase-price\" value=\""+purchasePrice+"\"> </div> <div class=\"form-group col-md-2\"> <label>MRP</label> <input type=\"number\" class=\"form-control update-product-batch-mrp\" value=\""+mrp+"\"> </div> <div class=\"form-group col-md-6\"> <label>Batch Description</label> <textarea class=\"form-control update-product-batch-description\" rows=\"1\" placeholder=\"Batch Description\">"+description+"</textarea> </div> </div> </div> </div> </div>";

  },

};

var inventoryPurchase = {

  dataCache : {},

  loadNewPurchaseMenu : function (){
      ajaxClient.get("/inventory/new-purchase-home", function(data){
        $("#main-content").html(data);
        inventoryPurchase.init();
     });
  },

  loadPurchaseOrderForm : function () {
      ajaxClient.get("/inventory/new-purchase-form", function(data){
        $("#purchase-order-body").html(data);
        inventoryPurchase.init();
     });

  },

  init : function () {
    var categories = readJson("#category-data");
    var brands = readJson("#brand-data");
    inventoryPurchase.dataCache = {'categories' : categories, 'brands' : brands};
    initDate(pastTransactionDays);
    liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId, inventoryUtils.liveSearchProductResultElementId, inventoryPurchase.donePurchaseProductTyping);
    inventoryPurchase.bindPaymentStatusSelect();
  },

  donePurchaseProductTyping : function(resultArea) {
      var searchText = $(inventoryUtils.liveSearchProductElementId).val();
      ajaxClient.get("/inventory/live-stock-search?text=" + searchText+"&addNewProduct=True", function(data) {
          $(resultArea).html(data);
          inventoryUtils.bindClickEventOnSearchProduct(searchText, inventoryPurchase.dataCache, inventoryPurchase.getBatchSelectColumn,
            inventoryPurchase.getRowColumns, true, inventoryPurchase.getRowDetails, inventoryPurchase.bindPurchaseNewRowEvent, true, inventoryPurchase.onDelete);
      });
  },

  getBatchSelectColumn : function (options) {
    return "<div class=\"input-group\"> <select class=\"custom-select product-batch-select\">"+options+"</select> <div class=\"input-group-append\"> <button class=\"btn btn-outline-secondary add-product-batch\" type=\"button\">+</button> </div> </div>";
  },

  onDelete : function () {
    inventoryPurchase.computeNetPurchaseAmount();
  },

  bindPurchaseNewRowEvent : function () {
    inventoryPurchase.bindCostComputeEvent();
  },

  bindPaymentStatusSelect : function () {
    // $('select#purchase-payment-status').on('change', function() {
    //   var status = this.value.trim();
    //   if(status == "PARTIALLY_PAID"){
    //       $("#purchase-partial-payment-amount-div").attr("style", "display:block");
    //   }else{
    //       $("#purchase-partial-payment-amount-div").attr("style", "display:none");
    //   }
    // });
  },

  getRowColumns : function (new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue) {
    var firstCol = "<td scope=\"row\" style=\"width:15%;\"> "+productName+" </td>";
    if(new_product){
      firstCol = "<td scope=\"row\" style=\"width:15%;\"> <input type=\"text\" class=\"form-control form-control-sm new-product-name\" placeholder=\"Product name\" value=\""+productName+"\"> </td>"
    }
    var purchasePrice = 0;
    if(batchDetails != null){
      purchasePrice = batchDetails.productBatch.purchasePrice;
      if(purchasePrice == null){
        purchasePrice = 0;
      }
    }

    return [firstCol, "<td style=\"width:15%;\">"+batchColumn+"</td>", "<td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\" "+readonly+"> </td> "," <td><input type=\"number\" class=\"form-control form-control-sm purchase-price mandatory-table-field positive-number\" placeholder=\"0\" value=\""+purchasePrice+"\" "+readonly+"></td> ", " <td><input type=\"number\" class=\"form-control form-control-sm purchase-item-amount\" placeholder=\"0\" readonly></td> ", "<td><input type=\"number\" class=\"form-control form-control-sm purchase-discount percentage-range\" placeholder=\"Discount\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm purchase-product-tax percentage-range\" placeholder=\"Tax\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm purchase-item-total\" placeholder=\"0\" readonly></td>"];
  },

  getCategoryCol : function () {
    var categories = inventoryPurchase.dataCache.categories;

    var categoryOptions = "<option value=\"\">select</option>";
    for(var i = 0; i< categories.length; i++){
      var category = categories[i];
      categoryOptions += "<option value=\""+category.categoryId+"\">"+category.categoryName+"</option>";
    }
    return "<td><select class=\"form-control product-category mandatory-field\">"+categoryOptions+"</select></td>";
  },

  getBrandCol : function () {
    var brands = inventoryPurchase.dataCache.brands;
    var brandOptions = "<option value=\"\">select</option>";
    for(var i = 0; i< brands.length; i++){
      var brand = brands[i];
      brandOptions += "<option value=\""+brand.brandId+"\">"+brand.brandName+"</option>";
    }
    return "<td><select class=\"form-control product-brand mandatory-field\">"+brandOptions+"</select></td>";
  },

  getRowDetails : function (new_product, batchDetails) {
    var categoryHeaderCol = "";
    var brandHeaderCol = "";
    var categoryCol = "";
    var brandCol = "";

    var productDescriptionHeaderCol = "";
    var productDescriptionCol = "";

    var show = ""
    if(new_product){
      show = "show";

      categoryCol = inventoryPurchase.getCategoryCol();
      brandCol = inventoryPurchase.getBrandCol();

      categoryHeaderCol = "<th scope=\"col\">Category*</th>";
      brandHeaderCol = "<th scope=\"col\">Brand</th>";

      var productDescriptionHeaderCol = "<th scope=\"col\">Product Description</th>";
      var productDescriptionCol = "<td><textarea class=\"form-control product-description\" rows=\"1\" placeholder=\"Product Description\" ></textarea></td>";
    }

    var readonly = "readonly";
    var disabled = "disabled";
    if(new_product || batchDetails == null){
        readonly = "";
        disabled = "";
    }

    var mrp = "";
    var sellingPrice = "";
    var discountVal = "";
    var taxVal = "";
    var discountInPercent = "";
    var batchDescription = "";
    if(batchDetails != null){
      mrp = batchDetails.productBatch.mrp;
      sellingPrice = batchDetails.productBatch.sellingPrice;
      discountVal = batchDetails.productBatch.discount;
      // taxVal = batchDetails.productBatch.tax;
      if(batchDetails.productBatch.discountInPercent){
        discountInPercent = "checked";
      }
      batchDescription = batchDetails.productBatch.description;
      if(batchDescription == null){
        batchDescription = "";
      }
    }
    // <td><input type=\"number\" class=\"form-control form-control-sm product-batch-tax\"  placeholder=\"0\" value=\""+taxVal+"\" "+readonly+"></td>
    return "<tr class=\"product-batch-details-row\"> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-batch-details-container "+show+" \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> "+categoryHeaderCol+ " " + brandHeaderCol + " " + productDescriptionHeaderCol+ " <th scope=\"col\">MRP</th> <th scope=\"col\">Selling Price</th> <th scope=\"col\">Discount</th> <th scope=\"col\"></th> <th scope=\"col\">Batch Description</th> </tr> </thead> <tbody> <tr> "+categoryCol+" "+brandCol+ " " + productDescriptionCol + " <td><input type=\"number\" class=\"form-control form-control-sm product-batch-mrp\"  placeholder=\"0\" value=\""+mrp+"\" "+readonly+"></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-selling-price\"  placeholder=\"0\" value=\""+sellingPrice+"\" "+readonly+"></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-discount\"  placeholder=\"0\" value=\""+discountVal+"\" "+readonly+"></td><td><div class=\"form-group\"> <div class=\"form-check\" style=\"margin-top:8%;\"> <input type=\"checkbox\" class=\"form-check-input discount-in-percent\" "+discountInPercent+" "+disabled+" > <label class=\"form-check-label\"> <strong>Disc. in % </strong> </label> </div> </div></td> <td><textarea class=\"form-control batch-description\" rows=\"1\" placeholder=\"Batch Description\" " +readonly +">"+batchDescription+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>";
  },

  bindCostComputeEvent : function () {
      $(".product-quantity").focus(function() {}).blur(function() {
         inventoryPurchase.computeTotal($(this).parent().parent());
      });

      $(".purchase-price").focus(function() {}).blur(function() {
          inventoryPurchase.computeTotal($(this).parent().parent());
      });

      $(".purchase-discount").focus(function() {}).blur(function() {
          inventoryPurchase.computeTotal($(this).parent().parent());
      });

      $(".purchase-product-tax").focus(function() {}).blur(function() {
          inventoryPurchase.computeTotal($(this).parent().parent());
      });

      $("#additional-cost-input").focus(function() {}).blur(function() {
          inventoryPurchase.computeTotal($(this).parent().parent().parent());
      });

      $("#additional-discount-input").focus(function() {}).blur(function() {
          inventoryPurchase.computeTotal($(this).parent().parent().parent());
      });
  },

  computeTotal : function (current) {

      var quanityString = $(current).find(".product-quantity").first().val();
      var quantity = 0;
      if (quanityString) {
          quantity = parseFloat(quanityString);
      }

      var priceString = $(current).find(".purchase-price").first().val();
      var price = 0;
      if (priceString) {
          price = parseFloat(priceString);
      }
      var discountString = parseFloat($(current).first().find(".purchase-discount").first().val());
      var discount = 0;
      if (discountString) {
          discount = parseFloat(discountString);
      }

      var taxString = parseFloat($(current).first().find(".purchase-product-tax").first().val());
      var tax = 0;
      if (taxString) {
          tax = parseFloat(taxString);
      }

      var totalAmount = quantity*price;

      var netAmount = (totalAmount + totalAmount*tax/100) - (totalAmount + totalAmount*tax/100) * discount / 100;
      // var netAmount = totalAmount - totalAmount * discount / 100;
      $(current).find(".purchase-item-amount").first().val(totalAmount.toFixed(2));
      $(current).find(".purchase-item-total").first().val(netAmount.toFixed(2));
      inventoryPurchase.computeNetPurchaseAmount();
  },

  computeNetPurchaseAmount : function (){
        var totalDiscount = 0;
        var totalTax = 0;
        var subTotal = 0;
        $("tr.selected-product-row").each(function() {
            var quantity = $(this).find("input.product-quantity").val();
            if(quantity == ""){
              quantity = 0;
            }
            var purchasePrice = $(this).find("input.purchase-price").val();
            if(purchasePrice == ""){
              purchasePrice = 0;
            }
            var discount = $(this).find("input.purchase-discount").val();
            if(discount == ""){
              discount = 0;
            }
            var tax = $(this).find("input.purchase-product-tax").val();
            if(tax == "" || typeof tax === "undefined"){
              tax = 0;
              }
            var totalAmount = parseFloat(purchasePrice) * parseFloat(quantity);
            var discountAmount =  totalAmount * parseFloat(discount) / 100;
            var taxAmount = (totalAmount - discountAmount) * parseFloat(tax) / 100;

            totalDiscount += discountAmount;
            totalTax += taxAmount;
            subTotal += totalAmount;
        });

        var additionalCostString = $("#additional-cost-input").val();
        var additionalCost = 0;
        if (additionalCostString != "") {
            additionalCost = parseFloat(additionalCostString);
        }

        var additionalDiscountString = $("#additional-discount-input").val();
        var additionalDiscount = 0;
        if (additionalDiscountString != "") {
            additionalDiscount = parseFloat(additionalDiscountString);
        }

        $("#sub-total").text(subTotal.toFixed(2));
        $("#total-discount").text(totalDiscount.toFixed(2));
        $("#total-tax").text(totalTax.toFixed(2));
        $("#additional-cost").text(additionalCost.toFixed(2));
        $("#additional-discount").text(additionalDiscount.toFixed(2));
        $("#net-total").text((subTotal + additionalCost - totalDiscount - additionalDiscount + totalTax).toFixed(2));
        // $("#net-total").text((subTotal + additionalCost - totalDiscount - additionalDiscount).toFixed(2));
        var netTotal = subTotal + additionalCost - totalDiscount - additionalDiscount + totalTax;
        // var netTotal = subTotal + additionalCost - totalDiscount - additionalDiscount;
        return netTotal;
  },

  addNewPurchase : function () {
        $("#new-purchase-confirmation-modal").modal('toggle');
        var purchasedProductBatchList = inventoryPurchase.collectPurchasedProducts();
        if (purchasedProductBatchList.length == 0){
            showErrorDialogBox("Please select atleast one product for purchase!")
            return;
        }

        var supplierId = $("#purchase-supplier option:selected").val();
        var paymentMode = $("#purchase-payment-mode option:selected").val();
        // var paymentStatus = $("#purchase-payment-status option:selected").val();
        // var paymentAmount = $('#purchase-partial-payment-amount').val();
        var reference = $('#purchase-reference').val();
        var description = $('#purchase-description').val();
        var date = getDate($('#purchase-date').val());
        var additionalCostString = $("#additional-cost-input").val();
        var additionalCost = 0;
        if (additionalCostString != "") {
            additionalCost = parseFloat(additionalCostString);
        }

        var additionalDiscountString = $("#additional-discount-input").val();
        var additionalDiscount = 0;
        if (additionalDiscountString != "") {
            additionalDiscount = parseFloat(additionalDiscountString);
        }
        //TODO : add validation
        // var invalid = inventoryUtils.validations($("#purchase-order-body"));
        // if(invalid){
        //   return;
        // }
        var netTotal = inventoryPurchase.computeNetPurchaseAmount();
        if(netTotal < 0){
          showErrorDialogBox("Net Payable amount can't be less than 0");
          return;
        }
        var paymentAmount = netTotal;

        newPurchasePayload = {
            'supplierId': supplierId,
            'transactionMode' : paymentMode,
            // 'paymentStatus' : paymentStatus,
            'paymentAmount' : paymentAmount,
            'reference': reference,
            'transactionDate': date.getTime(),
            'description': description,
            'additionalCost' : additionalCost,
            'additionalDiscount' : additionalDiscount,
            'purchasedProductBatchList': purchasedProductBatchList
        }

        ajaxClient.post( "/inventory/purchase-transaction", {'newPurchasePayload' : JSON.stringify(newPurchasePayload)}, function(data) {
            $('#purchase-status-modal-container').html(data);
            $("#purchase-status-modal").modal({backdrop: 'static', keyboard: false});
        });
  },

  collectPurchasedProducts : function () {
      productBatchList = []
      $("tr.selected-product-row").each(function() {
          if($(this).hasClass(inventoryUtils.batchSelectRowClass)){
            return;
          }

          var rowId = inventoryUtils.getClassWithPrefix(this, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var productBatchDetailRow = $(this).next("tr.product-batch-details-row");

          var tokens = rowId.split(inventoryUtils.delimiter);
          var skuId = tokens[1].trim();
          var productName = null;
          var categoryId = null;
          var brandId = null;

          var productDescription = null;
          var productBatchDescription = null;

          if(skuId == ""){
            skuId = null;
            productName = $(this).find("input.new-product-name").val().trim();
            categoryId = productBatchDetailRow.find(".product-category").find(':selected').val().trim();
            brandId = productBatchDetailRow.find(".product-brand").find(':selected').val().trim();
            productDescription = productBatchDetailRow.find(".product-description").val().trim();
            productBatchDescription = productBatchDetailRow.find(".batch-description").val().trim();
          }

          var batchIdToken = tokens[2];
          var batchId = null;
          var batchName = null;
          if(batchIdToken.trim() == ""){
              batchName = $(this).find("input.purchase-product-batch-name").val().trim();
          }else {
              batchId = batchIdToken;
          }

          var quantity = parseFloat($(this).find("input.product-quantity").val());
          var purchasePrice = parseFloat($(this).find("input.purchase-price").val());
          var discount = 0;
          if ($(this).find("input.purchase-discount").val() != "") {
              discount = parseFloat($(this).find("input.purchase-discount").val());
          }
          var tax = 0;
          if ($(this).find("input.purchase-product-tax").val() != "") {
              tax = parseFloat($(this).find("input.purchase-product-tax").val());
          }
          var total = $(this).find("input.purchase-item-total").val();
          var product = {}
          var totalAmount = quantity*purchasePrice;
          var discountAmount = totalAmount * discount / 100;
          var taxAmount =  (totalAmount - discountAmount) * tax / 100;



          var sellingPrice = productBatchDetailRow.find(".product-batch-selling-price").val().trim();
          if(sellingPrice == ""){
            sellingPrice = null;
          }else{
            sellingPrice = parseFloat(sellingPrice);
          }
          var saleDiscount = productBatchDetailRow.find(".product-batch-discount").val().trim();
          if(saleDiscount == ""){
            saleDiscount = null;
          }else{
            saleDiscount = parseFloat(saleDiscount);
          }
          var saleDiscountInPercent = productBatchDetailRow.find('.discount-in-percent').is(":checked");
          var mrp = productBatchDetailRow.find(".product-batch-mrp").val().trim();
          if(mrp == ""){
            mrp = null;
          }else{
            mrp = parseFloat(mrp);
          }


          productBatchList.push({'skuId' : skuId, 'productName' : productName, 'categoryId' : categoryId, 'brandId' : brandId, 'batchId' : batchId,
          'batchName' : batchName, 'quantity' : quantity, 'totalPrice' : totalAmount,
           'totalDiscount' : discountAmount, 'totalTax' : taxAmount, 'sellingPrice' : sellingPrice, 'saleDiscount' : saleDiscount,
           'saleDiscountInPercent' : saleDiscountInPercent, 'mrp' : mrp, 'purchasePrice' : purchasePrice, 'productDescription' : productDescription,
           'productBatchDescription' : productBatchDescription
         });
      });

      return productBatchList;

  },

};

var inventorySale = {

  dataCache : {},

  saleType : "SALE",

  returnType : "RETURN",

  saleReturnType : "SALES_RETURN",

  loadNewSaleMenu : function (transactionType){
      ajaxClient.get("/inventory/new-sale-home/"+transactionType, function(data){
        $("#main-content").html(data);
        var inventoryPreferences = readJson("#inventory-preferences-json");
        var studentWalletPreferences = readJson("#student-wallet-preferences-json");
        inventorySale.dataCache = {'transactionType' : transactionType, 'inventoryPreferences' : inventoryPreferences, 'studentWalletPreferences' : studentWalletPreferences};
        if(transactionType == inventorySale.returnType){
          inventorySale.dataCache.customerType = 'SELLER';
        }

        inventorySale.init();

     });
  },

  loadNewSaleForm : function () {
      var transactionType = inventorySale.dataCache.transactionType;
      ajaxClient.get("/inventory/new-sale-form/"+transactionType, function(data){
        $("#sales-order-body").html(data);
        inventorySale.dataCache = {'transactionType' : inventorySale.dataCache.transactionType, 'inventoryPreferences' : inventorySale.dataCache.inventoryPreferences, 'studentWalletPreferences' : inventorySale.dataCache.studentWalletPreferences};
        if(transactionType == inventorySale.returnType){
          inventorySale.dataCache.customerType = 'SELLER';
        }
        inventorySale.init();
     });

  },

  init : function () {
    initDate(pastTransactionDays);
    liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId, inventoryUtils.liveSearchProductResultElementId, inventorySale.doneSaleProductTyping);
    liveSearchHandler.bindEvent('#sales\\.search-product-group', "#sales\\.search-product-result", inventorySale.doneProductGroupSaleSearchTyping);
    inventorySale.bindStudentSaleEvents();
    inventorySale.showProductGroupOption();
    $("#additional-cost-input").focus(function() {}).blur(function() {
        inventorySale.computeNetSaleAmount();
        // inventorySale.computeTotal($(this).parent().parent().parent());
    });

    $("#additional-discount-input").focus(function() {}).blur(function() {
        inventorySale.computeNetSaleAmount();
        // inventorySale.computeTotal($(this).parent().parent().parent());
    });
    initUpdateDateInId("sale-date",$('#sale-global-date').val());
    $('#sale-global-date').focus(function() {}).blur(function() {
      const selectedDate = $(this).val();
      initUpdateDateInId("sale-date", selectedDate);
    });
  },

  doneProductGroupSaleSearchTyping: function(resultArea) {
    var searchText = $("#sales\\.search-product-group").val();
    inventorySale.searchProductGroupEvent(searchText,resultArea, inventorySale.bindClickEventOnSaleGroupProductSearch);
  },

  searchProductGroupEvent: function(searchText, resultArea, triggerMethod){
    ajaxClient.get("/inventory/live-product-group-search?text=" + searchText, function(data) {
        $(resultArea).html(data);
        triggerMethod();
    });
  },

  bindClickEventOnSaleGroupProductSearch: function() {
      $("#live-search-product-group-results tbody tr th").on("click", function() {
          var productGroupJson = $(this).parent().find("td.product-group-json").text().trim();
          var productGroup = JSON.parse(productGroupJson);
          var groupId = productGroup.productGroupBasicInfo.groupId;
          var groupName = productGroup.productGroupBasicInfo.groupName;
          var availableQuantity = productGroup.availableQuantity;
          var totalSellingPrice = productGroup.totalSellingPrice;
          var discountedSellingPrice = productGroup.discountedTotalSellingPrice;
          var additionalDiscount = productGroup.productGroupBasicInfo.discount;

          if($("#" + groupId+".sale-product-group-item").length != 0) {
            showErrorDialogBox("Product Group : " + groupName + " already selected. Please add quantity in that. ");
            return;
          }
          $("#sales\\.search-group-row").before(" <tr class=\"sale-product-group-item\" id=\""+groupId+"\"> <td class=\"sale-product-group-name\" scope=\"row\" style = \"cursor: pointer;\"> "+groupName+" <p class=\"product-group-json-row\" style=\"display: none;\"> </p> </td> <td style=\"display:none\"><input type=\"number\" class=\"form-control form-control-sm group-sale-available-quantity\" placeholder=\"Available Quanity\"></td><td><input type=\"number\" class=\"form-control form-control-sm group-sale-quantity mandatory-table-field positive-number unavailable-field-input\" id=\"inputEmail4\" placeholder=\"Quanity\"> <p class=\"unavailable-field\" style=\"display: none;\"> <span style=\"color:#e65f76; \">Unavailable</span></p> </td><td><button type=\"button\" class=\"btn btn-primary btn-sm get-product-group-batch-details\">+Details</button></td><td><span class=\"badge badge-success\" style=\"width:55%\">Yes</span></td> <td><input type=\"number\" class=\"form-control form-control-sm group-sale-price mandatory-table-field positive-number\" placeholder=\"0\" readonly > </td> <td><input type=\"number\" class=\"form-control form-control-sm group-sale-discounted-price mandatory-table-field positive-number\" placeholder=\"0\" readonly > </td> <td><input type=\"number\" class=\"form-control form-control-sm group-sale-amount\" placeholder=\"0\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm group-sale-discount percentage-range\" placeholder=\"Discount\"></td> <td><input type=\"number\" class=\"form-control form-control-sm group-sale-total\" placeholder=\"0\" readonly></td> <td> <button type=\"button\" class=\"close delete-sale-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr> ");
          var productList = productGroup.productDataQuantityList || [];
          var rowsHtml = "";
          for (var i=0; i < productList.length; i++){
            var product = productList[i];
            var batchList = product.productGroupBatchDataQuantities || [];
            if(batchList.length > 0){
              for(var j =0; j < batchList.length; j++){
                var batch = batchList[j];
                  rowsHtml += inventoryUtils.getProductRow(
                    true,
                    batch,
                    product.productMetadata.skuId,
                    product.productMetadata.name,
                    batch.productBatchData.batchId,
                    batch.productBatchData.batchName,
                    true,
                    inventorySale.getRowColumnsGroup,
                    true,
                    inventorySale.getRowDetailsGroup,
                    batch.quantity,
                    "selected-product-group-row",
                  )
              }
            }
          }
          $("#"+groupId).after("<tr class=\"product-group-batch-details-row\" id=\""+groupId+"\"_product> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-group-batch-details-container \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> <th scope=\"col\" class=\"w-25\">Product Name*</th> <th scope=\"col\">Batch*</th> <th scope=\"col\"></th> <th scope=\"col\">Quantity*</th> <th scope=\"col\">Price*</th> <th scope=\"col\">Amount</th> <th scope=\"col\">Discount(%)</th> <th scope=\"col\">Tax(%)</th> <th scope=\"col\">Total (INR)</th> </tr> </thead> <tbody class="+groupId+"_table>"+ rowsHtml +"</tbody> </table> </div> </div> </td> </tr>");
          var availableQuantity = parseFloat(availableQuantity);
          var totalSellingPrice = parseFloat(totalSellingPrice);
          var discountedSellingPrice = parseFloat(discountedSellingPrice);

          $("#" + groupId).find(".group-sale-available-quantity").first().val(availableQuantity);
          $("#" + groupId).find(".group-sale-quantity").first().val(1);
          $("#" + groupId).find(".group-sale-price").first().val(totalSellingPrice);
          $("#" + groupId).find(".group-sale-discounted-price").first().val(discountedSellingPrice);
          $("#" + groupId).find(".group-sale-discount").first().val(additionalDiscount);
          $("#" + groupId).find(".group-sale-tax").first().val(0);
          $("#" + groupId).find("p.product-group-json-row").text(productGroupJson);


          $("#sales\\.search-product-group").val("");
          $("#sales\\.search-product-result").html("");
          inventorySale.deleteSalesEntry();
          inventorySale.bindGroupSalesCostComputeEvent();
          inventoryUtils.checkProductTransactionAvailability($("#" + groupId),".group-sale-quantity", ".group-sale-available-quantity");
          inventorySale.computeGroupSaleProductTotal($("#" + groupId));
          inventoryUtils.bindRemoveErrorDisplayEvent();
          inventoryUtils.productGroupInformation("sale-product-group-name");
          inventorySale.expandProductGroupDetails();
          inventorySale.bindSaleNewRowEvent("group");
          inventorySale.deleteProductEntry();
          inventoryUtils.bindExpandBatchButton();
      });
  },
  deleteProductEntry : function() {
      $(".delete-product-batch-group-row").off('click').on("click", function() {
          var parentRow = $(this).parent().parent();
          var rowId = inventoryUtils.getClassWithPrefix(parentRow, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var selectedProductRows = $(this).parent().parent().parent().attr("class");
          var skuId = rowId.split(inventoryUtils.delimiter)[1];
          var batchId = rowId.split(inventoryUtils.delimiter)[2];
          var groupId = selectedProductRows.split("_")[0];
          console.log("entered to delete");
          parentRow.next('tr.product-batch-details-row').remove();
          parentRow.remove();
          inventorySale.computeNetSaleAmount($("#" + groupId), groupId);
          // second row is collapse card
          if($(".selected-product-group-row."+skuId).length == 0){
            $(".selected-product-group-row."+skuId).remove();
          }

        if ($("tbody." + selectedProductRows + " tr").length === 0) {
          $("tr.sale-product-group-item#" + groupId).remove();
          $("tr.product-group-batch-details-row#" + groupId).remove();
        }
          inventoryUtils.resetAddNewProductBatchButtonState(skuId);
          //onDelete();
      });
  },
  expandProductGroupDetails : function(){
     $(".get-product-group-batch-details").off('click').on('click', function () {
      $(this).closest("tr").next("tr").find('.product-group-batch-details-container').collapse('toggle');
    });
  },
  getRowColumnsGroup : function (new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue) {
    var sellingPrice = 0;
    var discountVal = 0;
    if(inventorySale.dataCache.transactionType == inventorySale.returnType){
      if(batchDetails != null && batchDetails.productBatchData.purchasePrice != null){
        sellingPrice = batchDetails.productBatch.purchasePrice;
      }else{
        sellingPrice = 0;
      }

      discountVal = 0;
    }
    else if(batchDetails != null){
      sellingPrice = batchDetails.productBatchData.sellingPrice;
      var discount = batchDetails.discount;
      var discountInPercent = true;
      if(discount != null){
        if(discountInPercent){
          discountVal = discount;
        }else if(sellingPrice != null && sellingPrice > 0){
          discountVal = discount * 100/sellingPrice;
        }
      }
    }
    var totalAmount = sellingPrice * quantityValue;
    var netAmount = inventorySale.computeProductGroupTotal(quantityValue, sellingPrice, discountVal, 0);

        return ["<td scope=\"row\"> "+productName+" </td>", "<td style=\"width:15%;\">"+batchColumn+"</td>", "<td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\" value=\""+quantityValue+"\" " + readonly+"> </td> "," <td><input type=\"number\" class=\"form-control form-control-sm sale-price mandatory-table-field positive-number\" placeholder=\"0\" value=\""+sellingPrice+"\" readonly></td> ", " <td><input type=\"number\" class=\"form-control form-control-sm sale-item-amount\" placeholder=\"0\" value=\""+totalAmount+"\" readonly></td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-discount percentage-range\" placeholder=\"Discount\" value=\""+discountVal+"\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm purchase-product-tax percentage-range\" placeholder=\"Tax\" readonly> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-item-total\" placeholder=\"0\" value=\""+netAmount+"\" readonly></td>"];  },

  getRowDetailsGroup : function (new_product, batchDetails) {
    var mrp = "";
    var purchasePrice = "";
    var sellingPrice = "";
    var batchDescription = "";
    if(batchDetails != null){
      mrp = batchDetails.productBatchData.mrp;
      purchasePrice = batchDetails.productBatchData.purchasePrice;
      sellingPrice = batchDetails.productBatchData.sellingPrice;
      batchDescription = batchDetails.productBatchData.description;
      if(batchDescription == null){
        batchDescription = "";
      }
    }

    return "<tr class=\"product-batch-details-row\"> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-batch-details-container \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> <th scope=\"col\">MRP</th> <th scope=\"col\">Purchase Price</th> <th scope=\"col\">Selling Price</th> <th scope=\"col\">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-mrp\"  placeholder=\"0\" value=\""+mrp+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-purchase-price\"  placeholder=\"0\" value=\""+purchasePrice+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-sale-price\"  placeholder=\"0\" value=\""+sellingPrice+"\" readonly></td> <td><textarea class=\"form-control batch-description\" rows=\"1\" placeholder=\"Batch Description\" readonly >"+batchDescription+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>";
  },

  computeProductGroupTotal : function (quantity, price, discount, tax) {

      var totalAmount = quantity*price;

      var netAmount = (totalAmount + totalAmount*tax/100) - (totalAmount + totalAmount*tax/100) * discount / 100;
      
      return netAmount;
  },

  deleteSalesEntry: function() {
    $(".delete-sale-row").click(function() {
        var groupRow = $(this).closest("tr.sale-product-group-item");
        if (!groupRow.length) return;

        var groupId = groupRow.attr("id");  // get groupId from the id attribute

        // Remove the group row itself
        groupRow.remove();

        // Remove the matching product-group-batch-details-row for this groupId
        $("tr.product-group-batch-details-row#" + groupId).remove();
        inventorySale.computeNetSaleAmount();
    });
  },

  bindGroupSalesCostComputeEvent: function() {
    $(".group-sale-quantity").focus(function() {}).blur(function() {
        inventoryUtils.checkProductTransactionAvailability($(this).parent().parent(),".group-sale-quantity", ".group-sale-available-quantity");
        inventorySale.computeGroupSaleProductTotal($(this).parent().parent());
    });

    $(".group-sale-discount").focus(function() {}).blur(function() {
        inventorySale.computeGroupSaleProductTotal($(this).parent().parent());
    });

    $(".group-sale-tax").focus(function() {}).blur(function() {
        inventorySale.computeGroupSaleProductTotal($(this).parent().parent());
    });

    // inventorySale.bindAdditionalChargeComputeEvent();
  },

  computeGroupSaleProductTotal: function(current) {
    var quantityToSellString = $(current).find(".group-sale-quantity").first().val();
    var quantityToSell = 0;
    if (quantityToSellString) {
        quantityToSell = parseFloat(quantityToSellString);
    }
    var priceString = $(current).find(".group-sale-price").first().val();
    var price = 0;
    if (priceString) {
        price = parseFloat(priceString);
    }
    var discountedPriceString = $(current).find(".group-sale-discounted-price").first().val();
    var discountedPrice = 0;
    if (discountedPriceString) {
        discountedPrice = parseFloat(discountedPriceString);
    }
    var discount_string = parseFloat($(current).first().find(".group-sale-discount").first().val());
    var discount = 0;
    if (discount_string) {
        discount = parseFloat(discount_string);
    }

    var tax_string = parseFloat($(current).first().find(".group-sale-tax").first().val());
    var tax = 0;
    if (tax_string) {
        tax = parseFloat(tax_string);
    }
    var totalMRP = price * quantityToSell;
    var totalDiscountedPrice = discountedPrice * quantityToSell;
    var netAmount = totalDiscountedPrice + totalMRP * tax / 100 - totalDiscountedPrice * discount / 100;
    $(current).find(".group-sale-amount").first().val(totalDiscountedPrice.toFixed(2));
    $(current).find(".group-sale-total").first().val(netAmount.toFixed(2));
    inventorySale.computeNetSaleAmount();
  },


  doneSaleProductTyping : function(resultArea) {
      var searchText = $(inventoryUtils.liveSearchProductElementId).val();
      ajaxClient.get("/inventory/live-stock-search?text=" + searchText, function(data) {
          $(resultArea).html(data);
          inventoryUtils.bindClickEventOnSearchProduct(searchText, inventorySale.dataCache, inventorySale.getBatchSelectColumn,
            inventorySale.getRowColumns, true, inventorySale.getRowDetails, inventorySale.bindSaleNewRowEvent, false, inventorySale.onDelete);
      });
  },

  getBatchSelectColumn : function (options) {
    return "<select class=\"custom-select product-batch-select\">"+options+"</select>";
  },

  onDelete : function () {
    inventorySale.computeNetSaleAmount();
  },

  bindSaleNewRowEvent : function (current = null) {
    inventorySale.bindCostComputeEvent(current);
  },

  getRowColumns : function (new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue) {
    var sellingPrice = 0;
    var discountVal = 0;
    if(inventorySale.dataCache.transactionType == inventorySale.returnType){
      if(batchDetails != null && batchDetails.productBatch.purchasePrice != null){
        sellingPrice = batchDetails.productBatch.purchasePrice;
      }else{
        sellingPrice = 0;
      }

      discountVal = 0;
    }
    else if(batchDetails != null){
      sellingPrice = batchDetails.productBatch.sellingPrice;
      var discount = batchDetails.productBatch.discount;
      var discountInPercent = batchDetails.productBatch.discountInPercent;
      if(discount != null){
        if(discountInPercent){
          discountVal = discount;
        }else if(sellingPrice != null && sellingPrice > 0){
          discountVal = discount * 100/sellingPrice;
        }
      }
    }

        return ["<td scope=\"row\"> "+productName+" </td>", "<td style=\"width:15%;\">"+batchColumn+"</td>", "<td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\" "+readonly+"> </td> "," <td><input type=\"number\" class=\"form-control form-control-sm sale-price mandatory-table-field positive-number\" placeholder=\"0\" value=\""+sellingPrice+"\" "+readonly+"></td> ", " <td><input type=\"number\" class=\"form-control form-control-sm sale-item-amount\" placeholder=\"0\" readonly></td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-discount percentage-range\" placeholder=\"Discount\" value=\""+discountVal+"\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm purchase-product-tax percentage-range\" placeholder=\"Tax\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-item-total\" placeholder=\"0\" readonly></td>"];  },

  getRowDetails : function (new_product, batchDetails) {
    var mrp = "";
    var purchasePrice = "";
    var sellingPrice = "";
    var batchDescription = "";
    if(batchDetails != null){
      mrp = batchDetails.productBatch.mrp;
      purchasePrice = batchDetails.productBatch.purchasePrice;
      sellingPrice = batchDetails.productBatch.sellingPrice;
      batchDescription = batchDetails.productBatch.description;
      if(batchDescription == null){
        batchDescription = "";
      }
    }

    return "<tr class=\"product-batch-details-row\"> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-batch-details-container \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> <th scope=\"col\">MRP</th> <th scope=\"col\">Purchase Price</th> <th scope=\"col\">Selling Price</th> <th scope=\"col\">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-mrp\"  placeholder=\"0\" value=\""+mrp+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-purchase-price\"  placeholder=\"0\" value=\""+purchasePrice+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-sale-price\"  placeholder=\"0\" value=\""+sellingPrice+"\" readonly></td> <td><textarea class=\"form-control batch-description\" rows=\"1\" placeholder=\"Batch Description\" readonly >"+batchDescription+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>";
  },

  bindCostComputeEvent : function (current) {
      $(".product-quantity").focus(function() {}).blur(function() {
          var groupIdCurrent = null;
          var groupId = null;
          if(current != null){
            var groupIdClass = $(this).parent().parent().parent().attr("class");
            groupId = groupIdClass.split("_")[0];
            groupIdCurrent = $("#" + groupId);
          }
         inventorySale.computeTotal($(this).parent().parent(), groupIdCurrent, groupId);
      });

      $(".sale-price").focus(function() {}).blur(function() {
          var groupIdCurrent = null
          if(current != null){
            var groupIdClass = $(this).parent().parent().parent().attr("class");
            var groupId = groupIdClass.split("_")[0];
            groupIdCurrent = $("#" + groupId);
          }
         inventorySale.computeTotal($(this).parent().parent(), groupIdCurrent, groupId);
      });

      $(".sale-discount").focus(function() {}).blur(function() {
          var groupIdCurrent = null
          if(current != null){
            var groupIdClass = $(this).parent().parent().parent().attr("class");
            var groupId = groupIdClass.split("_")[0];
            groupIdCurrent = $("#" + groupId);
          }
         inventorySale.computeTotal($(this).parent().parent(), groupIdCurrent, groupId);
      });

      $(".purchase-product-tax").focus(function() {}).blur(function() {
          var groupIdCurrent = null
          if(current != null){
            var groupIdClass = $(this).parent().parent().parent().attr("class");
            var groupId = groupIdClass.split("_")[0];
            groupIdCurrent = $("#" + groupId);
          }
         inventorySale.computeTotal($(this).parent().parent(), groupIdCurrent, groupId);
      });

      // $("#additional-cost-input").focus(function() {}).blur(function() {
      //     inventorySale.computeTotal($(this).parent().parent().parent());
      // });

      // $("#additional-discount-input").focus(function() {}).blur(function() {
      //     inventorySale.computeTotal($(this).parent().parent().parent());
      // });
  },

  computeTotal : function (current, groupCurrent = null, groupId = null) {

      var quanityString = $(current).find(".product-quantity").first().val();
      var quantity = 0;
      if (quanityString) {
          quantity = parseFloat(quanityString);
      }

      var priceString = $(current).find(".sale-price").first().val();
      var price = 0;
      if (priceString) {
          price = parseFloat(priceString);
      }
      var discountString = parseFloat($(current).first().find(".sale-discount").first().val());
      var discount = 0;
      if (discountString) {
          discount = parseFloat(discountString);
      }

      var taxString = parseFloat($(current).first().find(".purchase-product-tax").first().val());
      var tax = 0;
      if (taxString) {
          tax = parseFloat(taxString);
      }

      var totalAmount = quantity*price;

      var netAmount = (totalAmount + totalAmount*tax/100) - (totalAmount + totalAmount*tax/100) * discount / 100;
      // var netAmount = totalAmount - totalAmount * discount / 100;
      $(current).find(".sale-item-amount").first().val(totalAmount.toFixed(2));
      $(current).find(".sale-item-total").first().val(netAmount.toFixed(2));
      inventorySale.computeNetSaleAmount(groupCurrent, groupId);
  },

  computeNetSaleAmount : function (groupCurrent = null, groupId = null){
        var totalDiscount = 0;
        var totalTax = 0;
        var subTotal = 0;
        $("tr.selected-product-row").each(function() {
            var quantity = $(this).find("input.product-quantity").val();
            if(quantity == ""){
              quantity = 0;
            }
            var purchasePrice = $(this).find("input.sale-price").val();
            if(purchasePrice == ""){
              purchasePrice = 0;
            }
            var discount = $(this).find("input.sale-discount").val();
            if(discount == ""){
              discount = 0;
            }
            var tax = $(this).find("input.purchase-product-tax").val();
            if(tax == "" || typeof tax === "undefined"){
              tax = 0;
              }
            var totalAmount = parseFloat(purchasePrice) * parseFloat(quantity);
            var discountAmount =  totalAmount * parseFloat(discount) / 100;
            var taxAmount =  (totalAmount - discountAmount) * parseFloat(tax) / 100;

            totalDiscount += discountAmount;
            totalTax += taxAmount;
            subTotal += totalAmount;
        });
         if(groupCurrent != null){
          var totalProductDiscount = 0;
          var totalProductTax = 0;
          var subProductTotal = 0;
            $("tr.selected-product-group-row").each(function() {
              if(groupId === $(this).parent().attr("class").split("_")[0]){
                console.log("entered");
              var quantity = $(this).find("input.product-quantity").val();
              if(quantity == ""){
                quantity = 0;
              }
              var purchasePrice = $(this).find("input.sale-price").val();
              if(purchasePrice == ""){
                purchasePrice = 0;
              }
              var discount = $(this).find("input.sale-discount").val();
              if(discount == ""){
                discount = 0;
              }
              var tax = $(this).find("input.purchase-product-tax").val();
              if(tax == "" || typeof tax === "undefined"){
                tax = 0;
                }
              var totalAmount = parseFloat(purchasePrice) * parseFloat(quantity);
              var discountAmount =  totalAmount * parseFloat(discount) / 100;
              var taxAmount =  (totalAmount - discountAmount) * parseFloat(tax) / 100;

              totalProductDiscount += discountAmount;
              totalProductTax += taxAmount;
              subProductTotal += totalAmount;
              }
          });
          var totalAmountAfterDiscountWithTax = subProductTotal - totalProductDiscount;
          $(groupCurrent).find(".group-sale-price").first().val(subProductTotal);
          $(groupCurrent).find(".group-sale-discounted-price").first().val(totalAmountAfterDiscountWithTax);
          inventorySale.computeGroupSaleProductTotal(groupCurrent);
          return;
        }
        if(!inventorySale.isReturnTransaction() && $('#sell-product-group-check').is(":checked")){
          $("tr.sale-product-group-item").each(function() {
              var quantity = $(this).find("input.group-sale-quantity").val();
              if(quantity == ""){
                quantity = "0";
              }
              var mrp = $(this).find("input.group-sale-price").val();
              var discountedPrice = $(this).find("input.group-sale-discounted-price").val();
              var discount = $(this).find("input.group-sale-discount").val();
              if(discount == ""){
                discount = "0";
              }
              var tax = "0";
              // var tax = $(this).find("input.group-sale-tax").val();
              // if(tax == ""){
              //   tax = "0";
              // }

              var totalMRP = parseFloat(mrp) * parseFloat(quantity);
              var discountedPriceTotal = parseFloat(discountedPrice) * parseFloat(quantity);
              var discountAmount = (totalMRP - discountedPriceTotal) + discountedPriceTotal * parseFloat(discount) / 100;
              var taxAmount =  totalMRP * parseFloat(tax) / 100;

              totalDiscount += discountAmount;
              totalTax += taxAmount;
              subTotal += totalMRP;
          });
        }

        var additionalCostString = $("#additional-cost-input").val();
        var additionalCost = 0;
        if (additionalCostString != "") {
            additionalCost = parseFloat(additionalCostString);
        }

        var additionalDiscountString = $("#additional-discount-input").val();
        var additionalDiscount = 0;
        if (additionalDiscountString != "") {
            additionalDiscount = parseFloat(additionalDiscountString);
        }

        var netReturnedProductAmount = 0;
        if($("#net-returned-product-amount").length > 0){
          var netReturnedProductAmountString = $("#net-returned-product-amount").text();
          if (netReturnedProductAmountString != "") {
            netReturnedProductAmount = parseFloat(netReturnedProductAmountString);
          }
        }
        $("#sub-total").text(subTotal.toFixed(2));
        $("#total-discount").text(totalDiscount.toFixed(2));
        $("#total-tax").text(totalTax.toFixed(2));
        $("#additional-cost").text(additionalCost.toFixed(2));
        $("#additional-discount").text(additionalDiscount.toFixed(2));

        $("#net-total").text((subTotal + additionalCost - totalDiscount - additionalDiscount + totalTax - netReturnedProductAmount).toFixed(2));
        // $("#net-total").text((subTotal + additionalCost - totalDiscount - additionalDiscount).toFixed(2));
        var netTotal = subTotal + additionalCost - totalDiscount - additionalDiscount + totalTax - netReturnedProductAmount;
        // var netTotal = subTotal + additionalCost - totalDiscount - additionalDiscount;
        return netTotal;
  },

  showProductGroupOption: function(triggerMethod){
    $('.show-product-group-check').change(function() {
         // triggerMethod();
         if($(this).is(":checked")) {
             // $(this).attr("checked", returnVal);
             $('.product-group-transation-area').attr("style", "display:block");
         }
         else{
            $('.product-group-transation-area').attr("style", "display:none");
         }
     });
  },

  userTypeSelectionAction : function (customerType, skipStudentSearchToggle = false) {
    inventorySale.dataCache.customerType = customerType;
    $(".customer-name-input").attr("style", "display:none");
    $(".sale-confirm-modal-content").attr("style", "display:none");
    $("#sale-payment-mode").val("");
    if(inventorySale.getInventoryUserType() == "STUDENT"){
      if(!skipStudentSearchToggle){
        $("#inventory-student-search-modal").modal('toggle');
      }
      $("#student-name-input-div").attr("style", "display:block");
      // $("#student-wallet-payment-mode-option").attr("style", "display:block");

      if(inventorySale.dataCache.inventoryPreferences.allowWalletUsage){
        $(".sale-confirm-modal-content#sale-confirm-modal-student-wallet-usage-section").attr("style", "display:block");
      }
    }else{
      inventorySale.dataCache.selectedStudent= null;
      $("#customer-name-input-div").attr("style", "display:block");
      $("#student-wallet-amount-display-div").attr("style", "display:none");
      $("#student-wallet-amount-display").html("");
      // $("#student-wallet-payment-mode-option").attr("style", "display:none");

      $(".sale-confirm-modal-content#sale-confirm-modal-without-wallet-usage").attr("style", "display:block");
    }
  },

  bindStudentSaleEvents : function () {
    var resultArea = "#inventory-student-search-result";
    $('select#customer-type').off('change').on('change', function(){
      inventorySale.userTypeSelectionAction($(this).val());
    });

    $('#edit-sutdent-entry').off('click').on('click', function(){
        if(inventorySale.getInventoryUserType() == "STUDENT"){
          $("#inventory-student-search-modal").modal('toggle');
        }
    });

    $('#confirm-modal-use-wallet-amount-check').off('click').on('click', function(){
        if(inventorySale.dataCache.selectedStudent == null){
          return;
        }
        var netPaybleAmount = getFloatValue($("#net-total").text());
        if($(this).is(":checked")) {
           $("#confirm-modal-used-wallet-amount").parent().attr("style","display:block");
           var creditAllowed = false;
           if(inventorySale.dataCache.inventoryPreferences.allowWalletBasedCredit
             && inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit != null){

                $("#confirm-modal-credit-amount").parent().attr("style","display:block");
                $("#credit-amount-label").text("Credit Amount ("+inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit+")")
                creditAllowed = true;
           }
            var walletAmount = inventorySale.dataCache.selectedStudent.walletAmount < 0 ? 0 : inventorySale.dataCache.selectedStudent.walletAmount;
            if(netPaybleAmount < walletAmount){
              $("#confirm-modal-used-wallet-amount").val(netPaybleAmount);
              $("#confirm-modal-paid-amount").val(0);
              $("#confirm-modal-paid-amount").prop("readonly", true);

              $("#confirm-modal-credit-amount").val(0);
              $("#confirm-modal-credit-amount").prop("readonly", true);

            }
            else{
              $("#confirm-modal-used-wallet-amount").val(walletAmount);
              if(!creditAllowed){
                  $("#confirm-modal-paid-amount").val(netPaybleAmount - walletAmount);
                  $("#confirm-modal-paid-amount").prop("readonly", true);
                  $("#confirm-modal-credit-amount").val(0);
                  $("#confirm-modal-credit-amount").prop("readonly", true);
              }else{
                $("#confirm-modal-paid-amount").val(0);
                $("#confirm-modal-paid-amount").prop("readonly", false);
                $("#confirm-modal-credit-amount").val(netPaybleAmount - walletAmount);
                $("#confirm-modal-credit-amount").prop("readonly", false);
              }
            }
        }else{
          $("#confirm-modal-used-wallet-amount").parent().attr("style","display:none");
          $("#confirm-modal-credit-amount").parent().attr("style","display:none");
          $("#confirm-modal-paid-amount").val(netPaybleAmount);
          $("#confirm-modal-paid-amount").prop("readonly", true);
        }
    });



    $("#inventory-student-search-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          inventorySale.doneStudentSearchTyping(resultArea);
      }
    });

    liveSearchHandler.bindEvent('#inventory-student-search-text', resultArea, inventorySale.doneStudentSearchTyping);

    // $("#new-sale-confirm-modal").keypress((e) => {
    //   if (e.which === 13) {
    //       $("#sales\\.confirmation_add_order").click();
    //   }
    // });
  },

  doneStudentSearchTyping: function(resultArea) {
    var searchText = $('#inventory-student-search-text').val().trim();
    var status = "ENROLLED";
    ajaxClient.get("/inventory/student-live-search?searchText=" + searchText+"&statusList="+status, function(data) {
        $(resultArea).html(data);
        studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea, inventorySale.inventoryStudentSearchClickAction);
    });
  },

  getInventoryUserType : function () {
    var inventoryUserType = inventorySale.dataCache.customerType;
    if(inventoryUserType == null || inventoryUserType.trim() == ""){
      return "OTHER";
    }
    return inventoryUserType;
  },

  inventoryStudentSearchClickAction : function (studentId, skipSearchModalToggle = false) {
    inventorySale.dataCache.selectedStudentId = studentId;
    var includeWalletAmount = inventorySale.dataCache.inventoryPreferences.allowWalletUsage

    ajaxClient.get("/inventory/latest-session-student/"+studentId+"?includeWalletAmount="+includeWalletAmount, function(data) {
        $("#selected-student-data").html(data);
        var student = readJson("#student-data-entry-json");
        inventorySale.dataCache.selectedStudent= student;
        var isSaleRestricted = false; //
        if(student.studentTaggedDetailsList && student.studentTaggedDetailsList.length > 0){
          student.studentTaggedDetailsList.forEach(details => {
            if (details.taggedActions === "INVENTORY_SALE_RESTRICTED") {
                isSaleRestricted = true;
            }
          });
        }
        var selectedStudentDisplayName = student.studentBasicInfo.name + " ("+ student.studentBasicInfo.admissionNumber + ")";

        if(!skipSearchModalToggle){
          $("#inventory-student-search-modal").modal('toggle');
        }

        $("#edit-sutdent-entry-name").val(selectedStudentDisplayName);
        if(isSaleRestricted){
          $('button[data-target="#new-sale-confirm-modal"]').attr("disabled", true);
          $('#inventory-sale-tag').text('Inventory Sale Restricted')
        } else {
          $('button[data-target="#new-sale-confirm-modal"]').attr("disabled", false);
          $('#inventory-sale-tag').text('')
        }
        if(includeWalletAmount){
            $("#student-wallet-amount-display-div").attr("style", "display:block");
            $("#student-wallet-amount-display").html(student.walletAmount);
            $("#confirm-modal-wallet-amount").text(student.walletAmount);
        }
    });
  },

  updateConfirmModal : function () {
      var inventoryUserType = inventorySale.getInventoryUserType();
      var netPaybleAmount = getFloatValue($("#net-total").text());
      $("#confirm-modal-total-sale-payble-amount").text(netPaybleAmount);

      inventorySale.saleOrderConfirmModalResetState();
      $("#confirm-modal-paid-amount").val(netPaybleAmount);

      if(inventoryUserType == "STUDENT" && inventorySale.dataCache.inventoryPreferences.allowWalletUsage){
        $("#sale-confirm-modal-student-wallet-usage-section").attr("style","display:block;");
        $("#sale-confirm-modal-without-wallet-usage").attr("style","display:none;");
        if(netPaybleAmount <= 0){
          $("#with-wallet-return-amount-container").attr("style","display:block;");
          $("#with-wallet-collect-amount-container").attr("style","display:none;");
        } else {
          $("#with-wallet-return-amount-container").attr("style","display:none;");
          $("#with-wallet-collect-amount-container").attr("style","display:block;");
        }

      } else {
        $("#sale-confirm-modal-student-wallet-usage-section").attr("style","display:none;");
        $("#sale-confirm-modal-without-wallet-usage").attr("style","display:block;");
        if(netPaybleAmount <= 0){
          $("#without-wallet-return-payment-mode-selection").attr("style","display:block;");
          $("#without-wallet-collect-payment-mode-selection").attr("style","display:none;");
        } else {
          $("#without-wallet-return-payment-mode-selection").attr("style","display:none;");
          $("#without-wallet-collect-payment-mode-selection").attr("style","display:block;");
        }
      }


  },

  saleOrderConfirmModalResetState : function () {
    var netPaybleAmount = getFloatValue($("#net-total").text());
    $("#confirm-modal-use-wallet-amount-check").prop("checked",false);
    $("#confirm-modal-used-wallet-amount").parent().attr("style","display:none");
    $("#confirm-modal-credit-amount").parent().attr("style","display:none");
    $("#confirm-modal-paid-amount").val(netPaybleAmount);
    $("#confirm-modal-paid-amount").prop("readonly", true);
  },

  toggleNewSaleConfirmModal : function () {
    $("#new-sale-confirm-modal").modal('toggle');
  },

  isReturnTransaction : function () {
    return inventorySale.dataCache.transactionType == inventorySale.returnType;
  },

  addNewSale : function () {
        $("#new-sale-confirm-modal").modal('toggle');
        var selectedProductBatchList = inventorySale.collectSelectedProducts();
        var productGroups = [];
        /* if(!inventorySale.isReturnTransaction()){
            productGroups = inventorySale.collectProductGroupSalesOrderFormValues();
        } */

        if (selectedProductBatchList.length == 0 && productGroups.length == 0){
            showErrorDialogBox("Please select atleast one product or product group for sale!")
            return;
        }

        var inventoryUserType = inventorySale.getInventoryUserType();
        var customer = null;
        if(inventorySale.isReturnTransaction()){
          customer = $("#return-supplier option:selected").val();
          if(customer == null || customer.trim() == ""){
              inventorySale.toggleNewSaleConfirmModal();
              showErrorDialogBox("Select supplier");
              return;
          }
        }else{
          if(inventoryUserType == "STUDENT"){
              customer = inventorySale.dataCache.selectedStudentId;
          }else{
              customer = $("#sale-customer-name").val();
          }
          if(customer == null || customer.trim() == ""){
              inventorySale.toggleNewSaleConfirmModal();
              showErrorDialogBox("Invalid Customer");
              return;
          }
        }

        //
        // var isValid = true;
        // // var isValid = validateSaleOrderForm();
        //
        // var invalid = validations($("#sales-order-body"));
        // if(invalid || !isValid){
        //   inventorySale.toggleNewSaleConfirmModal();
        //   return;
        // }

        var netTotal = inventorySale.computeNetSaleAmount();
        if(netTotal < 0){
          inventorySale.toggleNewSaleConfirmModal();
          showErrorDialogBox("Net Payable amount can't be less than 0");
          return;
        }

        var netTotal = inventorySale.computeNetSaleAmount();
        if(netTotal < 0){
          inventorySale.toggleNewSaleConfirmModal();
          showErrorDialogBox("Net payable amount can't be less than 0");
          return;
        }

        var newSalePayload = inventorySale.getNewSalePayload(customer, selectedProductBatchList, productGroups, netTotal);

        console.log(newSalePayload);

        if (newSalePayload['useWallet'] && (newSalePayload['paidAmount'] == null || newSalePayload['paidAmount'] == 0)) {
          newSalePayload['transactionMode'] = "WALLET";
        }

        if(newSalePayload['transactionMode'] === undefined || newSalePayload['transactionMode'] === null || newSalePayload['transactionMode'] === "") {
          alert("Please select a payment mode!");
          return;
        }
        
        ajaxClient.post( "/inventory/sale-transaction", {'newSalePayload' : JSON.stringify(newSalePayload)}, function(data) {
            $('#sale-status-modal-container').html(data);
            $("#sale-status-modal").modal({backdrop: 'static', keyboard: false});
        });
  },

  getNewSalePayload : function (customer, selectedProductBatchList, productGroups, netTotal) {
        var inventoryUserType = inventorySale.getInventoryUserType();
        // var paymentMode = $("#sale-payment-mode option:selected").val();
        var paymentMode = null;
        if(inventorySale.dataCache.transactionType == "SALE"){
          if(inventoryUserType == "STUDENT" && inventorySale.dataCache.inventoryPreferences.allowWalletUsage){
            if(netTotal <= 0){
              paymentMode = $("#with-wallet-return-payment-mode-selection").find(".sale-payment-mode option:selected").val();
            } else {
              paymentMode = $("#with-wallet-collect-payment-mode-selection").find(".sale-payment-mode option:selected").val();
            }
          } else {
            if(netTotal <= 0){
              paymentMode = $("#without-wallet-return-payment-mode-selection").find(".sale-payment-mode option:selected").val();
            } else {
              paymentMode = $("#without-wallet-collect-payment-mode-selection").find(".sale-payment-mode option:selected").val();
            }
          }
        } else {
            paymentMode = $("#sale-payment-mode option:selected").val();
        }



        var email = $('#sale-customer-email').val();
        var description = $('#sale-description').val();
        var date = getDate($('#sale-date').val());
        var reference = $('#sale-reference-number').val();

        var additionalCostString = $("#additional-cost-input").val();
        var additionalCost = 0;
        if (additionalCostString != "") {
            additionalCost = parseFloat(additionalCostString);
        }

        var additionalDiscountString = $("#additional-discount-input").val();
        var additionalDiscount = 0;
        if (additionalDiscountString != "") {
            additionalDiscount = parseFloat(additionalDiscountString);
        }

        var useWallet = false;
        var usedWalletAmount = 0;
        var walletCreditAmount = 0;
        var paidAmount = getFloatValue($("#net-total").text());



        if(inventoryUserType == "STUDENT" && inventorySale.dataCache.inventoryPreferences.allowWalletUsage && $("#confirm-modal-use-wallet-amount-check").is(":checked")){
              useWallet = true;
              usedWalletAmount = getFloatValue($("#confirm-modal-used-wallet-amount").val());
              paidAmount = getFloatValue($("#confirm-modal-paid-amount").val());
              if(inventorySale.dataCache.inventoryPreferences.allowWalletBasedCredit
                && inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit != null){
                  walletCreditAmount = getFloatValue($("#confirm-modal-credit-amount").val());

                  if(walletCreditAmount > 0){
                    if(inventorySale.dataCache.selectedStudent.walletAmount - usedWalletAmount > 0){
                      inventorySale.toggleNewSaleConfirmModal();
                      showErrorDialogBox("Use available wallet amount instead of giving credit amount");
                      return;
                    }

                    if(inventorySale.dataCache.selectedStudent.walletAmount - usedWalletAmount - walletCreditAmount < -inventorySale.dataCache.studentWalletPreferences.walletBasedCreditLimit){
                      inventorySale.toggleNewSaleConfirmModal();
                      showErrorDialogBox("Cannot give more credit then allowed max limit");
                      return;
                    }
                  }
              }
        }

        var newSalePayload = {
            'inventoryUserType' : inventoryUserType,
            'inventoryTransactionType' : inventorySale.dataCache.transactionType,
            'transactionMode' : paymentMode,
            'purchasedBy': customer,
            'reference': reference,
            'email': email,
            'transactionDate': date.getTime(),
            'description': description,
            'additionalCost' : additionalCost,
            'additionalDiscount' : additionalDiscount,
            'useWallet' : useWallet,
            'usedWalletAmount' : usedWalletAmount,
            'walletCreditAmount' : walletCreditAmount,
            'paidAmount' : paidAmount,
            'saleProductPayloadList': selectedProductBatchList,
            'saleProductGroupPayloadList': productGroups
        }

        return newSalePayload;

  },

  collectProductGroupSalesOrderFormValues: function() {
    productGroups = []
    // var isValid = validateSaleOrderForm();
    // if (!isValid) {
    //     return products;
    // }
    if(inventorySale.isReturnTransaction() || !$('#sell-product-group-check').is(":checked")){
      return productGroups;
    }
    $("tr.sale-product-group-item").each(function() {
        var groupId = $(this).attr("id");
        var quantity = $(this).find("input.group-sale-quantity").val();
        var mrp = $(this).find("input.group-sale-price").val();
        var discountedPrice = $(this).find("input.group-sale-discounted-price").val();
        var discount = $(this).find("input.group-sale-discount").val();
        var tax = "0";
        // var tax = $(this).find("input.group-sale-tax").val();
        // var total = $(this).find("input.sale_product_total").val();
        var productGroup= {}
        // Discount is applied on top of discounted amount of group.
        // Tax is applied on MRP
        var totalAmount = parseFloat(discountedPrice) * parseFloat(quantity);
        var totalMRP = parseFloat(mrp) * parseFloat(quantity);
        var discountAmount =  totalAmount * parseFloat(discount) / 100;
        var taxAmount =  totalMRP * parseFloat(tax) / 100;
        productGroup['groupId'] = groupId;
        productGroup['quantity'] = quantity;
        productGroup['totalPrice'] = totalAmount;
        productGroup['totalDiscount'] = discountAmount;
        productGroup['totalTax'] = taxAmount;
        // productGroup['total'] = totalAmount + taxAmount - discountAmount;

        productGroups.push(productGroup);
    });
    return productGroups;

 },

  collectSelectedProducts : function () {
      productBatchList = []
      $("tr.selected-product-row").each(function() {
          if($(this).hasClass(inventoryUtils.batchSelectRowClass)){
            return;
          }

          var rowId = inventoryUtils.getClassWithPrefix(this, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var tokens = rowId.split(inventoryUtils.delimiter);
          var skuId = tokens[1].trim();
          if(skuId == ""){
            return;
          }

          var batchId = tokens[2].trim();
          if(batchId == ""){
              return;
          }

          var quantity = parseFloat($(this).find("input.product-quantity").val());
          var salePrice = parseFloat($(this).find("input.sale-price").val());
          var discount = 0;
          if ($(this).find("input.sale-discount").val() != "") {
              discount = parseFloat($(this).find("input.sale-discount").val());
          }
          var tax = 0;
          if ($(this).find("input.purchase-product-tax").val() != "") {
              tax = parseFloat($(this).find("input.purchase-product-tax").val());
          }
          var total = $(this).find("input.sale-item-total").val();
          var product = {}
          var totalAmount = quantity * salePrice;
          var discountAmount = totalAmount * discount / 100;
          var taxAmount =  (totalAmount - discountAmount) * tax / 100;

          productBatchList.push({'skuId' : skuId, 'batchId' : batchId, 'quantity' : quantity, 'totalPrice' : totalAmount, 'totalDiscount' : discountAmount,'totalTax' : taxAmount});
      });

      $("tr.selected-product-group-row").each(function() {
          if($(this).hasClass(inventoryUtils.batchSelectRowClass)){
            return;
          }
          var groupId = $(this).parent().attr("class").split("_")[0];
          var rowId = inventoryUtils.getClassWithPrefix(this, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var tokens = rowId.split(inventoryUtils.delimiter);
          var skuId = tokens[1].trim();
          if(skuId == ""){
            return;
          }

          var batchId = tokens[2].trim();
          if(batchId == ""){
              return;
          }

          var quantity = parseFloat($(this).find("input.product-quantity").val());
          var salePrice = parseFloat($(this).find("input.sale-price").val());
          var discount = 0;
          if ($(this).find("input.sale-discount").val() != "") {
              discount = parseFloat($(this).find("input.sale-discount").val());
          }
          var tax = 0;
          if ($(this).find("input.purchase-product-tax").val() != "") {
              tax = parseFloat($(this).find("input.purchase-product-tax").val());
          }
          var total = $(this).find("input.sale-item-total").val();
          var product = {}
          var totalAmount = quantity * salePrice;
          var discountAmount = totalAmount * discount / 100;
          var taxAmount =  (totalAmount - discountAmount) * tax / 100;
          var additionalDiscount = 0
          if($("#"+groupId).find(".group-sale-discount").val() != ""){
            additionalDiscount = $("#"+groupId).find(".group-sale-discount").val();
            discountAmount = discountAmount + ((totalAmount - discountAmount) * (additionalDiscount / 100));
          }

          productBatchList.push({'skuId' : skuId, 'batchId' : batchId, 'quantity' : quantity, 'totalPrice' : totalAmount, 'totalDiscount' : discountAmount,'totalTax' : taxAmount});
      });
      return productBatchList;

  },

};

var inventoryIssueProduct = {

  dataCache : {},

  loadIssueProductMenu : function (){
      ajaxClient.get("/inventory/issue-product-home", function(data){
        $("#main-content").html(data);
        inventoryIssueProduct.init();
     });
  },

  loadIssueProductForm : function () {
      ajaxClient.get("/inventory/issue-product-form", function(data){
        $("#issue-order-body").html(data);
        inventoryIssueProduct.init();
     });

  },

  init : function () {
    inventoryIssueProduct.dataCache = {};
    initDate(pastTransactionDays);
    liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId, inventoryUtils.liveSearchProductResultElementId, inventoryIssueProduct.doneIssueProductTyping);
  },

  doneIssueProductTyping : function(resultArea) {
      var searchText = $(inventoryUtils.liveSearchProductElementId).val();
      ajaxClient.get("/inventory/live-stock-search?text=" + searchText, function(data) {
          $(resultArea).html(data);
          inventoryUtils.bindClickEventOnSearchProduct(searchText, inventoryIssueProduct.dataCache, inventoryIssueProduct.getBatchSelectColumn,
            inventoryIssueProduct.getRowColumns, true, inventoryIssueProduct.getRowDetails,  inventoryIssueProduct.bindIssueProductNewRowEvent, false, inventoryIssueProduct.onDelete);
      });
  },

  getBatchSelectColumn : function (options) {
    return "<select class=\"custom-select product-batch-select\">"+options+"</select>";
  },

  onDelete : function () {
    inventoryIssueProduct.computeIssuedProducts();
  },

  bindIssueProductNewRowEvent : function () {
    inventoryIssueProduct.bindUnitComputeEvent();
  },

  getRowColumns : function (new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue) {
    return ["<td scope=\"row\"> "+productName+" </td>", "<td style=\"width:15%;\">"+batchColumn+"</td>", "<td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\" "+readonly+"> </td> "];
  },

  getRowDetails : function (new_product, batchDetails) {
    var mrp = "";
    var purchasePrice = "";
    var batchDescription = "";
    if(batchDetails != null){
      mrp = batchDetails.productBatch.mrp;
      purchasePrice = batchDetails.productBatch.purchasePrice;
      batchDescription = batchDetails.productBatch.description;
      if(batchDescription == null){
        batchDescription = "";
      }
    }

    return "<tr class=\"product-batch-details-row\"> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-batch-details-container \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> <th scope=\"col\">MRP</th> <th scope=\"col\">Purchase Price</th> <th scope=\"col\">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-mrp\"  placeholder=\"0\" value=\""+mrp+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-purchase-price\"  placeholder=\"0\" value=\""+purchasePrice+"\" readonly></td> <td><textarea class=\"form-control batch-description\" rows=\"1\" placeholder=\"Batch Description\" readonly >"+batchDescription+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>";
  },

  bindUnitComputeEvent : function () {
      $(".product-quantity").focus(function() {}).blur(function() {
         inventoryIssueProduct.computeIssuedProducts();
      });
  },

  computeIssuedProducts : function (){
        var totalQuantity = 0;
        $("tr.selected-product-row").each(function() {
            var quantityStr = $(this).find("input.product-quantity").val();
            var quantity = 0;
            if(quantityStr != ""){
                quantity = parseFloat(quantityStr);
            }
            totalQuantity += quantity;
        });

        $("#total-units").text(totalQuantity);
        return totalQuantity;
  },

  toggleConfirmModal : function () {
    $("#issue-confirmation-modal").modal('toggle');
  },

  issueProducts : function () {
        $("#issue-confirmation-modal").modal('toggle');
        var selectedProductBatchList = inventoryIssueProduct.collectSelectedProducts();
        if (selectedProductBatchList.length == 0){
            showErrorDialogBox("Please select atleast one product to issue!")
            return;
        }

        var issuePerson = $("#issue-person-name").val();
        if(issuePerson == null || issuePerson.trim() == ""){
              inventoryIssueProduct.toggleConfirmModal();
              showErrorDialogBox("Invalid person to issue products");
              return;
          }

        //
        // var isValid = true;
        // // var isValid = validateSaleOrderForm();
        //
        // var invalid = validations($("#sales-order-body"));
        // if(invalid || !isValid){
        //   inventorySale.toggleNewSaleConfirmModal();
        //   return;
        // }

        var netTotal = inventoryIssueProduct.computeIssuedProducts();
        if(netTotal < 0){
          inventoryIssueProduct.toggleConfirmModal();
          showErrorDialogBox("Total issued products can't be less than 0");
          return;
        }

        var email = $('#issue-person-email').val();
        var description = $('#issue-description').val();
        var date = getDate($('#issue-date').val());
        var reference = $('#issue-reference-number').val();

        var issueProductsPayload = {
            'issuedTo': issuePerson,
            'reference': reference,
            'email': email,
            'transactionDate': date.getTime(),
            'description': description,
            'issueProductPayloadList': selectedProductBatchList
        }


        ajaxClient.post( "/inventory/issue-transaction", {'issueProductsPayload' : JSON.stringify(issueProductsPayload)}, function(data) {
            $('#issue-status-model-container').html(data);
            $("#issue-status-modal").modal({backdrop: 'static', keyboard: false});
        });
  },

  collectSelectedProducts : function () {
      productBatchList = []
      $("tr.selected-product-row").each(function() {
          if($(this).hasClass(inventoryUtils.batchSelectRowClass)){
            return;
          }

          var rowId = inventoryUtils.getClassWithPrefix(this, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var tokens = rowId.split(inventoryUtils.delimiter);
          var skuId = tokens[1].trim();
          if(skuId == ""){
            return;
          }

          var batchId = tokens[2].trim();
          if(batchId == ""){
              return;
          }

          var quantity = parseFloat($(this).find("input.product-quantity").val());
          productBatchList.push({'skuId' : skuId, 'batchId' : batchId, 'quantity' : quantity});
      });

      return productBatchList;

  },

};

var inventoryExchangeProduct = {

  dataCache : {},

  loadExchangeProductMenu : function (){
      ajaxClient.get("/inventory/exchange-product-home", function(data){
        $("#main-content").html(data);
        inventoryExchangeProduct.init();

     });
  },

  init : function () {
    inventoryExchangeProduct.dataCache = {};
    initDate(pastTransactionDays);
    inventoryExchangeProduct.bindInvoiceSearchEvents();
  },

  bindInvoiceSearchEvents : function () {
    $("#exchange-invoice-id").on('keypress',function(e) {
      if(e.which == 13) {
        inventoryExchangeProduct.loadExchangeInvoiceDetails();
      }
    });
  },

  loadExchangeInvoiceForm : function () {
    ajaxClient.get("/inventory/exchange-invoice-form", function(data){
      $("#exchange-invoice-body-container").html(data);
      inventoryExchangeProduct.init();
    });
  },

  loadExchangeInvoiceDetails : function () {
    var invoiceId = $("#exchange-invoice-id").val();
    ajaxClient.get("/inventory/exchange-invoice-screen?invoiceId="+invoiceId, function(data){
      $("#exchange-invoice-screen").html(data);
      if($("#invoice-data-json").length ==  0){
        return;
      }

      inventoryExchangeProduct.init();
      var invoiceData = readJson("#invoice-data-json");

      inventoryExchangeProduct.dataCache.invoiceData = invoiceData;

      inventoryExchangeProduct.bindInvoiceProductCheckbox();
      var inventoryPreferences = readJson("#inventory-preferences-json");
      var studentWalletPreferences = readJson("#student-wallet-preferences-json");
      inventorySale.dataCache = {'transactionType' : 'SALE', 'inventoryPreferences' : inventoryPreferences, 'studentWalletPreferences' : studentWalletPreferences};
      inventorySale.init();



      if(invoiceData.inventoryUserType == "STUDENT"){
        inventorySale.userTypeSelectionAction("STUDENT", true);
        inventorySale.inventoryStudentSearchClickAction(invoiceData.studentLite.studentId, true);
      }

    });
  },

  bindInvoiceProductCheckbox : function () {
    $('.exchange-product-select-checkbox').change(function () {
      if($(this).is(":checked")) {
          $(this).closest('tr').find('.exchange-product-select-qty').prop('disabled', false);
          $(this).closest('tr').find('.exchange-product-select-qty').val(1);
      }
      else{
        $(this).closest('tr').find('.exchange-product-select-qty').prop('disabled', true);
        $(this).closest('tr').find('.exchange-product-select-qty').val(0);
      }
      inventoryExchangeProduct.computeAndUpdateNetReturnedProductAmount();
    });

    $(".exchange-product-select-qty").focus(function() {}).blur(function() {
      var newVal = $(this).val();
      if(newVal > $(this).closest('tr').find('product-net-qty').val()){
        $(this).val(1);
      }
      inventoryExchangeProduct.computeAndUpdateNetReturnedProductAmount();
   });

  },

  computeAndUpdateNetReturnedProductAmount : function () {
    var totalReturnedAmount = 0;
    $('.exchange-product-select-checkbox').each( function () {
      if($(this).is(":checked")) {
        var selectedQty = $(this).closest('tr').find('.exchange-product-select-qty').val();
        var orignalQty = $(this).closest('tr').find('.product-net-qty').val();
        var originalTotalAmount = $(this).closest('tr').find('.product-net-amount').val();
        if(selectedQty == orignalQty){
          totalReturnedAmount += originalTotalAmount;
        } else {
          totalReturnedAmount += (originalTotalAmount * selectedQty)/orignalQty;
        }
      }
    });
    $("#net-returned-product-amount").text(totalReturnedAmount.toFixed(2));
    inventorySale.computeNetSaleAmount();
  },

  getReturnProducPayload : function () {
    var returnProductList = [];
    $('.exchange-product-select-checkbox').each( function () {
      if($(this).is(":checked")) {

        var skuId = $(this).closest('tr').find('.exchange-product-id').text().trim();
        var batchId = $(this).closest('tr').find('.exchange-product-batch-id').text().trim();

        var selectedQty = $(this).closest('tr').find('.exchange-product-select-qty').val();
        var orignalQty = $(this).closest('tr').find('.product-net-qty').val();
        var originalTotalAmount = $(this).closest('tr').find('.product-net-amount').val();
        // if(selectedQty == orignalQty){
        //   totalReturnedAmount += originalTotalAmount;
        // } else {
        //   totalReturnedAmount += (originalTotalAmount * selectedQty)/orignalQty;
        // }
        returnProductList.push({'skuId' : skuId, 'batchId' : batchId, 'selectedQty' : selectedQty});
      }
    });
    return returnProductList;
  },

  addExchangeTransaction : function () {
    $("#new-sale-confirm-modal").modal('toggle');
    var netTotal = inventorySale.computeNetSaleAmount();
    // if(netTotal != 0){
    //   showErrorDialogBox("Net Payable amount for exchange of products must be zero.");
    //   return;
    // }

    var returnProductList = inventoryExchangeProduct.getReturnProducPayload();
    if(returnProductList.length == 0){
      showErrorDialogBox("Select atleast one product to return.");
      return;
    }

    var selectedProductBatchList = inventorySale.collectSelectedProducts();
    var productGroups = [];
    if(!inventorySale.isReturnTransaction()){
        productGroups = inventorySale.collectProductGroupSalesOrderFormValues();
    }

    if (selectedProductBatchList.length == 0 && productGroups.length == 0){
        showErrorDialogBox("Please select atleast one product or product group for sale!")
        return;
    }


    var newSalePayload = inventorySale.getNewSalePayload(null, selectedProductBatchList, productGroups, netTotal);

    if(newSalePayload['transactionMode'] === undefined || newSalePayload['transactionMode'] === null || newSalePayload['transactionMode'] === "") {
      alert("Please select a payment mode!");
      return;
    }

    var returnTransactionId = inventoryExchangeProduct.dataCache.invoiceData.transactionId;

    var exchangePayload = {'returnOrderPayload' : {'returnTransactionId' : returnTransactionId, 'returnedProductList' : returnProductList}, 'newTradePayload' : newSalePayload};

    ajaxClient.post( "/inventory/exchange-transaction", {'exchangePayload' : JSON.stringify(exchangePayload)}, function(data) {
        $('#exchange-status-model-container').html(data);
        $("#sale-status-modal").modal({backdrop: 'static', keyboard: false});
    });
   },
};

var inventoryUtils = {

  selectedProductsKey : "selectedProducts",

  selectedProductBatchesKey : "selectedProductBatches",

  batchSelectRowClass : "batch-select-row",

  rowPrefix : "rowproduct",

  delimiter : "_",

  liveSearchProductElementId : "#live-search-product",

  liveSearchProductResultElementId : "#live-search-product-result",

  liveSearchRowElementId : "#live-search-row",

  deleteProductBatchRowClass : "delete-product-batch-row",

  deleteProductBatchSelectRowClass : "delete-product-batch-select-row",

  addNewProductBatchButtonClass : "add-new-batch-button",

  getClassWithPrefix : function (selector, prefix) {
    var classList = $(selector).attr('class').split(/\s+/);
    var className = null;
    $.each(classList, function(index, item) {
      if (item.startsWith(prefix)) {
          className = item;
          return;
      }
    });
    return className;
  },

  getSkuIdFromRow : function(rowContext) {
     var rowId = inventoryUtils.getClassWithPrefix(rowContext, inventoryUtils.rowPrefix);
      if(rowId == null){
        return null;
      }
      var tokens = rowId.split(inventoryUtils.delimiter);
      var skuId = tokens[1].trim();
      if(skuId == ""){
        return null;
      }

      return skuId;
  },


  removeErrorOnClick : function () {

      $('.unavailable-field-input').on('click', function() {
          $(this).css("border", "");
          $(this).parent().find("p.unavailable-field").attr("style", "display:none");
      });
  },

  searchIndividualProductEvent : function (searchText, resultArea, triggerMethod){
    ajaxClient.get("/inventory/live-stock-search?text=" + searchText, function(data) {
        $(resultArea).html(data);
        triggerMethod();
    });
  },


  searchProductGroupEvent : function (searchText, resultArea, triggerMethod){
    ajaxClient.get("/inventory/live-product-group-search?text=" + searchText, function(data) {
        $(resultArea).html(data);
        triggerMethod();
    });
  },

  showProductGroupOption : function (triggerMethod){
    $('.show-product-group-check').change(function () {
         triggerMethod();
         if($(this).is(":checked")) {
             // $(this).attr("checked", returnVal);
             $('.product-group-transation-area').attr("style", "display:block");
         }
         else{
            $('.product-group-transation-area').attr("style", "display:none");
         }
     });
  },

  checkProductTransactionAvailability : function (current, requestedQuantityClass, availableQuantityClass) {
      var quantityToSellString = $(current).find(requestedQuantityClass).first().val();
      var quantityToSell = 0;
      if (quantityToSellString) {
          quantityToSell = parseFloat(quantityToSellString);
      }
      var availableQuantityString = parseFloat($(current).first().find(availableQuantityClass).first().val());
      var availableQuantity = 0;
      if (availableQuantityString) {
          availableQuantity = parseFloat(availableQuantityString);
      }

      var badge = $(current).find('.badge');
      badge.removeClass();
      badge.addClass('badge');
      if (availableQuantity < quantityToSell) {
          badge.addClass('badge-danger');
          badge.text("No");
      } else {
          badge.addClass('badge-success');
          badge.text(availableQuantity);
      }
  },

  checkIfCurrentDateSelected : function (current) {
    var currentDate = getDayStart(getCurrentDate());
    var selectedDate = getDate($(current).parent().parent().parent().parent().find('.select-date').val());
    if(currentDate > selectedDate){
      return false;
    }
    return true;
  },

  productGroupInformation : function (productGroupClass) {
      $("."+productGroupClass).click(function() {
        inventoryProductGroup.showProductGroupListModel($(this).find("p.product-group-json-row"));
      });
  },

  validations : function (element){
    inventoryUtils.clearValidationErrorDisplay();
    inventoryUtils.bindRemoveErrorDisplayEvent();
    inventoryUtils.removeErrorOnClick();
    var invalid = false;
    $(element).find("input.mandatory-field").each(function() {
        if ($(this).val() == "") {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
            invalid = true;
        }
    });
    $(element).find("select.mandatory-field").each(function() {
        if ($(this).find(':selected').length == 0 || $(this).find(':selected').val().trim() == "") {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
            invalid = true;
        }
    });
    $(element).find("input.mandatory-table-field").each(function() {
        if ($(this).val() == "") {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"mandatory-table-field-text\"> <span style=\"color:#e65f76;\">Mandatory</span></p>");
            invalid = true;
        }
    });
    $(element).find("input.percentage-range").each(function() {
        if ($(this).val() < 0 || $(this).val() > 100) {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
            invalid = true;
        }
    });
    $(element).find("input.positive-number").each(function() {
        if ($(this).val() < 0) {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
            invalid = true;
        }
    });
    $(element).find("input.validate-email").each(function() {
        if (!validateEmail($(this).val())) {
            $(this).css("border", "1px solid #ff8795");
            $(this).after("<p class=\"invalid-input-email-text\"> <span style=\"color:#e65f76;\">Invalid Email</span></p>");
            invalid = true;
        }
    });
    return invalid;
  },

  clearValidationErrorDisplay : function(){

    $(".percentage-range").css("border", "");
    $(".positive-number").css("border", "");
    $(".invalid-input-text").remove();

    $(".mandatory-field").css("border", "");
    $(".mandatory-field-text").remove();

    $(".mandatory-table-field").css("border", "");
    $(".mandatory-table-field-text").remove();

    $(".validate-email").css("border", "");
    $(".invalid-input-email-text").remove();

  },

  closeModal : function (){
    inventoryUtils.clearValidationErrorDisplay();
  },


  bindRemoveErrorDisplayEvent : function () {
      $(".percentage-range").focus(function() {
          $(this).css("border", "");
          if($(this).next().hasClass('invalid-input-text')){
            $(this).next().remove();
          }
      }).blur(function() {});
      $(".mandatory-field").focus(function() {
          $(this).css("border", "");
          if($(this).next().hasClass('mandatory-field-text')){
            $(this).next().remove();
          }
      }).blur(function() {});
      $(".mandatory-table-field").focus(function() {
          $(this).css("border", "");
          if($(this).next().hasClass('mandatory-table-field-text')){
            $(this).next().remove();
          }
      }).blur(function() {});
      $(".positive-number").focus(function() {
          $(this).css("border", "");
          if($(this).next().hasClass('invalid-input-text')){
            $(this).next().remove();
          }
      }).blur(function() {});
      $(".validate-email").focus(function() {
          $(this).css("border", "");
          if($(this).next().hasClass('invalid-input-email-text')){
            $(this).next().remove();
          }
      }).blur(function() {});
  },

  bindClickEventOnSearchProduct : function (searchText, dataCache, getBatchSelectColumn,
    getRowColumns, includeRowDetails, getRowDetails, bindCustomNewRowEvents, allowAddNewBatch, onDelete) {
      $("#live-search-stock-results tbody tr th").off('click').on("click", function() {
          if($(this).hasClass("skip-click")){
            return;
          }

          if($(this).hasClass("add-new-product")){
            var batchNameInput = inventoryUtils.getNewBatchInput();
            $(inventoryUtils.liveSearchRowElementId).before(inventoryUtils.getProductRow(true, null, "", searchText, "", batchNameInput, true, getRowColumns, includeRowDetails , getRowDetails));

            $(inventoryUtils.liveSearchProductResultElementId).html("");
            $(inventoryUtils.liveSearchProductElementId).val("");

            inventoryUtils.bindNewRowEvent(dataCache, onDelete, bindCustomNewRowEvents);
            return;
          }

          if(!(inventoryUtils.selectedProductsKey in dataCache)){
            dataCache[inventoryUtils.selectedProductsKey] = {};
          }
          if(!(inventoryUtils.selectedProductBatchesKey in dataCache)){
            dataCache[inventoryUtils.selectedProductBatchesKey] = {};
          }

          var attrsJson = $(this).parent().find("td.stock-json").text().trim();
          var product = JSON.parse(attrsJson);
          var productId = product.productMetadata.skuId;
          var productName = product.productMetadata.name;
          var existingProducts = dataCache[inventoryUtils.selectedProductsKey];
          for(productIdEntry in existingProducts){
              var existingProduct = existingProducts[productIdEntry];
              if(existingProduct.productMetadata.skuId == productId){
                showErrorDialogBox("Product :  " + productName + " already selected. Please add quantity in that.");
                return;
              }
          }

          if(!allowAddNewBatch && (product.productBatchDataList == null || product.productBatchDataList.length == 0)){
            showErrorDialogBox("Product :  " + productName + " has no active batch available. Please add a active batch before using it.");
            return;
          }

          existingProducts[productId] = product;
          var existingProductBatches = dataCache[inventoryUtils.selectedProductBatchesKey];
          var options = "<option value=\"\">select</option>";
          var firstBatchId = null;
          if(product.productBatchDataList != null && product.productBatchDataList.length > 0){
              var i = 0;
              var selectedOption = "selected";
              for(var i = 0; i < product.productBatchDataList.length; i++){
                  var productBatch = product.productBatchDataList[i];
                  var batchId = productBatch.batchId;
                  var batchName = productBatch.batchName;
                  options += "<option value=\"" + batchId + "\" "+ selectedOption + ">"+batchName+"</option>";
                  existingProductBatches[batchId] = {'productMetadata' : product.productMetadata, 'productBatch' : productBatch};
                  if(i == 0){
                    firstBatchId = batchId;
                  }
                  selectedOption = "";
              }
          }

          var batchSelectColumn = getBatchSelectColumn(options);

          $(inventoryUtils.liveSearchRowElementId).before(inventoryUtils.getProductRow(false, null, productId, productName, "", batchSelectColumn, false, getRowColumns, includeRowDetails , getRowDetails));

          $(inventoryUtils.liveSearchProductResultElementId).html("");
          $(inventoryUtils.liveSearchProductElementId).val("");

          inventoryUtils.bindProductBatchSelect(dataCache, getRowColumns, includeRowDetails , getRowDetails, bindCustomNewRowEvents, onDelete, allowAddNewBatch);

          var productBatchSelectClass = inventoryUtils.getBatchSelectRowClassName(productId);
          if(firstBatchId != null){
            inventoryUtils.productBatchSelectAction(dataCache, productBatchSelectClass, firstBatchId, getRowColumns, includeRowDetails , getRowDetails, bindCustomNewRowEvents, onDelete, allowAddNewBatch);
          } else if(firstBatchId == null && allowAddNewBatch){
            inventoryUtils.purchaseAddProductBatchButtonAction(productId, productBatchSelectClass, dataCache, getRowColumns,  includeRowDetails , getRowDetails, onDelete, bindCustomNewRowEvents);
          }

          if(allowAddNewBatch){
            inventoryUtils.bindPurchaseAddProductBatchButton(dataCache, getRowColumns, includeRowDetails , getRowDetails, onDelete, bindCustomNewRowEvents);
          }
      });
  },

  bindProductBatchSelect : function (dataCache, getRowColumns, includeRowDetails , getRowDetails, bindCustomNewRowEvents, onDelete, allowAddNewBatch) {
    $('select.product-batch-select').on('change', function() {
      var batchId = this.value;
      inventoryUtils.productBatchSelectAction(dataCache, this, batchId, getRowColumns, includeRowDetails , getRowDetails, bindCustomNewRowEvents, onDelete, allowAddNewBatch);
    });
  },

  getAddAnotherBatchTextElement : function(showElement) {
    if(showElement){
      return "<a class=\""+inventoryUtils.addNewProductBatchButtonClass+"\" style=\"display:block;\">Add Other Batch</a>";
    }
    return "<a class=\""+inventoryUtils.addNewProductBatchButtonClass+"\" style=\"display:none;\">Add Other Batch</a>";

  },

  productBatchSelectAction : function(dataCache, optionSelectContext, batchId, getRowColumns, includeRowDetails , getRowDetails, bindCustomNewRowEvents, onDelete, allowAddNewBatch) {
     if(batchId == null || batchId == undefined || batchId.trim() == ""){
        return;
      }

      var batchDetails = dataCache[inventoryUtils.selectedProductBatchesKey][batchId];
      var existingProducts = dataCache[inventoryUtils.selectedProductsKey];
      var parentProduct =  existingProducts[batchDetails.productMetadata.skuId];

      var totalQuantity = batchDetails.productBatch.totalQuantity;
      // var batchDisplayData = "<span style=\"display: block;\">" + batchDetails.productBatch.batchName + "</span>";
      var batchDisplayData = "";
      var addAnotherBatchText = "\n" + inventoryUtils.getAddAnotherBatchTextElement(true);
      // plus one to the existing count as this element will be inserted after this check
      if(!allowAddNewBatch && inventoryUtils.getSelectedBatchCount(batchDetails.productMetadata.skuId) + 1 >= parentProduct.productBatchDataList.length){
        addAnotherBatchText = "\n" + inventoryUtils.getAddAnotherBatchTextElement(false);
      }

      if(totalQuantity > 0){
          batchDisplayData = "<h5>"+ batchDetails.productBatch.batchName + " &nbsp;<span class=\"badge badge-success\">Available : "+batchDetails.productBatch.totalQuantity+"</span></h5>";
      }else{
          batchDisplayData = "<h5>"+ batchDetails.productBatch.batchName + " &nbsp;<span class=\"badge badge-danger\">Not Available</span></h5>";
      }
      batchDisplayData += addAnotherBatchText;
      // var batchDisplayData = batchDetails.productBatch.batchName + "<span class=\"badge badge-success\">Avl : </span>" + " [Avl : "+ batchDetails.productBatch.totalQuantity +"]"
      $(optionSelectContext).closest('tr').before(inventoryUtils.getProductRow(false, batchDetails, batchDetails.productMetadata.skuId, batchDetails.productMetadata.name, batchDetails.productBatch.batchId, batchDisplayData, true, getRowColumns, includeRowDetails , getRowDetails));
      $('option:selected', optionSelectContext).remove();
      inventoryUtils.bindNewRowEvent(dataCache, onDelete, bindCustomNewRowEvents);
      inventoryUtils.bindDeleteProductBatchSelectEntry();
      inventoryUtils.hideBatchSelectRow(batchDetails.productMetadata.skuId);

  },

  getSelectedBatchCount : function(skuId) {
    return $(".selected-product-row."+skuId).length;
  },

  hideBatchSelectRow :function(skuId) {
    var productBatchSelectClass = inventoryUtils.getBatchSelectRowClassName(skuId);
    $(productBatchSelectClass).css('display','none');
  },

  showBatchSelectRow :function(skuId) {
    var productBatchSelectClass = inventoryUtils.getBatchSelectRowClassName(skuId);
    $(productBatchSelectClass).css('display','');
  },

  bindNewRowEvent : function (dataCache, onDelete, bindCustomNewRowEvents) {
    inventoryUtils.deleteProductEntry(dataCache, onDelete);
    inventoryUtils.bindExpandBatchButton();
    inventoryUtils.bindShowAddNewBatchButton();
    bindCustomNewRowEvents();
  },

  bindExpandBatchButton : function () {
    $(".add-product-batch-details").off('click').on('click', function () {
      $(this).closest("tr").next("tr").find('.product-batch-details-container').collapse('toggle');
    });
  },

  bindShowAddNewBatchButton : function () {
    $("."+inventoryUtils.addNewProductBatchButtonClass).off('click').on('click', function () {
        var parentRow = $(this).closest('tr');
        var skuId = inventoryUtils.getSkuIdFromRow(parentRow);
        if(skuId == null){
          return;
        }
        inventoryUtils.showBatchSelectRow(skuId);
        inventoryUtils.hideAllAddNewProductBatchButtons(skuId);
    });
  },

  hideAllAddNewProductBatchButtons: function(skuId) {
     $("."+skuId).find("."+inventoryUtils.addNewProductBatchButtonClass).css('display', 'none');
  },

  showAddNewProductBatchButtonOnLastBatch: function(skuId) {
     var productBatchSelectClass = inventoryUtils.getBatchSelectRowClassName(skuId);
     $(productBatchSelectClass).parent().find(".selected-product-row."+skuId).last().find("."+inventoryUtils.addNewProductBatchButtonClass).css('display', 'block');

  },

  deleteProductEntry : function(dataCache, onDelete) {
      $("."+inventoryUtils.deleteProductBatchRowClass).off('click').on("click", function() {
          var batchSelectRowClass = "."+inventoryUtils.batchSelectRowClass;
          var parentRow = $(this).parent().parent();
          var rowId = inventoryUtils.getClassWithPrefix(parentRow, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }

          var skuId = rowId.split(inventoryUtils.delimiter)[1];
          var batchId = rowId.split(inventoryUtils.delimiter)[2];

          if(batchId != null && batchId != undefined && batchId.trim() != ""){
            var batchDetails = dataCache[inventoryUtils.selectedProductBatchesKey][batchId];
            var batchSelectRowElement = null;
            if(parentRow.next(batchSelectRowClass).length == 0){
              batchSelectRowElement = parentRow.nextUntil(batchSelectRowClass).last().next();
            }else{
              batchSelectRowElement = parentRow.next(batchSelectRowClass);
            }
            batchSelectRowElement.find('select.product-batch-select').append($("<option></option>")
                      .attr("value", batchId)
                      .text(batchDetails.productBatch.batchName));
          }

          parentRow.next('tr.product-batch-details-row').remove();
          parentRow.remove();
          // second row is collapse card
          if($(".selected-product-row."+skuId).length == 0){
            $(".selected-product-row."+skuId).remove();
            delete dataCache[inventoryUtils.selectedProductsKey][skuId];
          }

          inventoryUtils.resetAddNewProductBatchButtonState(skuId);
          onDelete();
      });
  },

  bindDeleteProductBatchSelectEntry : function() {
      $("."+inventoryUtils.deleteProductBatchSelectRowClass).off('click').on("click", function() {
          var parentRow = $(this).parent().parent();
          var skuId = inventoryUtils.getSkuIdFromRow(parentRow);
          inventoryUtils.resetAddNewProductBatchButtonState(skuId);
      });
  },

  resetAddNewProductBatchButtonState : function(skuId) {
      inventoryUtils.hideBatchSelectRow(skuId);
      inventoryUtils.hideAllAddNewProductBatchButtons(skuId);
      inventoryUtils.showAddNewProductBatchButtonOnLastBatch(skuId);
  },

  bindPurchaseAddProductBatchButton : function(dataCache, getRowColumns,  includeRowDetails , getRowDetails, onDelete, bindCustomNewRowEvents) {
      $(".add-product-batch").off('click').on("click", function() {
        var parentRow = $(this).parent().parent().parent().parent();
        var rowId = inventoryUtils.getClassWithPrefix(parentRow, inventoryUtils.rowPrefix);
        if(rowId == null){
          return;
        }

        var productId = rowId.split(inventoryUtils.delimiter)[1];

        if(productId == null || productId == undefined || productId.trim() == ""){
          return;
        }
        inventoryUtils.purchaseAddProductBatchButtonAction(productId, this, dataCache, getRowColumns,  includeRowDetails , getRowDetails, onDelete, bindCustomNewRowEvents);
      });
  },

   purchaseAddProductBatchButtonAction : function(productId, addButtonContext, dataCache, getRowColumns,  includeRowDetails , getRowDetails, onDelete, bindCustomNewRowEvents) {
      var product = dataCache[inventoryUtils.selectedProductsKey][productId];
      var batchNameInput = inventoryUtils.getNewBatchInput();
      var addAnotherBatchText = "\n" + inventoryUtils.getAddAnotherBatchTextElement(true);
      batchNameInput += addAnotherBatchText;
      $(addButtonContext).closest('tr').before(inventoryUtils.getProductRow(false, null, product.productMetadata.skuId, product.productMetadata.name, "", batchNameInput, true, getRowColumns, includeRowDetails , getRowDetails));
      inventoryUtils.bindNewRowEvent(dataCache, onDelete, bindCustomNewRowEvents);
      inventoryUtils.hideBatchSelectRow(productId);
  },

  getNewBatchInput : function () {
      return "<input type=\"text\" class=\"form-control form-control-sm purchase-product-batch-name mandatory-table-field\" placeholder=\"New batch name\">";

  },

  getRowClassName : function(productId, batchId) {
    return inventoryUtils.rowPrefix + inventoryUtils.delimiter + productId + inventoryUtils.delimiter + batchId;
  },

  getBatchSelectRowClassName : function(productId) {
    return "."+inventoryUtils.batchSelectRowClass + "." + inventoryUtils.getRowClassName(productId, "");
  },

  getProductRow : function (new_product, batchDetails, productId, productName, batchId, batchColumn, edit, getRowColumns, includeRowDetails, getRowDetails, quantityValue = 0, selectedProductRowClass = "selected-product-row") {
      var readonly = "";
      var disabled = "";
      var closeLabel = "<button type=\"button\" class=\"close "+inventoryUtils.deleteProductBatchRowClass+" \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
      if(selectedProductRowClass === "selected-product-group-row"){
        closeLabel = "<button type=\"button\" class=\"close delete-product-batch-group-row aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
      }
      var batchSelectRowClass = "";
      var display = "";
      var selectedProductRow = selectedProductRowClass;
      if(!edit){
          readonly = "readonly";
          disabled = "disabled";
          closeLabel = "<button type=\"button\" class=\"close "+inventoryUtils.deleteProductBatchSelectRowClass+" \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
          if(selectedProductRowClass === "selected-product-group-row"){
            closeLabel = "<button type=\"button\" class=\"close delete-product-batch-group-row aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
          }
          batchSelectRowClass = inventoryUtils.batchSelectRowClass;
          display = "style=\"display:none;\"";
          selectedProductRow = "";
      }
      var rowClass = inventoryUtils.getRowClassName(productId, batchId);
      var columns = getRowColumns(new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue);
      var row = "<tr class=\" "+productId+" " + selectedProductRow + " " +batchSelectRowClass+" "+rowClass+" \" "+display+" >";
      for(var i = 0; i < columns.length; i++){
        //  This is special column which needs to
        // be inserted at 3rd position for all
        if(i == 2){
          row += "<td><button type=\"button\" class=\"btn btn-primary btn-sm add-product-batch-details\" "+disabled+">+Details</button></td>";
        }
        row += columns[i];
      }
      row += "<td> "+closeLabel+"</td> </tr>";
      if(includeRowDetails && edit){
        var productBatchDetailRow = getRowDetails(new_product, batchDetails);
        row += productBatchDetailRow;
      }

      return row
  },
};

var inventoryBrand = {

  loadBrandsMenu : function () {
      ajaxClient.get("/inventory/brands", function(data) {
          $("#main-content").html(data);

          inventoryBrand.registerAddBrandCallback();
          inventoryBrand.registerBrandSearchCallback();

          inventoryBrand.fillDeleteBrandModel();
          inventoryBrand.fillUpdateBrandModel();
      });
  },

  registerBrandSearchCallback : function (){
      $('#search-brand').off('click').on('click', function () {
            inventoryBrand.searchBrands();
      });
      $("#brand-search-text").on('keyup', function (e) {
        if (e.keyCode == 13) {
            inventoryBrand.searchBrands();
        }
      });
  },

  searchBrands : function (){
      var searchText = $("#brand-search-text").val();
      $("#brand-search-result").html("");

      ajaxClient.get("/inventory/search-brands?text="+searchText, function(data){
          $("#brand-search-result").html(data);
          inventoryBrand.fillDeleteBrandModel();
          inventoryBrand.fillUpdateBrandModel();
     });
  },

  registerAddBrandCallback : function (){
      $('#add-brand').off('click').on('click', function () {
          var brandName = $("#brand-name").val();
          $("#addBrandModel").modal('toggle');

          var brandData = {"brandName":brandName};
          ajaxClient.post("/inventory/add-brand",{'brandData':JSON.stringify(brandData)}, function(data){
              inventoryBrand.searchBrands();
              inventoryBrand.clearAddBrandForm();
              $("#brand-status-modal-container").html(data);
              $("#brand-status-modal").modal('toggle');
          });
      });
  },

  fillUpdateBrandModel : function (){
      $('.update-brand').off('click').on('click', function () {
          inventoryBrand.clearUpdateBrandForm();
          var brandJson = $(this).parent().parent().find("td.brand-info").text().trim();
          var brands = JSON.parse(brandJson);
          $("#update-brand-name").val(brands.brandName);
          $("#update-brand-id").val(brands.brandId);
          $("#update-brand-modal").modal('toggle');
      });
  },

  updateBrand : function (){
        var brandName = $("#update-brand-name").val();
        var brandId = $("#update-brand-id").val();
        $("#update-brand-modal").modal('toggle');

        var brandData = {"brandId" : brandId, "brandName":brandName};
        ajaxClient.post("/inventory/update-brand",{'brandData':JSON.stringify(brandData)}, function(data){
            inventoryBrand.searchBrands();
            $("#brand-status-modal-container").html(data);
            $("#brand-status-modal").modal('toggle');
       });
  },

  fillDeleteBrandModel: function (){
        $('.delete-brand').off('click').on('click', function () {
            var brandJson = $(this).parent().parent().find("td.brand-info").text().trim();
            var brands = JSON.parse(brandJson);
            $("#delete-brand-id").text(brands.brandId);
            $("#brand-delete-confirmation-modal-text").text("Are you sure to delete brand " + brands.brandName);
            $("#brand-delete-confirmation-modal").modal('toggle');
        });
  },

  deleteBrand : function (){
        $("#brand-delete-confirmation-modal").modal('toggle');
        var brandId =  $("#delete-brand-id").text();

        ajaxClient.post("/inventory/delete-brand",{'brandId':brandId}, function(data){
            $("#brand-brand-status-modal-container").html(data);
            $("#brand-status-modal").modal('toggle');
            inventoryBrand.searchBrands();
       });
  },

  clearAddBrandForm : function (){
        $('#addBrandModel').find("input").val("");
        $('#addBrandModel').find("select").val("");
        $('#addBrandModel').find("span.label-area").remove();
  },

  clearUpdateBrandForm : function (){
        $('#update-brand-modal').find("input").val("");
        $('#update-brand-modal').find("select").val("");
        $('#update-brand-modal').find("span.label-area").remove();
  },

};

var inventorySupplier = {

  loadSupplierMenu : function () {
          ajaxClient.get("/inventory/supplier", function(data) {
              $("#main-content").html(data);

              inventorySupplier.registerSupplierSearchCallback();
              inventorySupplier.registerAddSupplierCallback();
              inventorySupplier.registerUpdateSupplierCallback();
              // enforceConstraints();
              inventoryUtils.removeErrorOnClick();
              inventoryUtils.bindRemoveErrorDisplayEvent();

              inventorySupplier.bindUpdateSupplierCallback();
              inventorySupplier.bindViewSupplierCallback();
          });
  },


  registerSupplierSearchCallback : function (){
      $('#searchSuppliers').off('click').on('click', function () {
           inventorySupplier.searchSuppliers();
      });

      $("#searchSuppliersInput").on('keyup', function (e) {
        if (e.keyCode == 13) {
            inventorySupplier.searchSuppliers();
        }
      });
  },

  searchSuppliers : function (){
        searchText = $("#searchSuppliersInput").val();
        $("#searchSuppliersResult").html("");

        ajaxClient.get("/inventory/supplier-search?text="+searchText, function(data){

            $("#searchSuppliersResult").html(data);
            inventorySupplier.bindUpdateSupplierCallback();
            inventorySupplier.bindViewSupplierCallback();
       });
  },

  registerAddSupplierCallback : function (){
      $('#add-supplier').off('click').on('click', function () {
          var isValid = inventorySupplier.validateAddSupplierForm()
          if(!isValid){
            return;
          }
          var supplierName = $("#supplier-name").val();
          var firstName = $("#supplier-first-name").val();
          var lastName = $("#supplier-last-name").val();
          var address = $("#supplier-address").val();
          var city = $("#supplier-city").val();
          var state = $("#supplier-state option:selected").val();
          var zipcode = $("#supplier-zipcode").val();
          var email = $("#supplier-email").val();
          var primaryMobile = $("#supplier-primary-mobile").val();
          var secondaryMobile = $("#supplier-secondary-mobile").val();
          var landline = $("#supplier-landline").val();

          $("#searchSuppliersResult").html("");
          $("#addSupplier").modal('toggle');

          var supplierPayload = {"supplierName": supplierName, "contactName":firstName+ " "+ lastName,
                        "address":address,"city":city,"state":state,"zipcode":zipcode,"email":email,
                        "primaryPhoneNumber":primaryMobile,"secondPhoneNumber":secondaryMobile,"landlineNumber":landline,
                      };

          ajaxClient.post("/inventory/add-supplier",{'supplierPayload' : JSON.stringify(supplierPayload)}, function(data){

            inventorySupplier.searchSuppliers();
            inventorySupplier.clearAddSupplierForm();
            $("#supplier-status-modal-container").html(data);
            $("#supplier-status-model").modal('toggle');
         });
      });
  },

  clearAddSupplierForm : function (){
      $('#addSupplier').find("input").val("");
      $('#addSupplier').find("select").val("");
  },


  bindUpdateSupplierCallback : function (){
      $(".update-supplier-button").off('click').on('click', function () {
        var attrs = $(this).parent().parent().find('p.supplier-info-json').text().trim();
        var supplierAttributes = JSON.parse(attrs);

        $("#supplier-update-supplierId").val(supplierAttributes.supplierId);
        $("#supplier-update-contactName").val(supplierAttributes.contactName);
        $("#supplier-update-supplierName").val(supplierAttributes.supplierName);
        $("#supplier-update-address").val(supplierAttributes.address);
        $("#supplier-update-city").val(supplierAttributes.city);
        $("#supplier-update-state").val(supplierAttributes.state);
        $("#supplier-update-zipcode").val(supplierAttributes.zipcode);
        $("#supplier-update-email").val(supplierAttributes.email);
        $("#supplier-update-primaryPhoneNumber").val(supplierAttributes.primaryPhoneNumber);
        $("#supplier-update-secondPhoneNumber").val(supplierAttributes.secondPhoneNumber);
        $("#supplier-update-landline").val(supplierAttributes.landlineNumber);
      });
  },

  bindViewSupplierCallback : function () {
      $(".view-supplier-button").off('click').on('click', function () {
        var attrs = $(this).parent().parent().find('p.supplier-info-json').text().trim();
        var supplierAttributes = JSON.parse(attrs);

        $("#supplier-view-supplierId").val(supplierAttributes.supplierId);
        $("#supplier-view-contactName").val(supplierAttributes.contactName);
        $("#supplier-view-supplierName").val(supplierAttributes.supplierName);
        $("#supplier-view-address").val(supplierAttributes.address);
        $("#supplier-view-city").val(supplierAttributes.city);
        $("#supplier-view-state").val(supplierAttributes.state);
        $("#supplier-view-zipcode").val(supplierAttributes.zipcode);
        $("#supplier-view-email").val(supplierAttributes.email);
        $("#supplier-view-primaryPhoneNumber").val(supplierAttributes.primaryPhoneNumber);
        $("#supplier-view-secondPhoneNumber").val(supplierAttributes.secondPhoneNumber);
        $("#supplier-view-landline").val(supplierAttributes.landlineNumber);
      });
  },

  registerUpdateSupplierCallback : function (){
      $('#update-supplier').off('click').on('click', function () {

          var supplierId = $("#supplier-update-supplierId").val();
          var contactName = $("#supplier-update-contactName").val();
          var supplierName = $("#supplier-update-supplierName").val();
          var address = $("#supplier-update-address").val();
          var city = $("#supplier-update-city").val();
          var state = $("#supplier-update-state").val();
          var zipcode = $("#supplier-update-zipcode").val();
          var email = $("#supplier-update-email").val();
          var primaryPhoneNumber = $("#supplier-update-primaryPhoneNumber").val();
          var secondPhoneNumber = $("#supplier-update-secondPhoneNumber").val();
          var landline = $("#supplier-update-landline").val();

          $("#supplier-update-supplier-modal").modal('toggle');

          var supplierPayload = {"supplierId":supplierId,"contactName":contactName,
                        "supplierName":supplierName,"address":address,"city":city,"state":state,
                        "zipcode":zipcode,"email":email,"primaryPhoneNumber":primaryPhoneNumber,
                        "secondPhoneNumber":secondPhoneNumber,"landlineNumber":landline};

          ajaxClient.post("/inventory/update-supplier",{'supplierPayload' : JSON.stringify(supplierPayload)}, function(data){
            inventorySupplier.searchSuppliers();
            $("#supplier-status-modal-container").html(data);
            $("#supplier-status-modal").modal('toggle');
         });
      });
  },

  validateAddSupplierForm : function (){
    $(".mandatory-field").attr("style", "display:none");
    $(".mandatory-field-input").css("border","");
    $(".validate-email").css("border","");
    $(".invalid-email").attr("style", "display:none");
    var invalidSupplier = false;
    var companyName = $("#supplier-name");
    if(companyName.val() == ""){
      companyName.css("border","1px solid #ff8795");
      $("#mandatoryFieldCompanyName").attr("style", "display:block");
      invalidSupplier = true;
    }

    var contactName = $("#supplier-first-name");
    if(contactName.val() == ""){
      contactName.css("border","1px solid #ff8795");
      $("#mandatoryFieldContactName").attr("style", "display:block");
      invalidSupplier = true;
    }


    var address = $("#supplier-address");
    if(address.val() == ""){
      address.css("border","1px solid #ff8795");
      $("#mandatoryFieldAddress").attr("style", "display:block");
      invalidSupplier = true;
    }
    var city = $("#supplier-city");
    if(city.val() == ""){
      city.css("border","1px solid #ff8795");
      $("#mandatoryFieldCity").attr("style", "display:block");
      invalidSupplier = true;
    }

    var state = $("#supplier-state");
    if(state.val() == ""){
      state.css("border","1px solid #ff8795");
      $("#mandatoryFieldState").attr("style", "display:block");
      invalidSupplier = true;
    }

    // var zipcode = $("#supplier-zipcode");
    // if(zipcode.val() == ""){
    //   zipcode.css("border","1px solid #ff8795");
    //   $("#mandatoryFieldZip").attr("style", "display:block");
    //   invalidSupplier = true;
    // }
    var email = $("#supplier-email");
    var isEmailValid = validateEmail(email.val());
    if(!isEmailValid){
      email.css("border","1px solid #ff8795");
      $("#invalidFieldEmail").attr("style", "display:block");
      invalidSupplier = true;
    }

    return !invalidSupplier;
  },

  removeErrorInitially : function (){
    $(".mandatory-field").attr("style", "display:none");
    $(".mandatory-field-input").css("border","");
    $(".validate-email").css("border","");
    $(".invalid-email").attr("style", "display:none");
  },

};

var inventoryTransactions = {
  dataCache : {},

  loadTransactionMenu : function (){
    ajaxClient.get("/inventory/transactions", function(data) {
        $("#main-content").html(data);
        inventoryTransactions.bindtransactionGenerationOptions();
        inventoryTransactions.dataCache = {};
    });
  },

  loadTransactionBody: function (){
    ajaxClient.get("/inventory/transactions-body", function(data) {
        $("#transations-body").html(data);
        inventoryTransactions.bindtransactionGenerationOptions();
        inventoryTransactions.dataCache = {};
    });
  },

  getTransactionDetails: function (transactionId){
     ajaxClient.get("/inventory/transactions-details/"+transactionId, function(data){
          $('#transaction-summary-modal-container').html(data);
          $("#transaction-summary-modal").modal('toggle');
     });
  },

  generateInvoice : function (transactionId) {
      window.open(baseURL + "/inventory/pdf-invoice/" + transactionId, '_blank');
  },

  deleteTransactionPreview : function (transactionId) {
    inventoryTransactions.dataCache.deleteSelectedTransactionId = transactionId;
    $("#delete-transaction-screen-modal").modal('toggle');
  },

  deleteTransaction: function () {
    $("#delete-transaction-screen-modal").modal('toggle');
    var transactionId = inventoryTransactions.dataCache.deleteSelectedTransactionId;
    ajaxClient.post("/inventory/delete-transaction/"+transactionId, {}, function(data) {
        $("#transaction-status-modal-container").html(data);
        $("#transaction-status-modal").modal('toggle');
        inventoryTransactions.loadTransactionBody();
    });
  },
  
  bindtransactionGenerationOptions :function () {
    $('#active-transaction').on('click', function () {
          $(".inventory-transaction").attr("style","display:none;");
          $("#active-transations-body").attr("style","display:block;");
          inventoryTransactions.loadTransactionMenu();
    });

    $('#cancelled-transaction').on('click', function () {
          $(".inventory-transaction").attr("style","display:none;");
          $("#cancelled-transactions-body").attr("style","display:block;");
    });
  },
};

var inventoryReports = {

  pastReportAllowedDays : 365,
  TRANSACTION : "TRANSACTION",
  USER_LEVEL_DAY_WISE_REPORT : "USER_LEVEL_DAY_WISE_REPORT",

  loadReportsMenu : function() {
    ajaxClient.get("/inventory/reports", function(data) {
        $("#main-content").html(data);

        initDate(inventoryReports.pastReportAllowedDays);
        inventoryReports.generateReport();
    });
  },

  generateReport : function (){
    $('.generate-report').on('click', function () {

      var containerElement = $(this).closest('div.report-field-container');
      var invalid = validateMandatoryFields($(containerElement));
      if(invalid){
        return;
      }

      var reportType = $(this).parent().parent().find("p").text();
      var startDate = -1;
      var endDate = -1;
      $(this).parent().parent().parent().parent().modal('toggle');
      if(reportType != "STOCK_DETAILS_REPORT"){
        var start = $(this).parent().parent().find("input.report-start-date").val();
        var end = $(this).parent().parent().find("input.report-end-date").val();
        var startDateVal = getDate(start);
        var endDateVal = getDate(end);

        startDate = getDayStart(startDateVal);
        endDate = getDayEnd(endDateVal);

        if(startDate.getTime() >= endDate.getTime()){
            showErrorDialogBox("Report start date cannot be greater than report end date. Please enter valid range.");
            return;
        }
        startDate = startDate.getTime()/1000;
        endDate = endDate.getTime()/1000;
      }

      window.open(baseURL+"/inventory/generate-report/"+reportType+"?start="+startDate+"&end="+endDate, '_blank');
    });
  }

};

var inventoryProductGroup = {

 dataCache : {},

 loadProductGroupsMenu : function() {
    ajaxClient.get("/inventory/product-groups", function(data) {
        $("#main-content").html(data);
        inventoryProductGroup.init();

        inventoryProductGroup.searchProductGroup();
        inventoryProductGroup.registerProductGroupSearchCallback();
        liveSearchHandler.bindEvent('#groups\\.search-product',"#groups\\.add\\.search-result", inventoryProductGroup.doneAddGroupProductTyping);
        liveSearchHandler.bindEvent('#groups\\.update\\.search-product',"#groups\\.update\\.search-result", inventoryProductGroup.doneUpdateGroupProductTyping);
        inventoryUtils.bindRemoveErrorDisplayEvent();
        inventoryProductGroup.registerAddProductGroupCallback();
         inventoryProductGroup.registerUpdateProductGroupCallback();
        inventoryProductGroup.registerDeleteProductGroupCallback();
        // preventSpecialChars();
        // removeErrorOnClick();
    });
  },

  init : function () {
    liveSearchHandler.bindEvent(inventoryUtils.liveSearchProductElementId, inventoryUtils.liveSearchProductResultElementId, inventoryProductGroup.doneProductTyping);
  },


  doneProductTyping : function(resultArea) {
      var searchText = $(inventoryUtils.liveSearchProductElementId).val();
      ajaxClient.get("/inventory/live-stock-search?text=" + searchText, function(data) {
          $(resultArea).html(data);
          inventoryUtils.bindClickEventOnSearchProduct(searchText, inventoryProductGroup.dataCache, inventoryProductGroup.getBatchSelectColumn,
            inventoryProductGroup.getRowColumns, true, inventoryProductGroup.getRowDetails, inventoryProductGroup.bindSaleNewRowEvent, false, inventoryProductGroup.onDelete);
      });
  },

  getBatchSelectColumn : function (options) {
    return "<select class=\"custom-select product-batch-select\">"+options+"</select>";
  },

  getRowColumns : function (new_product, batchDetails, productName, batchColumn, readonly, disabled, quantityValue) {
    var sellingPrice = 0;
    var discountVal = 0;
    if(batchDetails != null){
      sellingPrice = batchDetails.productBatch.sellingPrice;
      var discount = batchDetails.productBatch.discount;
      var discountInPercent = batchDetails.productBatch.discountInPercent;
      if(discount != null){
        if(discountInPercent){
          discountVal = discount;
        }else if(sellingPrice != null && sellingPrice > 0){
          discountVal = discount * 100/sellingPrice;
        }
      }
    }

    return ["<td scope=\"row\"> "+productName+" </td>", "<td style=\"width:15%;\">"+batchColumn+"</td>", "<td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\" "+readonly+"> </td> "," <td><input type=\"number\" class=\"form-control form-control-sm sale-price mandatory-table-field positive-number\" placeholder=\"0\" value=\""+sellingPrice+"\" readonly></td> ", " <td><input type=\"number\" class=\"form-control form-control-sm sale-item-amount\" placeholder=\"0\" readonly></td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-discount percentage-range\" placeholder=\"Discount\" value=\""+discountVal+"\" "+readonly+"> </td> ", "<td><input type=\"number\" class=\"form-control form-control-sm sale-item-total\" placeholder=\"0\" readonly></td>"];
  },

  getRowDetails : function (new_product, batchDetails) {
    var mrp = "";
    var purchasePrice = "";
    var sellingPrice = "";
    var batchDescription = "";
    if(batchDetails != null){
      mrp = batchDetails.productBatch.mrp;
      purchasePrice = batchDetails.productBatch.purchasePrice;
      sellingPrice = batchDetails.productBatch.sellingPrice;
      batchDescription = batchDetails.productBatch.description;
      if(batchDescription == null){
        batchDescription = "";
      }
    }

    return "<tr class=\"product-batch-details-row\"> <td colspan=\"12\" style=\"padding: 0 !important;\"> <div class=\"collapse product-batch-details-container \"> <div class=\"card card-body\"> <table id=\"datatables-reponsive\" class=\"table table-borderless datatables-reponsive-table text-center\"> <thead> <tr> <th scope=\"col\">MRP</th> <th scope=\"col\">Purchase Price</th> <th scope=\"col\">Selling Price</th> <th scope=\"col\">Batch Description</th> </tr> </thead> <tbody> <tr> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-mrp\"  placeholder=\"0\" value=\""+mrp+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-purchase-price\"  placeholder=\"0\" value=\""+purchasePrice+"\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm product-batch-sale-price\"  placeholder=\"0\" value=\""+sellingPrice+"\" readonly></td> <td><textarea class=\"form-control batch-description\" rows=\"1\" placeholder=\"Batch Description\" readonly >"+batchDescription+"</textarea></td> </tr> </tbody> </table> </div> </div> </td> </tr>";
  },

  bindSaleNewRowEvent : function () {
    inventoryProductGroup.bindCostComputeEvent();
  },

  onDelete : function () {
    // inventoryProductGroup.computeNetSaleAmount();
  },

  bindViewProductsGroup: function(){
    $(".view-product-group").on('click', function () {
        inventoryProductGroup.showProductGroupListModel($(this).parent().parent().find("p.product-group-info"));
    });
  },

  bindCostComputeEvent : function () {
      $(".product-quantity").focus(function() {}).blur(function() {
         inventorySale.computeTotal($(this).parent().parent());
      });

      $(".sale-price").focus(function() {}).blur(function() {
          inventorySale.computeTotal($(this).parent().parent());
      });

      $(".sale-discount").focus(function() {}).blur(function() {
          inventorySale.computeTotal($(this).parent().parent());
      });

      $(".purchase-product-tax").focus(function() {}).blur(function() {
          inventorySale.computeTotal($(this).parent().parent());
      });

      $("#additional-cost-input").focus(function() {}).blur(function() {
          inventorySale.computeTotal($(this).parent().parent().parent());
      });

      $("#additional-discount-input").focus(function() {}).blur(function() {
          inventorySale.computeTotal($(this).parent().parent().parent());
      });
  },

  computeTotal : function (current) {

      var quanityString = $(current).find(".product-quantity").first().val();
      var quantity = 0;
      if (quanityString) {
          quantity = parseFloat(quanityString);
      }

      var priceString = $(current).find(".sale-price").first().val();
      var price = 0;
      if (priceString) {
          price = parseFloat(priceString);
      }
      var discountString = parseFloat($(current).first().find(".sale-discount").first().val());
      var discount = 0;
      if (discountString) {
          discount = parseFloat(discountString);
      }

      var taxString = parseFloat($(current).first().find(".purchase-product-tax").first().val());
      var tax = 0;
      if (taxString) {
          tax = parseFloat(taxString);
      }

      var totalAmount = quantity*price;

      var netAmount = (totalAmount + totalAmount*tax/100) - (totalAmount + totalAmount*tax/100) * discount / 100;
      // var netAmount = totalAmount - totalAmount * discount / 100;
      $(current).find(".sale-item-amount").first().val(totalAmount.toFixed(2));
      $(current).find(".sale-item-total").first().val(netAmount.toFixed(2));
      inventorySale.computeNetSaleAmount();
  },

   collectSelectedProducts : function () {
      productBatchList = []
      $("tr.selected-product-row").each(function() {
          if($(this).hasClass(inventoryUtils.batchSelectRowClass)){
            return;
          }

          var rowId = inventoryUtils.getClassWithPrefix(this, inventoryUtils.rowPrefix);
          if(rowId == null){
            return;
          }
          var tokens = rowId.split(inventoryUtils.delimiter);
          var skuId = tokens[1].trim();
          if(skuId == ""){
            return;
          }

          var batchId = tokens[2].trim();
          if(batchId == ""){
              return;
          }

          var quantity = parseFloat($(this).find("input.product-quantity").val());
          var discount = 0;
          if ($(this).find("input.sale-discount").val() != "") {
              discount = parseFloat($(this).find("input.sale-discount").val());
          }

          productBatchList.push({'skuId' : skuId, 'batchId' : batchId, 'quantity' : quantity, 'discount' : discount});
      });

      return productBatchList;

  },

  showProductGroupListModel: function(element){
    var productGroupJson = element.text().trim();
    var productGroup = JSON.parse(productGroupJson);
    var groupName = titleCase(productGroup.productGroupBasicInfo.groupName);
    var tableList = "";
    var index = 1;
    for(var i = 0 ; i < productGroup.productDataQuantityList.length ; i++){
        var quantity = productGroup.productDataQuantityList[i].quantity;
        var productEntry = productGroup.productDataQuantityList[i];
        var productName = productEntry.productMetadata.name;
        var categoryName = productEntry.category.categoryName;
        var brandName = "-";
        if(productEntry.brand != null){
            brandName = productEntry.brand.brandName;
        }
        for(var j =0; j < productEntry.productGroupBatchDataQuantities.length; j++){
          var productBatchEntry = productEntry.productGroupBatchDataQuantities[j];
          var batchName = productBatchEntry.productBatchData.batchName;
          var quantity = productBatchEntry.quantity;
          var discount = productBatchEntry.discount;
          var sellingPrice = productBatchEntry.productBatchData.sellingPrice;
          var availableGroupCount = productBatchEntry.availableGroupCount;
          tableList += "<tr> <th>" + index + "</th> <td> " + productName + "</td> <td>"+batchName+"</td> <td>" + categoryName + "</td> <td> " +brandName + "</td> <td>" + quantity + "</td> <td> " + sellingPrice +"</td>  <td> " + discount +"</td>  <td> " + availableGroupCount + "</td> </tr>";
          var index = index+1;

        }
    }
    $("#groups\\.product-group-name").html(groupName);
    $("#groups\\.product-list-tbody").html(tableList);
    $("#groups\\.product_list_model").modal('toggle');
  },

  bindUpdateProductsGroup: function() {
    $(".update-product-group").on('click', function () {
      $("#groups\\.update\\.search_row").prevAll('tr').remove();

      $(".group-screen").hide();
      $("#groups\\.update_screen").show();
      var productGroupJson = $(this).parent().parent().find("p.product-group-info").text().trim();
      var productGroup = JSON.parse(productGroupJson);

      var groupId = productGroup.productGroupBasicInfo.groupId;
      var groupName = titleCase(productGroup.productGroupBasicInfo.groupName);
      var discount = productGroup.productGroupBasicInfo.discount;
       var description = productGroup.productGroupBasicInfo.description;

      for (var i = 0; i < productGroup.productDataQuantityList.length; i++) {
        var productItem = productGroup.productDataQuantityList[i];
        var batchData = (productItem.productGroupBatchDataQuantities &&
                         productItem.productGroupBatchDataQuantities.length > 0)
                        ? productItem.productGroupBatchDataQuantities[0]
                        : null;

        if (!batchData) continue;

        var batchId = (batchData.productBatchData && batchData.productBatchData.batchId)
                      ? batchData.productBatchData.batchId : '';
        var quantity = (batchData.quantity !== undefined) ? batchData.quantity : 0;
        var itemDiscount = (batchData.discount !== undefined) ? batchData.discount : 0;
        var sellingPrice = (batchData.productBatchData && batchData.productBatchData.sellingPrice)
                           ? batchData.productBatchData.sellingPrice
                           : 0;

        var productName = titleCase(productItem.productMetadata.name);
        var skuId = productItem.productMetadata.skuId;

        var brandName = productItem.brand ? titleCase(productItem.brand.brandName) : "";
        var categoryName = titleCase(productItem.category.categoryName);

        var batchName = batchData.productBatchData.batchName || "Unknown Batch";
        var totalQuantity = batchData.productBatchData.totalQuantity || 0;
        var availabilityLabel = "";
        if (totalQuantity > 0) {
          availabilityLabel = "<span class='badge badge-success'>Available: " + totalQuantity + "</span>";
        } else {
          availabilityLabel = "<span class='badge badge-danger'>Not Available</span>";
        }
        var batchDetails = {productMetadata : productItem.productMetadata , productBatch : batchData.productBatchData}
        var  row = inventoryProductGroup.getRowDetails(true, batchDetails)

        var tableEntry = " <tr class=\"update-grouped-product " + batchId + "\" id=\"" + skuId + "\">" +
                           " <td class=\"update-product-name\" scope=\"row\"> " + productName + " </td>" +
                           "<td>" + batchName + " " + availabilityLabel +
                           "</td>" +
                           "<td>" +
                           " <button type=\"button\" class=\"btn btn-primary btn-sm add-product-batch-details\">+Details</button>" +
                           "</td>" +
                           " <td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quantity\"> </td>" +
                           " <td> <input type=\"number\" class=\"form-control form-control-sm product-price mandatory-table-field positive-number\" placeholder=\"0\" readonly></td>" +
                           " <td> <input type=\"number\" class=\"form-control form-control-sm product-discount percentage-range\" placeholder=\"0\"> </td>" +
                           " <td><input type=\"number\" class=\"form-control form-control-sm product-amount\" placeholder=\"0\" readonly></td>" +
                           " <td> <button type=\"button\" class=\"close delete-group-item\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td>" +
                           " </tr>" + row;

        $("#groups\\.update\\.search_row").before(tableEntry);

        $("#" + skuId + ".update-grouped-product").find(".product-price").first().val(sellingPrice);
        $("#" + skuId + ".update-grouped-product").find(".product-quantity").first().val(quantity);
        $("#" + skuId + ".update-grouped-product").find(".product-discount").first().val(itemDiscount);

        inventoryProductGroup.computeProductTotal($("#" + skuId + ".update-grouped-product"));
      }

      inventoryProductGroup.deleteProductItemEntry();
      inventoryProductGroup.bindGroupProductCostComputeEvent();
      inventoryUtils.removeErrorOnClick();
      inventoryUtils.bindExpandBatchButton();

      $("#groups\\.group-id").text(groupId);
      $("#groups\\.update\\.group-name").val(groupName);
      $("#groups\\.update\\.discount").val(discount);
       $("#groups\\.update\\.description").val(description);
    });
  },

  bindDeleteProductsGroup: function(){
    $(".delete-product-group").on('click', function () {
      var productGroupJson = $(this).parent().parent().find("p.product-group-info").text().trim();
      var productGroup = JSON.parse(productGroupJson);
      var groupId = productGroup.productGroupBasicInfo.groupId;
      var groupName = titleCase(productGroup.productGroupBasicInfo.groupName);
      // var tableList = "";
      // for(var i = 0 ; i < productGroup.productDataQuantityList.length ; i++){
      //     var quantity = productGroup.productDataQuantityList[i].quantity;
      //     var productName = titleCase(productGroup.productDataQuantityList[i].productDetails.productName);
      //     var brandName = titleCase(productGroup.productDataQuantityList[i].productDetails.brand.brandName);
      //     var categoryName = titleCase(productGroup.productDataQuantityList[i].productDetails.category.categoryName);
      //     var sellingPrice = productGroup.productDataQuantityList[i].productDetails.sellingPrice;
      //     var totalQuantity = productGroup.productDataQuantityList[i].productDetails.totalQuantity;
      //     var index = i+1;
      //     tableList += "<tr> <th>" + index + "</th> <td> " + productName + "</td> <td>" + brandName + "</td> <td> " +categoryName + "</td> <td>" +sellingPrice+"</td> <td> " + quantity +"</td> </tr>";
      // }
      $("#groups\\.delete\\.product-group-id").html(groupId);
      $("#groups\\.delete\\.product-group-name").html(groupName);
      // $("#groups\\.delete\\.product-list-tbody").html(tableList);
      $("#groups\\.delete\\.product-group-model").modal('toggle');
    });
  },
  // <tr>
  //   <th scope="row">i</th>
  //   <td>titleCase(productName)</td>
  //   <td>titleCase(brandName)</td>
  //   <td>titleCase(categoryName)</td>
  //   <td>sellingPrice</td>
  //   <td>quantity</td>
  //   <td>totalQuantity</td>
  // </tr>

  registerProductGroupSearchCallback: function(){
      $('#groups\\.search-group').on('click', function () {
          inventoryProductGroup.searchProductGroup();
      });
      $("#groups\\.search-text").on('keyup', function (e) {
        if (e.keyCode == 13) {
            inventoryProductGroup.searchProductGroup();
        }
      });
  },

  searchProductGroup: function(){
      var search_text = $("#groups\\.search-text").val();
      $("#groups\\.search-result").html("");

      ajaxClient.get("/inventory/search-product-groups?text="+search_text, function(data){

          $("#groups\\.search-result").html(data);
          inventoryProductGroup.bindViewProductsGroup();
          inventoryProductGroup.bindUpdateProductsGroup();
          inventoryProductGroup.bindDeleteProductsGroup();
          // bindUpdateProductCallback();
     });
  },

  addNewGroup: function(){
    $(".group-screen").attr("style", "display:none");
    $("#groups\\.add_screen").attr("style", "display:block");
    $("#live-search-row").prevAll('tr').remove();
    inventoryUtils.clearValidationErrorDisplay();
    inventoryProductGroup.dataCache[inventoryUtils.selectedProductsKey] = {};
    inventoryProductGroup.clearFormFields();
  },

  returnToMainScreen: function(){
    $(".group-screen").attr("style", "display:none");
    $("#groups\\.default_screen").attr("style", "display:block");
    inventoryUtils.clearValidationErrorDisplay();
    inventoryProductGroup.dataCache[inventoryUtils.selectedProductsKey] = {};
    inventoryProductGroup.clearFormFields();
  },

  clearFormFields : function () {
    $("#groups\\.group-name").val("");
    $("#groups\\.discount").val("");
    $("#groups\\.description").val("");
    $(".product-batch-details-row").remove();
  },

  //user is "finished typing,"
  doneAddGroupProductTyping: function(resultArea) {
      var search_text = $("#groups\\.search-product").val();
      ajaxClient.get("/inventory/live-stock-search?text=" + search_text, function(data) {
          $(resultArea).html(data);
          inventoryProductGroup.bindClickEventOnAddProductSearchItem();
      });
  },

  //user is "finished typing,"
  doneUpdateGroupProductTyping: function(resultArea) {
      var search_text = $("#groups\\.update\\.search-product").val();
      ajaxClient.get("/inventory/live-stock-search?text=" + search_text, function(data) {
          $(resultArea).html(data);
          inventoryProductGroup.bindClickEventOnUpdateProductSearchItem();
      });
  },

  bindClickEventOnAddProductSearchItem: function() {
      $("#live_search_stock_results tbody tr th").on("click", function() {
          var attrsJson = $(this).parent().find("td.stock-json").text().trim();
          var attrs = JSON.parse(attrsJson);
          var skuId = attrs.skuId;
          var productName = attrs.productName;
          var sellingPrice = parseFloat(attrs.sellingPrice)
          var discountPrice = parseFloat(attrs.discount)
          if($(".grouped-product-item#" + skuId).length != 0) {
            showErrorDialogBox("Product :  " + productName + " already selected. Please add quantity in that. ");
            return;
          }
          $("#groups\\.add\\.search_row").before(" <tr class=\"grouped-product-item\" id=\""+skuId+"\"> <td class=\"purchase_product_name\" scope=\"row\"> "+productName+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quanity\"> </td> <td> <input type=\"number\" class=\"form-control form-control-sm product-price mandatory-table-field positive-number\" placeholder=\"0\" readonly></td> <td> <input type=\"number\" class=\"form-control form-control-sm product-discount mandatory-field percentage-range\" placeholder=\"0\"> </td> <td><input type=\"number\" class=\"form-control form-control-sm product-amount\" placeholder=\"0\" readonly></td> <td> <button type=\"button\" class=\"close delete-group-item \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr> ");

          $("#" + skuId+".grouped-product-item").find(".product-price").first().val(sellingPrice);
          $("#" + skuId+".grouped-product-item").find(".product-discount").first().val(discountPrice);
          $("#groups\\.add\\.search-result").html("");
          $("#groups\\.search-product").val("");
          inventoryProductGroup.deleteProductItemEntry();
          inventoryProductGroup.bindGroupProductCostComputeEvent();
          inventoryProductGroup.removeErrorOnClick();
      });
  },


 bindClickEventOnUpdateProductSearchItem: function() {
  $("#live-search-stock-results tbody tr th").on("click", function() {
    var attrsJson = $(this).parent().find("td.stock-json").text().trim();
    var attrs = JSON.parse(attrsJson);
    var skuId = attrs.productMetadata.skuId;
    var productName = attrs.productMetadata.name;

    var batchData = (attrs.productBatchDataList && attrs.productBatchDataList.length > 0)
                    ? attrs.productBatchDataList[0]
                    : null;
    if (!batchData) {
      showErrorDialogBox("No batch data available for product " + productName);
      return;
    }

    var batchId = batchData.batchId;

    var sellingPrice = batchData.sellingPrice;
    var discountPrice = batchData.discount;
    var discountInPercent = batchData.discountInPercent;
    if(discountPrice != null){
      if(sellingPrice != null && sellingPrice > 0 && !discountInPercent){
        discountPrice = discountPrice * 100/sellingPrice;
      }
    }
    var batchName = batchData.batchName || "";
    var totalQuantity = batchData.totalQuantity || 0;
    var availabilityLabel = "";
    if (totalQuantity > 0) {
      availabilityLabel = "<span class='badge badge-success'>Available: " + totalQuantity + "</span>";
    } else {
      availabilityLabel = "<span class='badge badge-danger'>Not Available</span>";
    }
    var batchDetails = {productMetadata : attrs.productMetadata , productBatch : batchData}
    var  row = inventoryProductGroup.getRowDetails(true, batchDetails)

    if ($("#" + skuId  + ".update-grouped-product").length != 0) {
      showErrorDialogBox("Product : " + productName + " already selected. Please add quantity in that.");
      return;
    }

    var rowString = "  <tr class=\"update-grouped-product " + batchId + "\" id=\"" + skuId +  "\">" +
                    "   <td class=\"update-product-name\" scope=\"row\"> " + productName + " </td>" +
                    "   <td>" + batchName + " " + availabilityLabel +
                    "  </td>" +
                    "  <td>" +
                    "   <button type=\"button\" class=\"btn btn-primary btn-sm add-product-batch-details\">+Details</button>" +
                    "  </td>" +
                    "   <td> <input type=\"number\" class=\"form-control form-control-sm product-quantity mandatory-table-field positive-number\" placeholder=\"Quantity\"> </td>" +
                    "   <td> <input type=\"number\" class=\"form-control form-control-sm product-price mandatory-table-field positive-number\" placeholder=\"0\" readonly></td>" +
                    "   <td> <input type=\"number\" class=\"form-control form-control-sm product-discount percentage-range\" placeholder=\"0\"> </td>" +
                    "   <td> <input type=\"number\" class=\"form-control form-control-sm product-amount\" placeholder=\"0\" readonly></td>" +
                    "   <td> <button type=\"button\" class=\"close delete-group-item\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td>" +
                    " </tr>" + row;;

    $("#groups\\.update\\.search_row").before(rowString);

    $("#" + skuId + ".update-grouped-product").find(".product-price").first().val(sellingPrice);
    $("#" + skuId  + ".update-grouped-product").find(".product-discount").first().val(discountPrice);

    $("#groups\\.update\\.search-result").html("");
    $("#groups\\.update\\.search-product").val("");

    inventoryProductGroup.deleteProductItemEntry();
    inventoryProductGroup.bindGroupProductCostComputeEvent();
     inventoryUtils.bindExpandBatchButton();
    // inventoryProductGroup.removeErrorOnClick();
  });
},

  deleteProductItemEntry: function() {
      $(".delete-group-item").click(function() {
          $(this).parent().parent().remove();
      });
  },

  bindGroupProductCostComputeEvent: function() {
      $(".product-quantity").focus(function() {}).blur(function() {
          inventoryProductGroup.computeProductTotal($(this).parent().parent());
      });

      $(".product-discount").focus(function() {}).blur(function() {
          inventoryProductGroup.computeProductTotal($(this).parent().parent());
      });
  },

  computeProductTotal: function(current) {

      var quanityString = $(current).find(".product-quantity").first().val();
      var quantity = 0;
      if (quanityString) {
          quantity = parseInt(quanityString);
      }

      var discountString = $(current).find(".product-discount").first().val();
      var discount = 0;
      if (discountString) {
          discount = parseInt(discountString);
      }

      var priceString = $(current).find(".product-price").first().val();
      var price = 0;
      if (priceString) {
          price = parseFloat(priceString);
      }
      var totalAmount = quantity*price*(100-discount)/100;
      $(current).find(".product-amount").first().val(totalAmount.toFixed(2));
  },


  collectProductListValues: function(search_tag) {
      products = []
      // var isValid = validateSaleOrderForm();
      // if (!isValid) {
      //     return products;
      // }
      $(search_tag).each(function() {
          var skuId = $(this).attr("id");
          var quantity = $(this).find("input.product-quantity").val();
          var discount = $(this).find("input.product-discount").val();
           var classes = $(this).attr('class').split(/\s+/);
                var batchId = classes.filter(function(className) {
                    return className !== "update-grouped-product";
                })[0];
          var product = {}

          product['skuId'] = skuId;
          product['batchId'] = batchId
          product['quantity'] = quantity;
          product['discount'] = discount;
          products.push(product);
      });
      return products;

  },

  registerAddProductGroupCallback: function() {
      $('#groups\\.add-confirm').on('click', function() {
          $("#groups-add-confirm-modal").modal('toggle');

          products = inventoryProductGroup.collectSelectedProducts();
          if (!(products.length > 0)) {
              alert("Please add atleast one product in group.");
              return;
          }

          // var invalidProductList = validations($(".tr.grouped-product-item"));
          // var invalid = validations($("#add\\.product-group"));
          // if(invalid || invalidProductList){
          //   return;
          // }
          var groupName = $("#groups\\.group-name").val();
          var discount = $('#groups\\.discount').val();
          var description = $('#groups\\.description').val();
          productGroup = {
              'productGroupBasicInfo':{
                    'groupName' : groupName,
                    'discount' : discount,
                    'description' : description
              } ,
              'productGroupItems': products
          }

          ajaxClient.post( "/inventory/add-product-group",  {"productGroup" : JSON.stringify(productGroup)}, function(data) {

              $('#groups\\.status-model-container').html(data);
              $("#groups\\.status_model").modal('toggle');
              inventoryProductGroup.returnToMainScreen();
              inventoryProductGroup.searchProductGroup();
          });
      });
  },

  registerUpdateProductGroupCallback: function() {
      $('#groups\\.update-confirm').on('click', function() {
          $("#groups-update-confirm-modal").modal('toggle');
          products = inventoryProductGroup.collectProductListValues("tr.update-grouped-product");
          if (!(products.length > 0)) {
              return;
          }
          var invalidProductList = inventoryUtils.validations($(".tr.update-grouped-product"));
          var invalid = inventoryUtils.validations($("#update\\.product-group"));
          if(invalid || invalidProductList){
            return;
          }
          var groupId = $("#groups\\.group-id").text().trim();
          var groupName = $("#groups\\.update\\.group-name").val();
          var discount = $('#groups\\.update\\.discount').val();
          var description = $('#groups\\.update\\.description').val();
          productGroup = {
              'productGroupBasicInfo': {
                    'groupId' : groupId,
                    'groupName' : groupName,
                    'discount' : discount,
                    'description' : description
              } ,
             'productGroupItems': products
          }
          ajaxClient.post( "/inventory/update-product-group",  {"productGroup" : JSON.stringify(productGroup)}, function(data) {

              $('#groups\\.status-model-container').html(data);
              $("#groups\\.status_model").modal('toggle');
              inventoryProductGroup.returnToMainScreen();
              inventoryProductGroup.searchProductGroup();
          });
      });
  },


  registerDeleteProductGroupCallback: function() {
      $('#groups\\.delete-confirm').on('click', function() {
          $("#groups\\.delete\\.product-group-model").modal('toggle');
          var groupId = $("#groups\\.delete\\.product-group-id").text().trim();

          ajaxClient.post( "/inventory/delete-product-group/"+groupId, {}, function(data) {

              $('#groups\\.status-model-container').html(data);
              $("#groups\\.status_model").modal('toggle');
              inventoryProductGroup.returnToMainScreen();
              inventoryProductGroup.searchProductGroup();
          });
      });
  },


};
