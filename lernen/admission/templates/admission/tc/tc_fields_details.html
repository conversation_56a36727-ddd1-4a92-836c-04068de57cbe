{% load displaytime %}
{% load json %}

<p style="display:none;" id="tc-generation-student-id"></p>
<p style="display:none;" id="tc-generation-is-tc-number-fixed">{{student_tc_details.tcnumberFixed|jsonstr}}</p>
<p style="display:none" id="tc-generation-student-tc-details">{{student_tc_details|jsonstr}}</p>
<p style="display:none;" id="tc-counter-enable">{{institute_metadata.metaDataPreferences.transferCertificateCounter|jsonstr}}</p>

<div id="student-tc-generation-school-variables" class="card" style="width:100%; height:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">School Variables</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Affiliation Number</label>
        <input type="text" class="form-control" id="tc-affiliation-no" placeholder="Affiliation Number..." {% if student_tc_details.tcSchoolDetails.affiliationNumber is not None %} value="{{student_tc_details.tcSchoolDetails.affiliationNumber}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Dise Code</label>
        <input type="text" class="form-control" id="tc-dise-code" placeholder="Dise Code..." {% if student_tc_details.tcSchoolDetails.diseCode is not None %} value="{{student_tc_details.tcSchoolDetails.diseCode}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>School Code</label>
        <input type="text" class="form-control" id="tc-school-code" placeholder="School Code..." {% if student_tc_details.tcSchoolDetails.schoolCode is not None %} value="{{student_tc_details.tcSchoolDetails.schoolCode}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Book Number</label>
        <input type="text" class="form-control" id="tc-book-no" placeholder="Book Number..." {% if student_tc_details.bookNumber is not None %} value="{{student_tc_details.bookNumber}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Serial No.</label>
        <!-- <input type="text" class="form-control" id="tc-serial-no" placeholder="Serial No..." {% if student_tc_details.tcNumber is not None %} value="{{student_tc_details.tcNumber}}" {% endif %}> -->
        <input type="text" class="form-control" id="tc-serial-no" placeholder="Serial No..." {% if student_tc_details.tcNumber is not None %} value="{{student_tc_details.tcNumber}}" {% endif %} {% if institute_metadata.metaDataPreferences.transferCertificateCounter %} readonly {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>RTE Affiliation Number</label>
        <input type="text" class="form-control" id="tc-rte-affli-no" placeholder="RTE Affiliation Number..." {% if student_tc_details.tcSchoolDetails.rteAffiliationNumber is not None %} value="{{student_tc_details.tcSchoolDetails.rteAffiliationNumber}}" {% endif %}>
      </div>
    </div>
  </div>
</div>


<div id="student-tc-generation-student-variables" class="card" style="width:100%; height:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">Student Variables</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Admission Number</label>
        <input type="text" class="form-control" id="tc-admission-no" placeholder="Admission Number..." {% if student_tc_details.tcStudentDetails.admissionNumber is not None %} value="{{student_tc_details.tcStudentDetails.admissionNumber}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Student Name</label>
        <input type="text" class="form-control" id="tc-student-name" placeholder="Student Name..." {% if student_tc_details.tcStudentDetails.studentName is not None %} value="{{student_tc_details.tcStudentDetails.studentName}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Admission Date</label>
        <input type="text" class="form-control select-date" id="tc-admission-date" placeholder="Admission Date..." {% if student_tc_details.tcStudentDetails.admissionDate is not None and student_tc_details.tcStudentDetails.admissionDate != 0 %} value="{{student_tc_details.tcStudentDetails.admissionDate|print_date}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Admission Class</label>
        <input type="text" class="form-control" id="tc-admission-class" placeholder="Admission Class..." {% if student_tc_details.tcStudentDetails.admissionClass is not None %} value="{{student_tc_details.tcStudentDetails.admissionClass}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-6">
        <label>Father Name/Guardian Name</label>
        <input type="text" class="form-control" id="tc-father-name" placeholder="Father Name/Guardian Name..." {% if student_tc_details.tcStudentDetails.fatherGuardianName is not None %} value="{{student_tc_details.tcStudentDetails.fatherGuardianName}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-6">
        <label>Mother Name</label>
        <input type="text" class="form-control" id="tc-mother-name" placeholder="Mother Name..." {% if student_tc_details.tcStudentDetails.motherName is not None %} value="{{student_tc_details.tcStudentDetails.motherName}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>DOB</label>
        <input type="text" class="form-control select-date" id="tc-dob" placeholder="DOB..." {% if student_tc_details.tcStudentDetails.dob is not None and student_tc_details.tcStudentDetails.dob != 0 %} value="{{student_tc_details.tcStudentDetails.dob|print_date}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Proof Of DOB at the time of admission</label>
        <input type="text" class="form-control" id="tc-proof-of-dob-at-the-time-of-admission" placeholder="Proof of DOB at the time of admission..." {% if student_tc_details.tcStudentDetails.proofOfDoBAtTheTimeOfAdmission is not None %} value="{{student_tc_details.tcStudentDetails.proofOfDoBAtTheTimeOfAdmission}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Category</label>
        <!-- <input type="text" class="form-control" id="tc-category" placeholder="Category..." value="{{student_tc_details.tcStudentDetails.category}}"> -->
        <select class="form-control" id="tc-category">
          <option {% if student_tc_details.tcStudentDetails.category is None %} selected {% endif %}></option>
          <option value="GENERAL" {% if student_tc_details.tcStudentDetails.category == "GENERAL" %} selected {% endif %}>General</option>
          <option value="OBC" {% if student_tc_details.tcStudentDetails.category == "OBC" %} selected {% endif %}>OBC</option>
          <option value="BC" {% if student_tc_details.tcStudentDetails.category == "BC" %} selected {% endif %}>BC</option>
          <option value="SC" {% if student_tc_details.tcStudentDetails.category == "SC" %} selected {% endif %}>SC</option>
          <option value="ST" {% if student_tc_details.tcStudentDetails.category == "ST" %} selected {% endif %}>ST</option>
          <option value="OTHER" {% if student_tc_details.tcStudentDetails.category == "OTHER" %} selected {% endif %}>Other</option>
        </select>
      </div>
      <div class="mb-3 col-md-3">
        <label>Nationality</label>
        <input type="text" class="form-control" id="tc-nationality" placeholder="Nationality..." {% if student_tc_details.tcStudentDetails.nationality is not None %} value="{{student_tc_details.tcStudentDetails.nationality}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-4">
        <label>Primary Education Affiliation Number (P.E.N)</label>
        <input type="text" class="form-control" id="tc-pri-edu-affli-no" placeholder="Primary Education Affiliation Number..." {% if student_tc_details.tcSchoolDetails.primaryEducationAffiliationNumber is not None %} value="{{student_tc_details.tcSchoolDetails.primaryEducationAffiliationNumber}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-4">
        <label>APAAR ID Number</label>
        <input type="text" class="form-control" id="tc-apaar-id-no" placeholder="APAAR ID Number..." {% if student_tc_details.tcStudentDetails.apaarIdNo is not None %} value="{{student_tc_details.tcStudentDetails.apaarIdNo}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-4">
        <label>Living time in current City</label>
        <input type="text" class="form-control" id="tc-living-time-in-currect-city" placeholder="Living time in the Current City..." {% if student_tc_details.tcStudentDetails.livingTimeInCurrectCity is not None %} value="{{student_tc_details.tcStudentDetails.livingTimeInCurrectCity}}" {% endif %}>
      </div>
    </div>
  </div>
</div>


<div id="student-tc-generation-student-variables" class="card" style="width:100%; height:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">Student Last Active Session Variables</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-4">
        <label>Last Active Session Class</label>
        <input type="text" class="form-control" id="tc-last-session-class" placeholder="Last class studied..." {% if student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClass is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClass}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-4">
        <label>Last Active Session Class (In Words)</label>
        <input type="text" class="form-control" id="tc-last-session-class-name-in-words" placeholder="Last class studied (in words)..." {% if student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClassInWords is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClassInWords}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-4">
        <label>Last Active Session Class (In Figures)</label>
        <input type="text" class="form-control" id="tc-last-session-class-name-in-figures" placeholder="Last class studied (in figures)..." {% if student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClassInFigures is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.lastActiveSessionClassInFigures}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-6">
        <label>School/Board Annual Examination last taken with result</label>
        <input type="text" class="form-control" id="tc-last-exam" placeholder="Exam Name..." {% if student_tc_details.tcStudentLastActiveSessionDetails.lastExamTakenWithResult is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.lastExamTakenWithResult}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-6">
        <label>Whether failed, if so once/twice in the same class</label>
        <input type="text" class="form-control" id="tc-last-exam-failed" placeholder="No/Once/Twice..." {% if student_tc_details.tcStudentLastActiveSessionDetails.numberOfTimeExamFailed is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.numberOfTimeExamFailed}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <label>Subjects in the last active session</label>
        <div class="row" id="tc-student-courses-div">
        {% for subject in student_tc_details.tcStudentLastActiveSessionDetails.scholasticCoursesLastActiveSession %}
          <div class="input-group mb-3 col-md-4">
            <input type="text" class="form-control tc-student-courses" placeholder="Subject..." value="{{subject}}">
            <div class="input-group-prepend" style="cursor:pointer;" onclick="transferCertificate.removeTCStudentCourses(this);">
              <span class="input-group-text" aria-hidden="true">x</span>
            </div>
          </div>
        {% endfor %}
        </div>
      </div>
      <div class="mb-3 col-md-12" style="cursor:pointer;" onclick="transferCertificate.addTCStudentCourses();">
        <p style="color:#43a2ad;"><u>Add More Subjects</u></p>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Whether Qualified for promotion to the higher class</label>
        <input type="text" class="form-control" id="tc-getting-promotion" placeholder="Promoting To..." {% if student_tc_details.tcStudentLastActiveSessionDetails.promotionToHigherClass is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.promotionToHigherClass}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>If so to which class</label>
        <input type="text" class="form-control" id="tc-promoting-class" placeholder="If so to which class..." {% if student_tc_details.tcStudentLastActiveSessionDetails.promotingClassName is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.promotingClassName}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>If so to which class (in words)</label>
        <input type="text" class="form-control" id="tc-promoting-class-name-in-words" placeholder="If so to which class (in words)..." {% if student_tc_details.tcStudentLastActiveSessionDetails.promotingClassNameInWords is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.promotingClassNameInWords}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>If so to which class (in figures)</label>
        <input type="text" class="form-control" id="tc-promoting-class-name-in-figures" placeholder="If so to which class (in figures)..." {% if student_tc_details.tcStudentLastActiveSessionDetails.promotingClassNameInFigures is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.promotingClassNameInFigures}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-6">
        <label>Last fees paid</label>
        <input type="text" class="form-control" id="tc-last-fees-paid" placeholder="Last fees paid..." {% if student_tc_details.tcStudentLastActiveSessionDetails.lastFeesPaid is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.lastFeesPaid}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-6">
        <label>Discount given & its nature</label>
        <input type="text" class="form-control" id="tc-discount" placeholder="Discount..." {% if student_tc_details.tcStudentLastActiveSessionDetails.discountWithNature is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.discountWithNature}}" {% endif %}>
      </div>
    </div>
    <div class="row">
      <div class="mb-3 col-md-6">
        <label>Total working days in the session</label>
        <input type="text" class="form-control" id="tc-total-working-days" placeholder="Working days in the session..." {% if student_tc_details.tcStudentLastActiveSessionDetails.totalWorkingDays is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.totalWorkingDays}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-6">
        <label>Total attended days by the student in the session</label>
        <input type="text" class="form-control" id="tc-total-attended-days" placeholder="Attended days in the session..." {% if student_tc_details.tcStudentLastActiveSessionDetails.totalAttendedDays is not None %} value="{{student_tc_details.tcStudentLastActiveSessionDetails.totalAttendedDays}}" {% endif %}>
      </div>
    </div>
  </div>
</div>

<div id="student-tc-generation-school-variables" class="card" style="width:100%; height:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">Other Variables</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-6">
        <label>Whether NCC Cadet/Boy Scout/Girl Guide<br/>(Details may be given)</label>
        <textarea class="form-control" id="tc-ncc-cadet" rows="3" placeholder="Whether NCC Cadet/Boy Scout/Girl Guide...">{% if student_tc_details.tcOtherDetails.nccCadetBoyScoutGirlGuideWithDetails is not None %} {{student_tc_details.tcOtherDetails.nccCadetBoyScoutGirlGuideWithDetails}}  {% endif %}
        </textarea>
      </div>
      <div class="mb-3 col-md-6">
        <label>Co-curricular activities usually took part in<br/>(Mention achievement level therein)</label>
        <textarea class="form-control" id="tc-cocurricular-activities" rows="3" placeholder="Co-curricular activities usually took part in...">{% if student_tc_details.tcOtherDetails.coCurricularActivities is not None %} {{student_tc_details.tcOtherDetails.coCurricularActivities}} {% endif %}
        </textarea>
      </div>
    </div>
  </div>
</div>


<div id="student-tc-generation-school-variables" class="card" style="width:100%; height:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">Relieving Variables</h5>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Code of Conduct</label>
        <input type="text" class="form-control" id="tc-code-of-conduct" placeholder="Code of Conduct..." {% if student_tc_details.tcStudentRelievingDetails.codeOfConduct is not None %} value="{{student_tc_details.tcStudentRelievingDetails.codeOfConduct}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>TC Generation Date</label>
        <input type="text" class="form-control select-date" id="tc-generation-date" placeholder="TC Generation Date..." {% if student_tc_details.tcGenerationDate is not None and student_tc_details.tcGenerationDate != 0 %} value="{{student_tc_details.tcGenerationDate|print_date}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Relieve Date</label>
        <input type="text" class="form-control select-date" id="tc-relieve-date" placeholder="Relieve Date..." {% if student_tc_details.tcStudentRelievingDetails.relieveDate is not None and student_tc_details.tcStudentRelievingDetails.relieveDate != 0 %} value="{{student_tc_details.tcStudentRelievingDetails.relieveDate|print_date}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-3">
        <label>Relieve Reason</label>
        <input type="text" class="form-control" id="tc-relieve-reason" placeholder="Relieve Reason..." {% if student_tc_details.tcStudentRelievingDetails.relieveReason is not None %} value="{{student_tc_details.tcStudentRelievingDetails.relieveReason}}" {% endif %}>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-md-6">
        <label>Date of Application of Certificate</label>
        <input type="text" class="form-control select-date" id="tc-date-of-application-of-certificate" placeholder="Date of Application of Certificate..." {% if student_tc_details.tcStudentRelievingDetails.dateOfApplicationOfCertificate is not None and student_tc_details.tcStudentRelievingDetails.dateOfApplicationOfCertificate != 0 %} value="{{student_tc_details.tcStudentRelievingDetails.dateOfApplicationOfCertificate|print_date}}" {% endif %}>
      </div>
      <div class="mb-3 col-md-6">
        <label>Date on which pupils name was stuck off the rolls of the school</label>
        <input type="text" class="form-control select-date" id="tc-date-pupils-name-stuck-off-the-rolls" placeholder="Date on which pupils name was stuck off the rolls of the school..." {% if student_tc_details.tcStudentRelievingDetails.datePupilsNameStuckOffTheRolls is not None and student_tc_details.tcStudentRelievingDetails.datePupilsNameStuckOffTheRolls != 0 %} value="{{student_tc_details.tcStudentRelievingDetails.datePupilsNameStuckOffTheRolls|print_date}}" {% endif %}>
      </div>
    </div>

    <div class="row">
      <div class="mb-3 col-md-12">
        <label>Remarks</label>
        <textarea class="form-control" id="tc-remarks" rows="3" placeholder="Remarks..." >{% if student_tc_details.tcStudentRelievingDetails.remarks is not None %} {{student_tc_details.tcStudentRelievingDetails.remarks}} {% endif %}
        </textarea>
      </div>
    </div>
  </div>
</div>

<div id="student-tc-previous-session-detail-summary-variables" class="card" style="width:100%; background-color:#fff;">
  <div class="card-header" style="padding-bottom:0px;">
    <h5 style="padding-bottom:0px;color:#43a2ad;">Student Previous Session Details</h5>
  </div>
  <div class="card-body">
    <div class="row" id="tc-previous-session-detail-summary-list-div">
      {% for details in student_tc_details.tcPreviousSessionDetailSummaryList %}
      <div class="col-md-12 mb-3 tc-previous-session-detail-summary-list">
        <div class="row">
          <div class="mb-3 col-md-2">
            <label>Previous Admission Class</label>
            <input type="text" class="form-control" placeholder="Previous AdmissionClass..."  id="tc-previous-admission-class" value="{{details.previousAdmissionClass}}">
          </div>
          <div class="mb-3 col-md-2">
            <label>Previous Admission Date</label>
            <input type="text" class="form-control select-date" placeholder="Admission Date..." id="tc-previous-admission-date" {% if details.previousAdmissionDate is not None %} value="{{details.previousAdmissionDate|print_date}}" {% endif %}>
          </div>
          <div class="mb-3 col-md-2">
            <label>Promotion Class</label>
            <input type="text" class="form-control" placeholder="Promotion Class..."  id="tc-admission-promotion-class" value="{{details.admissionPromotionClass}}">
          </div>
          <div class="mb-3 col-md-3">
            <label>Promotion Date</label>
            <input type="text" class="form-control select-date" placeholder="Promotion Date..." id="tc-admission-promotion-date" {% if details.admissionPromotionDate is not None %} value="{{details.admissionPromotionDate|print_date}}" {% endif %}>
          </div>
          <div class="mb-3 col-md-3">
            <label>Date of Passing from School</label>
            <input type="text" class="form-control select-date" placeholder="Date of Passing from School..." id="tc-date-of-passing-from-school" {% if details.dateOfPassingFromSchool is not None %} value="{{details.dateOfPassingFromSchool|print_date}}" {% endif %}>
          </div>
        </div>
        <div class="row">
          
          <div class="mb-3 col-md-2">
            <label>No. of Meeting</label>
            <input type="text" class="form-control" placeholder="No. of Meeting..." id="tc-no-of-meeting" value="{{details.noOfMeeting}}">
          </div>
          <div class="mb-3 col-md-2">
            <label>No of Present</label>
            <input type="text" class="form-control" placeholder="No. of Present..." id="tc-no-of-present" value="{{details.noOfPresent}}">
          </div>
          <div class="mb-3 col-md-3">
            <label>Result and Division in Final Exam</label>
            <input type="text" class="form-control" placeholder="Result and Division in Final Exam..." id="tc-result-division-final-exam" value="{{details.resultDivisionFinalExam}}">
          </div>
          
          <div class="mb-3 col-md-2">
            <label>Subjects taken</label>
            <input type="text" class="form-control" placeholder="Subjects taken..." id="tc-subjects-taken" value="{{details.subjectsTaken}}">
          </div>
          <div class="mb-3 col-md-2">
            <label>Rank in Class</label>
            <input type="text" class="form-control" placeholder="Rank in Class..." id="tc-rank-in-class" value="{{details.rankInClass}}">
          </div>
        </div>
        <div class="row">
          <div class="mb-3 col-md-2">
            <label>Medium</label>
            <input type="text" class="form-control" placeholder="Medium..." id="tc-medium" value="{{details.medium}}">
          </div>
            <div class="mb-3 col-md-4">
            <label>Duration: Admission date to the end of this session</label>
            <input type="text" class="form-control" placeholder="Admission date to the end of this session..." id="tc-duration-in-session" value="{{details.duration}}">
          </div>
          <div class="mb-3 col-md-3">
            <label>Conduct and Work in session</label>
            <input type="text" class="form-control" placeholder="Conduct and Work in Session..." id="tc-conduct-and-work-in-session" value="{{details.conductAndWorkInSession}}">
          </div>
        </div>
        <div class="col-md-12" style="cursor:pointer;" onclick="transferCertificate.removeTCStudentCourses(this);">
          <p style="color: #FF0000; text-align: right;"><u>Delete Session Detail</u></p>
        </div>
      </div>
      {% endfor %}
    </div>
    <div class="col-md-12" style="cursor:pointer;" onclick="transferCertificate.addTCStudentPrevSessionDetails();">
      <p style="color:#43a2ad;"><u>Add More Session Details</u></p>
    </div>

</div>
</div>
