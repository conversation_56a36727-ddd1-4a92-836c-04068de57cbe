{% load json %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
         <p style="display:none;" class="currect-session-json">{{current_session|jsonstr}}</p>
        <h3>REPORTS</h3>
    </div>
</div>
<br>
<div>
    <div class="row">
      <div class="col-sm-4" onclick="studyTrackerReport.resetPopUp();">
        <div id="study-tracker-report-with-date-range" class="card-hover bg-light report-card report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Daily Study Tracker</strong></h4>
            <p class="card-body" style="color:#6c757d">Effortlessly assess Study Tracker for selected class and date</p>
          </div>
        </div>
      </div>
      <div class="col-sm-4" onclick="studyTrackerReport.resetPopUp();">
        <div id="study-tracker-monthly-report-with-date-range" class="card-hover bg-light report-card report-card">
          <div class="card" style="margin-bottom:0px;">
            <h4 class="card-header"><strong>Monthly Avg Study Tracker</strong></h4>
            <p class="card-body" style="color:#6c757d">Effortlessly assess Study Tracker for selected class and date range</p>
          </div>
        </div>
      </div>
    </div>


    <div class="modal fade" id="study-tracker-report-with-date-range-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md" role="document">
            <div class="modal-content report-field-container">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Daily Study Tracker report </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Academic Session*</label>
                            <select class="form-control report-academic-session mandatory-field">
                            {% for academic_year in academic_years %}
                                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                            {% endfor %}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select Date*</label>
                            <input type="text" class="form-control select-date study-tracker-start-date mandatory-field" placeholder="Study Tracker Date"/>
                        </div>
                    </div>

                    <div class="form-row class-study-tracker-div">
                        <div class="form-group col-md-6">
                            <p style="display:none;" class="standards-json">{{standards|jsonstr}}</p>
                            <label>Select Standard*</label>
                            <select class="custom-select mr-sm-4 study-tracker-student-class mandatory-field" id="study-tracker-class-popup">
                            <option value="" selected="">Select Standard</option>
                            {% for standard in standards %}
                                <option value={{standard.standardId}}>{{standard.displayName}}</option>
                            {% endfor%}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select Section</label>
                            <select class="form-control mr-sm-4 study-tracker-section select2" id="study-tracker-popup-class-section" multiple data-style="bg-white border" title="Select Section">
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                        <label>Couse Type*</label>
                        <select class="form-control mr-sm-4 select2 course-type mandatory-field" id="study-tracker-popup-course-type" title="Select Course Type" data-style="bg-white border" multiple data-toggle="select2">
                            <option value="SCHOLASTIC" selected>SCHOLASTIC</option>
                        </select> 
                        </div>
                    </div>
                </div>
                <p style="display:none;" class="report-type">DAILY_STUDY_TRACKER</p>
                <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
                        <button type="button" class="btn btn-primary generate-report">View Report</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="study-tracker-monthly-report-with-date-range-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md" role="document">
            <div class="modal-content report-field-container">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">Monthly Avg Study Tracker report</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Academic Session*</label>
                            <select class="form-control report-academic-session mandatory-field">
                            {% for academic_year in academic_years %}
                                <option value={{academic_year.academicSessionId}}>{{academic_year.displayName}}</option>
                            {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Select Start Date*</label>
                            <input type="text" class="form-control select-date study-tracker-start-date mandatory-field" placeholder="Study Tracker Date"/>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select End Date*</label>
                            <input type="text" class="form-control select-date study-tracker-end-date mandatory-field" placeholder="Study Tracker Date"/>
                        </div>
                    </div>

                    <div class="form-row class-study-tracker-div">
                        <div class="form-group col-md-6">
                            <p style="display:none;" class="standards-json">{{standards|jsonstr}}</p>
                            <label>Select Standard*</label>
                            <select class="custom-select mr-sm-4 study-tracker-student-class mandatory-field">
                            <option value="" selected="">Select Standard</option>
                            {% for standard in standards %}
                                <option value={{standard.standardId}}>{{standard.displayName}}</option>
                            {% endfor%}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select Section</label>
                            <select class="form-control mr-sm-4 study-tracker-section select2" multiple data-style="bg-white border" title="Select Section">
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                        <label>Couse Type*</label>
                        <select class="form-control mr-sm-4 select2 course-type mandatory-field" title="Select Course Type" data-style="bg-white border" multiple data-toggle="select2">
                            <option value="SCHOLASTIC" selected>SCHOLASTIC</option>
                        </select> 
                        </div>
                    </div>
                </div>
                <p style="display:none;" class="report-type">MONTHLY_AVG_STUDY_TRACKER</p>
                <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" >Close</button>
                        <button type="button" class="btn btn-primary generate-report">View Report</button>
                </div>
            </div>
        </div>
    </div>
</div>

