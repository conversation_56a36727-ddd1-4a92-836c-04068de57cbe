var STUDENT_ASSIGNED_COURSES_LIST = "student-assigned-courses-list";
var STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-student-assignment";
var BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-bulk-student-assignment";

$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    // homePage.initHomePage();
    courses.loadCourseConfigurationPage();
    paymentReminder.readPaymentState();
});

var menuLoader = {
  
  registerSidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      // menuLoader.registerHomeMenu();
      menuLoader.registerCourseConfigurationMenu();
      menuLoader.registerCourseAssignmentMenu();
      menuLoader.registerReportsNav();
  },

  registerHomeMenu : function () {
      $('#homeNav').on('click', function() {
          homePage.loadHomePage();
      });
  },

  registerCourseConfigurationMenu : function () {
      $('#configureCourseNav').on('click', function() {
          courses.loadCourseConfigurationPage();
      });
  },

  registerCourseAssignmentMenu : function () {
      $('#assignCourseNav').on('click', function() {
          courseAssignment.loadCourseAssignmentPage();
      });
  },

  registerReportsNav : function () {
      $('#reportCourseNav').on('click', function() {
          courseReports.loadReportsPage();
      })
  },

};

var homePage =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/courses/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/courses/session-home/"+academicSessionId, function(data) {
          $("#courses-dashboard-session-content").html(data);
          homePage.displayDashboardContent();
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

   displayDashboardContent : function () {

   },
};

var courses = {
  dataCache : {},

  initDataCache: function () {
    var standards = readJson('#add-courses-standards');
    courses.dataCache = {};
    courses.dataCache.standards = standards;
    courses.updateDataCacheOnNewConfiguredClass();
    courses.dataCache.count = 0;
  },

  updateDataCacheOnNewConfiguredClass : function () {
    courses.dataCache.configuredStandards = [];
    courses.dataCache.configuredClassCoursesMap = {};
    var configuredClassCourses  = readJson('#configured-class-courses-info');
    var configuredStandards = [];
    var configuredClassCoursesMap = {};
    for(var i = 0; i < configuredClassCourses.length; i++){
       configuredStandards.push(configuredClassCourses[i].standard);
       configuredClassCoursesMap[configuredClassCourses[i].standard.standardId] = configuredClassCourses[i];
    }
    courses.dataCache.configuredStandards = configuredStandards;
    courses.dataCache.configuredClassCoursesMap = configuredClassCoursesMap;
  },

  loadCourseConfigurationPage : function (){
      ajaxClient.get("/courses/class-course-config", function(data) {
          $("#main-content").html(data);
          courses.initDataCache();
          courses.bindStandardChangeEvent();
          academicSessionHandler.bindSessionChangeEvent(courses.changeSession);
      });
  },

  loadCourseConfiguredClasses : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/courses/configured-class-courses/"+academicSessionId, function(data) {
        $("#configured-class-courses-list").html(data);
        courses.updateDataCacheOnNewConfiguredClass();
    });
  },

  changeSession: function() {
     courses.loadCourseConfiguredClasses();
  },
  populateAddClassCoursesModal : function () {
      $("#error-reason").attr("style", "color:red; display:none;");
      $("#configure-courses-class-list").attr("style", "");
      var academicYear = academicSessionHandler.getSelectedSession();
      $("#add-class-courses-session-display").val(academicYear.displayName);
      var standards = courses.dataCache.standards;
      var configuredStandards = courses.dataCache.configuredStandards;
      var configuredStandardsMap = {};
      for(var i = 0; i < configuredStandards.length; i++){
        configuredStandardsMap[configuredStandards[i].standardId] = configuredStandards[i];
      }
      var configurableStandards = [];
      for(var i = 0; i < standards.length; i++){
        if(!(standards[i].standardId in configuredStandardsMap)){
            configurableStandards.push(standards[i]);
        }
      }
      var selectOptions = "<option value=\"\"> select class</option>";
      for(var i = 0; i < configurableStandards.length; i++){
         selectOptions+= "<option value=\""+configurableStandards[i].standardId+"\">"+ configurableStandards[i].displayName + "</option>";
      }
      $("#configure-courses-class-list").html(selectOptions);

      var courseRow = courses.getBlankCourseRow();
      var courseInputDiv = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th scope=\"col\">Mandatory</th> <th scope=\"col\">Course Type</th> <th scope=\"col\">Course Name</th><th scope=\"col\">Sequence</th> </tr> </thead> <tbody> "+courseRow+" <tr id=\"new-course-add-row-bottom\" style=\"display:none;\"></tr> </tbody> </table> </div> <div class=\"col-md-1\"> </div> </div>";

      $("#add-class-courses-input-div").html(courseInputDiv);
  },

  getBlankCourseRow : function (actionType) {
      courses.dataCache.count++;
      var rowClass =  "new-course-row"
      var customSwitchId = "customSwitch" + courses.dataCache.count;

      return "<tr class=\""+rowClass+"\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\" checked> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div> </td> <td> <select id=\"\" class=\"form-control form-control-sm mandatory-field course-type\"> <option value=\"SCHOLASTIC\">Scholastic</option> <option value=\"COSCHOLASTIC\">Co-Scholastic</option> </select> </td> <td><input type=\"text\" class=\"form-control form-control-sm add-course-name\" placeholder=\"Course Name...\"></td><td><input type=\"number\" class=\"form-control form-control-sm add-course-sequence\" placeholder=\"Course Sequence...\" style=\"width: 75px;\" onChange=\"courses.validateCourseSequence(this,false)\"></td> <td> <button type=\"button\" onclick=\"courses.deleteCourseRow(this)\" class=\"close delete-course-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";

  },

  validateCourseSequence : function(data,update){
    var coursesList = [];
    if(update){
      $("tr.update-course-row").each(function() {
            var courseSequence = $(this).find('.update-course-sequence').val().trim();
            coursesList.push(courseSequence);
      });
      }
    else{
         $("input.add-course-sequence").each(function() {
               var courseSequence = $(this).val().trim();
               coursesList.push(courseSequence);
         });
         }
    coursesFilteredList = []
    coursesList.forEach(function(value){
      if(value!=''){
        coursesFilteredList.push(value);
      }
    });

    var courseSet = Array.from(new Set(coursesList));
    coursesFilteredSet = []
    courseSet.forEach(function(value){
      if(value!=''){
        coursesFilteredSet.push(value);
      }
    });

    if((coursesFilteredList.length>1) && (coursesFilteredList.length != coursesFilteredSet.length)){
      alert("Duplicate Sequencing is not allowed");
      $(data).val("")
      return;
    }
  },

   validateSequence : function(data,update){
    var coursesList = [];
    if(update){
      $("tr.update-course-row").each(function() {
            var courseSequence = $(this).find('.course-sequence').val().trim();
      });
      }
   else{
      $("input.add-course-sequence").each(function() {
            var courseSequence = $(this).val().trim();
      });
      }


    coursesFilteredList = []
    coursesList.forEach(function(value){
      if(value!=''){
        coursesFilteredList.push(value);
      }
    });

    var courseSet = Array.from(new Set(coursesList));
    coursesFilteredSet = []
    courseSet.forEach(function(value){
      if(value!=''){
        coursesFilteredSet.push(value);
      }
    });

    if((coursesFilteredList.length>1) && (coursesFilteredList.length != coursesFilteredSet.length)){
      if(update){
       $("#update-class-courses-modal").modal('toggle');
      }
      else{
        $("#add-class-courses-modal").modal('toggle');
      }
      alert("Duplicate Sequencing is not allowed");
      $(data).val("")
      return;
    }
  },
  addNewCourseRow : function () {

     var bottomRowId = "add-course-add-row-bottom";
      if(bottomRowId == null){
        return;
      }
      $("#"+bottomRowId).before(courses.getBlankCourseRow());

  },

  deleteCourseRow(ref) {
      $(ref).parent().parent().remove();
  },

  bindStandardChangeEvent: function (){
    $('#configure-courses-class-list').on('change', function () {
      var standardId = $(this).find(':selected').val().trim();
      if(standardId !== undefined && standardId !== ''){
        $("#error-reason").attr("style", "color:red; display:none;");
      }
    });
  },

  submitNewClassCourses : function () {
        var invalid = validateMandatoryFields($("#add-class-courses-modal"));

        if(invalid) {
          $("#error-reason").attr("style", "color:red; display:block;");

          return;
        }
        $("#add-class-courses-modal").modal('toggle');
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var standardId = $("#configure-courses-class-list").find(':selected').val().trim();

          var coursesList = [];
          $("tr.new-course-row").each(function() {
                var mandatory = $(this).find('.custom-control-input:checkbox:checked').length > 0;
                var courseType = $(this).find('.course-type').find(':selected').val().trim();
                var courseName = $(this).find('.course-name').val().trim();
                var courseSequence = $(this).find('.course-sequence').val().trim();
                if(courseSequence == ""){
                  courseSequence = null;
                }
                coursesList.push({'courseType': courseType, 'courseName' : courseName, 'mandatory' : mandatory,'sequence':courseSequence});
          });
          var classCoursesPayload = {'academicSessionId': academicSessionId, 'standardId' : standardId, 'courses' : coursesList};
          ajaxClient.post("/courses/add-class-courses", {'classCoursesPayload' : JSON.stringify(classCoursesPayload)}, function(data){
              $("#course-page-status-modal-container").html(data);
              $("#course-page-status-modal").modal({backdrop: 'static', keyboard: false});
              courses.loadCourseConfiguredClasses();
              courses.dataCache.count = 0;
         });

  },

  deleteStandardCoursesWithError : function(standardId) {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/courses/courses-with-error/"+standardId+"/"+academicSessionId, function(data) {
          $("#courses-with-error-message-div").html(data);
          courses.populateDeleteClassCoursesModal(standardId)
      });
  },


  populateDeleteClassCoursesModal : function (standardId) {
          var academicYear = academicSessionHandler.getSelectedSession();
          var courseErrorMap = JSON.parse($("#courses-with-error-message").text());
          $("#delete-class-courses-modal").modal('toggle')
          $("#delete-class-courses-session-display").val(academicYear.displayName);
          var configuredClassCourses = courses.dataCache.configuredClassCoursesMap[standardId];
          $("#delete-class-courses-class-display").val(configuredClassCourses.standard.displayName);
          $("#delete-class-courses-standrad-id").val(configuredClassCourses.standard.standardId);
          var viewCourseDivStartContent = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th style=\"vertical-align: middle; text-align: center; padding-left: 48px; padding-bottom: 30px;\"> <input type=\"checkbox\" class=\"form-check-input course-select-all-checkbox\" onclick=\"courses.selectAllCourses(this)\"></th> <th scope=\"col\">Course Type</th> <th scope=\"col\">Course Name</th> <th scope=\"col\">Sequence</th>  <th scope=\"col\"></th></tr> </thead> <tbody> ";
          for(var i = 0 ; i < configuredClassCourses.courses.length; i++){
             courses.dataCache.count += 1;
             var course = configuredClassCourses.courses[i];
             var checkedField = "";
             var defaultValueDropDown = "";
             var courseid= course.courseId;
             if(course.mandatory){
               checkedField = "checked";

             }
             var errorMessages = courseErrorMap[courseid];

             var customSwitchId = "customSwitch" + courses.dataCache.count;
             var courseRow = "<tr class=\"delete-course-row\"><td><div class=\"custom-control courses-remove\"><input type=\"checkbox\" class=\"course-id\" name=\"\" value=\"\" " + (errorMessages.length == 0 ? "" : "disabled") + "></div></td><td><select id=\"\" class=\"form-control form-control-sm mandatory-field course-type\" disabled><option value=\"SCHOLASTIC\" ~selectScholastic~ disabled>Scholastic</option><option value=\"COSCHOLASTIC\" ~selectCoscholastic~ disabled>Co-Scholastic</option></select></td><td><p class=\"bulk-course-id\" style=\"display:none;\">" + courseid + "</p><input type=\"text\" class=\"form-control form-control-sm delete-rename-manage-course\" placeholder=\"Course Name...\" value=\"" + course.courseName + "\" disabled></td><td><input type=\"number\" class=\"form-control form-control-sm course-sequence\" placeholder=\"Course Sequence...\" style=\"width: 75px;\" value=\"" + course.sequence + "\" disabled></td><td style=\"vertical-align: center; padding-top : 20px;\"><a onclick=\"courses.toggleErrorDetails(this,'" + courseid + "')\" style=\"" + (errorMessages.length == 0 ? "display: none;" : "color: red;") + "\"><u><p class=\"pb-0\" style=\"font-size: 10px; \">Can't Delete<i class=\"bi bi-chevron-down toggle-arrow\" style=\"margin-left: 5px;\"></i></p></u></a></td></tr>";


             if((course.courseTypeDisplay).includes("Co-Scholastic")) {
               courseRow = courseRow.replace("~selectScholastic~", "");
               courseRow = courseRow.replace("~selectCoscholastic~", "selected");
             }
             else {
               courseRow = courseRow.replace("~selectScholastic~", "selected");
               courseRow = courseRow.replace("~selectCoscholastic~", "");
             }

          if (errorMessages) {
              courseRow += "<tr class=\"error-row\" id=\"" + courseid + "\" style=\"display:none;\">"
                         + "<td colspan=\"5\" style=\"text-align: center; padding: 10px; padding-left: 35px\">"
                         + "<div class=\"card-border\" style=\"display: inline-block; width: 85%; padding: 10px; \">"
                         + "<div style=\"font-size: 12px; margin-bottom: 10px; text-align: left;\">"
                         + "Please resolve the below issues before deleting this course."
                         + "</div>"
                         + "<ul style=\"margin: 0; padding-left: 15px; list-style-type: disc; text-align: left;\">";

              errorMessages.forEach(function(error) {
                  const parts = error.split(":");
                  let key = "";
                  let value = "";
                  if (parts.length > 0 && parts[0] !== undefined) {
                    key = parts[0].trim();
                  }
                  if (parts.length > 1 && parts[1] !== undefined) {
                    value = parts[1].trim();
                  }
                  courseRow += "<li style=\"font-size: 12px; margin: 5px 0;\">" + "<strong style=\'font-weight: 800;\'>" + key + "</strong>" + ": " +  value + "</li>";
              });

              courseRow += "</ul>"
                         + "</div>"
                         + "</td>"
                         + "</tr>";
          }

                    viewCourseDivStartContent += courseRow;
                }

                viewCourseDivStartContent += "<tr id=\"update-course-add-row-bottom\" style=\"display:none;\"></tr></tbody></table></div><div class=\"col-md-1\"></div></div>";
                $("#delete-class-courses-input-div").html(viewCourseDivStartContent);
            },

     toggleErrorDetails : function(element, courseId) {
            $("#" + courseId).toggle();
            var icon = $(element).find(".toggle-arrow");
            if (icon.hasClass("bi-chevron-down")) {
               icon.removeClass("bi-chevron-down").addClass("bi-chevron-up");
            } else {
                icon.removeClass("bi-chevron-up").addClass("bi-chevron-down");
           }
    },


   deleteClassCourses : function () {
           $("#delete-class-courses-modal").modal('toggle');
           var course_info_dict = courses.getDictionaryCourseInfos();
           var academicSessionId = academicSessionHandler.getSelectedSessionId();
           var standardId = $('#delete-class-id').val();

           options = "";
           checkedCourse = course_info_dict[0];
           unCheckedCourse = course_info_dict[1];
           if(checkedCourse.length == 0){
             $('#delete-manage-course-modal').modal('toggle');
             showErrorDialogBox("Please Select atleast one course to delete");
             return
           }

           if(unCheckedCourse.length!=0){
             html1 = "";
             for(var i=0;i< unCheckedCourse.length;i++){
                  html1 = "<option value="+unCheckedCourse[i].courseId +">"+unCheckedCourse[i].courseName+"</option>";
                  options+=html1;
             }
           }
           $('#delete-manage-course-modal').modal('toggle');

           var course_ids = [];
           var checkedCourseIds = [];
           for(var i=0;i< checkedCourse.length; i++){
              courseId = checkedCourse[i].courseId;
              courseName = checkedCourse[i].courseName;
              course_ids.push(courseId)
              checkedCourseIds.push(courseName);
           }

           course_ids = course_ids.join(",");
           courses.dataCache.courseIds = course_ids
               html = "Do you wish to delete the selected courses?";
               $('#delete-standard-course-manage-id').html(html);
               $('#delete-id-manage-course-modal').modal('toggle');
        },

   deleteStudentCourseDetails : function(){
         $('#delete-id-manage-course-modal').modal('toggle');

         course_ids = courses.dataCache.courseIds;
         var academicSessionId = academicSessionHandler.getSelectedSessionId();
         var standardId =   $('#delete-class-courses-standrad-id').val().trim();

         ajaxClient.post("/courses/delete-class-courses/"+standardId+"/"+academicSessionId+"/"+course_ids,{},function(data){
         $("#course-page-status-modal-container").html(data);
         $("#course-page-status-modal").modal('toggle');
         courses.loadCourseConfiguredClasses();
    });
  },

  selectAllCourses: function(selectAllCheckbox) {
      if (selectAllCheckbox.checked) {
          $(".course-id:not(:disabled)").prop('checked', true);
          return;
      }
          $(".course-id:not(:disabled)").prop('checked', false);
  },

   getDictionaryCourseInfos : function(){
        var finalList = [];
        var unCheckedCourseList = [];
        var checkedCourseList = [];

          $("tr.delete-course-row").each(function() {
              if(!$(this).find('input.course-id').is(":checked")){
                courseName = $(this).find('input.delete-rename-manage-course').val();
                courseId = $(this).find('p.bulk-course-id').first().text().trim();
                course_dict = {"courseId":courseId,"courseName":courseName}
                unCheckedCourseList.push(course_dict);
              }
              else{
                courseName = $(this).find('input.delete-rename-manage-course').val();
                courseId = $(this).find('p.bulk-course-id').first().text().trim();
                course_dict = {"courseId":courseId,"courseName":courseName}
                checkedCourseList.push(course_dict);
              }
          });

          finalList.push(checkedCourseList);
          finalList.push(unCheckedCourseList);
          return finalList;
      },

   populateUpdateClassCoursesModal : function (standardId) {
          var academicYear = academicSessionHandler.getSelectedSession();
          $("#update-class-courses-session-display").val(academicYear.displayName);
          var configuredClassCourses = courses.dataCache.configuredClassCoursesMap[standardId];
          $("#update-class-courses-class-display").val(configuredClassCourses.standard.displayName);
          $("#update-class-courses-standrad-id").val(configuredClassCourses.standard.standardId);

          var viewCourseDivStartContent = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th scope=\"col\">Mandatory</th> <th scope=\"col\">Course Type</th> <th scope=\"col\">Course Name</th> <th scope=\"col\">Sequence</th>  </tr> </thead> <tbody> ";

          for(var i = 0 ; i < configuredClassCourses.courses.length; i++){
             courses.dataCache.count += 1;
             var course = configuredClassCourses.courses[i];
             var checkedField = "";
             var defaultValueDropDown = "";
             var courseid=course.courseId
             if(course.mandatory){
               checkedField = "checked";
             }
             var customSwitchId = "customSwitch" + courses.dataCache.count;
             var courseRow = "<tr class=\"update-course-row\"> <td> <div class=\"update-custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\" " + checkedField + " ><label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div>  </td> <td><select id=\"\" class=\"form-control form-control-sm mandatory-field update-course-type\"><option value=\"SCHOLASTIC\" ~selectScholastic~>Scholastic</option> <option value=\"COSCHOLASTIC\" ~selectCoscholastic~>Co-Scholastic</option> </select> </td> <td><p class=\"course-id\" style=\"display:none;\">"+courseid +"</p><input type=\"text\" class=\"form-control form-control-sm update-course-name\" placeholder=\"Course Name...\" value = \""+course.courseName+"\" </td>  <td><input type=\"number\" class=\"form-control form-control-sm update-course-sequence\" placeholder=\"Sequence...\" style=\"width: 105px;\" value = \""+course.sequence+"\" onChange=\"courses.validateCourseSequence(this,true)\" ></td> <td> <button type=\"button\" onclick=\"courses.deleteCourseRow(this)\" class=\"close delete-course-row\" aria-label=\"Close\"> </button> </td> </tr>";

             if((course.courseTypeDisplay).includes("Co-Scholastic")) {
               courseRow = courseRow.replace("~selectScholastic~", "");
               courseRow = courseRow.replace("~selectCoscholastic~", "selected");
             }
             else {
               courseRow = courseRow.replace("~selectScholastic~", "selected");
               courseRow = courseRow.replace("~selectCoscholastic~", "");
             }
             viewCourseDivStartContent += courseRow;
          }
          viewCourseDivStartContent += " <tr id=\"update-course-add-row-bottom\" style=\"display:none;\"></tr> </tbody> </table> </div> <div class=\"col-md-1\"> </div> </div>";
          $("#update-class-courses-input-div").html(viewCourseDivStartContent);
       },

       updateClassCourses : function () {
             var academicSessionId = academicSessionHandler.getSelectedSessionId();
             var standardId = $('#update-class-courses-standrad-id').val().trim();
             var i=0;
             var course = courses.dataCache.configuredClassCoursesMap[standardId];
             var coursesList = [];
             var courseName=[];
             courseName= courses.getUpdateCourse();
             var unique = Array.from(new Set(courseName));
                 if(courseName.length != unique.length){
                   alert("Duplicate Class Courses are not allowed");
                   return
                 }

             $("tr.update-course-row").each(function() {
                   var mandatory = $(this).find('.custom-control-input:checkbox:checked').length > 0;
                   var courseType = $(this).find('.update-course-type').find(':selected').val().trim();
                   var courseName = $(this).find('.update-course-name').val().trim();
                   var courseSequence = $(this).find('.update-course-sequence').val().trim();
                   if(courseSequence == ""){
                     courseSequence = null;
                   }
                   coursesList.push({'courseType': courseType, 'courseName' : courseName, 'courseId': course.courses[i].courseId, 'mandatory' : mandatory,'sequence':courseSequence});
                   i++
             });
             $("#update-class-courses-modal").modal('toggle');
             var classCoursesPayload = {'academicSessionId': academicSessionId, 'standardId' : standardId, 'courses' : coursesList};
             ajaxClient.post("/courses/update-class-courses/"+academicSessionId, {'classCoursesPayload' : JSON.stringify(classCoursesPayload)}, function(data){
                 $("#course-page-status-modal-container").html(data);
                 $("#course-page-status-modal").modal({backdrop: 'static', keyboard: false});
                 courses.loadCourseConfiguredClasses();
                 courses.dataCache.count = 0;
            });
             },

   getUpdateCourse : function(){
       var courseNameArray = [];
       $("tr.update-course-row").each(function(){
       var courseName= $(this).find('.update-course-name').val().trim() ;
       courseNameArray.push(courseName.toLowerCase());
          });
       return courseNameArray;
       },

   populateAddClassCoursesModal : function (standardId) {
       var academicYear = academicSessionHandler.getSelectedSession();
       $("#add-class-courses-session-display").val(academicYear.displayName);
       var configuredClassCourses = courses.dataCache.configuredClassCoursesMap[standardId];
       $("#add-class-courses-class-display").val(configuredClassCourses.standard.displayName);
       $("#add-class-courses-standrad-id").val(configuredClassCourses.standard.standardId);

       var viewCourseDivStartContent = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th scope=\"col\">Mandatory</th> <th scope=\"col\">Course Type</th> <th scope=\"col\">Course Name</th> <th scope=\"col\">Sequence</th>  </tr> </thead> <tbody> ";

       for(var i = 0 ; i < configuredClassCourses.courses.length; i++){
          courses.dataCache.count += 1;
          var course = configuredClassCourses.courses[i];
          var checkedField = "";
          var defaultValueDropDown = "";
          if(course.mandatory){
            checkedField = "checked";
          }
          var customSwitchId = "customSwitch" + courses.dataCache.count;
          var courseRow = "<tr class=\"existing-course-row\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\" " + checkedField + " disabled > <label class=\"custom-control-label\" for=\"" + customSwitchId + "\" disabled></label> </div>  </td> <td><select id=\"\" class=\"form-control form-control-sm mandatory-field course-type\" disabled><option value=\"SCHOLASTIC\" ~selectScholastic~ disabled>Scholastic</option> <option value=\"COSCHOLASTIC\" ~selectCoscholastic~ disabled>Co-Scholastic </option> </select> </td> <td><input type=\"text\" class=\"form-control form-control-sm add-course-name\" placeholder=\"Course Name...\" value = \""+course.courseName+"\" disabled></td>  <td><input type=\"number\" class=\"form-control form-control-sm add-course-sequence\" placeholder=\"Course Sequence...\" style=\"width: 75px;\" value = \""+course.sequence+"\" onChange=\"courses.validateCourseSequence(this,false)\" disabled></td>  </tr>";

          if((course.courseTypeDisplay).includes("Co-Scholastic")) {
            courseRow = courseRow.replace("~selectScholastic~", "");
            courseRow = courseRow.replace("~selectCoscholastic~", "selected");
          }
          else {
            courseRow = courseRow.replace("~selectScholastic~", "selected");
            courseRow = courseRow.replace("~selectCoscholastic~", "");
          }
          viewCourseDivStartContent += courseRow;

       }
       viewCourseDivStartContent += " <tr id=\"add-course-add-row-bottom\" style=\"display:none;\"></tr> </tbody> </table> </div> <div class=\"col-md-1\"> </div> </div>";
       $("#add-class-courses-input-div").html(viewCourseDivStartContent);
    },

    addClassCourses : function () {
          var academicSessionId = academicSessionHandler.getSelectedSessionId();
          var standardId = $('#add-class-courses-standrad-id').val().trim();
          var courseName = courses.getNewCourseName();
          var unique = Array.from(new Set(courseName));

          if(courseName.length != unique.length){
               alert("Duplicate Class Courses are not allowed");
               return;
                           }
          var coursesList = [];
          $("tr.new-course-row").each(function() {
                var mandatory = $(this).find('.custom-control-input:checkbox:checked').length > 0;
                var courseType = $(this).find('.course-type').find(':selected').val().trim();
                var courseSequence = $(this).find('.add-course-sequence').val().trim();
                var courseName = $(this).find('.add-course-name').val() ;

                if(courseSequence == ""){
                  courseSequence = null;
                }
                coursesList.push({'courseType': courseType, 'courseName' : courseName, 'mandatory' : mandatory,'sequence':courseSequence});
          });

          if(coursesList.length == 0){
                alert("Please Add atleast one Course")
                return;
          }

          $("#add-class-courses-modal").modal('toggle');
          var classCoursesPayload = {'academicSessionId': academicSessionId, 'standardId' : standardId, 'courses' : coursesList};
          ajaxClient.post("/courses/add-class-courses", {'classCoursesPayload' : JSON.stringify(classCoursesPayload)}, function(data){
             $("#course-page-status-modal-container").html(data);
             $("#course-page-status-modal").modal('toggle');
              courses.loadCourseConfiguredClasses();
              courses.dataCache.count = 0;

         });
},

    getNewCourseName : function() {
           var courseNameArray = new Array();
           $("input.add-course-name").each(function(){
           var courseName = $(this).val().trim();
           courseNameArray.push(courseName.toLowerCase());
                            });
           return courseNameArray;
           }
};

var courseAssignment = {
  dataCache : {},

  initDataCache: function () {
    courseAssignment.dataCache = {};

    var optionalScholasticCourses = readJson("#assigned-optional-scholastic-courses");
    var optionalCoScholasticCourses = readJson("#assigned-optional-co-scholastic-courses");
    var unassignedOptionalScholasticCourses = readJson("#unassigned-optional-scholastic-courses");
    var unassignedOptionalCoScholasticCourses = readJson("#unassigned-optional-co-scholastic-courses");

    courseAssignment.dataCache.optionalScholasticCourses = optionalScholasticCourses;
    courseAssignment.dataCache.optionalCoScholasticCourses = optionalCoScholasticCourses;
    courseAssignment.dataCache.unassignedOptionalScholasticCourses = unassignedOptionalScholasticCourses;
    courseAssignment.dataCache.unassignedOptionalCoScholasticCourses = unassignedOptionalCoScholasticCourses;
  },

  loadCourseAssignmentPage : function (){
      ajaxClient.get("/courses/course-assignment", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(courseAssignment.changeSession);
          courseAssignment.registerCourseAssignmentTabs();
          courseAssignment.loadStudentScreen();
      });
  },

  changeSession: function() {
     var selectedAssignOption = $(".course-assign-options.active").attr("id");
     if(selectedAssignOption == STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
         courseAssignment.loadStudentScreen()
     }
     else if(selectedAssignOption == BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
         courseAssignment.loadBulkStudentScreen();
     }
  },

  loadStudentScreen: function() {
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/courses/student-course-assignment", function(data) {
        $("#course-assignment-screen").html(data);
        courseAssignment.bindSearchStudentCourseAssignmentEvent();
    });
  },

  loadBulkStudentScreen: function() {
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/courses/bulk-course-assignment/"+academicSessionId, function(data) {
        $("#course-assignment-screen").html(data);
        courseAssignment.bindSearchStudentCourseAssignmentEvent();
        courseAssignment.bulkStandardChange();
    });
  },

  modifyStudentCourseAssignmentCallBack: function(){
    $("#student-modify-course-assignment-button").on('click', function () {
        courseAssignment.fillCurrentStudentCourseAssignment();
        $("#modify-student-course-assignment-modal").modal("toggle");
    });
  },

  fillCurrentStudentCourseAssignment: function(){
    var count = 1;
    var optionalScholasticCourses = courseAssignment.dataCache.optionalScholasticCourses;
    var optionalCoScholasticCourses = courseAssignment.dataCache.optionalCoScholasticCourses;
    var unassignedOptionalScholasticCourses = courseAssignment.dataCache.unassignedOptionalScholasticCourses;
    var unassignedOptionalCoScholasticCourses = courseAssignment.dataCache.unassignedOptionalCoScholasticCourses;

    var scholacticCourseRow = "";
    if(optionalScholasticCourses.length > 0){
      for(i=0; i<optionalScholasticCourses.length; i++){
        var customSwitchId = "customSwitch" + count;
        count = count + 1;
        // <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\"> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div>
        // scholacticCourseRow = scholacticCourseRow + "<tr class=\"course-row\" id=\""+optionalScholasticCourses[i].courseId+"\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked> <span class=\"slider round\"></span> </label> </td> <td><input type=\"text\" class=\"form-control form-control-sm course-name\" value=\""+optionalScholasticCourses[i].courseName+"\" readonly></td> </tr>";
        scholacticCourseRow = scholacticCourseRow + "<tr class=\"course-row\" id=\""+optionalScholasticCourses[i].courseId+"\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\" checked> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div> </td> <td><input type=\"text\" class=\"form-control form-control-sm course-name\" value=\""+optionalScholasticCourses[i].courseName+"\" readonly></td> </tr>";

      }
    }
    if(unassignedOptionalScholasticCourses.length > 0){
      for(i=0; i<unassignedOptionalScholasticCourses.length; i++){
        var customSwitchId = "customSwitch" + count;
        count = count + 1;
        scholacticCourseRow = scholacticCourseRow + "<tr class=\"course-row\" id=\""+unassignedOptionalScholasticCourses[i].courseId+"\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\"> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div> </td> <td><input type=\"text\" class=\"form-control form-control-sm course-name\" value=\""+unassignedOptionalScholasticCourses[i].courseName+"\" readonly></td> </tr>";
      }
    }
    var courseScholacticInputDiv = "";
    if(scholacticCourseRow != ""){
       courseScholacticInputDiv = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th scope=\"col\">#Select</th> <th scope=\"col\">Course Name</th> </tr> </thead> <tbody> "+scholacticCourseRow+"  </tbody> </table> </div>  </div>";
    }else{
      courseScholacticInputDiv = "<p> No optional scholastic courses available</p>"
    }

    $('#update\\.optional-scholastic-course').html(courseScholacticInputDiv);

    var coScholacticCourseRow = "";
    if(optionalCoScholasticCourses.length > 0){
      for(i=0; i<optionalCoScholasticCourses.length; i++){
        var customSwitchId = "customSwitch" + count;
        count = count + 1;
        coScholacticCourseRow = coScholacticCourseRow + "<tr class=\"course-row\" id=\""+optionalCoScholasticCourses[i].courseId+"\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\" checked> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div> </td> <td><input type=\"text\" class=\"form-control form-control-sm course-name\" value=\""+optionalCoScholasticCourses[i].courseName+"\" readonly></td> </tr>";
      }
    }
    if(unassignedOptionalCoScholasticCourses.length > 0){
      for(i=0; i<unassignedOptionalCoScholasticCourses.length; i++){
        var customSwitchId = "customSwitch" + count;
        count = count + 1;
        coScholacticCourseRow = coScholacticCourseRow + "<tr class=\"course-row\" id=\""+unassignedOptionalCoScholasticCourses[i].courseId+"\"> <td> <div class=\"custom-control custom-switch\"> <input type=\"checkbox\" class=\"custom-control-input\" id=\"" + customSwitchId + "\"> <label class=\"custom-control-label\" for=\"" + customSwitchId + "\"></label> </div> </td> <td><input type=\"text\" class=\"form-control form-control-sm course-name\" value=\""+unassignedOptionalCoScholasticCourses[i].courseName+"\" readonly></td> </tr>";
      }
    }
    var courseCoScholacticInputDiv = "";
    if(coScholacticCourseRow != ""){
      courseCoScholacticInputDiv = "<div class=\"row\"> <div class=\"col-md-1\"> </div> <div class=\"col-md-10\"> <table class=\"table borderless\"> <thead> <tr> <th scope=\"col\">#Select</th> <th scope=\"col\">Course Name</th> </tr> </thead> <tbody> "+coScholacticCourseRow+"  </tbody> </table> </div>  </div>";
    }else{
      courseCoScholacticInputDiv = "<p> No optional co-scholastic courses available</p>"
    }
    $('#update\\.optional-co-scholastic-course').html(courseCoScholacticInputDiv);
  },

  updateStudentOptionalCourseAssignment: function(){
    $("#modify-student-course-assignment-modal").modal("toggle");
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    var studentId = $("#course-assign-view-student-id").text().trim();
    var courseIds = [];
    $("tr.course-row").each(function() {
      if(!$(this).find('.custom-control-input:checkbox:checked').length > 0) {
          return;
      }
      var courseId = $(this).attr('id');
      courseIds.push(courseId);
    });
    var studentCourseAssignment = {'studentId': studentId, 'courseIds' : courseIds};
    ajaxClient.post("/courses/update-student-course-assignment/"+academicSessionId, {'studentCourseAssignment' : JSON.stringify(studentCourseAssignment)}, function(data){
        $("#course-page-status-modal-container").html(data);
        $("#course-page-status-modal").modal({backdrop: 'static', keyboard: false});
        courseAssignment.loadStudentCourseAssignment(studentId);
   });
  },

  doneStudentSearchTyping: function(resultArea) {
    var searchText = $('#course-assign-search-text').val().trim();
    var sessionId = academicSessionHandler.getSelectedSessionId();
    coursesCommomMethods.studentLiveSearchEvent(sessionId, searchText, resultArea, courseAssignment.loadStudentCourseAssignment);
  },

  loadStudentCourseAssignment: function(studentId) {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    courseAssignment.loadStudentCoursesAssignment(sessionId,studentId,STUDENT_ASSIGNED_COURSES_LIST);
  },

  loadStudentCoursesAssignment: function(sessionId,studentId,contentDiv) {
    studentId = studentId.trim();
    if(sessionId == ""){
      showErrorDialogBox("Please select session to assign course.")
      return;
    }
    ajaxClient.get("/courses/assigned-courses/"+sessionId+"/"+studentId, function(data) {
        $("#"+contentDiv).html(data);
        courseAssignment.initDataCache();
        courseAssignment.modifyStudentCourseAssignmentCallBack();
    });
  },

  registerCourseAssignmentTabs: function() {
    $("#"+STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        // var status = confirm("You will be taken to student level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
        // if(!status){
        //   return;
        // }
        courseAssignment.loadStudentScreen();

    });

    $("#"+BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        // var status = confirm("You will be taken to bulk student level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
        // if(!status){
        //   return;
        // }
        courseAssignment.loadBulkStudentScreen();
    });
  },

  bindSearchStudentCourseAssignmentEvent: function() {
    var resultArea = "#course-assign-student-search-result";
    $('#course-assign-search-student').on('click', function () {
         // loadStudentCourseAssignment();
         courseAssignment.doneStudentSearchTyping(resultArea);
    });
    $("#course-assign-search-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          // loadStudentCourseAssignment();
          courseAssignment.doneStudentSearchTyping(resultArea);
      }
    });
    liveSearchHandler.bindEvent('#course-assign-search-text',resultArea,courseAssignment.doneStudentSearchTyping);
  },

  resetToMainScreen: function () {
      feeAssignment.returnToStudentMainScreen();
      feeAssignment.returnToBulkStudentMainScreen();
  },

  returnToStudentMainScreen: function(){
      $(".student-course-assign-tab").attr("style", "display:none");
      $("#student-course-assign-main-tab").attr("style", "display:block");
      // clearMandatoryFieldsErrorDisplay();
  },

  bulkStandardChange : function(){
    $("#bulk-course-assign-class").change(function() {
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      // var classId = $(this).val();
      var standardId = $(this).val();
      var standardIdAndSection = standardId.split(":");
      var classId = standardIdAndSection[0].trim();
      if(classId === undefined || classId === "") {
        showErrorDialogBox("Please select a valid class!");
        return;
      }
      ajaxClient.get("/courses/class-students/"+academicSessionId+"/"+classId, function(data) {
        $(".optional-course-id").html(data);
        // $("#bulk-student-list").html(data);
        // $('.selectpicker').selectpicker();
      });

    });
  },

  getStudentCoursesDetails : function () {

    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    var standardId = $("#bulk-course-assign-class").val();
    var standardIdAndSection = standardId.split(":");
    var classId = standardIdAndSection[0].trim();
    var sectionId = 0;
    if(standardIdAndSection.length > 1) {
      sectionId = standardIdAndSection[1].trim();
    }
    if(classId === undefined || classId === "") {
      showErrorDialogBox("Please select a valid class!");
      return;
    }
    var courseId = $(".optional-course-id").val();
    if(courseId === undefined || courseId === "") {
      showErrorDialogBox("Please select a valid optional course to assign!");
      return;
    }
    ajaxClient.get("/courses/students-course-details/"+academicSessionId+"/"+classId+"/"+courseId+"?sectionId="+sectionId, function(data) {
      // $(".optional-course-id").html(data);
      $("#bulk-student-list").html(data);
      bulkAssignmentStudentSelectCheckbox();
      $('#datatables-reponsive').DataTable( {
        "paging":   false,
        searching: false,
        columnDefs: [
          { orderable: false, targets: "no-sort" },
        ],
      });
      // $('.selectpicker').selectpicker();
    });
  },

};

var courseReports = {

  dataCache : {},

  loadReportsPage : function(){
    ajaxClient.get("/courses/reports", function(data) {
        $("#main-content").html(data);
        initSelect2("All");
        commonUtils.bindCardHoverEvent();
        commonUtils.bindReportCardClickEvent();
        courseReports.bindSessionDropDown();
        courseReports.bindStandardDropDown();
        courseReports.bindGenerateReportEvent();
        courseReports.bindOptionalStandardDropDown();
    });
  },

  bindSessionDropDown : function () {
    $('.report-academic-session').on('change', function() {
      var selectedSessionId = $(this).find(':selected').val().trim();
      var sessionElement = this;
      var data = $(sessionElement).closest(".modal-body").find(".standard-json").html();
      $(sessionElement).closest(".modal-body").find(".different-value-wrapper").html(data);
      courseReports.bindStandardDropDown();
      $(sessionElement).closest(".modal-body").find(".report-standard-section").html("");
      $(sessionElement).closest(".modal-body").find(".report-courses").html("");
      $(sessionElement).closest(".modal-body").find(".report-standards").val("");
    });
  },

  bindStandardDropDown : function () {
    $('.report-standards').on('change', function() {
      var selectedSessionId = $(this).closest(".modal-body").find('.report-academic-session').find(':selected').val().trim();
      var standardId = $(this).find(':selected').val().trim();
      var sessionElement = this;
      $(sessionElement).closest(".modal-body").find(".report-standard-section").html("");
      $(sessionElement).closest(".modal-body").find(".report-courses").html("");
      var showCoursesWithMandatoryFlag = "";
      courseReports.fillStudentSections(this, selectedSessionId, standardId, showCoursesWithMandatoryFlag);
    });
  },

    bindOptionalStandardDropDown : function () {
      $('.report-standards-optional').on('change', function() {
        var selectedSessionId = $(this).closest(".modal-body").find('.report-academic-session').find(':selected').val().trim();
        var standardId = $(this).find(':selected').val().trim();
        var sessionElement = this;
        $(sessionElement).closest(".modal-body").find(".report-standard-section").html("");
        $(sessionElement).closest(".modal-body").find(".report-courses").html("");
        var showCoursesWithMandatoryFlag = false;
        courseReports.fillStudentSections(this, selectedSessionId, standardId, showCoursesWithMandatoryFlag);
      });
    },

  fillStudentSections : function(changeElementContext, selectedSessionId, standardId, showCoursesWithMandatoryFlag) {
    if(standardId === "" || standardId === undefined) {
      return;
    }
    ajaxClient.get("/courses/standard-details/"+standardId+"/"+selectedSessionId, function(data) {
        $(".section-json-wrapper").html(data);

        var standards = readJson("#standard-details-p");
        var classSectionMap = {};
        for(var i = 0 ; i < standards.length ; i++){
            var standard = standards[i]

            classSectionMap[standard.standardId] = standard
        }

        var options = "";
        if(!(standardId in classSectionMap)){
            $('select.student-section').html(options);
            return ;
        }

        var standard = classSectionMap[standardId];

        for(var i = 0 ; i < standard.standardSectionList.length; i++){
            options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>";
        }

        $(".report-standard-section").html(options);
        courseReports.fillCoursesDropDown(this, selectedSessionId, standardId, showCoursesWithMandatoryFlag);
        });
  },

  fillCoursesDropDown : function (changeElementContext, sessionId, standardId, showCoursesWithMandatoryFlag) {
         ajaxClient.get("/courses/course-details/"+standardId+"/"+sessionId+"?showCoursesWithMandatoryFlag="+showCoursesWithMandatoryFlag, function(data) {
                $(".report-courses").html(data);
          });
  },

  bindGenerateReportEvent : function () {
    $('.generate-report').on('click', function () {
      $(this).closest('div.modal').modal('toggle');
      var containerElement = $(this).closest('div.report-field-container');
      var academicSessionId = $(containerElement).find(".report-academic-session option:selected").val();
      var sectionId = $(containerElement).find(".report-standard-section").val();
      var courseId = $(containerElement).find(".report-courses").val();
      var reportType = $(containerElement).find(".report-type").html();
      var standardId = reportType === "STUDENT_OPTIONAL_COURSE_REPORT" ?
          $(containerElement).find(".report-standards-optional option:selected").val() :
          $(containerElement).find(".report-standards option:selected").val();
      if(academicSessionId == ""  || standardId == "" || standardId == undefined){
        showErrorDialogBox("Please select all the required fields to generate report")
        return
      }
      if(sectionId === undefined || sectionId.length == 0){
        sectionId = "";
      }
      else{
        sectionId = sectionId.join(",")
      }

      if(courseId === undefined || courseId.length == 0){
        courseId = "";
      }
      else{
        courseId = courseId.join(",");
      }

      window.open(baseURL+"/courses/generate-report?standard_id="+standardId+"&course_id="+courseId+"&section_id="+sectionId+
      "&academicSessionId="+academicSessionId+"&report_type="+reportType, '_blank');
    });
  },
};

var coursesCommomMethods =  {

  studentLiveSearchEvent : function (sessionId, searchText, resultArea, triggerMethod) {
    ajaxClient.get("/courses/student-live-search/"+sessionId+"?searchText=" + searchText, function(data) {
        $(resultArea).html(data);
        studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
    });
  },

};

function selectBulkAssignmentStudentList(selectAllCheckbox) {
    if(selectAllCheckbox.checked){
      $(".bulk-student-assignment-checkbox").prop('checked', true);
      var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
      $("#bulk-assign-selected-count").html(selectedStudentCount);
    }
    else{
      $(".bulk-student-assignment-checkbox").prop('checked', false);
        $("#bulk-assign-selected-count").html(0);
    }
}

function updateBulkAssignmentStudentConfirmModal() {
  var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
  $("#bulk-assignment-confirmation-modal-text").text("You have selected "+selectedStudentCount+ " students to assign course at student level. Do you want to proceed?");
}

function bulkAssignmentStudentSelectCheckbox(studentSelectCheckbox) {
      var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
      $("#bulk-assign-selected-count").html(selectedStudentCount);
}

function getBulkFeesAssignmentScreen() {
    $("#bulk-assignment-confirmation-modal").modal("toggle");
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    // var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;

    var studentIds = [];
    var assignCourseStudentList = [];
    var removeCourseStudentList = [];
    $("input.bulk-student-assignment-checkbox").each(function() {
        var studentId = $(this).parent().find('p.bulk-fee-assign-student-id').first().text().trim();
        if($(this).is(":checked")) {
          assignCourseStudentList.push(studentId);
        } else {
          removeCourseStudentList.push(studentId);
        }
    });

    // if(studentIds.length <= 0) {
    //   showErrorDialogBox("Please select atleast one student!");
    //   return;
    // }
    //
    // var courseIds = $("select.optional-course-id").val();
    // if(courseId.length <= 0) {
    //   showErrorDialogBox("Please select a course to assign!");
    //   return;
    // }

    // var classId = $("#bulk-course-assign-class").val();
    var standardId = $("#bulk-course-assign-class").val();
    var standardIdAndSection = standardId.split(":");
    var classId = standardIdAndSection[0].trim();
    if(classId === undefined || classId === "") {
      showErrorDialogBox("Please select a valid class!");
      return;
    }
    var courseId = $(".optional-course-id").val();
    if(courseId === undefined || courseId === "") {
      showErrorDialogBox("Please select a valid optional course to assign!");
      return;
    }

    var bulkStudentCoursesPayload = {'instituteId' : null, 'academicSessionId' : academicSessionId,
      'courseId' : courseId, 'assignCourseStudentList' : assignCourseStudentList,
      'removeCourseStudentList' : removeCourseStudentList}

    ajaxClient.post("/courses/bulk-student-course-assignment",
        {'bulkStudentCoursesPayload' : JSON.stringify(bulkStudentCoursesPayload)}, function(data){
        $("#course-page-status-modal-container").html(data);
        $("#fee-assignment-status-modal").modal({backdrop: 'static', keyboard: false});
        courseAssignment.getStudentCoursesDetails();
    });
}
