SCHOLASTIC_COURSE_TYPE = "SCHOLASTIC";
COSCHOLASTIC_COURSE_TYPE = "COSCHOLASTIC";
GRAPH_VIEW = "GRAPH_VIEW";
TREE_VIEW = "TREE_VIEW";
REPORT_CARD_VARIABLES = "REPORT_CARD_VARIABLES";
PERSONALITY_TRAITS = "PERSONALITY_TRAITS";

var ONE_KB = 1024;
var FILE_SIZE_LIMIT = 50;

var examinationConfig = {
    dataCache : {},

    loadExamConfigurationPage : function () {
      ajaxClient.get("/examination/configure-exam", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(examinationConfig.resetPage);
          examinationConfig.bindClassChangeEvent();
          examinationConfig.bindExamViewChangeEvent();
      });
    },

    resetPage :function () {
        var standardId = $("#exam-config-class").find(':selected').val().trim();
        if(standardId == ""){
          return;
        }
        examinationConfig.loadClassExamConfigurationPage(standardId);
    },

    loadExamConfigurationPageForStandard : function (standardId) {
      var examListView = $("#exam-list-view-p").text();
      ajaxClient.get("/examination/configure-exam", function(data) {
          $("#main-content").html(data);
          $("#exam-config-class").val(standardId);
          $("#exam-list-view").val(examListView);
          academicSessionHandler.bindSessionChangeEvent(examinationConfig.resetPage);
          examinationConfig.bindClassChangeEvent();
          examinationConfig.bindExamViewChangeEvent();
          examinationConfig.loadClassExamConfigurationWithViewTypePage(standardId, examListView);
      });
    },

    bindClassChangeEvent : function () {
      $("#exam-config-class").change(function() {
          var standardId = $(this).find(':selected').val().trim();
          if(standardId == ""){
            return;
          }
          examinationConfig.loadClassExamConfigurationPage(standardId);
      });
    },

    bindExamViewChangeEvent : function () {
      $("#exam-list-view").change(function() {
          var standardId = $("#exam-config-class").find(':selected').val().trim();
          if(standardId == ""){
            return;
          }
          examinationConfig.loadClassExamConfigurationPage(standardId);
      });
    },

    loadClassExamConfigurationPage : function (standardId) {
      var examListView = $("#exam-list-view").val();
      examinationConfig.loadClassExamConfigurationWithViewTypePage(standardId, examListView);
    },

    loadClassExamConfigurationWithViewTypePage : function (standardId, examListView) {
      if(examListView === GRAPH_VIEW) {
        examinationConfig.loadClassExamGraphConfigurationPage(standardId);
      } else {
        examinationConfig.loadClassExamTreeConfigurationPage(standardId);
      }
    },

    loadClassExamTreeConfigurationPage : function(standardId) {

      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/exam-forest/"+standardId+"/"+academicSessionId, function(data) {
          $("#tree-container").html(data);
          var classExamForest = readJson("#class-exam-tree-view");
          if(classExamForest.length == 0){
            showErrorDialogBox("No exam configured. Please contact support to create initial exam structure.");
            return;
          }
          var i = 0;
          $("div.tree-view-exam-name-div").each(function() {
            i++;
            if(i == 1) {
              $(this).attr("style", "padding:.75rem!important");
              $(this).parent().attr("style", "padding-left:0px!important;");
              $(this).find('.arrow-icon').remove();
              $(this).find('.tree-view-exam-name').attr("style", "display:inline-block;font-size:16px;font-weight:bold;");
              return;
            }
            // As a side note, this === el.
            if (i % 2 === 0) {
              /* we are even */
              $(this).attr("style", "background-color:#ecf5f6;padding:.75rem!important;display: table;width:100%;padding-top:0rem!important;");
            } else {
              /* we are odd */
              $(this).attr("style", "background-color:#ffffff;padding:.75rem!important;display: table;width:100%;padding-top:0rem!important;");
            }
          });
          initDateWithYearRange("-5:+5");
      });

    },

    loadClassExamGraphConfigurationPage : function (standardId) {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/exam-forest-graph/"+standardId+"/"+academicSessionId, function(data) {
          $("#tree-container").html(data);
          var classExamForest = readJson("#class-exam-forest");
          if(classExamForest.length == 0){
            showErrorDialogBox("No exam configured. Please contact support to create initial exam structure.");
            return;
          }
          examTree.render(classExamForest[0]);
      });
    },

};

var examConfig = {
  dataCache : {},

  addExamLitePopUp : function(ref) {
    var examMetadataDetailsJson = $(ref).parent().parent().find('.exam-metadata-details-json').text();
    var examMetadataDetails = JSON.parse(examMetadataDetailsJson);

    $('#add-class-child-exam-name').val("");
    $("#add-exam-lite-modal").modal('toggle');
    $("#exam-metadata-details").text(examMetadataDetailsJson);

  },

  addExamLite : function(ref) {
    var examMetadataDetails = JSON.parse($("#exam-metadata-details").text());

    var academicSessionId = examMetadataDetails.academicSessionId;
    var standardId = examMetadataDetails.standardId;
    var examName = $('#add-class-child-exam-name').val().trim();
    var parentExamId = examMetadataDetails.examId;

    var courseTypeDimensions = null;
    var examCreationPayload = {'academicSessionId': academicSessionId, 'standardId' : standardId, 'examName' : examName, 'parentExamId' : parentExamId, 'examType' : 'REGULAR', 'courseTypeDimensions' : courseTypeDimensions};
    $("#add-exam-lite-modal").modal('toggle');
    ajaxClient.post("/examination/create-exam/true", {'examCreationPayload' : JSON.stringify(examCreationPayload)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-creation-status-modal").modal({backdrop: 'static', keyboard: false});
        examinationConfig.loadClassExamConfigurationPage(standardId);
    });
  },


  updateExamLitePopUp : function (ref) {
    var examMetadataDetailsJson = $(ref).parent().parent().find('.exam-metadata-details-json').text();
    var examMetadataDetails = JSON.parse(examMetadataDetailsJson);

    $("#update-exam-lite-modal").find("#update-class-exam-name-lite").val(examMetadataDetails.examName);
    $("#update-exam-lite-modal").find("#update-exam-operation-lite").val(examMetadataDetails.operation);

    $("#update-exam-lite-modal").find("#attendance-start-date-lite").val(getFormattedDate(examMetadataDetails.attendanceStartDate));
    $("#update-exam-lite-modal").find("#attendance-end-date-lite").val(getFormattedDate(examMetadataDetails.attendanceEndDate));
    $("#update-exam-lite-modal").find("#exam-start-date-lite").val(getFormattedDate(examMetadataDetails.examStartDate));
    $("#update-exam-lite-modal").find("#date-of-result-declaration-lite").val(getFormattedDate(examMetadataDetails.dateOfResultDeclaration));

    $("#update-exam-lite-modal").modal('toggle');
    $("#update-exam-metadata-details").text(examMetadataDetailsJson);

  },

  updateExamLite : function () {
    $("#update-exam-lite-modal").modal('toggle');

    var examMetadataDetails = JSON.parse($("#update-exam-metadata-details").text());

    var examId = examMetadataDetails.examId;
    var standardId = examMetadataDetails.standardId;
    var examName = $("#update-exam-lite-modal").find("#update-class-exam-name-lite").val();
    if(examName == null || examName.trim() == ""){
      showErrorDialogBox("Invalid exam name provided.")
      return;
    }
    var operation = $("#update-exam-lite-modal").find("#update-exam-operation-lite").val();
    if(operation == null || operation.trim() == "" || operation === undefined){
      showErrorDialogBox("Invalid operation selected.")
      return;
    }

    var attendanceStartDate = getDate($('#attendance-start-date-lite').val());
    var attendanceStartDateVar = null;
    if(attendanceStartDate != null){
      attendanceStartDateVar = attendanceStartDate.getTime()/1000;
    }

    var attendanceEndDate = getDate($('#attendance-end-date-lite').val());
    var attendanceEndDateVar = null;
    if(attendanceEndDate != null){
      attendanceEndDateVar = attendanceEndDate.getTime()/1000;
    }

    var examStartDate = getDate($('#exam-start-date-lite').val());
    var examStartDateVar = null;
    if(examStartDate != null){
      examStartDateVar = examStartDate.getTime()/1000;
    }

    var dateOfResultDeclaration = getDate($('#date-of-result-declaration-lite').val());
    var dateOfResultDeclarationVar = null;
    if(dateOfResultDeclaration != null){
      dateOfResultDeclarationVar = dateOfResultDeclaration.getTime()/1000;
    }

    var updateExamMetadataPayload = {'examName' : examName, 'operation' : operation, 'attendanceStartDate' : attendanceStartDateVar, 'attendanceEndDate' : attendanceEndDateVar, 'examStartDate' : examStartDateVar, 'dateOfResultDeclaration' : dateOfResultDeclarationVar};
    ajaxClient.post("/examination/update-exam-metadata/"+examId+"/true", {'updateExamMetadataPayload' : JSON.stringify(updateExamMetadataPayload)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-update-status-modal").modal({backdrop: 'static', keyboard: false});
        examinationConfig.loadClassExamConfigurationPage(standardId);
    });
  },

  deleteExamLitePopUp : function(ref) {
    var examMetadataDetailsJson = $(ref).parent().parent().find('.exam-metadata-details-json').text();
    var examMetadataDetails = JSON.parse(examMetadataDetailsJson);

    $("#delete-exam-lite-modal").modal('toggle');
    $("#delete-exam-metadata-details").text(examMetadataDetailsJson);

  },

  deleteExamLite : function () {
    $("#delete-exam-lite-modal").modal('toggle');

    var examMetadataDetails = JSON.parse($("#delete-exam-metadata-details").text());

    var examId = examMetadataDetails.examId;
    var standardId = examMetadataDetails.standardId;

    ajaxClient.post("/examination/delete-exam/"+examId+"/"+standardId+"/true", {}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-delete-status-modal").modal({backdrop: 'static', keyboard: false});
        examinationConfig.loadClassExamConfigurationPage(standardId);
    });
  },

  expandCollaspeChildExams : function (ref) {
    var isCollaspe = $(ref).hasClass("bi-chevron-right");
    if(isCollaspe) {
      $(ref).removeClass("bi-chevron-right");
      $(ref).addClass("bi-chevron-down");

      $(ref).parent().parent().find('ul').attr("style", "list-style-type: none;display:block;");

    } else {
      $(ref).removeClass("bi-chevron-down");
      $(ref).addClass("bi-chevron-right");

      $(ref).parent().parent().find('ul').attr("style", "list-style-type: none;display:none;");
    }
  },
};

var examDetailsNamespace = {
  dataCache : {},

  loadExamDetailsPage : function (examId) {
    var examListViewType = $("#exam-list-view").val();
    if(examListViewType === "" || examListViewType === null || examListViewType === undefined) {
      examListViewType = $("#exam-list-view-p").text();
    }
    ajaxClient.get("/examination/exam-details/"+examId, function(data) {
        $("#main-content").html(data);
        examDetailsNamespace.dataCache = {};
        var examDetails = readJson("#exam-details-json");
        var dimensions = readJson("#dimensions-json");
        var classCourses = readJson("#class-courses-json");
        examDetailsNamespace.dataCache['examDetails'] = examDetails;
        examDetailsNamespace.dataCache['dimensions'] = dimensions;
        examDetailsNamespace.dataCache['classCourses'] = classCourses;
        $("#exam-list-view-p").text(examListViewType);
        initDateWithYearRange("-5:+5");
    });
  },

  populateCreateExamModal : function () {
    var examDetails = examDetailsNamespace.dataCache['examDetails']
    var academicYearDisplayName = examDetails.academicSession.yearDisplayName;
    $("#add-class-exam-session-display").val(academicYearDisplayName);
    var standardName = examDetails.standard.displayName;
    $("#add-class-exam-class-name").val(standardName);

    var dimensions = examDetailsNamespace.dataCache['dimensions'];
    var rows = "";
    for(var i = 0; i < dimensions.length; i++){
        if(examDetails.standardsMetaData.scholasticGradingEnabled){
            if(dimensions[i].examEvaluationType == "NUMBER"){
              continue;
            }
            if(dimensions[i].total){
              rows += "<tr id=\""+dimensions[i].dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td>  <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
              break;
            }
        }else{
            if(dimensions[i].examEvaluationType == "GRADE"){
              continue;
            }
            if(dimensions[i].total){
              rows += "<tr id=\""+dimensions[i].dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td> <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
              continue;
            }
            rows += "<tr id=\""+dimensions[i].dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\"> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td> <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
        }
    }

    $("#add-class-exam-scholastic-dimensions-input-div").find('tbody').html(rows);

    rows = "";
    for(var i = 0; i < dimensions.length; i++){
      if(examDetails.standardsMetaData.coScholasticGradingEnabled){
          if(dimensions[i].examEvaluationType == "NUMBER"){
            continue;
          }
          if(dimensions[i].total){
            rows += "<tr id=\""+dimensions[i].dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td>  <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
            break;
          }
      }else{
          if(dimensions[i].examEvaluationType == "GRADE"){
            continue;
          }
          if(dimensions[i].total){
            rows += "<tr id=\""+dimensions[i].dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td> <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
            continue;
          }
          rows += "<tr id=\""+dimensions[i].dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\"> <span class=\"slider round\"></span> </label> </td> <td> "+dimensions[i].dimensionName+" </td> <td> "+dimensions[i].examEvaluationType+" </td> </tr>";
      }

    }
    $("#add-class-exam-coscholastic-dimensions-input-div").find('tbody').html(rows);
  },

  createExam : function () {
    $("#add-class-exam-modal").modal('toggle');
    var examDetails = examDetailsNamespace.dataCache['examDetails']
    var academicSessionId = examDetails.academicSession.academicSessionId;
    var standardId = examDetails.standard.standardId;
    var examName = $('#add-class-exam-name').val().trim();
    var parentExamId = examDetails.examMetaData.examId;

    var operation = $("#add-exam-operation").val();
    if(operation == null || operation.trim() == "" || operation === undefined){
      showErrorDialogBox("Invalid operation selected.")
      return;
    }

    var schDisplayType = $("#add-exam-sch-display-type").val();
    var coschDisplayType = $("#add-exam-cosch-display-type").val();

    var attendanceStartDate = getDate($('#add-attendance-start-date').val());
    var attendanceStartDateVar = null;
    if(attendanceStartDate != null){
      attendanceStartDateVar = attendanceStartDate.getTime()/1000;
    }

    var attendanceEndDate = getDate($('#add-attendance-end-date').val());
    var attendanceEndDateVar = null;
    if(attendanceEndDate != null){
      attendanceEndDateVar = attendanceEndDate.getTime()/1000;
    }

    var examStartDate = getDate($('#add-exam-start-date').val());
    var examStartDateVar = null;
    if(examStartDate != null){
      examStartDateVar = examStartDate.getTime()/1000;
    }

    var dateOfResultDeclaration = getDate($('#add-date-of-result-declaration').val());
    var dateOfResultDeclarationVar = null;
    if(dateOfResultDeclaration != null){
      dateOfResultDeclarationVar = dateOfResultDeclaration.getTime()/1000;
    }





    var scholasticDimensions = [];
    $("tr.scholastic-dimension-row").each(function() {
          var selected = $(this).find('.toggle-switch:checkbox:checked').length > 0;
          if(!selected){
              return;
          }
          var dimensionId = $(this).attr("id").split("-")[0];
          var maxValue = $(this).find('.max-default-value').val();
          var minValue = $(this).find('.min-default-value').val();
          scholasticDimensions.push({'dimensionId': dimensionId, 'maxMarks' : maxValue, 'minMarks' : minValue});
    });

    var coscholasticDimensions = [];
    $("tr.coscholastic-dimension-row").each(function() {
          var selected = $(this).find('.toggle-switch:checkbox:checked').length > 0;
          if(!selected){
              return;
          }
          var dimensionId = $(this).attr("id").split("-")[0];
          var maxValue = $(this).find('.max-default-value').val();
          var minValue = $(this).find('.min-default-value').val();
          coscholasticDimensions.push({'dimensionId': dimensionId, 'maxMarks' : maxValue, 'minMarks' : minValue});
    });

    var courseTypeDimensions = {'SCHOLASTIC' : scholasticDimensions, 'COSCHOLASTIC':coscholasticDimensions};
    var examCreationPayload = {'academicSessionId': academicSessionId, 'standardId' : standardId, 'examName' : examName, 'parentExamId' : parentExamId, 'examType' : 'REGULAR', 'operation' : operation, 'attendanceStartDate' : attendanceStartDateVar, 'attendanceEndDate' : attendanceEndDateVar, 'examStartDate' : examStartDateVar, 'dateOfResultDeclaration' : dateOfResultDeclarationVar, 'scholasticExamMarksDisplayType' : schDisplayType, 'coScholasticExamMarksDisplayType' : coschDisplayType, 'courseTypeDimensions' : courseTypeDimensions};

    ajaxClient.post("/examination/create-exam/false", {'examCreationPayload' : JSON.stringify(examCreationPayload)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-creation-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  getCourseMaxGradeRow : function (course, examDimensionValues, assignedCourse) {
    var checked = "";
    if(assignedCourse){
      checked = "checked";
    }

    courseMarksRow = " <tr class=\"exam-course-update-row\" id=\""+course.courseId+"\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch child\" "+checked+"> <span class=\"slider round\"></span> </label> </td> <td>"+course.courseName+"</td>";
    for(var j = 0 ; j < examDimensionValues.length; j++){
            if(examDimensionValues[j].examDimension.examEvaluationType == "NUMBER"){
              continue;
            }
            // var maxGradeValue = 0;
            // if(assignedCourse){
            //   maxGradeValue = examDimensionValues[j].maxGrade.value;
            // }
            if(examDimensionValues[j].examDimension.total){
              // courseMarksRow += "<td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\">" + createGradeSelectElement(maxGradeValue,"max-grade-value", "") + "</td>";
              courseMarksRow += "<td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\"> Grade</td>";
              break;
            }
        }
      courseMarksRow += "</tr>";
      return courseMarksRow;
  },

  getCourseMaxMarksRow : function (course, examDimensionValues, assignedCourse) {

    var checked = "";
    if(assignedCourse){
      checked = "checked";
    }
    courseMarksRow = " <tr class=\"exam-course-update-row\" id=\""+course.courseId+"\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch child\" "+checked+"> <span class=\"slider round\"></span> </label> </td> <td>"+course.courseName+"</td>";
    for(var j = 0 ; j < examDimensionValues.length; j++){
          if(examDimensionValues[j].examDimension.examEvaluationType == "GRADE"){
            continue;
          }

          var value = "";
          var minValue = "";
          var readonly = "readonly";
          if(assignedCourse){
            readonly = "";
            value = "value="+examDimensionValues[j].maxMarks;
            minValue = "value="+examDimensionValues[j].minMarks;
          }

          if(examDimensionValues[j].examDimension.total){
              courseMarksRow += "<td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\"> <input type=\"number\" class=\"form-control form-control-sm max-marks-value input-max-marks-"+examDimensionValues[j].examDimension.dimensionId+"-value total-marks\" placeholder=\"Max Marks...\" " + value + " readonly></td>         <td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\"> <input type=\"number\" class=\"form-control form-control-sm min-marks-value input-min-marks-"+examDimensionValues[j].examDimension.dimensionId+"-value total-marks\" placeholder=\"Min Marks...\" " + minValue + " readonly></td>";
          }else{
              courseMarksRow += "<td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\"> <input type=\"number\" class=\"form-control form-control-sm max-marks-value input-max-marks-"+examDimensionValues[j].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Max Marks...\" "+value + " "+readonly+" ></td>    <td class=\"exam-dimension\" id=\""+examDimensionValues[j].examDimension.dimensionId+"\"> <input type=\"number\" class=\"form-control form-control-sm min-marks-value input-min-marks-"+examDimensionValues[j].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Min Marks...\"    onChange=\"examDetailsNamespace.checkMinMarks(this)\"  "+minValue + " "+readonly+" ></td>";
          }
    }
    courseMarksRow += "</tr>";
    return courseMarksRow;
  },

  checkMinMarks : function(data){
    val = $(data).val();
    maxMarks = $(data).parent().prev().find('.max-marks-value').val();
    if(val < 0){
      $('#update-class-exam-courses-modal').modal('toggle');
      showErrorDialogBox("Please Enter a number which is greater than zero");
      $(data).val("");
      $('#update-class-exam-courses-modal').modal('toggle');
    }
    if(parseInt(val) > parseInt(maxMarks)){
      $('#update-class-exam-courses-modal').modal('toggle');
      showErrorDialogBox("Min Marks cannot be greater than the Max Marks");
      $(data).val("");
      $('#update-class-exam-courses-modal').modal('toggle');
    }
    val="";
  },

  checkInputMinMarks : function(data,dimensionId){
      val = $(data).val();
      var maxMarks = $(data).parent().prev().find('.input-button-max-marks-'+dimensionId+'-value').val();
      if(val < 0 ){
        $('#update-class-exam-courses-modal').modal('toggle');
        showErrorDialogBox("Please Enter a number which is greater than zero");
        $(data).val("");
        return;
      }
      if(parseInt(val) > parseInt(maxMarks)){
        $('#update-class-exam-courses-modal').modal('toggle');
        showErrorDialogBox("Min Marks cannot be greater than the Max Marks");
        $(data).val("");
        return;
      }
  },

  populateUpdateExamCoursesModal: function(scholastic){
      var courseType = scholastic ? "SCHOLASTIC" : "COSCHOLASTIC";
      examDetailsNamespace.dataCache['updateCourseType'] = courseType;
      var examDetails = examDetailsNamespace.dataCache['examDetails']
      var classCourses = examDetailsNamespace.dataCache['classCourses']
      var academicYearDisplayName = examDetails.academicSession.yearDisplayName;
      var standardName = examDetails.standard.displayName;
      $('#update\\.class-exam-name').val(examDetails.examMetaData.examName);
      $('#update\\.class-exam-class-name').val(standardName);
      $('#update\\.class-exam-session-display').val(academicYearDisplayName);
      var courseTable = "";
      if(scholastic){
        $('#update-class-exam-courses-modal').find('.modal-title').html("Update Exam Scholastic Courses");
      }else{
        $('#update-class-exam-courses-modal').find('.modal-title').html("Update Exam Co-Scholastic Courses");
      }
      courseTable += "<table class=\"table table-bordered table-hover\"> <thead> <tr> <th scope=\"col\" style=\"width:5%;\" rowspan=\"2\"> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch parent\" checked > <span class=\"slider round\"></span> </label>  </th> <th scope=\"col\" rowspan=\"2\" class=\"p-4\">Name</th>";
      for(var i = 0; i < examDetails.examDimensionValues[courseType].length; i++){
        if(courseType == "SCHOLASTIC"){
          if(examDetails.standardsMetaData.scholasticGradingEnabled){
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "GRADE" &&examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" rowspan=\"2\" class=\"p-4\">Evaluation Type</th>";
            }
          }else{
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "NUMBER"){
              if(!examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" colspan=\"2\" class=\"text-center \">"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionName+"<button type=\"button\" class=\"btn btn-outline-success btn-sm mb-1 ml-3\" onclick=\"examDetailsNamespace.autofillData("+"'"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"'"+");\">Autofill</button>"+"</th>";
              }
              else{
                courseTable += "<th scope=\"col\" colspan=\"2\" class=\"text-center \">"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionName+"</th>";
              }
            }
          }
        } else if(courseType == "COSCHOLASTIC"){
          if(examDetails.standardsMetaData.coScholasticGradingEnabled){
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "GRADE" &&examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" rowspan=\"2\" class=\"p-4\">Evaluation Type</th>";
            }
          }else{
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "NUMBER"){
              if(!examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" colspan=\"2\" class=\"text-center\">"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionName+"<button type=\"button\" class=\"btn btn-outline-success btn-sm mb-1 ml-3\" onclick=\"examDetailsNamespace.autofillData("+"'"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"'"+");\">Autofill</button>"+"</th>";
              }
              else{
                courseTable += "<th scope=\"col\" colspan=\"2\" class=\"text-center \">"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionName+"</th>";
              }
            }
          }
        }
      }

      courseTable += "</tr><tr>";
      for(var i = 0; i < examDetails.examDimensionValues[courseType].length; i++){
        if(courseType == "SCHOLASTIC"){
          if(examDetails.standardsMetaData.scholasticGradingEnabled){
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "GRADE" &&examDetails.examDimensionValues[courseType][i].examDimension.total){
                // courseTable += "<th scope=\"col\"></th>";
            }
          }else{
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "NUMBER"){
              if(!examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" colspan=\"1\">Max Marks<input type=\"number\" class=\"form-control form-control-sm input-button-max-marks-"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Max Marks...\"></th>"+"<th scope=\"col\" colspan=\"1\">"+"Min Marks<input type=\"number\" class=\"form-control form-control-sm input-button-min-marks-"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Min Marks...\" onChange=\"examDetailsNamespace.checkInputMinMarks(this,'"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"')\"></th>";
              }
              else{
                courseTable += "<th scope=\"col\" colspan=\"1\"></th>"+"<th scope=\"col\" colspan=\"1\"></th>";
              }
            }
          }
        } else if(courseType == "COSCHOLASTIC"){
          if(examDetails.standardsMetaData.coScholasticGradingEnabled){
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "GRADE" &&examDetails.examDimensionValues[courseType][i].examDimension.total){
                // courseTable += "<th scope=\"col\"></th>";
            }
          }else{
            if(examDetails.examDimensionValues[courseType][i].examDimension.examEvaluationType == "NUMBER"){
              if(!examDetails.examDimensionValues[courseType][i].examDimension.total){
                courseTable += "<th scope=\"col\" colspan=\"1\">Max Marks<input type=\"number\" class=\"form-control form-control-sm input-button-max-marks-"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Max Marks...\"></th><th scope=\"col\" colspan=\"1\">"+"Min Marks<input type=\"number\" class=\"form-control form-control-sm input-button-min-marks-"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"-value dimension-marks\" placeholder=\"Min Marks...\" onChange=\"examDetailsNamespace.checkInputMinMarks(this,'"+examDetails.examDimensionValues[courseType][i].examDimension.dimensionId+"')\"></th>";
              }
              else{
                courseTable += "<th scope=\"col\" colspan=\"1\"></th>"+"<th scope=\"col\" colspan=\"1\"></th>";
              }
            }
          }
        }

      }
      var assignedCourses = {};
      courseTable += "</tr> </thead> <tbody>";
      if(courseType in examDetails.courseMarksMatrix){
        for(var i = 0 ; i < examDetails.courseMarksMatrix[courseType].length; i++){
            var examCourse = examDetails.courseMarksMatrix[courseType][i];
            assignedCourses[examCourse.course.courseId] = examCourse;

           if(courseType == "SCHOLASTIC"){
             if(examDetails.standardsMetaData.scholasticGradingEnabled){
                  courseTable += examDetailsNamespace.getCourseMaxGradeRow(examCourse.course, examCourse.examDimensionValues, true);
             }else{
                courseTable += examDetailsNamespace.getCourseMaxMarksRow(examCourse.course, examCourse.examDimensionValues, true);
             }
           }else if(courseType == "COSCHOLASTIC"){
              if(examDetails.standardsMetaData.coScholasticGradingEnabled){
                   courseTable += examDetailsNamespace.getCourseMaxGradeRow(examCourse.course, examCourse.examDimensionValues, true);
              }else{
                 courseTable += examDetailsNamespace.getCourseMaxMarksRow(examCourse.course, examCourse.examDimensionValues, true);
              }
           }
        }
      }

      var unassignedCourse = false;
      for(var i = 0; i < classCourses.length; i++){
          if(classCourses[i].courseId in assignedCourses || classCourses[i].courseType != courseType){
            continue;
          }
          if(courseType == "SCHOLASTIC"){
            if(examDetails.standardsMetaData.scholasticGradingEnabled){
                 courseTable += examDetailsNamespace.getCourseMaxGradeRow(classCourses[i], examDetails.examDimensionValues[courseType], false);
            }else{
               courseTable += examDetailsNamespace.getCourseMaxMarksRow(classCourses[i], examDetails.examDimensionValues[courseType], false);
            }
          }else if(courseType == "COSCHOLASTIC"){
             if(examDetails.standardsMetaData.coScholasticGradingEnabled){
                  courseTable += examDetailsNamespace.getCourseMaxGradeRow(classCourses[i], examDetails.examDimensionValues[courseType], false);
             }else{
                courseTable += examDetailsNamespace.getCourseMaxMarksRow(classCourses[i], examDetails.examDimensionValues[courseType], false);
             }
          }
          unassignedCourse = true;
      }
      courseTable += "</tbody> </table>";
      $('#update-class-exam-courses-modal').find('.exam-course-assignment').html(courseTable);
      if(unassignedCourse) {
        $('input.parent').prop('checked', false);
      }
      examDetailsNamespace.bindComputeCourseMarksTotal();
      $("#update-class-exam-courses-modal").modal({backdrop: 'static', keyboard: false});
  },

  autofillData : function(dimensionId) {
    var maxValue = $('.input-button-max-marks-'+dimensionId+'-value').val();
    var minValue = $('.input-button-min-marks-'+dimensionId+'-value').val();

    $("tr.exam-course-update-row").each(function() {
        $(this).find("input.input-max-marks-"+dimensionId+"-value").each(function() {
            if(maxValue != ""){
              $(this).val(maxValue)
            }
        });

        $(this).find("input.input-min-marks-"+dimensionId+"-value").each(function() {
            if(minValue != ""){
              $(this).val(minValue);
            }
        });
      });

    examDetailsNamespace.computeCourseMarksTotal();
  },

  bindComputeCourseMarksTotal : function () {
    $("input.dimension-marks").focus(function() {
    }).blur(function() {
        examDetailsNamespace.computeCourseMarksTotal();
    });

    $(".exam-course-update-row").find(".toggle-switch").change(function() {
        var child = 0;
        var childChecked = 0;
        if(this.checked) {
            $(this).parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",false);
            $(this).parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",false);
            $(this).parent().parent().parent().parent().find("input.child").each(function() {
              child++;
              if(this.checked) {
                childChecked++;
              }
          });
          if(childChecked == child) {
            $(this).parent().parent().parent().parent().parent().find("input.parent").prop('checked', true);
          }
        }else{
          $(this).parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",true);
          $(this).parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",true);
        }
    });

    $(".exam-course-update-row").parent().parent().find(".toggle-switch.parent").change(function() {
      if(this.checked) {
        $(this).parent().parent().parent().parent().parent().find("input.child").prop('checked', true);
        $(this).parent().parent().parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",false);
        $(this).parent().parent().parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",false);
      }
      else {
        $(this).parent().parent().parent().parent().parent().find("input.child").prop('checked', false);
        $(this).parent().parent().parent().parent().parent().find("input.dimension-marks.max-marks-value").prop("readonly",true);
        $(this).parent().parent().parent().parent().parent().find("input.dimension-marks.min-marks-value").prop("readonly",true);
      }
    });
  },


  computeCourseMarksTotal : function () {
      $("tr.exam-course-update-row").each(function() {
          var totalMarks = 0;
          var marksPresent = false;
          $(this).find("input.dimension-marks.max-marks-value").each(function() {
            if($(this).val().trim() != ""){
                marksPresent = true;
                totalMarks +=  parseFloat($(this).val());
            }
          });
          if(marksPresent){
            $(this).find("input.total-marks.max-marks-value").val(totalMarks);
          }

          totalMarks = 0;
          marksPresent = false;
          $(this).find("input.dimension-marks.min-marks-value").each(function() {
            if($(this).val().trim() != ""){
                marksPresent = true;
                totalMarks +=  parseFloat($(this).val());
            }
          });
          if(marksPresent){
            $(this).find("input.total-marks.min-marks-value").val(totalMarks<0?0:totalMarks);
          }
      });
  },

  submitExamCourseUpdate : function () {
    $("#update-class-exam-courses-modal").modal('toggle');
    var examId = examDetailsNamespace.dataCache['examDetails'].examMetaData.examId;
    var courseType =  examDetailsNamespace.dataCache['updateCourseType'];
    var examCoursesPayloads = []
    $("tr.exam-course-update-row").each(function() {
          var finalMinMarks;
          var finalMaxMarks;
          var selected = $(this).find('.toggle-switch:checkbox:checked').length > 0;
          if(!selected){
            return;
          }
          var courseId = $(this).attr("id");
          var dimensionValuesPayloadsMap = {};

          $(this).find("td.exam-dimension").each(function() {
              var dimensionId = $(this).attr("id");
              var maxMarks = $(this).find('.max-marks-value').val();
              var minMarks = $(this).find('.min-marks-value').val();
              if(minMarks!= undefined){
                finalMinMarks = minMarks;
              }
              if(maxMarks!= undefined){
                finalMaxMarks = maxMarks;
              }

              var maxGrade = $(this).find('.max-grade-value').val();
              dimensionValuesPayloadsMap[dimensionId] = {'dimensionId':dimensionId, 'maxMarks' : finalMaxMarks, 'minMarks':finalMinMarks, 'maxGrade':maxGrade};
          });

          var dimensionValuesPayloads = [];
          $.each(dimensionValuesPayloadsMap, function( key, value ) {
            dimensionValuesPayloads.push(value);
          });
          examCoursesPayloads.push({'courseId': courseId, 'dimensionValuesPayloads' : dimensionValuesPayloads});
    });

    ajaxClient.post("/examination/update-exam-courses/"+examId+"/"+courseType, {'examCoursesPayloads' : JSON.stringify(examCoursesPayloads)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-update-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  populateUpdateExamDimensionModal: function () {
    var examDetails = examDetailsNamespace.dataCache['examDetails'];
    var academicYearDisplayName = examDetails.academicSession.yearDisplayName;
    $("#update-dimension\\.class-exam-session-display").val(academicYearDisplayName);
    var standardName = examDetails.standard.displayName;
    $("#update-dimension\\.class-exam-class-name").val(standardName);
    $('#update-dimension\\.class-exam-name').val(examDetails.examMetaData.examName);

    var dimensions = examDetailsNamespace.dataCache['dimensions'];
    var existingScholasticDimensions = {};
    var existingCoScholasticDimensions = {};

    var examDimensionValuesMap = examDetails.examDimensionValues;
    var scholasticRows = "";
    var coScholasticRows = "";
    if(examDimensionValuesMap != null){
      if((SCHOLASTIC_COURSE_TYPE in examDimensionValuesMap) && examDimensionValuesMap[SCHOLASTIC_COURSE_TYPE].length > 0){
        for(var i=0; i<examDimensionValuesMap[SCHOLASTIC_COURSE_TYPE].length; i++){
          existingScholasticDimensions[examDimensionValuesMap[SCHOLASTIC_COURSE_TYPE][i].examDimension.dimensionId] = examDimensionValuesMap[SCHOLASTIC_COURSE_TYPE][i].examDimension;
          var examDimensionValue = examDimensionValuesMap[SCHOLASTIC_COURSE_TYPE][i];
          if(examDetails.standardsMetaData.scholasticGradingEnabled){
              if(examDimensionValue.examDimension.examEvaluationType == "NUMBER"){
                continue;
              }
              if(examDimensionValue.examDimension.total){
                scholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
                break;
              }
          }else{
              if(examDimensionValue.examDimension.examEvaluationType  == "GRADE"){
                  continue;
              }

              if(examDimensionValue.examDimension.total){
                scholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
                continue;
              }
              scholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
          }
        }
      }

      if((COSCHOLASTIC_COURSE_TYPE in examDimensionValuesMap) && examDimensionValuesMap[COSCHOLASTIC_COURSE_TYPE].length > 0){
        for(var i=0; i<examDimensionValuesMap[COSCHOLASTIC_COURSE_TYPE].length; i++){
          existingCoScholasticDimensions[examDimensionValuesMap[COSCHOLASTIC_COURSE_TYPE][i].examDimension.dimensionId] = examDimensionValuesMap[COSCHOLASTIC_COURSE_TYPE][i].examDimension;
          var examDimensionValue = examDimensionValuesMap[COSCHOLASTIC_COURSE_TYPE][i];
          if(examDetails.standardsMetaData.coScholasticGradingEnabled){
              if(examDimensionValue.examDimension.examEvaluationType == "NUMBER"){
                continue;
              }
              if(examDimensionValue.examDimension.total){
                coScholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
                break;
              }
          }else{
              if(examDimensionValue.examDimension.examEvaluationType  == "GRADE"){
                  continue;
              }

              if(examDimensionValue.examDimension.total){
                coScholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked disabled> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
                continue;
              }
              coScholasticRows += "<tr id=\""+examDimensionValue.examDimension.dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" checked> <span class=\"slider round\"></span> </label> </td> <td> "+examDimensionValue.examDimension.dimensionName+" </td> <td> "+examDimensionValue.examDimension.examEvaluationType+" </td>";
          }

        }
      }
    }

    var scholasticDimension = [];
    for(var i = 0; i < dimensions.length; i++){
      if(dimensions[i].dimensionId in existingScholasticDimensions){
        continue;
      }
      scholasticDimension.push(dimensions[i]);
    }

    var coScholasticDimension = [];
    for(var i = 0; i < dimensions.length; i++){
      if(dimensions[i].dimensionId in existingCoScholasticDimensions){
        continue;
      }
      coScholasticDimension.push(dimensions[i]);
    }

    // There should always be only one dimension (Total Grade) if grading is enabled
    for(var i = 0; i < scholasticDimension.length; i++){
      if(!examDetails.standardsMetaData.scholasticGradingEnabled){
        if(scholasticDimension[i].examEvaluationType  == "GRADE"){
            continue;
        }
        scholasticRows += "<tr id=\""+scholasticDimension[i].dimensionId+"-scholastic\" class=\"scholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\"> <span class=\"slider round\"></span> </label> </td> <td> "+scholasticDimension[i].dimensionName+" </td> <td> "+scholasticDimension[i].examEvaluationType+" </td></tr>";
      }
    }
    $("#update-dimension\\.class-exam-scholastic-dimensions-input-div").find('tbody').html(scholasticRows);

    // There should always be only one dimension (Total Grade) if grading is enabled
    for(var i = 0; i < coScholasticDimension.length; i++){
      if(!examDetails.standardsMetaData.coScholasticGradingEnabled){
        if(coScholasticDimension[i].examEvaluationType == "GRADE"){
            continue;
        }
        coScholasticRows += "<tr id=\""+coScholasticDimension[i].dimensionId+"-coscholastic\" class=\"coscholastic-dimension-row\"> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\"> <span class=\"slider round\"></span> </label> </td> <td> "+coScholasticDimension[i].dimensionName+" </td>  <td> "+coScholasticDimension[i].examEvaluationType+" </td> </tr>";
      }

    }
    $("#update-dimension\\.class-exam-coscholastic-dimensions-input-div").find('tbody').html(coScholasticRows);
  },

  updateExamDimension: function(){
    $("#update-exam-dimension-modal").modal('toggle');
    var examDetails = examDetailsNamespace.dataCache['examDetails']
    var academicSessionId = examDetails.academicSession.academicSessionId;
    var standardId = examDetails.standard.standardId;
    var examId = examDetails.examMetaData.examId;

    var scholasticDimensions = [];
    $("tr.scholastic-dimension-row").each(function() {
          var mandatory = $(this).find('.toggle-switch:checkbox:checked').length > 0;
          if(mandatory){
            var dimensionId = $(this).attr("id").split("-")[0];
            var maxValue = $(this).find('.max-default-value').val();
            var minValue = $(this).find('.min-default-value').val();
            scholasticDimensions.push({'dimensionId': dimensionId, 'maxMarks' : maxValue, 'minMarks' : minValue});
          }
    });

    var coscholasticDimensions = [];
    $("tr.coscholastic-dimension-row").each(function() {
          var mandatory = $(this).find('.toggle-switch:checkbox:checked').length > 0;
          if(mandatory){
            var dimensionId = $(this).attr("id").split("-")[0];
            var maxValue = $(this).find('.max-default-value').val();
            var minValue = $(this).find('.min-default-value').val();
            coscholasticDimensions.push({'dimensionId': dimensionId, 'maxMarks' : maxValue, 'minMarks' : minValue});
          }
    });

    var updateExamDimensionPayload = {'SCHOLASTIC' : scholasticDimensions, 'COSCHOLASTIC':coscholasticDimensions};
    ajaxClient.post("/examination/update-exam-dimension/"+examId, {'updateExamDimensionPayload' : JSON.stringify(updateExamDimensionPayload)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-update-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  updateExamMetaDataPopUp : function () {
    var examDetails = examDetailsNamespace.dataCache['examDetails'];
    $("#update-exam-metadata-modal").find("#update-class-exam-name").val(examDetails.examMetaData.examName);
    $("#update-exam-metadata-modal").find("#update-exam-operation").val(examDetails.examMetaData.operation);
    $("#update-exam-metadata-modal").find("#update-exam-sch-display-type").val(examDetails.examMetaData.scholasticExamMarksDisplayType);
    $("#update-exam-metadata-modal").find("#update-exam-cosch-display-type").val(examDetails.examMetaData.coScholasticExamMarksDisplayType);
    $("#update-exam-metadata-modal").find("#attendance-start-date").val(getFormattedDate(examDetails.examMetaData.attendanceStartDate));
    $("#update-exam-metadata-modal").find("#attendance-end-date").val(getFormattedDate(examDetails.examMetaData.attendanceEndDate));
    $("#update-exam-metadata-modal").find("#exam-start-date").val(getFormattedDate(examDetails.examMetaData.examStartDate));
    $("#update-exam-metadata-modal").find("#date-of-result-declaration").val(getFormattedDate(examDetails.examMetaData.dateOfResultDeclaration));

    $("#update-exam-metadata-modal").modal({backdrop: 'static', keyboard: false});
  },

  updateExamMetadata : function () {
    $("#update-exam-metadata-modal").modal('toggle');
    var examId = examDetailsNamespace.dataCache['examDetails'].examMetaData.examId;
    var examName = $("#update-exam-metadata-modal").find("#update-class-exam-name").val();
    if(examName == null || examName.trim() == ""){
      showErrorDialogBox("Invalid exam name provided.")
      return;
    }
    var operation = $("#update-exam-metadata-modal").find("#update-exam-operation").val();
    if(operation == null || operation.trim() == "" || operation === undefined){
      showErrorDialogBox("Invalid operation selected.")
      return;
    }

    var schDisplayType = $("#update-exam-sch-display-type").val();
    var coschDisplayType = $("#update-exam-cosch-display-type").val();

    var attendanceStartDate = getDate($('#attendance-start-date').val());
    var attendanceStartDateVar = null;
    if(attendanceStartDate != null){
      attendanceStartDateVar = attendanceStartDate.getTime()/1000;
    }

    var attendanceEndDate = getDate($('#attendance-end-date').val());
    var attendanceEndDateVar = null;
    if(attendanceEndDate != null){
      attendanceEndDateVar = attendanceEndDate.getTime()/1000;
    }

    var examStartDate = getDate($('#exam-start-date').val());
    var examStartDateVar = null;
    if(examStartDate != null){
      examStartDateVar = examStartDate.getTime()/1000;
    }

    var dateOfResultDeclaration = getDate($('#date-of-result-declaration').val());
    var dateOfResultDeclarationVar = null;
    if(dateOfResultDeclaration != null){
      dateOfResultDeclarationVar = dateOfResultDeclaration.getTime()/1000;
    }

    var updateExamMetadataPayload = {'examName' : examName, 'operation' : operation, 'attendanceStartDate' : attendanceStartDateVar, 'attendanceEndDate' : attendanceEndDateVar, 'examStartDate' : examStartDateVar, 'dateOfResultDeclaration' : dateOfResultDeclarationVar,  'scholasticExamMarksDisplayType' : schDisplayType, 'coScholasticExamMarksDisplayType' : coschDisplayType};
    ajaxClient.post("/examination/update-exam-metadata/"+examId+"/false", {'updateExamMetadataPayload' : JSON.stringify(updateExamMetadataPayload)}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-update-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  deleteExamPopUp: function(){
      var examDetails = examDetailsNamespace.dataCache['examDetails']
      var academicYearDisplayName = examDetails.academicSession.yearDisplayName;
      var standardName = examDetails.standard.displayName;
      $('#delete-class-exam-courses-modal').find('.modal-title').html("Delete Exam");
      $("#delete-class-exam-courses-modal").modal({backdrop: 'static', keyboard: false});
  },

  submitDeleteExam : function () {
    $("#delete-class-exam-courses-modal").modal('toggle');
    var examId = examDetailsNamespace.dataCache['examDetails'].examMetaData.examId;
    var standardId = examDetailsNamespace.dataCache['examDetails'].standard.standardId;

    ajaxClient.post("/examination/delete-exam/"+examId+"/"+standardId+"/false", {}, function(data){
        $("#global-modal-container").html(data);
        $("#exam-delete-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },


};

var dimensions = {
  loadDimensionsPage : function () {
    ajaxClient.get("/examination/dimension-config", function(data) {
        $("#main-content").html(data);
        dimensions.registerAddDimensionCallback();
        dimensions.registerUpdateDimensionCallBack();
        dimensions.registerDeleteDimensionCallBack();
        bindRemoveErrorDisplayEvent();
    });
  },

  loadDimensionlist : function () {
    ajaxClient.get("/examination/dimension-list", function(data) {
        $("#dimension-list").html(data);
        dimensions.registerUpdateDimensionCallBack();
        dimensions.registerDeleteDimensionCallBack();
    });
  },

  resetDimensionConfigModal: function() {
    $("#add-dimension-modal").find('input').val("");
    $("#add-dimension-modal").find('select').val("");
    // $("#add-dimension-modal").find('textarea').val("");
  },

  registerAddDimensionCallback: function(){
      $('#add-dimension').on('click', function () {
          var invalid = validateMandatoryFields($("#add-dimension-modal"));
          if(invalid){
            return;
          }
          var dimensionName = $("#dimension-name").val().trim();
          var examEvaluationType = $("#exam-evaluation-type option:selected").val();
          $("#add-dimension-modal").modal('toggle');

          var dimensionData = {"dimensionName":dimensionName,"examEvaluationType":examEvaluationType};
          ajaxClient.post("/examination/add-dimension",{'dimensionData':JSON.stringify(dimensionData)}, function(data){

              $("#dimension\\.status-modal-container").html(data);
              $("#exam-config\\.status-modal").modal('toggle');
              dimensions.loadDimensionlist();
         });
      });
  },

  registerUpdateDimensionCallBack: function(){
    $('.update-dimension').on('click', function () {
        var dimensionJson = $(this).parent().find('.dimension-info').text().trim();
        var dimension = JSON.parse(dimensionJson);
        $('#update\\.dimension-id').val(dimension.dimensionId);
        $('#update\\.dimension-name').val(dimension.dimensionName);
        $('#update\\.exam-evaluation-type').val(dimension.examEvaluationType);
        $("#update-dimension-modal").modal('toggle');
    });
  },

  updateDimension: function(){
    var invalid = validateMandatoryFields($("#update-dimension-modal"));
    if(invalid){
      return;
    }
    var dimensionId = $("#update\\.dimension-id").val();
    var dimensionName = $("#update\\.dimension-name").val().trim();
    var examEvaluationType = $("#update\\.exam-evaluation-type").val();
    $("#update-dimension-modal").modal('toggle');

    var updateDimensionData = {"dimensionId" : dimensionId, "dimensionName":dimensionName,"examEvaluationType":examEvaluationType};
    ajaxClient.post("/examination/update-dimension",{'updateDimensionData':JSON.stringify(updateDimensionData)}, function(data){

      $("#dimension\\.status-modal-container").html(data);
      $("#exam-config\\.status-modal").modal('toggle');
      dimensions.loadDimensionlist();
      });
  },

  registerDeleteDimensionCallBack: function(){
    $('.delete-dimension').on('click', function () {
      var dimensionJson = $(this).parent().find('.dimension-info').text().trim();
      var dimension = JSON.parse(dimensionJson);
      $('#delete-dimension-id').val(dimension.dimensionId);
      $("#delete-dimension-confirm-modal").modal('toggle');
    });
  },

  deleteDimension: function(){
    $("#delete-dimension-confirm-modal").modal('toggle');
    var dimensionId = $("#delete-dimension-id").val();
    ajaxClient.post("/examination/delete-dimension/"+dimensionId,{}, function(data){
      $("#dimension\\.status-modal-container").html(data);
      $("#exam-config\\.status-modal").modal('toggle');
      dimensions.loadDimensionlist();
    });
  }
};

var datesheet = {

    dataCache : {},

    initDataCache : function() {
      var examCourses = JSON.parse($("#exam-courses-json").text().trim());
      var examCoursesMap = {};

      for( var i = 0; i < examCourses.length; i++) {
          var examCourseData = examCourses[i];
          examCoursesMap[examCourseData.examMetaData.examId] = examCourseData;
      }
      datesheet.dataCache = {};

      datesheet.dataCache.examCoursesMap = examCoursesMap;
      datesheet.dataCache.selectedCourses = {};
    },

    loadDatesheetPage : function () {

      ajaxClient.get("/examination/datesheet-config", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(datesheet.changeSession);
          datesheet.bindClassChangeEvent();
      });
    },

    changeSession : function () {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      datesheet.loadDatesheetPage();
    },

    bindClassChangeEvent : function () {
      $("#datesheet-config-class").change(function() {
          var standardId = $(this).find(':selected').val().trim();
          if(standardId == ""){
            return;
          }
          datesheet.loadDatesheetList();
        });
    },

    loadDatesheetList : function () {
      var standardId = $("#datesheet-config-class").find(':selected').val();
      if(standardId == ""){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/datesheet-list/" + sessionId + "/" + standardId, function(data) {
          $("#datesheet-list").html(data);
          datesheet.initDataCache();
          datesheet.registerDeleteDatesheetCallback();
          datesheet.addDatesheetPopup();
          bindRemoveErrorDisplayEvent();
      });
    },

    resetAddDatesheetModal : function () {
      $("#class-level-datesheet-container").html("<p class=\"class-add-datesheet-hint-text black-color\"> Select course to add new Datesheet</p>")
      $("#datesheet-modal-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName());
      var standardId = $("#datesheet-config-class").find(':selected').text();
      $("#datesheet-modal-standard").val(standardId);
      $("#datesheet-exam").val("");
      $("#datesheet-exam-start-date").val("");
      $("#datesheet-exam-end-date").val("");
      $("#datesheet-notes").val("");

      $("#datesheet-exam").attr("disabled",false);
//      $("#datesheet-exam-start-date").attr("disabled",false);
//      $("#datesheet-exam-end-date").attr("disabled",false);
      $("#datesheet-notes").attr("disabled",false);
      $("#datesheet-config-modal").find(".modal-footer").css("display","");

      $("#nav-structure-tab").find(".nav-item").removeClass("active");
      $("#nav-class-datesheet-config-tab").addClass("active");

      $("#nav-structure-tabContent").find(".tab-pane").removeClass("show active");
      $("#nav-class-datesheet-config").addClass("show active");
    },

    addDatesheetPopup : function() {
      initFutureDate(360 * 5);
      datesheet.resetAddDatesheetModal();

      $("#datesheet-config-modal").find(".modal-title").html("Add New Datesheet");
      $(".course-dropdown-container").css("display","block");

      datesheet.dataCache.selectedCourses = {};
      datesheet.dataCache.datesheetId = null;

      datesheet.bindSelectCourseDropdown(datesheet.dataCache.selectedCourses);

     $("#datesheet-exam-start-date").closest("div.form-group").css("display", "none");
     $("#datesheet-exam-end-date").closest("div.form-group").css("display", "none");
    },

    bindDimensionRowEvents : function (courseContainerId) {
      $("#"+courseContainerId).find(".delete-dimension-row").on('click', function () {
        $(this).closest(".class-dimension").remove();
      });
    },

    insertDimensionRow : function(courseContainerId, examId, courseId, dimensionDatesheetPayload) {
      var examCoursesMap = datesheet.dataCache.examCoursesMap;
      var examCourseData = examCoursesMap[examId];
      for(var i = 0; i <  examCourseData.examCourses.length; i++){
        if(examCourseData.examCourses[i].course.courseId === courseId){
          var examDimensions = examCourseData.examCourses[i].examDimensionValues;
        }
      }
      var examDimensionOptions = "";
      for(var i = 0; i <  examDimensions.length; i++){
        if(dimensionDatesheetPayload != null){
          if(examDimensions[i].examDimension.dimensionId === dimensionDatesheetPayload.examDimension.dimensionId){
            examDimensionOptions += " <option value="+ examDimensions[i].examDimension.dimensionId+ " selected >"+ examDimensions[i].examDimension.dimensionName+"</option>";
            continue;
          }
        }
        examDimensionOptions += " <option value="+ examDimensions[i].examDimension.dimensionId+ ">"+ examDimensions[i].examDimension.dimensionName+"</option>";
      }
      if(dimensionDatesheetPayload === null){
        var dimensionRow = "<div class=\"card card-border text-center class-dimension\" id=\"class-dimension\"><div class = \"card-body\"><div style=\"float:right;\"> <button type=\"button\" class=\"close delete-dimension-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">×</span> </button> </div><div class=\"form-row\"> <div class=\"form-group col-md-3\"> <label >Dimension*</label><select class=\"form-control mandatory-field class-datesheet-dimension\"> <option value=\"\">-choose-</option>" + examDimensionOptions + " </select> </div> <div class=\"form-group col-md-3\"> <label>Course Exam Date*</label><input type=\"text\" class=\"form-control mandatory-field select-date class-datesheet-exam-date\" placeholder=\"Exam Date\"></div><div class=\"form-group col-md-3  clockpicker\"><label>Start Time*</label> <input type=\"text\" class=\"form-control mandatory-field in-time clockpicker-input class-datesheet-start-time\" placeholder=\"Exam Start Time\"></div> <div class=\"form-group col-md-3 clockpicker\"><label>End Time*</label><input type=\"text\" class=\"form-control mandatory-field clockpicker-input class-datesheet-end-time\" placeholder=\"Exam End Time\"></div> </div><div class=\"form-group\"><label >Syllabus</label><textarea class=\"form-control class-datesheet-syllabus\" rows=\"3\" placeholder=\"Syllabus\"></textarea></div></div></div><br>";
        $("#" + courseContainerId).find(".class-dimension-container").append(dimensionRow);
        initFutureDate(360 * 5);
        $('.clockpicker').clockpicker({
          autoclose: true,
          'default': 'now'
        });
      } else{
        var startTime = getFormattedTime(dimensionDatesheetPayload.startTime.hour) + ":" + getFormattedTime(dimensionDatesheetPayload.startTime.minute);
        var endTime = getFormattedTime(dimensionDatesheetPayload.endTime.hour) + ":" + getFormattedTime(dimensionDatesheetPayload.endTime.minute);
        var dimensionRow = "<div class=\"card card-border text-center class-dimension\" id=\"class-dimension\"><div class = \"card-body\"><div style=\"float:right;\"> <button type=\"button\" class=\"close delete-dimension-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">×</span> </button> </div><div class=\"form-row\"> <div class=\"form-group col-md-3\"> <label >Dimension*</label><select class=\"form-control mandatory-field class-datesheet-dimension\"> <option value=\"\">-choose-</option>" + examDimensionOptions + " </select> </div> <div class=\"form-group col-md-3\"> <label>Course Exam Date*</label><input type=\"text\" class=\"form-control mandatory-field select-date class-datesheet-exam-date\" placeholder=\"Exam Date\" value="+getFormattedDate(dimensionDatesheetPayload.courseExamDate)+"></div><div class=\"form-group col-md-3  clockpicker\"><label>Start Time*</label> <input type=\"text\" class=\"form-control mandatory-field in-time clockpicker-input class-datesheet-start-time\" placeholder=\"Exam Start Time\" value="+startTime+"></div> <div class=\"form-group col-md-3 clockpicker\"><label>End Time*</label><input type=\"text\" class=\"form-control mandatory-field clockpicker-input class-datesheet-end-time\" placeholder=\"Exam End Time\"value="+endTime+"></div> </div><div class=\"form-group\"><label >Syllabus</label><textarea class=\"form-control class-datesheet-syllabus\" rows=\"3\" placeholder=\"Syllabus\">"+dimensionDatesheetPayload.syllabus+"</textarea></div></div></div><br>";
        $("#" + courseContainerId).find(".class-dimension-container").append(dimensionRow);
        initFutureDate(360 * 5);
        $('.clockpicker').clockpicker({
          autoclose: true,
          'default': 'now'
        });
      }
      datesheet.bindDimensionRowEvents(courseContainerId);
    },

    bindCourseContainerEvents: function(entityParentContainerId, courseContainerId, examId, courseId, selectedCourses) {
      $("#"+courseContainerId).find(".add-dimension-button").on('click', function () {
        datesheet.insertDimensionRow(courseContainerId, examId, courseId, null);
      });

      $("#"+courseContainerId).find(".delete-course-row").on('click', function () {
        $(this).closest(".course-datesheet").remove();
        delete selectedCourses[courseId];
        if(isEmpty(selectedCourses)){
          $(entityParentContainerId).find(".class-add-datesheet-hint-text").attr("style","display:block;");
        }
        datesheet.populateSelectCourseDropdown(entityParentContainerId, examId, selectedCourses);
      });
    },

    insertCourseDatesheetCard : function (entityParentContainerId, courseId, examId, selectedCourses) {
      selectedCourses[courseId] = true;
      $(entityParentContainerId).find(".class-add-datesheet-hint-text").attr("style","display:none;");

      var examCoursesMap = datesheet.dataCache.examCoursesMap;
      var examCourseData = examCoursesMap[examId];
      for(var i = 0; i <  examCourseData.examCourses.length; i++){
        if(examCourseData.examCourses[i].course.courseId === courseId){
          var courseName = examCourseData.examCourses[i].course.courseName;
        }
      }
      var courseContainerId = courseId + "-select-" + examId;
      var courseDatesheetCard = "<div class=\"course-datesheet\"> <div class=\"card card-border text-center\" id=\""+courseContainerId+"\"><p class=\"course-id\" style=\"display:none;\">"+ courseId+"</p> <div class=\"card-body\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-course-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">×</span> </button> </div> <h5 class=\"card-title\"> <strong>" + courseName + "</strong> </h5> <div class = \"class-dimension-container\"></div> <button type=\"button\" class=\"btn btn-outline-secondary btn-sm add-dimension-button\"> + Add Dimension</button></div></div></div>";
      $("#class-level-datesheet-container").append(courseDatesheetCard);
      datesheet.bindCourseContainerEvents(entityParentContainerId, courseContainerId, examId, courseId, selectedCourses);
      datesheet.populateSelectCourseDropdown(".class-datesheet", examId, selectedCourses);
    },

    bindCourseSelectEvent : function (entityParentContainerId, selectedCourses) {
      $(entityParentContainerId).find('.course-select-option').on('click', function () {
        var courseId = $(this).attr("id").split("-select-")[0];
        var examId = $(this).attr("id").split("-select-")[1];
        datesheet.insertCourseDatesheetCard(entityParentContainerId, courseId, examId, selectedCourses);
      });
    },

    bindSelectCourseDropdown : function (selectedCourses){
      $("#datesheet-exam").change(function() {
          var examId = $(this).find(':selected').val().trim();
          if(examId == ""){
            return;
          }
          datesheet.populateAddDateSheetPopUp(".class-datesheet", examId, selectedCourses);
        });
    },

    populateSelectCourseDropdown : function(entityParentContainerId, examId, selectedCourses) {
      var examCoursesMap = datesheet.dataCache.examCoursesMap;
      var examCoursesDropdownContent = "";
      examCourseData = examCoursesMap[examId];

      for(var i = 0; i <  examCourseData.examCourses.length; i++){
        if(!(examCourseData.examCourses[i].course.courseId in selectedCourses))
        examCoursesDropdownContent += "<a class=\"dropdown-item course-select-option\" href=\"#\" id=\"" + examCourseData.examCourses[i].course.courseId +"-select-"+examId+"\">" + examCourseData.examCourses[i].course.courseName +"</a>";

      }
      $(entityParentContainerId).find(".course-select-dropdown").html(examCoursesDropdownContent);
      datesheet.bindCourseSelectEvent(".class-datesheet", selectedCourses);
      $("#autofill-button").click(datesheet.autofillTimes);
    },

  generateTableHeader : function (viewOnly) {
   var disabled = viewOnly ? "disabled" : "";
      return "<table id=\"exam-courses-table\" class=\"table\">" +
          "<thead>" +
          "<tr>" +
          "<th style=\"width: 5%; text-align: center;\">" +
          "<label class=\"switch\" style=\"display: inline-block; position: relative; top: 13px;\">" +
          "<input type=\"checkbox\" class=\"primary toggle-switch parent\" id=\"parent-toggle\" checked " + disabled + ">" +
          "<span class=\"slider round\"></span>" +
          "</label>" +
          "</th>" +
          "<th style=\"width: 15%;\">Subject</th>" +
          "<th style=\"width: 10%;\">Dimension</th>" +
          "<th style=\"width: 12%;\">Date</th>" +
          "<th style=\"width: 10%;\">Start Time</th>" +
          "<th style=\"width: 10%;\">End Time</th>" +
          "<th style=\"width: 38%;\">Syllabus</th>" +
          "</tr>" ;
  },

  generateTableFooter : function(){
  return "</thead>" +
         "<tbody>";
  },

  generateAutofillRow : function() {
    return "<tr>" +
           "<th colspan=\"4\" style=\"text-align: right;\"></th>" +
           "<th class=\"form-group clockpicker\" >" +
           "  <input type=\"text\"  class=\"form-control clockpicker-input default-start-time\" placeholder=\"Start Time\">" +
           "</th>" +
           "<th class=\"form-group clockpicker\">" +
           "  <input type=\"text\"  class=\"form-control clockpicker-input default-end-time\" placeholder=\"End Time\" onchange=\"datesheet.validateExamTime(this, 'default-start-time', 'default-end-time')\">" +
           "</th>" +
           "<th>" +
           "  <button id=\"autofill-button\" class=\"btn btn-outline-success btn-sm mb-1\"  style=\"position: relative; float: left; margin-left: 0;\"; onclick=\"datesheet.autofillTimes()\">Autofill</button>" +
           "</th>" +
           "</tr>" ;
  },

  populateAddDateSheetPopUp : function(entityParentContainerId, examId, selectedCourses) {

     var examCoursesMap = datesheet.dataCache.examCoursesMap;

     var examCoursesDropdownContent = "";
     examCourseData = examCoursesMap[examId];

     var tableHtml = datesheet.generateTableHeader(false);
     tableHtml += datesheet.generateAutofillRow();
     tableHtml += datesheet.generateTableFooter();

     for (var i = 0; i < examCourseData.examCourses.length; i++) {
         var course = examCourseData.examCourses[i].course;
         var dimensions = examCourseData.examCourses[i].examDimensionValues;

         for (var j = 0; j < dimensions.length; j++) {
             var dimension = dimensions[j].examDimension;

             if (dimension.total && dimension.examEvaluationType === "NUMBER") {
                 continue;
             }
              tableHtml += datesheet.generateRow(course, dimension, "", "", "", "", true, false);
         }
     }

          tableHtml += "</tbody>" +
                  "</table>";

          $(entityParentContainerId).find("#class-level-datesheet-container").html(tableHtml);
          initFutureDate(360 * 5);
          $('.clockpicker').clockpicker({
              autoclose: true,
              'default': 'now'
          });

          datesheet.bulkToggleChange();
          datesheet.childToggleChange();
  },


    autofillTimes : function() {
       var defaultStartTime = $(".default-start-time").val();
       var defaultEndTime = $(".default-end-time").val();
       $(".class-datesheet-start-time").each(function() {
            if (!$(this).prop("disabled")) {
                 $(this).val(defaultStartTime);
            }
       });

       $(".class-datesheet-end-time").each(function() {
            if (!$(this).prop("disabled")) {
                 $(this).val(defaultEndTime);
            }
       });
    },

    bulkToggleChange : function(){
     $("#parent-toggle").change(function () {
            var isChecked = $(this).is(":checked");
            $(".toggle-switch.child").prop("checked", isChecked);

            $(".toggle-switch.child").each(function () {
               var parentRow = $(this).closest('tr');
               parentRow.find('.class-datesheet-exam-date')
               .prop('disabled', !isChecked)
               .toggleClass('mandatory-field', isChecked)
               .css("border", "");

               parentRow.find('.class-datesheet-start-time')
               .prop('disabled', !isChecked)
               .toggleClass('mandatory-field', isChecked)
               .css("border", "");

               parentRow.find('.class-datesheet-end-time')
               .prop('disabled', !isChecked)
               .toggleClass('mandatory-field', isChecked)
               .css("border", "");

               parentRow.find('.class-datesheet-syllabus')
               .prop('disabled', !isChecked);
            });
        });
    },

    childToggleChange : function () {
         $(".child").change(function () {
            var parentRow = $(this).closest('tr');

            var isDisabled = !$(this).is(':checked');
            parentRow.find('.class-datesheet-exam-date')
            .prop('disabled', isDisabled)
            .toggleClass('mandatory-field', !isDisabled)
            .css("border", "");

            parentRow.find('.class-datesheet-start-time')
            .prop('disabled', isDisabled)
            .toggleClass('mandatory-field', !isDisabled)
            .css("border", "");

            parentRow.find('.class-datesheet-end-time')
            .prop('disabled', isDisabled)
            .toggleClass('mandatory-field', !isDisabled)
            .css("border", "");

            parentRow.find('.class-datesheet-syllabus')
            .prop('disabled', isDisabled)

        });
    },

     validateExamTime: function(endTimeInput, startTimeClass, endTimeClass) {

        var row = $(endTimeInput).closest('tr');
        var startTimeInput = row.find("." + startTimeClass);
        var endTime = $(endTimeInput).val();
        var startTime = startTimeInput.val();
        var startTimeHour = startTime.hour;
        var errorMessage = "Start time should not be greater than or equal to end time.";
        var table = row.closest('div').find('table').parent();
        var existingErrorMessage = table.next('.error-message');
        if (startTime >= endTime) {
              row.find("." + endTimeClass).css("border", "1px solid #ff8795");
               if (existingErrorMessage.length === 0) {
                          table.after('<div class="error-message" style="color: red; margin-top: 10px; text-align: center;">' + errorMessage + '</div>');
               }
               return;
          }

        row.find("." + endTimeClass).css('border', '');
        existingErrorMessage.remove();
    },

//    addDatesheet : function (status) {
//      var invalid = validateMandatoryFields($("#datesheet-config-modal"));
//      if(invalid){
//        showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.", "#datesheet-config-modal");
//        return;
//      }
//
//      var standardId = $("#datesheet-config-class").find(':selected').val();
//      var academicSessionId = academicSessionHandler.getSelectedSessionId();
//
//      $("#datesheet-config-modal").modal('toggle');
//      var examId = $("#datesheet-exam option:selected").val();
//      var examStartDate = getDate($("#datesheet-exam-start-date").val());
//      var examEndDate = getDate($("#datesheet-exam-end-date").val());
//      var examStartDateInt = examStartDate.getTime()/1000;
//      var examEndDateInt = examEndDate.getTime()/1000;
//      var datesheetId = null;
//      var notes = $("#datesheet-notes").val().trim();
//
//      if(datesheet.dataCache.datesheetId != null && datesheet.dataCache.datesheetId.trim() != "" ){
//        datesheetId = datesheet.dataCache.datesheetId;
//      }
//
//
//      var courseWiseDatesheetList =  datesheet.getCourseWiseData('#class-level-datesheet-container', examStartDateInt, examEndDateInt);
//      console.log(courseWiseDatesheetList)
//
//      if(courseWiseDatesheetList.length === 0){
//        showErrorDialogBox("Please select course to add datesheet.");
//        return;
//      }
//
//      var datesheetAndSyllabusPayload = { 'instituteId' : null,'datesheetId': datesheetId, 'academicSessionId' : academicSessionId, 'standardId' : standardId, 'examId' : examId, 'examStartDate' : examStartDateInt, 'examEndDate' : examEndDateInt, 'datesheetStatus' : status, 'notes' : notes, 'courseWiseDatesheetList' : courseWiseDatesheetList};
//
//      var submitDatesheetURL = "/examination/add-datesheet";
//      if(datesheetId != null){
//        submitDatesheetURL = "/examination/update-datesheet";
//      }
//
//      ajaxClient.post(submitDatesheetURL ,{'datesheetAndSyllabusPayload':JSON.stringify(datesheetAndSyllabusPayload)}, function(data){
//        $("#datesheet-status-modal-container").html(data);
//        $("#datesheet-status-modal").modal('toggle');
//        datesheet.loadDatesheetList();
//      });
//
//
//    },

       addDatesheet : function (status) {

          var standardId = $("#datesheet-config-class").find(':selected').val();
          var academicSessionId = academicSessionHandler.getSelectedSessionId();

          $("#datesheet-config-modal").modal('toggle');
          var examId = $("#datesheet-exam option:selected").val();
          var invalid = validateMandatoryFields($("#datesheet-config-modal"));
          if(invalid){
              showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.", "#datesheet-config-modal");
              return;
          }
          var datesheetId = null;
          var notes = $("#datesheet-notes").val().trim();

          if(datesheet.dataCache.datesheetId != null && datesheet.dataCache.datesheetId.trim() != "" ){
            datesheetId = datesheet.dataCache.datesheetId;
          }


          var result =  datesheet.getCourseWiseData('#class-level-datesheet-container');

          if(result.courseWiseDatesheetList.length === 0){
            showErrorDialogBox("Please select course to add datesheet.");
            return;
          }

          var datesheetAndSyllabusPayload = { 'instituteId' : null,'datesheetId': datesheetId, 'academicSessionId' : academicSessionId, 'standardId' : standardId, 'examId' : examId, 'examStartDate' : result.examStartDateInt, 'examEndDate' : result.examEndDateInt, 'datesheetStatus' : status, 'notes' : notes, 'courseWiseDatesheetList' : result.courseWiseDatesheetList};

          var submitDatesheetURL = "/examination/add-datesheet";
          if(datesheetId != null){
            submitDatesheetURL = "/examination/update-datesheet";
          }

          ajaxClient.post(submitDatesheetURL ,{'datesheetAndSyllabusPayload':JSON.stringify(datesheetAndSyllabusPayload)}, function(data){
             $("#datesheet-status-modal-container").html(data);
             $(".modal-backdrop").remove();
             $("#datesheet-status-modal").modal('toggle');
             datesheet.loadDatesheetList();
          });
        },

    registerDeleteDatesheetCallback : function() {
      $('.delete-datesheet-button').on('click', function () {
        var datesheetId = $(this).parent().find('.datesheet-info').html();
        $('#delete-datesheet-id').text(datesheetId);
        $("#delete-datesheet-confirm-modal").modal('toggle');
      });
    },

    deleteDatesheet : function() {
      $("#delete-datesheet-confirm-modal").modal('toggle');
      var datesheetId = $("#delete-datesheet-id").text().trim();
      ajaxClient.post("/examination/delete-datesheet/"+datesheetId, {}, function(data){
          $("#datesheet-status-modal-container").html(data);
          $("#datesheet-status-modal").modal('toggle');
          datesheet.loadDatesheetList();
      });
    },

    getDatesheetDetails : function(datesheetId, viewOnly, ref) {
      datesheet.dataCache.datesheetId = datesheetId;
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var standardId = $("#datesheet-config-class").find(':selected').val().trim();
      ajaxClient.get("/examination/view-datesheet-detail/" + sessionId + "/" + standardId + "/" + datesheetId, function(data) {
          $("#detailed-datesheet-json-container").html(data);
          datesheet.fillDatesheetModal(datesheetId, viewOnly);
      });
    },

    fillDatesheetModal : function (datesheetId, viewOnly) {
      datesheet.resetAddDatesheetModal();

      var sessionId = academicSessionHandler.getSelectedSessionId();
      var standardId = $("#datesheet-config-class").find(':selected').val().trim();

      var datesheetDetail = JSON.parse($("#detailed-datesheet-json").text().trim());
      $("#datesheet-exam").val(datesheetDetail.examMetaData.examId);
      $("#datesheet-exam").attr("disabled",true);
      $("#datesheet-exam-start-date").closest("div.form-group").css("display", "");
      $("#datesheet-exam-end-date").closest("div.form-group").css("display", "");
      $("#datesheet-exam-start-date").val(getFormattedDate(datesheetDetail.examStartDate));
      $("#datesheet-exam-end-date").val(getFormattedDate(datesheetDetail.examEndDate));
      $("#datesheet-notes").val(datesheetDetail.notes);

      if(viewOnly){
        $("#datesheet-config-modal").find(".modal-title").html("View Datesheet");
//        $("#datesheet-exam-start-date").attr("disabled",true);
//        $("#datesheet-exam-end-date").attr("disabled",true);
        $("#datesheet-notes").attr("disabled",true);
      } else {
        $("#datesheet-config-modal").find(".modal-title").html("Update Datesheet");
      }
      $(".course-dropdown-container").css("display","block");
      $("#datesheet-config-modal").find(".modal-footer").css("display","");

      var selectedCourses = {};
      var selectedDimensionsOfCourse = {};
      datesheet.dataCache.selectedCourses = {};

      for(var i =0; i < datesheetDetail.courseWiseDatesheetDetailsList.length; i++){
        selectedCourses[datesheetDetail.courseWiseDatesheetDetailsList[i].course.courseId] = datesheetDetail.courseWiseDatesheetDetailsList[i];
      }

      var examCoursesMap = datesheet.dataCache.examCoursesMap;
      var examCoursesDropdownContent = "";
      examCourseData = examCoursesMap[datesheetDetail.examMetaData.examId];

      datesheet.populateViewUpdateTable(".class-datesheet", selectedCourses, examCourseData.examCourses, viewOnly);
//      for(courseId in selectedCourses){
//        datesheet.insertCourseDatesheetCard(".class-datesheet", courseId, datesheetDetail.examMetaData.examId, datesheet.dataCache.selectedCourses);
//        var courseContainerId = courseId + "-select-" + datesheetDetail.examMetaData.examId;
//        for(var i = 0; i < selectedCourses[courseId].dimensionWiseDatesheetDetailsList.length ; i++){
//          datesheet.insertDimensionRow(courseContainerId, datesheetDetail.examMetaData.examId, courseId, selectedCourses[courseId].dimensionWiseDatesheetDetailsList[i]);
//
//        }
//      }

      if(viewOnly){
        $(".course-dropdown-container").css("display","none");
        $(".add-dimension-button").css("display","none");
        $(".delete-dimension-row").remove();
        $(".delete-course-row").remove();

        $("#fee-structure-name").attr("disabled",true);
        $("#fee-structure-types").attr("disabled",true);
        $("#datesheet-config-modal").find(".modal-footer").css("display","none");

        $(".class-datesheet-dimension").attr("disabled","true");
        $(".class-datesheet-exam-date").attr("disabled","true");
        $(".class-datesheet-start-time").attr("disabled","true");
        $(".class-datesheet-end-time").attr("disabled","true");
        $(".class-datesheet-syllabus").attr("disabled","true");

      }


      datesheet.bindSelectCourseDropdown(datesheet.dataCache.selectedCourses);

    },

    populateViewUpdateTable: function (entityParentContainerId, selectedCourses, examCourses, viewOnly) {
        var tableHtml = datesheet.generateTableHeader(viewOnly);
        if(!viewOnly){
           tableHtml += datesheet.generateAutofillRow();
        }
        tableHtml += datesheet.generateTableFooter();
        var uniqueEntries = new Map();

        for (var courseId in selectedCourses) {
            if (selectedCourses.hasOwnProperty(courseId)) {
                var courseDetails = selectedCourses[courseId];
                var course = courseDetails.course;
                var dimensions = courseDetails.dimensionWiseDatesheetDetailsList;

                for (var j = 0; j < dimensions.length; j++) {
                    var dimension = dimensions[j].examDimension;

                    if (dimension.total && dimension.examEvaluationType === "NUMBER") {
                          continue;
                    }

                    var uniqueKey = courseId + "-" + dimension.dimensionId;
                    if (!uniqueEntries.has(uniqueKey)) {
                        uniqueEntries.set(uniqueKey, true);

                        var startTime = getFormattedTime(dimensions[j].startTime.hour) + ":" +
                            getFormattedTime(dimensions[j].startTime.minute);
                        var endTime = getFormattedTime(dimensions[j].endTime.hour) + ":" +
                            getFormattedTime(dimensions[j].endTime.minute);
                        var examDate = getFormattedDate(dimensions[j].courseExamDate);
                        var syllabus = dimensions[j].syllabus || "";

                        tableHtml += datesheet.generateRow(course, dimension, examDate, startTime, endTime, syllabus, true, viewOnly); // Editable rows
                    }
                }
            }
        }

      if(!viewOnly){
        examCourses.forEach(function (examCourse) {
            var course = examCourse.course;
            var dimensions = examCourse.examDimensionValues;

            dimensions.forEach(function (dimensionData) {
                var dimension = dimensionData.examDimension;

                if (dimension.total && dimension.examEvaluationType === "NUMBER") {
                     return;
                }

                var uniqueKey = course.courseId + "-" + dimension.dimensionId;
                if (!uniqueEntries.has(uniqueKey)) {
                    uniqueEntries.set(uniqueKey, true);

                    tableHtml += datesheet.generateRow(course, dimension, "", "", "", "", false, viewOnly);
                }
            });
        });
        }

        tableHtml += "</tbody>" +
            "</table>";

        $(entityParentContainerId).find("#class-level-datesheet-container").html(tableHtml);
        initFutureDate(360 * 5);
        $('.clockpicker').clockpicker({
             autoclose: true,
             'default': 'now'
        });

        datesheet.bulkToggleChange();
        datesheet.childToggleChange();
    },

    generateRow: function (course, dimension, examDate, startTime, endTime, syllabus, isToggleActive, viewOnly) {
        var toggleChecked = isToggleActive ? "checked" : "";
        var disabled = !isToggleActive ? "disabled" : "";
        var toggleDisable = viewOnly ? "disabled" : "";
        var mandatoryField = isToggleActive ? "mandatory-field" : ""


        return  "<tr class=\"course-datesheet\">" +
            "    <td>" +
            "        <label class=\"switch\" style=\"display: inline-block; position: relative; top: 9px;\">" +
            "            <input type=\"checkbox\" class=\"primary toggle-switch child\" " + toggleChecked + " " + toggleDisable + ">" +
            "            <span class=\"slider round\"></span>" +
            "        </label>" +
            "    </td>" +
            "    <td class=\"course-id\" style=\"display:none;\">" + course.courseId + "</td>" +
            "    <td>" + course.courseName + "</td>" +
            "    <td style=\"display:none;\" class=\"class-datesheet-dimension\">" + dimension.dimensionId + "</td>" +
            "    <td>" + dimension.dimensionName + "</td>" +
            "    <td>" +
            "        <div class=\"form-group\" style=\"padding-top: 15px;\">" +
            "            <input type=\"text\" class=\"form-control " + mandatoryField + " select-date class-datesheet-exam-date\"" +
            "                value=\"" + examDate + "\" placeholder=\"Exam Date\" " + disabled + ">" +
            "        </div>" +
            "    </td>" +
            "    <td>" +
            "        <div class=\"form-group clockpicker\" style=\"padding-top: 15px;\">" +
            "            <input type=\"text\" class=\"form-control " + mandatoryField + " in-time clockpicker-input class-datesheet-start-time\"" +
            "                value=\"" + startTime + "\" placeholder=\"Start Time\" " + disabled + ">" +
            "        </div>" +
            "    </td>" +
            "    <td>" +
            "        <div class=\"form-group clockpicker\" style=\"padding-top: 15px;\">" +
            "            <input type=\"text\" class=\"form-control " + mandatoryField + " clockpicker-input class-datesheet-end-time\"" +
            "                value=\"" + endTime + "\" placeholder=\"End Time\" " + disabled + " onchange=\"datesheet.validateExamTime(this, 'class-datesheet-start-time', 'class-datesheet-end-time')\">" +
            "        </div>" +
            "    </td>" +
            "    <td>" +
            "         <textarea  class=\"form-control class-datesheet-syllabus\"" +
            "             placeholder=\"Syllabus\" " + disabled + ">" + syllabus + "</textarea>" +
            "    </td>" +
            "</tr>";
    },


//    getCourseWiseData : function (entityParentContainerId){
//      var courseWiseDatesheetList = [];
//      $(entityParentContainerId).find(".course-datesheet").each(function () {
//        var courseId = $(this).find(".course-id").text().trim();
//        var dimensionWiseDatesheetList = [];
//        var courseWiseDatesheetPayload = {};
//        var addedDimension = false;
//        $(this).find('.class-dimension-container').find('.class-dimension').each(function () {
//            var invalid = validateMandatoryFields($(".class-dimension"));
//            if(invalid){
//              showErrorDialogBox("Please fill mandatory fields.");
//              return;
//            }
//            addedDimension = true;
//            var dimensionId = $(this).find(".class-datesheet-dimension").find(':selected').val();
//            if(dimensionId == ""){
//              return;
//            }
//            var courseExamDate = getDate($(this).find(".class-datesheet-exam-date").val());
//            var courseExamDateInt = courseExamDate.getTime()/1000;
//
//            var examStartTime = $(this).find(".class-datesheet-start-time").val();
//            if(examStartTime == undefined || examStartTime == "") {
//              return;
//            }
//            var token = examStartTime.split(":");
//
//            var inHourST = token[0];
//            var inMinuteST = token[1];
//            var inSecondST = '00';
//
//            var examStartTimePayload = {'hour' : inHourST, 'minute' : inMinuteST, 'second' : inSecondST};
//
//            var examEndTime = $(this).find(".class-datesheet-end-time").val();
//            if(examEndTime == undefined || examEndTime == "") {
//              return;
//            }
//            var token1 = examEndTime.split(":");
//
//            var inHourET = token1[0];
//            var inMinuteET = token1[1];
//            var inSecondET = '00';
//
//            var examEndTimePayload = {'hour' : inHourET, 'minute' : inMinuteET, 'second' : inSecondET};
//
//            var syllabus = $(this).find(".class-datesheet-syllabus").val().trim();
//
//            dimensionWiseDatesheetList.push({'dimensionId' : dimensionId, 'courseExamDate' : courseExamDateInt, 'startTime' : examStartTimePayload, 'endTime' : examEndTimePayload, 'syllabus' : syllabus });
//
//        });
//        if(addedDimension === true){
//          courseWiseDatesheetList.push({'courseId' : courseId, 'dimensionWiseDatesheetList' : dimensionWiseDatesheetList});
//        }
//      });
//      return courseWiseDatesheetList;
//    },

   getCourseWiseData: function (entityParentContainerId) {
       var courseWiseDatesheetList = [];
       var allExamDates = [];

       $(entityParentContainerId).find("tr.course-datesheet").each(function () {
           var isToggleActive = $(this).find(".toggle-switch.child").is(":checked");
           if (!isToggleActive) {
               return;
           }
           var courseId = $(this).find(".course-id").text().trim();
           if (!courseId) {
               return;
           }

           var dimensionWiseDatesheetList = [];

           var dimensionId = $(this).find(".class-datesheet-dimension").text().trim();
           if (!dimensionId) {
               return;
           }
           var courseExamDateValue = $(this).find(".class-datesheet-exam-date").val();
           if (!courseExamDateValue) {
               return;
           }
           var courseExamDate = getDate(courseExamDateValue);
           var courseExamDateInt = courseExamDate.getTime() / 1000;
           allExamDates.push(courseExamDateInt);

           var examStartTime = $(this).find(".class-datesheet-start-time").val();
           if (!examStartTime) {
               return;
           }
           var startTimeTokens = examStartTime.split(":");
           var examStartTimePayload = {
               'hour': startTimeTokens[0],
               'minute': startTimeTokens[1],
               'second': '00'
           };
           var examEndTime = $(this).find(".class-datesheet-end-time").val();
           if (!examEndTime) {
               return;
           }
           var endTimeTokens = examEndTime.split(":");
           var examEndTimePayload = {
               'hour': endTimeTokens[0],
               'minute': endTimeTokens[1],
               'second': '00'
           };
           var syllabus = $(this).find(".class-datesheet-syllabus").val().trim();

           dimensionWiseDatesheetList.push({
               'dimensionId': dimensionId,
               'courseExamDate': courseExamDateInt,
               'startTime': examStartTimePayload,
               'endTime': examEndTimePayload,
               'syllabus': syllabus
           });

           if (dimensionWiseDatesheetList.length > 0) {
               courseWiseDatesheetList.push({
                   'courseId': courseId,
                   'dimensionWiseDatesheetList': dimensionWiseDatesheetList
               });
           }
       });

       var  examStartDateInt = Math.min.apply(null, allExamDates);
       var   examEndDateInt = Math.max.apply(null, allExamDates);

       return {courseWiseDatesheetList: courseWiseDatesheetList,
               examStartDateInt: examStartDateInt,
               examEndDateInt: examEndDateInt
       }
   },

    generateDatesheetPdf : function(datesheetId) {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var standardId = $("#datesheet-config-class").find(':selected').val().trim();
      window.open(baseURL + "/examination/pdf-datesheet/" + datesheetId + "/" + standardId + "?academic_session_id=" + sessionId, '_blank');
    }

};

var publishMarks = {

    dataCache : {},

    loadPublishMarksPage : function () {

      ajaxClient.get("/examination/publish-exam-config", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(publishMarks.changeSession);
          publishMarks.bindClassChangeEvent();
      });
    },

    changeSession : function () {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      publishMarks.loadPublishMarksPage();
    },

    bindClassChangeEvent : function () {
      $("#publish-exam-config-class").change(function() {
          var standardId = $(this).find(':selected').val().trim();
          if(standardId == ""){
            return;
          }
          publishMarks.loadPublishMarksList();
        });
    },

    loadPublishMarksList : function () {
      var standardId = $("#publish-exam-config-class").find(':selected').val();
      if(standardId == ""){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/publish-exam-list/" + sessionId + "/" + standardId, function(data) {
          $("#publish-exam-list").html(data);
      });
    },

    updateMarksStatusPopup : function (ref, status) {
      var examCourseJson = JSON.parse($(ref).parent().parent().find('.exam-course-json').text().trim());
      var examId = examCourseJson.examMetaData.examId;
      $("#update-exam-id").text(examId);
      $("#update-exam-status").text(status);
      var text = "Do you want to unpublish exam's marks?";
      if(status === "PUBLISHED") {
        text = "Do you want to publish exam's marks?";
      }
      $("#update-exam-status-config-modal-text").text(text);
    },

    updateMarksStatus : function () {
      $("#update-exam-status-config-modal").modal('toggle');
      var examId = $("#update-exam-id").text();
      var status = $("#update-exam-status").text();

      ajaxClient.post("/examination/update-exam-status/"+examId+"/"+status, {}, function(data){
        $("#publish-exam-status-modal-container").html(data);
        $("#publish-exam-status-modal").modal('toggle');
        publishMarks.loadPublishMarksList();
      });
    },

    marksStatusDetails : function (ref) {
      var visibility = $(ref).parent().find('.courses-details-visibility').text();
      if(visibility === "true") {
        $(ref).parent().find('.courses-details-visibility').text(false);
        $(".courses-marks-status-details").remove();
        return;
      }
      var examCourseJson = JSON.parse($(ref).parent().parent().find('.exam-course-json').text().trim());
      var examId = examCourseJson.examMetaData.examId;
      var sessionId = academicSessionHandler.getSelectedSessionId();
      $(".courses-marks-status-details").remove();
      ajaxClient.get("/examination/courses-marks-status-details/" + sessionId + "/" + examId, function(data) {
        $(ref).parent().parent().after(data);
        $(ref).parent().find('.courses-details-visibility').text(true);
      });
    },

    updateCourseMarksStatusPopup : function (ref, status) {
      var dimensionId = $(ref).parent().find('.exam-course-dimension-id').text();
      var courseId = $(ref).parent().find('.exam-course-id').text();
      var examId = $(ref).parent().find('.exam-id').text();
      var sectionId = $(ref).parent().find('.exam-section-id').text();

      $("#update-dimension-id").text(dimensionId);
      $("#update-course-id").text(courseId);
      $("#update-exam-id").text(examId);
      $("#update-exam-section-id").text(sectionId);
      $("#update-exam-status").text(status);

      var text = "Do you want to submit course exam's marks?";

      $("#update-course-exam-status-config-modal-text").text(text);
    },

    updateCourseMarksStatus : function () {
      $("#update-course-exam-status-config-modal").modal('toggle');

      var dimensionId = $("#update-dimension-id").text();
      var courseId = $("#update-course-id").text();
      var examId = $("#update-exam-id").text();
      var sectionId = $("#update-exam-section-id").text();
      var status = $("#update-exam-status").text();

      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var standardId = $("#publish-exam-config-class").find(':selected').val();

      ajaxClient.post("/examination/update-course-exam-status/"+dimensionId+"/"+courseId+"/"+examId+"/"+status+"/"+academicSessionId+"/"+standardId+"?sectionId="+sectionId, {}, function(data){
        $("#publish-exam-status-modal-container").html(data);
        $("#publish-exam-status-modal").modal('toggle');
        publishMarks.loadPublishMarksList();

      });
    },
};

var marksFeeding = {
    dataCache : {},

    loadMainPage : function () {
      ajaxClient.get("/examination/marks-feed-input-page", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(marksFeeding.changeSession);
          marksFeeding.bindClassChangeEvent();
          marksFeeding.dataCache = {};
      });
    },

    changeSession : function () {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/standards/"+sessionId, function(data) {
          $("#standard-with-section-options-wrapper").html(data);
          marksFeeding.bindClassChangeEvent();
          $("#marks-feed-main-container").html("<br><br><br><br> <br> <br> <br><br><br><br> <h5 style=\"text-align:center;color:#223b5c;\"> Select class from top menu to see course exams for marks updation !</h5><br><br><br><br> <br> <br> <br><br><br><br>");
          marksFeeding.dataCache = {};
      });

    },

    clearMarks : function(){
        $("tr.student-course-marks").each(function() {
              $(this).find("td.exam-marks-dimension").each(function() {
                  $(this).find('input').val("");
                  $(this).find('input').removeAttr('readonly');
              });

              $(this).find("td.exam-grade-dimension").each(function() {
                $(this).find('select').val("");
                $(this).find('select').removeAttr('readonly');
              });
          });
          marksFeeding.computeStudentMarksTotal();
    },

    bindClassChangeEvent : function () {
      $("#examination-standard-section-select").change(function() {
         marksFeeding.loadMarksFeedStructure($(this).find(':selected').val().trim());
      });
    },

    loadMarksFeedStructure : function (standardSectionMapId) {
      if(standardSectionMapId == ""){
        return;
      }
      var tokens = standardSectionMapId.split(":");
      var standardId = tokens[0];
      if(standardId == ""){
        return;
      }
      var sectionId = null;
      if(tokens.length == 2){
        sectionId = tokens[1];
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      marksFeeding.dataCache['classMarksStructures'] = []
      marksFeeding.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
      marksFeeding.dataCache['selectedStandard'] = standardId;
      marksFeeding.dataCache['selectedSection'] = sectionId;

      ajaxClient.get("/examination/marks-feed-structure/"+standardId+"/"+sessionId, function(data) {
          $("#marks-feed-main-container").html(data);
          marksFeeding.bindCourseChangeEvent();
          var classMarksStructures = readJson("#class-marks-structures-json");
          marksFeeding.dataCache['classMarksStructures'] = classMarksStructures;
          initSelect2("Select dimension");
      });
    },

    bindCourseChangeEvent : function () {
      $("#marks-structures-courses").change(function() {
          var courseId = $(this).find(':selected').val().trim();
          var classMarksStructures = marksFeeding.dataCache['classMarksStructures'];
          marksFeeding.bindExamChangeEvent();
          for(var i = 0; i < classMarksStructures.length; i++){
            var classMarksStructure = classMarksStructures[i];
            if(classMarksStructure.course.courseId != courseId){
              continue;
            }
            var options = "<option value=\"\" selected>Choose Exam ...</option>";
            for(var j = 0 ; j < classMarksStructure.examDimensionCoursesDataList.length ; j ++){
              var examMetaData = classMarksStructure.examDimensionCoursesDataList[j].examMetaData;
              if(examMetaData)
              {
                options += "<option value=\""+examMetaData.examId+"\">"+examMetaData.examName+"</option>"
              }
            }
            $("#marks-structures-exams").html(options);
            var options = "<option value=\"\" selected>Choose Dimension ...</option>";
            $("#marks-structures-exams-dimension").html(options);
            break;
          }
      });
    },

    bindExamChangeEvent: function(){
      $("#marks-structures-exams").change(function(){
        var examId = $(this).find(':selected').val().trim();
        var dimensionDisplayCount=0;
        var courseId= $("#marks-structures-courses").find(':selected').val().trim();
        var classMarksStructures = marksFeeding.dataCache['classMarksStructures'];
        for(var i = 0; i < classMarksStructures.length; i++){
          var classMarksStructure = classMarksStructures[i];
          if(classMarksStructure.course.courseId != courseId){
            continue;
          }
          for(var j = 0 ; j < classMarksStructure.examDimensionCoursesDataList.length ; j ++){
            var examMetaData = classMarksStructure.examDimensionCoursesDataList[j].examMetaData;
            if(examMetaData.examId != examId)
            {
              continue;
            }
            var options = "";
            var examDimensionValues = classMarksStructure.examDimensionCoursesDataList[j].examDimensionValues;
            for (var k = 0; k < examDimensionValues.length; k++) {
              var examDimensionData = examDimensionValues[k].examDimension;
              if(classMarksStructure.course.courseType=="SCHOLASTIC" && classMarksStructure.scholasticGradingEnabled){
                if(examDimensionData.total && examDimensionData.examEvaluationType=="NUMBER"){
                  marksFeeding.dataCache["totalDimensionId"]=examDimensionData.dimensionId;
                }
                else{
                  options += "<option value=\""+examDimensionData.dimensionId+"\" selected>"+examDimensionData.dimensionName+"</option>"
                  dimensionDisplayCount = dimensionDisplayCount+1
                }
              }
              else if(classMarksStructure.course.courseType=="COSCHOLASTIC" && classMarksStructure.coscholasticGradingEnabled){
                if(examDimensionData.total && examDimensionData.examEvaluationType=="NUMBER"){
                  marksFeeding.dataCache["totalDimensionId"]=examDimensionData.dimensionId;
                }
                else{
                  options += "<option value=\""+examDimensionData.dimensionId+"\" selected>"+examDimensionData.dimensionName+"</option>"
                  dimensionDisplayCount = dimensionDisplayCount+1
                }
              }
              else{
                if(examDimensionData.total && examDimensionData.examEvaluationType=="GRADE"){
                  marksFeeding.dataCache["gradeDimensionId"]=examDimensionData.dimensionId;
                }
                else if(examDimensionData.total && examDimensionData.examEvaluationType=="NUMBER"){
                  marksFeeding.dataCache["totalDimensionId"]=examDimensionData.dimensionId;
                }
                else{
                  options += "<option value=\""+examDimensionData.dimensionId+"\" selected>"+examDimensionData.dimensionName+"</option>"
                  dimensionDisplayCount = dimensionDisplayCount+1
                }
              }
              
            }
            marksFeeding.dataCache['dimensionDisplayCount'] = dimensionDisplayCount;
            $("#marks-structures-exams-dimension").html(options);
            break;
          }
          break;
        }
      });
    },

    loadMarksFeedSheet : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var courseId =  $("#marks-structures-courses").find(':selected').val().trim();
      var examId =  $("#marks-structures-exams").find(':selected').val().trim();
      var courseType;
      var dimensionIdStr = $("#marks-structures-exams-dimension").val();
      var standardIdWithSection = $("#examination-standard-section-select").val().trim();
      var standardIdAndSection = standardIdWithSection.split(":");
      var standardId = standardIdAndSection[0].trim();
      marksFeeding.dataCache['selectedCourseId'] = courseId;
      marksFeeding.dataCache['selectedExamId'] = examId;
      var classMarksStructures = marksFeeding.dataCache['classMarksStructures'];
        for(var i = 0; i < classMarksStructures.length; i++){
          var classMarksStructure = classMarksStructures[i];
          if(classMarksStructure.course.courseId == courseId){
            courseType = classMarksStructure.course.courseType;
          }
        }
      if(courseId == ""){
        showErrorDialogBox("Please select course");
        return;
      }
      if(examId == ""){
        showErrorDialogBox("Please select exam");
        return;
      }
      if(dimensionIdStr==""){
        showErrorDialogBox("please select Dimension");
        return;
      }
      var sectionId = marksFeeding.dataCache['selectedSection'];
      var addRelievedStudents = $("#relieved-student-checkbox").is(":checked");

      var dimensionIds = [];

      var totalGradeDimensionIds = [];
      totalGradeDimensionIds.push(marksFeeding.dataCache["totalDimensionId"]);
      if("gradeDimensionId" in marksFeeding.dataCache){
        totalGradeDimensionIds.push(marksFeeding.dataCache["gradeDimensionId"]);
      }
     
      if (dimensionIdStr.length === marksFeeding.dataCache['dimensionDisplayCount']) {
          dimensionIds = [dimensionIdStr].concat(totalGradeDimensionIds);
      } else {
          dimensionIds = [dimensionIdStr];
      }

      var dimensionIdsParam = dimensionIds.join(",");

      ajaxClient.get("/examination/marks-feed-sheet/" + academicSessionId + "/" + standardId + "/"+examId+"/"+courseId+"?sectionId="+sectionId+"&add_relieved_students="+addRelievedStudents+"&dimension_id_str="+dimensionIdsParam+"&course_type="+courseType, function(data) { 
        $("#student-marks-feed-container").html(data);
          $('#datatables-reponsive').DataTable( {
            "paging":   false,
            searching: false,
            columnDefs: [
              { orderable: false, targets: "no-sort" },
            ],
            order: [[2, 'asc']]
          });
          marksFeeding.bindComputeTotalMarks();
          var examDimensionObtainedValuesMap = JSON.parse($("#exam-dimension-obtained-value-json").text().trim());
          marksFeeding.initExamDimensionObtainedValue(examDimensionObtainedValuesMap);   
      });  
   },

    initExamDimensionObtainedValue : function (examDimensionObtainedValuesMap) {
      examDimensionMaxMarksValue = {};
      examDimensionMinMarksValue = {};
      $.each(examDimensionObtainedValuesMap, function( key, value ) {
        examDimensionMaxMarksValue[value.examDimension.dimensionId] = value.originalMaxMarks;
        examDimensionMinMarksValue[value.examDimension.dimensionId] = value.minMarks;
      });
      marksFeeding.dataCache.examDimensionMaxMarksValue = examDimensionMaxMarksValue;
      marksFeeding.dataCache.examDimensionMinMarksValue = examDimensionMinMarksValue;
    },

    bindComputeTotalMarks : function () {
      $("input.dimension-marks").focus(function() {
      }).blur(function() {
          marksFeeding.computeStudentMarksTotal();
      });

      $(".fill-mark-option").off('click').on('click' , function(e) {
        var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'number');
         inputElement.val("");
         inputElement.prop('readonly', false);
         marksFeeding.computeStudentMarksTotal();
      });

      $(".medical-leave-option").off('click').on('click' , function(e) {
         var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'text');
         inputElement.val("ML");
         inputElement.prop('readonly', true);
         marksFeeding.computeStudentMarksTotal();
      });

      $(".not-joined-option").off('click').on('click' , function(e) {
         var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'text');
         inputElement.val("NJ");
         inputElement.prop('readonly', true);
         marksFeeding.computeStudentMarksTotal();
      });

      $(".absent-option").off('click').on('click' , function(e) {
        var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
        inputElement.attr('type', 'text');
        inputElement.val("AB");
        inputElement.prop('readonly', true);
        marksFeeding.computeStudentMarksTotal();
     });

     $(".exempted-option").off('click').on('click' , function(e) {
      var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
      inputElement.attr('type', 'text');
      inputElement.val("EX");
      inputElement.prop('readonly', true);
      advanceMarksFeeding.computeStudentMarksTotal();
     });

      $('input.dimension-marks').keydown(function (e) {
        if (e.which === 13) {
          if($(this).parent().next().find('input.dimension-marks').length > 0){
            $(this).parent().next().find('input.dimension-marks').focus();
          }else{
            $(this).closest('tr').next().find('input.dimension-marks').eq(0).focus();
          }
        }
     });
    },

    checkMaxMarks : function (ref) {
      var splitArray = $(ref).parent().parent().attr('id').split(":");
      var maxMarks = marksFeeding.dataCache.examDimensionMaxMarksValue[splitArray[0]];
      var minMarks = marksFeeding.dataCache.examDimensionMinMarksValue[splitArray[0]];
      if(maxMarks < parseFloat($(ref).val())) {
        $(ref).css('border-color', 'red');
        $(ref).val("");
        return false;
      }
      else if(minMarks > parseFloat($(ref).val())){
          $(ref).attr('style', 'color:red');
      }
      else {
        $(ref).removeAttr('style');
      }
      return true;
    },

    computeStudentMarksTotal : function () {
      var flag = true;
        $("tr.student-course-marks").each(function() {
            var totalMarks = 0;
            var marksPresent = false;
            $(this).find("input.dimension-marks").each(function() {
              if($(this).attr("type") == "number" && $(this).val().trim() != ""){
                  marksPresent = true;
                  currentFlag = marksFeeding.checkMaxMarks(this);
                  flag &= currentFlag
                  if(currentFlag) {
                    totalMarks +=  parseFloat($(this).val());
                  }
              }
            });
            if(marksPresent){
              $(this).find("input.total-marks").val(totalMarks);
            }else{
              $(this).find("input.total-marks").val("");
            }
        });

        if(!flag) {
          showErrorDialogBox("Obtained marks cannot be greater than Max Marks");
          $(this).val("");
          return;
        }
    },

    submitExamMarks : function (status) {
      if(status == "SUBMITTED"){
        var submit = confirm("You are submitting the marks. Once submitted you cannot update the marks of this course.\n Click on save instead if you want alter marks at later stage. \n Are you sure to submit the marks?")
        if(!submit){
          return;
        }
      }
      var studentMarks = [];
      var courseId = marksFeeding.dataCache['selectedCourseId'];
      var examId = marksFeeding.dataCache['selectedExamId'];
      var sectionId = marksFeeding.dataCache['selectedSection'];

      $("tr.student-course-marks").each(function() {
            var studentId = $(this).attr("id");
            var examDimensionObtainedValuesList = [];
            $(this).find("td.exam-marks-dimension").each(function() {
                  var dimensionId = $(this).attr("id").split(":")[0];
                  var inputElement = $(this).find('input');
                  var obtainedMarks = null;
                  var attendanceStatus = null;
                  if(inputElement.attr("type") == "number"){
                    obtainedMarks = inputElement.val();
                  } else {
                    var inputVal = inputElement.val().trim();
                    if(inputVal == "ML"){
                      attendanceStatus = "MEDICAL_LEAVE";
                    } else if (inputVal == "NJ") {
                      attendanceStatus = "NOT_JOINED";
                    } else if (inputVal == "AB") {
                      attendanceStatus = "ABSENT";
                    } else if (inputVal == "EX") {
                      attendanceStatus = "EXEMPTED";
                    }
                  }
                  var examDimensionObtainedValues = {'examDimension' : {'dimensionId' : dimensionId}, 'obtainedMarks' : obtainedMarks, 'attendanceStatus' : attendanceStatus};
                  examDimensionObtainedValuesList.push(examDimensionObtainedValues);
            });

            $(this).find("td.exam-grade-dimension").each(function() {
                  var dimensionId = $(this).attr("id").split(":")[0];
                  var selectedValue = $(this).find('select').val();
                  var obtainedGrade = null;
                  var attendanceStatus = null;
                  if(selectedValue == "ML"){
                      attendanceStatus = "MEDICAL_LEAVE";
                  } else if (selectedValue == "NJ") {
                    attendanceStatus = "NOT_JOINED";
                  }  else if (selectedValue == "AB") {
                    attendanceStatus = "ABSENT";
                  } else if (selectedValue == "EX") {
                    attendanceStatus = "EXEMPTED";
                  } else {
                    obtainedGrade = {'gradeId':selectedValue} ;
                  }

                  var examDimensionObtainedValues = {'examDimension' : {'dimensionId' : dimensionId}, 'obtainedGrade' : obtainedGrade, 'attendanceStatus' : attendanceStatus};
                  examDimensionObtainedValuesList.push(examDimensionObtainedValues);
            });
            studentMarks.push({'studentId' : studentId, 'examId' : examId, 'courseId' : courseId, 'examDimensionObtainedValues' : examDimensionObtainedValuesList});
      });

      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var standardIdWithSection = $("#examination-standard-section-select").val().trim();
      var standardIdAndSection = standardIdWithSection.split(":");
      var standardId = standardIdAndSection[0].trim();

      ajaxClient.post("/examination/submit-marks/"+status+"/"+academicSessionId+"/"+standardId+"?sectionId="+sectionId, {'studentsMarksPayload' : JSON.stringify(studentMarks)}, function(data){
          $("#marks-feed-status-modal-container").html(data);
          $("#exam-config\\.status-modal").modal({backdrop: 'static', keyboard: false});
      });
    }
};

var advanceMarksFeeding = {

  dataCache : {},

  loadMainPage :function () {
    ajaxClient.get("/examination/advance-marks-feed-input-page", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(advanceMarksFeeding.changeSession);
        advanceMarksFeeding.bindClassChangeEvent();
        advanceMarksFeeding.dataCache = {};
    });
  },

  changeSession : function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/standards/"+sessionId, function(data) {
        $("#standard-with-section-options-wrapper").html(data);
        advanceMarksFeeding.bindClassChangeEvent();
        $("#marks-feed-main-container").html("<br><br><br><br> <br> <br> <br><br><br><br> <h5 style=\"text-align:center;color:#223b5c;\"> Select class from top menu to feed marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> ");
        advanceMarksFeeding.dataCache = {};
    });
  },

  bindClassChangeEvent : function () {
    $("#examination-standard-section-select").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        advanceMarksFeeding.loadExamCourseStructure(standardId);
    });
  },

  loadExamCourseStructure : function (standardSectionMapId) {
    if(standardSectionMapId == ""){
      return;
    }
    var tokens = standardSectionMapId.split(":");
    var standardId = tokens[0];
    if(standardId == ""){
      return;
    }
    var sectionId = null;
    if(tokens.length == 2){
      sectionId = tokens[1];
    }

    var sessionId = academicSessionHandler.getSelectedSessionId();
    advanceMarksFeeding.dataCache['classMarksStructures'] = []
    advanceMarksFeeding.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
    advanceMarksFeeding.dataCache['selectedStandard'] = standardId;
    advanceMarksFeeding.dataCache['selectedSection'] = sectionId;

    var onlyLeaveNode = true;
    ajaxClient.get("/examination/exam-course-structure/"+standardId+"/"+sessionId+"?onlyLeaveNode="+onlyLeaveNode, function(data) {
        $("#marks-feed-main-container").html(data);
        advanceMarksFeeding.bindExamChangeEvent();
        var classExamsCourses = $("#class-exam-json").text().trim();
        if(classExamsCourses ===  "None" || classExamsCourses === undefined) {
          classExamsCourses = "";
        } else {
          classExamsCourses = JSON.parse(classExamsCourses);
        }
        advanceMarksFeeding.dataCache['classExamsCourses'] = classExamsCourses;
        initSelect2("Select Course");
    });
  },

  clearMarks : function(){
      $("tr.student-course-marks").each(function() {
            $(this).find("td.exam-marks-dimension").each(function() {
              $(this).find('input').val("");
              $(this).find('input').removeAttr('readonly');
            });

            $(this).find("td.exam-grade-dimension").each(function() {
              $(this).find('select').val("");
              $(this).find('select').removeAttr('readonly');
            });
        });
        marksFeeding.computeStudentMarksTotal();
  },

  bindExamChangeEvent : function () {
    $("#marks-structures-exams").change(function() {
        var examId = $(this).find(':selected').val().trim();
        var classExamsCourses = advanceMarksFeeding.dataCache['classExamsCourses'];
        for(var i = 0; i < classExamsCourses.length; i++){
          var classExamCourse = classExamsCourses[i];
          if(classExamCourse.examMetaData.examId != examId){
            continue;
          }
          var options = "";
          for(var j = 0 ; j < classExamCourse.examCourses.length ; j ++){
            var examCourse = classExamCourse.examCourses[j];
            options += "<option value=\""+examCourse.course.courseId+"\">"+examCourse.course.courseName+"</option>"
          }
          $("#marks-structures-courses").html(options);
        }
    });
  },

  loadMarksFeedSheet : function () {
      var courseId =  $("#marks-structures-courses").val();
      var examId =  $("#marks-structures-exams").find(':selected').val().trim();
      advanceMarksFeeding.dataCache['selectedCourseId'] = courseId;
      advanceMarksFeeding.dataCache['selectedExamId'] = examId;

      if(courseId == ""){
        showErrorDialogBox("Please select course");
        return;
      }
      if(examId == ""){
        showErrorDialogBox("please select exam");
        return;
      }

      var sectionId = advanceMarksFeeding.dataCache['selectedSection'];
      var addRelievedStudents = $("#relieved-student-checkbox").is(":checked");
      var courseId = courseId.join();


      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var standardIdWithSection = $("#examination-standard-section-select").val().trim();
      var standardIdAndSection = standardIdWithSection.split(":");
      var standardId = standardIdAndSection[0].trim();

      ajaxClient.get("/examination/advance-marks-feed-sheet/"+examId+"/"+standardId+"/"+academicSessionId+"?sectionId="+sectionId+"&courseId="+courseId+"&add_relieved_students="+addRelievedStudents, function(data) {
          $("#student-marks-feed-container").html(data);
          $('#datatables-reponsive').DataTable( {
            "paging":   false,
            searching: false,
            columnDefs: [
              { orderable: false, targets: "no-sort" },
            ],
            order: [[2, 'asc']]
          });
          advanceMarksFeeding.bindComputeTotalMarks();
          var courseExamDimensionObtainedValuesMap = JSON.parse($("#exam-dimension-obtained-value-json").text().trim());
          advanceMarksFeeding.initExamCourseDimensionObtainedValue(courseExamDimensionObtainedValuesMap);
      });
    },

    initExamCourseDimensionObtainedValue : function (courseExamDimensionObtainedValuesMap) {
      examCourseDimensionMaxMarksValue = {};
      examCourseDimensionMinMarksValue = {};
      $.each(courseExamDimensionObtainedValuesMap, function( key1, value1 ) {
        examCourseDimensionMaxMarksValue[key1] = examCourseDimensionMaxMarksValue[key1] || {};
        examCourseDimensionMinMarksValue[key1] = examCourseDimensionMinMarksValue[key1] || {};
        $.each(value1.examDimensionObtainedValues, function( key, value ) {
          examCourseDimensionMaxMarksValue[key1][value.examDimension.dimensionId] = value.originalMaxMarks;
          examCourseDimensionMinMarksValue[key1][value.examDimension.dimensionId] = value.minMarks;
        });
      });
      advanceMarksFeeding.dataCache.examCourseDimensionMaxMarksValue = examCourseDimensionMaxMarksValue;
      advanceMarksFeeding.dataCache.examCourseDimensionMinMarksValue = examCourseDimensionMinMarksValue;
    },

    bindComputeTotalMarks : function () {
      $("input.dimension-marks").focus(function() {
      }).blur(function() {
          advanceMarksFeeding.computeStudentMarksTotal();
      });

      $(".fill-mark-option").off('click').on('click' , function(e) {
        var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'number');
         inputElement.val("");
         inputElement.prop('readonly', false);
         advanceMarksFeeding.computeStudentMarksTotal();
      });

      $(".medical-leave-option").off('click').on('click' , function(e) {
         var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'text');
         inputElement.val("ML");
         inputElement.prop('readonly', true);
         advanceMarksFeeding.computeStudentMarksTotal();
      });

      $(".not-joined-option").off('click').on('click' , function(e) {
         var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
         inputElement.attr('type', 'text');
         inputElement.val("NJ");
         inputElement.prop('readonly', true);
         advanceMarksFeeding.computeStudentMarksTotal();
      });

      $(".absent-option").off('click').on('click' , function(e) {
        var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
        inputElement.attr('type', 'text');
        inputElement.val("AB");
        inputElement.prop('readonly', true);
        advanceMarksFeeding.computeStudentMarksTotal();
     });

     $(".exempted-option").off('click').on('click' , function(e) {
      var inputElement = $(this).closest(".exam-marks-dimension").find("input.dimension-marks");
      inputElement.attr('type', 'text');
      inputElement.val("EX");
      inputElement.prop('readonly', true);
      advanceMarksFeeding.computeStudentMarksTotal();
   });

      $('input.dimension-marks').keydown(function (e) {
        if (e.which === 13) {
          if($(this).parent().next().find('input.dimension-marks').length > 0){
            $(this).parent().next().find('input.dimension-marks').focus();
          }else{
            $(this).closest('tr').next().find('input.dimension-marks').eq(0).focus();
          }
        }
     });
    },

    checkMaxMarks : function (ref) {
      var splitArray = $(ref).parent().parent().attr('id').split(":");
      var maxMarks = advanceMarksFeeding.dataCache.examCourseDimensionMaxMarksValue[splitArray[2]][splitArray[0]];
      var minMarks = advanceMarksFeeding.dataCache.examCourseDimensionMinMarksValue[splitArray[2]][splitArray[0]];
      if(maxMarks < parseFloat($(ref).val())) {
        $(ref).css('border-color', 'red');
        $(ref).val("");
        return false;
      }
      else if(minMarks > parseFloat($(ref).val())){
          $(ref).attr('style', 'color:red');
      }
      else {
        $(ref).removeAttr('style');
      }
      return true;
    },


    computeStudentMarksTotal : function () {
      var flag = true;
        $("tr.student-course-marks").each(function() {
            // var totalMarks = 0;
            var marksPresent = false;
            var courseIdDimensionMap = {};
            $(this).find("input.dimension-marks").each(function() {
              if($(this).attr("type") == "number" && $(this).val().trim() != ""){
                  marksPresent = true;
                  currentFlag = advanceMarksFeeding.checkMaxMarks(this);
                  flag &= currentFlag
                  if(currentFlag) {
                    var courseId = $(this).parent().parent().attr("id").split(":")[2];
                    var obtainedMarks = parseFloat($(this).val());
                    courseIdDimensionMap[courseId] = courseIdDimensionMap[courseId]|| 0;
                    courseIdDimensionMap[courseId] = courseIdDimensionMap[courseId] + obtainedMarks;
                  }
              }
            });
            if (marksPresent) {
              $(this).find("input.total-marks").each(function() {
                  var token = $(this).parent().attr("id").split(":");
                  var dimensionId = token[0].trim();
                  var studentId = token[1].trim();
                  var courseId = token[2].trim();
                  var totalMarks = courseIdDimensionMap[courseId];
                  var totalId = dimensionId + ":" + studentId + ":" + courseId;
                  $(this).val(totalMarks);
              });
            } else {
              $(this).find("input.total-marks").each(function() {
                  $(this).val("");
              });
            }
        });
        if(!flag) {
          showErrorDialogBox("Obtained marks cannot be greater than Max Marks");
          $(this).val("");
          return;
        }
    },

    submitExamMarks : function (status) {
      if(status == "SUBMITTED"){
        var submit = confirm("You are submitting the marks. Once submitted you cannot update the marks of this course.\n Click on save instead if you want alter marks at later stage. \n Are you sure to submit the marks?")
        if(!submit){
          return;
        }
      }
      var studentMarks = [];
      // var courseId = advanceMarksFeeding.dataCache['selectedCourseId'];
      var examId = advanceMarksFeeding.dataCache['selectedExamId'];
      var sectionId = advanceMarksFeeding.dataCache['selectedSection'];

      $("tr.student-course-marks").each(function() {
            var studentId = $(this).attr("id");
            // var examDimensionObtainedValuesList = [];
            var courseIdDimensionMap = {};
            $(this).find("td.exam-marks-dimension").each(function() {
                  var dimensionId = $(this).attr("id").split(":")[0];
                  var courseId = $(this).attr("id").split(":")[2];

                  var inputElement = $(this).find('input');
                  var obtainedMarks = null;
                  var attendanceStatus = null;
                  if(inputElement.attr("type") == "number"){
                    obtainedMarks = inputElement.val();
                  } else {
                    var inputVal = inputElement.val().trim();
                    if(inputVal == "ML"){
                      attendanceStatus = "MEDICAL_LEAVE";
                    } else if (inputVal == "NJ") {
                      attendanceStatus = "NOT_JOINED";
                    } else if (inputVal == "AB") {
                      attendanceStatus = "ABSENT";
                    } else if (inputVal == "EX") {
                      attendanceStatus = "EXEMPTED";
                    } 
                  }


                  var examDimensionObtainedValues = {'examDimension' : {'dimensionId' : dimensionId}, 'obtainedMarks' : obtainedMarks, 'attendanceStatus' : attendanceStatus};
                  courseIdDimensionMap[courseId] = courseIdDimensionMap[courseId] || [];
                  courseIdDimensionMap[courseId].push(examDimensionObtainedValues);
            });
            $(this).find("td.exam-grade-dimension").each(function() {
                  var dimensionId = $(this).attr("id").split(":")[0];
                  var courseId = $(this).attr("id").split(":")[2];

                  var selectedValue = $(this).find('select').val();
                  var obtainedGrade = null;
                  var attendanceStatus = null;
                  if(selectedValue == "ML"){
                      attendanceStatus = "MEDICAL_LEAVE";
                  } else if (selectedValue == "NJ") {
                    attendanceStatus = "NOT_JOINED";
                  } else if (selectedValue == "AB") {
                    attendanceStatus = "ABSENT";
                  } else if (selectedValue == "EX") {
                    attendanceStatus = "EXEMPTED";
                  } else {
                    obtainedGrade = {'gradeId':selectedValue} ;
                  }


                  var examDimensionObtainedValues = {'examDimension' : {'dimensionId' : dimensionId}, 'obtainedGrade' : obtainedGrade, 'attendanceStatus' : attendanceStatus};
                  courseIdDimensionMap[courseId] = courseIdDimensionMap[courseId] || [];
                  courseIdDimensionMap[courseId].push(examDimensionObtainedValues);
            });
            $.each(courseIdDimensionMap, function( key, value ) {
              studentMarks.push({'studentId' : studentId, 'examId' : examId,
              'courseId' : key, 'examDimensionObtainedValues' : value});
            });

      });

      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var standardIdWithSection = $("#examination-standard-section-select").val().trim();
      var standardIdAndSection = standardIdWithSection.split(":");
      var standardId = standardIdAndSection[0].trim();

      ajaxClient.post("/examination/submit-marks/"+status+"/"+academicSessionId+"/"+standardId+"?sectionId="+sectionId, {'studentsMarksPayload' : JSON.stringify(studentMarks)}, function(data){
          $("#marks-feed-status-modal-container").html(data);
          $("#exam-config\\.status-modal").modal({backdrop: 'static', keyboard: false});
      });
    },
};

var marksView = {
  dataCache : {},

  loadMainPage :function () {
    ajaxClient.get("/examination/marks-view-input-page", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(marksView.changeSession);
        marksView.bindClassChangeEvent();
        marksView.dataCache = {};
    });
  },

  changeSession : function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/standards/"+sessionId, function(data) {
        $("#standard-with-section-options-wrapper").html(data);
        marksView.bindClassChangeEvent();
        $("#marks-view-main-container").html("<br><br><br><br> <br> <br> <br><br><br><br> <h5 style=\"text-align:center;color:#223b5c;\"> Select class from top menu to view marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> ");
        marksView.dataCache = {};
    });
  },

  bindClassChangeEvent : function () {
    $("#examination-standard-section-select").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        marksView.loadExamCourseStructure(standardId);
    });
  },

  loadExamCourseStructure : function (standardSectionMapId) {
    if(standardSectionMapId == ""){
      return;
    }
    var tokens = standardSectionMapId.split(":");
    var standardId = tokens[0];
    if(standardId == ""){
      return;
    }
    var sectionId = null;
    if(tokens.length == 2){
      sectionId = tokens[1];
    }

    var sessionId = academicSessionHandler.getSelectedSessionId();
    marksView.dataCache['classMarksStructures'] = []
    marksView.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
    marksView.dataCache['selectedStandard'] = standardId;
    marksView.dataCache['selectedSection'] = sectionId;

    ajaxClient.get("/examination/exam-course-structure/"+standardId+"/"+sessionId, function(data) {
        $("#marks-view-main-container").html(data);
        marksView.bindExamChangeEvent();
        var classExamsCourses = readJson("#class-exam-courses-json");
        marksView.dataCache['classExamsCourses'] = classExamsCourses;
    });
  },

  bindExamChangeEvent : function () {
    $("#class-exams").change(function() {
        var examId = $(this).find(':selected').val().trim();
        var classExamsCourses = marksView.dataCache['classExamsCourses'];
        for(var i = 0; i < classExamsCourses.length; i++){
          var classExamCourse = classExamsCourses[i];
          if(classExamCourse.examMetaData.examId != examId){
            continue;
          }
          var options = "<option value=\"\" selected>Choose Course ...</option>";
          for(var j = 0 ; j < classExamCourse.examCourses.length ; j ++){
            var examCourse = classExamCourse.examCourses[j];
            options += "<option value=\""+examCourse.course.courseId+"\">"+examCourse.course.courseName+"</option>"
          }
          $("#class-exam-courses").html(options);
        }
    });
  },

  loadMarksViewSheet : function () {
    var examId =  $("#class-exams").find(':selected').val().trim();
    var courseId =  $("#class-exam-courses").find(':selected').val().trim();
    marksView.dataCache['selectedCourseId'] = courseId;
    marksView.dataCache['selectedExamId'] = examId;

    if(courseId == ""){
      showErrorDialogBox("Please select course");
      return;
    }
    if(examId == ""){
      showErrorDialogBox("please select exam");
      return;
    }
    var addRelievedStudents = $("#relieved-student-checkbox").is(":checked");
    var sectionId = marksView.dataCache['selectedSection'];

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardIdWithSection = $("#examination-standard-section-select").val().trim();
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();

    ajaxClient.get("/examination/marks-view-sheet/"+academicSessionId+"/"+standardId+"/"+examId+"/"+courseId+"?sectionId="+sectionId+"&add_relieved_students="+addRelievedStudents, function(data) {
        $("#student-marks-view-container").html(data);
        $('#datatables-reponsive').DataTable( {
          "paging":   false,
          searching: false,
          columnDefs: [
            { orderable: false, targets: "no-sort" },
          ],
          order: [[2, 'asc']]
        });
    });
  },

  generateViewMarksReport : function() {

  $(this).closest('div.modal').modal('toggle');
  // var containerElement = $(this).closest('div.report-field-container');
  // var academicSession = $(containerElement).find(".report-academic-session option:selected").val();
  // var standardId = $(containerElement).find(".report-standards option:selected").val();
  // var sectionId = $(containerElement).find(".report-standard-section option:selected").val();
  // var examId = $(containerElement).find(".report-exams option:selected").val();
  // var addRelievedStudents = $(containerElement).find(".relieved-student-checkbox").is(":checked");

  // if(academicSession == ""  || standardId == "" || examId == ""){
  //   showErrorDialogBox("Please select all the required fields to generate report")
  //   return
  // }
  // if(sectionId === undefined) {
  //   sectionId = "";
  // } else {
  //   sectionId = sectionId.trim();
  // }

  // var courseId = "";

  var examId =  $("#class-exams").find(':selected').val().trim();
  var courseId =  $("#class-exam-courses").find(':selected').val().trim();
  marksView.dataCache['selectedCourseId'] = courseId;
  marksView.dataCache['selectedExamId'] = examId;

  if(courseId == ""){
    showErrorDialogBox("Please select course");
    return;
  }
  if(examId == ""){
    showErrorDialogBox("please select exam");
    return;
  }
  var addRelievedStudents = $("#relieved-student-checkbox").is(":checked");
  var sectionId = marksView.dataCache['selectedSection'];

  var academicSessionId = academicSessionHandler.getSelectedSessionId();
  var standardIdWithSection = $("#examination-standard-section-select").val().trim();
  var standardIdAndSection = standardIdWithSection.split(":");
  var standardId = standardIdAndSection[0].trim();
  

  if(sectionId === undefined || sectionId === null) {
    sectionId = "";
  } else {
    sectionId = sectionId.trim();
  }

  var noOfStudentPerPage = $('#student-per-page').val();
  var studentSortingParameters = $("#student-sorting-parameters").val();

  window.open(baseURL+"/examination/generate-feed-marks-sheet/" + academicSessionId + "/" + standardId + "/"+ examId + "?section_id="+sectionId+"&student_count="+noOfStudentPerPage+"&add_relieved_students="+addRelievedStudents+"&course_id="+courseId+"&student_sorting_parameters="+studentSortingParameters, '_blank');

  },

};

var personalityTraits = {

  dataCache : {},

  loadMainPage : function(){

    ajaxClient.get("/examination/personality-traits-home", function(data) {
        $("#main-content").html(data);
        personalityTraits.initPage();
        academicSessionHandler.bindSessionChangeEvent(personalityTraits.changeSession);
    });
  },

  initPage : function(){
    personalityTraits.registerUpdateSectionsModal();
    personalityTraits.registerAddSectionsModal();
    personalityTraits.registerDeleteSectionsModal();
  },

  clearCache : function(){
      personalityTraits.dataCache = {}
  },

  changeSession : function(){
      personalityTraits.loadManageSectionslist();
  },

  loadManageSectionslist : function() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/personality-traits/"+academicSessionId, function(data) {
        $("#personality-traits-list").html(data);
          personalityTraits.initPage();
          personalityTraits.clearCache();
    });
  },

  registerUpdateSectionsModal : function(){
    $('.update-personality-traits').on('click',function(){
      var manageSectionsJson = $(this).parent().find('.personality-traits-info').text().trim();
      var sectionDetails = JSON.parse(manageSectionsJson);
      personalityTraits.dataCache.sectionDetails = sectionDetails;
      $('#update-personality-standard-id').val(sectionDetails.standard.standardId);
      var sectionNameList = "";
      for(var i = 0;i<sectionDetails.personalityTraitsDetailsList.length;i++){
        var sectionName = sectionDetails.personalityTraitsDetailsList[i].personalityTraitName;
        html = "<div class=\"form-group  col-md-8\"><input  type=\"text\" class=\"form-control update-rename-manage-sections add-label-input mandatory-field update.section-name\" placeholder=\"Enter Personality Trait\"   value=\""+sectionName+"\"></div>";
        sectionNameList += html;
      }

      $('#update-personality-traits-div').html(sectionNameList);
      $("#update-manage-sections-modal").modal('toggle');
    });
  },

  registerAddSectionsModal : function(){
    $('.add-personality-traits').on('click',function(){
        var manageSectionsJson = $(this).parent().find('.personality-traits-info').text().trim();
        var sectionDetails = JSON.parse(manageSectionsJson);
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var standardId = sectionDetails.standard.standardId;
        $('#add-personality-standard-id').val(standardId);
        var sectionNameList = "";
        sectionNameList = "<div class=\"form-group row sections-remove col-md-8\"><input id=\"sectionInput\"  type=\"text\" class=\"form-control add-rename-manage-sections col-md-9 add-label-input mandatory-field add.section-name\" placeholder=\"Enter Personality Trait\" ></div>";
        $('#add-personality-traits-div').html(sectionNameList);
        $('#add-manage-sections-modal').modal('toggle');
        personalityTraits.clearCache();
    });
  },

  addMoreClass : function(ref){
        html = $('#add-personality-traits-div').html();
        newHtml = "<div class=\"form-group row sections-remove col-md-8\"><input id=\"sectionInput\"  type=\"text\" class=\"form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name\" placeholder=\"Enter Personality Trait\"  value=\"\"><button type=\"button\" class=\"col-md-2 close\" data-dismiss=\"modal\" aria-label=\"Close\" onclick=\"personalityTraits.removeSectionDetailsRow(this)\"><span aria-hidden=\"true\"> × </span></button></div>";
        $('#add-personality-traits-div').append(newHtml);
  },

  removeSectionDetailsRow : function (ref) {
    $(ref).parent().remove()
  },


  addSectionsDetails: function(){
    var invalid = validations($("#add-manage-sections-modal"));
    if(invalid){
      return;
    }
    var sectionNames = getAddSectionsName();
    var unique = Array.from(new Set(sectionNames));
    var hiddenSectionNames = getHiddenSectionNames();
    var studentsSectionName = "";
    if(sectionNames.length != unique.length){
        $('#add-manage-sections-modal').modal('toggle');
        showErrorDialogBox("Duplicate Personality Traits");
        return;
    }
    var commonSections = commonBetweenTwoNames(sectionNames,hiddenSectionNames);
    var sections = ""
    for(var i=0;i<hiddenSectionNames.length;i++){
      sections += hiddenSectionNames[i]
      if(hiddenSectionNames.length>i+1){
        sections+=","
      }
    }
    if(commonSections.length!=0){
        $('#add-manage-sections-modal').modal('toggle');
        showErrorDialogBox("Personality Trait " + sections + " are already present. Please write a different Personality Traits");
        return;
    }

          var personalityTraitsDetailList = []
          var studentsSectionName = $('input.standard:checked').parent().parent().find('.add-rename-manage-sections').val();
          var academicSessionId = academicSessionHandler.getSelectedSessionId();
          var standardId = $('#add-personality-standard-id').val();

          for(var i=0;i<sectionNames.length;i++){
            section_dict = {"academicSessionId" : academicSessionId, "personalityTraitId":null,"personalityTraitName":sectionNames[i],"standardId":standardId}
            personalityTraitsDetailList.push(section_dict);
          }

          $('#add-manage-sections-modal').modal('toggle');

          ajaxClient.post("/examination/bulk-add-personality-trait/"+academicSessionId,{'personalityTraitsDetailList':JSON.stringify(personalityTraitsDetailList)},function(data){
            $("#manage-sections\\.status-modal-container").html(data);
            $("#manage-sections\\.status-modal").modal('toggle');
             personalityTraits.loadManageSectionslist();
          });
        },


  registerDeleteSectionsModal : function(){
  $('.delete-personality-traits').on('click',function(){
    var manageSectionsJson = $(this).parent().find('.personality-traits-info').text().trim();
    var sectionDetails = JSON.parse(manageSectionsJson);
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = sectionDetails.standard.standardId;
    $('#delete-personality-standard-id').val(standardId);
    var sectionNameList = "";
    for(var i = 0;i<sectionDetails.personalityTraitsDetailsList.length;i++){
      var sectionName = sectionDetails.personalityTraitsDetailsList[i].personalityTraitName;
      var sectionId = sectionDetails.personalityTraitsDetailsList[i].personalityTraitId;
      html = "<div class=\"form-group row sections-remove col-md-8\"><input type=\"checkbox\" class=\"section-id\" name=\"\" value=\"\"><p class=\"bulk-section-id\" style=\"display:none;\">"+sectionId +"</p><input id=\"sectionInput\"  type=\"text\" class=\"form-control ml-2 delete-rename-manage-sections add-label-input mandatory-field delete.section-name col-md-8\" placeholder=\"Enter Personality Trait\"  value=\""+sectionName+"\"readonly></div>";
      sectionNameList += html;
    }
    $('#delete-personality-traits-div').html(sectionNameList);
    $('#delete-manage-sections-modal').modal('toggle');
    personalityTraits.clearCache();
  });
  },

  deleteSectionDetails: function(){

    var invalid = validations($("#delete-manage-sections-modal"));
    if(invalid){
      return;
    }
     var section_info_dict = personalityTraits.getDictionarySectionInfos();
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     var standardId = $('#delete-personality-standard-id').val();
     options = "";
     checkedSection = section_info_dict[0];
     unCheckedSection = section_info_dict[1];


     if(checkedSection.length == 0){
         $('#delete-manage-sections-modal').modal('toggle');
       showErrorDialogBox("Please Select atleast one personality trait");
       return
     }

     if(unCheckedSection.length!=0){
       html1 = "";
       for(var i=0;i< unCheckedSection.length;i++){
            html1 = "<option value="+unCheckedSection[i].personalityTraitId +">"+unCheckedSection[i].personalityTraitName+"</option>";
            options+=html1;
       }
       mainhtml = "<p>Where do you wish to move them?</p>" + "<select class=\"col-md-6 form-control which-section mandatory-field\" name=\"\">"+options+"</select>";
     }
      $('#delete-manage-sections-modal').modal('toggle');


     var section_ids = [];
     var checkedSectionIds = [];
     for(var i=0;i< checkedSection.length; i++){
        sectionId = checkedSection[i].personalityTraitId;
        sectionName = checkedSection[i].personalityTraitName;
        section_ids.push(sectionId)
        checkedSectionIds.push(sectionName);
     }

     section_ids = section_ids.join(",");
     personalityTraits.dataCache.sectionIds = section_ids

         html = "<p style=\"display:none;\" class=\"std-id\">"+standardId+"</p>"+"Do you wish to delete the Personality Traits?";
          $('#delete\\.standard-sections-manage-id').html(html);
         $('#delete-id-manage-sections-modal').modal('toggle');
  },


  deleteStudentSectionsDetails : function(){
    $('#delete-id-manage-sections-modal').modal('toggle');
    var invalid = validations($("#delete-id-manage-sections-modal"));
    if(invalid){
      return;
    }

     section_ids = personalityTraits.dataCache.sectionIds;
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     var standardId = $('.std-id').html();

    ajaxClient.post("/examination/bulk-delete-personality-trait/"+academicSessionId+"/"+standardId+"/"+section_ids,{},function(data){
      $("#manage-sections\\.status-modal-container").html(data);
      $("#manage-sections\\.status-modal").modal('toggle');
       personalityTraits.loadManageSectionslist();
    });
  },

  getDictionarySectionInfos : function(){
    var finalList = [];
    var unCheckedSectionList = [];
    var checkedSectionList = [];
      $("input.section-id").each(function() {
          if(!$(this).is(":checked")) {
            sectionName = $(this).parent().find('input.delete-rename-manage-sections').val();
            sectionId = $(this).parent().find('p.bulk-section-id').first().text().trim();
            section_dict = {"personalityTraitId":sectionId,"personalityTraitName":sectionName}
            unCheckedSectionList.push(section_dict);
          }
          else{
            sectionName = $(this).parent().find('input.delete-rename-manage-sections').val();
            sectionId = $(this).parent().find('p.bulk-section-id').first().text().trim();
            section_dict = {"personalityTraitId":sectionId,"personalityTraitName":sectionName}
            checkedSectionList.push(section_dict);
          }
      });

      finalList.push(checkedSectionList);
      finalList.push(unCheckedSectionList);
      return finalList;
  },

  updateSectionsDetails : function(){
    var invalid = validations($("#update-manage-sections-modal"));
    if(invalid){
      return;
    }
    var sectionNames= [];
    sectionNames = getUpdateSectionsName();
    var unique = Array.from(new Set(sectionNames));
    if(sectionNames.length != unique.length){
      $("#update-manage-sections-modal").modal('toggle');
      showErrorDialogBox("Duplicate Personality Traits");
      return;
    }
    var personalityTraitsDetailList = []
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = $('#update-personality-standard-id').val();
    var standardSectionList = personalityTraits.dataCache.sectionDetails.personalityTraitsDetailsList;
    for(var i=0;i<standardSectionList.length;i++){
      section_dict = {"academicSessionId" : academicSessionId, "personalityTraitId":standardSectionList[i].personalityTraitId,"personalityTraitName":sectionNames[i],"standardId":standardId}
      personalityTraitsDetailList.push(section_dict);
    }
    $("#update-manage-sections-modal").modal('toggle');
    ajaxClient.post("/examination/bulk-update-personality-trait/"+academicSessionId,{'personalityTraitsDetailList':JSON.stringify(personalityTraitsDetailList)},function(data){
      $("#manage-sections\\.status-modal-container").html(data);
      $("#manage-sections\\.status-modal").modal('toggle');
       personalityTraits.loadManageSectionslist();
    });
  },

};

var reportCardVariables = {
  dataCache : {},

  loadMainPage :function () {
    ajaxClient.get("/examination/report-card-variables-input-view", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(reportCardVariables.changeSession);
        reportCardVariables.bindClassChangeEvent();
        // reportCardVariables.loadViewReportCardVariablesSheet();
        reportCardVariables.dataCache = {};
    });
  },

  changeSession : function () {
      reportCardVariables.loadMainPageWithSessionDetails();
  },

  loadMainPageWithSessionDetails :function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/report-card-variables/session-details/" + academicSessionId, function(data) {
        $("#marks-view-main-container").html(data);
       reportCardVariables.bindClassChangeEvent();
       reportCardVariables.dataCache = {};
    });
  },

  bindClassChangeEvent : function () {
    $("#marks-view-class").change(function() {
        var standardSectionMapId = $(this).find(':selected').val().trim();
        reportCardVariables.dataCache.standardSelected = standardSectionMapId;
        if(standardSectionMapId == ""){
          return;
        }
        var tokens = standardSectionMapId.split(":");
        var standardId = tokens[0];
        if(standardId == ""){
          return;
        }
        var sessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/examination/report-card-variable-types/"+sessionId+"/"+standardId, function(data) {
            $("#report-card-type").html(data);
            $("#student-marks-feed-container").empty();
        });
    });

    $("#report-card-type").change(function() {
      $("#student-marks-feed-container").empty();
    });
  },

  loadViewReportCardVariablesScreen : function () {
    var reportCardDetailsOption = $("#report-card-details-option").val();
    if(reportCardDetailsOption === REPORT_CARD_VARIABLES) {
      reportCardVariables.loadViewReportCardVariablesSheet();
    } else if(reportCardDetailsOption === PERSONALITY_TRAITS) {
      reportCardVariables.loadViewPersonalityTraitsSheet();
    }
  },

  loadViewPersonalityTraitsSheet : function () {
      var invalid = validateMandatoryFields($("#page-top-class-report-type"));
      if(invalid){
        showErrorDialogBox("Please fill mandatory fields.")
        return;
      }
      standardSectionMapId = reportCardVariables.dataCache.standardSelected;
      if(standardSectionMapId == ""){
        return;
      }
      var tokens = standardSectionMapId.split(":");
      var standardId = tokens[0];
      if(standardId == ""){
        return;
      }
      var sectionId = null;
      if(tokens.length == 2){
        sectionId = tokens[1];
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var reportType = $("#report-card-type").val();
      ajaxClient.get("/examination/view-personality-trait/"+standardId+"/" + sessionId + "?sectionId="+sectionId+"&reportType="+reportType, function(data) {
          $("#student-marks-feed-container").html(data);
          initDateWithYearRange("-5:+5");
          $('#datatables-reponsive').DataTable( {
            "paging":   false,
            searching: false,
            columnDefs: [
              { orderable: false, targets: "no-sort" },
            ],
            order: [[2, 'asc']]
          });
          reportCardVariables.populatePersonalityTraitSheetData();
      });
  },

  populatePersonalityTraitSheetData : function () {
    $("tr.personality-traits-student-row").each(function() {
          var studentPersonalityTraitDetails = JSON.parse($(this).find(".student_personality_trait_details").text());
          var studentPersonalityTraitsResponseList = studentPersonalityTraitDetails.studentPersonalityTraitsResponseList;
          var responseMap = {};
          if(studentPersonalityTraitsResponseList != null && studentPersonalityTraitsResponseList != undefined) {
            for(var i = 0; i < studentPersonalityTraitsResponseList.length; i++) {
              var studentPersonalityTraitsResponse = studentPersonalityTraitsResponseList[i];
              var response = studentPersonalityTraitsResponse.response;
              var personalityTraitId = studentPersonalityTraitsResponse.personalityTraitsDetails.personalityTraitId;
              responseMap[personalityTraitId] = response;
            }
          }

          $(this).find("input.personality-trait-input").each(function() {
            var id = $(this).attr('id');
            var response = responseMap[id];
            if(response === undefined || response === null) {
              response = "";
            }
            $(this).val(response);
          });
    });
  },

  loadViewReportCardVariablesSheet : function () {
      var invalid = validateMandatoryFields($("#page-top-class-report-type"));
      if(invalid){
        showErrorDialogBox("Please fill mandatory fields.")
        return;
      }
      standardSectionMapId = reportCardVariables.dataCache.standardSelected;
      if(standardSectionMapId == ""){
        return;
      }
      var tokens = standardSectionMapId.split(":");
      var standardId = tokens[0];
      if(standardId == ""){
        return;
      }
      var sectionId = null;
      if(tokens.length == 2){
        sectionId = tokens[1];
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var reportType = $("#report-card-type").val();
      ajaxClient.get("/examination/view-report-card-variables/"+standardId+"/" + sessionId + "?sectionId="+sectionId+"&reportType="+reportType, function(data) {
          $("#student-marks-feed-container").html(data);
          // initDate(365*30);
          initDateWithYearRange("-5:+5");
          $('#datatables-reponsive').DataTable( {
            "paging":   false,
            searching: false,
            columnDefs: [
              { orderable: false, targets: "no-sort" },
            ],
            order: [[1, 'asc']]
          });
      });
  },

  autofillTotalDays : function () {
    var totalDays = $("#report-variable-total-days").val();
    $(".total-days").val(totalDays);
  },

  autofillDateOfResultDeclaration : function () {
    var dateOfResultDeclaration = $('#report-variable-date-of-result-declaration').val();
    $(".date-of-result-declaration").val(dateOfResultDeclaration);
  },

  submitPersonalityTraits : function (session_id, reportTypeStr) {
    var invalid = validateMandatoryFields($("#marks-view-main-container"));
    if(invalid){
      showErrorDialogBox("Please fill mandatory fields.")
      return;
    }
    var studentPersonalityTraitsPayloadList = [];
    $("tr.personality-traits-student-row").each(function() {
          var studentId = $(this).attr("id");
          $(this).find("input.personality-trait-input").each(function() {
            var personalityTraitId = $(this).attr('id');
            var response = $(this).val();
            var studentPersonalityTraitsPayload =  {'studentId' : studentId, 'personalityTraitId' : personalityTraitId, 'remarks' : response};
            studentPersonalityTraitsPayloadList.push(studentPersonalityTraitsPayload);
          });
    });
    var reportType = reportTypeStr;
    if(reportType === undefined || reportType === null || reportType === "") {
      reportType = $("#report-card-type").find(':selected').val();
    }
    var academicSessionId = session_id;
    if(academicSessionId === undefined || academicSessionId === null || academicSessionId === "") {
      academicSessionId = academicSessionHandler.getSelectedSessionId();
    }
    ajaxClient.post("/examination/submit-personality-traits/" + academicSessionId + "/" + reportType, {'studentPersonalityTraitsPayloadList' : JSON.stringify(studentPersonalityTraitsPayloadList)}, function(data){
        $("#report-card-variables-view-container").html(data);
        $("#report-card-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  submitReportCardVariables : function (session_id, reportTypeStr) {
    var invalid = validateMandatoryFields($("#marks-view-main-container"));
    if(invalid){
      showErrorDialogBox("Please fill mandatory fields.")
      return;
    }
    var studentReportCardVariableDetailsPayload = [];
    $("tr.student-row").each(function() {
          var studentId = $(this).attr("id");
          var height = $(this).find('.height').val();
          var weight = $(this).find('.weight').val();
          var attendedDays = $(this).find('.attended-days').val();
          var totalDays = $(this).find('.total-days').val();
          var dateOfResultDeclaration = getDate($(this).find('.date-of-result-declaration').val());
          var dateOfResultDeclarationVar = null;
          if(dateOfResultDeclaration != null){
            dateOfResultDeclarationVar = dateOfResultDeclaration.getTime()/1000;
          }
          var remarks = $(this).find('.remarks').val();
          var remarkDropDown = $(this).find('.remark-dropdown').text();
          if(remarkDropDown === "true") {
             remarks = $(this).find('.remarks option:selected').text();
          }
          var principalRemarks = $(this).find('.principal-remarks').val();
          var principalRemarksDropDown = $(this).find('.principal-remark-dropdown').text();
          if(principalRemarksDropDown === "true") {
             principalRemarks = $(this).find('.principal-remarks option:selected').text();
          }
          var studentReportCardVariableDetails =  {'studentId' : studentId, 'attendedDays' : attendedDays, 'remarks' : remarks, 'principalRemarks' : principalRemarks,
        'height' : height, 'weight' : weight, 'totalDays' : totalDays, 'dateOfResultDeclaration' : dateOfResultDeclarationVar};
        studentReportCardVariableDetailsPayload.push(studentReportCardVariableDetails);
    });
    var reportType = reportTypeStr;
    if(reportType === undefined || reportType === null || reportType === "") {
      reportType = $("#report-card-type").find(':selected').val();
    }
    var academicSessionId = session_id;
    if(academicSessionId === undefined || academicSessionId === null || academicSessionId === "") {
      academicSessionId = academicSessionHandler.getSelectedSessionId();
    }
    var reportCardVariableDetails = {'instituteId' : null, 'reportType' : reportType, 'academicSessionId' : academicSessionId, 'studentReportCardVariableDetailsPayload' : studentReportCardVariableDetailsPayload};
    ajaxClient.post("/examination/submit-report-card-variables", {'reportCardVariableDetails' : JSON.stringify(reportCardVariableDetails)}, function(data){
        $("#report-card-variables-view-container").html(data);
        $("#report-card-status-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  openReportCard : function(studentId) {

    var reportType = $("#report-card-type").find(':selected').val();
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    standardSectionMapId = reportCardVariables.dataCache.standardSelected;
    if(standardSectionMapId == ""){
      return;
    }
    var tokens = standardSectionMapId.split(":");
    var standardId = tokens[0];
    if(standardId == ""){
      return;
    }
    var sectionId = null;
    if(tokens.length == 2){
      sectionId = tokens[1];
    }

    var newWindow = window.open(baseURL + "/examination/report-card-variable-page/" + academicSessionId + "/" + studentId + "/" + reportType + "/" + standardId + "?sectionId="+sectionId+"&reportType="+reportType, '_blank');

    reportCardVariables.loadIFrame(academicSessionId, studentId, reportType, newWindow);

    hideLoader();

  },

  loadIFrame : function (academicSessionId, studentId, reportType, newWindow) {
    ajaxClient.get("/examination/student-report-variable-pdf/" + academicSessionId + "/" + studentId + "/" + reportType, function(data) {
      $("#report-card-byte-array-div").html(data);

      var byteList = JSON.parse($("#report-card-byte-array").text());

      var pdfByteArray = new Uint8Array(byteList);
      var pdfBase64 = reportCardVariables.uint8ArrayToBase64(pdfByteArray);
      var pdfDataUri = "data:application/pdf;base64," + pdfBase64 + "#toolbar=0&navpanes=0&scrollbar=0";

      newWindow.document.getElementById("report-card-iframe").src=pdfDataUri;
      $("#report-card-byte-array-div").html("");

      hideLoader();

    });
  },

  uint8ArrayToBase64 : function( bytes ) {
			var binary = '';
			var len = bytes.byteLength;
			for (var i = 0; i < len; i++) {
				binary += String.fromCharCode( bytes[ i ] );
			}
			return window.btoa( binary );

		}
};

var gradeRowConfig = {
  gradeId: "",
  gradeValue: "",
  marksRangeStart: "",
  marksRangeEnd: "",
  gradeName: "",
  rangeDisplayName: "",
  remark: "",
  creditScore: "",
  newTd: "",
  marksRangeStartDisabled: "",
  marksRangeEndDisabled: "",
  gradeNameDisabled: "",
  rangeDisplayNameDisabled: "",
  remarkDisabled: "",
  creditScoreDisabled: "",
};

var gradeScheme = {

  dataCache : {
  },

  loadGradeSchemeListPage: function(){
    ajaxClient.get("/examination/exam-grade-details", function(data){
      $("#main-content").html(data);
      academicSessionHandler.bindSessionChangeEvent(gradeScheme.changeSession);
      $("#grade-screen-back-button").hide();
      initSelect2("Select Class");
      gradeScheme.populateGradeSchemeModal();
    });
  },
  changeSession: function() {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/exam-grade-details-with-session/"+sessionId, function(data) {
      $("#grade-scheme-div").html(data);
      initSelect2("Select Class");
      $("#grade-screen-back-button").hide();
      gradeScheme.populateGradeSchemeModal();
    }); 
  },
  populateGlobalHtmlCode: function(tableHeading, scholasticLastRow, coScholasticLastRow, headingTitle) {
    if (tableHeading) {
        if (scholasticLastRow || coScholasticLastRow) {
            return `
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th scope="col" colspan="6">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <span style="font-size: 1rem;">
                                                ${headingTitle}
                                            </span>
                                            ${coScholasticLastRow ? `
                                                <div style="position: absolute; right: 20%;">
                                                    <button type="button" class="btn btn-outline-primary btn-sm copyGrade"  onclick="gradeScheme.copyGrade()">
                                                        Same As Scholastic Grade
                                                    </button>
                                                </div>
                                            ` : ''}
                                        </div>
                                    </th>
                                </tr>
                                <tr class="text-center">
                                    <th scope="col">Start Range</th>
                                    <th scope="col">End Range</th>
                                    <th scope="col">Grade Name</th>
                                    <th scope="col">Display Range Name</th>
                                    <th scope="col">Remarks</th>
                                    <th scope="col">Credit Score</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
        }
    } else if (scholasticLastRow || coScholasticLastRow) {
        const id = scholasticLastRow 
            ? 'add-scholastic-grade-add-row-bottom' 
            : 'add-coscholastic-grade-add-row-bottom';
        return `
            <tr id="${id}" style="display:none;"></tr>
            </tbody>
            </table>
            </div>
            </div>
        `;
    }
},
  populateGlobalTableRow: function(config) {
    return `
      <tr class="${config.trClass}">
        <td>
          <p class="grade-id" style="display:none">${config.gradeId}</p>
          <p class="grade-value" style="display:none">${config.gradeValue}</p>
          <p class="grade-name" style="display:none">${config.gradeName}</p>
          <input type="number" class="form-control form-control-sm add-start-range" placeholder="Start Range..." value="${config.marksRangeStart}" ${config.marksRangeStartDisabled}>
        </td>
        <td>
          <input type="number" class="form-control form-control-sm add-end-range" placeholder="End Range..." value="${config.marksRangeEnd}" ${config.marksRangeEndDisabled}>
        </td>
        <td>
          <input type="text" class="form-control form-control-sm add-grade-name" placeholder="Grade Name..."  value="${config.gradeName}" ${config.gradeNameDisabled}>
        </td>
        <td>
          <input type="text" class="form-control form-control-sm add-display-range-name" placeholder="Display Range Name..." value="${config.rangeDisplayName}" ${config.rangeDisplayNameDisabled}>
        </td>
        <td>
          <input type="text" class="form-control form-control-sm add-remark" placeholder="Remarks..." value="${config.remark}" ${config.remarkDisabled}>
        </td>
        <td>
          <input type="text" class="form-control form-control-sm add-credit-score" placeholder="Credit Score..." value="${config.creditScore}" ${config.creditScoreDisabled}>
        </td>
        ${config.newTd}
      </tr>
    `;
  },
  populateAddGradeSchemeModal : function(){
    $(".select2-search__field").css('width', '100%');
    $(".screen-name").text('ADD');
    $("#action-grade-detail-button").text('Add Grade Details');
    var viewScholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, true, false, 'Scholastic Grade');
    viewScholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, true, false, 'Scholastic Grade');
    $("#add-scholastic-grade-input-div").html(viewScholasticGradeDivStartContent);
    var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
    viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true, 'Co-Scholastic Grade');
    $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
    $("#grade-details-div").hide();
    $("#grade-screen-back-button").show();
    $(".add-grade-div").hide();
    $("#update-grade-scheme-screen").show();
  },
  gradeInfoDataFormating: function(viewGradeDivStartContent, gradeInfo, rowId, gradeName) {
    if (gradeInfo === undefined) {
      if (gradeName == "RENAME" || gradeName == "VIEW") {
        var gradeRow = `<tr class="text-center"><td colspan=5 style="text-align: center;">No Grade</td></tr>`;
        viewGradeDivStartContent += gradeRow;
      }
      return viewGradeDivStartContent;
    }
    for (var i = 0; i < gradeInfo.length; i++) {
      var grade = gradeInfo[i];
      var config = Object.assign({}, gradeRowConfig, {
        gradeId: grade.gradeId,
        gradeValue: grade.gradeValue,
        marksRangeStart: grade.marksRangeStart * 100,
        marksRangeEnd: (grade.marksRangeEnd == 1.1 ? 100 : grade.marksRangeEnd * 100 >= 100 ? 100 : grade.marksRangeEnd * 100),
        gradeName: grade.gradeName,
        rangeDisplayName: grade.rangeDisplayName,
        remark: grade.remarks || "",
        creditScore: grade.creditScore === 0 ? 0 : grade.creditScore || "",
        trClass: rowId,
        marksRangeStartDisabled: "disabled",
        marksRangeEndDisabled: "disabled",
        gradeNameDisabled: "",
        rangeDisplayNameDisabled: "",
        remarkDisabled: "",
        creditScoreDisabled: "",
        newTd: ""
      });

      // Handle Rename, Update, and View Grades based on flags
      if (gradeName == "RENAME") {
        viewGradeDivStartContent += gradeScheme.populateGlobalTableRow(config);
      }
      if (gradeName == "UPDATE") {
        config.gradeId = "";
        config.gradeValue = "";
        config.marksRangeStartDisabled = "";
        config.marksRangeEndDisabled = "";
        config.gradeNameDisabled = "";
        config.rangeDisplayNameDisabled = "";
        config.remarkDisabled = "";
        config.creditScoreDisabled = "";
        config.newTd = `<td><button type="button" onclick="gradeScheme.deleteGradeRow(this)" class="close delete-grade-row" aria-label="Close"><span aria-hidden="true">&times;</span></button></td>`;
        viewGradeDivStartContent += gradeScheme.populateGlobalTableRow(config);
      }
      if (gradeName == "VIEW") {
        config.marksRangeStartDisabled = "disabled";
        config.marksRangeEndDisabled = "disabled";
        config.gradeNameDisabled = "disabled";
        config.rangeDisplayNameDisabled = "disabled";
        config.remarkDisabled = "disabled";
        config.creditScoreDisabled = "disabled";
        config.newTd = "";
        viewGradeDivStartContent += gradeScheme.populateGlobalTableRow(config);
      }
    }
    return viewGradeDivStartContent;
  },
  populateGradeSchemeModal : function () {
    $('.rename-grade-details').on('click', function(){
        var gradeInfoJson = $(this).parent().parent().parent().find(".grade-scheme-info").text().trim();
        var standardInfoJson = $(this).parent().parent().parent().find(".grade-standard-info").text().trim();
        var gradeInfo = JSON.parse(gradeInfoJson);
        var standardInfo = JSON.parse(standardInfoJson);
        $('#standard-selected-details').val(standardInfo.standardId).trigger('change');
        $(".screen-name").text('RENAME');
        $("#action-grade-detail-button").text('Update Grade Details');
        var viewScholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, true, false, 'Scholastic Grade');
        viewScholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewScholasticGradeDivStartContent, gradeInfo.SCHOLASTIC, "new-scholastic-grade-row", "RENAME");
        viewScholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, true, false, 'Scholastic Grade');
        $("#add-scholastic-grade-input-div").html(viewScholasticGradeDivStartContent);
        
        var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
        viewCoscholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewCoscholasticGradeDivStartContent, gradeInfo.COSCHOLASTIC, "new-co-scholastic-grade-row", "RENAME");
        viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true, 'Co-Scholastic Grade');
        $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
        if (gradeInfo.COSCHOLASTIC == undefined) {
          $(".copyGrade").hide();
        }
        $("#grade-screen-back-button").show();
        $("#grade-details-div").hide();
        $(".ranges").hide();
        $("#add-scholastic-grade-button").hide();
        $("#add-coscholastic-grade-button").hide();
        $(".add-grade-div").hide();
        $("#update-grade-scheme-screen").show();
    });
    $('.update-grade-details').on('click', function(){
      var gradeInfoJson = $(this).parent().parent().parent().find(".grade-scheme-info").text().trim();
      var standardInfoJson = $(this).parent().parent().parent().find(".grade-standard-info").text().trim();
      var gradeInfo = JSON.parse(gradeInfoJson);
      var standardInfo = JSON.parse(standardInfoJson);
      $('#standard-selected-details').val(standardInfo.standardId).trigger('change');
      $('#startRange').prop('checked', true);
      $(".screen-name").text('UPDATE');
      $("#action-grade-detail-button").text('Update Grade Details');
      var viewScholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, true, false, 'Scholastic Grade');
      viewScholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewScholasticGradeDivStartContent, gradeInfo.SCHOLASTIC, "new-scholastic-grade-row", "UPDATE");
      viewScholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, true, false, 'Scholastic Grade');
      $("#add-scholastic-grade-input-div").html(viewScholasticGradeDivStartContent);
      
      var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
      viewCoscholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewCoscholasticGradeDivStartContent, gradeInfo.COSCHOLASTIC, "new-co-scholastic-grade-row", "UPDATE");
      viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true, 'Co-Scholastic Grade');
      $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
      $("#grade-details-div").hide();
      $("#grade-screen-back-button").show();
      $(".add-grade-div").hide();
      $("#update-grade-scheme-screen").show();
    });
    $('.view-grade').on('click', function(){
      var gradeInfoJson = $(this).parent().parent().find(".grade-scheme-info").text().trim();
      var standardInfoJson = $(this).parent().parent().find(".grade-standard-info").text().trim();
      var gradeInfo = JSON.parse(gradeInfoJson);
      var standardInfo = JSON.parse(standardInfoJson);
      $('#standard-selected-details').val(standardInfo.standardId).trigger('change').prop('disabled', true);;
      var viewScholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, true, false, 'Scholastic Grade');
      viewScholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewScholasticGradeDivStartContent, gradeInfo.SCHOLASTIC, "new-scholastic-grade-row", "VIEW");
      viewScholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, true, false, 'Scholastic Grade');
      $("#add-scholastic-grade-input-div").html(viewScholasticGradeDivStartContent);
      var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
      viewCoscholasticGradeDivStartContent = gradeScheme.gradeInfoDataFormating(viewCoscholasticGradeDivStartContent, gradeInfo.COSCHOLASTIC, "new-co-scholastic-grade-row", "VIEW");
      viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true, 'Co-Scholastic Grade');
      $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
      $("#grade-details-div").hide();
      $(".ranges").hide();
      $("#grade-screen-back-button").show();
      $(".add-grade-div").hide();
      $(".copyGrade").hide();
      $("#close-grade-detail-button").hide();
      $("#action-grade-detail-button").hide();
      $("#add-scholastic-grade-button").hide();
      $("#add-coscholastic-grade-button").hide();
      $("#update-grade-scheme-screen").show();
    });
    $('.delete-grade').on('click', function(){
      var standardInfoJson = $(this).parent().parent().find(".grade-standard-info").text().trim();
      var standardInfo = JSON.parse(standardInfoJson);
      $('#delete-grade-standard-id').text(standardInfo.standardId);
      $('#delete-manage-grade-modal').modal('toggle');
    });
    $('#close-grade-detail-button').on('click', function(){ 
      $(".update-grade-scheme-screen").hide();
      $("#grade-details-div").show();
      $(".ranges").show();
      $(".add-grade-div").show();
      $(".copyGrade").show();
      $("#add-scholastic-grade-button").show();
      $("#add-coscholastic-grade-button").show();
      $("#grade-screen-back-button").hide();
      gradeScheme.loadGradeSchemeListPage();
    });
    $('#grade-screen-back-button').on('click', function(){   
      $(".update-grade-scheme-screen").hide();
      $("#grade-details-div").show();
      $(".ranges").show();
      $(".add-grade-div").show();
      $(".copyGrade").show();
      $("#add-scholastic-grade-button").show();
      $("#add-coscholastic-grade-button").show();
      $("#grade-screen-back-button").hide();
      $("#close-grade-detail-button").show();
      $("#action-grade-detail-button").show();
      gradeScheme.loadGradeSchemeListPage();
    });
    $('#action-grade-detail-button').on('click',function(){
      var screen = $(".screen-name").text();
      if(screen == 'RENAME'){
        gradeScheme.renameGradeDetail();
      }
      else if(screen == 'UPDATE'){
        gradeScheme.gradeDetailInputData('UPDATE');
      }
      else if(screen == 'ADD'){
        gradeScheme.gradeDetailInputData('ADD');
      }
    });
  },

  copyGrade: function(){
    var screen = $(".screen-name").text();
    if(screen == 'RENAME'){
      gradeScheme.copyRenameScholasticGradeDetail();
    }
    else if(screen == 'UPDATE'){
      gradeScheme.copyAddOrUpdateScholasticGradeDetail();
    }
    else if(screen == 'ADD'){
      gradeScheme.copyAddOrUpdateScholasticGradeDetail();
    }
  },
  getBlankGradeRow : function (actionType) {
    var rowClass =  "new-grade-row"
    return "<tr class=\""+rowClass+"\"> <td><input type=\"number\" class=\"form-control form-control-sm add-start-range\" style=\"width: 75px;\" placeholder=\"Start Range...\"></td> <td><input type=\"number\" class=\"form-control form-control-sm add-end-range\" style=\"width: 75px;\" placeholder=\"End Range...\"></td>  <td><input type=\"text\" class=\"form-control form-control-sm add-grade-name\" placeholder=\"Grade Name...\" style=\"width: 75px;\"></td> <td><input type=\"text\" class=\"form-control form-control-sm add-display-range-name\" style=\"width: 150px;\" placeholder=\"Display Range Name...\"></td> <td><input type=\"text\" class=\"form-control form-control-sm add-remark\" placeholder=\"Remarks...\"></td> <td><input type=\"text\" class=\"form-control form-control-sm add-credit-score\" placeholder=\"Credit Score...\"></td><td> <button type=\"button\" onclick=\"gradeScheme.deleteGradeRow(this)\" class=\"close delete-grade-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
  },
  addNewScholasticGradeRow : function () {
    var bottomRowId = "add-scholastic-grade-add-row-bottom";
     if(bottomRowId == null){
       return;
     }
      var config = Object.assign({}, gradeRowConfig, {
        gradeId: "",
        gradeValue: "",
        marksRangeStart: "",
        marksRangeEnd: "",
        gradeName: "",
        rangeDisplayName: "",
        remark: "",
        creditScore: "",
        trClass: "new-scholastic-grade-row",
        marksRangeStartDisabled: "",
        marksRangeEndDisabled: "",
        gradeNameDisabled: "",
        rangeDisplayNameDisabled: "",
        remarkDisabled: "",
        creditScoreDisabled: "",
        newTd: "<td> <button type=\"button\" onclick=\"gradeScheme.deleteGradeRow(this)\" class=\"close delete-grade-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td>"
      });
     $("#"+bottomRowId).before(gradeScheme.populateGlobalTableRow(config));
 },
 addNewCoScholasticGradeRow : function () {
  var bottomRowId = "add-coscholastic-grade-add-row-bottom";
   if(bottomRowId == null){
     return;
   }
    var config = Object.assign({}, gradeRowConfig, {
        gradeId: "",
        gradeValue: "",
        marksRangeStart: "",
        marksRangeEnd: "",
        gradeName: "",
        rangeDisplayName: "",
        remark: "",
        creditScore: "",
        trClass: "new-co-scholastic-grade-row",
        marksRangeStartDisabled: "",
        marksRangeEndDisabled: "",
        gradeNameDisabled: "",
        rangeDisplayNameDisabled: "",
        remarkDisabled: "",
        creditScoreDisabled: "",
        newTd: "<td> <button type=\"button\" onclick=\"gradeScheme.deleteGradeRow(this)\" class=\"close delete-grade-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td>"
    });
   $("#"+bottomRowId).before(gradeScheme.populateGlobalTableRow(config));
},

 deleteGradeRow(ref) {
    $(ref).parent().parent().remove();
 },
 deleteGradeDetails : function(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = $('#delete-grade-standard-id').text().trim();
    var standardIdList = [standardId];

    $('#delete-manage-grade-modal').modal('toggle');
    ajaxClient.post("/examination/delete-grades/"+academicSessionId+"?standardIdList="+standardIdList, {},function(data){
      $("#grade-scheme-generation-status-modal-container").html(data);
      $("#grade-status-modal").modal('toggle');
      $("#grade-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function() {
        gradeScheme.loadGradeSchemeListPage();
      });
    });
 },
 copyAddOrUpdateScholasticGradeDetail : function(){
  var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
    $("tr.new-scholastic-grade-row").each(function() {
      var marksRangeStart = $(this).find('.add-start-range').val().trim();
      var marksRangeEnd = $(this).find('.add-end-range').val().trim();
      var gradeName = $(this).find('.add-grade-name').val().trim();
      var rangeDisplayName = $(this).find('.add-display-range-name').val().trim();
      var remarks = $(this).find('.add-remark').val().trim();
      var creditScores = $(this).find('.add-credit-score').val().trim();
      var config = Object.assign({}, gradeRowConfig, {
        gradeId: "",
        gradeValue: "",
        marksRangeStart: marksRangeStart,
        marksRangeEnd: marksRangeEnd,
        gradeName: gradeName,
        rangeDisplayName: rangeDisplayName,
        remark: remarks,
        creditScore: creditScores,
        trClass: "new-co-scholastic-grade-row",
        marksRangeStartDisabled: "",
        marksRangeEndDisabled: "",
        gradeNameDisabled: "",
        rangeDisplayNameDisabled: "",
        remarkDisabled: "",
        creditScore: "",
        newTd: "<td> <button type=\"button\" onclick=\"gradeScheme.deleteGradeRow(this)\" class=\"close delete-grade-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td>"
      });
      var gradeRow = gradeScheme.populateGlobalTableRow(config);
      viewCoscholasticGradeDivStartContent += gradeRow;
    });
    viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true);
    viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true);
    $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
 },
 copyRenameScholasticGradeDetail : function(){
  var coScholasticRows = $("tr.new-co-scholastic-grade-row"); // Get all rows of new-co-scholastic-grade-row
  var scholasticRows = $("tr.new-scholastic-grade-row"); // Get all rows of new-scholastic-grade-row
  var viewCoscholasticGradeDivStartContent = gradeScheme.populateGlobalHtmlCode(true, false, true, 'Co-Scholastic Grade');
  // Loop through each row of new-co-scholastic-grade-row and new-scholastic-grade-row together
  coScholasticRows.each(function(index) {
    var gradeId = $(this).find('.grade-id').text().trim();
    var gradeValue = $(this).find('.grade-value').text().trim();
    var marksRangeStart = $(this).find('.add-start-range').val().trim();
    var marksRangeEnd = $(this).find('.add-end-range').val().trim();

    // Get the corresponding row from new-scholastic-grade-row
    var scholasticRow = scholasticRows.eq(index); // Get the row at the same index from new-scholastic-grade-row
    var gradeName = scholasticRow.find('.add-grade-name').val().trim();
    var rangeDisplayName = scholasticRow.find('.add-display-range-name').val().trim();
    var remarks = scholasticRow.find('.add-remark').val().trim();
    var creditScores = scholasticRow.find('.add-credit-score').val().trim();
    
    // Generate the HTML content
    var config = Object.assign({}, gradeRowConfig, {
        gradeId: gradeId,
        gradeValue: gradeValue,
        marksRangeStart: marksRangeStart,
        marksRangeEnd: marksRangeEnd,
        gradeName: gradeName,
        rangeDisplayName: rangeDisplayName,
        remark: remarks,
        creditScore: creditScores,
        trClass: "new-co-scholastic-grade-row",
        marksRangeStartDisabled: "disabled",
        marksRangeEndDisabled: "disabled",
        gradeNameDisabled: "",
        rangeDisplayNameDisabled: "",
        remarkDisabled: "",
        creditScores: "",
        newTd: ""
      });
    var gradeRow = gradeScheme.populateGlobalTableRow(config);
    
    // Append the generated row to the content
    viewCoscholasticGradeDivStartContent += gradeRow;
  });
  viewCoscholasticGradeDivStartContent += gradeScheme.populateGlobalHtmlCode(false, false, true, 'Co-Scholastic Grade');
  $("#add-coscholastic-grade-input-div").html(viewCoscholasticGradeDivStartContent);
},
 renameGradeDetail : function(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardIdList = $('#standard-selected-details').val();
    
    if (!standardIdList || standardIdList.length <= 0) {
      alert("Please select at least one class to add/update grades.");
      return;
    }

    var gradeSchemeMap = {};
    var courseTypeGradeSchemeMap = {};
    var isValid = true;
    
    $("tr.new-scholastic-grade-row").each(function() {
          var gradeId = $(this).find('.grade-id').text().trim();
          var gradeValue = $(this).find('.grade-value').text().trim();
          var oldGradeName = $(this).find('.grade-name').text().trim();
          var marksRangeStart = $(this).find('.add-start-range').val().trim();
          var marksRangeEnd = $(this).find('.add-end-range').val().trim();
          var gradeName = $(this).find('.add-grade-name').val().trim();
          var rangeDisplayName = $(this).find('.add-display-range-name').val().trim();
          var remarks = $(this).find('.add-remark').val().trim();
          var creditScore = $(this).find('.add-credit-score').val().trim();
          
          if(gradeName == "" || rangeDisplayName == ""){
            alert("Field Can't be Empty for Grade Name or Display Range Name");
            isValid = false;
            return false;
          }

          gradeSchemeMap[oldGradeName]=({'gradeId': gradeId, 'gradeName' : gradeName, 'gradeValue' : gradeValue, 'marksRangeStart':marksRangeStart, 'marksRangeEnd':marksRangeEnd, 'rangeDisplayName': rangeDisplayName, 'remarks': remarks, 'creditScore': creditScore});
    });
    
    if (!isValid) return;
    
    courseTypeGradeSchemeMap['SCHOLASTIC']=gradeSchemeMap;
    gradeSchemeMap={};
    var isValid = true;
    
    $("tr.new-co-scholastic-grade-row").each(function() {
          var gradeId = $(this).find('.grade-id').text().trim();
          var gradeValue = $(this).find('.grade-value').text().trim();
          var oldGradeName = $(this).find('.grade-name').text().trim();
          var marksRangeStart = $(this).find('.add-start-range').val().trim();
          var marksRangeEnd = $(this).find('.add-end-range').val().trim();
          var gradeName = $(this).find('.add-grade-name').val().trim();
          var rangeDisplayName = $(this).find('.add-display-range-name').val().trim();
          var remarks = $(this).find('.add-remark').val().trim();
          var creditScore = $(this).find('.add-credit-score').val().trim();
          
          if(gradeName == "" || rangeDisplayName == ""){
            alert("Field Can't be Empty for Grade Name or Display Range Name");
            isValid = false;
            return false;
          }
          gradeSchemeMap[oldGradeName]=({'gradeId': gradeId, 'gradeName' : gradeName, 'gradeValue' : gradeValue, 'marksRangeStart':marksRangeStart, 'marksRangeEnd':marksRangeEnd, 'rangeDisplayName': rangeDisplayName, 'remarks': remarks, 'creditScore': creditScore});
  });

  if (!isValid) return;
  
  courseTypeGradeSchemeMap['COSCHOLASTIC']=gradeSchemeMap;
  var gradeRenamePayload={"standardIdList":standardIdList, "courseTypeExaminationGradesMap":courseTypeGradeSchemeMap}

  ajaxClient.post("/examination/rename-grades/"+academicSessionId, {"gradeRenamePayload" : JSON.stringify(gradeRenamePayload)},function(data){
    $("#grade-scheme-generation-status-modal-container").html(data);
    $("#grade-status-modal").modal('toggle');
  });
 },


 gradeDetailInputData: function(type) {
  var academicSessionId = academicSessionHandler.getSelectedSessionId();
  var standardIdList = $('#standard-selected-details').val();
  var marksRangeStartInclusive = $('#startRange').prop('checked');
  var marksRangeEndInclusive = $('#endRange').prop('checked');

  if (!standardIdList || standardIdList.length <= 0) {
    alert("Please select at least one class to add/update grades.");
    return;
  }
  if (!marksRangeStartInclusive && !marksRangeEndInclusive) {
      alert("At least one should be checked from 'Marks Starting Range Inclusive' and 'Marks Ending Range Inclusive'");
      return;
  }

  var gradeSchemeList = [];
  var courseTypeGradeSchemeMap = {};
  var isValid = true;

  $("tr.new-scholastic-grade-row").each(function() {
      var marksRangeStart = $(this).find('.add-start-range').val().trim();
      var marksRangeEnd = $(this).find('.add-end-range').val().trim();
      var gradeName = $(this).find('.add-grade-name').val().trim();
      var rangeDisplayName = $(this).find('.add-display-range-name').val().trim();
      var remarks = $(this).find('.add-remark').val().trim();
      var creditScore = $(this).find('.add-credit-score').val().trim();

      if (marksRangeStart === "" || marksRangeEnd === "" || gradeName === "" || rangeDisplayName === "") {
          alert("Field Can't be Empty for Start Range or End Range or Grade Name or Display Range Name");
          isValid = false;
          return false; // Break the loop
      }

      gradeSchemeList.push({
          'gradeName': gradeName,
          'marksRangeStart': marksRangeStart,
          'marksRangeEnd': marksRangeEnd,
          'rangeDisplayName': rangeDisplayName,
          'remarks': remarks,
          'creditScore': creditScore
      });
  });

  if (!isValid) return; // Exit the add/update function if invalid data is found

  courseTypeGradeSchemeMap['SCHOLASTIC'] = gradeSchemeList;
  gradeSchemeList = [];
  isValid = true;

  $("tr.new-co-scholastic-grade-row").each(function() {
      var marksRangeStart = $(this).find('.add-start-range').val().trim();
      var marksRangeEnd = $(this).find('.add-end-range').val().trim();
      var gradeName = $(this).find('.add-grade-name').val().trim();
      var rangeDisplayName = $(this).find('.add-display-range-name').val().trim();
      var remarks = $(this).find('.add-remark').val().trim();
      var creditScore = $(this).find('.add-credit-score').val().trim();


      if (marksRangeStart === "" || marksRangeEnd === "" || gradeName === "" || rangeDisplayName === "") {
          alert("Field Can't be Empty for Start Range or End Range or Grade Name or Display Range Name");
          isValid = false;
          return false; // Break the loop
      }

      gradeSchemeList.push({
          'gradeName': gradeName,
          'marksRangeStart': marksRangeStart,
          'marksRangeEnd': marksRangeEnd,
          'rangeDisplayName': rangeDisplayName,
          'remarks': remarks,
          'creditScore': creditScore
      });
  });

  if (!isValid) return; // Exit the add/update function if invalid data is found

  courseTypeGradeSchemeMap['COSCHOLASTIC'] = gradeSchemeList;
  if (
    (!courseTypeGradeSchemeMap['SCHOLASTIC'] || courseTypeGradeSchemeMap['SCHOLASTIC'].length === 0) &&
    (!courseTypeGradeSchemeMap['COSCHOLASTIC'] || courseTypeGradeSchemeMap['COSCHOLASTIC'].length === 0)
  ) {
    alert("Both SCHOLASTIC and COSCHOLASTIC grade schemes are empty. Please provide valid entries.");
    return;
}
  var gradeAddUpdatePayload = {
      "standardIdList": standardIdList,
      "marksRangeStartInclusive": marksRangeStartInclusive,
      "marksRangeEndInclusive": marksRangeEndInclusive,
      "courseTypeExaminationGradesMap": courseTypeGradeSchemeMap,
      "dataUpdationAction": type
  };
  ajaxClient.post("/examination/add-update-grades/" + academicSessionId, {"gradeAddUpdatePayload" : JSON.stringify(gradeAddUpdatePayload)}, function(data) {
      $("#grade-scheme-generation-status-modal-container").html(data);
      $("#grade-status-modal").modal('toggle');
  });
}

};
var reportCard = {

  dataCache : {},

  loadReportCardPage: function(){
    ajaxClient.get("/examination/report-card-generation-homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(reportCard.changeSession);
        reportCard.bindSearchStudentEvent();
        reportCard.bindReportCardGenerationOptions();
        reportCard.bindClassChangeEvent();
    });
  },

  changeSession: function() {
      $("#report-card-generation-search-student-text").val("");
      $("#student-report-card-view").html("");
      $(".report-card-types").html("");

      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/standards/"+sessionId, function(data) {
          $(".standard-with-section-options-wrapper").html(data);
          reportCard.bindClassChangeEvent();
      });
  },

  bindReportCardGenerationOptions :function () {
    $('#student-report-card-generation-option').on('click', function () {
          $(".report-card-screen").attr("style","display:none;");
          $("#student-report-card-screen").attr("style","display:block;");
          reportCard.loadReportCardPage();
      });

      $('#bulk-student-report-card-generation-option').on('click', function () {
            $(".report-card-screen").attr("style","display:none;");
            $("#bulk-student-report-card-screen").attr("style","display:block;");
        });

        $('#class-report-card-generation-option').on('click', function () {
             $(".report-card-screen").attr("style","display:none;");
             $("#class-report-card-screen").attr("style","display:block;");
         });
  },

  loadClassStudents : function () {
    var reportType = $(".report-card-types").val();
    var standardSection = $('#examination-standard-section-select').val();
    if(standardSection =="" || reportType == undefined){
      showErrorDialogBox("Please select standard and report type");
      return;
    }
    $("#send-notifications-class-student-list").attr('style','display:block')
    var standardIdAndSection = standardSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1){
      sectionId = standardIdAndSection[1].trim();
    }
    var sessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.get("/examination/class-students/"+sessionId+"?standards="+standardId+"&sectionId="+sectionId, function(data) {
        $("#send-notifications-class-student-list").html(data);
    });
  },

  bindClassChangeEvent : function () {
    $(".report-card-change").change(function() {
      $('#send-notifications-class-student-list').attr('style','display:none;');
        var standardIdWithSection = $(this).find(':selected').val().trim();
        var standardIdAndSection = standardIdWithSection.split(":");
        var standardId = standardIdAndSection[0].trim();
        var sectionId = ""
        if(standardIdAndSection[1] != undefined);
          var sectionId = standardIdAndSection[1];
        if(standardId == ""){
          return;
        }
        var sessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/examination/report-card-types/"+sessionId+"/"+standardId, function(data) {
            $(".class-report").html(data);
        });
    });
  },

  bindSearchStudentEvent: function() {
    var resultArea = "#report-card-generation-student-search-result";
    $('#report-card-generation-search-student').on('click', function () {
         reportCard.doneStudentSearchTyping(resultArea);
    });
    $("#report-card-generation-search-student-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
          reportCard.doneStudentSearchTyping(resultArea);
      }
    });
    liveSearchHandler.bindEvent('#report-card-generation-search-student-text',resultArea, reportCard.doneStudentSearchTyping);
  },

  doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#report-card-generation-search-student-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED,RELIEVED";
      studentLiveSearchEvent(sessionId, searchText, resultArea, reportCard.loadStudentReportCard, status);
  },

  loadStudentReportCard: function(studentId) {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/report-card-student-details/"+sessionId+"/"+studentId, function(data) {
        $("#student-report-card-view").html(data);
        reportCard.registerReportCardGenerationCallBack();
    });
  },

  registerReportCardGenerationCallBack: function(){
    $("#student-generate-report-card-button").on('click', function () {
        var studentId = $("#report-card-view-student-id").text().trim();
        var reportType = $("#report-card-type").val().trim();
        reportCard.generateReportCard(studentId, reportType);
    })
  },

  generateReportCard: function(studentId, reportType){
    var sessionId = academicSessionHandler.getSelectedSessionId();
    window.open(baseURL + "/examination/student-report-card/" + sessionId + "/" + studentId + "/" + reportType, '_blank');
  },

  studentSelectCheckbox : function(studentSelectCheckbox) {
         var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
         $("#students-selected-count").html(selectedStudentCount);
   },

   selectStudentList : function (selectAllCheckbox) {
        if(selectAllCheckbox.checked){
            $(".student-select-checkbox").prop('checked', true);
            var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
            $("#students-selected-count").html(selectedStudentCount);
        }
        else{
            $(".student-select-checkbox").prop('checked', false);
            $("#students-selected-count").html(0);
        }
    },

  generateStudentsBulkReportCard : function(){
    var standardIdWithSection = $("#examination-standard-section-select").val().trim();
    var reportType = $("#class-report-type").find(".report-card-types").find(':selected').val().trim();
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    studentIds = studentIds.join(",");

    if(standardIdWithSection == ""){
      showErrorDialogBox("Please select class for generating report");
      return;
    }
    if (reportType == ""){
      showErrorDialogBox("Please select report type for generating report");
      return;
    }
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1) {
        sectionId = standardIdAndSection[1].trim();
    }

    window.open(baseURL + "/examination/bulk-student-report-card/" + sessionId + "/"+ studentIds + "/" + standardId + "/" + reportType+"?sectionId="+sectionId, '_blank');
  },

  generateClassReportCard: function(dataReport){
    var standardIdWithSection = $(".class-standard-filter").find('.report-card-change').val();
    var sessionId = academicSessionHandler.getSelectedSessionId();

    if(standardIdWithSection == ""){
      showErrorDialogBox("Please select class for generating report");
      return;
    }

    var reportType = $("#only-class-report-type").find(".report-card-types").find(':selected').val().trim();
    if (reportType == ""){
      showErrorDialogBox("Please select report type for generating report");
      return;
    }
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1) {
        sectionId = standardIdAndSection[1].trim();
    }
    var studentPerPage = "";
    if(dataReport) {
      studentPerPage = $("#student-per-page").val();
      $("#generate-class-report-data-modal").modal('toggle');
    }

    window.open(baseURL + "/examination/class-report-card/" + sessionId + "/" + standardId + "/" + reportType+"?sectionId="+sectionId+"&studentPerPage="+studentPerPage, '_blank');
  }
};

var hpc = {

  dataCache : {},

  loadHPCFormPage: function(){
    ajaxClient.get("/examination/hpc/form/homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(hpc.changeSession);
        hpc.bindSearchStudentEvent();
        hpc.dataCache = {}
    });
  },

  changeSession: function() {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/standards/"+sessionId, function(data) {
          $("#standard-with-section-options-wrapper").html(data);
          $("#student-hpc-form-view").html("");
          $("#relieved-student-checkbox").prop('checked', false);
          hpc.dataCache = {}
      });
  },

  bindSearchStudentEvent: function() {
    var resultArea = "#hpc-form-generation-student-search-result";
    $('#hpc-form-generation-search-student').on('click', function () {
      hpc.doneStudentSearchTyping(resultArea);
    });
    $("#hpc-form-generation-search-student-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
        hpc.doneStudentSearchTyping(resultArea);
      }
    });
    liveSearchHandler.bindEvent('#hpc-form-generation-search-student-text',resultArea, hpc.doneStudentSearchTyping);
  },

  doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#hpc-form-generation-search-student-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED";
      studentLiveSearchEvent(sessionId, searchText, resultArea, hpc.loadStudentHPCForm, status);
  },

  loadStudentHPCForm: function(studentId, studentInfo) {
    hpc.dataCache['selectedStudentId'] = studentId;
    hpc.dataCache['selectedStudentInfo'] = studentInfo;
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var examType = $("#hpc-exam-type").val().trim();
    $("#hpc-form-student-name-adm-no").text(studentInfo);
    $("#student-hpc-form-generation-search-tab").attr('style','display:none');
    $("#hpc-academic-session-div").attr('style','display:none');
    $("#fill-hpc-form-navbar").attr('style','display: ');

    ajaxClient.get("/examination/hpc/student-form/"+sessionId+"/"+studentId+"/"+examType, function(data) {
        $("#student-hpc-form-view").html(data);
        hpc.registerUploadFileCallback();
    });
  },

  submitForm : async function (status, formUserType, thisContext) {
    if(status == "SUBMITTED"){
      var submit = confirm("You are submitting the form. Once submitted you cannot update the form of this student.\n Click on save instead if you want alter form at later stage. \n Are you sure to submit the form?")
      if(!submit){
        return;
      }
    }
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var examType = $("#hpc-exam-type").val().trim();
    var studentId = hpc.dataCache['selectedStudentId'];

    var fieldValues = [];

    $(thisContext).closest('.student-hpc-form').find("input.hpc-form-value").each(function() {
          var fieldId = $(this).attr("id");
          var fieldValue = $(this).val().trim();
          fieldValues.push({'id' : fieldId, 'value' : fieldValue});
    });

    $(thisContext).closest('.student-hpc-form').find("select.hpc-form-dropdown-value").each(function() {
      var fieldId = $(this).attr("id");
      var fieldValue = $(this).find("option:selected").text().trim();
      fieldValues.push({'id' : fieldId, 'value' : fieldValue});
});

    var hpcDocumentPayloadList = [];
    var hpcDocuments = [];

    var errorInImage = false;
    $(thisContext).closest('.student-hpc-form').find("input.hpc-form-document").each(function() {
      
      var fileList = $(this)[0].files;
      if(fileList.length <= 0) {
        return;
      }
      
      for(var i = 0; i < fileList.length; i++) {
        var file = fileList[i];
        // var file = await compressFileUtils.compress(uncompressedFile);
        if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
          errorInImage = true;
          showErrorDialogBox("Size Of document cannot be greater than " + FILE_SIZE_LIMIT + " kb");
          return;
        }
        if(errorInImage) {
          return;
        }
        hpcDocuments.push(file);

        var fieldId = $(this).attr("id");
        var dataUpdationAction = "ADD";

        var documentType = $(this).parent().find(".image-type").text();
        var documentName = (studentId + "-" + documentType);
        var document = {'documentType' : documentType, 'documentName' : documentName};
        hpcDocumentPayloadList.push({'id' : fieldId, 'document' : document, 'dataUpdationAction' : dataUpdationAction});

      }

      if(errorInImage) {
        return;
      }

    });


    console.log(hpcDocumentPayloadList);
    console.log(hpcDocuments);
    console.log(errorInImage);

    if(errorInImage) {
      return;
    }

    var payload = {'academicSessionId' : academicSessionId, 'studentId' : studentId, 'examType' : examType, 'formUserType' : formUserType, 'status' : status, 'fieldValues' : fieldValues, 'hpcDocumentPayloadList' : hpcDocumentPayloadList};
    var formData = new FormData();
    Array.from(hpcDocuments).forEach(file => formData.append('document', file));
    formData.append('payload', JSON.stringify(payload));
    ajaxClient.uploadFile("/examination/hpc/form-data", formData, function(data){
        $("#hpc-status-modal-container").html(data);
        $("#hpc-form-status-modal").modal({backdrop: 'static', keyboard: false});
        hpc.loadStudentHPCForm(studentId, hpc.dataCache['selectedStudentInfo']);
    });
  },

  loadClassStudents : function(){
     var sessionId = academicSessionHandler.getSelectedSessionId();
     var standardIdWithSection = $('#examination-standard-section-select').val();

     if(standardIdWithSection ==""){
       showErrorDialogBox("Please select standard ");
       return;
     }
     var status = "ENROLLED"
     var addRelievedStudents = $("#relieved-student-checkbox").is(":checked");
     if(addRelievedStudents){
        status += ",RELIEVED";
     }

     ajaxClient.get("/examination/hpc/class-students-list/"+sessionId+"?status="+status+"&standardSection="+standardIdWithSection, function(data) {
         $("#student-hpc-form-view").html(data)
         $('#datatables-reponsive').DataTable( {
                   "paging":   false,
                   searching: false,
                   columnDefs: [
                     { orderable: false, targets: "no-sort" },
                   ],
                   order: [[3, 'asc']]
         });
     });
  },

  back : function() {
    hpc.loadClassStudents();
    $("#student-hpc-form-generation-search-tab").attr('style','display: ');
    $("#hpc-academic-session-div").attr('style','display: ');
    $("#fill-hpc-form-navbar").attr('style','display:none');
  },

  downloadDocument : function (id, formUserType) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var examType = $("#hpc-exam-type").val().trim();
    var studentId = hpc.dataCache['selectedStudentId'];
    window.open(baseURL+"/examination/hpc/download-document/"+ academicSessionId + "/" + examType + "/" + formUserType + "/" + studentId + "/" + id, '_blank');
  },

  deleteDocument : function (id, formUserType) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var examType = $("#hpc-exam-type").val().trim();
    var studentId = hpc.dataCache['selectedStudentId'];
    ajaxClient.post("/examination/hpc/delete-document/"+ academicSessionId + "/" + examType + "/" + formUserType + "/" + studentId + "/" + id, {}, function(data){
      $("#hpc-status-modal-container").html(data);
      $("#hpc-form-status-modal").modal({backdrop: 'static', keyboard: false});
      hpc.loadStudentHPCForm(studentId);
  });
  },

  registerUploadFileCallback : function() {
    $('.hpc-form-document').on('change',function() {
         //get the file name
         var fileName = $(this).val();
         var values = fileName.split("\\");
         if(values.length <= 0){
           showErrorDialogBox("Invalid file");
           return;
         }
         //replace the "Choose a file" label
         $(this).next('.custom-file-label').html(values[values.length-1]);
     });

  }

};

var hpcReportCard = {

  dataCache : {},

  loadHPCReportCardPage: function(){
    ajaxClient.get("/examination/hpc-report-card-generation-homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(hpcReportCard.changeSession);
        hpcReportCard.bindSearchStudentEvent();
        hpcReportCard.bindReportCardGenerationOptions();
    });
  },

  changeSession: function() {
      $("#hpc-report-card-generation-search-student-text").val("");
      $("#student-hpc-report-card-view").html("");
      $(".hpc-report-card-types").html("");

      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/standards/"+sessionId, function(data) {
          $(".hpc-standard-with-section-options-wrapper").html(data);
      });
  },

  bindReportCardGenerationOptions :function () {
    $('#student-hpc-report-card-generation-option').on('click', function () {
          $(".hpc-report-card-screen").attr("style","display:none;");
          $("#student-hpc-report-card-screen").attr("style","display:block;");
          hpcReportCard.loadHPCReportCardPage();
      });

      $('#bulk-student-hpc-report-card-generation-option').on('click', function () {
            $(".hpc-report-card-screen").attr("style","display:none;");
            $("#bulk-student-hpc-report-card-screen").attr("style","display:block;");
        });

        $('#class-hpc-report-card-generation-option').on('click', function () {
             $(".hpc-report-card-screen").attr("style","display:none;");
             $("#class-hpc-report-card-screen").attr("style","display:block;");
         });
  },

  loadClassStudents : function () {
    var reportType = $("#class-hpc-report-card-type").val();
    var standardSection = $('#examination-standard-section-select').val();
    if(standardSection =="" || reportType == undefined){
      showErrorDialogBox("Please select standard and report type");
      return;
    }
    $("#send-notifications-hpc-class-student-list").attr('style','display:block')
    var standardIdAndSection = standardSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1){
      sectionId = standardIdAndSection[1].trim();
    }
    var sessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.get("/examination/hpc-class-students/"+sessionId+"?standards="+standardId+"&sectionId="+sectionId, function(data) {
        $("#send-notifications-hpc-class-student-list").html(data);
    });
  },

  bindSearchStudentEvent: function() {
    var resultArea = "#hpc-report-card-generation-student-search-result";
    $('#hpc-report-card-generation-search-student').on('click', function () {
        hpcReportCard.doneStudentSearchTyping(resultArea);
    });
    $("#hpc-report-card-generation-search-student-text").on('keyup', function (e) {
      if (e.keyCode == 13) {
        hpcReportCard.doneStudentSearchTyping(resultArea);
      }
    });
    liveSearchHandler.bindEvent('#hpc-report-card-generation-search-student-text',resultArea, hpcReportCard.doneStudentSearchTyping);
  },

  doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#hpc-report-card-generation-search-student-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED,RELIEVED";
      studentLiveSearchEvent(sessionId, searchText, resultArea, hpcReportCard.loadStudentReportCard, status);
  },

  loadStudentReportCard: function(studentId) {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/hpc-report-card-student-details/"+sessionId+"/"+studentId, function(data) {
        $("#student-hpc-report-card-view").html(data);
        hpcReportCard.registerReportCardGenerationCallBack();
    });
  },

  registerReportCardGenerationCallBack: function(){
    $("#student-generate-hpc-report-card-button").on('click', function () {
        var studentId = $("#hpc-report-card-view-student-id").text().trim();
        var examType = $("#hpc-report-card-type").val().trim();
        hpcReportCard.generateReportCard(studentId, examType);
    })
  },

  generateReportCard: function(studentId, examType){
    var sessionId = academicSessionHandler.getSelectedSessionId();
    window.open(baseURL + "/examination/student-hpc-report-card/" + sessionId + "/" + studentId + "/" + examType, '_blank');
  },

  studentSelectCheckbox : function(studentSelectCheckbox) {
         var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
         $("#students-selected-count").html(selectedStudentCount);
   },

   selectStudentList : function (selectAllCheckbox) {
        if(selectAllCheckbox.checked){
            $(".student-select-checkbox").prop('checked', true);
            var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
            $("#students-selected-count").html(selectedStudentCount);
        }
        else{
            $(".student-select-checkbox").prop('checked', false);
            $("#students-selected-count").html(0);
        }
    },

  generateStudentsBulkReportCard : function(){
    var standardIdWithSection = $("#examination-standard-section-select").val().trim();
    var reportType = $("#class-hpc-report-card-type").val();

    var sessionId = academicSessionHandler.getSelectedSessionId();
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    studentIds = studentIds.join(",");

    if(standardIdWithSection == ""){
      showErrorDialogBox("Please select class for generating report");
      return;
    }
    if (reportType == ""){
      showErrorDialogBox("Please select report type for generating report");
      return;
    }
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1) {
        sectionId = standardIdAndSection[1].trim();
    }

    window.open(baseURL + "/examination/bulk-student-hpc-report-card/" + sessionId + "/"+ studentIds + "/" + standardId + "/" + reportType+"?sectionId="+sectionId, '_blank');
  },

  generateClassReportCard: function(dataReport){
    var standardIdWithSection = $(".class-standard-filter").find('.report-card-change').val();
    var sessionId = academicSessionHandler.getSelectedSessionId();

    if(standardIdWithSection == ""){
      showErrorDialogBox("Please select class for generating report");
      return;
    }

    var reportType = $("#only-class-hpc-report-type").val();
    if (reportType == ""){
      showErrorDialogBox("Please select report type for generating report");
      return;
    }
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();
    var sectionId = "";
    if(standardIdAndSection.length > 1) {
        sectionId = standardIdAndSection[1].trim();
    }
    var studentPerPage = "";
    if(dataReport) {
      studentPerPage = $("#student-per-page").val();
      $("#generate-class-report-data-modal").modal('toggle');
    }

    window.open(baseURL + "/examination/class-hpc-report-card/" + sessionId + "/" + standardId + "/" + reportType+"?sectionId="+sectionId+"&studentPerPage="+studentPerPage, '_blank');
  }
};

var examinationReports = {
  dataCache : {},

  loadMainScreen: function() {
    ajaxClient.get("/examination/reports", function(data) {
        $("#main-content").html(data);
        var sessionClassExams =  readJson('#session-class-exams');
        examinationReports.dataCache.sessionClassExams = sessionClassExams;
        var sessionClassCourses =  readJson('#session-class-courses');
        examinationReports.dataCache.sessionClassCourses = sessionClassExams;
        var currectSession = readJson('#current-session');
        initSelect2();
        initSelect2("None", "report-cumulative-exams");
        commonUtils.bindCardHoverEvent();
        commonUtils.bindReportCardClickEvent();
        examinationReports.bindSessionDropDown();
        examinationReports.bindStandardDropDown();
        examinationReports.bindLeafExamNodeStandardDropDown();
        examinationReports.bindGenerateReportEvent();
        examinationReports.checkboxEvents();
        $('.report-academic-session').val(currectSession.academicSessionId);
        $('.rank-till').val(1);
    });
  },

  getStandardMap : function (standards) {
    var standardsMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        standardsMap[standard.standardId] = standard
    }
    return standardsMap;
  },

  bindStandardDropDown : function () {
    $('.report-standards').on('change', function() {
      var selectedSessionId = $(this).closest(".modal-body").find('.report-academic-session').find(':selected').val().trim();
      var standardId = $(this).find(':selected').val().trim();
      var sessionElement = this;
      $(sessionElement).closest(".modal-body").find(".report-exams").html("");
      $(sessionElement).closest(".modal-body").find(".report-courses").html("");
      examinationReports.fillStudentSections(this, selectedSessionId, standardId);
      examinationReports.fillExamsDropDown(this, selectedSessionId, standardId);
      examinationReports.fillCumulativeExamsDropDown(this, selectedSessionId, standardId);
      $("input.show-dimensions").prop("checked", false);
      $("input.exclude-coscholastic-subjects").prop("checked", false);
      $("input.show-scholastic-grade").prop("checked", false);
      $("input.show-co-scholastic-grade").prop("checked", false);
      // $("input.compute-rank").prop("checked", false);
      // $("input.display-attendance").prop("checked",false);
      $("input.relieved-student-checkbox").prop("checked",false);
      // $("input.parent-remarks").prop("checked", false);
      $(standardId).closest(".modal-body").find(".report-standard-section").html("");
      $(sessionElement).closest(".modal-body").find(".report-standard-section").html("");
      $(standardId).closest(".modal-body").find(".section-ids-multiple").html("");
      $(sessionElement).closest(".modal-body").find(".section-ids-multiple").html("");
      $(sessionElement).closest(".modal-body").find(".report-courses").html("");
    });
  },

   bindLeafExamNodeStandardDropDown : function () {
      $('#select-standards').on('change', function() {
        var sessionId = $(this).closest(".modal-body").find('.report-academic-session').find(':selected').val().trim();
        var standardId = $(this).find(':selected').val().trim();
        examinationReports.fillStudentSections(this, sessionId, standardId);
        ajaxClient.get("/examination/reports-only-leaf-exams/"+standardId+"/"+sessionId, function(data) {
             $('#leaf-exams').html(data);
        });
      });
    },

  fillStudentSections : function(changeElementContext, selectedSessionId, standardId) {
    ajaxClient.get("/examination/section-details/"+standardId+"/"+selectedSessionId, function(data) {
        $(".section-json-wrapper").html(data);

        var standards = readJson("#standard-details-p");
        var classSectionMap = {};
        for(var i = 0 ; i < standards.length ; i++){
            var standard = standards[i]
            classSectionMap[standard.standardId] = standard
        }

        var options = "";
        if(!(standardId in classSectionMap)){
            $('select.student-section').html(options);
            return ;
        }

        var standard = classSectionMap[standardId];
        for(var i = 0 ; i < standard.standardSectionList.length; i++){
            options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
        }
        $(".report-standard-section").html(options);
        $(".section-ids-multiple").html(options)

        examinationReports.fillCoursesDropDown(this, selectedSessionId, standardId);
    });
  },

  // populateStandardsSelectOptions : function (changeElementContext) {
  //   var options = "<option value=\"\"></option>"
  //   var standards = examinationReports.getReportStandards(changeElementContext);
  //   for(var i = 0 ; i < standards.length; i++){
  //       var standard = standards[i];
  //       options += "<option value=\""+standard.standardId+"\">"+standard.displayName+"</option>"
  //   }
  //   $(changeElementContext).closest(".modal-body").find(".report-standards").html(options);
  // },

  populateReportCardTypeSelectOptions : function (changeElementContext) {
    var options = "<option value=\"\"></option>"
    var reportCardTypeList = examinationReports.getReportCardType(changeElementContext);
    for(var i = 0 ; i < reportCardTypeList.length; i++){
        var reportCardType = reportCardTypeList[i];
        options += "<option value=\""+reportCardType.reportCardType+"\">"+reportCardType.reportCardName+"</option>"
    }
    $(changeElementContext).closest(".modal-body").find(".report-type-name").html(options);
  },

  getReportStandards : function (changeElementContext) {
    return JSON.parse($(changeElementContext).closest(".modal-body").find(".standards-json").text().trim());
  },

  getReportCardType : function (changeElementContext) {
    if($(changeElementContext).closest(".modal-body").find(".report-card-type-json").length > 0) {
      return JSON.parse($(changeElementContext).closest(".modal-body").find(".report-card-type-json").text().trim());
    }
  },

  bindSessionDropDown : function () {
    $('.report-academic-session').on('change', function() {
      var selectedSessionId = $(this).find(':selected').val().trim();
      var sessionElement = this;
      ajaxClient.get("/examination/standards-json/"+selectedSessionId, function(data) {
          $(sessionElement).closest(".modal-body").find(".different-value-wrapper").html(data);
          examinationReports.bindStandardDropDown();
          if($(sessionElement).closest(".modal-body").find(".report-card-type-json").length > 0) {
            examinationReports.populateReportCardTypeSelectOptions(sessionElement);
          }
          $(sessionElement).closest(".modal-body").find(".report-standard-section").html("");
          $(sessionElement).closest(".modal-body").find(".section-ids-multiple").html("");
          $(sessionElement).closest(".modal-body").find(".report-exams").html("");
          $(sessionElement).closest(".modal-body").find(".report-courses").html("");
          $(sessionElement).closest(".modal-body").find(".report-standards").val("");
      });
    });
  },

  fillExamsDropDown : function (changeElementContext, sessionId, standardId) {
    if(sessionId == ""  || standardId == ""){
      return
    }
    var examOptions = '<option value = "" ></option>';

    $(changeElementContext).closest(".modal-body").find(".report-exams").html(examOptions);
    if(sessionId in examinationReports.dataCache.sessionClassExams){
        var classMap = examinationReports.dataCache.sessionClassExams[sessionId]
        if(standardId in classMap){
           var exams = classMap[standardId]
           for(var i = 0; i < exams.length; i++) {
             examOptions += "<option value=\""+exams[i].examId+"\">"+exams[i].examName+"</option>";
           }
           $(changeElementContext).closest(".modal-body").find(".report-exams").html(examOptions);
        }
    }
  },

  fillCumulativeExamsDropDown : function (changeElementContext, sessionId, standardId) {
    if(sessionId == ""  || standardId == ""){
      return
    }
    var examOptions = '<option value = "" ></option>';

    $(changeElementContext).closest(".modal-body").find(".report-cumulative-exams").html(examOptions);
    if(sessionId in examinationReports.dataCache.sessionClassExams){
        var classMap = examinationReports.dataCache.sessionClassExams[sessionId]
        if(standardId in classMap){
           var exams = classMap[standardId]
           for(var i = 0; i < exams.length; i++) {
             examOptions += "<option value=\""+exams[i].examId+"\">"+exams[i].examName+"</option>";
           }
           $(changeElementContext).closest(".modal-body").find(".report-cumulative-exams").html(examOptions);
        }
    }
  },

  fillCoursesDropDown : function (changeElementContext, sessionId, standardId) {
    ajaxClient.get("/examination/exam-course-details/"+standardId+"/"+sessionId, function(data) {
        $(".report-courses").html(data);
    });
  },

  bindGenerateReportEvent : function () {
    $('.generate-report').on('click', function () {
      $(this).closest('div.modal').modal('toggle');
      var containerElement = $(this).closest('div.report-field-container');
      var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
      var reportType = $(containerElement).find('p.report-type').text().trim();
      var academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      var standardId = $(containerElement).find(".report-standards option:selected").val();
      var sectionId = $(containerElement).find(".report-standard-section").val();
      var examId = $(containerElement).find(".report-exams option:selected").val();
      if(academicSession == ""  || standardId == "" || examId == ""){
        showErrorDialogBox("Please select all the required fields to generate report")
        return
      }
      // var displayRank = false;
      // if($(containerElement).find("input.compute-rank").is(":checked")){
      //   displayRank = true;
      // }

      var showScholasticGrade = false;
      if($(containerElement).find("input.show-scholastic-grade").is(":checked")){
        showScholasticGrade = true;
      }

      var showCoscholasticGrade = false;
      if($(containerElement).find("input.show-co-scholastic-grade").is(":checked")){
        showCoscholasticGrade = true;
      }

      // var displayAttendance = false;
      // if($(containerElement).find("input.display-attendance").is(":checked")){
      //   displayAttendance = true;
      // }

      excludeCoscholasticSubjects = $(containerElement).find("input.exclude-coscholastic-subjects").is(":checked");
      showDimensions = $(containerElement).find("input.show-dimensions").is(":checked");
      // parentRemark = $(containerElement).find("input.parent-remarks").is(":checked");
      showTotalColumnDimension = false;
      if(sectionId === undefined) {
        sectionId = "";
      } else {
        sectionId = sectionId.join(",").trim();
      }
      window.open(baseURL+"/examination/generate-report/" + examId + "?section_id_str="+sectionId+"&exclude_coscholastic_subjects="+excludeCoscholasticSubjects+"reportType="+reportType+
      "&show_dimensions="+showDimensions+"&show_total_column_dimension="+showTotalColumnDimension+"&show_coscholastic_grade="+showCoscholasticGrade+"&show_scholastic_grade="+showScholasticGrade+"&requiredHeaders="+requiredHeadersCSV, '_blank');
    });

    $('.generate-feed-marks-sheet').on('click', function () {
      $(this).closest('div.modal').modal('toggle');
      var containerElement = $(this).closest('div.report-field-container');
      var academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      var standardId = $(containerElement).find(".report-standards option:selected").val();
      var sectionId = $(containerElement).find(".report-standard-section option:selected").val();
      var examId = $(containerElement).find(".report-exams option:selected").val();
      var addRelievedStudents = $(containerElement).find(".relieved-student-checkbox").is(":checked");

      if(academicSession == ""  || standardId == "" || examId == ""){
        showErrorDialogBox("Please select all the required fields to generate report")
        return
      }
      if(sectionId === undefined) {
        sectionId = "";
      } else {
        sectionId = sectionId.trim();
      }

      var courseId = "";
      var noOfStudentPerPage = $(containerElement).find('.student-per-page').val();
      var studentSortingParameters = $(containerElement).find("#student-sorting-parameters").val();

      window.open(baseURL+"/examination/generate-feed-marks-sheet/" + academicSession + "/" + standardId + "/"+ examId + "?section_id="+sectionId+"&student_count="+noOfStudentPerPage+"&add_relieved_students="+addRelievedStudents+"&course_id="+courseId+"&student_sorting_parameters="+studentSortingParameters, '_blank');
    });

    $('.course-report').on('click', function () {
      $(this).closest('div.modal').modal('toggle');
      var containerElement = $(this).closest('div.report-field-container');
      var academicSessionId = $(containerElement).find(".report-academic-session option:selected").val();
      var standardId = $(containerElement).find(".report-standards option:selected").val();
      var sectionId = $(containerElement).find(".report-standard-section option:selected").val();
      var examId = $(containerElement).find(".report-exams option:selected").val();
      if(academicSessionId == ""  || standardId == "" || examId == ""){
        showErrorDialogBox("Please select all the required fields to generate report")
        return
      }
      var displayRank = false;
      if($(containerElement).find("input.compute-rank").is(":checked")){
        displayRank = true;
      }

      excludeCoscholasticSubjects = $(containerElement).find("input.exclude-coscholastic-subjects").is(":checked");
      showDimensions = false;
      showTotalColumnDimension = false;
      if(sectionId === undefined) {
        sectionId = "";
      } else {
        sectionId = sectionId.trim();
      }
      window.open(baseURL+"/examination/generate-course-report/" + standardId + "/" + examId + "?section_id="+sectionId+"&exclude_coscholastic_subjects="+excludeCoscholasticSubjects+
        "&show_dimensions="+showDimensions+"&show_total_column_dimension="+showTotalColumnDimension+"&academicSessionId="+academicSessionId, '_blank');
    });


    $('.generate-reports').on('click', function () {
      var containerElement = $(this).closest('div.report-field-container');
      var invalid = validateMandatoryFields($(containerElement));
      if(invalid){
        return;
      }
      var academicSessionId = $(containerElement).find(".report-academic-session option:selected").val();
      var reportType = $(containerElement).find('p.report-type').text().trim();
      
      var standardId = $(containerElement).find(".report-standards option:selected").val();
      if(standardId === undefined) {
        standardId = "";
      } else {
        standardId = standardId.trim();
      }
      var sectionId = $(containerElement).find(".report-standard-section option:selected").val();
      if(sectionId === undefined) {
        sectionId = "";
      } else {
        sectionId = sectionId.trim();
      }
      var courseId = $(containerElement).find(".report-courses option:selected").val();
      if(courseId === undefined) {
        courseId = "";
      } else {
        courseId = courseId.trim();
      }
      var courseType = $(containerElement).find(".report-course-type option:selected").val();
      if(courseType === undefined) {
        courseType = "";
      } else {
        courseType = courseType.trim();
      }
      var examId = $(containerElement).find(".report-exams option:selected").val();
      if(examId === undefined) {
        examId = "";
      } else {
        examId = examId.trim();
      }
      var staffId = $(containerElement).find(".report-staff option:selected").val();
      if(staffId === undefined) {
        staffId = "";
      } else {
        staffId = staffId.trim();
      }
      var reportTypeName = $(containerElement).find('.report-type-name').val();
      if(reportTypeName === undefined) {
        reportTypeName = "";
      } else {
        reportTypeName = reportTypeName.trim();
      }

      var sectionIdsStr = $(containerElement).find('.section-ids-multiple').val();
      if(sectionIdsStr === undefined) {
        sectionIdsStr = "";
      } else {
        sectionIdsStr = sectionIdsStr.join(",");
      }

      var examIdsStr = $(containerElement).find('.exam-ids-multiple').val();
      if(examIdsStr === undefined) {
        examIdsStr = "";
      } else {
        examIdsStr = examIdsStr.join(",");
      }

      var courseIdsStr = $(containerElement).find('.course-ids-multiple').val();
      if(courseIdsStr === undefined) {
        courseIdsStr = "";
      } else {
        courseIdsStr = courseIdsStr.join(",");
      }

      var compareCumulativeExamIdsStr = "";
      var compareCumulativeExamIdsStr = $(containerElement).find('.report-cumulative-exams').val();
      if(compareCumulativeExamIdsStr === undefined) {
        compareCumulativeExamIdsStr = "";
      } else {
        compareCumulativeExamIdsStr = compareCumulativeExamIdsStr.join(",");
      }

      var rankTill = $(containerElement).find('.rank-till').val();
      if(rankTill === undefined) {
        rankTill = "";
      }

      var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
      var showScholasticGrade = false;
      if($(containerElement).find("input.show-scholastic-grade").is(":checked")){
        showScholasticGrade = true;
      }


      var showCoscholasticGrade = false;
      if($(containerElement).find("input.show-co-scholastic-grade").is(":checked")){
        showCoscholasticGrade = true;
      }
      var excludeCoscholasticSubjects = $(containerElement).find("input.exclude-coscholastic-subjects").is(":checked");

      var sortStudentOnRank = $(containerElement).find("input.sort-student-on-rank").is(":checked");

      var additionalCourseIdsStr = $(containerElement).find('.additional-course-ids-multiple').val();
      if(additionalCourseIdsStr === undefined) {
        additionalCourseIdsStr = "";
      } else {
        additionalCourseIdsStr = additionalCourseIdsStr.join(",");
      }

      $(this).closest('div.modal').modal('toggle');

      var scholasticMarksDisplayTypeStr = $(containerElement).find('.scholastic-marks-display-type-multiple').val();
      if(scholasticMarksDisplayTypeStr === undefined) {
        scholasticMarksDisplayTypeStr = "";
      } else {
        scholasticMarksDisplayTypeStr = scholasticMarksDisplayTypeStr.join(",");
      }
      
      var coScholasticMarksDisplayTypeStr = $(containerElement).find('.coscholastic-marks-display-type-multiple').val();
      if(coScholasticMarksDisplayTypeStr === undefined) {
        coScholasticMarksDisplayTypeStr = "";
      } else {
        coScholasticMarksDisplayTypeStr = coScholasticMarksDisplayTypeStr.join(",");
      }

      var show_class_average_details = false;
      if($(containerElement).find("input.show-class-average-details").is(":checked")){
        show_class_average_details = true;
      }

      var show_staff_details = false;
      if($(containerElement).find("input.show-staff-details").is(":checked")){
        show_staff_details = true;
      }

      window.open(baseURL+"/examination/generate-reports/"+reportType+"?academic_session_id="+academicSessionId
          +"&standard_id="+standardId+"&section_id="+sectionId+"&exam_id="+examId+"&course_type="+courseType
          +"&course_id="+courseId+"&staffId="+staffId+"&reportTypeName="+reportTypeName
          +"&sectionIdsStr="+sectionIdsStr+"&examIdsStr="+examIdsStr+"&courseIdsStr="+courseIdsStr
          +"&compareCumulativeExamIdsStr="+compareCumulativeExamIdsStr+"&rankTill="+rankTill+"&exclude_coscholastic_subjects="+excludeCoscholasticSubjects
          +"&requiredHeaders="+requiredHeadersCSV+"&sort_student_on_rank="+sortStudentOnRank+"&additionalCourseIdsStr="+additionalCourseIdsStr
          +"&scholasticMarksDisplayTypeStr="+scholasticMarksDisplayTypeStr+"&coScholasticMarksDisplayTypeStr="+coScholasticMarksDisplayTypeStr
          +"&show_class_average_details="+show_class_average_details+"&show_staff_details="+show_staff_details, '_blank');
    });

    $('.generate-graphical-report').on('click', function () {
            var containerElement = $(this).closest('div.report-field-container');
            var invalid = validateMandatoryFields($(containerElement));
            if(invalid){
              return;
            }
            var academicSessionId = $(containerElement).find(".report-academic-session option:selected").val();
            var reportType = $(containerElement).find('p.report-type').text().trim();

            var standardId = $(containerElement).find(".report-standards option:selected").val();
            if(standardId === undefined) {
              standardId = "";
            } else {
              standardId = standardId.trim();
            }
            var sectionId = $(containerElement).find(".report-standard-section option:selected").val();
            if(sectionId === undefined) {
              sectionId = "";
            } else {
              sectionId = sectionId.trim();
            }
            var examId = $(containerElement).find(".report-exams option:selected").val();
            if(examId === undefined) {
              examId = "";
            } else {
              examId = examId.trim();
            }
            var sectionIdsStr = $(containerElement).find('.section-ids-multiple').val();
            if(sectionIdsStr === undefined) {
              sectionIdsStr = "";
            } else {
              sectionIdsStr = sectionIdsStr.join(",");
            }
            var examIdsStr = $(containerElement).find('.exam-ids-multiple').val();
            if(examIdsStr === undefined) {
              examIdsStr = "";
            } else {
              examIdsStr = examIdsStr.join(",");
            }

            $(this).closest('div.modal').modal('toggle');

            var url = "/examination/generate-graphical-reports/" + reportType +"?academic_session_id=" + academicSessionId + "&standard_id=" + standardId +
                        "&section_id=" + sectionId +"&exam_id=" + examId + "&sectionIdsStr=" + sectionIdsStr + "&examIdsStr=" + examIdsStr + "&courseIdsStr=";

            ajaxClient.get(url, function(data) {
                var newWindow = window.open(baseURL+url, '_blank');
                newWindow.document.open();
                newWindow.history.pushState({}, '', url);
                newWindow.document.write(data);

                newWindow.document.close();
                newWindow.onload = function() {
                graphicalReportGenerator.loadGraphicalData(newWindow.document, newWindow);
                };

            });
    });
  },

  checkboxEvents : function () {
  $('.all-column-select').on('change', function() {
      const childCheckboxes = $(this).closest('.report-header-container').find('.select-report-column');

      if ($(this).is(':checked')) {
          childCheckboxes.prop('checked', true);
      } else {
          childCheckboxes.each(function() {
              if (!$(this).is(':disabled')) {
                  $(this).prop('checked', false);
              }
          });
      }
  });
  },

  bindClassChangeEvent : function () {
    $("#examination-standard-section-select").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        marksView.loadExamCourseStructure(standardId);
    });
  },

  loadExamCourseStructure : function (standardSectionMapId) {
    if(standardSectionMapId == ""){
      return;
    }
    var tokens = standardSectionMapId.split(":");
    var standardId = tokens[0];
    if(standardId == ""){
      return;
    }
    var sectionId = null;
    if(tokens.length == 2){
      sectionId = tokens[1];
    }

    var sessionId = academicSessionHandler.getSelectedSessionId();
    marksView.dataCache['classMarksStructures'] = []
    marksView.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
    marksView.dataCache['selectedStandard'] = standardId;
    marksView.dataCache['selectedSection'] = sectionId;

    ajaxClient.get("/examination/exam-course-structure/"+standardId+"/"+sessionId, function(data) {
        $("#marks-view-main-container").html(data);
        marksView.bindExamChangeEvent();
        var classExamsCourses = readJson("#class-exam-courses-json");
        marksView.dataCache['classExamsCourses'] = classExamsCourses;
    });
  },

};

var admitCards = {

  dataCache : {},

  loadMainScreen: function() {
    ajaxClient.get("/examination/admitcard", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(admitCards.changeSession);
        var sessionClassExams = readJson('#session-class-exams');
        admitCards.dataCache.sessionClassExams = sessionClassExams;
        admitCards.bindStandardDropDown();
        admitCards.onClickBulkAdmitCardTab();
        admitCards.onClickClassAdmitCardTab();
    });
  },

  changeAdmitCardType: function(){
      $('.admitcard-type').change(function() {
          var selectedValue = $(this).val();
          if (selectedValue === 'ADMIT_CARD_WITH_DATESHEET') {
              $('.count-per-page-row').attr("style", "display:none");
              $('.count-per-page').removeClass('mandatory-field');
          } else if (selectedValue === 'ADMIT_CARD_WITHOUT_DATESHEET') {
               $('.count-per-page-row').attr("style", "display:block");
               $('.count-per-page').addClass('mandatory-field');
          }
      });
  },


  changeSession: function() {
    var isBulkSelected = ($('#class-admit-card-screen').css('display') == 'none');
    var selectedSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/admitcard-session-change/"+selectedSessionId, function(data) {
      $("#admit-card-div").html(data);
      admitCards.bindStandardDropDown();
      if(!isBulkSelected) {
        $("#class-admit-card-screen").attr("style", "display:block");
        $("#bulk-admit-card-screen").attr("style", "display:none;");
      }
    });
  },

  bindStandardDropDown : function () {
    $('.admitcard-standards').on('change', function() {
      var container = "class-admit-card-screen";
      $('#admit-card-student-list-div').attr('style','display:none;');
      if($('#class-admit-card-screen').css('display') == 'none') {
        container = "bulk-admit-card-screen";
      }
      var standardId = $("#" + container).find(".admitcard-standards").find(':selected').val().trim();
      var selectedSessionId = academicSessionHandler.getSelectedSessionId();
      admitCards.fillExamsDropDown(container, selectedSessionId, standardId);
    });
  },

  fillExamsDropDown : function (container, sessionId, standardIdStr) {
    if(sessionId == ""  || standardId == ""){
      return;
    }
    var token = standardIdStr.split(":");
    var standardId = token[0];
    var examOptions = '<option value = "" ></option>';

    if(sessionId in admitCards.dataCache.sessionClassExams){
        var classMap = admitCards.dataCache.sessionClassExams[sessionId];
        if(standardId in classMap){
           var exams = classMap[standardId]
           for(var i = 0; i < exams.length; i++) {
             examOptions += "<option value=\""+exams[i].examId+"\">"+exams[i].examName+"</option>";
           }
        }
    }
    $("#" + container).find(".admitcard-exams").html(examOptions);
  },

  loadStandardStudents : function () {

      var standardSection = $('#examination-standard-section-select').val();
      if(standardSection ==""){
        showErrorDialogBox("Please select standard");
        return;
      }
      $("#send-notifications-class-student-list").attr('style','display:block')
      var standardIdAndSection = standardSection.split(":");
      var standardId = standardIdAndSection[0].trim();
      var sectionId = "";
      if(standardIdAndSection.length > 1){
        sectionId = standardIdAndSection[1].trim();
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();

      ajaxClient.get("/examination/class-students-with-sections/"+sessionId+"?standards="+standardId+"&sectionId="+sectionId, function(data) {
          $("#admit-card-student-list-div").html(data);
          $('#admit-card-student-list-div').attr('style','display:block;');
      });
  },

  studentSelectCheckbox : function(studentSelectCheckbox) {
    var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
    $("#students-selected-count").html(selectedStudentCount);
   },

   selectStudentList : function (selectAllCheckbox) {
    if(selectAllCheckbox.checked){
      $(".student-select-checkbox").prop('checked', true);
      var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
      $("#students-selected-count").html(selectedStudentCount);
    }
    else{
      $(".student-select-checkbox").prop('checked', false);
      $("#students-selected-count").html(0);
    }
  },

  onClickGenerateBulkAdmitCard : function () {
    var studentIds = admitCards.getSelectedBulkStudentIds();
    if(!(Array.isArray(studentIds) && studentIds.length > 0)) {
      showErrorDialogBox("Please select atleast one student to generate admit card. ");
      return;
    }
    var container = "bulk-admit-card-screen";
    $("#generate-admit-card-popup-modal").closest('div.modal').modal('toggle');
    admitCards.changeAdmitCardType();
    var standardId = $("#" + container).find(".admitcard-standards").find(':selected').val().trim();
    var selectedSessionId = academicSessionHandler.getSelectedSessionId();
    admitCards.fillExamsDropDown("bulk-admit-card-screen", selectedSessionId, standardId);
  },

  generateBulkAdmitCard : function () {
    var invalid = validateMandatoryFields($("#generate-admit-card-popup-modal"));
    if(invalid){
      return;
    }
    var container = "generate-admit-card-popup-modal";
    var examId = $("#" + container).find(".admitcard-exams option:selected").val();
    var admitCardType = $("#" + container).find(".admitcard-type option:selected").val();
    var countPerPage =  $("#" + container).find(".count-per-page option:selected").val();
    var studentIds = admitCards.getSelectedBulkStudentIds();
    studentIds = studentIds.join(",");
    $("#generate-admit-card-popup-modal").closest('div.modal').modal('toggle');
    window.open(baseURL+"/examination/generate-admitcard/" + examId + "/" + admitCardType+"?studentIds="+studentIds+"&countPerPage="+countPerPage, '_blank');
  },

  getSelectedBulkStudentIds :function () {
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    return studentIds;
  },

  onClickBulkAdmitCardTab : function () {
    $("#bulk-admit-card-generation-option").on('click', function () {
      $("#class-admit-card-screen").attr("style", "display:none");
      $("#bulk-admit-card-screen").attr("style", "display:block");
      admitCards.loadMainScreen();
    });
  },

  onClickClassAdmitCardTab : function () {
    $("#class-admit-card-generation-option").on('click', function () {
      $("#bulk-admit-card-screen").attr("style", "display:none");
      $("#class-admit-card-screen").attr("style", "display:block");
      admitCards.changeAdmitCardType();
      $('.count-per-page-row').attr("style", "display:block");
      $('.count-per-page').addClass('mandatory-field');
    });
  },

  generateClassAdmitCard : function () {
    var invalid = validateMandatoryFields($("#class-admit-card-screen"));
    if(invalid){
      return;
    }
    var container = "class-admit-card-screen";
    var examId = $("#" + container).find(".admitcard-exams option:selected").val();
    var admitCardType = $("#" + container).find(".admitcard-type option:selected").val();
    var studentIds = "";
     var countPerPage =  $("#" + container).find(".count-per-page option:selected").val();
    window.open(baseURL+"/examination/generate-admitcard/" + examId + "/" + admitCardType+"?studentIds="+studentIds+"&countPerPage="+countPerPage, '_blank');
  },
};

var greenSheet = {
  loadGreenSheetPage: function(){
    ajaxClient.get("/examination/green-sheet", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(greenSheet.changeSession);
    });
  },

  changeSession: function() {
      $("#report-card-generation-class").val("");
  },

  generateGreenSheet: function(){
    var standardId = $("#report-card-generation-class").val().trim();
    var sessionId = academicSessionHandler.getSelectedSessionId();
    if(standardId == ""){
      showErrorDialogBox("Please select class for generating green sheet");
      return;
    }
    window.open(baseURL + "/examination/generate-green-sheet/" + sessionId + "/" + standardId, '_blank');
  },
};

var notification = {

  dataCache : {},

  loadMainScreen :function () {
    ajaxClient.get("/examination/notification-input-page", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(notification.changeSession);
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/examination/get-channel-credits");
        notification.bindClassChangeEvent();
        notification.dataCache = {};
        notification.dataCache['sendNotifications'] = {};
        notification.dataCache.selectedUserCount = 0;
    });
  },

  changeSession : function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/standards/"+sessionId, function(data) {
        $("#standard-with-section-options-wrapper").html(data);
        notification.bindClassChangeEvent();
        $("#marks-view-main-container").html("<br><br><br><br> <br> <br> <br><br><br><br> <h5 style=\"text-align:center;color:#223b5c;\"> Select class from top menu to view marks for course exams!</h5><br><br><br><br> <br> <br> <br><br><br><br> ");
        notification.dataCache = {};
    });
  },

  bindClassChangeEvent : function () {
    $("#examination-standard-section-select").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        notification.loadExamCourseStructure(standardId);
    });
  },

  loadExamCourseStructure : function (standardSectionMapId) {
    if(standardSectionMapId == ""){
      return;
    }
    var tokens = standardSectionMapId.split(":");
    var standardId = tokens[0];
    if(standardId == ""){
      return;
    }
    var sectionId = null;
    if(tokens.length == 2){
      sectionId = tokens[1];
    }

    var sessionId = academicSessionHandler.getSelectedSessionId();
    notification.dataCache['classMarksStructures'] = []
    notification.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
    notification.dataCache['selectedStandard'] = standardId;
    notification.dataCache['selectedSection'] = sectionId;

    ajaxClient.get("/examination/exam-notification-detail/"+standardId+"/"+sessionId, function(data) {
        $("#marks-view-main-container").html(data);
        notification.bindExamChangeEvent();
        var classExamsCourses = readJson("#class-exam-courses-json");
        notification.dataCache['classExamsCourses'] = classExamsCourses;
    });
  },

  bindExamChangeEvent : function () {
    $("#class-exams").change(function() {
        var examId = $(this).find(':selected').val().trim();
        var classExamsCourses = notification.dataCache['classExamsCourses'];
        for(var i = 0; i < classExamsCourses.length; i++){
          var classExamCourse = classExamsCourses[i];
          if(classExamCourse.examMetaData.examId != examId){
            continue;
          }
          var options = "<option value=\"\" selected>Choose Course ...</option>";
          for(var j = 0 ; j < classExamCourse.examCourses.length ; j ++){
            var examCourse = classExamCourse.examCourses[j];
            options += "<option value=\""+examCourse.course.courseId+"\">"+examCourse.course.courseName+"</option>"
          }
          $("#class-exam-courses").html(options);
        }
    });
  },

  loadMarksViewSheet : function () {
    var examId =  $("#class-exams").find(':selected').val().trim();
    var courseId =  $("#class-exam-courses").find(':selected').val().trim();
    notification.dataCache['selectedCourseId'] = courseId;
    notification.dataCache['selectedExamId'] = examId;

    if(courseId == ""){
      showErrorDialogBox("Please select course");
      return;
    }
    if(examId == ""){
      showErrorDialogBox("please select exam");
      return;
    }
    var sectionId = notification.dataCache['selectedSection'];

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardIdWithSection = $("#examination-standard-section-select").val().trim();
    var standardIdAndSection = standardIdWithSection.split(":");
    var standardId = standardIdAndSection[0].trim();

    ajaxClient.get("/examination/student-details-sheet/"+academicSessionId+"/"+standardId+"/"+examId+"/"+courseId+"?sectionId="+sectionId, function(data) {
        $("#student-marks-view-container").html(data);
        $("#total-student-count").text($('.student-course-marks').length);
    });
  },

  selectStudentList : function (selectAllCheckbox) {
       if(selectAllCheckbox.checked){
           $(".student-select-checkbox").prop('checked', true);
           var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
           $("#students-selected-count").html(selectedStudentCount);
           notification.dataCache.selectedUserCount = selectedStudentCount;
       }
       else{
           $(".student-select-checkbox").prop('checked', false);
           $("#students-selected-count").html(0);
           notification.dataCache.selectedUserCount = 0;
       }
   },

  studentSelectCheckbox : function(studentSelectCheckbox) {
         var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
         $("#students-selected-count").html(selectedStudentCount);
         notification.dataCache.selectedUserCount = selectedStudentCount;
   },

   bulkReminderConfirmModal : function () {
     var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
     if(selectedStudentCount == 0){
       showErrorDialogBox("Please select atleast one student for sending sms.");
       return;
     }
     notification.dataCache.selectedStudentCount = selectedStudentCount;
     var sendToAbsentees = $("#include-fee-fine").is(":checked");

     ajaxClient.get("/examination/reminder-templates", function(data){
         $("#bulk-reminder-template-modal-container").html(data);
         notification.updateReminderModalDisplay(sendToAbsentees);
         $("#bulk-reminder-confirmation-modal").modal({backdrop: 'static', keyboard: false});
         var smsTemplates = readJson("#sms-templates-json");
         notification.bindSMSTemplateClickAction(smsTemplates);
         $(".selected-user-count").text(selectedStudentCount);
    });
   },

   updateReminderModalDisplay : function (sendToAbsentees) {
     if(sendToAbsentees){
         $(".fine-included").text("Yes");
     }else{
         $(".fine-included").text("No");
     }

       $("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count);
       if(notification.dataCache.selectedStudentCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
         $("#notification-send-confirm-button").prop('disabled', true);
         $("#insufficient-sms-credits-message").attr("style", "color:red; display:block;");
       }else{
         $("#notification-send-confirm-button").prop('disabled', false);
         $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
       }
   },

   bindSMSTemplateClickAction : function (smsTemplates) {
     $('#sms-template-select').change(function(){
         var templateId = $(this).val();
         if(templateId == ""){
           $("#sms-template-selection-display").attr("style", "display:none");
           $("#notification-send-confirm-button").prop('disabled', true);
           return;
         }

         $("#sms-template-selection-display").attr("style", "display:block");

         for(var i = 0; i < smsTemplates.length; i++){
           var smsTemplate = smsTemplates[i];
           if(templateId == smsTemplate.templateId){
             $("#selected-sms-template-header").text("Template Message (" + smsTemplate.templateName + ")");
             $("#notification-template-content").text(smsTemplate.templateValue);

             if(smsTemplate.templateVariableDetails != null && smsTemplate.templateVariableDetails.templateVariableList != null){
                 var count = 1;
                 var tableValue = "";
                 for(var j = 0 ; j < smsTemplate.templateVariableDetails.templateVariableList.length; j++){
                     var templateVariable = smsTemplate.templateVariableDetails.templateVariableList[j];
                     var varContent = "";
                     var varValue = "";
                     if(templateVariable.defaultValue != null){
                       varValue = templateVariable.defaultValue;
                     }
                     var classValue = "";
                     if(templateVariable.templateVariableType != "CUSTOM"){
                       classValue = "template-var-row system-var";
                       varContent = "System Derived";
                     }else{
                       classValue = "template-var-row custom-var";
                       varContent = "<input type=\"text\" class=\"variable-value\" value=\""+ varValue +"\"/>";
                     }

                     tableValue += "<tr class=\""+classValue+"\" id='"+templateVariable.varName+"'> <td class='template-var-sr-no'>"+count+"</td><td class='template-var-name'>"+templateVariable.varName+"</td><td class='template-var-type'>"+templateVariable.templateVariableTypeDisplayName+"</td><td class='template-var-value'>"+varContent+"</td></tr>"
                     count += 1;
                 }
             }
             $("#template-var-content").html(tableValue);
             break;
           }
         }
         notification.bindSMSTemplateVarInput();
         notification.setVariableValues();
         notification.updateSMSTemplatePreview();

     });
   },

   setVariableValues : function () {
     $(".template-var-row").each(function() {
       var varName = $(this).find(".template-var-name").first().text();
       var varValue = varName;
       if($(this).hasClass("custom-var")){

         if(varName === "standard_name") {
           $(this).find(".variable-value").val($("#examination-standard-section-select option:selected").text());
         } else if (varName === "exam_name") {
           var name = $("#class-exams option:selected").text() + " of " + $("#class-exam-courses option:selected").text();
           if(name.length > 30) {
             name = name.substring(0, 30);
           }
           $(this).find(".variable-value").val(name);
         }
         // else if (varName === "course_name") {
         //   $(this).find(".variable-value").val($("#class-exam-courses option:selected").text());
         // }
       }
     });
   },

   bindSMSTemplateVarInput : function () {
     $(".variable-value").on('keyup', function (e) {
       notification.updateSMSTemplatePreview();
     });
   },

   updateSMSTemplatePreview : function () {
       var smsTemplateValue = $("#notification-template-content").text();
       $(".template-var-row").each(function() {
         var varName = $(this).find(".template-var-name").first().text();
         var varValue = varName;
         if($(this).hasClass("custom-var")){
           varValue = $(this).find(".variable-value").val();
           if(varValue != null && varValue.trim() != ""){
             smsTemplateValue = smsTemplateValue.replace("${"+varName+"}", varValue);
           }
         }
       });
       $("#preview-notification-template-content").text(smsTemplateValue);
       notification.updateSMSCreditDisplay();
   },

   updateSMSCreditDisplay : function () {
     var message = $("#preview-notification-template-content").text();
     var creditUsed = getSmsCredits(message);
     $("#notification-content-char-count").html("Total characters : "+ message.length + " / Credit(s) Used : " + creditUsed + " (Actual credits may vary depending on the variables values)");
     var confirmButtonContent = "Yes, Send SMS";
     var removeClass = "btn-danger";
     var addClass = "btn-primary";
     if(creditUsed > 1){
        confirmButtonContent = "Yes, Send SMS With " + creditUsed + " Credits";
        removeClass = "btn-primary";
        addClass = "btn-danger";
     }
     $("#notification-send-confirm-button").html(confirmButtonContent);
     $("#notification-send-confirm-button").removeClass(removeClass);
     $("#notification-send-confirm-button").addClass(addClass);

     if(creditUsed == 0){
       $("#notification-send-confirm-button").prop('disabled', true);
       $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
     }
     else if(notification.dataCache.selectedUserCount * creditUsed > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
       $("#notification-send-confirm-button").prop('disabled', true);
       $("#insufficient-sms-credits-message").attr("style", "color:red; display:block;");
     }else{
       $("#notification-send-confirm-button").prop('disabled', false);
       $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
     }
   },

   getSMSTemplateVariables : function () {
       var templateVars = {};
       $(".template-var-row").each(function() {
         var varName = $(this).find(".template-var-name").first().text();
         var varValue = null;
         if($(this).hasClass("custom-var")){
           varValue = $(this).find(".variable-value").val();
         }
         templateVars[varName] = varValue;
       });
       return templateVars;
   },

   sendSMSNotifications : function () {
     var batchName = $("#notification-batch-name").val();
     if(batchName == null || batchName == ""){
       // showErrorDialogBox("Please give batch name to identify the sent notifications.")
       alert("Please give batch name to identify the sent notifications.")
       return;
     }
     var templateId = $('#sms-template-select').find(':selected').val().trim();
     if(templateId == ""){
       // showErrorDialogBox("Please select sms template to send notifications.");
       alert("Please select sms template to send notifications.");
       return;
     }

     var message = $("#preview-notification-template-content").text();
     if(message == null || message == ""){
       // showErrorDialogBox("Please select sms template to send notifications.")
       alert("Please select sms template to send notifications.")
       return;
     }

     var sendToAbsentees = false;
     if($("#include-fee-fine").is(":checked")){
       sendToAbsentees = true;
     }

     // $(this).find(".variable-value").val($("#examination-standard-section-select option:selected").text());
     // $(this).find(".variable-value").val($("#class-exams option:selected").text());
     // $(this).find(".variable-value").val($("#class-exam-courses option:selected").text());

     var examId =  $("#examination-standard-section-select").find(':selected').val().trim();
     var courseId =  $("#class-exam-courses").find(':selected').val().trim();
     var examId =  $("#class-exams").find(':selected').val().trim();

     var tokens = examId.split(":");
     var standardId = tokens[0];
     if(standardId == ""){
       return;
     }
     var sectionId = null;
     if(tokens.length == 2){
       sectionId = tokens[1];
     }

     var userType = "STUDENT";

     var userIds = null;
     userIds = notification.getSelectedBulkStudentIds();

     var creditUsed = getSmsCredits(message);

     if(notification.dataCache.selectedUserCount * creditUsed > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
       // showErrorDialogBox("You do not have sufficient credits to send " + notification.dataCache.selectedUserCount + " SMS. Please recharge.");
       alert("You do not have sufficient credits to send " + notification.dataCache.selectedUserCount + " SMS. Please recharge.");
       return;
     }

     var customVariables = notification.getSMSTemplateVariables();
     if(customVariables != null){
       for(var key in customVariables){
         if(customVariables[key] == null){
           // Case for system variable
           continue;
         }
         else if(customVariables[key].length == 0){
             alert("Invalid value for variable " + key);
             // showErrorDialogBox("Invalid value for variable " + key);
             return;
         }else if(customVariables[key].length > 30){
           // showErrorDialogBox("Value of variable " + key + " exceeds limit of 30 characters.");
             alert("Value of variable " + key + " exceeds limit of 30 characters.");
             return;
         }
       }
     }

     $("#bulk-reminder-confirmation-modal").modal('toggle');

     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     var data = {'batchName':batchName, 'academicSessionId' : academicSessionId, 'deliveryMode':'SMS', 'userType' : userType, 'userIds': userIds, 'smsTemplateId': templateId, 'customVariables' : customVariables};
     ajaxClient.post("/examination/send-notifications/"+sendToAbsentees+"/"+examId+"/"+courseId+"?sectionId="+sectionId,{'sendNotificationsPayload':JSON.stringify(data)}, function(data){
         $("#marks-view-status-modal-container").html(data);
         $("#bulk-notification-status-modal").modal('toggle');
         channelCreditsHandler.loadChannelCredits("/examination/get-channel-credits")
    });
   },

   getSelectedBulkStudentIds :function () {
     var studentIds = [];
     $("input.student-select-checkbox").each(function() {
         if(!$(this).is(":checked")) {
             return;
         }
         var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
         studentIds.push(studentId);
     });
     return studentIds;
   },


};

var notificationHistory = {
  dataCache : {},

  loadNotificationHistoryHomePage : function () {
    ajaxClient.get("/examination/notification-history-homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(notificationHistory.changeSession);
        notificationHistory.getNotificationHistory(true, "SMS", "BATCH");
        notificationHistory.dataCache['notificationHistoryTab'] = "BATCH";

    });
  },

  changeSession : function () {
    var notificationHistoryTab = notificationHistory.dataCache['notificationHistoryTab'];
    var deliveryModeTab = notificationHistory.getDeliveryModeTab();
    notificationHistory.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
  },

  getNotificationHistory : function (freshSearch, deliveryMode, notificationHistoryTab) {
    notificationHistory.dataCache['notificationHistoryTab'] = notificationHistoryTab;
    notificationHistory.dataCache['deliveryModeTab'] = deliveryMode;

    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;

    var sessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.get("/examination/notification-history/"+sessionId+"/"+deliveryMode+"/"+notificationHistoryTab+"/"+offset+"/"+itemsPerPage, function(data) {
        var screenHeight = 0;
        if(deliveryMode == "SMS"){
          $("#smsNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }else if(deliveryMode == "CALL"){
          $("#voiceNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }

        $('.notification-history-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
        var response = JSON.parse($("#pagination-info").text().trim());
        $('#items-per-page').val(response.itemsPerPage);
        $('.page-item').removeClass('active');
        var pageNumber = (response.offset/response.itemsPerPage) + 1;
        $('#page-number-'+page_number).addClass('active');
        if(notificationHistoryTab == 'INDIVIDUAL'){
          $("#nav-individual-history-tab").addClass('active');
          $("#nav-batch-history-tab").removeClass('active');
          $("#individual-history-content").addClass('show active');
          $("#batch-history-content").removeClass('show active');
        }
        notificationHistory.bindIndividualNotificationsContentView();
        notificationHistory.initPagination();
    });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var notificationHistoryTab = notificationHistory.dataCache['notificationHistoryTab'];
        var deliveryModeTab = notificationHistory.getDeliveryModeTab();
        notificationHistory.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notificationHistory.dataCache['notificationHistoryTab'];
        var deliveryModeTab = notificationHistory.getDeliveryModeTab();
        notificationHistory.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notificationHistory.dataCache['notificationHistoryTab'];
        var deliveryModeTab = notificationHistory.getDeliveryModeTab();
        notificationHistory.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notificationHistory.dataCache['notificationHistoryTab'];
        var deliveryModeTab = notificationHistory.getDeliveryModeTab();
        notificationHistory.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
      }
    );
  },

  getDeliveryModeTab : function () {
      var deliveryModeTab = notificationHistory.dataCache['deliveryModeTab'];
      if(deliveryModeTab == null || deliveryModeTab == ""){
        return "SMS";
      }
      return deliveryModeTab;
  },

  loadBatchNotificationsDetails : function (batchId) {
    ajaxClient.get("/examination/batch-notification-details/"+batchId, function(data) {
        $("#batch-notification-detail-modal-container").html(data);
        $("#view-batch-notification-modal").modal('toggle');
    });
  },

  bindIndividualNotificationsContentView : function () {
    $(".view-individual-notification").click(function () {
        var notificationContent = $(this).parent().find("p.notification-content").text().trim();
        $("#individual-notification-detail-modal").find(".modal-body").html("<span style=\"white-space: pre-line;\">" + notificationContent + "</span>");
        $("#individual-notification-detail-modal").modal('toggle');
    });
  }
};



function getUpdateSectionsName(){
  var sectionNames = [];
  $(".update-rename-manage-sections").each(function(){
     var sectionName = $(this).val().trim();
     sectionNames.push(sectionName);
  });
  return sectionNames;
}

function getAddSectionsName(){
  var sectionNames = [];
  $(".add-rename-manage-sections").each(function(){
     var sectionName = $(this).val().trim();
     sectionNames.push(sectionName);
  });
  return sectionNames;
}

function getHiddenSectionNames(){
  var hiddenSectionNames = [];
  $(".hidden-rename-manage-sections").each(function(){
     var hiddenSectionName = $(this).val().trim();
     hiddenSectionNames.push(hiddenSectionName);
  });
  return hiddenSectionNames;
}

function commonBetweenTwoNames(sectionNames,hiddenSectionNames){
  var commonSections = [];
  for(var i=0;i<sectionNames.length;i++){
    for(var j=0;j<hiddenSectionNames.length;j++){
      if(sectionNames[i].trim() == hiddenSectionNames[j].trim()){
        commonSections.push(sectionNames[i]);
      }
    }
  }
  return commonSections;
}


function validations(element){
  clearValidationErrorDisplay();
  var invalid = false;
  $(element).find("input.mandatory-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("select.mandatory-field").each(function() {
      if ($(this).find(':selected').length == 0 || $(this).find(':selected').val().trim() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.mandatory-table-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-table-field-text\"> <span style=\"color:#e65f76;\">Mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.hour-range").each(function() {
      if ($(this).val() < 0 || $(this).val() > 23) {
          $(this).css("border", "1px solid #ff8795");
          // $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.minute-range").each(function() {
      if ($(this).val() < 0 || $(this).val() > 59) {
          $(this).css("border", "1px solid #ff8795");
          // $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.positive-number").each(function() {
      if ($(this).val() < 0) {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  return invalid;
}

function clearValidationErrorDisplay(){

  $(".hour-range").css("border", "");
  $(".minute-range").css("border", "");
  $(".positive-number").css("border", "");
  $(".invalid-input-text").remove();

  $(".mandatory-field").css("border", "");
  $(".mandatory-field-text").remove();

  $(".mandatory-table-field").css("border", "");
  $(".mandatory-table-field-text").remove();

}

function closeModal(){
  clearValidationErrorDisplay();
}



// Exam Update Student Info
var updateStudentInfo = {

  dataCache : {},

  loadMainScreen : function () {
    ajaxClient.get("/examination/student-details-home-v2", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(updateStudentInfo.changeSession);
        updateStudentInfo.initPage();
    });
  },

  initPage : function () {
    updateStudentInfo.dataCache = {};
    var standards = readJson("#standards-json");
    updateStudentInfo.dataCache['standards'] = standards;

    var sessionClassExams =  readJson('#session-class-exams');
    updateStudentInfo.dataCache.sessionClassExams = sessionClassExams;

    updateStudentInfo.bindStandardChangeEvent();
    initSelect2("None");

  },

  changeSession : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/update-page-session-change-v2/"+academicSessionId, function(data) {
        $("#student-details-main-container").html(data);
        updateStudentInfo.initPage();
    });
  },

  bindStandardChangeEvent  : function () {
    $("#student-feed-class").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        if(standardId == ""){
          return;
        }
        $("#exam-ids-multiple").html("");
        $("#additional-course-ids-multiple").html("");
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        updateStudentInfo.updateStandardSections(standardId);
        updateStudentInfo.fillExamsDropDown(this, academicSessionId, standardId);
        updateStudentInfo.fillCoursesDropDown(this, academicSessionId, standardId);
    });
  },

  fillCoursesDropDown : function (changeElementContext, sessionId, standardId) {
    ajaxClient.get("/examination/exam-course-details/"+standardId+"/"+sessionId, function(data) {
        $("#additional-course-ids-multiple").html(data);
        initSelect2("None");
    });
  },

  fillExamsDropDown : function (changeElementContext, sessionId, standardId) {
    if(sessionId == ""  || standardId == ""){
      return
    }
    var examOptions = '<option value = "" ></option>';

    $(changeElementContext).closest(".modal-body").find("#exam-ids-multiple").html(examOptions);
    if(sessionId in updateStudentInfo.dataCache.sessionClassExams){
        var classMap = updateStudentInfo.dataCache.sessionClassExams[sessionId]
        if(standardId in classMap){
           var exams = classMap[standardId]
           for(var i = 0; i < exams.length; i++) {
             examOptions += "<option value=\""+exams[i].examId+"\">"+exams[i].examName+"</option>";
           }
           $(changeElementContext).closest(".form-row").find("#exam-ids-multiple").html(examOptions);
        }
    }
    initSelect2("None");
  },

  updateStandardSections : function (standardId) {
    var standards =  updateStudentInfo.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }

    var options =  "<option value=\"\"></option>";

    if(!(standardId in classSectionMap)){
        $('select.student-section').html(options);
        return ;
    }

    var standard = classSectionMap[standardId];
    for(var i = 0 ; i < standard.standardSectionList.length; i++){
        options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
    }

    $('select.student-section').html(options);
    initSelect2("None");
  },

  loadStudentList : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId =  $("#student-feed-class").find(':selected').val().trim();

    if(standardId == ""){
      showErrorDialogBox("Please select class.");
      return;
    }

    var sectionId = "";
    var standards =  updateStudentInfo.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }
    if((standardId in classSectionMap)){
      var standard = classSectionMap[standardId];
      if(standard.standardSectionList.length > 0){
          sectionId =  $("#student-feed-section").val();
          if(sectionId == "") {
            showErrorDialogBox("Please select section.");
            return;
          }
      }
    }

    var examIdsList = $('#exam-ids-multiple').val();
    if(examIdsList === undefined || examIdsList.length <= 0) {
      showErrorDialogBox("Please select exam.");
      return;
    }
    var examIdsStr = examIdsList.join(",");

    var additionalCourseIdsStr = $('#additional-course-ids-multiple').val();
    if(additionalCourseIdsStr === undefined) {
      additionalCourseIdsStr = "";
    } else {
      additionalCourseIdsStr = additionalCourseIdsStr.join(",");
    }
    if(sectionId != "") {
      sectionId = sectionId.join(",");
    }
    ajaxClient.get("/examination/student-list-v2/"+academicSessionId+"/"+standardId+"?sectionId="+sectionId+"&examIdStr="+examIdsStr+"&additionalCourseIdsStr="+additionalCourseIdsStr, function(data) {
        $("#student-feed-container").html(data);
        var studentFieldDetails = readJson("#student-field-details-json");
        updateStudentInfo.dataCache['studentFieldDetails'] = studentFieldDetails;

        $('#datatables-reponsive').DataTable( {
          "paging":   false,
          searching: false,
          columnDefs: [
            { orderable: false, targets: "no-sort" },
          ],
          order: [[7, 'desc'], [8, 'asc']]
        });
        initDate(365*100);
    });
  },

  saveStudentDetails : function () {
    var updateFieldType = "SESSION_DATA";
    var selectedStandardId = $("#selected-standard-id").text().trim();
    var selectedSectionId = $("#selected-section-id").text().trim();

    var studentDataList = [];
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    var studentFieldDetails = updateStudentInfo.dataCache['studentFieldDetails'];
    var fieldInfoList = studentFieldDetails.fieldMetadata.fieldInfoList;
    var fieldMap = {};
    var requiredFields = [];

    for(var i = 0; i < fieldInfoList.length; i++){
      var fieldInfo = fieldInfoList[i];
      fieldMap[fieldInfo.field] = fieldInfo;

      requiredFields.push(fieldInfo.field);
    }

    var flag = false;
    var errorStatement = "";
    updateStudentInfo.dataCache['studentFieldDetails'] = studentFieldDetails;

    $("tr.student-field-row").each(function() {
      var studentId = $(this).attr("id");

      var fieldValueList = [];
      $(this).find("td.student-field-column").each(function() {
        var fieldType = $(this).attr("id");
        if(fieldType in fieldMap) {
          var fieldInfo = fieldMap[fieldType];
          var updatedValue = "";
          if(fieldInfo.fieldType == "TEXT"){
            updatedValue = $(this).find("input.student-field-value").val().trim();
          }
          else if(fieldInfo.fieldType == "LONGTEXT"){
            updatedValue = $(this).find(".student-field-value").val().trim();
          }
          else if(fieldInfo.fieldType == "NUMBER"){
            updatedValue = $(this).find("input.student-field-value").val();
          }
          else if(fieldInfo.fieldType == "DATE"){
            updatedValue = $(this).find("input.student-field-value").val();
          }
          else if(fieldInfo.fieldType == "DROPDOWN"){
            updatedValue = $(this).find("select.student-field-value").find(':selected').val().trim();
          }

          // TODO : Validations
          if(fieldType == "ADMISSION_NUMBER" && updatedValue.trim() == ""){
            flag = true;
            errorStatement = "Admission number cannot be blank."
          }

          fieldValueList.push({'field' : fieldType, 'value' : updatedValue});
        }
      });

      studentDataList.push({'studentId' : studentId, 'fieldValueList' : fieldValueList});
    });

    if(flag) {
      showErrorDialogBox(errorStatement);
      return;
    }

    var updateFieldPayload = { 'dataType' : updateFieldType, 'requiredFields': requiredFields, 'studentDataList' : studentDataList};

    ajaxClient.post("/examination/update-student-details-v2/"+academicSessionId+"/"+selectedStandardId+"?sectionId="+selectedSectionId, {'updateFieldPayload' : JSON.stringify(updateFieldPayload)}, function(data){
        $("#student-details-status-modal-container").html(data);
        $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
        updateStudentInfo.loadStudentList();
    });

  },
};
// Exam Update Student Info

//Publish Exam Report Cards
var publishReportCards = {

  dataCache : {},

  loadMainScreen : function () {
    ajaxClient.get("/examination/publish-report-card-home", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(publishReportCards.changeSession);
        publishReportCards.initPage();
    });
  },

  initPage : function () {
    publishReportCards.dataCache = {};
    var standards = readJson("#standards-json");
    publishReportCards.dataCache['standards'] = standards;

    publishReportCards.bindStandardChangeEvent();
    initSelect2("None");

  },

  changeSession : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/publish-report-card-session-home/"+academicSessionId, function(data) {
        $("#student-details-main-container").html(data);
        publishReportCards.initPage();
    });
  },

  bindStandardChangeEvent  : function () {
    $("#student-feed-class").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        if(standardId == ""){
          return;
        }
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        publishReportCards.updateStandardSections(standardId);
        publishReportCards.fillReportTypeDropdown(this, academicSessionId, standardId);
    });
  },

  updateStandardSections : function (standardId) {
    var standards =  publishReportCards.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }

    var options =  "<option value=\"\"></option>";

    if(!(standardId in classSectionMap)){
        $('select.student-section').html(options);
        return ;
    }

    var standard = classSectionMap[standardId];
    for(var i = 0 ; i < standard.standardSectionList.length; i++){
        options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
    }

    $('select.student-section').html(options);
    initSelect2("None");
  },

  fillReportTypeDropdown : function (changeElementContext, sessionId, standardId) {
    ajaxClient.get("/examination/report-card-variable-types-with-id/"+sessionId+"/"+standardId, function(data) {
        $("#class-report-type-div").html(data);
        $("#student-feed-container").empty();
    });
  },

  loadStudentList : function () {

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId =  $("#student-feed-class").find(':selected').val().trim();

    if(standardId == ""){
      showErrorDialogBox("Please select class.");
      return;
    }

    var sectionIds = "";
    var standards =  publishReportCards.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }
    if((standardId in classSectionMap)){
      var standard = classSectionMap[standardId];
      if(standard.standardSectionList.length > 0){
          sectionIds =  $("#student-feed-section").val();
          if(sectionIds == "" || sectionIds.length <= 0) {
            showErrorDialogBox("Please select section.");
            return;
          }
      }
    }

    if(sectionIds != "") {
      sectionIds = sectionIds.join(",");
    }

    var reportCardId = $("#report-card-type").val();
    if(reportCardId == ""){
      showErrorDialogBox("Please select report type");
      return;
    }
    ajaxClient.get("/examination/student-report-card-status/"+academicSessionId+"/"+standardId+"/"+reportCardId+"?sectionId="+sectionIds, function(data) {
        $("#student-feed-container").html(data);
    });
  },

  updateReportCardStatusPopup : function (status) {

    $("#update-student-report-card-status-config-modal").modal('toggle');
    $("#update-exam-status").text(status);
    var text = "Do you want to unpublish student's report cards?";
    if(status === "PUBLISHED") {
      text = "Do you want to publish student's report cards?";
    }
    $("#update-exam-status-config-modal-text").text(text);
  },

  updateReportCardStatus : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var reportCardId = $("#selected-report-card-id").text().trim();
    var studentExamDisplayDataStatus = $("#update-exam-status").text().trim();
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });

    if(studentIds.length <= 0) {
      $("#update-student-report-card-status-config-modal").modal('toggle');
      showErrorDialogBox("Please select atleast one student to update status");
      return;
    }

    var studentReportCardStatusPayload = {'instituteId' : null, 'academicSessionId': academicSessionId, 'reportCardId' : reportCardId, 'studentExamDisplayDataStatus' : studentExamDisplayDataStatus, 'studentIdSet' : studentIds };
    $("#update-student-report-card-status-config-modal").modal('toggle');

    ajaxClient.post("/examination/update-student-report-card-status", {'studentReportCardStatusPayload' : JSON.stringify(studentReportCardStatusPayload)}, function(data){
      $("#student-details-status-modal-container").html(data);
      $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
      publishReportCards.loadStudentList();
    });

  },

};
// Publish HPC Exam Report Cards
var publishHPCReportCards = {

  dataCache : {},
  loadMainScreen : function () {
    ajaxClient.get("/examination/publish-hpc-report-card-home", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(publishHPCReportCards.changeSession);
        publishHPCReportCards.initPage();
    });
  },

  initPage : function () {
    publishHPCReportCards.dataCache = {};
    var standards = readJson("#standards-json");
    publishHPCReportCards.dataCache['standards'] = standards;

    publishHPCReportCards.bindStandardChangeEvent();
    initSelect2("None");

  },

  changeSession : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/examination/publish-hpc-report-card-session-home/"+academicSessionId, function(data) {
        $("#student-details-main-container").html(data);
        publishHPCReportCards.initPage();
    });
  },

  bindStandardChangeEvent  : function () {
    $("#student-feed-class").change(function() {
        var standardId = $(this).find(':selected').val().trim();
        if(standardId == ""){
          return;
        }
        publishHPCReportCards.updateStandardSections(standardId);
    });
  },

  updateStandardSections : function (standardId) {
    var standards =  publishHPCReportCards.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }

    var options =  "<option value=\"\"></option>";

    if(!(standardId in classSectionMap)){
        $('select.student-section').html(options);
        return ;
    }

    var standard = classSectionMap[standardId];
    for(var i = 0 ; i < standard.standardSectionList.length; i++){
        options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
    }

    $('select.student-section').html(options);
    initSelect2("None");
  },


  loadStudentList : function () {

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId =  $("#student-feed-class").find(':selected').val().trim();

    if(standardId == ""){
      showErrorDialogBox("Please select class.");
      return;
    }

    var sectionIds = "";
    var standards =  publishHPCReportCards.dataCache['standards'];
    var classSectionMap = {}
    for(var i = 0 ; i < standards.length ; i++){
        var standard = standards[i]
        classSectionMap[standard.standardId] = standard
    }
    if((standardId in classSectionMap)){
      var standard = classSectionMap[standardId];
      if(standard.standardSectionList.length > 0){
          sectionIds =  $("#student-feed-section").val();
          if(sectionIds == "" || sectionIds.length <= 0) {
            showErrorDialogBox("Please select section.");
            return;
          }
      }
    }

    if(sectionIds != "") {
      sectionIds = sectionIds.join(",");
    }

    var examCardType = $("#exam-card-type").val();
    if(examCardType == ""){
      showErrorDialogBox("Please select exam type");
      return;
    }
    ajaxClient.get("/examination/student-hpc-report-card-status/"+academicSessionId+"/"+standardId+"/"+examCardType+"?sectionId="+sectionIds, function(data) {
        $("#student-feed-container").html(data);
    });
  },

  updateReportCardStatusPopup : function (status) {

    $("#update-student-report-card-status-config-modal").modal('toggle');
    $("#update-exam-status").text(status);
    var text = "Do you want to unpublish student's report cards?";
    if(status === "PUBLISHED") {
      text = "Do you want to publish student's report cards?";
    }
    $("#update-exam-status-config-modal-text").text(text);
  },

  updateReportCardStatus : function () {
    var examType = $("#selected-exam-type").text().trim();
    var studentExamDisplayDataStatus = $("#update-exam-status").text().trim();
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });

    if(studentIds.length <= 0) {
      $("#update-student-report-card-status-config-modal").modal('toggle');
      showErrorDialogBox("Please select atleast one student to update status");
      return;
    }

    var studentHPCReportCardStatusPayload = {'hpcExamType' : examType, 'studentExamDisplayDataStatus' : studentExamDisplayDataStatus, 'studentIdSet' : studentIds };
    $("#update-student-report-card-status-config-modal").modal('toggle');

    ajaxClient.post("/examination/update-hpc-student-report-card-status", {'studentHPCReportCardStatusPayload' : JSON.stringify(studentHPCReportCardStatusPayload)}, function(data){
      $("#student-details-status-modal-container").html(data);
      $("#configuration-status-modal").modal({backdrop: 'static', keyboard: false});
      publishHPCReportCards.loadStudentList();
    });

  },
};

var examTree = {
  rectNode : { width : 150, height : 60, textMargin : 5 },
  sidebarWidth : 270,
  // Set the dimensions and margins of the diagram
  margin : {top: 20, right: 0, bottom: 30, left: 0},
  width : function () {
    return screen.width - examTree.sidebarWidth - examTree.margin.left - examTree.margin.right;
  },

  height : function () {
    return 1000 - examTree.margin.top - examTree.margin.bottom;
  },

  nodeId : 0,
  duration : 750,

  initSVG : function () {
    // append the svg object to the body of the page
    // appends a 'group' element to 'svg'
    // moves the 'group' element to the top left margin
    return d3.select("#tree-container").append("svg")
        .attr("id", "render-svg")
        .attr("width", examTree.width() + examTree.margin.right + examTree.margin.left)
        .attr("height", examTree.height() + examTree.margin.top + examTree.margin.bottom)
      .append("g")
        .attr("transform", "translate("
              + examTree.margin.left + "," + examTree.margin.top + ")");
  },

  intiArrowMarker : function (svg) {
    svg.append('defs').append('marker')
            .attr('id','arrowhead')
            .attr('viewBox','-0 -5 10 10')
            .attr('refX',9)
            .attr('refY',0)
            .attr('orient','auto')
            .attr('markerWidth',4)
            .attr('markerHeight',4)
            .attr('xoverflow','visible')
            .append('svg:path')
            .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
            .attr('fill', 'darkgrey')
            .style('stroke','none');
  },

  initShadow : function (svg) {
    // filters go in defs element
    var defs = svg.append("defs");
    // create filter with id #drop-shadow
    // height=130% so that the shadow is not clipped
    var filter = defs.append("filter")
        .attr("id", "drop-shadow")
        .attr("height", "130%");

    // SourceAlpha refers to opacity of graphic that this filter will be applied to
    // convolve that with a Gaussian with standard deviation 3 and store result
    // in blur
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 5)
        .attr("result", "blur");

    // translate output of Gaussian blur to the right and downwards with 2px
    // store result in offsetBlur
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 5)
        .attr("dy", 5)
        .attr("result", "offsetBlur");

    // overlay original SourceGraphic over translated blurred opacity by using
    // feMerge filter. Order of specifying inputs is important!
    var feMerge = filter.append("feMerge");

    feMerge.append("feMergeNode")
        .attr("in", "offsetBlur")
    feMerge.append("feMergeNode")
        .attr("in", "SourceGraphic");
  },

  breadthFirstTraversal : function (rootNode) {
        var queue=[];
        var animX=0;
        queue.push(rootNode);
        queue.push(null);
        var maxLevelNodes = 0;
        var nodeCount = 0;
        while(queue.length!==0){
          var element = queue.shift();
          if(element == null){
            if(maxLevelNodes < nodeCount){
              maxLevelNodes = nodeCount;
            }
            nodeCount = 0;
            if(queue.length!==0){
              queue.push(null);
            }
            continue;
          }
          nodeCount++;
          // visitElement(element,animX);
          animX= animX+1;
          if(element.children!= null){
            for(var i=0; i<element.children.length; i++){
              queue.push(element.children[i]);
            }
          }
        }
  },

  initTreeLayout : function () {
    // declares a tree layout
    return d3.tree();
  },

  createTreeHierarchy : function (data) {
      // Assigns parent, children, height, depth
      var root = d3.hierarchy(data, function(d) { return d.children; });
      root.x0 = 0;
      root.y0 = 0;
      return root;
  },

  updateTree : function (svg, treeLayout, root, source) {
    // Assigns the x and y position for the nodes
    var treeData = treeLayout(root);
    var numLeaves = treeData.leaves().length;
    var treeHeight = root.height;
    var minSvgWidth = numLeaves*examTree.rectNode.width*2 < examTree.width() ? examTree.width() : numLeaves*examTree.rectNode.width*2;
    var minSvgHeight = treeHeight*80 < examTree.height() ? examTree.height() : (treeHeight+1)*80;
    // var newWidth = numLeaves*(examTree.rectNode.width*1.5);
    // if(newWidth < examTree.width()){
    //   newWidth = examTree.width();
    // }
    //Update new tree width based on current node state
    d3.select("#render-svg")
        .attr("width", minSvgWidth + examTree.margin.right + examTree.margin.left)
        .attr("height", minSvgHeight + examTree.margin.top + examTree.margin.bottom);

    treeLayout.size([minSvgWidth, minSvgHeight]);
    treeData = treeLayout(root);
    // Compute the new tree layout.
    var nodes = treeData.descendants(),
        links = treeData.descendants().slice(1);

    // Normalize for fixed-depth.
    nodes.forEach(function(d){ d.y = d.depth * 140});
    var node = svg.selectAll('g.node')
        .data(nodes, function(d) {return d.id || (d.id = ++examTree.nodeId); });

    var nodeEnter = examTree.createNodes(svg, node , treeLayout, root, source);
    examTree.updateNodes(node, nodeEnter, svg, source);
    examTree.bindExitNodes(node, svg, source);

    var link = svg.selectAll('path.link')
        .data(links, function(d) { return d.id; });

    var linkEnter = examTree.createLinks(svg, link, source);
    examTree.updateLinks(svg,linkEnter, link);
    examTree.bindLinkExit (link, source);

    // Store the old positions for transition.
    nodes.forEach(function(d){
        d.x0 = d.x;
        d.y0 = d.y;
    });
  },

  createNodes : function (svg, node, treeLayout, root, source) {
    // Update the nodes...

    // Enter any new modes at the parent's previous position.
    var nodeEnter = node.enter().append('g')
        .attr('class', 'node')
        .attr("transform", function(d) {
          return "translate(" + source.x0 + "," + source.y0 + ")";
      });

    nodeEnter.append('rect')
        .attr('class', 'node first')
        .attr('width', examTree.rectNode.width)
        .attr('height', examTree.rectNode.height)
        .style("filter", "url(#drop-shadow)")
        .style("fill", function(d) {
            return d._children ? "lightsteelblue" : "#fff";
        })
        .on('click', function (d) {
          return examTree.nodeClick(d);
        });

    nodeEnter.append('text')
        .attr("y", examTree.rectNode.height/1.8)
        .attr("x", function(d) {
          return examTree.rectNode.width/2;
        })
        .attr("text-anchor", function(d) {
          return "middle";
        })
        .attr('cursor', 'pointer')
        .attr('fill', 'white')
        .style("font-size",function (d) {
            // var num = 1.27 - 0.023*d.data.examMetaData.examName.length;
            // num = num > 1 ? 1 : num;
            // num = num < 0.5 ? 0.5 : num;
            return "1em";
        })
        .style("font-weight","bold")
        .text(function(d) {
            if(d.data.examMetaData.examName.length > 16){
              return d.data.examMetaData.examName.substring(0,14) + "...";
            }
            return d.data.examMetaData.examName;
          })
        .on('mouseover', function () {
          $(this).css("font-size","1.1em");
        })
        .on('mouseout', function () {
          $(this).css("font-size","1em");
        })
        .on('click', function (d) {
          return examTree.nodeClick(d);
        });


    nodeEnter.append('rect')
        .attr('class', 'maximize')
        .attr('x', examTree.rectNode.width - 15)
        .attr('y', source.y0 + 5)
        // .attr('width', rectNode.width)
        .attr('width', 10)
        .attr('height', 10)
        .style("stroke", "#C8C8C8")
        .style("stroke-width", "1.5")
        .style("fill", function(d) {
            return d._children ? "#da5353" : "#2984C7";
        })
        .style("display", function (d) {
            return d._children ? "" : "none";
        })
        .attr('cursor', 'pointer')
        .on('click', function (d) {
            return examTree.click(svg, treeLayout, root, d);
        });

     nodeEnter.append('text')
            .attr("class", "minimize")
            .attr("y", examTree.rectNode.height*0.3)
            .attr("x", function(d) {
              return examTree.rectNode.width*0.95;
            })
            .attr("text-anchor", function(d) {
              return "end";
            })
            .attr('cursor', 'pointer')
            .attr('fill', '#C8C8C8')
            .style("font-size","2em")
            .style("display", function (d) {
                return d.children  ? "" : "none";
            })
            .text(function(d) { return "-"; })
            .on('click', function (d) {
                return examTree.click(svg, treeLayout, root, d);
            });
        return nodeEnter;
  },

  updateNodes : function (node, nodeEnter, svg, source) {
    // UPDATE
    var nodeUpdate = nodeEnter.merge(node);
    // Transition to the proper position for the node
    nodeUpdate.transition()
      .duration(examTree.duration)
      .attr("transform", function(d) {
          return "translate(" + (d.x - examTree.rectNode.width/2) + "," + d.y + ")";
       });

    // Update the node attributes and style
    nodeUpdate.selectAll('rect.node')
      // .attr('r', 10)
      .attr('width', examTree.rectNode.width)
      .attr('height', examTree.rectNode.height)
      .attr('rx', 2)
      .attr('ry', 2)
      .style("filter", "url(#drop-shadow)")
      .style("fill", function(d) {
          return d._children ? "#da5353" : "#2984C7";
      })
      .attr('cursor', 'pointer');

      nodeUpdate.selectAll('text.minimize')
      .attr("y", examTree.rectNode.height*0.3)
      .attr("x", function(d) {
        return examTree.rectNode.width*0.95;
          // return d.children || d._children ? -13 : 13;
      })
      .attr("text-anchor", function(d) {
        return "end";
          // return d.children || d._children ? "end" : "start";
      })
      .attr('cursor', 'pointer')
      .attr('fill', '#C8C8C8')
      .style("font-size","2em")
      .style("display", function (d) {
          return  d.children ? "" : "none";
      })
      // .style("font-weight","bold")
      .text(function(d) { return "-"; });
      // .on('click', click);

      nodeUpdate.selectAll('rect.maximize')
          .attr('x', examTree.rectNode.width - 15)
          .attr('y', 5)
          // .attr('width', rectNode.width)
          .attr('width', 10)
          .attr('height', 10)
          .style("stroke", "#C8C8C8")
          .style("stroke-width", "1.5")
          .style("fill", function(d) {
              return d._children ? "#da5353" : "#2984C7";
          })
          .style("display", function (d) {
              return d._children ? "" : "none";
          })
          .attr('cursor', 'pointer');

  },

  bindExitNodes : function (node, svg, source) {
    // Remove any exiting nodes
    var nodeExit = node.exit().transition()
        .duration(examTree.duration)
        .attr("transform", function(d) {
            return "translate(" + source.x + "," + source.y + ")";
        })
        .remove();

    // On exit reduce the node circles size to 0
    nodeExit.select('rect')
      // .attr('r', 1e-6)
      .attr('width', 1e-6)
      .attr('height', 1e-6);

    // On exit reduce the opacity of text labels
    nodeExit.select('text')
      .style('fill-opacity', 1e-6);
  },

  createLinks : function (svg, link, source) {
    // Enter any new links at the parent's previous position.
    var linkEnter = link.enter().insert('path', "g")
        .attr("class", "link")
        .attr('marker-end','url(#arrowhead)')
        .attr('d', function(d){
          var o = {x: source.x0, y: source.y0}
          return examTree.diagonal(o, o)
        });
        return linkEnter;
  },

  updateLinks : function (svg,linkEnter, link) {
    var linkUpdate = linkEnter.merge(link);

    // Transition back to the parent element position
    linkUpdate.transition()
        .duration(examTree.duration)
        .attr('d', function(d){ return examTree.diagonal(d, d.parent) });
  },

  bindLinkExit : function (link, source) {
    // Remove any exiting links
    var linkExit = link.exit().transition()
        .duration(examTree.duration)
        .attr('d', function(d) {
          var o = {x: source.x, y: source.y}
          return examTree.diagonal(o, o)
        })
        .remove();

  },

  diagonal : function (s, d) {

    path = `M ${d.x} ${d.y}
            V ${(s.y + d.y)/2 + examTree.rectNode.height/2}
            H ${s.x}
            V ${s.y}`
    return path
  },

  click : function (svg, treeLayout, root, d) {
    // var collapse = confirm("Collapse or not?");
    // if(!collapse){
    //   return;
    // }
    if (d.children) {
        d._children = d.children;
        d.children = null;
      } else {
        d.children = d._children;
        d._children = null;
      }
    examTree.updateTree(svg, treeLayout, root, d);
  },

  nodeClick : function (d) {
    examDetailsNamespace.loadExamDetailsPage(d.data.examMetaData.examId);
  },

  render: function (data) {
    var svg = examTree.initSVG();
    examTree.intiArrowMarker(svg);
    examTree.initShadow(svg);
    var treeLayout = examTree.initTreeLayout();
    var root = examTree.createTreeHierarchy(data);
    examTree.updateTree(svg, treeLayout, root, root);
  }
};

var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;

$(document).ready(function() {
  menuLoader.registerSidebarMenu();
  // homePage.initHomePage();

  var urlParams = new URLSearchParams(window.location.search);
  actionType = urlParams.get(ACTION_TYPE);

  if(actionType === "" || actionType === null) {
      homePage.initHomePage();
  }
  else if (actionType === "fill-marks"){
    $(".sidebar-nav").find('.sidebar-item').each(function() {
      $(this).removeClass("active");
    });
    $("#feedMarksNav").parent().addClass("active");
    marksFeeding.loadMainPage();

    //remove params from url
    var url_string = window.location.href;
    url = removeURLParameter(url_string, ACTION_TYPE)
    let stateObj = { id: "100" };
    window.history.replaceState(stateObj, url_string, url);
  }

  paymentReminder.readPaymentState();
});

var menuLoader = {

    registerSidebarMenu : function () {
        sideBarHoverEventCallback();
        activateMenuItem();
        menuLoader.registerHomeMenu();
        menuLoader.registerExamConfigurationMenu();
        menuLoader.registerFeedMarksMenu();
        menuLoader.registerAdvanceFeedMarksMenu();
        menuLoader.registerViewMarksMenu();
        menuLoader.registerDimensionsMenu();
        menuLoader.registerDatesheetMenu();
        menuLoader.registerPublishMarksMenu();
        menuLoader.registerPublishReportCardsMenu();
        menuLoader.registerUpdateStudentInfoMenu();
        menuLoader.registerPersonalityTraits();
        menuLoader.registerReportCardVariables();
        menuLoader.registerReportCardMenu();
        menuLoader.registerGreenSheetMenu();
        menuLoader.registerReportsDetailsMenu();
        menuLoader.registerAdmitCardsMenu();
        menuLoader.registerNotificationMenu();
        menuLoader.registerNotificationHistoryMenu();
        menuLoader.registerGradeSchemeMenu();
        menuLoader.registerStudentHPCFormMenu();
        menuLoader.registerHPCReportCardMenu();
        menuLoader.registerHPCPublishReportCardsMenu();
    },

    registerHomeMenu : function () {
        $('#homeNav').on('click', function() {
            homePage.loadHomePage();
        });
    },

    registerExamConfigurationMenu : function () {
        $('#configureExamNav').on('click', function() {
            examinationConfig.loadExamConfigurationPage();
        });
    },

    registerFeedMarksMenu : function () {
        $('#feedMarksNav').on('click', function() {
            marksFeeding.loadMainPage();
        });
    },

    registerAdvanceFeedMarksMenu : function () {
        $('#advanceFeedMarksNav').on('click', function() {
            advanceMarksFeeding.loadMainPage();
        });
    },

    registerViewMarksMenu : function () {
        $('#viewMarksNav').on('click', function() {
            marksView.loadMainPage();
        });
    },

    registerDimensionsMenu : function () {
        $('#dimensionsNav').on('click', function() {
            dimensions.loadDimensionsPage();
        });
    },

    registerPersonalityTraits : function () {
      $('#personalityTraits').on('click', function() {
          personalityTraits.loadMainPage();
      });
    },

    registerReportCardVariables : function () {
        $('#reportCardVariables').on('click', function() {
            reportCardVariables.loadMainPage();
        });
    },

    registerReportCardMenu : function () {
        $('#reportCardNav').on('click', function() {
            reportCard.loadReportCardPage();
        });
    },

    registerDatesheetMenu : function () {
        $('#datesheetNav').on('click', function(){
            datesheet.loadDatesheetPage();
        });
    },

    registerPublishMarksMenu : function () {
        $('#publishMarksNav').on('click', function(){
            publishMarks.loadPublishMarksPage();
        });
    },

    registerPublishReportCardsMenu : function () {
      $('#publishReportCardsNav').on('click', function(){
          publishReportCards.loadMainScreen();
      });
    },

    registerUpdateStudentInfoMenu : function () {
        $('#updateStudentInfoNav').on('click', function(){
            updateStudentInfo.loadMainScreen();
        });
    },
    
    registerStudentHPCFormMenu : function () {
      $('#hpcStudentForm').on('click', function() {
          hpc.loadHPCFormPage();
      });
    },

    registerHPCPublishReportCardsMenu : function () {
      $('#publishHPCReportCardsNav').on('click', function(){
          publishHPCReportCards.loadMainScreen();
      });
    },

    registerHPCReportCardMenu : function () {
      $('#generateHPC').on('click', function() {
          hpcReportCard.loadHPCReportCardPage();
      });
    },
    
    registerGreenSheetMenu : function () {
        $('#greenSheetNav').on('click', function() {
            greenSheet.loadGreenSheetPage();
        });
    },

    registerAdmitCardsMenu : function () {
        $('#admitCardsNav').on('click', function() {
            admitCards.loadMainScreen();
        });
    },

    registerNotificationMenu : function () {
        $('#notificationNav').on('click', function() {
            notification.loadMainScreen();
        });
    },

    registerNotificationHistoryMenu : function () {
        $('#notificationHistoryNav').on('click', function() {
            notificationHistory.loadNotificationHistoryHomePage();
        });
    },

    registerReportsDetailsMenu : function () {
        $('#reportsNav').on('click', function() {
            examinationReports.loadMainScreen();
        });
    },

    registerGradeSchemeMenu : function (){
      $('#gradeNav').on('click', function(){
        gradeScheme.loadGradeSchemeListPage(); 
      });
    },
};

var homePage =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        // homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/examination/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/examination/session-home/"+academicSessionId, function(data) {
          $("#examination-dashboard-session-content").html(data);
          // homePage.displayDashboardContent();
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

   displayDashboardContent : function () {
     var instituteStats= readJson("#home-page-stats");
     var labelArr = [];
     var dataArr = []
     var total = 0;
     for( i = 0 ; i < instituteStats.length; i++){
       labelArr.push(instituteStats[i].standard.displayName);
       dataArr.push(instituteStats[i].standard.studentCount);
       total += instituteStats[i].standard.studentCount;
     }
     $("#total-student-count").text(total);

     homePage.renderStudentCountChart(labelArr, dataArr);

     homePage.renderFeeCollectionPieChart(labelArr, dataArr);
   },

   renderStudentCountChart : function (labelArr, dataArr) {
     // Bar chart
     new Chart($("#chartjs-student-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
           label: "Last year",
           backgroundColor: window.theme.primary,
           borderColor: window.theme.primary,
           hoverBackgroundColor: window.theme.primary,
           hoverBorderColor: window.theme.primary,
           data: dataArr,
           barPercentage: .325,
           categoryPercentage: .5
         }]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             gridLines: {
               display: false
             },
             stacked: false,
             ticks: {
               stepSize: 20
             },
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

   renderFeeCollectionPieChart : function (labelArr, dataArr) {
     new Chart($("#chartjs-fee-collection-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: [
             window.theme.success,
             window.theme.warning,
             window.theme.danger,
             window.theme.info,
             "#108F2B",
             "#C08143 ",
             "#DCDF69",
             "#719E90",
             "#A8B9DF ",
             "#B74034",
             "#F196CD",
             "#6486B9",
             "#5551FA",
             "#E8F697",
             "#0591F6",
             "#4C6C42",
             "#442BC6",
           ],
           borderWidth: 5,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: true,
         cutoutPercentage: 70,
         legend: {
           display: false
         }
       }
     });
   },
};

function studentLiveSearchEvent(sessionId, searchText, resultArea, triggerMethod, status){
  ajaxClient.get("/examination/student-live-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
      $(resultArea).html(data);
      studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
  });
}
