$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    homePage.initHomePage();
    paymentReminder.readPaymentState();
});

var menuLoader = {

    registerSidebarMenu : function () {
        sideBarHoverEventCallback();
        activateMenuItem();
        menuLoader.registerHomeMenu();
    },
  
    registerHomeMenu : function () {
        $('#homeNav').on('click', function() {
            homePage.loadHomePage();
        });
        $('#addLessonPlanNav').on('click', function(){
            lessonPlanPage.loadMainPage();
        })
    },

};

var homePage =  {
    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);

    },

    loadHomePage : function () {
      ajaxClient.get("/lesson-plan/home", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/lesson-plan/session-home/"+academicSessionId, function(data) {
          $("#lesson-plan-dashboard-session-content").html(data);
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

};

var lessonPlanPage = {
    dataCache : {},

    loadMainPage : function () {
      ajaxClient.get("/lesson-plan/lesson-plan-view-page", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(lessonPlanPage.changeSession);
          lessonPlanPage.bindClassChangeEvent();
          lessonPlanPage.dataCache['adminAccess'] = $('#admin-access').text().trim();
          lessonPlanPage.dataCache['staffAccess'] = $('#staff-access').text().trim();
          lessonPlanPage.dataCache['staffAccess'] = $('#staff-response-access').text().trim();
          lessonPlanPage.dataCache.selectedLessonPlanCount = 0;
          lessonPlanPage.loadActionForStaffAndLessonPlan("");
      });
    },

    changeSession : function () {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/lesson-plan/standards/"+sessionId, function(data) {
          if(lessonPlanPage.dataCache['adminAccess'] == "True"){
            $("#standard-with-section-options-wrapper").html(data);
            lessonPlanPage.bindClassChangeEvent();
            $("#lesson-plan-main-container").html("<br><br><br><br> <br> <br> <br><br><br><br> <h5 style=\"text-align:center;color:#223b5c;\"> No Courses Lesson Plan is Avalible!</h5><br><br><br><br> <br> <br> <br><br><br><br>");
          }
          else{
            $("#lesson-plan-main-container").html(data);
            lessonPlanPage.loadActionForStaffAndLessonPlan("");
          }
          
      });

    },

    bindClassChangeEvent : function () {
        $("#lesson-plan-standard-section-select").change(function() {
           lessonPlanPage.loadCourseLessonPlanStructure($(this).find(':selected').val().trim());
        });
      },
  
    loadCourseLessonPlanStructure : function (standardSectionMapId) {
        if(standardSectionMapId == ""){
          return;
        }
        var tokens = standardSectionMapId.split(":");
        var standardId = tokens[0];
        if(standardId == ""){
          return;
        }
        var sectionId = null;
        if(tokens.length == 2){
          sectionId = tokens[1];
        }
        var sessionId = academicSessionHandler.getSelectedSessionId();
        lessonPlanPage.dataCache['classMarksStructures'] = []
        lessonPlanPage.dataCache['selectedStandardSectionMapId'] = standardSectionMapId;
        lessonPlanPage.dataCache['selectedStandard'] = standardId;
        lessonPlanPage.dataCache['selectedSection'] = sectionId;
  
        ajaxClient.get("/lesson-plan/course-lesson-plan-structure/"+standardId+"/"+sectionId+"/"+sessionId, function(data) {
            $("#lesson-plan-main-container").html(data);
            if(sectionId == null){
              $(".lesson-plan-clone-div").hide();
              $(".lesson-plan-course-clone").hide();
            }
            lessonPlanPage.loadActionForStaffAndLessonPlan();
        });
      },

      loadActionForStaffAndLessonPlan : function(){
        $(".lesson-plan-details").on('click', function() {
          var courseDetails =  $(this).parent().parent().parent().find('.course-staff-details-info-json').text().trim();
          var courseDetailsJson = JSON.parse(courseDetails);
          var courseId = courseDetailsJson.courseId;
          var courseName = courseDetailsJson.courseName;
          var sectionId = courseDetailsJson.standardRowDetails.sectionId;
          var standardId = courseDetailsJson.standardRowDetails.standardId;
          var standardName = "";
          var sessionId = academicSessionHandler.getSelectedSessionId();
          if(sectionId){
            standardName = courseDetailsJson.standardRowDetails.displayNameWithSection;
          }
          else{
            standardName = courseDetailsJson.standardRowDetails.displayName;
          }
          var lessonPlanId = $(this).parent().parent().parent().find('.lesson-plan-id').text().trim();
          var lessonPlanStatus = "ACTIVE";
          lessonPlanPage.getLessonPlanDetails(lessonPlanId, lessonPlanStatus, courseName, standardName, true, courseId, standardId, sectionId, sessionId, "");
        });
        $(".staff-response-details").on('click', function() {
          var courseDetails =  $(this).parent().parent().parent().find('.course-staff-details-info-json').text().trim();
          var courseDetailsJson = JSON.parse(courseDetails);
          var courseId = courseDetailsJson.courseId;
          var courseName = courseDetailsJson.courseName;
          var standardName = "";
          var staffId = courseDetailsJson.staffId;
          var sectionId = courseDetailsJson.standardRowDetails.sectionId;
          var standardId = courseDetailsJson.standardRowDetails.standardId;
          var sessionId = academicSessionHandler.getSelectedSessionId();
          if(courseDetailsJson.standardRowDetails.sectionId){
            standardName = courseDetailsJson.standardRowDetails.displayNameWithSection;
          }
          else{
            standardName = courseDetailsJson.standardRowDetails.displayName;
          }
          var lessonPlanId = $(this).parent().parent().parent().find('.lesson-plan-id').text().trim();
          var lessonPlanStatus = "ACTIVE";
          lessonPlanPage.getLessonPlanDetails(lessonPlanId, lessonPlanStatus, courseName, standardName, false, courseId, standardId, sectionId, sessionId, staffId);
        });
        $("#lesson-plan-course-clone-button").on('click', function(){
          initSelect2("Select Section ....", "clone-lesson-section");
          $('.select2-search__field').attr("style", "width:100%");
          var lessonPlanIds = lessonPlanPage.getSelectedBulkLessonPlanIds();
          var sessionId = academicSessionHandler.getSelectedSessionId();
          if(lessonPlanPage.dataCache.selectedLessonPlanCount == undefined){
              lessonPlanPage.dataCache.selectedLessonPlanCount = 0;
          }
          var standardSectionMapId = $("#lesson-plan-standard-section-select").find(':selected').val().trim();
          if(standardSectionMapId == ""){
            return;
          }
          var tokens = standardSectionMapId.split(":");
          var standardId = tokens[0];
          if(standardId == ""){
            return;
          }
          var sectionId = null;
          if(tokens.length == 2){
            sectionId = tokens[1];
          }
          lessonPlanPage.updatePopupStandardSections(standardId, sectionId);
          $('#clone-confirm-popup-text').html(
            "Total Number Of Lesson Plans Cloned: " + 
            lessonPlanPage.dataCache.selectedLessonPlanCount + 
            " <span class='tooltip-star' title='Only Created lesson plans are considered for cloning'>*</span>"
          );

          $('#clone-conformation-data-popup').modal('toggle');
          lessonPlanPage.getCloneConformationDialogBox(sessionId, lessonPlanIds, standardId);
        });
        $(".delete-lesson-plan").on('click', function(){
          var lessonPlanId = $(this).parent().parent().find('p.lesson-plan-id').text().trim();
          var sessionId = academicSessionHandler.getSelectedSessionId();
          if(lessonPlanId == null || lessonPlanId == "" && lessonPlanId == 'None' || lessonPlanId == 'undefined'){
              showErrorDialogBox("Lesson Plan Is Not Created So Not Able To Delete");
              return;
          }
          $("#lesson-plan-delete-warning-popup").modal('toggle');
          lessonPlanPage.deleteLessonPlanDetails(sessionId, lessonPlanId);
        });
      },
      deleteLessonPlanDetails : function(academicSessionId, lessonPlanId){
        $("#lesson-plan-delete-confirm-button").on('click', function(){
          $("#lesson-plan-delete-warning-popup").modal('toggle');
          ajaxClient.post("/lesson-plan/lesson-plan-delete-details/"+academicSessionId+"/"+lessonPlanId,{},function(data){
            $("#lesson-plan-status-modal-container").html(data);
            $("#lesson-plan-status-modal").modal('toggle');
            $("#lesson-plan-status-modal").on('hidden.bs.modal', function () {
              if(lessonPlanPage.dataCache['adminAccess'] == "True"){
                var standardIdstr = lessonPlanPage.dataCache['selectedStandardSectionMapId'];
                lessonPlanPage.loadCourseLessonPlanStructure(standardIdstr);
              }else{
                lessonPlanPage.loadMainPage();
              }
            });
          });
        });
      },
      getCloneConformationDialogBox : function(sessionId, lessonPlanIds, standardId){
          $('#lesson-plan-confirm-button').on('click', function(){
            if(lessonPlanPage.dataCache.selectedLessonPlanCount == 0){
               $('#clone-conformation-data-popup').modal('toggle');
               showErrorDialogBox("Please select lesson Plan first to Clone");
                return;
            }
            var selectedSections = $("#add-clone-lesson-plan-popup-class-section").val() || [];
            if(selectedSections.length <= 0){
                $('#clone-conformation-data-popup').modal('toggle');
                showErrorDialogBox("Please select a section. Cloning is permitted only between sections within the same standard.");
                $("#dialog-box-modal").on('hidden.bs.modal', function () {
                  $('#clone-conformation-data-popup').modal('toggle');
                });
                return;
            }
            var cloneLessonPlanPayload = {"lessonPlanIds": lessonPlanIds, "standardId": standardId, "sectionIds": selectedSections}
            lessonPlanPage.cloneLessonPlanDetails(sessionId, cloneLessonPlanPayload);
            $('#clone-conformation-data-popup').modal('toggle');
            $('#clone-conformation-popup').modal('toggle');

          });
      },
      cloneLessonPlanDetails : function(sessionId, cloneLessonPlanPayload){
        $('#clone-conformation-popup-confirm-button').on('click', function(){
          $("#clone-conformation-popup").modal('toggle');
          ajaxClient.post("/lesson-plan/lesson-plan-clone-details/"+sessionId, JSON.stringify(cloneLessonPlanPayload),function(data){
            $("#lesson-plan-status-modal-container").html(data);
            $("#lesson-plan-status-modal").modal('toggle');
            $("#lesson-plan-status-modal").on('hidden.bs.modal', function () {
              if(lessonPlanPage.dataCache['adminAccess'] == "True"){
                var standardIdstr = lessonPlanPage.dataCache['selectedStandardSectionMapId'];
                lessonPlanPage.loadCourseLessonPlanStructure(standardIdstr);
               }else{
                lessonPlanPage.loadMainPage();
              }
            });
          });
        });
      },
      getLessonPlanDetails : function(lessonPlanId, lessonPlanStatus, courseName, standardName, adminAccess, courseId, standardId, sectionId, sessionId, staffId){
        ajaxClient.get("/lesson-plan/lesson-plan-structure/"+lessonPlanId+"/"+lessonPlanStatus, function(data) {
              $("#lesson-plan-staff-response-detail-container").html(data);
              $("#lesson-plan-course-name").text(courseName);
              $("#lesson-plan-standard-name").text(standardName);
              $("#lesson-plan-academic-session-div").css("display","none");
              $("#lesson-plan-main-container").css("display","none");
              $("#lesson-plan-form-navbar").css("display","block");
              $("#lesson-plan-staff-response-detail-container").css("display", "block");
              const chapterData = $('.chapter-details-list').text() != 'None' ? $('.chapter-details-list').text().trim() : '[]';
              const chapterList = JSON.parse(chapterData);
              lessonPlanAndStaffResponseUI.renderLessonPlan(chapterList, 0, adminAccess);
              initDateWithYearRange("-1:+1");
              if(adminAccess){
                lessonPlanPage.registerSaveLessonPlanData(lessonPlanId, courseId, standardId, sectionId, sessionId);
              }
              else{
                lessonPlanPage.registerSaveStaffResponse(lessonPlanId, courseId, standardId, sectionId, sessionId, staffId);
                $(".add-chapter-plan").hide();
              }
          });
      },
      back : function(){
        $("#lesson-plan-academic-session-div").css("display","block");
        $("#lesson-plan-main-container").css("display","block");
        $("#lesson-plan-form-navbar").css("display","none");
        $("#lesson-plan-staff-response-detail-container").css("display", "none");
      },
      registerSaveStaffResponse : function(lessonPlanId, courseId, standardId, sectionId, sessionId, staffId){
         $('#add-lesson-plan-details').on('click', function () {
          var lessonPlanStaffResponseList = lessonPlanPage.collectStaffResponsePayload(lessonPlanId, staffId);
          if(lessonPlanStaffResponseList == false){
            return;
          }
          if(lessonPlanStaffResponseList.length <= 0){
              showErrorDialogBox("Please add atleast one Staff Response");
              return;
          }
          $("#lesson-plan-alert-popup").modal('toggle');
          $("#lesson-plan-save-details-popup-text").text("Do you want to save Staff Response details?");
           $("#lesson-plan-save-confirm-button").on('click', function() {
            lessonPlanPage.saveStaffResponseData(courseId, lessonPlanStaffResponseList, standardId, sectionId, sessionId);
          });
        })
      },

      saveStaffResponseData : function(courseId, finalPayload, standardId, sectionId, sessionId){
        $("#lesson-plan-alert-popup").modal('toggle');
        ajaxClient.post("/lesson-plan/staff-response-details/"+sessionId+"?course_id="+courseId, JSON.stringify(finalPayload),function(data){
            $("#lesson-plan-status-modal-container").html(data);
            $("#lesson-plan-status-modal").modal('toggle');
            $("#lesson-plan-status-modal").on('hidden.bs.modal', function () {
              lessonPlanPage.back();
              if(lessonPlanPage.dataCache['adminAccess'] == "True"){
                var standardIdstr = standardId+":";
                if(sectionId){
                  standardIdstr = standardIdstr + sectionId;
                }
                lessonPlanPage.loadCourseLessonPlanStructure(standardIdstr);
               }else{
                lessonPlanPage.loadMainPage();
              }
            });
        });
      },

      registerSaveLessonPlanData : function(lessonPlanId, courseId, standardId, sectionId, sessionId){
        $('#add-lesson-plan-details').on('click', function () {
          var lessonPlanChapterPayloadList = lessonPlanPage.collectChapterTopicPayload();
          if(lessonPlanChapterPayloadList == false){
            return;
          }
          if(lessonPlanChapterPayloadList.length <= 0){
              showErrorDialogBox("Please add atleast one Chapter");
              return;
          }
          var finalPayload = lessonPlanPage.collectLessonPlanPayload(lessonPlanId, courseId, standardId, sectionId, lessonPlanChapterPayloadList);
          if(finalPayload.length <= 0){
            showErrorDialogBox("Please add atleast one Chapter");
            return;
          }
           $("#lesson-plan-alert-popup").modal('toggle');
          $("#lesson-plan-save-confirm-button").on('click', function() {
            lessonPlanPage.saveLessonPlanData(courseId, finalPayload, standardId, sectionId, sessionId);
          });
        })
      },

      saveLessonPlanData : function(courseId, finalPayload, standardId, sectionId, sessionId){
        $("#lesson-plan-alert-popup").modal('toggle');
        ajaxClient.post("/lesson-plan/lesson-plan-details/"+sessionId+"?course_id="+courseId, JSON.stringify(finalPayload),function(data){
            $("#lesson-plan-status-modal-container").html(data);
            $("#lesson-plan-status-modal").modal('toggle');
            $("#lesson-plan-status-modal").on('hidden.bs.modal', function () {
              lessonPlanPage.back();
              if(lessonPlanPage.dataCache['adminAccess'] == "True"){
                var standardIdstr = standardId+":";
                if(sectionId){
                  standardIdstr = standardIdstr + sectionId;
                }
                lessonPlanPage.loadCourseLessonPlanStructure(standardIdstr);
              }else{
                lessonPlanPage.loadMainPage();
              }
            });
        });
      },

      collectLessonPlanPayload : function (lessonPlanId, courseId, standardId, sectionId, chapterTopicPayload){
        const payload = [];
        const sectionIdList = [];
        if(sectionId != null){
            sectionIdList.push(sectionId);
        }
        if(lessonPlanId == null || lessonPlanId == "" || lessonPlanId == 'None' || lessonPlanId == 'undefined'){
          lessonPlanId = null
        }
        const lessonPlanMetaDataPayload ={
          lessonPlanId : lessonPlanId,
          title : "",
          description : "",
          standardId : standardId,
          sectionIds : sectionIdList,
          courseId : courseId,
          lessonPlanStatus : "ACTIVE"
        }
        payload.push({
          lessonPlanMetaDataPayload : lessonPlanMetaDataPayload,
          lessonPlanChapterPayloadList : chapterTopicPayload
        })

        return payload;
        
      },
      collectChapterTopicPayload : function() {
        const payload = [];
        let hasError = false;

        $('#chapter-container .card.card-border').each(function () {
          if (hasError) return false;

          const $chapterCard = $(this);
          const $chapterRow = $chapterCard.find('.chapter-details tbody tr');

          let chapterIdText = $chapterRow.find('.chapter-id').text();
          let chapterId = null;
          if (chapterIdText && chapterIdText.trim() !== '') {
            chapterId = chapterIdText.trim();
          }
          const chapterTitleText = $chapterRow.find('.chapter-title').val();
          let chapterTitle = '';
          if (chapterTitleText && chapterTitleText.trim() !== '') {
            chapterTitle = chapterTitleText.trim();
          }
          const chapterDescriptionText = $chapterRow.find('.chapter-description').val();
          let chapterDescription = '';
          if (chapterDescriptionText && chapterDescriptionText.trim() !== '') {
            chapterDescription = chapterDescriptionText.trim();
          }
          let dueDate = getDate($chapterRow.find('.chapter-duedate').val());
          var invalid = validateMandatoryFields($chapterRow)
          if(invalid){
            showErrorDialogBox("Chapter Name Must be given");
            hasError = true;
            return false;
          }
          if(dueDate != null){
            dueDate = dueDate.getTime() / 1000;
          }
          let chapter = {};
          if(chapterId == "undefined" || chapterId == 'None'){
            chapterId = null;
          }
          if(chapterTitle !== ''){
            chapter = {
              chapterId: chapterId,
              title: chapterTitle,
              description: chapterDescription,
              dueDate: dueDate,
              chapterTopicPayloadList: []
            };
          }
      
          $chapterCard.find('.topic-details tbody tr').each(function () {
            if (hasError) return false;

            const $topicRow = $(this);
            
            const topicIdText = $topicRow.find('.topic-id').val();
            let topicId = null;
            if (topicIdText && topicIdText.trim() !== '') {
              topicId = topicIdText.trim();
            }
            const topicNameText = $topicRow.find('.topic-name').val();
            let topicName = '';
            if (topicNameText && topicNameText.trim() !== '') {
              topicName = topicNameText.trim();
            }
            const topicDescText = $topicRow.find('.topic-description').val();
            let topicDesc = '';
            if (topicDescText && topicDescText.trim() !== '') {
              topicDesc = topicDescText.trim();
            }

            let topicDueDate = getDate($topicRow.find('.topic-duedate').val());
            var invalid = validateMandatoryFields($topicRow)
            if(invalid){
              showErrorDialogBox("Topic Name Must be given");
              hasError = true;
              return false;
            }
            if(topicDueDate != null){
              topicDueDate = topicDueDate.getTime() / 1000;
            }
            if(topicId == 'None' || topicId == 'undefined'){
            topicId = null;
          }
            if (chapterTitle !== '' && topicName !== '') {
              chapter.chapterTopicPayloadList.push({
                topicId : topicId,
                title: topicName,
                description: topicDesc,
                dueDate: topicDueDate
              });
            }
          });
          
          if (chapterTitle !== '') {
            payload.push(chapter);
          }
        });
        if (hasError) return false;
        return payload;
      },
      
      collectStaffResponsePayload : function(lessonPlanId, staffId) {
        const payload = [];
        let hasError = false;

        $('#chapter-container .card.card-border').each(function () {
          if (hasError) return false;

          const $chapterCard = $(this);
          const $chapterRow = $chapterCard.find('.chapter-details tbody tr');

          const chapterIdText = $chapterRow.find('.chapter-id').text();
          let chapterId = null;
          if (chapterIdText && chapterIdText.trim() !== '') {
            chapterId = chapterIdText.trim();
          }
      
          $chapterCard.find('.topic-details tbody tr').each(function () {
            if (hasError) return false;

            const $topicRow = $(this);
            let topicId = $topicRow.find('.topic-id').val() || null;
            
            const topicRemarkText = $topicRow.find('.topic-remarks').val();
            let topicRemark = '';
            if (topicRemarkText && topicRemarkText.trim() !== '') {
              topicRemark = topicRemarkText.trim();
            }

            const topicStatusText = $topicRow.find('.topic-status').val();
            let topicStatus = '';
            if (topicStatusText && topicStatusText.trim() !== '') {
              topicStatus = topicStatusText.trim();
            }

            let topicStartDate = getDate($topicRow.find('.topic-startdate').val());
            let topicCompleteDate = getDate($topicRow.find('.topic-compeletedate').val());
            var invalid = validateMandatoryFields($topicRow)
            if(invalid){
              showErrorDialogBox("Topic Status Must be given");
              hasError = true;
              return false;
            }
            if(topicStartDate != null){
              topicStartDate = topicStartDate.getTime() / 1000;
            }
             if(topicCompleteDate != null){
              topicCompleteDate = topicCompleteDate.getTime() / 1000;
            }
            let staffResponse = {};
            if(topicId != 'None' && topicId != 'undefined' && topicId != null && chapterId != "undefined" && chapterId != 'None' && chapterId != null){
              staffResponse = {
                lessonPlanId: lessonPlanId,
                staffId: staffId,
                chapterId: chapterId,
                topicId: topicId,
                startedOn : topicStartDate,
                completedOn : topicCompleteDate,
                lessonPlanStatus : topicStatus,
                remarks : topicRemark
              };
              payload.push(staffResponse);
            }
          });
        });
        if (hasError) return false;
        return payload;
      },
      selectLessonPlanList : function (selectAllCheckbox) {
        if(selectAllCheckbox.checked){
            $(".lesson-plan-course-clone").prop('checked', true);
            var selectedLessonPlanCount = $('input.lesson-plan-course-clone:checkbox:checked').length;
            $("#lesson-plan-course-clone-count").html(selectedLessonPlanCount);
            lessonPlanPage.dataCache.selectedLessonPlanCount = selectedLessonPlanCount;
        }
        else{
            $(".lesson-plan-course-clone").prop('checked', false);
            $("#lesson-plan-course-clone-count").html(0);
            lessonPlanPage.dataCache.selectedLessonPlanCount = 0;
        }
    },
    lessonPlanSelectCheckbox : function(lessonPlanSelectCheckbox) {
        var selectedlessonPlanCount = $('input.lesson-plan-course-clone:checkbox:checked').length;
        $("#lesson-plan-course-clone-count").html(selectedlessonPlanCount);
        lessonPlanPage.dataCache.selectedLessonPlanCount = selectedlessonPlanCount;
    },
    getSelectedBulkLessonPlanIds :function () {
        var lessonPlanIds = [];
        $("input.lesson-plan-course-clone").each(function() {
            if(!$(this).is(":checked")) {
                return;
            }
            var lessonPlanId = $(this).parent().parent().find('p.lesson-plan-id').text().trim();
            if(lessonPlanId != null && lessonPlanId != "" && lessonPlanId != 'None' && lessonPlanId != 'undefined'){
              lessonPlanIds.push(lessonPlanId);
            }
        });
        lessonPlanPage.dataCache.selectedLessonPlanCount = lessonPlanIds.length;
        return lessonPlanIds;
    },
    updatePopupStandardSections : function (standardId, sectionId) {
        var standards = readJson(".standards-json");
        var classSectionMap = {}
        for(var i = 0 ; i < standards.length ; i++){
            var standard = standards[i]
            classSectionMap[standard.standardId] = standard
        }
        var options = "";
        if(!(standardId in classSectionMap)){
            $('.clone-lesson-section').html(options);
            return ;
        }
        var standard = classSectionMap[standardId];
        for(var i = 0 ; i < standard.standardSectionList.length; i++){
            if(standard.standardSectionList[i].sectionId == sectionId){
              continue;
            }
            options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
        }
        $('.clone-lesson-section').html(options);
    },

}
var lessonPlanAndStaffResponseUI = {
  globalChapterCounter : 0,
  
  createTextarea : function(value = '', className = '', fieldType = '') {
    const textarea = document.createElement('textarea');
    textarea.className = `form-control ${fieldType} ${className}`;
    textarea.value = value;
    return textarea;
  },

  createInput : function(value = '', placeholder = '', className = '', fieldType = '') {
    const input = document.createElement('input');
    input.className = `form-control select-date ${fieldType} ${className}`;
    input.type = 'text';
    input.placeholder = placeholder;
    input.value = value;
    return input;
  },
  
  createSelect : function(options = [], selectedValue = '', className = '', fieldType = '') {
    const select = document.createElement('select');
    select.className = `form-control ${fieldType} ${className}`;
  
    options.forEach(option => {
      const opt = document.createElement('option');
      opt.value = option.value;
      opt.textContent = option.label;
      if (option.value === selectedValue) {
        opt.selected = true;
      }
      select.appendChild(opt);
    });
  
    return select;
  },

  createTrashIcon : function(onClick) {
    const icon = document.createElement('i');
    icon.className = 'fas fa-trash text-danger';
    icon.style.cursor = 'pointer';
    icon.onclick = onClick;
    return icon;
  },

  createTopicRow : function(topicData = {}, lessonPlan = true) {
    const tr = document.createElement('tr');
    tr.style.textAlign = 'center';
    const hiddenTopicIdInput = document.createElement('input');
    hiddenTopicIdInput.type = 'hidden';
    hiddenTopicIdInput.className = 'topic-id';
    hiddenTopicIdInput.value = topicData.topicId || '';
    tr.appendChild(hiddenTopicIdInput);
    if(lessonPlan){
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTrashIcon(() => tr.remove())));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(topicData.title || '', 'topic-name', 'mandatory-field')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(topicData.description || '', 'topic-description')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(topicData.dueDate) || '', 'Enter Due Date ...', 'topic-duedate')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(topicData.staffStartedOn) || '-')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(topicData.staffCompletedOn) || '-')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(topicData.staffTopicStatus || '-')));
      tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(topicData.remarks || '-')));
      return tr;
    }
    const selectOption =[
        { value: 'NOT_STARTED', label: 'NOT STARTED' },
        { value: 'IN_PROGRESS', label: 'IN PROGRESS' },
        { value: 'COMPLETED', label: 'COMPLETED' },
      ]
      
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(topicData.title || '-')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(topicData.description || '-')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(document.createTextNode(getFormattedDate(topicData.dueDate) || '-')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(topicData.staffStartedOn) || '', 'Enter Started Date ...', 'topic-startdate')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createInput(getFormattedDate(topicData.staffCompletedOn) || '', 'Enter Complete Date ...', 'topic-compeletedate')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createSelect(selectOption, topicData.staffTopicStatus || 'NOT_STARTED', 'topic-status', 'mandatory-field')));
    tr.appendChild(lessonPlanAndStaffResponseUI.createCell(lessonPlanAndStaffResponseUI.createTextarea(topicData.remarks || '', 'topic-remarks')));

    return tr;
  },

  createCell : function(content) {
    const td = document.createElement('td');
    if (content instanceof Node) td.appendChild(content);
    else td.innerText = content;
    return td;
  },

  createTopicSection : function(topics = [], lessonPlan = true) {
    const table = document.createElement('table');
    table.className = 'table table-bordered table-hover topic-details';
    table.style.minWidth = '1500px';

    const thead = table.createTHead();
    if(lessonPlan){
      thead.innerHTML = `
      <tr style="text-align: center;">
        <th>Action</th>
        <th>Name*</th>
        <th>Description</th>
        <th>DueDate</th>
        <th>Started On</th>
        <th>Completed On</th>
        <th>Status</th>
        <th>Remarks</th>
      </tr>`;
    }
    else{
      thead.innerHTML = `
      <tr style="text-align: center;">
        <th>Name*</th>
        <th>Description</th>
        <th>DueDate</th>
        <th>Started On</th>
        <th>Completed On</th>
        <th>Status</th>
        <th>Remarks</th>
      </tr>`;
    }

    const tbody = document.createElement('tbody');
    topics.forEach(topic => {
      tbody.appendChild(lessonPlanAndStaffResponseUI.createTopicRow(topic, lessonPlan));
    });

    table.appendChild(tbody);

    const wrapper = document.createElement('div');
    wrapper.className = 'card-body';
    wrapper.style.overflowX = 'auto';
    wrapper.appendChild(table);
    if(lessonPlan){
      const addBtn = document.createElement('a');
      addBtn.textContent = 'Add More Topic';
      addBtn.style = 'text-decoration: none; color: #1F9BCF; cursor: pointer;';
     addBtn.onclick = () => {
        const newRow = lessonPlanAndStaffResponseUI.createTopicRow();
        tbody.appendChild(newRow);
        initDateWithYearRange("-1:+1");
      };
      wrapper.appendChild(addBtn);
    }
    

    return wrapper;
  },

  createChapterBox : function(chapterData = {}, topics = [], lessonPlan = true) {
    let chapterCounter = lessonPlanAndStaffResponseUI.globalChapterCounter;

    const chapterId = `chapter-${chapterCounter++}`;
    const card = document.createElement('div');
    card.className = 'card card-border';
    card.id = chapterId;

    const header = document.createElement('div');
    header.className = 'card-header';
    header.innerHTML = `
      <div class="row mb-2 mb-xl-3">
        <div class="col-auto mr-auto text-left mt-n1">
          <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-${chapterId}">
            <h5 style="padding-bottom:0px;color:#43a2ad;">Chapter ${chapterCounter}</h5>
          </button>
        </div>
      </div>`;

      const trash = lessonPlanAndStaffResponseUI.createTrashIcon(() => {
        card.remove();
        if (document.querySelectorAll('#chapter-container .card.card-border').length === 0) {
          $('#add-lesson-Plan').css('display','none');
          $('.no-chapter').show();
        }
      });
    if(lessonPlan){
      const trashWrapper = document.createElement('div');
      trashWrapper.className = 'col-auto ml-auto text-right mt-n1';
      trashWrapper.appendChild(trash);
      header.querySelector('.row').appendChild(trashWrapper);
    }  
    const collapse = document.createElement('div');
    collapse.className = 'collapse show';
    collapse.id = `collapse-${chapterId}`;
    const tableHeader = `
      <thead>
        <tr style="text-align: center;">
          <th>Chapter Name*</th>
          <th>Chapter Description</th>
          <th>Chapter DueDate</th>
        </tr>
      </thead>`;

    const chapterIdCell = `<td style="display: none;" class="chapter-id">${chapterData.chapterId}</td>`;

    let rowContent = '';

    if(lessonPlan){
      rowContent = `
        <tr style="text-align: center;">
          ${chapterIdCell}
          <td><textarea class="form-control mandatory-field chapter-title">${chapterData.title || ''}</textarea></td>
          <td><textarea class="form-control chapter-description">${chapterData.description || ''}</textarea></td>
          <td><input type="text" class="form-control select-date chapter-duedate" placeholder="Enter Due Date ..." value="${getFormattedDate(chapterData.dueDate) || ''}"/></td>
        </tr>`;
    } else {
      rowContent = `
        <tr style="text-align: center;">
          ${chapterIdCell}
          <td>${chapterData.title || '-'}</td>
          <td>${chapterData.description || '-'}</td>
          <td>${getFormattedDate(chapterData.dueDate) || '-'}</td>
        </tr>`;
    }

    collapse.innerHTML = `
    <div class="card-body">
      <table class="table table-bordered table-hover chapter-details">
        ${tableHeader}
        <tbody>${rowContent}</tbody>
      </table>
    </div>`;
    
    // Topic Section Collapse
    const topicCard = document.createElement('div');
    topicCard.className = 'card card-border mt-3';

    const topicHeader = document.createElement('div');
    topicHeader.className = 'card-header';
    topicHeader.innerHTML = `
      <h5 class="mb-0">
        <button class="btn btn-link" data-toggle="collapse" data-target="#collapse-topic-${chapterId}" aria-expanded="true">
          <h5 style="padding-bottom:0px;color:#43a2ad;">Topic</h5>
        </button>
      </h5>
    `;

    const topicCollapse = document.createElement('div');
    topicCollapse.className = 'collapse show';
    topicCollapse.id = `collapse-topic-${chapterId}`;
    topicCollapse.appendChild(lessonPlanAndStaffResponseUI.createTopicSection(topics, lessonPlan));

    topicCard.appendChild(topicHeader);
    topicCard.appendChild(topicCollapse);

    collapse.querySelector('.card-body').appendChild(topicCard);

    card.appendChild(header);
    card.appendChild(collapse);
    lessonPlanAndStaffResponseUI.globalChapterCounter = chapterCounter;
    return card;
  },

  renderLessonPlan : function(chapterList = [], chapterCounter = 0, lessonPlan = true) {
    const container = document.getElementById('chapter-details-div');
    const detailsDiv = document.createElement('div');
    detailsDiv.id = 'chapter-container';

    lessonPlanAndStaffResponseUI.globalChapterCounter = chapterCounter;

    container.querySelectorAll('#chapter-container').forEach(el => el.remove());

    if (chapterList.length !== 0) {
      chapterList.forEach(chapter => {
        detailsDiv.appendChild(lessonPlanAndStaffResponseUI.createChapterBox(chapter, chapter.chapterTopicDetails || [], lessonPlan));
      });
      $('.no-chapter').hide(); // Hide the no-chapter message
      $('#add-lesson-Plan').css('display','flex');
    } else {
      $('.no-chapter').show(); // Show the no-chapter message
      $('#add-lesson-Plan').css('display','none');
    }
  
    container.appendChild(detailsDiv);
  },
  addNewChapter: function (){
      $('#chapter-container').append(lessonPlanAndStaffResponseUI.createChapterBox());
      initDateWithYearRange("-1:+1");
      if (document.querySelectorAll('#chapter-container .card.card-border').length !== 0) {
        $('.no-chapter').hide();
        $('#add-lesson-Plan').css('display','flex');
      }
    }

  
}

