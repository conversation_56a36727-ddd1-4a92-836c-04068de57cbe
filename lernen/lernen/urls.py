"""lernen URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.urls import include
from django.conf.urls import handler403, handler404, handler500
from core.views import *

urlpatterns = [
    # path('admin/', admin.site.urls),
    path('', include('home.urls')),
    path('website/', include('website.urls')),
    path('website/2.0/', include('newwebsite.urls')),
    path('core/', include('core.urls')),
    path('<institute_unique_code>/store-v1/', include('store.urls')),
    path('<institute_unique_code>/fees/', include('fees.urls')),
    path('<institute_unique_code>/admission/', include('admission.urls')),
    path('<institute_unique_code>/assessment/', include('online_assessment.urls')),
    path('<institute_unique_code>/lesson-plan/', include('lesson_plan.urls')),
    path('<institute_unique_code>/study-tracker/', include('study_tracker.urls')),
    path('<institute_unique_code>/staffportal/', include('staffportal.urls')),
    path('<institute_unique_code>/adminportal/', include('adminportal.urls')),
    path('<institute_unique_code>/transport/', include('transport.urls')),
    path('<institute_unique_code>/courses/', include('courses.urls')),
    path('<institute_unique_code>/examination/', include('examination.urls')),
    path('<institute_unique_code>/attendance/', include('attendance.urls')),
    path('<institute_unique_code>/staff-management/', include('staffmanagement.urls')),
    path('<institute_unique_code>/income-expense/', include('income_expense.urls')),
    path('<institute_unique_code>/student-registration/', include('student_registration.urls')),
    path('<institute_unique_code>/user-management/', include('user_management.urls')),
    path('<institute_unique_code>/salary-management/', include('salary_management.urls')),
    path('<institute_unique_code>/lecture-management/', include('lecturemanagement.urls')),
    path('<institute_unique_code>/homework-management/', include('homeworkmanagement.urls')),
    path('<institute_unique_code>/timetable-management/', include('timetable_management.urls')),
    path('<institute_unique_code>/noticeboard/', include('noticeboard.urls')),
    path('<institute_unique_code>/studentportal/', include('studentportal.urls')),
    path('organisation-portal/', include('organisation_portal.urls')),
    path('institute-onboarding/', include('instituteonboarding.urls')),
    path('reset-password/<user_id>/<token_id>/', include('reset_password.urls')),
    path('<institute_unique_code>/student-management/', include('studentmanagement.urls')),
    path('<institute_unique_code>/audit-logs/', include('auditlogs.urls')),
    path('<institute_unique_code>/communication/', include('communication.urls')),
    path('<institute_unique_code>/staff-attendance/', include('staff_attendance.urls')),
    path('<institute_unique_code>/holiday-calendar/', include('holiday_calendar.urls')),
    path('<institute_unique_code>/inventory/', include('inventory.urls')),
    path('<institute_unique_code>/frontdesk/', include('frontdesk.urls')),
    path('<institute_unique_code>/mobileapp-management/', include('mobileapp_management.urls')),
    path('<institute_unique_code>/student-diary/', include('diary.urls')),
    path('<institute_unique_code>/institute-management/', include('institute_management.urls')),
    path('<institute_unique_code>/leave-management/', include('leave_management.urls')),
    path('<institute_unique_code>/library-management/', include('library_management.urls')),
    path('<institute_unique_code>/complaint-box/', include('complaint_box.urls')),
    path('<institute_unique_code>/staff-diary/', include('staff_diary.urls')),
    path('<institute_unique_code>/parents-appointment/',include('parents_appointment.urls')),
    path('<institute_unique_code>/visitors-desk/',include('visitors_desk.urls')),
    path('<institute_unique_code>/visitors-form/', include('visitors_form.urls')),
    path('<institute_unique_code>/admission-enquiry-form/', include('enquiry_form.urls')),
    path('<institute_unique_code>/hostel-management/', include('hostel_management.urls')),
    path('<institute_unique_code>/student-finance/', include('student_finance.urls'))


]

handler403 = error_403
handler404 = error_404
handler500 = error_500
