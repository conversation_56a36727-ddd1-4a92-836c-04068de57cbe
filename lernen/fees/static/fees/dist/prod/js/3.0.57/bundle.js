var DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-discount-assignment";
var DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID = "select-discount-view";

function registerDiscountTabs() {
  $("#"+DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
      var displayName = academicSessionHandler.getSelectedSessionDisplayName()
      $('#select-discount-assignment').addClass('active');
      $('#select-discount-view').removeClass('active');
      $("#academic-year-display").text(displayName);
      $("#student-discount-assign").html("");
      $("#discount-assign-student-search-result").html("");
      discountAssignment.loadDiscountAssignmentPage();
  });

  $("#"+DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
    var displayName = academicSessionHandler.getSelectedSessionDisplayName()
    $('#select-discount-assignment').removeClass('active');
    $('.discount-id').prop( "checked", false );
    $('#select-discount-view').addClass('active');
    $("#academic-year-display").text(displayName);
    $("#student-discount-view").html("");
    initSelect2("Discounts");
    bindSearchStudentDiscountViewEvent();
    academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession);
    applyDiscountSearchFilter();
    discountAssignment.loadDiscountViewPage();
  });
}

function bindSearchStudentDiscountViewEvent() {
  $('#discount-view-search-student').on('click', function () {
      bindSearchStudentDiscountView();
  });

  $("#discount-view-search-student-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        bindSearchStudentDiscountView();
    }
  });

}

function bindSearchStudentDiscountView() {
  var sessionId = academicSessionHandler.getSelectedSessionId();
   var search_text = $("#discount-view-search-student-text").val();
   // var discountIds = "";
   // var count = 0;
   // $(".discount-dropdown-div").each(function() {
   //   if($(this).find('.discount-id:checked').length > 0) {
   //     if(count == 0) {
   //       discountIds += $(this).find('.discount-id:checked').attr('id').toString();
   //     }
   //     else {
   //       discountIds += "," + $(this).find('.discount-id:checked').attr('id').toString();
   //     }
   //     count++;
   //   }
   // });

   var discountIds =  $("#discounts").val();
   discountIds = discountIds.join();

   ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/" + sessionId + "?searchText=" + search_text+ "&discountIds=" + discountIds, function(data) {
     $("#student-discount-view").html(data);
 });
}

function bindSearchStudentDiscountAssignmentEvent() {
  var resultArea = "#discount-assign-student-search-result";
  $('#discount-assign-search-student').on('click', function () {
       discountAssignment.doneStudentSearchTyping(resultArea);
  });
  $("#discount-assign-search-student-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        discountAssignment.doneStudentSearchTyping(resultArea);
    }
  });

  liveSearchHandler.bindEvent('#discount-assign-search-student-text',resultArea, discountAssignment.doneStudentSearchTyping);
}

var discountAssignment = {
      dataCache : {},

      doneStudentSearchTyping: function(resultArea) {
          var searchText = $('#discount-assign-search-student-text').val().trim();
          var sessionId = academicSessionHandler.getSelectedSessionId();
          studentLiveSearchEvent(sessionId, searchText, resultArea, loadStudentDiscountAssignment);
      },

      changeSession: function() {
          var displayName = academicSessionHandler.getSelectedSessionDisplayName();
          var selectedAssignOption = $(".discount-options.active").attr("id");

          if(selectedAssignOption == DISCOUNT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
              $("#academic-year-display").text(displayName);
              $("#discount-assign-search-student-text").val("");
              $("#student-discount-assign").html("");
              $("#discount-assign-student-search-result").html("");
              discountAssignment.loadDiscountAssignmentPage();
          }
          else if(selectedAssignOption == DISCOUNT_VIEW_SCREEN_SELECTION_BUTTON_ID){
              $("#academic-year-display").text(displayName);
              $('.discount-id').prop( "checked", false );
              $("#discount-view-search-student-text").val("");
              $("#student-discount-view").html("");
              applyDiscountSearchFilter();
              discountAssignment.loadDiscountViewPage();
          }

      },

      resetDataCache : function () {
          discountAssignment.dataCache = {}
      },

      loadDiscountAssignmentPage : function (){
          ajaxClient.get("/fees/discount-assignment", function(data) {
              $("#main-content").html(data);
              academicSessionHandler.bindSessionChangeEvent(discountAssignment.changeSession);
              $("#discount-assignment-screen").attr("style", "display:block");
              $("#discount-view-screen").attr("style", "display:none");
              registerDiscountTabs();
              bindSearchStudentDiscountAssignmentEvent();
          });
      },

      loadDiscountViewPage : function () {
        var sessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/fees/get-student-fee-discount-list/" + sessionId, function(data) {
          $("#student-discount-view").html(data);

          $("#student-discount-view-search-tab").attr("style", "display:block");
          $("#discount-assignment-screen").attr("style", "display:none");
          $("#discount-view-screen").attr("style", "display:block");
      });
    },

    loadFeeDiscountStructures : function (studentId) {
      var sessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/get-student-discount-assignment-details/" +studentId +"/"+ sessionId, function(data) {
        $("#view-student-discount-assignment-details-modal-container").html(data);
        $("#view-student-assigned-discounts-modal").modal("toggle");
      });
    },

    viewFeeDiscountStructure : function (structure_id, assigned_structure) {
        discount_structure_list = []
        if(assigned_structure){
            discount_structure_list = discountAssignment.dataCache.assignedDiscounts;
        }else{
          discount_structure_list = discountAssignment.dataCache.unassignedDiscounts;
        }

        var requestedDiscountStructure = null
        for(var i = 0; i < discount_structure_list.length; i++){
          if(structure_id == discount_structure_list[i].feeDiscountMetadata.discountStructureId){
              requestedDiscountStructure = discount_structure_list[i]
              break;
          }
        }

        if(requestedDiscountStructure == null){
          showErrorDialogBox("Invalid discount structure");
          return
        }

        discountAssignment.displayDiscountStructure(requestedDiscountStructure, assigned_structure);

    },

    displayDiscountStructure : function (requestedDiscountStructure, assigned_structure) {

      $("#view-discount-modal").find("#view-discount-title").text(requestedDiscountStructure.feeDiscountMetadata.name);

      var feeIdFeeHeadStructure = null
      if(assigned_structure){
        var tableData = ""
        for(var i = 0; i < requestedDiscountStructure.feeIdDiscountAssignmentDetailsList.length; i++){
            var feeIdDiscountAssignmentDetails = requestedDiscountStructure.feeIdDiscountAssignmentDetailsList[i];
            var feeHeadRows = "";
            for(var j = 0; j < feeIdDiscountAssignmentDetails.feeHeadDiscountAssignmentDetailsList.length; j++){
              var feeHeadDiscountAssignmentDetails = feeIdDiscountAssignmentDetails.feeHeadDiscountAssignmentDetailsList[j]

              var amount = feeHeadDiscountAssignmentDetails.amount;
              if(feeHeadDiscountAssignmentDetails.appliedDiscountAmount != null){
                  amount = feeHeadDiscountAssignmentDetails.appliedDiscountAmount;
              }

              if(feeHeadDiscountAssignmentDetails.percent) {
                amount = amount +"%"
              }
              else {
                amount = amount +"/-"
              }

              feeHeadRows += discountAssignment.getDiscountFeeHeadRow(feeHeadDiscountAssignmentDetails.feeHeadConfiguration.feeHead, amount);
            }

            tableData += discountAssignment.getDiscountFeeRow(feeIdDiscountAssignmentDetails.feeConfigurationBasicInfo.feeName, feeHeadRows);
        }
        $("#view-discount-modal").find(".modal-body").html(tableData);
      }else{
        if(requestedDiscountStructure.feeDiscountEntityStructures == null || requestedDiscountStructure.feeDiscountEntityStructures.length == 0){
          return;
        }
        //Assuming institute entity always
        var feeIdFeeHeadStructure = requestedDiscountStructure.feeDiscountEntityStructures[0].feeIdDiscountStructures;
        var tableData = ""
        for(var i = 0; i < feeIdFeeHeadStructure.length; i++){
            var feeIdDiscountStructure = feeIdFeeHeadStructure[i];
            var feeHeadRows = "";
            for(var j = 0; j < feeIdDiscountStructure.feeHeadDiscountStructures.length; j++){
              var feeHeadDiscountStructure = feeIdDiscountStructure.feeHeadDiscountStructures[j]
              var amount = feeHeadDiscountStructure.amount
              if(feeHeadDiscountStructure.percent) {
                amount = amount +"%"
              }
              else {
                amount = amount +"/-"
              }
              feeHeadRows += discountAssignment.getDiscountFeeHeadRow(feeHeadDiscountStructure.feeHeadConfiguration.feeHead,amount);
            }

            tableData += discountAssignment.getDiscountFeeRow(feeIdDiscountStructure.feeConfigurationBasicInfo.feeName, feeHeadRows);
        }
        $("#view-discount-modal").find(".modal-body").html(tableData);
      }

      $("#view-discount-modal").modal("toggle");
    },

    getDiscountFeeHeadRow : function (feeHeadName, feeHeadAmount) {
       return "<tr> <td> "+ feeHeadName +" </td> <td>"+feeHeadAmount+"</td> </tr>"
    },

    getDiscountFeeRow : function (feeName, feeHeadRows) {
      return "<div class=\"fee-structure\"> <div class=\"card text-center card-border\"> <div class=\"card-body\"> <h5 class=\"card-title\"> <strong>"+feeName+"</strong> </h5> <div style=\"padding-left:15%; padding-right:15%;\"> <table class=\"table table-bordered\"> <thead> <tr> <th scope=\"col\" class=\"w-50\">Fee Head</th> <th scope=\"col\">Amount</th>  </tr> </thead> <tbody class=\"fee-structure-body\"> "+feeHeadRows+" </tbody> </table> </div> </div> </div> </br> </div>";
    }
};

function loadStudentDiscountAssignment(studentId) {
  var sessionId = academicSessionHandler.getSelectedSessionId();
  ajaxClient.get("/fees/assigned-discounts/"+sessionId+"/"+studentId, function(data) {
      $("#student-discount-assign").html(data);
      bindDiscountInfoViewEvents();
      bindAssignNewDiscountEvent();
      addNewStudentDiscountAssignment();

      discountAssignment.resetDataCache();
      discountAssignment.dataCache.studentId = studentId;
      var assignedDiscounts = readJson("#student-fee-discount-assigned-structures");
      var unassignedDiscounts = readJson("#student-fee-discount-unassigned-structures");
      discountAssignment.dataCache.assignedDiscounts = assignedDiscounts;
      discountAssignment.dataCache.unassignedDiscounts = unassignedDiscounts;
      instantDiscountStructure.initDataCache();
  });
}

function applyDiscountSearchFilter() {
  $("#apply-discount-search-filter").on('click', function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var search_text = $("#discount-view-search-student-text").val();
    var discountIds =  $("#discounts").val();
    discountIds = discountIds.join();
  // var count = 0;
  //   $(".discount-dropdown-div").each(function() {
  //     if($(this).find('.discount-id:checked').length > 0) {
  //       if(count == 0) {
  //         discountIds += $(this).find('.discount-id:checked').attr('id').toString();
  //       }
  //       else {
  //         discountIds += "," + $(this).find('.discount-id:checked').attr('id').toString();
  //       }
  //       count++;
  //     }
  // });
  ajaxClient.get("/fees/get-student-fee-discount-list-by-search-text/" + sessionId + "?searchText=" + search_text+ "&discountIds=" + discountIds, function(data) {
      $("#student-discount-view").html(data);
  });
  });
}

function returnToAssignedDiscountDisplay() {
  $("#top-academic-session-bar").attr("style", "display:block");
  $(".student-discount-assign-tab").attr("style", "display:none");
  $("#student-assigned-discount-list").attr("style", "display:block");
  $("#student-discount-assign-search-tab").attr("style", "display:block");
}

function bindAssignNewDiscountEvent() {
  $("#student-assign-new-discount-button").on('click', function () {
      assignNewDiscountDisplay();
  })
}
function assignNewDiscountDisplay() {
  $("#top-academic-session-bar").attr("style", "display:none");
  $(".student-discount-assign-tab").attr("style", "display:none");
  $("#student-new-discount-assignment-tab").attr("style", "display:block");
  bindDiscountInfoViewEvents();
}

function addNewStudentDiscountAssignment(){
  $("#student-new-discount-assign-confirm").on('click', function () {
      $("#student-new-discount-assign-confirm-modal").modal('toggle');

      var studentId = $("#assign-new-discount-student-id").val();
      var currentSessionId = academicSessionHandler.getSelectedSessionId();
      var discountAssignmentInfo = getDiscountAssignmentInfo(studentId,currentSessionId);
      ajaxClient.post("/fees/add-discount-assignment/"+currentSessionId,{'discountAssignmentInfo':JSON.stringify(discountAssignmentInfo)}, function(data){
          $("#discount-assignment-status-modal-container").html(data);
          $("#discount-assignment-status-modal").modal('toggle');
          returnToAssignedDiscountDisplay();
          loadStudentDiscountAssignment(studentId);
     });
  });
}

function getDiscountAssignmentInfo(studentId,academicSessionId) {
  var discounts = [];
  $("tr.discount-row").each(function() {
      if(!$(this).find('.discount-assignment-enable').is(":checked")) {
          return;
      }
      var discountId = $(this).attr("id");
      discounts.push(discountId);
  });
  return {'studentIds':[studentId],'discountStructureIds':discounts};
}

function confirmDeleteDiscountAssignment(structure_id){
    $('#delete\\.discount-assignment-id').val(structure_id);
    $("#delete-discount-assignment-info-modal").modal('toggle');
}

function deleteDiscountAssignment(){
  $("#delete-discount-assignment-info-modal").modal('toggle');
  var currentSessionId = academicSessionHandler.getSelectedSessionId();
  var discountId = $('#delete\\.discount-assignment-id').val();
  var studentId = $("#fee-assign-view-student-id").text().trim();
  var deleteDiscountAssignmentInfo = {'academicSessionId' : currentSessionId, 'studentId' : studentId, 'discountId': discountId}
  ajaxClient.post("/fees/delete-discount-assignment",{'deleteDiscountAssignmentInfo':JSON.stringify(deleteDiscountAssignmentInfo)}, function(data){
      $("#discount-assignment-status-modal-container").html(data);
      $("#discount-assignment-status-modal").modal('toggle');
      returnToAssignedDiscountDisplay();
      loadStudentDiscountAssignment(studentId);
 });
}

var instantDiscountStructure = {

  dataCache : {},
  INSTITUTE_ENTITY : "institute",

  initDataCache: function () {
    var feeData = readJson('#fee-data');
    var standardsMap = {};
    var feeMap = {};
    var feeHeadMap = {};
    var feeIdFeeHeadSelectOptionData = {};
    var feeConfigurationBasicInfoList = feeData.feeConfigurationBasicInfoList;
    var feeHeadConfigurationResponseList = feeData.feeHeadConfigurationResponseList;
    for(var i = 0; i < feeConfigurationBasicInfoList.length; i++) {
      var feeConfigurationBasicInfo = feeConfigurationBasicInfoList[i]
      feeMap[feeConfigurationBasicInfo.feeId] = feeConfigurationBasicInfo;
      var feeHeadSelect =  instantDiscountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, "");
      feeIdFeeHeadSelectOptionData[feeConfigurationBasicInfo.feeId] = feeHeadSelect;
    }

    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++) {
      var feeHeadConfigurationResponse = feeHeadConfigurationResponseList[i]
      feeHeadMap[feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId] = feeHeadConfigurationResponse;
    }

    instantDiscountStructure.dataCache = {};
    instantDiscountStructure.dataCache.feeMap = feeMap;
    instantDiscountStructure.dataCache.feeHeadMap = feeHeadMap;
    instantDiscountStructure.dataCache.feeHeadConfigurationResponseList = feeHeadConfigurationResponseList;
    instantDiscountStructure.dataCache.configureNewStructure = {'institute' : {'selectedFees' : {}}, 'standard' : {'selectedStandards' : {}, selectedFees : {} }};
    instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData = feeIdFeeHeadSelectOptionData;
    // instantDiscountStructure.dataCache.discountAssignmentStructurePayload = null;
  },

  createFeeHeadSelectMenu : function (feeHeadConfigurationResponseList , selectedFeeHeadId) {
    var feeHeadSelect = "<select class=\"form-control form-control-sm mandatory-field fee-head\"> <option value=\"\">select</option>";
    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++){
      var feeHeadId = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHeadId;
      var feeHeadName = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHead;
      if(selectedFeeHeadId == feeHeadId){
        feeHeadSelect += "<option value=\""+feeHeadId+"\" selected>"+feeHeadName+"</option>";
      }else{
        feeHeadSelect += "<option value=\""+feeHeadId+"\">"+feeHeadName+"</option>";
      }
    }
    feeHeadSelect += "</select>"
    return feeHeadSelect;
  },

  loadNewFeeStructureConfigureModal : function() {
    $("#discount-structure-config-modal").modal('toggle');
    // Clear old dataType
    instantDiscountStructure.resetDiscountStructureModal();
    $("#discount-structure-config-modal").find(".modal-title").html("Assign Instant Discounts");
    $("#submit-fee-structure").html("Assign Instant Discounts");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");
    // Clear the older values
    instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees = {};
    instantDiscountStructure.dataCache.selectedStructureId = null;

    instantDiscountStructure.populateSelectFeesDropdown(".institute-structure", instantDiscountStructure.INSTITUTE_ENTITY, instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);
  },

  resetDiscountStructureModal : function () {
    $("#institute-fee-structure-container").html("<p class=\"institute-add-structure-hint-text pt-7 pb-7\"> Select fees to add new discount structure</p>");
    $("#discount-structure-name").val("");
    $("#discount-structure-name").attr("disabled",false);
    $("#discount-structure-config-modal").find(".modal-footer").css("display","");

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = instantDiscountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    instantDiscountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  bindSelectFeesEvent : function (entityParentContainerId, entityId, selectedFees) {
    $(entityParentContainerId).find('.fee-select-option').on('click', function () {
      var feeId = $(this).attr("id").split("-select-")[0];
      var entityId = $(this).attr("id").split("-select-")[1];
      instantDiscountStructure.insertFeeDetailsCard(entityParentContainerId, entityId, feeId, selectedFees);
    });
  },

  bindStandardSelectEvent : function () {
    $('.class-fee-structure').find('.standard-select-option').on('click', function () {
      var standardId = $(this).attr("id").split("-select")[0];
      instantDiscountStructure.insertStandardDetailsCard(standardId);
    });
  },

  insertFeeDetailsCard : function (entityParentContainerId, entityId, feeId, selectedFees) {
    selectedFees[feeId] = true;
    $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:none;");

    var entityContainerId = instantDiscountStructure.createEntityContainerId(entityId);
    var feeContainerId = instantDiscountStructure.createFeeContainerId(entityId,feeId);
    var feeMap = instantDiscountStructure.dataCache.feeMap;
    var feeName = feeMap[feeId].feeName.toUpperCase();
    var feeDetailsCard = "<div class=\"fee-structure\"> <div class=\"card card-border text-center\" id=\""+feeContainerId+"\"> <p class=\"fee-id\" style=\"display:none;\">"+feeId+"</p> <div class=\"card-body\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-fee-structure-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h5 class=\"card-title\"> <strong>"+feeName+"</strong> </h5> <div style=\"padding-left:15%; padding-right:15%;\"> <table class=\"table table-borderless\"> <thead> <tr> <th scope=\"col\" class=\"w-50\">Fee Head</th> <th scope=\"col\">Amount</th> <th scope=\"col\">Percentage</th> <th scope=\"col\"></th> </tr> </thead> <tbody class=\"fee-structure-body\"> </tbody> </table> <button type=\"button\" class=\"btn btn-outline-secondary btn-sm add-fee-head-button\"> + Add More Fee Head</button> </div> </div> </div> </br> <div>";
    $("#"+entityContainerId).append(feeDetailsCard);
    instantDiscountStructure.bindFeeContainerEvents(entityParentContainerId, feeContainerId, entityId, feeId, selectedFees);
    instantDiscountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);

  },

  insertStandardDetailsCard : function (standardId) {
    instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId] = true;
    $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");
    var standardMap = instantDiscountStructure.dataCache.standardsMap;
    var standardName = standardMap[standardId].standardName.toUpperCase();
    var standardContainerId = instantDiscountStructure.getStandardContainerId(standardId);
    var standardDetailsCard = "<div id=\""+standardContainerId+"\" class=\"card card-border standard-fee-structure\"> <p class=\"standard-id\" style=\"display:none;\">"+standardId+"</p> <div class=\"card-header card-header-color\" id=\""+standardId+"-heading\" data-toggle=\"collapse\" data-target=\"#collapse-"+standardId+"\" aria-expanded=\"true\" aria-controls=\"collapse-"+standardId+"\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-standard-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h2 class=\"mb-0\"> <button class=\"btn btn-link\" type=\"button\"> <strong>"+standardName+"</strong> </button> </h2> </div> <div id=\"collapse-"+standardId+"\" class=\"collapse\" aria-labelledby=\""+standardId+"-heading\" data-parent=\"#class-fee-structure-config-accordion\"> <div class=\"card-body\"> <div id=\""+standardId+"-fee-structure-container\" style=\"text-align:center;\"> <p class=\"institute-add-structure-hint-text pt-7 pb-7 black-color\"> Select fees to add new fee structure</p> <!-- Next card here --> </div> <br /> <div class=\"fee-select-dropdown-container pt-3 pb-3\" style=\"float:right;\"> <div class=\"btn-group dropup\" style=\"width:150px;\"> <button type=\"button\" class=\"btn btn-info btn-sm dropdown-toggle\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"> Select Fees </button> <div class=\"dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right \" style=\"max-width:fit-content; max-height: 300px ; \"> </div> </div> </div> <br/> </div> </div> </div>";
    $("#class-fee-structure-config-accordion").append(standardDetailsCard);
    instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId] = {};
    instantDiscountStructure.populateSelectFeesDropdown("#"+standardContainerId, standardId, instantDiscountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
    // instantDiscountStructure.bindStandardContainerEvents(standardContainerId, standardId);
  },

  createEntityContainerId : function (entityId) {
    return entityId + "-" + "fee-structure-container";
  },

  createFeeContainerId : function (entityId, feeId) {
    return entityId + "-" + feeId;
  },

  bindFeeContainerEvents : function (entityParentContainerId, feeContainerId, entityId, feeId, selectedFees) {
    $("#"+feeContainerId).find(".add-fee-head-button").on('click', function () {
      instantDiscountStructure.insertFeeHeadRow(feeContainerId, feeId, null, 0, "");
    });

    $("#"+feeContainerId).find(".delete-fee-structure-row").on('click', function () {
      $(this).closest(".fee-structure").remove();
      delete selectedFees[feeId];
      if(isEmpty(selectedFees)){
        $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:block;");
      }
      instantDiscountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);
    });

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = instantDiscountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    instantDiscountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  getStandardContainerId : function (standardId) {
      return standardId + "-container";
  },

  bindStandardContainerEvents : function (standardContainerId, standardId) {

    $("#"+standardContainerId).find(".delete-standard-row").on('click', function () {
      $(this).closest(".standard-fee-structure").remove();
      delete instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId];
      if(isEmpty(instantDiscountStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;");
      }
    });

  },

  insertFeeHeadRow  :function (feeContainerId, feeId , selectedFeeHeadId, feeHeadAmount, checked) {
    var feeHeadRow = "";
    if(selectedFeeHeadId == null){
      var feeHeadSelectOptionData = instantDiscountStructure.dataCache.feeIdFeeHeadSelectOptionData[feeId];
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"> <span class=\"slider round\"></span> </label> </td><td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }else{
      var feeHeadConfigurationResponseList = instantDiscountStructure.dataCache.feeHeadConfigurationResponseList;
      var feeHeadSelectOptionData =  instantDiscountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, selectedFeeHeadId);
      if(checked) {
        checked = "checked";
      }
      else {
        checked = "";
      }
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\" value=\""+feeHeadAmount+"\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"" + checked + " /><span class=\"slider round\"></span> </label> </td> <td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }

    $("#"+feeContainerId).find(".fee-structure-body").append(feeHeadRow);
    instantDiscountStructure.bindFeeHeadRowEvents(feeContainerId);
  },

  bindFeeHeadRowEvents : function (feeContainerId) {
      var previousSelectedValue = "";
      $("#"+feeContainerId).find(".fee-head").on('focus', function () {
        // Store the current value on focus and on change
        previousSelectedValue = $(this).find(':selected').val().trim();
      })
      .change(function () {
          var feeHeadId = $(this).find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          var presenceCount = 0;
          $(this).closest('tbody.fee-structure-body').find("tr").each(function() {
            var existingFeeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
            if(existingFeeHeadId == ""){
              return;
            }else if(feeHeadId == existingFeeHeadId){
              presenceCount++;
            }
          });
          if(presenceCount > 1){
            showErrorDialogBox("Fee Head " + $(this).find(':selected').text() + " already selected. Please fill amount in that");
            $(this).val(previousSelectedValue);
          }
      });

      $("#"+feeContainerId).find(".delete-fee-head-row").on('click', function () {
        $(this).closest("tr").remove();
      });

      $("#"+feeContainerId).find(".fee-head-amount").change(function() {
        var isPercentage = $(this).parent().parent().find(".is-percentage-switch").is(':checked');
        if(isPercentage) {
          var percentage = $(this).val();
          if(percentage > 100 || percentage <= 0) {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).val("");
            return;
          }
        } else {
          var amount = $(this).val();
          if(amount <= 0 && amount != "") {
            showErrorDialogBox("Amount should be a positive number.");
            $(this).val("");
            return;
          }
        }
      });

      $("#"+feeContainerId).find(".is-percentage-switch").change(function() {
        if($(this).is(':checked')) {
          var percentage = $(this).parent().parent().parent().find(".fee-head-amount").val();
          if((percentage > 100 || percentage <= 0) && percentage != "") {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).parent().parent().parent().find(".fee-head-amount").val("");
            return;
          }
        }
      });
  },

  submitDiscountStructure : function () {
    var structureName = $("#discount-structure-name").val();
    if(structureName == "") {
      alert("Discount Detail is mandatory!");
      return;
    }
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    var structureId = $("#instant-discount-structure-id").text();
    if(instantDiscountStructure.dataCache.selectedStructureId != null && instantDiscountStructure.dataCache.selectedStructureId.trim() != "" ){
      structureId = instantDiscountStructure.dataCache.selectedStructureId;
    }

    var instituteFeeIdFeeHeadsList =  instantDiscountStructure.getFeeStructureData('#institute-fee-structure-container');

    if(Array.isArray(instituteFeeIdFeeHeadsList) && !instituteFeeIdFeeHeadsList.length) {
      return;
    }
    var instituteFeeAssignmentPayload = {'feeEntity' : 'INSTITUTE', 'feeIdFeeHeadsList' : instituteFeeIdFeeHeadsList};

    var entityFeeAssignmentPayloadList = [];
    entityFeeAssignmentPayloadList.push(instituteFeeAssignmentPayload);

    var studentId = $("#fee-assign-view-student-id").text().trim();

    // var metadata = {'title' : structureName};
    // var discountAssignmentStructurePayload = {'structureId': structureId, 'discountStructureType' : 'SYSTEM', 'metadata' : metadata, 'entityFeeAssignmentPayloads' : entityFeeAssignmentPayloadList};
    // instantDiscountStructure.createDiscountStructureTable();
    var metadata = {'title' : structureName};
    var discountAssignmentStructurePayload = [{'structureId': structureId, 'discountStructureType' : 'SYSTEM', 'metadata' : metadata, 'entityFeeAssignmentPayloads' : entityFeeAssignmentPayloadList}];
    // instantDiscountStructure.dataCache.discountAssignmentStructurePayload = discountAssignmentStructurePayload;
    $("#discount-structure-config-modal").modal('toggle');
    var submitStructureURL = "/fees/add-student-discount-structure/"+academicSessionId+"/"+studentId;
    if(structureId != null && structureId != "" && structureId != undefined){
      submitStructureURL = "/fees/update-discount-structure/"+structureId+"/"+academicSessionId;
    }
    
    ajaxClient.post(submitStructureURL, {'discountAssignmentStructurePayload':JSON.stringify(discountAssignmentStructurePayload)}, function(data){
      $("#discount-structure-status-modal-container").html(data);
      $("#fee-structure-status-modal").modal('toggle');
      loadStudentDiscountAssignment(studentId);
    });
  },

  getFeeStructureData : function (entityFeeStructureContainerId) {
    var feeIdFeeHeadsList = [];
    $(entityFeeStructureContainerId).find(".fee-structure").each( function () {
      var feeId = $(this).find(".fee-id").text().trim();
      var feeHeadAmountList = [];
      var feeHeads = {};
      $(this).find('tbody.fee-structure-body').find('tr').each(function () {
          var feeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          if(feeHeadId in feeHeads){
            // Need to handle properly to display relevent error
            return;
          }
          var feeHeadAmount = $(this).find(".fee-head-amount").val();
          var isPercentage = $(this).find(".toggle-switch").is(':checked');
          if(feeHeadAmount === "" || feeHeadAmount === undefined) {
            alert("Amount cannot be empty");
            return [];
          }
          feeHeadAmountList.push({'feeHeadId' : feeHeadId, 'amount' : feeHeadAmount, 'isPercentage' : isPercentage});
      });
      if(feeHeadAmountList.length > 0){
        feeIdFeeHeadsList.push({'feeId' : feeId, 'feeHeadAmountList' : feeHeadAmountList});
      }
    });

    if(Array.isArray(feeIdFeeHeadsList) && !feeIdFeeHeadsList.length) {
      alert("Please select atleast one fees to create discount structure");
      return [];
    }

    return feeIdFeeHeadsList;
  },

  fillFeeDiscountStructureModal : function (ref, viewOnly) {

    $("#discount-structure-name").attr("disabled",false);
    // var selectedDiscountStructure = instantDiscountStructure.dataCache.discountAssignmentStructurePayload;
    var selectedDiscountStructure = readJson($(ref).parent().parent().find(".student-assigned-discount-structure"));
    instantDiscountStructure.resetDiscountStructureModal();
    $("#discount-structure-name").val(selectedDiscountStructure.feeDiscountMetadata.metadata.title);
    $("#discount-structure-config-modal").find(".modal-title").html("Update Instant Discounts");
    $("#submit-discount-structure").html("Update Instant Discounts");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");
    $("#instant-discount-structure-id").text(selectedDiscountStructure.feeDiscountMetadata.discountStructureId);

    instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    // var instituteEntitySelectedFees = {};
    // var standradEntitySelectedFees = {};
    // for(var i = 0; i < selectedDiscountStructure.feeIdDiscountAssignmentDetailsList.length; i++){
    //   var entityFeeAssignment = selectedDiscountStructure.feeIdDiscountAssignmentDetailsList[i];
    //     for(var j = 0; j < entityFeeAssignment.feeHeadDiscountAssignmentDetailsList.length; j++){
    //       var feeIdFeeHeadDetails = entityFeeAssignment.feeHeadDiscountAssignmentDetailsList[j];
    //       var feeId = feeIdFeeHeadDetails.feeId;
    //       instituteEntitySelectedFees[feeId] = feeIdFeeHeadDetails;
    //     }
    // }

    var instituteEntitySelectedFees = {};
    for(var i = 0; i < selectedDiscountStructure.feeIdDiscountAssignmentDetailsList.length; i++){
      var entityFeeAssignment = selectedDiscountStructure.feeIdDiscountAssignmentDetailsList[i];
      // for(var j = 0; j < entityFeeAssignment.feeHeadDiscountAssignmentDetailsList.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment;
          var feeId = entityFeeAssignment.feeConfigurationBasicInfo.feeId;
          instituteEntitySelectedFees[feeId] = feeIdFeeHeadDetails;
      // }
    }


    // for(feeId in instituteEntitySelectedFees){
    //     instantDiscountStructure.insertFeeDetailsCard(".institute-structure", instantDiscountStructure.INSTITUTE_ENTITY, feeId, instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);
    //     var feeContainerId = instantDiscountStructure.createFeeContainerId(instantDiscountStructure.INSTITUTE_ENTITY,feeId);
    //     for(var i = 0; i < instituteEntitySelectedFees[feeId].feeHeadAmountList.length; i++){
    //       var feeHeadId = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].feeHeadId;
    //       var feeHeadAmount = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].amount;
    //       var checked = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].percent;
    //       instantDiscountStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount, checked);
    //     }
    // }
    for(feeId in instituteEntitySelectedFees){
        instantDiscountStructure.insertFeeDetailsCard(".institute-structure", instantDiscountStructure.INSTITUTE_ENTITY, feeId, instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);
        var feeContainerId = instantDiscountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,feeId);
        for(var i = 0; i < instituteEntitySelectedFees[feeId].feeHeadDiscountAssignmentDetailsList.length; i++){
          var feeHeadId = instituteEntitySelectedFees[feeId].feeHeadDiscountAssignmentDetailsList[i].feeHeadConfiguration.feeHeadId;
          var feeHeadAmount = instituteEntitySelectedFees[feeId].feeHeadDiscountAssignmentDetailsList[i].amount;
          var checked = instituteEntitySelectedFees[feeId].feeHeadDiscountAssignmentDetailsList[i].percent;
          instantDiscountStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount, checked);
        }
    }
    instantDiscountStructure.populateSelectFeesDropdown(".institute-structure", instantDiscountStructure.INSTITUTE_ENTITY, instantDiscountStructure.dataCache.configureNewStructure.institute.selectedFees);
    if(viewOnly){
        $(".standard-select-dropdown-container").css("display","none");
        $(".fee-select-dropdown-container").css("display","none");
        $(".add-fee-head-button").css("display","none");
        $(".fee-head").attr("disabled","true");
        $(".fee-head-amount").attr("disabled","true");
        $(".toggle-switch").attr("disabled","true");
        $(".delete-fee-head-row").remove();
        $(".delete-fee-structure-row").remove();
        $(".delete-standard-row").remove();
        $("#discount-structure-config-modal").find(".modal-title").html("View Instant Discounts");
        $("#discount-structure-name").attr("disabled",true);
        $("#fee-structure-types").attr("disabled",true);
        $("#discount-structure-config-modal").find(".modal-footer").css("display","none");
    }
    $("#discount-structure-config-modal").modal('toggle');
  },

};

var CLASS_ASSIGNED_FEE_LIST = "class-assigned-fees-list";
var STUDENT_ASSIGNED_FEE_LIST = "student-assigned-fees-list";
// var CLASS_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-class-assignment";
var STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-student-assignment";
var BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-bulk-student-assignment";
var FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID = "select-fee-structure-assignment"
var CLASS_ENTITY = "CLASS";
var STUDENT_ENTITY = "STUDENT";

var feeAssignment = {

    loadMainScreen: function () {
        ajaxClient.get("/fees/fee-assignment", function(data) {
            $("#main-content").html(data);
            registerFeeAssignmentTabs();
            academicSessionHandler.bindSessionChangeEvent(feeAssignment.changeSession);
            // bindClassAssignmentScreenEvents();
            addStudentNewFeeAssignmentCallBack();
            bindSearchStudentFeeAssignmentEvent();
            bindAssignmentFeeSelectionEvents();
        });
    },

    doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#fee-assign-search-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO";
      studentLiveSearchEvent(sessionId, searchText, resultArea, loadStudentFeesAssignment, status);
    },

    changeSession: function() {
          var academicSession =  academicSessionHandler.getSelectedSession();
          $("#academic-year-display").text(academicSession.displayName);
          var selectedAssignOption = $(".fee-assign-options.active").attr("id");

          // if(selectedAssignOption == CLASS_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
          //     loadFeeAssignment.loadClassScreen();
          // }
          // else
          if(selectedAssignOption == STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
              loadFeeAssignment.loadStudentScreen()
          }
          else if(selectedAssignOption == BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID){
              loadFeeAssignment.loadBulkStudentScreen();
          }
          else if (selectedAssignOption == FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID) {
            loadFeeAssignment.loadFeeStructureAssignmentStudentScreen();
          }
    },

    resetToMainScreen: function () {
        feeAssignment.returnToClassMainScreen();
        feeAssignment.returnToStudentMainScreen();
        feeAssignment.returnToBulkStudentMainScreen();
    },

    returnToClassMainScreen: function(){
        $(".class-fee-assign-tab").attr("style", "display:none");
        $("#class-fee-assign-main-tab").attr("style", "display:block");
        clearMandatoryFieldsErrorDisplay();
    },

    returnToStudentMainScreen: function(){
        $(".student-fee-assign-tab").attr("style", "display:none");
        $("#student-fee-assign-main-tab").attr("style", "display:block");
        clearMandatoryFieldsErrorDisplay();
    },

    returnToBulkStudentMainScreen: function(){
        $("#searched-student-list").html("");
        $("#bulk-fee-assign-class").val("");
        $(".bulk-fee-assign-tab").attr("style", "display:none");
        $("#bulk-fee-assign-main-tab").attr("style", "display:block");
        clearMandatoryFieldsErrorDisplay();
    },
}



function populateUnassignedFees(status) {
  var assignedFeesJson = $("#assigned-fee-list").text().trim();
  var assignedFees = JSON.parse(assignedFeesJson);

  var assignedFeeIds = {};
  for( var j = 0 ; j < assignedFees.assigned_fees.length ; j++){
      assignedFeeIds[assignedFees.assigned_fees[j].feeConfigurationResponse.feeConfigurationBasicInfo.feeId] = true
  }


  var sessionAuthorizedFeeAssignmentsJson = $("#session-authorized-fee-assignments").text().trim();
  var sessionAuthorizedFeeAssignments = JSON.parse(sessionAuthorizedFeeAssignmentsJson);

  var regularFees = [];
  var onetimeFees = [];
  var specialFees = [];
  for( var j = 0 ; j < sessionAuthorizedFeeAssignments.authorized_fee_assignments.length ; j++){
      // Filtering out enrollement fees for ENROLMENT_PENDING students
      if(status === "ENROLMENT_PENDING") {
        var allowPendingEnrollment = sessionAuthorizedFeeAssignments.authorized_fee_assignments[j].feeConfigurationResponse.feeConfigurationBasicInfo.allowPendingEnrollment;
        if(allowPendingEnrollment == null || !allowPendingEnrollment) {
          continue;
        }
      }
      if(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j].feeConfigurationResponse.feeConfigurationBasicInfo.feeId in assignedFeeIds){
          continue;
      }

      if(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j].feeConfigurationResponse.feeConfigurationBasicInfo.feeType == "REGULAR"){
          regularFees.push(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j]);
      }
      else if(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j].feeConfigurationResponse.feeConfigurationBasicInfo.feeType == "ONE_TIME"){
          onetimeFees.push(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j]);
      }
      else if(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j].feeConfigurationResponse.feeConfigurationBasicInfo.feeType == "SPECIAL"){
          specialFees.push(sessionAuthorizedFeeAssignments.authorized_fee_assignments[j]);
      }
  }

  var options = '<option selected value=""> </option>';
  for( var i = 0; i < regularFees.length; i++) {
        options += '<option value="'+regularFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(regularFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+'</option>';
  }
  $("#regular-fee-select").empty().append(options);

  options = '<option selected value=""> </option>';
  for( var i = 0; i < onetimeFees.length; i++) {
        options += '<option value="'+onetimeFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(onetimeFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+'</option>';
  }
  $("#onetime-fee-select").empty().append(options);

  options = '<option selected value=""> </option>';
  for( var i = 0; i < specialFees.length; i++) {
        options += '<option value="'+specialFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId+'">'+titleCase(specialFees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeName)+'</option>';
  }
  $("#special-fee-select").empty().append(options);
}

function addClassNewFeeAssignmentCallBack(){
  $("#class-assign-new-fee-button").on('click', function () {
      var classId = $("#fee-assign-class").find(':selected').val().trim();
      if(classId == ""){
          showErrorDialogBox("Please select class for which you want to assign fees");
          return;
      }
      var className = $("#fee-assign-class").find(':selected').text().trim();
      $("#assign-new-fee-class-name").val(className);
      $("#assign-new-fee-class-id").val(classId);

      clearAuthorizedFeeHeads();
      populateUnassignedFees(null);

      $(".class-fee-assign-tab").attr("style", "display:none");
      $("#class-new-fee-assignment-tab").attr("style", "display:block");
  });
}

function addStudentNewFeeAssignmentCallBack(){
  $("#student-assign-new-fee-button").on('click', function () {
      var studentId = $("#fee-assign-view-student-id").text().trim();
      var studentName = $("#search-student-name").text().trim();
      var admissionNumber = $("#search-admission-number").text().trim();
      var className = $("#search-student-class").text().trim();
      var section = $("#search-student-class-section").text().trim();
      var status = $("#fee-assign-view-student-status").text().trim();

      $("#assign-new-fee-student-id").val(studentId);
      $("#assign-new-fee-admission-number").val(admissionNumber);
      $("#new-fee-view-student-name").text(studentName);
      $("#new-fee-view-admission-number").text(admissionNumber);
      $("#new-fee-view-student-class").text(className);
      $("#new-fee-view-student-class-section").text(section);

      clearAuthorizedFeeHeads();
      populateUnassignedFees(status);

      $(".student-fee-assign-tab").attr("style", "display:none");
      $("#student-new-fee-assignment-tab").attr("style", "display:block");
  });
}

function getBulkFeesAssignmentScreen() {
    $("#bulk-assignment-confirmation-modal").modal("toggle");
    // var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
    var studentIds = [];
    $("input.bulk-student-assignment-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-fee-assign-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    var sessionId = academicSessionHandler.getSelectedSessionId();

    var selectedClass = $("#bulk-fee-assign-class option:selected").text();
    ajaxClient.post("/fees/bulk-fee-assignment/status/"+sessionId,{'studentIds':JSON.stringify(studentIds)}, function(data){
        $("#bulk-new-fee-assignment-tab").html(data);
        $(".bulk-fee-assign-tab").attr("style", "display:none");
        $("#bulk-new-fee-assignment-tab").attr("style", "display:block");

        bindShowSelectedStudentModal();
        bindAssignmentFeeSelectionEvents();

        var studentText = $("#student-text-underline-id").text().trim();
        if(selectedClass.indexOf("Class") == -1){
          selectedClass = "Class " + selectedClass;
        }
        var bulkStudentText = studentText + " of " + selectedClass;
        $("#student-text-underline-id").text(bulkStudentText);

    });
}
function updateBulkAssignmentStudentConfirmModal() {
  var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
  $("#bulk-assignment-confirmation-modal-text").text("You have selected "+selectedStudentCount+ " students to assign fees at student level. Do you want to proceed?");
}

function bulkAssignmentStudentSelectCheckbox(studentSelectCheckbox) {
      var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
      $("#bulk-assign-selected-count").html(selectedStudentCount);
}


function selectBulkAssignmentStudentList(selectAllCheckbox) {
    if(selectAllCheckbox.checked){
      $(".bulk-student-assignment-checkbox").prop('checked', true);
      var selectedStudentCount = $('input.bulk-student-assignment-checkbox:checkbox:checked').length;
      $("#bulk-assign-selected-count").html(selectedStudentCount);
    }
    else{
      $(".bulk-student-assignment-checkbox").prop('checked', false);
        $("#bulk-assign-selected-count").html(0);
    }
}
//
// function bindClassAssignmentScreenEvents() {
//     addClassNewFeeAssignmentCallBack();
//     bindClassChangeEvent();
//     bindAssignmentFeeSelectionEvents();
// }

function bindAssignmentFeeSelectionEvents() {
    bindFeeTypeChangeEvent();
    bindAssignmentFeeChangeEvent();
    enableFeeAssignmentFeeHeadCallback();
}
var loadFeeAssignment = {
    // loadClassScreen: function() {
    //   var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    //   ajaxClient.get("/fees/class-assignment/"+academicSessionId, function(data) {
    //       $("#fee-assignment-screen").html(data);
    //       bindClassAssignmentScreenEvents();
    //   });
    // },
    loadStudentScreen: function() {
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/student-assignment/"+academicSessionId, function(data) {
          $("#fee-assignment-screen").html(data);
          addStudentNewFeeAssignmentCallBack();
          bindSearchStudentFeeAssignmentEvent();
          bindAssignmentFeeSelectionEvents();

      });
    },
    loadBulkStudentScreen: function() {
        ajaxClient.get("/fees/bulk-student-assignment", function(data) {
            $("#fee-assignment-screen").html(data);
            bindBulkFeeClassChangeEvent();
        });
    },

    loadFeeStructureAssignmentStudentScreen: function() {
        ajaxClient.get("/fees/fee-structure-change-home", function(data) {
            $("#fee-assignment-screen").html(data);
            loadFeeAssignment.bindPaymentSearchStudentEvent();
        });
    },

    bindPaymentSearchStudentEvent : function () {
      var resultArea = "#student-payment-search-result";
      $('#student-payment-search').on('click', function () {
           loadFeeAssignment.doneStudentSearchTyping(resultArea);
      });
      $("#student-payment-search-text").on('keyup', function (e) {
        if (e.keyCode == 13) {
            loadFeeAssignment.doneStudentSearchTyping(resultArea);
        }
      });
      liveSearchHandler.bindEvent('#student-payment-search-text', resultArea, loadFeeAssignment.doneStudentSearchTyping);
    },

    doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#student-payment-search-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED";
      loadFeeAssignment.studentLiveSearchEvent(sessionId, searchText, resultArea, loadFeeAssignment.loadStudentPaymentDetails, status);
    },

    studentLiveSearchEvent : function (sessionId, searchText, resultArea, triggerMethod, status){
      ajaxClient.get("/fees/student-live-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
          $(resultArea).html(data);
          studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
      });
    },

    loadStudentPaymentDetails : function(studentId) {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get( "/fees/student-details/"+academicSessionId+"/"+studentId, function(data) {
          $("#student-payment-screen").html(data);
      });
    },

    classChangeStudentPopUp : function () {
      $('#student-promotion-modal').modal('toggle');
      loadFeeAssignment.bindPromotingStandardChange();
    },

    bindPromotingStandardChange : function () {
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var studentJson = JSON.parse($("#student-details-json").text().trim());
        var studentId = studentJson.studentId;
        ajaxClient.get("/fees/fee-structure-configuration/" + academicSessionId + "/" + studentId, function(data) {
            $("#promotion-configuration-div").html(data);
        });
    },

    changeStudentClass : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var studentJson = JSON.parse($("#student-details-json").text().trim());
      var studentId = studentJson.studentId;

      var uuids = [];
      $("input.fee-structure-checkbox").each(function() {
        if($(this).is(":checked")) {
          var structureId = $(this).parent().find('.structure-id').text().trim();
          uuids.push(structureId);
        }
      });

      var uuids = { 'uuids' : uuids };

        $('#student-promotion-modal').modal('toggle');
      ajaxClient.post("/fees/fee-structure-change/" + academicSessionId + "/" + studentId, {'uuids' : JSON.stringify(uuids)}, function(data){
          $("#attendance-feed-status-modal-container").html(data);
          $("#fee-assignment-status-modal").modal({backdrop: 'static', keyboard: false});
      });

    },
}

function registerFeeAssignmentTabs() {
  // $("#"+CLASS_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
  //     var status = confirm("You will be taken to class level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
  //     if(!status){
  //       return;
  //     }
  //     loadFeeAssignment.loadClassScreen();
  // });

  $("#"+STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
      // var status = confirm("You will be taken to student level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
      // if(!status){
      //   return;
      // }
      loadFeeAssignment.loadStudentScreen();

  });

  $("#"+BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
      // var status = confirm("You will be taken to bulk student level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
      // if(!status){
      //   return;
      // }
      loadFeeAssignment.loadBulkStudentScreen();
  });

  $("#"+FEE_STRUCTURE_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
      // var status = confirm("You will be taken to bulk student level assignment and the unsaved data will be lost on current screen. Do you want to proceed?");
      // if(!status){
      //   return;
      // }
      loadFeeAssignment.loadFeeStructureAssignmentStudentScreen();
  });
}

function bindFeeTypeChangeEvent(){
  $("#fee-type-select").change(function() {
      var feeType = $(this).find(':selected').val().trim();
      $(".fee-select").attr("style", "display:none");
      $(".fee-select").removeClass('active');
      $(".fee-select").val("");

      if(feeType == "regular"){
          $("#regular-fee-select").attr("style", "display:block");
          $("#regular-fee-select").addClass('active');
      }
      else if(feeType == "onetime"){
          $("#onetime-fee-select").attr("style", "display:block");
          $("#onetime-fee-select").addClass('active');
      }
      else if(feeType == "special"){
          $("#special-fee-select").attr("style", "display:block");
          $("#special-fee-select").addClass('active');
      }
      clearAuthorizedFeeHeads();
  });
}

function clearAuthorizedFeeHeads() {
    $(".authorized-assignment-feeheads").attr("style", "display:none");
    $(".authorized-assignment-feeheads").removeClass("active");
    resetAuthorizedFeeHeads();
}

function resetAuthorizedFeeHeads() {
    $('.add-assignment-fee-head-row').find('.assignment-fee-head').prop("checked",false);
    $('.add-assignment-fee-head-row').find('.assignment-amount').val("");
    $('.add-assignment-fee-head-row').find('.assignment-amount').prop("readonly",true);
}

function displaySelectedAuthorizedFeeHeads(feeId) {
    $("#"+feeId+"-feeheads").addClass("active");
    $("#"+feeId+"-feeheads").attr("style", "display:block");
}
function bindAssignmentFeeChangeEvent() {
  $(".fee-select").change(function() {
      var feeId = $(this).find(':selected').val().trim();
      clearAuthorizedFeeHeads();
      displaySelectedAuthorizedFeeHeads(feeId);
  });
}

function bindClassChangeEvent() {
  $("#fee-assign-class").change(function() {
      var classId = $(this).find(':selected').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      loadEntityFeesAssignment(sessionId,CLASS_ENTITY,classId,CLASS_ASSIGNED_FEE_LIST);
  });
}

function bindBulkFeeClassChangeEvent() {
  $("#bulk-fee-assign-class").change(function() {
      var classId = $(this).find(':selected').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      searchStudentsInBulkAssignment(sessionId,classId);
  });
}

function bindShowSelectedStudentModal() {
  $('#selected-bulk-student-text').on("click",function(){
      $("#bulk-assignment-selected-students-modal").modal("toggle");
  });
}

function searchStudentsInBulkAssignment(sessionId,classId) {
  ajaxClient.get("/fees/class-students/"+sessionId+"/"+classId, function(data) {
      $("#searched-student-list").html(data);
  });
}

function bindSearchStudentFeeAssignmentEvent() {
  var resultArea = "#fee-assign-student-search-result";

  $('#fee-assign-search-student').on('click', function () {
       // loadStudentFeesAssignment();
       feeAssignment.doneStudentSearchTyping(resultArea);
  });
  $("#fee-assign-search-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        // loadStudentFeesAssignment();
        feeAssignment.doneStudentSearchTyping(resultArea);
    }
  });
  liveSearchHandler.bindEvent('#fee-assign-search-text',resultArea,feeAssignment.doneStudentSearchTyping);
}


function loadStudentFeesAssignment(studentId) {
  var sessionId = academicSessionHandler.getSelectedSessionId();
  loadEntityFeesAssignment(sessionId,STUDENT_ENTITY,studentId,STUDENT_ASSIGNED_FEE_LIST);
}

function loadEntityFeesAssignment(sessionId,entity,entityId,contentDiv) {
  entity = entity.trim();
  entityId = entityId.trim();
  if(sessionId == "" || entity == ""){
    showErrorDialogBox("Please select entity and session to assign fees.")
    return;
  }

  ajaxClient.get("/fees/v2/assigned-fees/"+sessionId+"/"+entity+"/"+entityId, function(data) {
      $("#"+contentDiv).html(data);

      bindViewFeeAssignmentEvent();
      bindUpdateFeeAssignmentEvent();
      bindDeleteFeeAssignmentEvent();
      if(entity == STUDENT_ENTITY){
        addStudentNewFeeAssignmentCallBack();
      }
  });
}




function enableFeeAssignmentFeeHeadCallback(){
  $('.assignment-fee-head').change(function() {
       if($(this).is(":checked")) {
          $(this).closest('tr').find('input.assignment-amount').prop("readonly", false);
       }
       else{
         $(this).closest('tr').find('input.assignment-amount').prop("readonly", true);
       }
   });
}

function addNewClassFeeAssignment(){
      var invalid = validateMandatoryFields($("#class-new-fee-assign-modal"));
      if(invalid){
        return;
      }
      $("#class-new-fee-assign-confirm-modal").modal('toggle');

      var classId = $("#assign-new-fee-class-id").val();
      var feeAssignmentInfo = getFeeAssignmentInfo(CLASS_ENTITY,[classId]);
      if(feeAssignmentInfo == null){
        return;
      }
      ajaxClient.post("/fees/add-fee-assignment",{'feeAssignmentInfo':JSON.stringify(feeAssignmentInfo)}, function(data){
          feeAssignment.resetToMainScreen();
          $("#fee-assignment-status-modal-container").html(data);
          $("#fee-assignment-status-modal").modal('toggle');
          var sessionId = academicSessionHandler.getSelectedSessionId();
          loadEntityFeesAssignment(sessionId,CLASS_ENTITY,classId,CLASS_ASSIGNED_FEE_LIST);
     });
}

function addNewStudentFeeAssignment(){
      $("#student-new-fee-assign-confirm-modal").modal('toggle');

      var studentId = $("#assign-new-fee-student-id").val();
      var admissionNumber = $("#assign-new-fee-admission-number").val();
      var feeAssignmentInfo = getFeeAssignmentInfo(STUDENT_ENTITY,[studentId]);
      if(feeAssignmentInfo == null){
        return;
      }
      ajaxClient.post("/fees/add-fee-assignment",{'feeAssignmentInfo':JSON.stringify(feeAssignmentInfo)}, function(data){

          feeAssignment.resetToMainScreen();
          $("#fee-assignment-status-modal-container").html(data);
          $("#fee-assignment-status-modal").modal('toggle');
          var sessionId = academicSessionHandler.getSelectedSessionId();
          loadEntityFeesAssignment(sessionId,STUDENT_ENTITY,studentId,STUDENT_ASSIGNED_FEE_LIST);
     });
}

function generateFeesChart(){
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      var studentId = $("#fee-assign-view-student-id").text().trim();
      var includeDueAmount = $("#due-amount").is(":checked");
      var includeCollectedAmount = $("#collected-amount").is(":checked");
      var includeDiscountedAmount = $("#discounted-amount").is(":checked");
      var includeDueAmountTillToday = $("#due-amount-till-today").is(":checked");
      var studentIds = [];
      studentIds.push(studentId);
      var studentPerPageValue = 1;
      $("#generate-fee-chart-modal").modal('toggle');
      window.open(baseURL + "/fees/generate-fee-chart?academic_session_id="+academicSessionId+"&student_Ids="+studentIds+"&student_per_page="+studentPerPageValue+"&due_amount="+includeDueAmount+"&collected_amount="+includeCollectedAmount+"&disounted_amount="+includeDiscountedAmount+"&due_amount_till_today="+includeDueAmountTillToday, '_blank');

}

function generateBulkFeesCharts(){
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      // var studentIds = getSelectedBulkStudentIds();
      var studentIds = [];
      $("input.bulk-student-assignment-checkbox").each(function() {
          if(!$(this).is(":checked")) {
              return;
          }
          var studentId = $(this).parent().find('p.bulk-fee-assign-student-id').first().text().trim();
          studentIds.push(studentId);
      });
      if(studentIds.length <= 0) {
        alert("Please select at least one student to geenrate fee chart.");
        return ;
      }
      var includeDueAmount = $("#bulk-due-amount").is(":checked");
      var includeCollectedAmount = $("#bulk-collected-amount").is(":checked");
      var includeDiscountedAmount = $("#bulk-discount-amount").is(":checked");
      var includeDueAmountTillToday = $("#bulk-due-amount-till-today").is(":checked");
      var studentPerPageValue = $("#student-per-page").val();
      if(studentPerPageValue < 1) {
        alert("Student Per Page Value Should Be greater than 0.");
        return ;
      }
      $("#generate-bulk-fee-chart-modal").modal('toggle');
      window.open(baseURL + "/fees/generate-fee-chart?academic_session_id="+academicSessionId+"&student_Ids="+studentIds+"&student_per_page="+studentPerPageValue+"&due_amount="+includeDueAmount+"&collected_amount="+includeCollectedAmount+"&disounted_amount="+includeDiscountedAmount+"&due_amount_till_today="+includeDueAmountTillToday, '_blank');

}

function addBulkStudentFeeAssignment(){
      $("#bulk-student-new-fee-assign-confirm-modal").modal('toggle');

      var studentIds = getSelectedBulkStudentIds();
      var feeAssignmentInfo = getFeeAssignmentInfo(STUDENT_ENTITY, studentIds);
      if(feeAssignmentInfo == null){
          return;
      }
      ajaxClient.post("/fees/add-fee-assignment",{'feeAssignmentInfo':JSON.stringify(feeAssignmentInfo)}, function(data){

          feeAssignment.resetToMainScreen();
          $("#fee-assignment-status-modal-container").html(data);
          $("#fee-assignment-status-modal").modal('toggle');
     });
}

function getSelectedBulkStudentIds() {
  var studentIds = [];
  $("tr.bulk-assignment-selected-student").each(function() {
      var studentId = $(this).attr("id");
      studentIds.push(studentId);
  });
  return studentIds;
}

function getFeeAssignmentInfo(entityName, entityIds) {
  var feeId = $('.fee-select.active').first().find(':selected').val().trim();
  if(feeId.trim() == ""){
     showErrorDialogBox("Please select one fees before proceeding for assignment.");
     return null;
  }
  var feeHeadAmounts = getAssignedFeeHeadAmounts();
  if(feeHeadAmounts.length == 0){
    showErrorDialogBox("Atleast one fee head must be assigned");
    return null;
  }
  return {'entityIds': entityIds, 'feeEntity' : entityName, 'feeId':feeId,'feeHeadAmounts':feeHeadAmounts};
}

// function getBulkFeeAssignmentInfo(feeEntity, entityIds) {
//   var feeId = $('.fee-select.active').first().find(':selected').val().trim();
//   var feeHeadAmounts = getAssignedFeeHeadAmounts();
//   return {'entityIds':entityIds, 'feeEntity' : feeEntity, 'feeId':feeId,'feeHeadAmounts':feeHeadAmounts};
// }

function getAssignedFeeHeadAmounts() {
    var feeHeadAmounts = [];
    $(".authorized-assignment-feeheads.active").find("tr.add-assignment-fee-head-row").each(function() {
        if($(this).find('.assignment-fee-head').length == 0 || !$(this).find('.assignment-fee-head').is(":checked")) {
            return;
        }
        var feeHeadId = $(this).attr("id");
        var amount = 0;
        if ($(this).find("input.assignment-amount").val() != "") {
            amount = parseFloat($(this).find("input.assignment-amount").val());
        }
        var feeHeadAmount = {}
        feeHeadAmount['feeHeadId'] = feeHeadId;
        feeHeadAmount['amount'] = amount;
        feeHeadAmounts.push(feeHeadAmount);
    });
    return feeHeadAmounts;
}



///////////////////////////////////////////////////// VIEW ////////////////////////////////

function bindViewFeeAssignmentEvent() {
    $('.view-assigned-fee').on('click', function () {
        var assignedFeesJson = $(this).parent().find('.assigned-fee-info').text().trim();
        var assignedFees = JSON.parse(assignedFeesJson);
        populateViewFeeAssignmentContent(assignedFees);
    });
}

function bindUpdateFeeAssignmentEvent() {
    $('.update-assigned-fee').off('click').on('click', function () {
        var assignedFeesJson = $(this).parent().find('.assigned-fee-info').text().trim();
        var assignedFees = JSON.parse(assignedFeesJson);

        var sessionAuthorizedFeeAssignmentsJson = $("#session-authorized-fee-assignments").text().trim();
        var sessionAuthorizedFeeAssignments = JSON.parse(sessionAuthorizedFeeAssignmentsJson).authorized_fee_assignments;

        var sessionAuthorizedFeeAssignment = null;
        for( var i = 0 ; i < sessionAuthorizedFeeAssignments.length; i++){
            if(sessionAuthorizedFeeAssignments[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId == assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeId){
                sessionAuthorizedFeeAssignment = sessionAuthorizedFeeAssignments[i];
                break;
            }
        }
        populateUpdateFeeAssignmentContent(assignedFees, sessionAuthorizedFeeAssignment);
    });

    $("#update-assigned-fee-amounts").off('click').on('click', function () {
      $("#update-assigned-fee-amounts-modal").modal('toggle');
    });
}


function updateAssignedFeeAmounts(){
  $("#update-assigned-fee-amounts-modal").modal('toggle');

  var sessionId = academicSessionHandler.getSelectedSessionId();
  var studentId = $("#fee-assign-view-student-id").text().trim();

  var feeIdFeeHeadAmountsList = getUpdateFeeAssignmentAmounts();
  var payload = {'studentId' : studentId , 'feeIdFeeHeadList' : feeIdFeeHeadAmountsList};
  ajaxClient.post("/fees/update-fee-assignment-amounts/"+sessionId,{'payload':JSON.stringify(payload)}, function(data){

      feeAssignment.resetToMainScreen();
      $("#fee-assignment-status-modal-container").html(data);
      $("#fee-assignment-status-modal").modal('toggle');

      loadEntityFeesAssignment(sessionId, STUDENT_ENTITY, studentId, STUDENT_ASSIGNED_FEE_LIST);
  });
}

function moduleExists(authorizedModules, moduleId) {
  for(var i = 0; i < authorizedModules.length; i++){
    if(authorizedModules[i].moduleId == moduleId){
      return true;
    }
  }
  return false;
}

function populateUpdateFeeAssignmentContent(assignedFees, sessionAuthorizedFeeAssignment) {
      var screenVal = $('#entity-type').text().trim();
      $('#update\\.assigned-fee-id').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeId);
      $('#update\\.assigned-fee-name').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeName);
      $('#update\\.assigned-fee-academic-session').val(assignedFees.feeConfigurationResponse.academicSession.displayName);
      $('#update\\.assigned-fee-description').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.description);
      if(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeType != 'ONE_TIME'){
        $('#update\\.assigned-fee-duration').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month + " to " + assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month);
      }else{
        $('#update\\.assigned-fee-duration-container').attr('style','display:none');
      }

      var assignedFeeHeadsMap = {};
      for(i = 0; i < assignedFees.feeHeadAmountDetailsList.length; i++){
        assignedFeeHeadsMap[assignedFees.feeHeadAmountDetailsList[i].feeHeadConfiguration.feeHeadId] = assignedFees.feeHeadAmountDetailsList[i];
      }

      var unassignedFeeHeadMap = {};
      for( var i=0; i < sessionAuthorizedFeeAssignment.authorizedFeeHeadAssignments.length; i++){
        if(!(sessionAuthorizedFeeAssignment.authorizedFeeHeadAssignments[i].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId in assignedFeeHeadsMap)){
          unassignedFeeHeadMap[sessionAuthorizedFeeAssignment.authorizedFeeHeadAssignments[i].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId] = sessionAuthorizedFeeAssignment.authorizedFeeHeadAssignments[i];
        }
      }

      var feeHeadRow = "";
      var i = 0;
      if(screenVal == STUDENT_ENTITY){
          var entityId = $('#fee-assign-view-student-id').text().trim();
          var admissionNumber = $('#fee-assign-search-text').val().trim();
          $('#update\\.assigned-entity-id').val(entityId);
          $('#update\\.assigned-admission-number').val(admissionNumber);
          for(var assignedFeeHeadId in assignedFeeHeadsMap){
            if(assignedFeeHeadsMap[assignedFeeHeadId].feeEntity == CLASS_ENTITY || !moduleExists(assignedFeeHeadsMap[assignedFeeHeadId].authorizedModules, "FEES")){
              feeHeadRow = feeHeadRow + "<tr class=\"fee-head-row non-editable-row\" id = \""+assignedFeeHeadId+"\"><td><div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\" onclick=\"return false;\" checked disabled><label class=\"form-check-label\"> "+assignedFeeHeadsMap[assignedFeeHeadId].feeHeadConfiguration.feeHead+" </label></div></td><td><input type=\"number\" class=\"form-control form-control-sm assignment-amount\" value=\""+assignedFeeHeadsMap[assignedFeeHeadId].amount+"\" readonly></td></tr>"
            } else{
              feeHeadRow = feeHeadRow + "<tr class=\"fee-head-row\" id = \""+assignedFeeHeadId+"\"><td><div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\" checked><label class=\"form-check-label\"> "+assignedFeeHeadsMap[assignedFeeHeadId].feeHeadConfiguration.feeHead+" </label></div></td><td><input type=\"number\" class=\"form-control form-control-sm assignment-amount\" value=\""+assignedFeeHeadsMap[assignedFeeHeadId].amount+"\"></td></tr>"
            }
          }
      } else{
          var entityId = $("#fee-assign-class").val();
          $('#update\\.assigned-entity-id').val(entityId)
          for(var assignedFeeHeadId in assignedFeeHeadsMap){
            if(!moduleExists(assignedFeeHeadsMap[assignedFeeHeadId].authorizedModules,"FEES")){
              feeHeadRow = feeHeadRow + "<tr class=\"fee-head-row non-editable-row\" id = \""+assignedFeeHeadId+"\"><td><div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\" onclick=\"return false;\" checked disabled><label class=\"form-check-label\"> "+assignedFeeHeadsMap[assignedFeeHeadId].feeHeadConfiguration.feeHead+" </label></div></td><td><input type=\"number\" class=\"form-control form-control-sm assignment-amount\" value=\""+assignedFeeHeadsMap[assignedFeeHeadId].amount+"\" readonly></td></tr>"
            } else{
              feeHeadRow = feeHeadRow + "<tr class=\"fee-head-row\" id = \""+assignedFeeHeadId+"\"><td><div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\" checked><label class=\"form-check-label\"> "+assignedFeeHeadsMap[assignedFeeHeadId].feeHeadConfiguration.feeHead+" </label></div></td><td><input type=\"number\" class=\"form-control form-control-sm assignment-amount\" value=\""+assignedFeeHeadsMap[assignedFeeHeadId].amount+"\"></td></tr>"
            }
          }
      }

      for(var unassignedFeeHeadId in unassignedFeeHeadMap){
        if(unassignedFeeHeadMap[unassignedFeeHeadId].module_ids.indexOf("FEES") < 0 ){
            feeHeadRow = feeHeadRow + "<tr class=\"fee-head-row non-editable-row\" id = \""+unassignedFeeHeadId+"\"><td><div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\" onclick=\"return false;\" checked disabled><label class=\"form-check-label\"> "+unassignedFeeHeadMap[unassignedFeeHeadId].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+" </label></div></td><td><input type=\"number\" class=\"form-control form-control-sm assignment-amount\" readonly></td></tr>"
         } else{
            feeHeadRow = feeHeadRow + "<tr class = \"fee-head-row\" id = \""+unassignedFeeHeadId+"\"><td> <div class=\"form-check\"><input type=\"checkbox\" class=\"form-check-input assignment-fee-head\"><label class=\"form-check-label\" for=\"fee-head\"> "+unassignedFeeHeadMap[unassignedFeeHeadId].feeHeadConfigurationResponse.feeHeadConfiguration.feeHead+" </label></div></td> <td> <input type=\"number\" class=\"form-control form-control-sm assignment-amount\" id=\"update.amount"+unassignedFeeHeadId+"\" readonly></td></tr>"
        }
      }

      $('#update\\.assigned-fee-head-amounts').html(feeHeadRow);
      enableFeeAssignmentFeeHeadCallback();
      $("#update-assigned-fee-modal").modal('toggle');
}

function updateFeeAssignment(){
  var screenVal = $('#entity-type').text().trim();
  $("#update-assigned-fee-modal").modal('toggle');


  var entityId = $("#update\\.assigned-entity-id").val();
  var admissionNumber = $("#update\\.assigned-admission-number").val();
  var feeAssignmentInfo = getUpdateFeeAssignmentInfo(screenVal,entityId);
  ajaxClient.post("/fees/update-fee-assignment",{'updateFeeAssignmentInfo':JSON.stringify(feeAssignmentInfo)}, function(data){

      feeAssignment.resetToMainScreen();
      $("#fee-assignment-status-modal-container").html(data);
      $("#fee-assignment-status-modal").modal('toggle');
      var sessionId = academicSessionHandler.getSelectedSessionId();
      if(screenVal == STUDENT_ENTITY){
        loadEntityFeesAssignment(sessionId, screenVal, entityId, STUDENT_ASSIGNED_FEE_LIST);
      }
      else {
        loadEntityFeesAssignment(sessionId, screenVal, entityId, CLASS_ASSIGNED_FEE_LIST);
      }
  });
}

function getUpdateFeeAssignmentInfo(entityName, entityId) {
  var feeId = $("#update\\.assigned-fee-id").val();
  var feeHeadAmounts = [];
  $("tr.fee-head-row").each(function() {
      if(!$(this).find('.assignment-fee-head').is(":checked") || $(this).hasClass("non-editable-row")) {
          return;
      }
      var feeHeadId = $(this).attr("id");
      var amount = 0;
      if ($(this).find("input.assignment-amount").val() != "") {
          amount = parseFloat($(this).find("input.assignment-amount").val());
      }
      var feeHeadAmount = {}
      feeHeadAmount['feeHeadId'] = feeHeadId;
      feeHeadAmount['amount'] = amount;
      feeHeadAmounts.push(feeHeadAmount);

  });
  return {'entityId':entityId,'feeEntity' : entityName,'feeId':feeId,'feeHeadAmounts':feeHeadAmounts};
}

function getUpdateFeeAssignmentAmounts() {
  var feeIdFeeHeadAmounts = {};
  $("tr.update-assigned-fee-amount-row").each(function() {
      if($(this).find("input.fee-row-assignment-amount").hasClass("read-only-value")) {
          return;
      }
      var feeIdFeeHeadId = $(this).attr("id").split("|");
      var feeId = feeIdFeeHeadId[0];
      var feeHeadId = feeIdFeeHeadId[1];

      var amount = 0;
      if ($(this).find("input.assignment-amount").val() != "") {
          amount = parseFloat($(this).find("input.fee-row-assignment-amount").val());
      }
      var feeHeadAmount = {}
      feeHeadAmount['feeHeadId'] = feeHeadId;
      feeHeadAmount['amount'] = amount;

      var feeHeadAmounts = [];
      if (feeId in feeIdFeeHeadAmounts) {
        feeHeadAmounts = feeIdFeeHeadAmounts[feeId];
      }
      feeHeadAmounts.push(feeHeadAmount);
      feeIdFeeHeadAmounts[feeId] = feeHeadAmounts;
  });

  var feeIdFeeHeadAmountsList = [];
  $.each(feeIdFeeHeadAmounts, function(key, valueObj){
      feeIdFeeHeadAmountsList.push({'feeId' : key, 'feeHeadAmountList' : valueObj});
  });
  return feeIdFeeHeadAmountsList;
}

function bindDeleteFeeAssignmentEvent() {
    $('.delete-assigned-fee').on('click', function () {
        var assignedFeesJson = $(this).parent().find('.assigned-fee-info').text().trim();
        var assignedFees = JSON.parse(assignedFeesJson);
        var screenVal = $('#entity-type').text().trim();
        $('#delete\\.assigned-fee-id').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeId);

        if(screenVal == STUDENT_ENTITY){
          var entityId = $('#fee-assign-view-student-id').text().trim();
          var admissionNumber = $('#fee-assign-search-text').val().trim();
          $('#delete\\.assigned-entity-id').val(entityId);
          $('#delete\\.assigned-admission-number').val(admissionNumber);
        } else{
          var entityId = $("#fee-assign-class").val();
          $('#delete\\.assigned-entity-id').val(entityId)
        }
        $("#delete-assigned-fee-modal").modal('toggle');
    });
}

function deleteAssignedFees(){
  var screenVal = $('#entity-type').text().trim();
  $("#delete-assigned-fee-modal").modal('toggle');

  var entityId = $("#delete\\.assigned-entity-id").val();
  var admissionNumber = $("#delete\\.assigned-admission-number").val();
  var feeId = $("#delete\\.assigned-fee-id").val();
  ajaxClient.post("/fees/delete-fee-assignment/"+screenVal+"/"+entityId+"/"+feeId,{}, function(data){

      feeAssignment.resetToMainScreen();
      $("#fee-assignment-status-modal-container").html(data);
      $("#fee-assignment-status-modal").modal('toggle');
      var sessionId = academicSessionHandler.getSelectedSessionId();
      if(screenVal == STUDENT_ENTITY){
        loadEntityFeesAssignment(sessionId, screenVal, entityId, STUDENT_ASSIGNED_FEE_LIST);
      }
      else {
        loadEntityFeesAssignment(sessionId, screenVal, entityId, CLASS_ASSIGNED_FEE_LIST);
      }
  });
}

var dueDateMaxPastDays = 365;
var REGULAR_FEE_LIST_TAB_ID = "regular-fee-tab"
var ONETIME_FEE_LIST_TAB_ID = "onetime-fee-tab"
var SPECIAL_FEE_LIST_TAB_ID = "special-fee-tab"
var NO_STREAM_VALUE = "NA"

function loadFeeConfigurationPage(){

    ajaxClient.get("/fees/fee-config", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(initFeeConfigScreen);
        registerAddFeeConfigCallback();
        reloadFeeConfigPageBindEvents();
        bindRemoveErrorDisplayEvent();
    });
}

function reloadFeeConfigPageBindEvents() {
    bindViewFeeEvents();
    bindUpdateFeeEvents();
    initDateWithYearRange("-5:+5", true);
}

function initFeeConfigScreen(){
      var academicYear = academicSessionHandler.getSelectedSession();
      $(".fee-config-academic-session").html("<option value=\""+academicYear.academicSessionId+"\">"+academicYear.displayName+"</option>");
      $("#regular-fee-start-month").val(academicYear.startMonth);
      var selectedTab = $(".fee-config-list-type.active").first().attr('id').trim();
      loadFeelist(academicYear.academicSessionId,selectedTab);
}


function openAddFeeConfigModal() {
    resetFeeConfigModal();
    var selectedTab = $(".fee-config-list-type.active").first().attr('id').trim();
    if(selectedTab==REGULAR_FEE_LIST_TAB_ID){
        $("a#regular-fee").trigger('click');
    }
    else if(selectedTab==ONETIME_FEE_LIST_TAB_ID){
      $("a#one-time-fee").trigger('click');
    }
    else if(selectedTab==SPECIAL_FEE_LIST_TAB_ID){
      $("a#special-fee").trigger('click');
    }

    $('#add-fee-config-modal').modal({backdrop: 'static', keyboard: false});
    $("#add-fee-config-modal").modal('toggle');
}

function resetFeeConfigModal() {
    $("#add-onetime-fee-form").find('input').val("");
    $("#add-onetime-fee-form").find('select:not(.fee-config-academic-session)').val("");
    $("#add-onetime-fee-form").find('textarea').val("");

    $("#add-special-fee-form").find('input').val("");
    $("#add-special-fee-form").find('select:not(.fee-config-academic-session)').val("");
    $("#add-special-fee-form").find('textarea').val("");
}

function resetFeeHeadConfigModal() {
  $("#add-fee-head-modal").find('input').val("");
  $("#add-fee-head-modal").find('select').val("");
  $("#add-fee-head-modal").find('textarea').val("");
}


// function refreshFeeConfigPage(sessionId,loadScreenId) {
//     // $("a#"+sessionId).trigger("click");
//     loadFeelist(academicYear.academicSessionId,loadScreenId);
// }

function refreshFeeConfigPage(loadScreenId) {
  var sessionId = academicSessionHandler.getSelectedSessionId();
  loadFeelist(sessionId,loadScreenId);
}


function loadFeeHeadConfigurationPage(){

    ajaxClient.get("/fees/fee-head-config", function(data) {
        $("#main-content").html(data);

        registerAddFeeHeadCallback();
        registerUpdateFeeHeadCallBack();
        registerDeleteFeeHeadCallBack();
        bindRemoveErrorDisplayEvent();
    });
}


function registerAddFeeHeadCallback(){
    $('#add-fee-head').on('click', function () {
        var invalid = validateMandatoryFields($("#add-fee-head-modal"));
        if(invalid){
          return;
        }
        var feeHeadName = $("#fee-head-name").val();
        var feeCategoryId = $("#fee-head-category option:selected").val();
        var feeHeadDescription = $("#fee-head-description").val();
        $("#add-fee-head-modal").modal('toggle');

        var feeHeadData = {"feeCategoryId":feeCategoryId,"feeHead":feeHeadName,"description" : feeHeadDescription};
        ajaxClient.post("/fees/add-fee-head",{'feeHeadData':JSON.stringify(feeHeadData)}, function(data){

            $("#feehead\\.status-modal-container").html(data);
            $("#feeconfig\\.status-modal").modal('toggle');
            loadFeeHeadlist();
       });
    });
}

function registerAddFeeConfigCallback(){
    $('#add-fee-config-button').on('click', function () {
        var feeConfigId = $('.fee-config-type.active').attr('id');
        if(feeConfigId == "regular-fee"){
            var invalid = validateMandatoryFields($("#add-regular-fee-form"));
            if(invalid){
              return;
            }
            var sessionId = $("#regular-fee-academic-session option:selected").val();
            var duration = $("#regular-fee-duration").val();
            var fineApplicable = $('#regular-fee-applicable-fine:checkbox:checked').length > 0;
            var transferToWallet = $('#regular-fee-transfer-to-wallet:checkbox:checked').length > 0;

            $("#add-fee-config-modal").modal('toggle');

            ajaxClient.post("/fees/add-regular-fee-config",{'sessionId':sessionId, 'duration':duration, 'fineApplicable' : fineApplicable, 'transferToWallet': transferToWallet}, function(data){

              $("#feeconfig\\.status-modal-container").html(data);
              $("#feeconfig\\.status-modal").modal('toggle');
              refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID);
           });
        }
        else if(feeConfigId == "one-time-fee"){
          var invalid = validateMandatoryFields($("#add-onetime-fee-form"));
          if(invalid){
            return;
          }

          var sessionId = $("#onetime-fee-academic-session option:selected").val();
          var feeName = $("#onetime-fee-name").val();
          var description = $("#onetime-fee-description").val();
          var allowPendingEnrollment = $('#onetime-fee-allow-pending-enrollment:checkbox:checked').length > 0;
          var fineApplicable = $('#onetime-fee-applicable-fine:checkbox:checked').length > 0;
          var transferToWallet = $('#onetime-fee-transfer-to-wallet:checkbox:checked').length > 0;
          var date = getDate($('#onetime-fee-due-date').val());
          var dueDate = null;
          if(date != null){
            dueDate = date.getTime()/1000;
          }
          $("#add-fee-config-modal").modal('toggle');
          oneTimeFeeData = {'feeName':feeName, 'academicSessionId' : sessionId, 'description' : description, 'dueDate' : dueDate, 'allowPendingEnrollment':allowPendingEnrollment, 'fineApplicable' : fineApplicable, 'transferToWallet' : transferToWallet}
          ajaxClient.post("/fees/add-onetime-fee",{'oneTimeFeeData':JSON.stringify(oneTimeFeeData)}, function(data){

            $("#feeconfig\\.status-modal-container").html(data);
            $("#feeconfig\\.status-modal").modal('toggle');
            refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID);
         });
        }
        else if(feeConfigId == "special-fee"){
            var invalid = validateMandatoryFields($("#add-special-fee-form"));
            if(invalid){
              return;
            }

            var sessionId = $("#special-fee-academic-session option:selected").val();
            var feeName = $("#special-fee-name").val();
            var description = $("#special-fee-description").val();
            var startMonth = $("#special-fee-start-month option:selected").val();
            var endMonth = $("#special-fee-end-month option:selected").val();
            var allowPendingEnrollment = $('#special-fee-allow-pending-enrollment:checkbox:checked').length > 0;
            var fineApplicable = $('#special-fee-applicable-fine:checkbox:checked').length > 0;
            var transferToWallet = $('#special-fee-transfer-to-wallet:checkbox:checked').length > 0;

            var date = getDate($('#special-fee-due-date').val());
            var dueDateTime = null;
            if(date != null){
               dueDateTime = date.getTime()/1000;
            }
            $("#add-fee-config-modal").modal('toggle');
            specialFeeData = {'startMonth':startMonth, 'endMonth' : endMonth, 'feeConfigurationBasicInfo' : {'feeName':feeName, 'academicSessionId' : sessionId, 'description' : description, 'dueDate' :dueDateTime, 'allowPendingEnrollment':allowPendingEnrollment, 'fineApplicable' : fineApplicable, 'transferToWallet' : transferToWallet}}
            ajaxClient.post("/fees/add-special-fee",{'specialFeeData':JSON.stringify(specialFeeData)}, function(data){

              $("#feeconfig\\.status-modal-container").html(data);
              $("#feeconfig\\.status-modal").modal('toggle');
              refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID);
           });
        }
    });
}

function loadFeelist(sessionId,loadScreenId){

  ajaxClient.get("/fees/fee-list/"+sessionId, function(data) {
      $("#fees-list").html(data);

      reloadFeeConfigPageBindEvents();
      if(loadScreenId != null){
          $("#"+loadScreenId).trigger('click');
      }
  });
}

function loadFeeHeadlist(){

  ajaxClient.get("/fees/fee-head-list", function(data) {
      $("#fee-head-list").html(data);

      registerUpdateFeeHeadCallBack();
      registerDeleteFeeHeadCallBack();
  });
}

function bindViewFeeEvents() {
      bindViewRegularFeeEvent();
      bindViewOneTimeFeeEvent();
      bindViewSpecialFeeEvent();

      bindDeleteOneTimeFeeEvent();
      bindDeleteSpecialFeeEvent();
}

function bindViewRegularFeeEvent() {
    $('.view-regular-fee').on('click', function () {
        var regularFeesJson = $(this).parent().find('.regular-fee-info').text().trim();
        var regularFees = JSON.parse(regularFeesJson);
        $('#view\\.regular-fee-name').val(regularFees.feeConfigurationBasicInfo.feeName);
        $('#view\\.regular-fee-academic-session').val(regularFees.academicSession.displayName);
        $('#view\\.regular-fee-due-date').val(regularFees.feeConfigurationBasicInfo.displayDueDate);
        $('#view\\.regular-fee-description').val(regularFees.feeConfigurationBasicInfo.description);
        $('#view\\.regular-fee-duration').val(regularFees.feeConfigurationBasicInfo.startMonthYear.month + " to " + regularFees.feeConfigurationBasicInfo.endMonthYear.month);
        $('#view\\.regular-fee-applicable-fine').prop('checked',regularFees.feeConfigurationBasicInfo.fineApplicable);
        $('#view\\.regular-fee-transfer-to-wallet').prop('checked',regularFees.feeConfigurationBasicInfo.transferToWallet);
        $("#view-regular-fee-modal").modal('toggle');
    });
}

function bindViewOneTimeFeeEvent() {
    $('.view-onetime-fee').on('click', function () {
        var onetimeFeesJson = $(this).parent().find('.onetime-fee-info').text().trim();
        var onetimeFees = JSON.parse(onetimeFeesJson);
        $('#view\\.onetime-fee-name').val(onetimeFees.feeConfigurationBasicInfo.feeName);
        $('#view\\.onetime-fee-academic-session').val(onetimeFees.academicSession.displayName);
        $('#view\\.onetime-fee-due-date').val(onetimeFees.feeConfigurationBasicInfo.displayDueDate);
        $('#view\\.onetime-fee-allow-pending-enrollment').prop('checked',onetimeFees.feeConfigurationBasicInfo.allowPendingEnrollment);
        $('#view\\.onetime-fee-applicable-fine').prop('checked',onetimeFees.feeConfigurationBasicInfo.fineApplicable);
        $('#view\\.onetime-fee-transfer-to-wallet').prop('checked',onetimeFees.feeConfigurationBasicInfo.transferToWallet);
        $('#view\\.onetime-fee-description').val(onetimeFees.feeConfigurationBasicInfo.description);
        $("#view-onetime-fee-modal").modal('toggle');
    });
}
function bindDeleteOneTimeFeeEvent() {
    $('.delete-onetime-fee').on('click', function () {
        var onetimeFeesJson = $(this).parent().find('.onetime-fee-info').text().trim();
        var onetimeFees = JSON.parse(onetimeFeesJson);
        $('#delete-one-time-fee-id').text(onetimeFees.feeConfigurationBasicInfo.feeId);
        $("#delete-one-time-fee-confirm-modal").modal('toggle');
    });
}

function bindViewSpecialFeeEvent() {
    $('.view-special-fee').on('click', function () {
        var specialFeesJson = $(this).parent().find('.special-fee-info').text().trim();
        var specialFees = JSON.parse(specialFeesJson);
        $('#view\\.special-fee-name').val(specialFees.feeConfigurationBasicInfo.feeName);
        $('#view\\.special-fee-academic-session').val(specialFees.academicSession.displayName);
        $('#view\\.special-fee-due-date').val(specialFees.feeConfigurationBasicInfo.displayDueDate);
        $('#view\\.special-fee-description').val(specialFees.feeConfigurationBasicInfo.description);
        $('#view\\.special-fee-duration').val(specialFees.feeConfigurationBasicInfo.startMonthYear.month + " to " + specialFees.feeConfigurationBasicInfo.endMonthYear.month);
        $('#view\\.special-fee-allow-pending-enrollment').prop('checked',specialFees.feeConfigurationBasicInfo.allowPendingEnrollment);
        $('#view\\.special-fee-applicable-fine').prop('checked',specialFees.feeConfigurationBasicInfo.fineApplicable);
        $('#view\\.special-fee-transfer-to-wallet').prop('checked',specialFees.feeConfigurationBasicInfo.transferToWallet);
        $("#view-special-fee-modal").modal('toggle');
    });
}

function bindDeleteSpecialFeeEvent() {
    $('.delete-special-fee').on('click', function () {
        var specialFeesJson = $(this).parent().find('.special-fee-info').text().trim();
        var specialFees = JSON.parse(specialFeesJson);
        $('#delete-special-fee-id').text(specialFees.feeConfigurationBasicInfo.feeId);
        $("#delete-special-fee-confirm-modal").modal('toggle');
    });
}

function deleteRegularFees() {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    $("#delete-regular-fee-confirm-modal").modal('toggle');

    ajaxClient.post("/fees/delete-regular-fees/"+sessionId,{}, function(data){

        $("#feeconfig\\.status-modal-container").html(data);
        $("#feeconfig\\.status-modal").modal('toggle');
        refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID);
    });
}

function deleteSpecialFee() {
    $("#delete-special-fee-confirm-modal").modal('toggle');

    var specialFeeId = $('#delete-special-fee-id').text();
    ajaxClient.post("/fees/delete-special-fees/"+specialFeeId,{}, function(data){

        $("#feeconfig\\.status-modal-container").html(data);
        $("#feeconfig\\.status-modal").modal('toggle');
        refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID);
    });
}

function deleteOneTimeFee() {
    $("#delete-one-time-fee-confirm-modal").modal('toggle');

    var oneTimeFeeId = $('#delete-one-time-fee-id').text();
    ajaxClient.post("/fees/delete-one-time-fees/"+oneTimeFeeId,{}, function(data){

        $("#feeconfig\\.status-modal-container").html(data);
        $("#feeconfig\\.status-modal").modal('toggle');
        refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID);
    });
}

function bindUpdateFeeEvents(){
  bindUpdateRegularFeeEvent();
  bindUpdateOneTimeFeeEvent();
  bindUpdateSpecialFeeEvent();

}

function bindUpdateRegularFeeEvent() {
    $('.update-regular-fee').on('click', function () {
        var regularFeesJson = $(this).parent().find('.regular-fee-info').text().trim();
        var regularFees = JSON.parse(regularFeesJson);
        $('#update\\.regular-fee-id').val(regularFees.feeConfigurationBasicInfo.feeId);
        $('#update\\.regular-fee-name').val(regularFees.feeConfigurationBasicInfo.feeName);
        $('#update\\.regular-fee-academic-session').val(regularFees.academicSession.displayName);
        $('#update\\.regular-fee-due-date').val(regularFees.feeConfigurationBasicInfo.displayDueDate);
        $('#update\\.regular-fee-description').val(regularFees.feeConfigurationBasicInfo.description);
        $('#update\\.regular-fee-duration').val(regularFees.feeConfigurationBasicInfo.startMonthYear.month + " to " + regularFees.feeConfigurationBasicInfo.endMonthYear.month);
        $('#update\\.regular-fee-applicable-fine').prop('checked',regularFees.feeConfigurationBasicInfo.fineApplicable);
        $('#update\\.regular-fee-transfer-to-wallet').prop('checked',regularFees.feeConfigurationBasicInfo.transferToWallet);
        $("#update-regular-fee-modal").modal('toggle');
    });
}

function bindUpdateOneTimeFeeEvent() {
    $('.update-onetime-fee').on('click', function () {
        var oneTimeFeesJson = $(this).parent().find('.onetime-fee-info').text().trim();
        var oneTimeFees = JSON.parse(oneTimeFeesJson);
        $('#update\\.onetime-fee-id').val(oneTimeFees.feeConfigurationBasicInfo.feeId);
        $('#update\\.one-time-fee-name').val(oneTimeFees.feeConfigurationBasicInfo.feeName);
        $('#update\\.one-time-fee-academic-session').val(oneTimeFees.academicSession.displayName);
        $('#update\\.one-time-fee-due-date').val(oneTimeFees.feeConfigurationBasicInfo.displayDueDate);
        $('#update\\.one-time-fee-allow-pending-enrollment').prop('checked',oneTimeFees.feeConfigurationBasicInfo.allowPendingEnrollment);
        $('#update\\.onetime-fee-applicable-fine').prop('checked',oneTimeFees.feeConfigurationBasicInfo.fineApplicable);
        $('#update\\.onetime-fee-transfer-to-wallet').prop('checked',oneTimeFees.feeConfigurationBasicInfo.transferToWallet);
        $('#update\\.one-time-fee-description').val(oneTimeFees.feeConfigurationBasicInfo.description);
        $("#update-one-time-fee-modal").modal('toggle');
    });
}

function bindUpdateSpecialFeeEvent() {
    $('.update-special-fee').on('click', function () {
        var specialFeesJson = $(this).parent().find('.special-fee-info').text().trim();
        var specialFees = JSON.parse(specialFeesJson);
        $('#update\\.special-fee-id').val(specialFees.feeConfigurationBasicInfo.feeId);
        $('#update\\.special-fee-name').val(specialFees.feeConfigurationBasicInfo.feeName);
        $('#update\\.special-fee-academic-session').val(specialFees.academicSession.displayName);
        $('#update\\.special-fee-due-date').val(specialFees.feeConfigurationBasicInfo.displayDueDate);
        $('#update\\.special-fee-description').val(specialFees.feeConfigurationBasicInfo.description);
        $('#update\\.special-fee-start-month').val(specialFees.feeConfigurationBasicInfo.startMonthYear.month);
        $('#update\\.special-fee-end-month').val(specialFees.feeConfigurationBasicInfo.endMonthYear.month);
        $('#update\\.special-fee-allow-pending-enrollment').prop('checked',specialFees.feeConfigurationBasicInfo.allowPendingEnrollment);
        $('#update\\.special-fee-applicable-fine').prop('checked',specialFees.feeConfigurationBasicInfo.fineApplicable);
        $('#update\\.special-fee-transfer-to-wallet').prop('checked',specialFees.feeConfigurationBasicInfo.transferToWallet);
        $("#update-special-fee-modal").modal('toggle');
    });
}

function updateRegularFeeConfiguration(){
  var invalid = validateMandatoryFields($("#update-regular-fee-modal"));
  if(invalid){
    return;
  }
  var feeId = $("#update\\.regular-fee-id").val();
  var feeName = $("#update\\.regular-fee-name").val();
  var description = $("#update\\.regular-fee-description").val();
  var date = getDate($("#update\\.regular-fee-due-date").val());
  var fineApplicable = $('#update\\.regular-fee-applicable-fine:checkbox:checked').length > 0;
  var transferToWallet = $('#update\\.regular-fee-transfer-to-wallet:checkbox:checked').length > 0;

  var dueDate = null;
  if(date != null){
    dueDate = date.getTime()/1000;
  }
  $("#update-regular-fee-modal").modal('toggle');

  var updateFeeConfigData = {"feeId" : feeId, "feeName":feeName,"dueDate":dueDate, "fineApplicable" : fineApplicable, "transferToWallet" : transferToWallet, "description":description};
  ajaxClient.post("/fees/update-fee-config",{'updateFeeConfigData':JSON.stringify(updateFeeConfigData)}, function(data){

    $("#feeconfig\\.status-modal-container").html(data);
    $("#feeconfig\\.status-modal").modal('toggle');
    refreshFeeConfigPage(REGULAR_FEE_LIST_TAB_ID);
    });
}

function updateOneTimeFeeConfiguration(){
  var invalid = validateMandatoryFields($("#update-one-time-fee-modal"));
  if(invalid){
    return;
  }
  var feeId = $("#update\\.onetime-fee-id").val();
  var feeName = $("#update\\.one-time-fee-name").val();
  var description = $("#update\\.one-time-fee-description").val();
  var allowPendingEnrollment = $('#update\\.one-time-fee-allow-pending-enrollment:checkbox:checked').length > 0;
  var fineApplicable = $('#update\\.onetime-fee-applicable-fine:checkbox:checked').length > 0;
  var transferToWallet = $('#update\\.onetime-fee-transfer-to-wallet:checkbox:checked').length > 0;
  var date = getDate($("#update\\.one-time-fee-due-date").val());
  var dueDate = null;
  if(date != null){
    dueDate = date.getTime()/1000;
  }
  $("#update-one-time-fee-modal").modal('toggle');

  var updateFeeConfigData = {"feeId" : feeId, "feeName":feeName,"dueDate":dueDate, "description":description,"allowPendingEnrollment":allowPendingEnrollment, 'fineApplicable' : fineApplicable, 'transferToWallet' : transferToWallet};
  ajaxClient.post("/fees/update-fee-config",{'updateFeeConfigData':JSON.stringify(updateFeeConfigData)}, function(data){

      refreshFeeConfigPage(ONETIME_FEE_LIST_TAB_ID);
      $("#feeconfig\\.status-modal-container").html(data);
      $("#feeconfig\\.status-modal").modal('toggle');
  });
}

function updateSpecialFeeConfiguration(){
  var invalid = validateMandatoryFields($("#update-special-fee-modal"));
  if(invalid){
    return;
  }
  var feeId = $("#update\\.special-fee-id").val();
  var feeName = $("#update\\.special-fee-name").val();
  var startMonth = $("#update\\.special-fee-start-month option:selected").val();
  var endMonth = $("#update\\.special-fee-end-month option:selected").val();
  var allowPendingEnrollment = $('#update\\.special-fee-allow-pending-enrollment:checkbox:checked').length > 0;
  var fineApplicable = $('#update\\.special-fee-applicable-fine:checkbox:checked').length > 0;
  var transferToWallet = $('#update\\.special-fee-transfer-to-wallet:checkbox:checked').length > 0;
  var description = $("#update\\.special-fee-description").val();
  var date = getDate($("#update\\.special-fee-due-date").val());
  var dueDate = null;
  if(date != null){
    dueDate = date.getTime()/1000;
  }
  $("#update-special-fee-modal").modal('toggle');

  var updateSpecialFeeConfigData = {"feeId" : feeId, "feeName":feeName,"dueDate":dueDate, "description":description, "startMonth":startMonth, "endMonth":endMonth,"allowPendingEnrollment":allowPendingEnrollment, 'fineApplicable' : fineApplicable, 'transferToWallet' : transferToWallet};
  ajaxClient.post("/fees/update-special-fee-config",{'updateSpecialFeeConfigData':JSON.stringify(updateSpecialFeeConfigData)}, function(data){

    refreshFeeConfigPage(SPECIAL_FEE_LIST_TAB_ID);
    $("#feeconfig\\.status-modal-container").html(data);
    $("#feeconfig\\.status-modal").modal('toggle');

    });
}

function registerUpdateFeeHeadCallBack(){
  $('.update-fee-head').on('click', function () {
      var feeHeadJson = $(this).parent().find('.fee-head-info').text().trim();
      var feeHead = JSON.parse(feeHeadJson);
      $('#update\\.fee-head-id').val(feeHead.feeHeadConfiguration.feeHeadId);
      $('#update\\.fee-head-name').val(feeHead.feeHeadConfiguration.feeHead);
      $('#update\\.fee-category-id').val(feeHead.feeCategory.feeCategoryId);
      $('#update\\.fee-category-name').val(feeHead.feeCategory.feeCategoryName);
      $('#update\\.fee-head-description').val(feeHead.feeHeadConfiguration.description);
      $("#update-fee-head-modal").modal('toggle');
  });
}

function updateFeeHead(){
  var invalid = validateMandatoryFields($("#update-fee-head-modal"));
  if(invalid){
    return;
  }
  var feeHeadId = $("#update\\.fee-head-id").val();
  var feeHeadName = $("#update\\.fee-head-name").val();
  var feeCategoryId = $("#update\\.fee-category-id").val();
  var feeHeadDescription = $("#update\\.fee-head-description").val();
  $("#update-fee-head-modal").modal('toggle');

  var updateFeeHeadData = {"feeHeadId" : feeHeadId, "feeHead":feeHeadName,"feeCategoryId":feeCategoryId, "description":feeHeadDescription};
  ajaxClient.post("/fees/update-fee-head",{'updateFeeHeadData':JSON.stringify(updateFeeHeadData)}, function(data){

    $("#feehead\\.status-modal-container").html(data);
    $("#feeconfig\\.status-modal").modal('toggle');
    loadFeeHeadlist();
    });
}

function registerDeleteFeeHeadCallBack(){
  $('.delete-fee-head').on('click', function () {
    var feeHeadJson = $(this).parent().find('.fee-head-info').text().trim();
    var feeHead = JSON.parse(feeHeadJson);
    $('#delete-fee-head-id').val(feeHead.feeHeadConfiguration.feeHeadId);
    $("#delete-fee-head-confirm-modal").modal('toggle');
  });
}

function deleteFeeHead(){
  $("#delete-fee-head-confirm-modal").modal('toggle');
  var feeHeadId = $("#delete-fee-head-id").val();
  ajaxClient.post("/fees/delete-fee-head-configuration/"+feeHeadId,{}, function(data){
      $("#feehead\\.status-modal-container").html(data);
      $("#feeconfig\\.status-modal").modal('toggle');
      loadFeeHeadlist();
  });
}



var feeStructure = {

  dataCache : {},
  INSTITUTE_ENTITY : "institute",

  initDataCache: function () {
    var authorizedFeeAssignments = readJson('#session-authorized-fee-assignments');
    var standards = readJson('#add-fee-standards');
    var feeStructures = readJson('#configured-fee-structure-info');

    var standardsMap = {};
    var feeStructureMap = {};
    var feeMap = {};
    var feeHeadMap = {};
    var authorizedFeeIdFeeHeadMap = {};
    var feeIdFeeHeadSelectOptionData = {};

    for(var i = 0; i < authorizedFeeAssignments.length; i++) {
        var feeConfigurationBasicInfo = authorizedFeeAssignments[i].feeConfigurationResponse.feeConfigurationBasicInfo;
        feeMap[feeConfigurationBasicInfo.feeId] = feeConfigurationBasicInfo;
        var authorizedFeeHeadList = [];
        for(var j = 0; j < authorizedFeeAssignments[i].authorizedFeeHeadAssignments.length; j++){
          var feeHeadId = authorizedFeeAssignments[i].authorizedFeeHeadAssignments[j].feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId;
          feeHeadMap[feeHeadId] = authorizedFeeAssignments[i].authorizedFeeHeadAssignments[j].feeHeadConfigurationResponse.feeHeadConfiguration;

          if(authorizedFeeAssignments[i].authorizedFeeHeadAssignments[j].module_ids.includes("FEES")) {
            authorizedFeeHeadList.push(feeHeadId);
          }
        }
        authorizedFeeIdFeeHeadMap[feeConfigurationBasicInfo.feeId] = authorizedFeeHeadList;
    }

    for(var i = 0; i < standards.length; i++) {
        var standard = standards[i];
        standardsMap[standard.standardId] = standard;
    }

    for( var i = 0; i < feeStructures.length; i++) {
        var feeStructureData = feeStructures[i];
        feeStructureMap[feeStructureData.structureId] = feeStructureData;
    }



    // Reset cache
    feeStructure.dataCache = {};
    // Add new session values
    feeStructure.dataCache.feeStructureMap = feeStructureMap;
    feeStructure.dataCache.standards = standards;
    feeStructure.dataCache.standardsMap = standardsMap;
    feeStructure.dataCache.feeMap = feeMap;
    feeStructure.dataCache.feeHeadMap = feeHeadMap;
    feeStructure.dataCache.authorizedFeeIdFeeHeadMap = authorizedFeeIdFeeHeadMap;
    feeStructure.dataCache.configureNewStructure = {'institute' : {'selectedFees' : {}}, 'standard' : {'selectedStandards' : {}, selectedFees : {} }};

    for(feeId in authorizedFeeIdFeeHeadMap){
      var authorizedFeeHeadList = authorizedFeeIdFeeHeadMap[feeId];
      var feeHeadSelect =  feeStructure.createFeeHeadSelectMenu(authorizedFeeHeadList, "");
      feeIdFeeHeadSelectOptionData[feeId] = feeHeadSelect;
    }
    feeStructure.dataCache.feeIdFeeHeadSelectOptionData = feeIdFeeHeadSelectOptionData;

  },

  createFeeHeadSelectMenu : function (authorizedFeeHeadList , selectedFeeHeadId) {
    var feeHeadSelect = "<select class=\"form-control form-control-sm mandatory-field fee-head\"> <option value=\"\">select</option>";
    for(var i = 0; i < authorizedFeeHeadList.length; i++){
      var feeHeadId = authorizedFeeHeadList[i];
      var feeHeadName = feeStructure.dataCache.feeHeadMap[feeHeadId].feeHead;
      if(selectedFeeHeadId == feeHeadId){
        feeHeadSelect += "<option value=\""+feeHeadId+"\" selected>"+feeHeadName+"</option>";
      }else{
        feeHeadSelect += "<option value=\""+feeHeadId+"\">"+feeHeadName+"</option>";
      }
    }
    feeHeadSelect += "</select>"
    return feeHeadSelect;
  },

  loadFeeStructureConfigurationPageWithoutSession : function(){
    ajaxClient.get("/fees/fee-structure-config-without-session", function(data) {
      $("#main-content").html(data);
      feeStructure.initDataCache();
      academicSessionHandler.bindSessionChangeEvent(feeStructure.changeSession);
    });
  },

  loadFeeStructureConfigurationPage : function(){
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/fee-structure-config/"+sessionId, function(data) {
      $("#configured-fee-structure-list").html(data);
      feeStructure.initDataCache();
    });
  },

  changeSession: function() {
    feeStructure.loadFeeStructureConfigurationPage();
  },

  createFeeContainerId : function (entityId, feeId) {
    return entityId + "-" + feeId;
  },

  createEntityContainerId : function (entityId) {
    return entityId + "-" + "fee-structure-container";
  },

  bindFeeHeadRowEvents : function (feeContainerId) {
      var previousSelectedValue = "";
      $("#"+feeContainerId).find(".fee-head").on('focus', function () {
        // Store the current value on focus and on change
        previousSelectedValue = $(this).find(':selected').val().trim();
      })
      .change(function () {
          var feeHeadId = $(this).find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          var presenceCount = 0;
          $(this).closest('tbody.fee-structure-body').find("tr").each(function() {
            var existingFeeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
            if(existingFeeHeadId == ""){
              return;
            }else if(feeHeadId == existingFeeHeadId){
              presenceCount++;
            }
          });
          if(presenceCount > 1){
            showErrorDialogBox("Fee Head " + $(this).find(':selected').text() + " already selected. Please fill amount in that");
            $(this).val(previousSelectedValue);
          }
      });

      $("#"+feeContainerId).find(".delete-fee-head-row").on('click', function () {
        $(this).closest("tr").remove();
      });
  },

  insertFeeHeadRow  :function (feeContainerId, feeId , selectedFeeHeadId, feeHeadAmount) {
    var feeHeadRow = "";
    if(selectedFeeHeadId == null){
      var feeHeadSelectOptionData = feeStructure.dataCache.feeIdFeeHeadSelectOptionData[feeId];
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\"> </td> <td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }else{
      var authorizedFeeHeadList = feeStructure.dataCache.authorizedFeeIdFeeHeadMap[feeId];
      var feeHeadSelectOptionData =  feeStructure.createFeeHeadSelectMenu(authorizedFeeHeadList, selectedFeeHeadId);
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\" value=\""+feeHeadAmount+"\"> </td> <td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }

    $("#"+feeContainerId).find(".fee-structure-body").append(feeHeadRow);
    feeStructure.bindFeeHeadRowEvents(feeContainerId);
  },

  bindFeeContainerEvents : function (entityParentContainerId, feeContainerId, entityId, feeId, selectedFees) {
    $("#"+feeContainerId).find(".add-fee-head-button").on('click', function () {
      feeStructure.insertFeeHeadRow(feeContainerId, feeId, null, 0);
    });

    $("#"+feeContainerId).find(".delete-fee-structure-row").on('click', function () {
      $(this).closest(".fee-structure").remove();
      delete selectedFees[feeId];
      if(isEmpty(selectedFees)){
        $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:block;");
      }
      feeStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);
    });

  },

  insertFeeDetailsCard : function (entityParentContainerId, entityId, feeId, selectedFees) {
    selectedFees[feeId] = true;
    $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:none;");

    var entityContainerId = feeStructure.createEntityContainerId(entityId);
    var feeContainerId = feeStructure.createFeeContainerId(entityId,feeId);
    var feeMap = feeStructure.dataCache.feeMap;
    var feeName = feeMap[feeId].feeName.toUpperCase();
    var feeDetailsCard = "<div class=\"fee-structure\"> <div class=\"card card-border text-center\" id=\""+feeContainerId+"\"> <p class=\"fee-id\" style=\"display:none;\">"+feeId+"</p> <div class=\"card-body\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-fee-structure-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h5 class=\"card-title\"> <strong>"+feeName+"</strong> </h5> <div style=\"padding-left:15%; padding-right:15%;\"> <table class=\"table table-borderless\"> <thead> <tr> <th scope=\"col\" class=\"w-50\">Fee Head</th> <th scope=\"col\">Amount</th> <th scope=\"col\"></th> </tr> </thead> <tbody class=\"fee-structure-body\"> </tbody> </table> <button type=\"button\" class=\"btn btn-outline-secondary btn-sm add-fee-head-button\"> + Add More Fee Head</button> </div> </div> </div> </br> <div>";
    $("#"+entityContainerId).append(feeDetailsCard);
    feeStructure.bindFeeContainerEvents(entityParentContainerId, feeContainerId, entityId, feeId, selectedFees);
    feeStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);

  },

  bindSelectFeesEvent : function (entityParentContainerId, entityId, selectedFees) {
    $(entityParentContainerId).find('.fee-select-option').on('click', function (event) {
      //This is for keeping the fees drop-down open (https://embrate.atlassian.net/browse/PD-2535)
      event.stopPropagation();
      var feeId = $(this).attr("id").split("-select-")[0];
      var entityId = $(this).attr("id").split("-select-")[1];
      feeStructure.insertFeeDetailsCard(entityParentContainerId, entityId, feeId, selectedFees);
    });
  },


  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = feeStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    feeStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },


  bindStandardContainerEvents : function (standardContainerId, standardId) {

    $("#"+standardContainerId).find(".delete-standard-row").on('click', function () {
      $(this).closest(".standard-fee-structure").remove();
      delete feeStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId];
      if(isEmpty(feeStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;");
      }
      feeStructure.populateStandardSelectDropdown();
    });

  },

  getStandardContainerId : function (standardId) {
      return standardId + "-container";
  },

  insertStandardDetailsCard : function (standardId) {
    feeStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId] = true;
    $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");
    // var feeContainerId = feeStructure.createFeeContainerId(entityId,feeId);
    var standardMap = feeStructure.dataCache.standardsMap;
    var stream = "";
    if(standardMap[standardId].stream != null && standardMap[standardId].stream != NO_STREAM_VALUE) {
      stream = " (" + standardMap[standardId].stream + ")";
    }
    var standardName = standardMap[standardId].standardName.toUpperCase() + stream;
    var standardContainerId = feeStructure.getStandardContainerId(standardId);
    var standardDetailsCard = "<div id=\""+standardContainerId+"\" class=\"card card-border standard-fee-structure\"> <p class=\"standard-id\" style=\"display:none;\">"+standardId+"</p> <div class=\"card-header card-header-color\" id=\""+standardId+"-heading\" data-toggle=\"collapse\" data-target=\"#collapse-"+standardId+"\" aria-expanded=\"true\" aria-controls=\"collapse-"+standardId+"\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-standard-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h2 class=\"mb-0\"> <button class=\"btn btn-link\" type=\"button\"> <strong>"+standardName+"</strong> </button> </h2> </div> <div id=\"collapse-"+standardId+"\" class=\"collapse\" aria-labelledby=\""+standardId+"-heading\" data-parent=\"#class-fee-structure-config-accordion\"> <div class=\"card-body\"> <div id=\""+standardId+"-fee-structure-container\" style=\"text-align:center;\"> <p class=\"institute-add-structure-hint-text black-color\"> Select fees to add new fee structure</p> <!-- Next card here --> </div> <br /> <div class=\"fee-select-dropdown-container\" style=\"float:right;\"> <div class=\"btn-group dropup\" style=\"width:150px;\"> <button type=\"button\" class=\"btn btn-info btn-sm dropdown-toggle\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"> Select Fees </button> <div class=\"dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right\"> </div> </div> </div> <br/> </div> </div> </div>";
    $("#class-fee-structure-config-accordion").append(standardDetailsCard);
    feeStructure.dataCache.configureNewStructure.standard.selectedFees[standardId] = {};
    feeStructure.populateSelectFeesDropdown("#"+standardContainerId, standardId, feeStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
    feeStructure.bindStandardContainerEvents(standardContainerId, standardId);
    feeStructure.populateStandardSelectDropdown();
  },

  bindStandardSelectEvent : function () {
    $('.class-fee-structure').find('.standard-select-option').on('click', function () {
      var standardId = $(this).attr("id").split("-select")[0];
      feeStructure.insertStandardDetailsCard(standardId);
    });
  },

  populateStandardSelectDropdown : function () {
    var standardsMap = feeStructure.dataCache.standardsMap;
    var standardDropdownListContent = "";
    for(standardId in standardsMap){
      var stream = "";
      if(standardsMap[standardId].stream != null && standardsMap[standardId].stream != NO_STREAM_VALUE) {
        stream = " (" + standardsMap[standardId].stream + ")";
      }
      var standardName = standardsMap[standardId].standardName.toUpperCase() + stream;
      if(!(standardId in feeStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          standardDropdownListContent += "<a class=\"dropdown-item standard-select-option\" href=\"#\" id=\"" + standardId +"-select"+"\">" + standardName +"</a>";
      }
    }
    $(".class-fee-structure").find(".standard-select-dropdown").html(standardDropdownListContent);
    feeStructure.bindStandardSelectEvent();
  },

  resetFeeStructureModal : function () {
    $("#institute-fee-structure-container").html("<p class=\"institute-add-structure-hint-text black-color\"> Select fees to add new fee structure</p>")
    $("#class-fee-structure-config-accordion").html("<p class=\"standrad-add-structure-hint-text black-color text-center\"> Select class to add new fee structure</p>")
    $("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName());
    $("#fee-structure-name").val("");
    $("#fee-structure-types").val("");

    $("#fee-structure-name").attr("disabled",false);
    $("#fee-structure-types").attr("disabled",false);
    $("#fee-structure-config-modal").find(".modal-footer").css("display","");

    $("#nav-structure-tab").find(".nav-item").removeClass("active");
    $("#nav-institute-structure-config-tab").addClass("active");

    $("#nav-structure-tabContent").find(".tab-pane").removeClass("show active");
    $("#nav-institute-structure-config").addClass("show active");

  },

  loadNewFeeStructureConfigureModal : function() {
    // Clear old dataType
    feeStructure.resetFeeStructureModal();
    $("#fee-structure-config-modal").find(".modal-title").html("Configure New Fee Structure");
    $("#submit-fee-structure").html("Configure New Fee Structure");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");
    // Clear the older values
    feeStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    feeStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    feeStructure.dataCache.configureNewStructure.standard.selectedFees = {};
    feeStructure.dataCache.selectedStructureId = null;

    feeStructure.populateSelectFeesDropdown(".institute-structure", feeStructure.INSTITUTE_ENTITY, feeStructure.dataCache.configureNewStructure.institute.selectedFees);
    feeStructure.populateStandardSelectDropdown();

    // var instituteLevelFeeRow = feeStructure.getBlankFeeRowInstituteLevel();
    // var instituteLevelFeeStructureInputDiv = "<div class=\"row\"><div class=\"col-md-12\"> <table class=\"table borderless\"> <thead> <tr><th scope=\"col\">Fee Name</th> 	<th scope=\"col\">Fee Head</th> 	<th scope=\"col\">Amount</th> 	<th scope=\"col\"></th> </tr></thead> "+instituteLevelFeeRow+" <tbody class=\"blank-tbody\"><tr id=\"new-institute-level-add-row-bottom\" style=\"display:none;\"> </tr> </tbody> </table></div></div>";
    // $('#add-institute-level-fee-structure-input-div').html(instituteLevelFeeStructureInputDiv);
    // var classLevelFeeRow= feeStructure.getBlankClassLevelClassRow();
    // var classLevelFeeStructureInputDiv = "<div class=\"form-row\"><div class=\"form-group col-md-3 class-name-class-level\">Class</div><div class=\"form-group col-md-3 class-name-class-level\">Fee Name</div><div class=\"form-group col-md-3 class-name-class-level\">Fee Head</div><div class=\"form-group col-md-3 class-name-class-level\">Amount</div></div><hr>"+classLevelFeeRow+" <table class=\"new-class-level-blank-tbody\"><tr id=\"new-class-level-add-row-bottom\" style=\"display:none;\"> </tr></table></div></div>";
    // $("#add-class-level-fee-structure-input-div").html(classLevelFeeStructureInputDiv);
    // $('#fee-structure-config-modal').modal({backdrop: 'static', keyboard: false});
    // $("#fee-structure-config-modal").modal('toggle');
  },

  fillFeeStructureModal : function (structureId, viewOnly) {
    feeStructure.dataCache.selectedStructureId = structureId;
    var feeStructureMap = feeStructure.dataCache.feeStructureMap;
    if(!(structureId in feeStructureMap)){
      showErrorDialogBox("Invalid Fee Structure.")
      return;
    }
    var selectedFeeStructure = feeStructureMap[structureId];
    feeStructure.resetFeeStructureModal();
    $("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName());
    $("#fee-structure-name").val(selectedFeeStructure.structureName);
    $("#fee-structure-types").val(selectedFeeStructure.feeStructureType);

    $("#fee-structure-config-modal").find(".modal-title").html("Update Fee Structure");
    $("#submit-fee-structure").html("Update Fee Structure");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");

    feeStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    feeStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    feeStructure.dataCache.configureNewStructure.standard.selectedFees = {};

    var instituteEntitySelectedFees = {};
    var standradEntitySelectedFees = {};
    for(var i = 0; i < selectedFeeStructure.entityFeeAssignments.length; i++){
      var entityFeeAssignment = selectedFeeStructure.entityFeeAssignments[i];
      if(entityFeeAssignment.feeEntity == 'INSTITUTE'){
        for(var j = 0; j < entityFeeAssignment.feeIdFeeHeadDetailsList.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment.feeIdFeeHeadDetailsList[j];
          var feeId = feeIdFeeHeadDetails.feeConfigurationResponse.feeConfigurationBasicInfo.feeId;
          instituteEntitySelectedFees[feeId] = feeIdFeeHeadDetails;
        }
      }else if(entityFeeAssignment.feeEntity == 'CLASS'){
        var standardId = entityFeeAssignment.entityId;
        standradEntitySelectedFees[standardId] = {};
        for(var j = 0; j < entityFeeAssignment.feeIdFeeHeadDetailsList.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment.feeIdFeeHeadDetailsList[j];
          var feeId = feeIdFeeHeadDetails.feeConfigurationResponse.feeConfigurationBasicInfo.feeId;
          standradEntitySelectedFees[standardId][feeId] = feeIdFeeHeadDetails;
        }
      }
    }


    for(feeId in instituteEntitySelectedFees){
        feeStructure.insertFeeDetailsCard(".institute-structure", feeStructure.INSTITUTE_ENTITY, feeId, feeStructure.dataCache.configureNewStructure.institute.selectedFees);
        var feeContainerId = feeStructure.createFeeContainerId(feeStructure.INSTITUTE_ENTITY,feeId);
        for(var i = 0; i < instituteEntitySelectedFees[feeId].feeHeadAmountDetailsList.length; i++){
          var feeHeadId = instituteEntitySelectedFees[feeId].feeHeadAmountDetailsList[i].feeHeadId;
          var feeHeadAmount = instituteEntitySelectedFees[feeId].feeHeadAmountDetailsList[i].amount;
          feeStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount);
        }
    }

    for(standardId in standradEntitySelectedFees){
      feeStructure.insertStandardDetailsCard(standardId);
      var standardContainerId = feeStructure.getStandardContainerId(standardId);
      for(feeId in standradEntitySelectedFees[standardId]){
          feeStructure.insertFeeDetailsCard("#"+standardContainerId, standardId, feeId, feeStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
          var feeContainerId = feeStructure.createFeeContainerId(standardId, feeId);
          for(var i = 0; i < standradEntitySelectedFees[standardId][feeId].feeHeadAmountDetailsList.length; i++){
            var feeHeadId = standradEntitySelectedFees[standardId][feeId].feeHeadAmountDetailsList[i].feeHeadId;
            var feeHeadAmount = standradEntitySelectedFees[standardId][feeId].feeHeadAmountDetailsList[i].amount;
            feeStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount);
          }
      }
    }
    // Needed in case standradEntitySelectedFees or instituteEntitySelectedFees is empty
    feeStructure.populateSelectFeesDropdown(".institute-structure", feeStructure.INSTITUTE_ENTITY, feeStructure.dataCache.configureNewStructure.institute.selectedFees);
    feeStructure.populateStandardSelectDropdown();

    if(viewOnly){
        $(".standard-select-dropdown-container").css("display","none");
        $(".fee-select-dropdown-container").css("display","none");
        $(".add-fee-head-button").css("display","none");
        $(".fee-head").attr("disabled","true");
        $(".fee-head-amount").attr("disabled","true");
        $(".delete-fee-head-row").remove();
        $(".delete-fee-structure-row").remove();
        $(".delete-standard-row").remove();

        $("#fee-structure-name").attr("disabled",true);
        $("#fee-structure-types").attr("disabled",true);
        $("#fee-structure-config-modal").find(".modal-footer").css("display","none");
    }
  },

  populateDeleteStructureModal : function (structureId) {
    var selectedFeeStructure = feeStructure.dataCache.feeStructureMap[structureId];
    feeStructure.dataCache.feeStructureID = structureId;
    $("#delete-fee-structure-modal-text").html("Do you want to delete fee Structure : "+selectedFeeStructure.structureName +"?");

  },


  submitFeeStructure : function () {
    var invalid = validateMandatoryFields($("#fee-structure-config-modal"));
    if(invalid){
      showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.", "#fee-structure-config-modal");
      return;
    }

    $("#fee-structure-config-modal").modal('toggle');
    var structureName = $("#fee-structure-name").val();
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var structureType = $("#fee-structure-types option:selected").val();
    var structureId = null;
    if(feeStructure.dataCache.selectedStructureId != null && feeStructure.dataCache.selectedStructureId.trim() != "" ){
      structureId = feeStructure.dataCache.selectedStructureId;
    }

    var instituteFeeIdFeeHeadsList =  feeStructure.getFeeStructureData('#institute-fee-structure-container');
    var instituteFeeAssignmentPayload = {'feeEntity' : 'INSTITUTE', 'feeIdFeeHeadsList' : instituteFeeIdFeeHeadsList};

    var entityFeeAssignmentPayloadList = feeStructure.getStandardsFeeStructureData();
    entityFeeAssignmentPayloadList.push(instituteFeeAssignmentPayload);

    var feeAssignmentStructurePayload = [{'structureId': structureId, 'structureName' : structureName, 'feeStructureType' : structureType, 'entityFeeAssignmentPayloads' : entityFeeAssignmentPayloadList}];

    var submitStructureURL = "/fees/add-fee-structure/"+academicSessionId;
    if(structureId != null){
      submitStructureURL = "/fees/update-fee-structure/"+structureId+"/"+academicSessionId;
    }

    ajaxClient.post(submitStructureURL ,{'feeAssignmentStructurePayload':JSON.stringify(feeAssignmentStructurePayload)}, function(data){
      $("#fee-structure-status-modal-container").html(data);
      $("#fee-structure-status-modal").modal('toggle');
      feeStructure.loadFeeStructureConfigurationPage();
    });

  },

  getStandardsFeeStructureData : function () {
    var standardFeeAssignmentPayloadList = [];
    $("#class-fee-structure-config-accordion").find(".standard-fee-structure").each( function () {
      var standardId = $(this).find(".standard-id").text().trim();
      var standardFeeIdFeeHeadsList = feeStructure.getFeeStructureData(this);
      standardFeeAssignmentPayloadList.push({'entityId' : standardId, 'feeEntity' : 'CLASS', 'feeIdFeeHeadsList' : standardFeeIdFeeHeadsList});
    });
    return standardFeeAssignmentPayloadList;
  },

  getFeeStructureData : function (entityFeeStructureContainerId) {
    var feeIdFeeHeadsList = [];
    $(entityFeeStructureContainerId).find(".fee-structure").each( function () {
      var feeId = $(this).find(".fee-id").text().trim();
      var feeHeadAmountList = [];
      var feeHeads = {};
      $(this).find('tbody.fee-structure-body').find('tr').each(function () {
          var feeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          if(feeHeadId in feeHeads){
            // Need to handle properly to display relevent error
            return;
          }
          var feeHeadAmount = $(this).find(".fee-head-amount").val();
          feeHeadAmountList.push({'feeHeadId' : feeHeadId, 'amount' : feeHeadAmount});
      });
      if(feeHeadAmountList.length > 0){
        feeIdFeeHeadsList.push({'feeId' : feeId, 'feeHeadAmountList' : feeHeadAmountList});
      }
    });
    return feeIdFeeHeadsList;
  },

  deleteFeeStructure : function () {
     $("#delete-fee-structure-modal").modal('toggle');
     var structureId = feeStructure.dataCache.feeStructureID;
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     ajaxClient.post("/fees/delete-fee-structure/"+structureId+"/"+academicSessionId, {}, function(data){
       $("#fee-structure-status-modal-container").html(data);
       $("#fee-structure-status-modal").modal('toggle');
       feeStructure.loadFeeStructureConfigurationPage();
     });
   }
};

var discountStructure = {

  dataCache : {},
  INSTITUTE_ENTITY : "institute",

  loadDiscountStructureConfigurationPageWithoutSession : function(){
    ajaxClient.get("/fees/discount-structure-without-session", function(data) {
      $("#main-content").html(data);
      discountStructure.initDataCache();
      academicSessionHandler.bindSessionChangeEvent(discountStructure.changeSession);
    });
  },

  loadDiscountStructureConfigurationPage : function(){
    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/discount-structure/"+sessionId, function(data) {
      $("#configured-discount-structure-list").html(data);
      discountStructure.initDataCache();
    });
  },

  changeSession: function() {
    discountStructure.loadDiscountStructureConfigurationPage();
  },

  initDataCache: function () {
    var feeData = readJson('#fee-data');
    var standards = readJson('#discount-standards');
    var discountStructures = readJson('#discount-structures');

    var standardsMap = {};
    var discountStructureMap = {};
    var feeMap = {};
    var feeHeadMap = {};
    var feeIdFeeHeadSelectOptionData = {};
    var feeConfigurationBasicInfoList = feeData.feeConfigurationBasicInfoList;
    var feeHeadConfigurationResponseList = feeData.feeHeadConfigurationResponseList;
    for(var i = 0; i < feeConfigurationBasicInfoList.length; i++) {
      var feeConfigurationBasicInfo = feeConfigurationBasicInfoList[i]
      feeMap[feeConfigurationBasicInfo.feeId] = feeConfigurationBasicInfo;
      var feeHeadSelect =  discountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, "");
      feeIdFeeHeadSelectOptionData[feeConfigurationBasicInfo.feeId] = feeHeadSelect;
    }

    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++) {
      var feeHeadConfigurationResponse = feeHeadConfigurationResponseList[i]
      feeHeadMap[feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId] = feeHeadConfigurationResponse;
    }

    for(var i = 0; i < standards.length; i++) {
        var standard = standards[i];
        standardsMap[standard.standardId] = standard;
    }

    for( var i = 0; i < discountStructures.length; i++) {
        var discountStructureData = discountStructures[i];
        discountStructureMap[discountStructureData.feeDiscountMetadata.discountStructureId] = discountStructureData;
    }
    // Reset cache
    discountStructure.dataCache = {};
    // Add new session values
    discountStructure.dataCache.discountStructureMap = discountStructureMap;
    discountStructure.dataCache.standards = standards;
    discountStructure.dataCache.standardsMap = standardsMap;
    discountStructure.dataCache.feeMap = feeMap;
    discountStructure.dataCache.feeHeadMap = feeHeadMap;
    discountStructure.dataCache.feeHeadConfigurationResponseList = feeHeadConfigurationResponseList;
    discountStructure.dataCache.configureNewStructure = {'institute' : {'selectedFees' : {}}, 'standard' : {'selectedStandards' : {}, selectedFees : {} }};
    discountStructure.dataCache.feeIdFeeHeadSelectOptionData = feeIdFeeHeadSelectOptionData;
  },

  createFeeHeadSelectMenu : function (feeHeadConfigurationResponseList , selectedFeeHeadId) {
    var feeHeadSelect = "<select class=\"form-control form-control-sm mandatory-field fee-head\"> <option value=\"\">select</option>";
    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++){
      var feeHeadId = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHeadId;
      var feeHeadName = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHead;
      if(selectedFeeHeadId == feeHeadId){
        feeHeadSelect += "<option value=\""+feeHeadId+"\" selected>"+feeHeadName+"</option>";
      }else{
        feeHeadSelect += "<option value=\""+feeHeadId+"\">"+feeHeadName+"</option>";
      }
    }
    feeHeadSelect += "</select>"
    return feeHeadSelect;
  },

  loadNewFeeStructureConfigureModal : function() {
    // Clear old dataType
    discountStructure.resetDiscountStructureModal();
    $("#discount-structure-config-modal").find(".modal-title").html("Configure New Discount Structure");
    $("#submit-fee-structure").html("Configure New Discount Structure");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");
    // Clear the older values
    discountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedFees = {};
    discountStructure.dataCache.selectedStructureId = null;

    discountStructure.populateSelectFeesDropdown(".institute-structure", discountStructure.INSTITUTE_ENTITY, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
    discountStructure.populateStandardSelectDropdown();
  },

  resetDiscountStructureModal : function () {
    $("#institute-fee-structure-container").html("<p class=\"institute-add-structure-hint-text pt-7 pb-7\"> Select fees to add new discount structure</p>");
    $("#discount-structure-name").val("");
    $("#discount-structure-name").attr("disabled",false);
    $("#discount-structure-config-modal").find(".modal-footer").css("display","");

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = discountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    discountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  populateStandardSelectDropdown : function () {
    var standardsMap = discountStructure.dataCache.standardsMap;
    var standardDropdownListContent = "";
    for(standardId in standardsMap){
      if(!(standardId in discountStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          standardDropdownListContent += "<a class=\"dropdown-item standard-select-option\" href=\"#\" id=\"" + standardId +"-select"+"\">" + standardsMap[standardId].standardName +"</a>";
      }
    }
    $(".class-fee-structure").find(".standard-select-dropdown").html(standardDropdownListContent);
    discountStructure.bindStandardSelectEvent();
  },

  bindSelectFeesEvent : function (entityParentContainerId, entityId, selectedFees) {
    $(entityParentContainerId).find('.fee-select-option').on('click', function (event) {
      //This is for keeping the fees drop-down open (https://embrate.atlassian.net/browse/PD-2535)
      event.stopPropagation();
      var feeId = $(this).attr("id").split("-select-")[0];
      var entityId = $(this).attr("id").split("-select-")[1];
      discountStructure.insertFeeDetailsCard(entityParentContainerId, entityId, feeId, selectedFees);
    });
  },

  bindStandardSelectEvent : function () {
    $('.class-fee-structure').find('.standard-select-option').on('click', function () {
      var standardId = $(this).attr("id").split("-select")[0];
      discountStructure.insertStandardDetailsCard(standardId);
    });
  },

  insertFeeDetailsCard : function (entityParentContainerId, entityId, feeId, selectedFees) {
    selectedFees[feeId] = true;
    $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:none;");

    var entityContainerId = discountStructure.createEntityContainerId(entityId);
    var feeContainerId = discountStructure.createFeeContainerId(entityId,feeId);
    var feeMap = discountStructure.dataCache.feeMap;
    var feeName = feeMap[feeId].feeName.toUpperCase();
    var feeDetailsCard = "<div class=\"fee-structure\"> <div class=\"card card-border text-center\" id=\""+feeContainerId+"\"> <p class=\"fee-id\" style=\"display:none;\">"+feeId+"</p> <div class=\"card-body\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-fee-structure-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h5 class=\"card-title\"> <strong>"+feeName+"</strong> </h5> <div style=\"padding-left:15%; padding-right:15%;\"> <table class=\"table table-borderless\"> <thead> <tr> <th scope=\"col\" class=\"w-50\">Fee Head</th> <th scope=\"col\">Amount</th> <th scope=\"col\">Percentage</th> <th scope=\"col\"></th> </tr> </thead> <tbody class=\"fee-structure-body\"> </tbody> </table> <button type=\"button\" class=\"btn btn-outline-secondary btn-sm add-fee-head-button\"> + Add More Fee Head</button> </div> </div> </div> </br> <div>";
    $("#"+entityContainerId).append(feeDetailsCard);
    discountStructure.bindFeeContainerEvents(entityParentContainerId, feeContainerId, entityId, feeId, selectedFees);
    discountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);

  },

  insertStandardDetailsCard : function (standardId) {
    discountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId] = true;
    $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");
    // var feeContainerId = discountStructure.createFeeContainerId(entityId,feeId);
    var standardMap = discountStructure.dataCache.standardsMap;
    var standardName = standardMap[standardId].standardName.toUpperCase();
    var standardContainerId = discountStructure.getStandardContainerId(standardId);
    var standardDetailsCard = "<div id=\""+standardContainerId+"\" class=\"card card-border standard-fee-structure\"> <p class=\"standard-id\" style=\"display:none;\">"+standardId+"</p> <div class=\"card-header card-header-color\" id=\""+standardId+"-heading\" data-toggle=\"collapse\" data-target=\"#collapse-"+standardId+"\" aria-expanded=\"true\" aria-controls=\"collapse-"+standardId+"\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-standard-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h2 class=\"mb-0\"> <button class=\"btn btn-link\" type=\"button\"> <strong>"+standardName+"</strong> </button> </h2> </div> <div id=\"collapse-"+standardId+"\" class=\"collapse\" aria-labelledby=\""+standardId+"-heading\" data-parent=\"#class-fee-structure-config-accordion\"> <div class=\"card-body\"> <div id=\""+standardId+"-fee-structure-container\" style=\"text-align:center;\"> <p class=\"institute-add-structure-hint-text black-color\"> Select fees to add new fee structure</p> <!-- Next card here --> </div> <br /> <div class=\"fee-select-dropdown-container\" style=\"float:right;\"> <div class=\"btn-group dropup\" style=\"width:150px;\"> <button type=\"button\" class=\"btn btn-info btn-sm dropdown-toggle\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"> Select Fees </button> <div class=\"dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right\"> </div> </div> </div> <br/> </div> </div> </div>";
    $("#class-fee-structure-config-accordion").append(standardDetailsCard);
    discountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId] = {};
    discountStructure.populateSelectFeesDropdown("#"+standardContainerId, standardId, discountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
    discountStructure.bindStandardContainerEvents(standardContainerId, standardId);
    discountStructure.populateStandardSelectDropdown();
  },

  createEntityContainerId : function (entityId) {
    return entityId + "-" + "fee-structure-container";
  },

  createFeeContainerId : function (entityId, feeId) {
    return entityId + "-" + feeId;
  },

  bindFeeContainerEvents : function (entityParentContainerId, feeContainerId, entityId, feeId, selectedFees) {
    $("#"+feeContainerId).find(".add-fee-head-button").on('click', function () {
      discountStructure.insertFeeHeadRow(feeContainerId, feeId, null, 0, "");
    });

    $("#"+feeContainerId).find(".delete-fee-structure-row").on('click', function () {
      $(this).closest(".fee-structure").remove();
      delete selectedFees[feeId];
      if(isEmpty(selectedFees)){
        $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:block;");
      }
      discountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);
    });

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = discountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    discountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  getStandardContainerId : function (standardId) {
      return standardId + "-container";
  },

  bindStandardContainerEvents : function (standardContainerId, standardId) {

    $("#"+standardContainerId).find(".delete-standard-row").on('click', function () {
      $(this).closest(".standard-fee-structure").remove();
      delete discountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId];
      if(isEmpty(discountStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;");
      }
      discountStructure.populateStandardSelectDropdown();
    });

  },

  insertFeeHeadRow  :function (feeContainerId, feeId , selectedFeeHeadId, feeHeadAmount, checked) {
    var feeHeadRow = "";
    if(selectedFeeHeadId == null){
      var feeHeadSelectOptionData = discountStructure.dataCache.feeIdFeeHeadSelectOptionData[feeId];
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"> <span class=\"slider round\"></span> </label> </td><td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }else{
      var feeHeadConfigurationResponseList = discountStructure.dataCache.feeHeadConfigurationResponseList;
      var feeHeadSelectOptionData =  discountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, selectedFeeHeadId);
      if(checked) {
        checked = "checked";
      }
      else {
        checked = "";
      }
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\" value=\""+feeHeadAmount+"\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"" + checked + " /><span class=\"slider round\"></span> </label> </td> <td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }

    $("#"+feeContainerId).find(".fee-structure-body").append(feeHeadRow);
    discountStructure.bindFeeHeadRowEvents(feeContainerId);
  },

  bindFeeHeadRowEvents : function (feeContainerId) {
      var previousSelectedValue = "";
      $("#"+feeContainerId).find(".fee-head").on('focus', function () {
        // Store the current value on focus and on change
        previousSelectedValue = $(this).find(':selected').val().trim();
      })
      .change(function () {
          var feeHeadId = $(this).find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          var presenceCount = 0;
          $(this).closest('tbody.fee-structure-body').find("tr").each(function() {
            var existingFeeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
            if(existingFeeHeadId == ""){
              return;
            }else if(feeHeadId == existingFeeHeadId){
              presenceCount++;
            }
          });
          if(presenceCount > 1){
            showErrorDialogBox("Fee Head " + $(this).find(':selected').text() + " already selected. Please fill amount in that");
            $(this).val(previousSelectedValue);
          }
      });

      $("#"+feeContainerId).find(".delete-fee-head-row").on('click', function () {
        $(this).closest("tr").remove();
      });

      $("#"+feeContainerId).find(".fee-head-amount").change(function() {
        var isPercentage = $(this).parent().parent().find(".is-percentage-switch").is(':checked');
        if(isPercentage) {
          var percentage = $(this).val();
          if(percentage > 100 || percentage <= 0) {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).val("");
            return;
          }
        } else {
          var amount = $(this).val();
          if(amount <= 0 && amount != "") {
            showErrorDialogBox("Amount should be a positive number.");
            $(this).val("");
            return;
          }
        }
      });

      $("#"+feeContainerId).find(".is-percentage-switch").change(function() {
        if($(this).is(':checked')) {
          var percentage = $(this).parent().parent().parent().find(".fee-head-amount").val();
          if((percentage > 100 || percentage <= 0) && percentage != "") {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).parent().parent().parent().find(".fee-head-amount").val("");
            return;
          }
        }
      });
  },

  submitDiscountStructure : function () {

    var invalid = validateMandatoryFields($("#discount-structure-config-modal"));
    if(invalid){
      showErrorDialogBoxWithExistingModalDetails("Please fill mandatory fields.", "#discount-structure-config-modal");
      return;
    }
    $("#discount-structure-config-modal").modal('toggle');
    var structureName = $("#discount-structure-name").val();
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var structureId = null;
    if(discountStructure.dataCache.selectedStructureId != null && discountStructure.dataCache.selectedStructureId.trim() != "" ){
      structureId = discountStructure.dataCache.selectedStructureId;
    }

    var instituteFeeIdFeeHeadsList =  discountStructure.getFeeStructureData('#institute-fee-structure-container');

    if(Array.isArray(instituteFeeIdFeeHeadsList) && !instituteFeeIdFeeHeadsList.length) {
      return;
    }
    var instituteFeeAssignmentPayload = {'feeEntity' : 'INSTITUTE', 'feeIdFeeHeadsList' : instituteFeeIdFeeHeadsList};

    var entityFeeAssignmentPayloadList = [];
    entityFeeAssignmentPayloadList.push(instituteFeeAssignmentPayload);

    var metadata = {'title' : structureName};
    var discountAssignmentStructurePayload = [{'structureId': structureId, 'structureName' : structureName, 'discountStructureType' : 'CUSTOM', 'metadata' : metadata, 'entityFeeAssignmentPayloads' : entityFeeAssignmentPayloadList}];

    var submitStructureURL = "/fees/add-discount-structure/"+academicSessionId;
    if(structureId != null){
      submitStructureURL = "/fees/update-discount-structure/"+structureId+"/"+academicSessionId;
    }
    ajaxClient.post(submitStructureURL ,{'discountAssignmentStructurePayload':JSON.stringify(discountAssignmentStructurePayload)}, function(data){
      $("#discount-structure-status-modal-container").html(data);
      $("#fee-structure-status-modal").modal('toggle');
      discountStructure.loadDiscountStructureConfigurationPage();
    });

  },

  getFeeStructureData : function (entityFeeStructureContainerId) {
    var feeIdFeeHeadsList = [];
    $(entityFeeStructureContainerId).find(".fee-structure").each( function () {
      var feeId = $(this).find(".fee-id").text().trim();
      var feeHeadAmountList = [];
      var feeHeads = {};
      $(this).find('tbody.fee-structure-body').find('tr').each(function () {
          var feeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          if(feeHeadId in feeHeads){
            // Need to handle properly to display relevent error
            return;
          }
          var feeHeadAmount = $(this).find(".fee-head-amount").val();
          var isPercentage = $(this).find(".toggle-switch").is(':checked');
          if(feeHeadAmount === "" || feeHeadAmount === undefined) {
            showErrorDialogBoxWithExistingModalDetails("Amount cannot be empty", "#discount-structure-config-modal");
            return [];
          }
          feeHeadAmountList.push({'feeHeadId' : feeHeadId, 'amount' : feeHeadAmount, 'isPercentage' : isPercentage});
      });
      if(feeHeadAmountList.length > 0){
        feeIdFeeHeadsList.push({'feeId' : feeId, 'feeHeadAmountList' : feeHeadAmountList});
      }
    });

    if(Array.isArray(feeIdFeeHeadsList) && !feeIdFeeHeadsList.length) {
      showErrorDialogBoxWithExistingModalDetails("Please select atleast one fees to create discount structure", "#discount-structure-config-modal");
      return [];
    }

    return feeIdFeeHeadsList;
  },

  fillFeeDiscountStructureModal : function (structureId, viewOnly) {

    $("#discount-structure-name").attr("disabled",false);
    // $("#discount-structure-config-modal").find(".modal-footer").css("display","block");

    discountStructure.dataCache.selectedStructureId = structureId;
    var discountStructureMap = discountStructure.dataCache.discountStructureMap;
    if(!(structureId in discountStructureMap)){
      showErrorDialogBox("Invalid Fee Discount Structure.")
      return;
    }
    var selectedDiscountStructure = discountStructureMap[structureId];
    discountStructure.resetDiscountStructureModal();
    $("#fee-structure-academic-session").val(academicSessionHandler.getSelectedSessionDisplayName());
    $("#discount-structure-name").val(selectedDiscountStructure.feeDiscountMetadata.name);
    // $("#fee-structure-types").val(selectedDiscountStructure.discountStructureType);
    // $("#fee-structure-types").attr("disabled",true);

    $("#discount-structure-config-modal").find(".modal-title").html("Update Discount Structure");
    $("#submit-discount-structure").html("Update Discount Structure");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");

    discountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedFees = {};

    var instituteEntitySelectedFees = {};
    var standradEntitySelectedFees = {};
    for(var i = 0; i < selectedDiscountStructure.feeDiscountEntityStructures.length; i++){
      var entityFeeAssignment = selectedDiscountStructure.feeDiscountEntityStructures[i];
      if(entityFeeAssignment.feeEntity == 'INSTITUTE'){
        for(var j = 0; j < entityFeeAssignment.feeIdDiscountStructures.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment.feeIdDiscountStructures[j];
          var feeId = feeIdFeeHeadDetails.feeConfigurationBasicInfo.feeId;
          instituteEntitySelectedFees[feeId] = feeIdFeeHeadDetails;
        }
      }else if(entityFeeAssignment.feeEntity == 'CLASS'){
        var standardId = entityFeeAssignment.entityId;
        standradEntitySelectedFees[standardId] = {};
        for(var j = 0; j < entityFeeAssignment.feeIdDiscountStructures.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment.feeIdDiscountStructures[j];
          var feeId = feeIdFeeHeadDetails.feeConfigurationBasicInfo.feeId;
          standradEntitySelectedFees[standardId][feeId] = feeIdFeeHeadDetails;
        }
      }
    }


    for(feeId in instituteEntitySelectedFees){
        discountStructure.insertFeeDetailsCard(".institute-structure", discountStructure.INSTITUTE_ENTITY, feeId, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
        var feeContainerId = discountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,feeId);
        for(var i = 0; i < instituteEntitySelectedFees[feeId].feeHeadDiscountStructures.length; i++){
          var feeHeadId = instituteEntitySelectedFees[feeId].feeHeadDiscountStructures[i].feeHeadConfiguration.feeHeadId;
          var feeHeadAmount = instituteEntitySelectedFees[feeId].feeHeadDiscountStructures[i].amount;
          var checked = instituteEntitySelectedFees[feeId].feeHeadDiscountStructures[i].percent;
          discountStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount, checked);
        }
    }

    for(standardId in standradEntitySelectedFees){
      discountStructure.insertStandardDetailsCard(standardId);
      var standardContainerId = discountStructure.getStandardContainerId(standardId);
      for(feeId in standradEntitySelectedFees[standardId]){
          discountStructure.insertFeeDetailsCard("#"+standardContainerId, standardId, feeId, discountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
          var feeContainerId = discountStructure.createFeeContainerId(standardId, feeId);
          for(var i = 0; i < standradEntitySelectedFees[standardId][feeId].feeHeadDiscountStructures.length; i++){
            var feeHeadId = standradEntitySelectedFees[standardId][feeId].feeHeadDiscountStructures[i].feeHeadConfiguration.feeHeadId;
            var feeHeadAmount = standradEntitySelectedFees[standardId][feeId].feeHeadDiscountStructures[i].amount;
            var checked = instituteEntitySelectedFees[feeId].feeHeadDiscountStructures[i].percent;
            discountStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount, checked);
          }
      }
    }
    // Needed in case standradEntitySelectedFees or instituteEntitySelectedFees is empty
    discountStructure.populateSelectFeesDropdown(".institute-structure", discountStructure.INSTITUTE_ENTITY, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
    discountStructure.populateStandardSelectDropdown();

    if(viewOnly){
        $(".standard-select-dropdown-container").css("display","none");
        $(".fee-select-dropdown-container").css("display","none");
        $(".add-fee-head-button").css("display","none");
        $(".fee-head").attr("disabled","true");
        $(".fee-head-amount").attr("disabled","true");
        $(".toggle-switch").attr("disabled","true");
        $(".delete-fee-head-row").remove();
        $(".delete-fee-structure-row").remove();
        $(".delete-standard-row").remove();
        $("#discount-structure-config-modal").find(".modal-title").html("View Discount Structure");
        $("#discount-structure-name").attr("disabled",true);
        $("#fee-structure-types").attr("disabled",true);
        $("#discount-structure-config-modal").find(".modal-footer").css("display","none");
    }
  },

  populateDeleteStructureModal : function (structureId) {
    var selectedDiscountStructure = discountStructure.dataCache.discountStructureMap[structureId];
    discountStructure.dataCache.discountStructureID = structureId;
    $("#delete-fee-structure-modal-text").html("Do you want to delete discount Structure : "+selectedDiscountStructure.feeDiscountMetadata.name +"?");
  },

  deleteFeeStructure : function () {
     $("#delete-fee-structure-modal").modal('toggle');
     var structureId = discountStructure.dataCache.discountStructureID;
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     ajaxClient.post("/fees/delete-discount-structure/"+structureId+"/"+academicSessionId, {}, function(data){
       $("#discount-structure-status-modal-container").html(data);
       $("#fee-structure-status-modal").modal('toggle');
       discountStructure.loadDiscountStructureConfigurationPage();
     });
   }

};

var graphLoadTimer; //timer identifier
var graphLoadInterval = 100; //time in ms


function getFeesHomePageGraphDetails() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/home-graph/"+academicSessionId, function(data) {
        $("#home-page-graph-area").html(data);
        drawHomeCharts();
    });
}

function graphReadyToRender() {
    graphLibraryLoaded = true;
}

function drawHomeCharts() {
    displayLoader();
    var duration = 7;
    // var transaction_stats_str = $("#home\\.chart_data").text().trim();
    // if(transaction_stats_str == ""){
    //   hideLoader();
    //   return
    // }
    // var transaction_stats = JSON.parse(transaction_stats_str);
    drawPieChart();
    hideLoader();
}

function drawPieChart() {
    if (!graphLibraryLoaded) {
      // console.log("Not loaded");
        clearTimeout(graphLoadTimer);
        typingTimer = setTimeout(function() {
            drawPieChart()
        }, graphLoadInterval);
        return;
    }
    // console.log("------- loaded");
    var statsJson = $("#home-page-stats").text().trim();
    var stats = JSON.parse(statsJson);
    var data = google.visualization.arrayToDataTable([
          ['Amount Type', 'Value'],
          ['Total Collected Fees', stats.collectedAmount],
          ['Total Discount Fees',  stats.discountAmount],
          ['Total Due Fees', stats.dueAmount]
        ]);

        var options = {
          title: 'Total Fees Summary',
          is3D: true,
        };
        if(stats.classFeePaymentAggregatedDatas.length > 0){
          var chart = new google.visualization.PieChart(document.getElementById('piechart_3d'));
          chart.draw(data, options);
        }

}

var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;
var PAST_DAYS = 365 * 5;

$(document).ready(function() {
  menuLoader.registerSidebarMenu();

  var urlParams = new URLSearchParams(window.location.search);
  actionType = urlParams.get(ACTION_TYPE);

  if(actionType != null && actionType != undefined && actionType != "") {
      if (actionType === "payments"){
        $("#paymentNav").parent().addClass("active");
        loadPaymentPage();
      } else if (actionType === "send-reminders"){
        $("#sendReminderNav").parent().addClass("active");
        reminders.loadSendReminderPage();
      }

    //remove params from url
      var url_string = window.location.href;
      url = removeURLParameter(url_string, ACTION_TYPE)
      let stateObj = { id: "100" };
      window.history.replaceState(stateObj, url_string, url);

  }

  paymentReminder.readPaymentState();

});

var menuLoader = {

  registerSidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.registerHomeMenu();
      menuLoader.registerFeeConfigurationMenu();
      menuLoader.registerFeeStructureConfigurationMenu();
      menuLoader.registerDiscountStructureConfigurationMenu();
      menuLoader.registerFeeHeadConfigurationMenu();
      menuLoader.registerFeeAssignmentMenu();
      menuLoader.registerDiscountAssignmentMenu();
      menuLoader.registerPaymentMenu();
      menuLoader.registerPaymentTransactionsMenu();
      menuLoader.registerFollowUpMenu();
      menuLoader.registerReportsMenu();
      menuLoader.registerSendReminderMenu();
      menuLoader.registerNotificationHistoryMenu();
  },

  registerHomeMenu : function () {
      $('#statisticsNav').on('click', function() {
          statistics.loadHomePage();
      });
  },

  registerFeeConfigurationMenu : function () {
      $('#feeConfigurationNav').on('click', function() {
          loadFeeConfigurationPage();
      });
  },

  registerFeeHeadConfigurationMenu : function () {
      $('#feeHeadConfigNav').on('click', function() {
          loadFeeHeadConfigurationPage();
      });
  },

  registerFeeAssignmentMenu : function () {
      $('#feeAssignmentNav').on('click', function() {
          feeAssignment.loadMainScreen();
      });
  },

  registerDiscountAssignmentMenu : function () {
      $('#discountAssignmentNav').on('click', function() {
          discountAssignment.loadDiscountAssignmentPage();
      });
  },

  registerPaymentMenu : function () {
      $('#paymentNav').on('click', function() {
          loadPaymentPage();
      });
  },

  registerSendReminderMenu : function () {
      $('#sendReminderNav').on('click', function() {
          reminders.loadSendReminderPage();
      });
  },

  registerNotificationHistoryMenu : function () {
      $('#notificationHistoryNav').on('click', function() {
          reminders.loadNotificationHistoryHomePage();
      });
  },

  registerPaymentTransactionsMenu : function () {
      $('#transactionsNav').on('click', function() {
          loadPaymentTransactionsMenu();
      });
  },

  registerFollowUpMenu : function () {
      $('#followupsNav').on('click', function() {
          loadfollowUpsMenu();
      });
  },

  registerReportsMenu : function () {
      $('#reportsNav').on('click', function() {
          feeReports.loadReportsMenu();
      });
  },

  registerFeeStructureConfigurationMenu : function () {
      $('#feeStructureConfigurationNav').on('click', function() {
          feeStructure.loadFeeStructureConfigurationPageWithoutSession();
      });
  },

  registerDiscountStructureConfigurationMenu : function () {
      $('#discountStructureConfigurationNav').on('click', function() {
          discountStructure.loadDiscountStructureConfigurationPageWithoutSession();
      });
  },

};

var statistics =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(statistics.loadHomePageForSession);
        statistics.displayDashboardContent();
        statistics.onClickFeeStatsSwitchButton();
        $('[data-toggle="tooltip"]').tooltip();
        statistics.onClickFeeStatsType();
    },

    onClickFeeStatsType : function () {
      $("#fee-stats-type").change(function() {
        var feeStatsType = $(this).val();
        if(feeStatsType === "TODAY") {
          $("#fees-stats-date").attr("style", "display:inline-block;");
          statistics.loadHomePageForDate();
        } else {
          $("#fees-stats-date").attr("style", "display:none;");
          statistics.loadHomePageForSession();
        }
      });

      $("input#fees-stats-date").daterangepicker({
        autoApply : true,
        singleDatePicker: true,
        showDropdowns: true,
        minDate: moment().startOf("day").subtract(PAST_DAYS, "days"),
        maxDate: moment().startOf("day"),
        locale: {
          format: inputDatePickerFormat,
          cancelLabel: 'Clear'
        },
        onSelect: function(dateText) {
          $(this).change();
        }
      }).on("change", function() {
          statistics.loadHomePageForDate();
      });

    },

    onClickFeeStatsSwitchButton : function () {
      $("#fee-stats-switch-button").on('click', function () {
        var currentCardViewType = $("#current-card-view-type").text();
        if(currentCardViewType === TABULAR) {
          $("#current-card-view-type").text(GRAPHICAL);
          $("#bar-graph-header-text").text("Class Fee Distribution (Graphical View)");
          $(this).html("Tabular View");
          statistics.loadStudentCountChart();
          $("#tabularview-class-fee-distribution").attr("style", "display:none;");
          $("#chartjs-class-fee-distribution").attr("style", "display:block;");
        } else {
          $("#current-card-view-type").text(TABULAR);
          $("#bar-graph-header-text").text("Class Fee Distribution (Tabular View)");
          $(this).html("Graphical View");
          statistics.loadBarGraphTabularView();
          $("#chartjs-class-fee-distribution").attr("style", "display:none;");
          $("#tabularview-class-fee-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
        }
      });
    },

    loadHomePage : function () {
      ajaxClient.get("/fees/home", function(data) {
          $("#main-content").html(data);
          statistics.initHomePage();
          $("#fee-stats-type").val("SESSION");
      });
    },

    loadHomePageForSession : function () {
      $("#fees-stats-date").attr("style", "display:none;");
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/session-home/"+academicSessionId, function(data) {
          $("#fees-dashboard-session-content").html(data);
          statistics.displayDashboardContent();
          statistics.onClickFeeStatsSwitchButton();
          $('[data-toggle="tooltip"]').tooltip();
          $("#fee-stats-type").val("SESSION");
      });
    },

    loadHomePageForDate : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      // var date = (getDate(new Date()).getTime()/1000).toFixed(0);
      var date = getDate($('input#fees-stats-date').val()).getTime()/1000;
      ajaxClient.get("/fees/date-wise-fee-stats/"+academicSessionId+"/" + date, function(data) {
          $("#fees-dashboard-session-content").html(data);
          statistics.displayDashboardContent();
          statistics.onClickFeeStatsSwitchButton();
          $('[data-toggle="tooltip"]').tooltip();
      });
    },

    refreshHomePage : function () {
        statistics.loadHomePageForSession();
    },

    loadBarGraphTabularView : function () {
      var classFeeStats = readJson("#home-page-fees-stats");
      if(classFeeStats == null || classFeeStats.classFeePaymentAggregatedDatas == null){
        return;
      }
      var labelArr = [];
      var totalFeeDataArr = []
      var collectedFeeDataArr = []
      var discountFeeDataArr = []
      var dueFeeDataArr = []
      var dataArr = []
      var total = 0;
      for( var i = 0 ; i < classFeeStats.classFeePaymentAggregatedDatas.length; i++){
        var classPayload = classFeeStats.classFeePaymentAggregatedDatas[i];
        labelArr.push(classPayload.standardName);
        totalFeeDataArr.push(classPayload.assignedAmount);
        collectedFeeDataArr.push(classPayload.collectedAmount);
        discountFeeDataArr.push(classPayload.discountAmount);
        dueFeeDataArr.push(classPayload.dueAmount);
      }

      statistics.renderBarGraphTabularView(labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr);

    },

    renderBarGraphTabularView : function (labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr) {
      var tabularViewHtml = "<table id=\"datatables-reponsive\" class=\"table table-bordered table-striped datatables-reponsive-table\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">Total (" + RUPEE_SYMBOL + ")</th><th scope=\"col\">Collected (" + RUPEE_SYMBOL + ")</th><th scope=\"col\">Discounted (" + RUPEE_SYMBOL + ")</th><th scope=\"col\">Due (" + RUPEE_SYMBOL + ")</th></tr></thead><tbody>";
      var grandTotalFees = 0;
      var grandCollectedAmount = 0;
      var grandDiscountAmount = 0;
      var grandDueAmount = 0;
      for(var i = 0; i < labelArr.length; i++) {

        var className = labelArr[i];
        var totalFees = formatINRCurrency(parseFloat(totalFeeDataArr[i].toFixed(2)));
        grandTotalFees += parseFloat(totalFeeDataArr[i].toFixed(2));
        var collectedAmount = formatINRCurrency(parseFloat(collectedFeeDataArr[i].toFixed(2)));
        grandCollectedAmount += parseFloat(collectedFeeDataArr[i].toFixed(2));
        var discountAmount = formatINRCurrency(parseFloat(discountFeeDataArr[i].toFixed(2)));
        grandDiscountAmount += parseFloat(discountFeeDataArr[i].toFixed(2));
        var dueAmount = formatINRCurrency(parseFloat(dueFeeDataArr[i].toFixed(2)));
        grandDueAmount += parseFloat(dueFeeDataArr[i].toFixed(2));
        tabularViewHtml += "<tr><th scope=\"row\">" + className + "</th><td>" + totalFees + "</td><td>" + collectedAmount +"</td><td>" + discountAmount + "</td><td>" + dueAmount +"</td></tr>";

      }
      tabularViewHtml += "<tr><th scope=\"row\">" + "Grand Total" + "</th><td><b style=\"font-weight:bold\">" + grandTotalFees +  "</b></td><td><b style=\"font-weight:bold\">" + grandCollectedAmount +  "</b></td><td><b style=\"font-weight:bold\">" + grandDiscountAmount +  "</b></td><td><b style=\"font-weight:bold\">" + grandDueAmount + "</b></td><tr>";
      tabularViewHtml += "</tbody></table>";
      $("#tabularview-class-fee-distribution").html(tabularViewHtml);

    },

   displayDashboardContent : function () {

     statistics.loadStudentCountChart();

     var classFeeStats = readJson("#home-page-fees-stats");
     if(classFeeStats == null || classFeeStats.classFeePaymentAggregatedDatas == null){
       return;
     }

     var pielabelArr = ["", "", ""];
     var totalAmount = classFeeStats.assignedAmount;
     var collectedAmountPercentage = (classFeeStats.collectedAmount / classFeeStats.assignedAmount) * 100;
     var discountAmountPercentage = (classFeeStats.discountAmount / classFeeStats.assignedAmount) * 100;
     var dueAmountPercentage = (classFeeStats.dueAmount / classFeeStats.assignedAmount) * 100;
     var pieDataArr = [collectedAmountPercentage.toFixed(2), discountAmountPercentage.toFixed(2), dueAmountPercentage.toFixed(2)]
     statistics.renderFeeCollectionPieChart(pielabelArr, pieDataArr);
   },

   loadStudentCountChart : function () {

     var classFeeStats = readJson("#home-page-fees-stats");
     if(classFeeStats == null || classFeeStats.classFeePaymentAggregatedDatas == null){
       return;
     }
     var labelArr = [];
     var totalFeeDataArr = []
     var collectedFeeDataArr = []
     var discountFeeDataArr = []
     var dueFeeDataArr = []
     var dataArr = []
     var total = 0;
     for( var i = 0 ; i < classFeeStats.classFeePaymentAggregatedDatas.length; i++){
       var classPayload = classFeeStats.classFeePaymentAggregatedDatas[i];
       labelArr.push(classPayload.standardName);
       totalFeeDataArr.push(classPayload.assignedAmount);
       collectedFeeDataArr.push(classPayload.collectedAmount);
       discountFeeDataArr.push(classPayload.discountAmount);
       dueFeeDataArr.push(classPayload.dueAmount);
     }

     statistics.renderStudentCountChart(labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr);

   },

   renderStudentCountChart : function (labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr ) {
     // Bar chart
     new Chart($("#chartjs-class-fee-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [{
						label: "Collected Fees",
						backgroundColor: window.theme.success,
						borderColor: window.theme.success,
						hoverBackgroundColor: window.theme.success,
						hoverBorderColor: window.theme.success,
						data: collectedFeeDataArr,
						barPercentage: .325,
						categoryPercentage: .5
					},{
           label: "Discounted Fees",
           backgroundColor: window.theme.warning,
           borderColor: window.theme.warning,
           hoverBackgroundColor: window.theme.warning,
           hoverBorderColor: window.theme.warning,
           data: discountFeeDataArr,
           barPercentage: .325,
           categoryPercentage: .5
         },
         {
          label: "Due Fees",
          backgroundColor: window.theme.danger,
          borderColor: window.theme.danger,
          hoverBackgroundColor: window.theme.danger,
          hoverBorderColor: window.theme.danger,
          data: dueFeeDataArr,
          barPercentage: .325,
          categoryPercentage: .5
        }
         ]
       },
       options: {
         maintainAspectRatio: false,
         cornerRadius: 15,
         legend: {
           display: false
         },
         scales: {
           yAxes: [{
             ticks: {
                  beginAtZero: true
             },
             gridLines: {
               display: false
             },
             stacked: false,
             stacked: true,
           }],
           xAxes: [{
             stacked: false,
             gridLines: {
               color: "transparent"
             },
             stacked: true,
           }]
         }
       }
     });
   },

   renderFeeCollectionPieChart : function (labelArr, dataArr) {
     new Chart($("#chartjs-fee-collection-pie"), {
       type: "pie",
       data: {
         datasets: [{
           data: dataArr,
           backgroundColor: [
             window.theme.success,
             window.theme.warning,
             window.theme.danger,
             window.theme.info,
             "#108F2B",
             "#C08143 ",
             "#DCDF69",
             "#719E90",
             "#A8B9DF ",
             "#B74034",
             "#F196CD",
             "#6486B9",
             "#5551FA",
             "#E8F697",
             "#0591F6",
             "#4C6C42",
             "#442BC6",
           ],
           borderWidth: 5,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: true,
         cutoutPercentage: 70,
         legend: {
           display: false
         },
         tooltips: {
          enabled: true,
          mode: 'single',
          displayColors: false,
          callbacks: {
            label: function(tooltipItems, data) {
              return  ' ' + data.datasets[0].data[tooltipItems.index] + '% ';
            }
          }
        }
       }
     });
   },
};

// --------------------------------Fee Follow Up Trail FE ------------------------------------------------------
function loadfollowUpsMenu() {
  ajaxClient.get("/fees/fee-followup-home",function(data) {
      $("#main-content").html(data);
      getFeeFollowUpDetails(true,"ACTIVE");
      initDate(5 * 365);
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
  });
}
// --------------------------------------------------------------------------------------------------------

function loadPaymentTransactionsMenu() {
  ajaxClient.get("/fees/all-payment-transactions-home", function(data) {
      $("#main-content").html(data);
      getPaymentTransactions(true, "ACTIVE");
      feeTransaction.dataCache['status'] = "ACTIVE";
      academicSessionHandler.bindSessionChangeEvent(loadPaymentTransactionsForSession);
      feeTransactionSearchCallback();
      bindPaymentTransactionClickEvents();
      initDate(5 * 365);
  });
}

function feeTransactionSearchCallback(){
  $('#searchTransactions').on('click', function () {
    getPaymentTransactions(true, "ACTIVE");
  });
  $("#searchTransactionsInput").on('keyup', function (e) {
    if (e.keyCode == 13) {
      getPaymentTransactions(true, "ACTIVE");
    }
  });
}

function getPaymentTransactions(freshSearch, status) {
  feeTransaction.dataCache['status'] = status;
  var search_text = $("#searchTransactionsInput").val();
  var page_number = $('.page-item.active').find('.page-number').text().trim();
  if(freshSearch){
      page_number = 1;
  }
  var itemsPerPage = $('#items-per-page').val();
  if(page_number == null || page_number == ''){
    page_number = 1;
  }
  var offset = (page_number - 1)*itemsPerPage;

  var sessionId = academicSessionHandler.getSelectedSessionId();

  $("#searchTransactionsResult").html("");
  ajaxClient.get("/fees/payment-transactions/session/"+sessionId+"/"+status+"/"+offset+"/"+itemsPerPage+"?text="+search_text, function(data) {
      $("#searchTransactionsResult").html(data);
      var screenHeight = $("#sidebar").height() - $("#searchTransactionsResult").position().top - 300;
      $('#fee-payment-transactions-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
      var response = JSON.parse($("#pagination-info").text().trim());
      $('#items-per-page').val(response.itemsPerPage);
      $('.page-item').removeClass('active');
      var pageNumber = (response.offset/response.itemsPerPage) + 1;
      $('#page-number-'+page_number).addClass('active');
      if(status == 'CANCELLED'){
        $("#nav-cancel-transaction-tab").addClass('active');
        $("#nav-active-transaction-tab").removeClass('active');
        $("#cancel-transaction-content").addClass('show active');
        $("#active-transaction-content").removeClass('show active');
      }
      bindPaymentTransactionClickEvents();
      feeTransaction.initPagination();
      // $('.datatables-reponsive').DataTable( {
      //   searching: true,
      //   bPaginate: true,
      //   ordering: false,
      // });
  });
}

// --------------------------------Fee Follow Up Trail FE ------------------------------------------------------

function getFeeFollowUpDetails(freshStart, status){

    var sessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/student-fee-follow-up-details/"+sessionId, function(data) {
        $("#feeFollowUpResult").html(data);
        $('.datatables-reponsive').DataTable( {
          searching: true,
          bPaginate: true,
          ordering: false,
        });
    });
}

function closeTransactionFeeFollowUpDetails(studentId){
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close All Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong style=\"color:red\">Are you sure that you want to close the all followup trail for this student ?</strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentTransactionFeeFollowupTrail('true','INACTIVE')\">Yes, Close All</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

function closeStudentTransactionFeeFollowupTrail(isStudent,status){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var studentId = $("#delete-student-follow-up-id").text().trim();
      if(studentId == ""){
        showErrorDialogBox("Invalid Follow Up to cancel");
        return;
      }
      $("#fee-follow-up-close-modal").modal('toggle');
        ajaxClient.post("/fees/close-fee-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+studentId, {}, function(data) {
            $("#fee-followup-trail-transaction-status-modal-container").html(data);
            $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
            loadStudentFollowUpDetails(studentId)
        });
}

function addFeeTransactionFollowUpDetails(studentId){
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
      $("p#student-follow-up-id").text(studentId);
      $("#next-transaction-fee-follow-up-date").val("");
      $("#add-fee-transaction-follow-up-modal").modal('toggle');
}

function backButton(){
  getFeeFollowUpDetails();
}

function closeFeeTransactionFollowUpDetails(followUpId,studentId,isStudent){
    var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
    var modalBody = "<strong>Are you sure that you want to close this followup trail ?</strong>";
    var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentTransactionSingleFeeFollowupTrail('false','INACTIVE')\">Yes, Close It</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
    $("#modal-header").html(modalHeader);
    $("#modal-body").html(modalBody);
    $("#modal-footer").html(modalFooter);
    $("p#delete-student-follow-up-id").text(followUpId);
    $("p#student-follow-up-id").text(studentId);
    $("#fee-follow-up-close-modal").modal('toggle');
}

function closeStudentTransactionSingleFeeFollowupTrail(isStudent,status){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
      $("#fee-follow-up-close-modal").modal('toggle');
      ajaxClient.post("/fees/close-fee-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+followUpId, {}, function(data) {
        $("#fee-followup-trail-transaction-status-modal-container").html(data);
        $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
        loadStudentFollowUpDetails(studentId);
      });
}

function updateStudentTransactionFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#update-transaction-fee-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#update-transaction-entity-name").val();
      var followUpDate = getDate($('#update-transaction-fee-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#update-transaction-contact-person-name").val();
      var followUpType = $("select#update-transaction-follow-up-type").val();
      var conversation = $("#update-transaction-conversation").val();
      var nextFollowUpDate = $('#update-transaction-next-fee-follow-up-date').val();
      if(nextFollowUpDate!=""){
         nextFollowUpDate = getDate($('#update-transaction-next-fee-follow-up-date').val());
         nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }
      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#update-transaction-fee-follow-up-modal").modal('toggle');
        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#update-transaction-amount").val();
      var followUpStatus = status;
      var followUpId = $("#update-student-follow-up-id").text();
      var entityId = $("#student-id").text();
      var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#update-transaction-fee-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/update-fee-followup-trail/"+sessionId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
        $("#fee-followup-trail-transaction-status-modal-container").html(data);
        $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
            loadStudentFollowUpDetails(entityId);
     });
}

function deleteTransactionFeeFollowUpDetails(followUpId,studentId){

      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong color=\"red\">Are you sure that you want to Delete this followup trail ?</strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"deleteStudentTransactionFeeFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(followUpId);
      $("p#student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

function deleteStudentTransactionFeeFollowupTrail(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
    $("#fee-follow-up-close-modal").modal('toggle');
    ajaxClient.post( "/fees/delete-fee-followup-trail/"+academicSessionId+"/"+followUpId, {}, function(data) {
        returnToPaymentMainScreenDisplay();
        $("#fee-followup-trail-transaction-status-modal-container").html(data);
        $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
        loadStudentFollowUpDetails(studentId);
    });
}


function editTransactionFeeFollowUpDetails(value){
     initDateWithYearRange("-10:+10", true);
     initPastDateById("fee-transaction-follow-up-date", 4000);
     var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
     $("#update-transaction-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
     $("#update-transaction-fee-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
     $("#update-transaction-follow-up-mode").html("<option>"+followUpPayload.followUpMode+"</option>");
     $("#update-transaction-contact-person-name").val(followUpPayload.contactPersonName);
     $("#update-transaction-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
     $("#update-transaction-conversation").val(followUpPayload.conversation);

     if(followUpPayload.nextFollowUpDate != null){
       $("#update-transaction-next-fee-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
     }
     else{
        $("#update-transaction-next-fee-follow-up-date").val("");
     }

     $("#update-transaction-amount").val(followUpPayload.amount);
     $("#update-student-follow-up-id").text(followUpPayload.followUpId);
     $("#student-id").text(followUpPayload.entityId);
     $("#update-transaction-fee-follow-up-modal").modal('toggle');
}

function addTransactionStudentFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#add-fee-transaction-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#entity-transaction-name").val();
      var followUpDate = getDate($('#fee-transaction-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#contact-person-transaction-name").val();
      var followUpType = $("select#follow-up-transaction-type").val();
      var conversation = $("#transaction-conversation").val();
      var nextFollowUpDate = getDate($('#next-transaction-fee-follow-up-date').val());
      if(nextFollowUpDate != null){
        nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }
      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#add-fee-transaction-follow-up-modal").modal('toggle');
        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#amount-transaction").val();
      var followUpStatus = status;
      var entityId = $("#student-follow-up-id").text();
      var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#add-fee-transaction-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/add-fee-followup-trail/"+sessionId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
          $("#fee-followup-trail-transaction-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
          loadStudentFollowUpDetails(entityId);
     });
}

function loadStudentFollowUpDetails(studentId){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/get-follow-up-details/"+academicSessionId+"/"+studentId,function(data){
            $("#feeFollowUpResult").html(data);
      });
}

// --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

function loadPaymentTransactionsForSession() {
  getPaymentTransactions(true, "ACTIVE")
}

function bindPaymentTransactionClickEvents() {
  $(".view-payment-transaction").on('click', function () {
      var transactionId = $(this).attr("id").trim();
      showPaymentDetails(transactionId);
  });
  $(".invoice-payment-transaction").on('click', function () {
      var transactionId = $(this).attr("id").trim();
      generateFeePaymentInvoice(transactionId);
  });
  $(".print-payment-transaction").on('click', function () {
      var transactionId = $(this).attr("id").trim();
      printFeePaymentInvoice(transactionId);
  });
  $(".cancel-payment-transaction").on('click', function () {
      var transactionId = $(this).attr("id").trim();
      showCancelPaymentDetails(transactionId);
  });
}

function generateFeePaymentInvoice(transactionId) {
    window.open(baseURL + "/fees/invoice/" + transactionId+"/pdf-summary", '_blank');
}

function generateBulkFeePaymentInvoice(transaction_ids_str) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    window.open(baseURL + "/fees/bulk-invoice/pdf-summary/" + academicSessionId + "?transaction_ids_str=" + transaction_ids_str, '_blank');
}

function printFeePaymentInvoice(transactionId) {
    printJS({printable: baseURL + "/fees/invoice/" + transactionId+"/pdf-summary", type:'pdf', showModal:true});
}

function printBulkFeePaymentInvoice(transaction_ids_str) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    printJS({printable: baseURL + "/fees/bulk-invoice/pdf-summary/" + academicSessionId + "?transaction_ids_str=" + transaction_ids_str, type:'pdf', showModal:true});
}

function generateWalletInvoice(transactionId) {
  window.open(baseURL + "/fees/wallet-invoice/" + transactionId+"/pdf", '_blank');
}


function showPaymentDetails(transactionId) {

  ajaxClient.get("/fees/payment-transaction-details/"+transactionId, function(data) {
      $("#fee-payment-transaction-detail-modal-container").html(data);
      $("#confirm-cancel-transaction-button").remove();
      $("#fee-payment-transaction-view-modal").modal('toggle');

  });
}

function showCancelPaymentDetails(transactionId) {

  ajaxClient.get("/fees/payment-transaction-details/"+transactionId, function(data) {
      $("#fee-payment-transaction-detail-modal-container").html(data);
      $("#confirm-cancel-transaction-button").attr("style", "display:block");
      $("#fee-payment-transaction-view-modal").modal('toggle');

  });
}

function cancelTransaction() {
  var transactionId = $("#cancel-transaction-id").text().trim();
  if(transactionId == ""){
    showErrorDialogBox("Invalid Transaction to cancel");
    return;
  }
  var cancelAt = new Date().getTime() / 1000;
  var cancelRemarks = $("#payment-cancel-remarks").val().trim();
  var feeCancelledPayload = {'instituteId' : null, 'transactionId' : transactionId, 'cancelBy' : null, 'cancelAt' : cancelAt, 'cancelRemarks' : cancelRemarks};

  $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');

  ajaxClient.post( "/fees/cancel-payment-transaction", {'feeCancelledPayload' : JSON.stringify(feeCancelledPayload)}, function(data) {

      $("#fee-payment-status-modal-container").html(data);
      $("#fee-payment-status-modal").modal({backdrop: 'static', keyboard: false});
  });
}


function sendEmails(email) {
  //
  baseURL2 = baseURL;
  if(baseURL =="http://127.0.0.1:9123"){
    baseURL2 = "http://127.0.0.1";
  }
  ajaxClient.get(baseURL2 + ":8080/data-server/2.0/fee-payment/due-payment-emails/"+email, function(data) {
    //
      // $("#main-content").html(data);
      //
      // drawHomeCharts();
  });
}

function bindDiscountInfoViewEvents() {
  // $('.view-discount-info').on('click', function () {
  //     var discountInfoJson = $(this).parent().find('.discount-info').text().trim();
  //     var discount = JSON.parse(discountInfoJson);
  //     $('#view\\.discount-name').val(discount.discountBasicInfo.discountName);
  //     // $('#view\\.discount-academic-session').val(discount.academicSession.displayName);
  //     if(discount.discountBasicInfo.variable == true){
  //       $('#view\\.discount-variable-amount').prop( "checked", true );
  //     } else{
  //       $('#view\\.discount-variable-amount').prop( "checked", false );
  //     }
  //     $('#view\\.discount-description').val(discount.discountBasicInfo.description);
  //
  //     var feeHeadRow = "";
  //     if(discount.discountAmountFeeHeadResponseList.length > 0){
  //       var i = 0;
  //       var totalDiscount = 0;
  //       var isPercent = false;
  //       if(discount.discountAmountFeeHeadResponseList[0].percentage){
  //         isPercent = true;
  //       }
  //
  //       for( i = 0 ; i < discount.discountAmountFeeHeadResponseList.length; i++){
  //         totalDiscount += discount.discountAmountFeeHeadResponseList[i].amount;
  //         feeHeadRow = feeHeadRow + "<tr> <td>"+discount.discountAmountFeeHeadResponseList[i].feeHeadConfiguration.feeHead+"</td> <td>"+discount.discountAmountFeeHeadResponseList[i].amount+"</td></tr>";
  //       }
  //       if(isPercent){
  //         $("#view\\.discount-amount-title").text("Amount (%)");
  //       }
  //       else{
  //         $("#view\\.discount-amount-title").text("Amount (Rs)");
  //         if(totalDiscount < 0){
  //           totalDiscount = 0;
  //         }
  //         feeHeadRow = feeHeadRow + "<tr> <td> <strong> TOTAL </strong></td> <td>"+totalDiscount+"</td></tr>";
  //       }
  //     }
  //
  //     $('#discount-fee-head-assigned-amounts').html(feeHeadRow);
  //     $("#view-discount-info-modal").modal('toggle');
  // });
}

function showViewDiscount() {
   var discountJson = $( "tbody tr td p" ).text();
   var discountParse = JSON.parse(discountJson)
   var discountAssigned = "None";
   for(var i = 0; i < discountParse.assigned_discounts.length; i++) {
     if(discountAssigned === "None") {
       discountAssigned = "";
       discountAssigned += discountParse.assigned_discounts[i].discountBasicInfo.discountName;
     }
     else {
       discountAssigned += ", " + discountParse.assigned_discounts[i].discountBasicInfo.discountName;
     }
   }
   $("#search-student-class-section").html(discountAssigned);
}


function bindDiscountViewEvents() {
  $('.view-discount').on('click', function () {
      var discountJson = $(this).parent().parent().find('.discount-assign').text();
      var discountParse = JSON.parse(discountJson)
      var html = "<table class=\"table borderless\"><thead><tr><th scope=\"col\" class=\"w-25\">Discount Name</th><th scope=\"col\" >Fee Head</th><th scope=\"col\" >Amount</th><th scope=\"col\" >Percentage</th></tr></thead><tbody>";
      for(var i = 0; i < discountParse.assigned_discounts.length; i++) {
        var discountName = discountParse.assigned_discounts[i].discountBasicInfo.discountName;
        html += "<tr class=\"row-discount\"><td id=\"discount-name\">" + discountName + "</td>";
        var child = true;
        for(var j = 0; j < discountParse.assigned_discounts[i].discountAmountFeeHeadResponseList.length; j++) {
          var feeHead = discountParse.assigned_discounts[i].discountAmountFeeHeadResponseList[j].feeHeadConfiguration.feeHead;
          var amount = discountParse.assigned_discounts[i].discountAmountFeeHeadResponseList[j].amount;
          var percentage = discountParse.assigned_discounts[i].discountAmountFeeHeadResponseList[j].percentage;
          var checkedField = "";
          if(percentage == true) {
            checkedField = "checked";
          }
          if(child) {
            html += "<td id=\"fee-head-name\">" + feeHead + "</td><td id=\"amount\">" + amount + "</td><td id=\"percentage\"><label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" "+checkedField+" disabled ><span class=\"slider round\"></span></label></td></tr>";
          }
          else {
            html += "<tr class=\"row-discount\"><td></td><td id=\"fee-head-name\">" + feeHead + "</td><td id=\"amount\">" + amount + "</td><td id=\"percentage\"><label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch\" "+checkedField+" disabled ><span class=\"slider round\"></span></label></td></tr>";
          }
          child = false;
        }
      }
      html += "</tbody></table>";
      $("#view-popup").html(html);
      $("#view-discount-modal").modal('toggle');
  });
}

function deleteDiscountCallBack(){
  $('.delete-discount-info').on('click', function () {
      var discountInfoJson = $(this).parent().find('.discount-info').text().trim();
      var discount = JSON.parse(discountInfoJson);
      $('#delete\\.discount-id').val(discount.discountBasicInfo.discountId);
      $('#delete\\.discount-name').val(discount.discountBasicInfo.discountName);
      $("#delete-discount-info-modal").modal('toggle');
  });
}

function deleteDiscount(){
  $("#delete-discount-info-modal").modal('toggle');

  var discountId = $("#delete\\.discount-id").val();
  ajaxClient.post("/fees/delete-discount-configuration/"+discountId,{}, function(data){

    returnToMainScreen();
    $("#discount\\.status-modal-container").html(data);
    $("#discount\\.status-modal").modal('toggle');
    getAllDiscounts();
  });
}

function populateViewFeeAssignmentContent(assignedFees) {
      $('#view\\.assigned-fee-name').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeName);
      $('#view\\.assigned-fee-academic-session').val(assignedFees.feeConfigurationResponse.academicSession.displayName);
      $('#view\\.assigned-fee-description').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.description);
      if(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.feeType != 'ONE_TIME'){
        $('#view\\.assigned-fee-duration').val(assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.startMonthYear.month + " to " + assignedFees.feeConfigurationResponse.feeConfigurationBasicInfo.endMonthYear.month);
      }else{
        $('#assigned-fee-duration-container').attr('style','display:none');
      }
      var feeHeadRow = "";
      var i = 0;
      var totalDiscount = 0;
      for( i = 0 ; i < assignedFees.feeHeadAmountDetailsList.length; i++){
        totalDiscount += assignedFees.feeHeadAmountDetailsList[i].amount;
        feeHeadRow = feeHeadRow + "<tr> <td>"+assignedFees.feeHeadAmountDetailsList[i].feeHeadConfiguration.feeHead+"</td> <td>"+assignedFees.feeHeadAmountDetailsList[i].amount+"</td><td><span class=\"badge badge-logo-primary\">"+assignedFees.feeHeadAmountDetailsList[i].feeEntity+"</span></td></tr>";
      }
      feeHeadRow = feeHeadRow + "<tr> <td> <strong> TOTAL </strong></td> <td>"+totalDiscount+"</td> <td></td></tr>";
      $('#assigned-fee-head-amounts').html(feeHeadRow);
      $("#view-assigned-fee-modal").modal('toggle');
}


function studentLiveSearchEvent(sessionId, searchText, resultArea, triggerMethod, status){
  ajaxClient.get("/fees/student-live-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
      $(resultArea).html(data);
      studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
  });
}

var feeTransaction = {
  dataCache : {},

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var status = feeTransaction.dataCache['status'];
        getPaymentTransactions(false,status);
      },
      function () {
        var status = feeTransaction.dataCache['status'];
        getPaymentTransactions(false,status);
      },
      function () {
        var status = feeTransaction.dataCache['status'];
        getPaymentTransactions(false,status);
      },
      function () {
        var status = feeTransaction.dataCache['status'];
        getPaymentTransactions(true,status);
      }
    );
  },

  downloadBulkInvoice: function (){
    var invalid = validateMandatoryFields($("#download-bulk-invoice-modal"));
    if(invalid){
      return;
    }
    var academicSessionId = $(".academic-session option:selected").val();
    var startDate = getDate($("#download-bulk-invoice-modal").find('input.start-date').val()).getTime()/1000;
    var endDate = getDate($("#download-bulk-invoice-modal").find('input.end-date').val()).getTime()/1000;
    var feeTransactionStatus = $("#download-bulk-invoice-modal").find('#fee-transaction-status').val();

    var difference = endDate - startDate;
    // To calculate the no. of days between two dates
    var differenceInDays = difference / (3600 * 24);
    if(differenceInDays < 0 || differenceInDays >= 65) {
      alert("Total number of days cannot be greater than 65. Please reduce the range!");
      return;
    }

    $("#download-bulk-invoice-modal").modal('toggle');
    window.open(baseURL + "/fees/bulk-invoice-pdf?academic_session_id="
      +academicSessionId+"&start_date="+startDate+"&end_date="+endDate
      +"&fee_transaction_status="+feeTransactionStatus, '_blank');
  }

};

function formatINRCurrency (number) {
  if(number == null || number == undefined || number == "") {
    return 0;
  }
  number = number.toLocaleString('en-IN');
  return number;
}

var pastFeePaymentTransactionDays = 4000;
var lastCollectedAmount = 0;
var lastInstantDiscountAmount = 0;
var lastTotalFineAmount = 0;
var lastFeeLevelPaidFineAmount = 0;

function loadPaymentPage(){
    ajaxClient.get("/fees/payment", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(feePayment.changeSession);
        bindPaymentSearchStudentEvent();
    });
}

var feePayment = {

    dataCache : {},

    initCache : function () {
      var studentFeePaymentDetails = readJson("#student-fee-payment-details-json");
      feePayment.dataCache['studentFeePaymentDetails'] = studentFeePaymentDetails;
      var sessionDueFees = {};
      var sessionData = {};
      var dueFeeDetailsMap = {};

      var selectedSessionId = studentFeePaymentDetails.selected_session.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
      sessionDueFees[selectedSessionId] = studentFeePaymentDetails.selected_session.due_fee_list;
      sessionData[selectedSessionId] = studentFeePaymentDetails.selected_session.student.studentAcademicSessionInfoResponse.academicSession;

      for(var i =0; i< studentFeePaymentDetails.selected_session.due_fee_list.length; i++){
        dueFeeDetailsMap[studentFeePaymentDetails.selected_session.due_fee_list[i].feeConfigurationBasicInfo.feeId] = studentFeePaymentDetails.selected_session.due_fee_list[i];
      }

      for(var i = 0; i < studentFeePaymentDetails.other_sessions.length; i++){
        var otherSession = studentFeePaymentDetails.other_sessions[i];
        var otherSessionId = otherSession.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
        sessionDueFees[otherSessionId] = otherSession.due_fee_list;
        sessionData[otherSessionId] = otherSession.student.studentAcademicSessionInfoResponse.academicSession;

        for(var j =0; j < otherSession.due_fee_list.length; j++){
          dueFeeDetailsMap[otherSession.due_fee_list[j].feeConfigurationBasicInfo.feeId] = otherSession.due_fee_list[j];
        }

      }
      feePayment.dataCache['sessionDueFees'] = sessionDueFees;
      feePayment.dataCache['sessionData'] = sessionData;
      feePayment.dataCache['dueFeeDetailsMap'] = dueFeeDetailsMap;
    },


    doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#student-payment-search-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO";
      studentLiveSearchEvent(sessionId, searchText, resultArea, loadStudentPaymentDetails, status);
    },

    changeSession: function() {
      $("#student-payment-search-text").val("");
      $("#student-payment-screen").html("");
      feePayment.dataCache = {};
    },

    showFeeHeadPaymentDetails : function (arrowWrapper) {
      if($(arrowWrapper).hasClass("closed")){
          $(arrowWrapper).find(".arrow").attr("style", "display:none");
          $(arrowWrapper).find(".arrow.up").attr("style", "display:block");
          $(arrowWrapper).removeClass("closed");
          $(arrowWrapper).addClass("open");
      }else{
        $(arrowWrapper).find(".arrow").attr("style", "display:none");
        $(arrowWrapper).find(".arrow.down").attr("style", "display:block");
        $(arrowWrapper).removeClass("open");
        $(arrowWrapper).addClass("closed");
      }
    },

   populateCollectFeesScreen : function (sessionId) {
      var dueFeeList = feePayment.dataCache['sessionDueFees'][sessionId];
      var sessionData = feePayment.dataCache['sessionData'][sessionId];
      $("#collect-fee-session-display").html(sessionData.displayName);
      $("#collect-fee-session-id").html(sessionId);
      // $("#payment-mode-input").val("CASH");
      // $("#payment-date").val(getTodayDateInInputFormat());

      var dueFeeRows = "";
      for(var i = 0; i < dueFeeList.length; i++){
           dueFeeRows += " <tr class=\"collect-fee-row\" id=\""+dueFeeList[i].feeConfigurationBasicInfo.feeId+"\"> <td>   <div class=\"form-check\">  <input type=\"checkbox\" class=\"form-check-input collect-fee-enable\" >  <label class=\"form-check-label\"> "+dueFeeList[i].feeConfigurationBasicInfo.feeName+" </label>  </div> </td> </tr>";
      }

      $("#due-fee-list-container").html(dueFeeRows);
      $('.select-all-fee-enable').prop('checked', false);
      $("#selected-fees-end").siblings().remove();
      feePayment.bindFeesForPaymentCheckboxes();
      feePayment.computeFeesPaymentAmounts();
      feePayment.fillNetAmountDisplay();

    },

    collectFeesDisplay : function(sessionId) {
      var restrictSessionFeesPaymentJson = $("#restrict-session-fees-payment-json").text().trim();
      if(restrictSessionFeesPaymentJson != "None" && restrictSessionFeesPaymentJson != undefined) {
        var restrictSessionFeesPayment = JSON.parse(restrictSessionFeesPaymentJson);
        if(restrictSessionFeesPayment) {
          showErrorDialogBox("Student past session fees are still due, please clear them then take current session payment!");
          return;
        }
      }
      $(".payment-main-screen-info").attr("style", "display:none");
      $(".fee-payment-tab").attr("style", "display:none");
      feePayment.populateCollectFeesScreen(sessionId);

      $("#collect-fee-tab").attr("style", "display:block");
    },

    bindFeesForPaymentCheckboxes :function() {
        $('.select-all-fee-enable').change(function () {
           if($(this).is(":checked")) {
             $('.collect-fee-enable').prop('checked', true);
             $('.collect-fee-enable').trigger("change");
           }else{
             $('.collect-fee-enable').prop('checked', false);
             $('.collect-fee-enable').trigger("change");
           }
        });

        $('.collect-fee-row').on('click', function () {
          if($(this).find('.collect-fee-enable').is(":checked")) {
            $(this).find('.collect-fee-enable').prop('checked', false);
            $(this).find('.collect-fee-enable').trigger("change");
          }else{
            $(this).find('.collect-fee-enable').prop('checked', true);
            $(this).find('.collect-fee-enable').trigger("change");
          }
        });

        $('.collect-fee-enable').on('click', function () {
          if($(this).is(":checked")) {
            $(this).prop('checked', false);
            $(this).trigger("change");
          }else{
            $(this).prop('checked', true);
            $(this).trigger("change");
          }
        });

        // $('.collect-fee-row-label').on('click', function () {
        //   if($(this).siblings('.collect-fee-enable').is(":checked")) {
        //     $(this).siblings('.collect-fee-enable').prop('checked', false);
        //     $(this).siblings('.collect-fee-enable').trigger("change");
        //   }else{
        //     $(this).siblings('.collect-fee-enable').prop('checked', true);
        //     $(this).siblings('.collect-fee-enable').trigger("change");
        //   }
        // });


        $('.collect-fee-enable').change(function() {
             var feeName = $(this).parent().find("label").text();
             var feeId = $(this).closest("tr").attr("id").trim();
             var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];

             if($(this).is(":checked")) {
                if($("#fee-card-"+feeId).length == 0) {
                  feePayment.insertFeesDetails(feeDetails);
                }
             }
             else{
               $('.select-all-fee-enable').prop('checked', false);

               var oldVariousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();

               if($("#fee-card-"+feeId).length > 0) {
                  $("#fee-card-"+feeId).remove();
               }

               feePayment.computeFeesPaymentAmounts();
               feePayment.computeFineAmount();
               feePayment.fillNetAmountDisplay();

               var newVariousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
               var totalNewCollectedAmount = newVariousTotalFeesAmounts['totalCollectedAmount'] + newVariousTotalFeesAmounts['totalFineAmount'];
               var totalOldCollectedAmount = oldVariousTotalFeesAmounts['totalCollectedAmount'] + oldVariousTotalFeesAmounts['totalFineAmount'];
               // Fees is removed for which there was some amount present
               if(totalNewCollectedAmount < totalOldCollectedAmount){
                  var lessAmount = totalOldCollectedAmount - totalNewCollectedAmount;
                  // First Keep wallet amount fixed and decrease paid amount only.
                  // If paid amount is not sufficient reduce wallet amount
                  var existingPaidAmount = feePayment.getPaidAmountValue();
                  if(lessAmount <= existingPaidAmount){
                     $("#student-paid-amount").val(existingPaidAmount - lessAmount);
                  }else{
                    if(!feePayment.isWalletUsed()){
                       showErrorDialogBox("Invalid state. Fee amount was not included in paid amount");
                       return;
                    }
                    $("#student-paid-amount").val(0);
                    var existingWalletAmount = getUsedWalletAmount();
                    var newWalletAmount = existingWalletAmount - (lessAmount - existingPaidAmount);
                    if(newWalletAmount < 0){
                       showErrorDialogBox("Invalid state. Fee amount was not included in paid amount of wallet");
                       return;
                    }
                    $("#student-used-wallet-amount").val(newWalletAmount);
                  }
               }
             }
         });

         $("#student-used-wallet-amount").focus(function() {}).blur(function() {
           var walletAmount = feePayment.getStudentWalletAmount();
           var usedWalletAmount = feePayment.getUsedWalletAmountValue();
           if(usedWalletAmount > walletAmount){
             showErrorDialogBox("Used wallet amount "+usedWalletAmount+" cannot be greater then student available wallet amount "+walletAmount+". Decreasing used wallet amount");
             usedWalletAmount = walletAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }

           var paidAmount = feePayment.getPaidAmountValue();
           var totalFeeAmount = usedWalletAmount + paidAmount;

           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
           // Atleast fine amount has to be paid
           var minimumCollectableAmount = variousTotalFeesAmounts['totalFineAmount'];

           if(totalFeeAmount > totalCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be greater then payble amount " + totalCollectableAmount + ". Reducing the wallet amount.");
             usedWalletAmount = totalCollectableAmount - paidAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }else if(totalFeeAmount < minimumCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be less then fine payment amount " + minimumCollectableAmount + ". Increasing the wallet amount.");
             usedWalletAmount = minimumCollectableAmount - paidAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }
           feePayment.fillCollectedAmounts(totalFeeAmount - minimumCollectableAmount);
         });

         $("#student-paid-amount").focus(function() {}).blur(function() {
           var paidAmount = feePayment.getPaidAmountValue();
           var usedWalletAmount = getUsedWalletAmount();
           var totalFeeAmount = usedWalletAmount + paidAmount;

           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
           // Atleast fine amount has to be paid
           var minimumCollectableAmount = variousTotalFeesAmounts['totalFineAmount'];

           if(totalFeeAmount > totalCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be greater then payble amount " + totalCollectableAmount + ". Reducing the paid amount.");
             paidAmount = totalCollectableAmount - usedWalletAmount;
             $("#student-paid-amount").val(paidAmount);
           }else if(totalFeeAmount < minimumCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be less then fine payment amount " + minimumCollectableAmount + ". Increasing the paid amount.");
             paidAmount = minimumCollectableAmount - usedWalletAmount;
             $("#student-paid-amount").val(paidAmount);
           }
           feePayment.fillCollectedAmounts(totalFeeAmount - minimumCollectableAmount);
         });

         $("#student-total-instant-discount-amount").focus(function() {}).blur(function() {
           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalBalanceAmount = variousTotalFeesAmounts['totalBalanceAmount'];
           var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'];

           var maxDiscountAmount = totalBalanceAmount - totalCollectedAmount;
           var discountedAmount = feePayment.getTotalInstantDiscountAmountValue();
           if(discountedAmount > maxDiscountAmount){
             showErrorDialogBox("Discounted amount "+discountedAmount+" cannot be greater then max discount amount " + maxDiscountAmount + ". Reducing the discounted amount.");
             discountedAmount = maxDiscountAmount;
           }
           $("#student-total-instant-discount-amount").val(discountedAmount);
            feePayment.fillInstantDiscountAmounts(discountedAmount);
         });

         $("#student-total-fine-collected-amount").focus(function() {
           lastTotalFineAmount = feePayment.getTotalFineAmountValue();
         }).blur(function() {
           var newTotalFeeAmount = feePayment.getTotalFineAmountValue();
           feePayment.fillFineAmounts(newTotalFeeAmount);
           feePayment.computeFineAmount();
           feePayment.updatePaidAmountDelta(lastTotalFineAmount, newTotalFeeAmount);
           feePayment.fillNetAmountDisplay();
         });
    },

    updatePaidAmountDelta: function (oldTotalAmount, newTotalAmount) {
      if(oldTotalAmount <= newTotalAmount){
        // Keeping wallet amount fixed and increasing paid amount only
        var excessAmount = newTotalAmount - oldTotalAmount;
        var existingPaidAmount = feePayment.getPaidAmountValue();
        $("#student-paid-amount").val(existingPaidAmount + excessAmount);
      }else{
        // First Keep wallet amount fixed and decrease paid amount only.
        // If paid amount is not sufficient reduce wallet amount
        var lessAmount = oldTotalAmount - newTotalAmount;
        var existingPaidAmount = feePayment.getPaidAmountValue();
        if(lessAmount <= existingPaidAmount){
           $("#student-paid-amount").val(existingPaidAmount - lessAmount);
        }else{
          if(!feePayment.isWalletUsed()){
             showErrorDialogBox("Invalid state. Changed amount was not included in paid amount");
             return;
          }
          $("#student-paid-amount").val(0);
          var existingWalletAmount = getUsedWalletAmount();
          var newWalletAmount = existingWalletAmount - (lessAmount - existingPaidAmount);
          if(newWalletAmount < 0){
             showErrorDialogBox("Invalid state. Changed amount was not included in paid amount of wallet");
             return;
          }
          $("#student-used-wallet-amount").val(newWalletAmount);
        }
      }
    },

    isWalletUsed : function () {
      return $('#use-wallet-amount').is(":checked");
    },

    getStudentWalletAmount : function () {
      return parseFloat($("#student-wallet-amount-value").text().trim());
    },

    getUsedWalletAmountValue : function () {
      var usedWalletAmountStr = $("#student-used-wallet-amount").val();
      if(usedWalletAmountStr != ""){
          return parseFloat(usedWalletAmountStr);
      }
      return 0;
    },

    insertFeesDetails : function(feeDetails) {
        var authorisedToUpdateFineAssignedAmount = $("#authorised-to-update-fine-assigned-amount").text();
        var readOnlyForFineAmount = "readonly";
        if(authorisedToUpdateFineAssignedAmount === "true") {
          readOnlyForFineAmount = "";
        }
        var feesDisplayDiv = "";
        var feeHeadRows = "";
        var feeId = ""
          var feeHeadPayments = feeDetails.feeHeadPaymentDetails;
          var isFineApplicable = feeDetails.feeConfigurationBasicInfo.fineApplicable;
          var totalFeeheadAssignedAmount = 0;
          var totalFeeheadAssignedDiscount = 0;
          var totalFeeheadPaidAmount = 0;
          var totalFeeheadBalanceAmount = 0;
          var totalFeeheadCollectedAmount = 0;
          var totalFeeheadInstantDiscountAmount = 0;
          var totalFeeheadDueAmount = 0;
          var totalDueFineAmount = feeDetails.totalDueFineAmount;
          var totalPaidFineAmount = feeDetails.totalPaidFineAmount;
          var totalRemainingFineAmount = 0
          if(isFineApplicable) totalRemainingFineAmount = totalDueFineAmount - totalPaidFineAmount;
          for(var i = 0; i < feeHeadPayments.length; i++){
            var feeHeadId = feeHeadPayments[i].feeHeadConfiguration.feeHeadId;
            var feeHeadName = feeHeadPayments[i].feeHeadConfiguration.feeHead;
            var assignedAmount = feeHeadPayments[i].assignedAmount;
            var paidAmount = feeHeadPayments[i].paidAmount;
            var instantDiscountAmount = feeHeadPayments[i].instantDiscountAmount;
            var assignedDiscountAmount = feeHeadPayments[i].assignedDiscountAmount;
            var balanceAmount = feeHeadPayments[i].balanceAmount;
            var totalDiscount = instantDiscountAmount + assignedDiscountAmount;
            feeHeadRows += feeHeadPaymentDetailRow(feeHeadId,feeHeadName,assignedAmount,paidAmount,balanceAmount,totalDiscount);
          }
          feeHeadRows += "<tr class=\"total-feehead-payment-row\"> <td> <strong> <span>TOTAL</span> </strong> </td> <td class=\"total-feeHead-assigned-amount\"><strong> <span>"+totalFeeheadAssignedAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-assigned-discount\"><strong> <span>"+totalFeeheadAssignedDiscount.toFixed(2)+"</span></td> <td class=\"total-feeHead-paid-amount\"><strong> <span>"+totalFeeheadPaidAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-payment-balance-amount\"><strong> <span>"+totalFeeheadBalanceAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-collected-amount\"><strong> <span>0</span></td> <td class=\"total-feeHead-instant-discount-amount\"><strong> <span>0</span></td> <td class=\"total-feeHead-payment-due-amount\"><strong> <span>"+totalFeeheadDueAmount.toFixed(2)+"</span></td> </tr>";

          feeId = feeDetails.feeConfigurationBasicInfo.feeId;
          var feeName = feeDetails.feeConfigurationBasicInfo.feeName + " ("+ getFeeTypeDisplayName(feeDetails.feeConfigurationBasicInfo.feeType) + ")";

          var feeFineRow = "<div class=\"table-responsive\"> <table class=\"table table-hover fine-table\"> <thead> <tr> <th scope=\"col\" class=\"w-25\"></th> <th scope=\"col\" >Computed Fine</th> <th scope=\"col\" >Collected Fine</th>  <th scope=\"col\" >Paid Fine</th> </tr> </thead> <tbody> <tr class=\"\"> <td> <strong>FINE</strong> </td> <td><input type=\"text\" class=\"form-control form-control-sm computed-fine-amount\" value=\"" + totalDueFineAmount+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm collected-fine-amount\" value=\"" + totalPaidFineAmount+ "\" readonly></td>  <td><input type=\"text\" class=\"form-control form-control-sm paid-fine-amount\" value=\"" + totalRemainingFineAmount + "\"" + readOnlyForFineAmount + "></td>  </tbody> </table> </div>";
          var cardId = "fee-card-" + feeId;
          feesDisplayDiv = generateFeesPaymentCard(cardId, feeId, feeName, feeHeadRows, feeFineRow);
          $("#selected-fees-end").before(feesDisplayDiv);
          feePayment.computeFeesPaymentAmounts();
          feePayment.computeFineAmount();
          feePayment.fillNetAmountDisplay();
          feePayment.updatePaidAmountDelta(0, totalRemainingFineAmount);
          feePayment.bindPaymentDueComputation($("#"+cardId));
    },

    getFloatValue : function (value) {
      if(value == null || value == undefined){
        return 0;
      }

      if(value == ""){
        return 0;
      }

      return parseFloat(value);
    },

    bindPaymentDueComputation : function(parentDiv) {
      $(parentDiv).find(".collected-amount").focus(function() {
        lastCollectedAmount = feePayment.getFloatValue($(this).val());
      }).blur(function() {
          var newValue = feePayment.getFloatValue($(this).val());
          var valid = newValue >= 0 && feePayment.computePaymentDueAmount($(this).parent().parent());
          if(!valid){
            $(this).val(lastCollectedAmount);
            return;
          }
        feePayment.computeFeesPaymentAmounts();
        feePayment.fillNetAmountDisplay();
        feePayment.updatePaidAmountDelta(lastCollectedAmount, feePayment.getFloatValue($(this).val()));
      });
      $(parentDiv).find(".instant-discount-amount").focus(function() {
        lastInstantDiscountAmount = $(this).val();
      }).blur(function() {
        var newValue = feePayment.getFloatValue($(this).val());
        var valid = newValue >= 0 && feePayment.computePaymentDueAmount($(this).parent().parent());
        if(!valid){
          $(this).val(lastInstantDiscountAmount);
          return;
        }
         feePayment.computeFeesPaymentAmounts();
         feePayment.fillNetAmountDisplay();
      });
      $(parentDiv).find(".paid-fine-amount").focus(function() {
        lastFeeLevelPaidFineAmount = feePayment.getFloatValue($(this).val());
      }).blur(function() {
        var newValue = feePayment.getFloatValue($(this).val());
        if(newValue < 0){
          $(this).val(lastFeeLevelPaidFineAmount);
          return;
        }
        feePayment.computeFineAmount();
        feePayment.fillNetAmountDisplay();
        feePayment.updatePaidAmountDelta(lastFeeLevelPaidFineAmount, feePayment.getFloatValue($(this).val()));

      });
    },

   computeFineAmount : function() {
        var totalComputedFine = 0;
        var totalCollectedFine = 0;
        var totalPaidFine = 0;
        $("div.collected-fees-details").each(function() {
           totalComputedFine +=parseFloat($(this).find(".fine-table").find(".computed-fine-amount").val());
           totalCollectedFine += parseFloat($(this).find(".fine-table").find(".collected-fine-amount").val());
           totalPaidFine += parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val());
        });
        $("#net-total-fine-computed-amount").find('span').first().text(totalComputedFine);
        $("#net-total-fine-collected-amount").find('span').first().text(totalCollectedFine);
        $("#net-total-fine-paid-amount").find('span').first().text(totalPaidFine);
    },

   getPaidAmountValue : function() {
      var inputFeesAmountStr = $("#student-paid-amount").val();
      var inputFeesAmount = 0;
      if(inputFeesAmountStr != ""){
          inputFeesAmount = parseFloat(inputFeesAmountStr);
      }
      return inputFeesAmount < 0 ? 0 : inputFeesAmount;
    },

    getTotalInstantDiscountAmountValue : function() {
       var inputDiscountAmountStr = $("#student-total-instant-discount-amount").val();
       var inputDiscountAmount = 0;
       if(inputDiscountAmountStr != ""){
           inputDiscountAmount = parseFloat(inputDiscountAmountStr);
       }
       return inputDiscountAmount < 0 ? 0 : inputDiscountAmount;
     },

     getTotalFineAmountValue : function() {
        var inputFineAmountStr = $("#student-total-fine-collected-amount").val();
        var inputFineAmount = 0;
        if(inputFineAmountStr != ""){
            inputFineAmount = parseFloat(inputFineAmountStr);
        }
        return inputFineAmount < 0 ? 0 : inputFineAmount ;
      },

    fillCollectedAmounts : function(collectedAmount) {
       feePayment.clearCollectedAmounts();
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                   if(collectedAmount <= 0){
                       return;
                   }
                   feePayment.computePaymentDueAmount($(this));
                   var dueAmountStr = $(this).find('input.payment-due-amount').val();
                   var dueAmount = 0;
                   if(dueAmountStr != ""){
                       dueAmount = parseFloat(dueAmountStr);
                   }

                   var fillAmount = 0;
                   if(collectedAmount >= dueAmount){
                       fillAmount = dueAmount;
                       collectedAmount -= dueAmount;
                   }else{
                       fillAmount = collectedAmount;
                       collectedAmount = 0;
                   }
                   $(this).find('input.collected-amount').first().val(fillAmount);
                   feePayment.computePaymentDueAmount($(this));
             });
       });
       if(collectedAmount > 0){
         showErrorDialogBox(collectedAmount + " Rs. amount is more than the assigned fees. Please select more fees or reduce the amount.")
         feePayment.clearCollectedAmounts();
         $("#student-paid-amount").val(0);
       }
       feePayment.computeFeesPaymentAmounts();
       feePayment.fillNetAmountDisplay();

     },

     fillInstantDiscountAmounts : function(discountedAmount) {
        feePayment.clearDiscountedAmounts();
        $("div.collected-fees-details").each(function() {
              $(this).find("tr.feehead-payment-row").each(function() {
                    if(discountedAmount <= 0){
                        return;
                    }
                    feePayment.computePaymentDueAmount($(this));
                    var dueAmountStr = $(this).find('input.payment-due-amount').val();
                    var dueAmount = 0;
                    if(dueAmountStr != ""){
                        dueAmount = parseFloat(dueAmountStr);
                    }

                    var fillAmount = 0;
                    if(discountedAmount >= dueAmount){
                        fillAmount = dueAmount;
                        discountedAmount -= dueAmount;
                    }else{
                        fillAmount = discountedAmount;
                        discountedAmount = 0;
                    }
                    $(this).find('input.instant-discount-amount').first().val(fillAmount);
                    feePayment.computePaymentDueAmount($(this));
              });
        });
        if(discountedAmount > 0){
          showErrorDialogBox(discountedAmount + " Rs. amount is more than the max applicable discount. Please select more fees or reduce the amount.")
        }
        feePayment.computeFeesPaymentAmounts();
        feePayment.fillNetAmountDisplay();
      },


      fillFineAmounts : function(totalFineAmount) {
          feePayment.clearFineAmounts();
          var currentTime = new Date().getTime() / 1000;
          //Iterating over only due fees which have computed amount available
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                 return;
                }

                var computedFineStr = $(this).find(".fine-table").find(".computed-fine-amount").val();
                var computedFine = 0;
                if(computedFineStr != ""){
                   computedFine = parseFloat(computedFineStr);
                }

                var collectedFineStr = $(this).find(".fine-table").find(".collected-fine-amount").val();
                var collectedFine = 0;
                if(collectedFineStr != ""){
                   collectedFine = parseFloat(collectedFineStr);
                }

                // Skip all due fees in first iteration which does not have any computed amount
                if(computedFine <= 0){
                 return;
                }
                var paybleFine = computedFine - collectedFine;
                if(totalFineAmount < paybleFine){
                    $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                    totalFineAmount = 0;
                }else{
                    $(this).find(".fine-table").find(".paid-fine-amount").val(paybleFine);
                    totalFineAmount = totalFineAmount - paybleFine;
                }
          });

          if(totalFineAmount <= 0){
            return;
          }

          //Iterating over only due fees which do not have computed amount available
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                   return;
                }

                var computedFineStr = $(this).find(".fine-table").find(".computed-fine-amount").val();
                var computedFine = 0;
                if(computedFineStr != ""){
                   computedFine = parseFloat(computedFineStr);
                }

                if(computedFine <= 0){
                  $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                  totalFineAmount = 0;
                  return;
                }
          });

          if(totalFineAmount <= 0){
            return;
          }

          //Iterating over non due fees and assigning all fine amount on first available fees
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                   $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                   totalFineAmount = 0;
                   return;
                }
          });
       },

     clearCollectedAmounts : function() {
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                 $(this).find('input.collected-amount').val(0);
             });
       });
     },

     clearDiscountedAmounts : function() {
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                 $(this).find('input.instant-discount-amount').val(0);
             });
       });
     },

     clearFineAmounts : function() {
       $("div.collected-fees-details").each(function() {
          $(this).find(".fine-table").find(".paid-fine-amount").val(0);
       });
     },

    computePaymentDueAmount : function(current) {
       var paidAmountString = $(current).find(".collected-amount").first().val();
       var paidAmount = 0;
       if (paidAmountString) {
           paidAmount = parseFloat(paidAmountString);
       }

       var instantDiscountString = $(current).find(".instant-discount-amount").first().val();
       var instantDiscount = 0;
       if (instantDiscountString) {
           instantDiscount = parseFloat(instantDiscountString);
       }

       var balanceAmountString = $(current).find(".payment-balance-amount").first().val();
       var balanceAmount = 0;
       if (balanceAmountString) {
           balanceAmount = parseFloat(balanceAmountString);
       }
       var collectedAmount = parseFloat(paidAmount.toFixed(2)) + parseFloat(instantDiscount.toFixed(2));
       var dueAmount = parseFloat(balanceAmount.toFixed(2)) - collectedAmount;
       if(dueAmount < 0){
         showErrorDialogBox("Collected amount = "+collectedAmount+ " is more than the Payable amount = "+balanceAmount +". Please review the collected and instant discount amounts.");
         return false;
         // dueAmount = 0;
         // $(current).find(".payment-due-amount").first().val(balanceAmount.toFixed(0));
         // $(current).find(".collected-amount").first().val(0);
         // $(current).find(".instant-discount-amount").first().val(0);

       }else{
         $(current).find(".payment-due-amount").first().val(dueAmount.toFixed(0));
         return true;
       }
     },


    computeFeesPaymentAmounts : function() {
       var totalFeesAssignedAmount = 0;
       var totalFeesAssignedDiscount = 0;
       var totalFeesPaidAmount = 0;
       var totalFeesBalanceAmount = 0;
       var totalFeesCollectedAmount = 0;
       var totalFeesInstantDiscountAmount = 0;
       var totalFeesDueAmount = 0;
       $("div.collected-fees-details").each(function() {
             var totalFeeheadAssignedAmount = 0;
             var totalFeeheadAssignedDiscount = 0;
             var totalFeeheadPaidAmount = 0;
             var totalFeeheadBalanceAmount = 0;
             var totalFeeheadCollectedAmount = 0;
             var totalFeeheadInstantDiscountAmount = 0;
             var totalFeeheadDueAmount = 0;
             $(this).find("tr.feehead-payment-row").each(function() {
                 feePayment.computePaymentDueAmount($(this));
                 totalFeeheadAssignedAmount += parseFloat($(this).find('input.assigned-amount').val());
                 totalFeeheadAssignedDiscount += parseFloat($(this).find('input.assigned-discount').val());
                 totalFeeheadPaidAmount += parseFloat($(this).find('input.paid-amount').val());
                 totalFeeheadBalanceAmount += parseFloat($(this).find('input.payment-balance-amount').val());
                 totalFeeheadCollectedAmount += parseFloat($(this).find('input.collected-amount').val());
                 totalFeeheadInstantDiscountAmount += parseFloat($(this).find('input.instant-discount-amount').val());
                 totalFeeheadDueAmount += parseFloat($(this).find('input.payment-due-amount').val());
             });
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-assigned-amount').find('span').first().text(totalFeeheadAssignedAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-assigned-discount').find('span').first().text(totalFeeheadAssignedDiscount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-paid-amount').find('span').first().text(totalFeeheadPaidAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-payment-balance-amount').find('span').first().text(totalFeeheadBalanceAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-collected-amount').find('span').first().text(totalFeeheadCollectedAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-instant-discount-amount').find('span').first().text(totalFeeheadInstantDiscountAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-payment-due-amount').find('span').first().text(totalFeeheadDueAmount);

             totalFeesAssignedAmount += totalFeeheadAssignedAmount;
             totalFeesAssignedDiscount += totalFeeheadAssignedDiscount;
             totalFeesPaidAmount += totalFeeheadPaidAmount;
             totalFeesBalanceAmount += totalFeeheadBalanceAmount;
             totalFeesCollectedAmount += totalFeeheadCollectedAmount;
             totalFeesInstantDiscountAmount += totalFeeheadInstantDiscountAmount;
             totalFeesDueAmount += totalFeeheadDueAmount;
       });
       $("#net-total-fees-assigned-amount").find('span').first().text(totalFeesAssignedAmount.toFixed(2));
       $("#net-total-fees-assigned-discount").find('span').first().text(totalFeesAssignedDiscount.toFixed(2));
       $("#net-total-fees-paid-amount").find('span').first().text(totalFeesPaidAmount.toFixed(2));
       $("#net-total-fees-payment-balance-amount").find('span').first().text(totalFeesBalanceAmount.toFixed(2));
       $("#net-total-fees-collected-amount").find('span').first().text(totalFeesCollectedAmount.toFixed(2));
       $("#net-total-fees-instant-discount-amount").find('span').first().text(totalFeesInstantDiscountAmount.toFixed(2));
       $("#net-total-fees-payment-due-amount").find('span').first().text(totalFeesDueAmount.toFixed(2));
     },

    fillNetAmountDisplay : function() {
       var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
       var totalBalanceAmount = variousTotalFeesAmounts['totalBalanceAmount'];
       var totalInstantDiscountAmount =  variousTotalFeesAmounts['totalInstantDiscountAmount'];
       var totalFineAmount =  variousTotalFeesAmounts['totalFineAmount'];
       var totalCollectableAmount = totalBalanceAmount +  totalFineAmount - totalInstantDiscountAmount;

       $("#student-total-balance-amount-display").val(totalBalanceAmount.toFixed(2));
       $("#student-total-fine-collected-amount").val(totalFineAmount.toFixed(2));
       $("#student-total-instant-discount-amount").val(totalInstantDiscountAmount.toFixed(2));
       $("#student-net-payble-amount-display").val(totalCollectableAmount.toFixed(2));

       // var usedWalletAmount = getUsedWalletAmount();
       // var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'] + totalFineAmount;
       // if(totalCollectedAmount > usedWalletAmount){
       //   // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
       //   $("#student-paid-amount").val(totalCollectedAmount - usedWalletAmount);
       // }else if(totalCollectedAmount <= usedWalletAmount){
       //   usedWalletAmount = totalCollectedAmount;
       //   $("#student-used-wallet-amount").val(usedWalletAmount);
       //   // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
       //   $("#student-paid-amount").val(0);
       // }else{
       //   showErrorDialogBox("Total collected amount " + totalCollectedAmount + " is more then payble amount " + totalCollectableAmount);
       // }
     },

    computeVariousTotalFeesAmounts : function() {
       var totalBalanceAmount = 0;
       var totalInstantDiscountAmount = 0;
       var totalCollectedAmount = 0;
       var totalFineAmount = 0;
       $("div.collected-fees-details").each(function() {
             var fineAmountStr = $(this).find(".fine-table").find(".paid-fine-amount").val();
             if(fineAmountStr != ""){
                 totalFineAmount += parseFloat(fineAmountStr);
             }
             $(this).find("tr.feehead-payment-row").each(function() {
                 var balanceAmountStr = $(this).find('input.payment-balance-amount').val();
                 if(balanceAmountStr != ""){
                     totalBalanceAmount += parseFloat(balanceAmountStr);
                 }
                 var instantDiscountAmountStr = $(this).find('input.instant-discount-amount').val();
                 if(instantDiscountAmountStr != ""){
                     totalInstantDiscountAmount += parseFloat(instantDiscountAmountStr);
                 }
                 var collectedAmountStr = $(this).find('input.collected-amount').val();
                 if(collectedAmountStr != ""){
                     totalCollectedAmount += parseFloat(collectedAmountStr);
                 }
             });
       });
       return {'totalBalanceAmount':totalBalanceAmount ,'totalInstantDiscountAmount': totalInstantDiscountAmount,'totalCollectedAmount': totalCollectedAmount, 'totalFineAmount' : totalFineAmount, 'totalCollectableAmount' : totalBalanceAmount + totalFineAmount - totalInstantDiscountAmount};
     },


     collectSiblingFeesPage : function (studentId, sessionId) {

       ajaxClient.get( "/fees/sibling-due-fee-details/" + sessionId + "/" + studentId, function(data) {

         $("#sibling-collect-fee-tab").html(data);

         $(".payment-main-screen-info").attr("style", "display:none");
         $(".fee-payment-tab").attr("style", "display:none");
         $("#sibling-collect-fee-tab").attr("style", "display:block");

         feePayment.populateSiblingCollectFeesScreen(studentId, sessionId);
         feePayment.registerSummaryAmountInput();
         feePayment.registerIndividualAmountInput();

       });
     },

     populateSiblingCollectFeesScreen : function (studentId, sessionId) {
       var sessionData = feePayment.dataCache['sessionData'][sessionId];
       $("#collect-sibling-fee-session-display").html(sessionData.displayName);
       $("#collect-sibling-fee-session-id").html(sessionId);
       initDate(pastFeePaymentTransactionDays);

       var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

       var totalBalanceAmount = 0;
       var totalFineAmount = 0;
       var totalInstantDiscountAmount = 0;
       for(var i = 0; i < siblingDueFeeDetails.length; i++) {
         var siblingDueFeeDetail = siblingDueFeeDetails[i];
         if(siblingDueFeeDetail.feePaymentRestricted){
         continue;
         }
         totalBalanceAmount += siblingDueFeeDetail.dueAmount;
         totalFineAmount += siblingDueFeeDetail.dueFineAmount;
       }

       var totalCollectableAmount = totalBalanceAmount +  totalFineAmount - totalInstantDiscountAmount;

       $("#sibling-total-balance-amount-display").val(totalBalanceAmount);
       $("#sibling-total-fine-collected-amount").val(totalFineAmount);
       $("#sibling-total-instant-discount-amount").val(totalInstantDiscountAmount);
       $("#sibling-net-payble-amount-display").val(totalCollectableAmount);
       $("#sibling-student-paid-amount").val(totalFineAmount);

       $("tr.sibling-payment-details-tr").each(function() {
         var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
         var fineAmount = siblingDueFeeDetail.dueFineAmount;
         $(this).find(".sibling-student-fine-amount").val(fineAmount);

         // We must pay fine amount
         $(this).find(".sibling-student-paid-amount").val(fineAmount);

         var dueAmount = siblingDueFeeDetail.dueAmount;
         var instantDiscountAmount = $("#sibling-student-instant-discount-amount").val();
         if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
           instantDiscountAmount = 0;
         }
         var netPayableAmount = parseFloat(dueAmount) + parseFloat(fineAmount) - parseFloat(instantDiscountAmount);

         $(this).find(".sibling-student-net-payable-amount").val(roundOffNumber(netPayableAmount, 2));

       });

     },

     registerSummaryAmountInput : function () {

       $("#sibling-student-paid-amount").focus(function() {}).blur(function() {

         var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

         var totalPaidAmount = $(this).val();
         if(totalPaidAmount === "" || totalPaidAmount === undefined || totalPaidAmount === null) {
           totalPaidAmount = 0;
         }
         totalPaidAmount = parseFloat(totalPaidAmount);

         var totalCollectableAmount = parseFloat($("#sibling-net-payble-amount-display").val());
         if(totalCollectableAmount === "" || totalCollectableAmount === undefined || totalCollectableAmount === null) {
           totalCollectableAmount = 0;
         }
         totalCollectableAmount = parseFloat(totalCollectableAmount);
         var totalFineAmount = $("#sibling-total-fine-collected-amount").val()

         if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
           totalFineAmount = 0;
         }
         totalFineAmount = parseFloat(totalFineAmount);
         if(totalPaidAmount > totalCollectableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than new payable amount!")
           $(this).val(0);
           return;
         }

         if(totalPaidAmount < totalFineAmount) {
           showErrorDialogBox("Paid Amount cannot be lesser than total fine amount!")
           return;
         }

         feePayment.updateIndividualPaidAmount(true);

      });


      $("#sibling-total-fine-collected-amount").focus(function() {}).blur(function() {


          var valid = feePayment.setNetPayableAmount();
          if(!valid) {
            return;
          }

          var totalFineAmount = $("#sibling-total-fine-collected-amount").val();
          if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
            totalFineAmount = 0;
          }
          totalFineAmount = parseFloat(totalFineAmount);

          var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());
          var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
          var individualFineAmount = 0;
          var reminder = 0;
          if(totalFineAmount % noOfSiblings == 0) {
            individualFineAmount = totalFineAmount / noOfSiblings;
          } else {
            reminder = totalFineAmount % noOfSiblings;
            individualFineAmount = (totalFineAmount - reminder) / noOfSiblings;
          }
          var count = 0;
          $("tr.sibling-payment-details-tr").each(function() {
            var siblingDetailJson = $(this).find(".sibling-due-fee-detail").text();
            var siblingDetail = JSON.parse(siblingDetailJson);
            if (siblingDetail.feePaymentRestricted) {
                    return;
            }
            $(this).find(".sibling-student-fine-amount").val(individualFineAmount + reminder);
            reminder = 0;
            var valid = feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-fine-amount"));
            if(!valid) {
              return;
            }

          });

          var paidAmount = $("#sibling-student-paid-amount").val();
          if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
            paidAmount = 0;
          }
          paidAmount = parseFloat(paidAmount);

          if(paidAmount < totalFineAmount) {
            $("#sibling-student-paid-amount").val(totalFineAmount);
          }

          feePayment.updateIndividualPaidAmount(true);

        });


     $("#sibling-total-instant-discount-amount").focus(function() {}).blur(function() {

         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }

         var totalInstantDiscountAmount = $("#sibling-total-instant-discount-amount").val();
         if(totalInstantDiscountAmount === "" || totalInstantDiscountAmount === undefined || totalInstantDiscountAmount === null) {
           totalInstantDiscountAmount = 0;
         }
         totalInstantDiscountAmount = parseFloat(totalInstantDiscountAmount);

         var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());
         var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
         var individualInstantDiscountAmount = 0;
         var reminder = 0;
         if(totalInstantDiscountAmount % noOfSiblings == 0) {
           individualInstantDiscountAmount = totalInstantDiscountAmount / noOfSiblings;
         } else {
           reminder = totalInstantDiscountAmount % noOfSiblings;
           individualInstantDiscountAmount = (totalInstantDiscountAmount - reminder) / noOfSiblings;
         }
         var count = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDetailJson = $(this).find(".sibling-due-fee-detail").text();
           var siblingDetail = JSON.parse(siblingDetailJson);
           if (siblingDetail.feePaymentRestricted) {
                return; // Skip this iteration
           }
           $(this).find(".sibling-student-instant-discount-amount").val(individualInstantDiscountAmount + reminder);
           reminder = 0;
           var valid = feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-instant-discount-amount"));
           if(!valid) {
             return;
           }
         });
         feePayment.updateIndividualPaidAmount(true);
       });
     },

    countSiblingsWithFeePaymentAllowed :  function (siblingDueFeeDetails) {
         return siblingDueFeeDetails.filter(sibling => !sibling.feePaymentRestricted).length;
     },

     registerIndividualAmountInput : function () {
       $('.sibling-student-paid-amount').focus(function() {}).blur(function() {
         var totalPaidAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var paidAmount = $(this).find(".sibling-student-paid-amount").val();
           if(paidAmount != undefined && paidAmount != null && paidAmount != "") {
             totalPaidAmount += parseFloat(paidAmount);
           }
         });
         var row = $(this).closest("tr");
         var fineAmount = parseFloat(row.find(".sibling-student-fine-amount").val()) || 0;
         var discountAmount = parseFloat(row.find(".sibling-student-instant-discount-amount").val()) || 0;
         var paidAmount = parseFloat($(this).val()) || 0;
         var netPayableAmount = parseFloat(row.find(".sibling-student-net-payable-amount").val()) || 0;

         var totalCollectableAmount = parseFloat($("#sibling-net-payble-amount-display").val());

         if(totalPaidAmount > totalCollectableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than new payable amount!");
           return;
         }

         if(paidAmount > netPayableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than net payable amount!");
           return;
         }

         if(fineAmount > paidAmount && netPayableAmount > fineAmount) {
           showErrorDialogBox("Paid Amount cannot be lesser than fine amount!");
           return;
         }
         $("#sibling-student-paid-amount").val(totalPaidAmount);
       });



       $('.sibling-student-fine-amount').focus(function() {}).blur(function() {
         var totalFineAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var fineAmount = $(this).find(".sibling-student-fine-amount").val();
           if(fineAmount != undefined && fineAmount != null && fineAmount != "") {
             totalFineAmount += parseFloat(fineAmount);
           }
         });
         $("#sibling-total-fine-collected-amount").val(totalFineAmount);
         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }
         valid = feePayment.setIndividualNetPayableAmount(this);
         if(!valid) {
           return;
         }

         var paidAmount = $("#sibling-student-paid-amount").val();
         if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
           paidAmount = 0;
         }
         paidAmount = parseFloat(paidAmount);
         var row = $(this).closest("tr");
         var fineAmount = parseFloat($(this).val()) || 0;
         var individualPaidAmount = parseFloat(row.find(".sibling-student-paid-amount").val()) || 0;

         if(individualPaidAmount < fineAmount) {
           row.find(".sibling-student-paid-amount").val(fineAmount);
         }

         feePayment.updateIndividualPaidAmount(false);
         feePayment.updateTotalPaidAmount();

       });


       $('.sibling-student-instant-discount-amount').focus(function() {}).blur(function() {
         var totalInstantDiscountAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var instantDiscountAmount = $(this).find(".sibling-student-instant-discount-amount").val();
           if(instantDiscountAmount != undefined && instantDiscountAmount != null && instantDiscountAmount != "") {
             totalInstantDiscountAmount += parseFloat(instantDiscountAmount);
           }
         });

         $("#sibling-total-instant-discount-amount").val(totalInstantDiscountAmount);
         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }
         valid = feePayment.setIndividualNetPayableAmount(this);
         if(!valid) {
           return;
         }

         feePayment.updateIndividualPaidAmount(false);

       });

     },

     updateTotalPaidAmount : function() {
         var totalPaidAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var paidAmount = $(this).find(".sibling-student-paid-amount").val();
           if(paidAmount != undefined && paidAmount != null && paidAmount != "") {
             totalPaidAmount += parseFloat(paidAmount);
           }
         });
         $("#sibling-student-paid-amount").val(totalPaidAmount);
     },

     setNetPayableAmount : function () {
       var totalFineAmount = $("#sibling-total-fine-collected-amount").val();
       if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
         totalFineAmount = 0;
       }
       totalFineAmount = parseFloat(totalFineAmount);


       var totalBalanceAmount = $("#sibling-total-balance-amount-display").val();
       if(totalBalanceAmount === "" || totalBalanceAmount === undefined || totalBalanceAmount === null) {
         totalBalanceAmount = 0;
       }
       totalBalanceAmount = parseFloat(totalBalanceAmount);
       var totalInstantDiscountAmount = $("#sibling-total-instant-discount-amount").val();
       if(totalInstantDiscountAmount === "" || totalInstantDiscountAmount === undefined || totalInstantDiscountAmount === null) {
         totalInstantDiscountAmount = 0;
       }
       totalInstantDiscountAmount = parseFloat(totalInstantDiscountAmount);
       var totalCollectableAmount = parseFloat(totalBalanceAmount) +  parseFloat(totalFineAmount) - parseFloat(totalInstantDiscountAmount);

       if(totalCollectableAmount < 0) {
         showErrorDialogBox("Net Payable amount cannot be negative!");
         return false;
       }
       $("#sibling-net-payble-amount-display").val(totalCollectableAmount);
       return true;
     },

     setIndividualNetPayableAmount : function (ref) {
       var instantDiscountAmount = $(ref).parent().parent().find(".sibling-student-instant-discount-amount").val();
       if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
         instantDiscountAmount = 0;
       }
       instantDiscountAmount = parseFloat(instantDiscountAmount);
       var fineAmount = $(ref).parent().parent().find(".sibling-student-fine-amount").val();
       if(fineAmount === "" || fineAmount === undefined || fineAmount === null) {
         fineAmount = 0;
       }
       fineAmount = parseFloat(fineAmount);
       var siblingDueFeeDetail = JSON.parse($(ref).parent().parent().find(".sibling-due-fee-detail").text());
       var dueAmount = siblingDueFeeDetail.dueAmount;
       var netPayableAmount = parseFloat(dueAmount) + parseFloat(fineAmount) - parseFloat(instantDiscountAmount);
       if(netPayableAmount < 0) {
         showErrorDialogBox("Net Payable amount cannot be negative!");
         return false;
       }
       $(ref).parent().parent().find(".sibling-student-net-payable-amount").val(roundOffNumber(netPayableAmount, 2));
       return true;
     },

     updateIndividualPaidAmount : function (updateSiblingFeeDetails) {
       var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

       var totalPaidAmount = $("#sibling-student-paid-amount").val();
       if(totalPaidAmount === "" || totalPaidAmount === undefined || totalPaidAmount === null) {
         totalPaidAmount = 0;
       }
       totalPaidAmount = parseFloat(totalPaidAmount);

       var studentIdNetPayableAmountMap = {};
       if(updateSiblingFeeDetails){
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
           if(!siblingDueFeeDetail.feePaymentRestricted){
               var studentId = siblingDueFeeDetail.studentId;
               var netPayableAmount = $(this).find(".sibling-student-net-payable-amount").val();
               studentIdNetPayableAmountMap[studentId] = parseFloat(netPayableAmount);
           }
         });
       }

//       var noOfSiblings = siblingDueFeeDetails.length;
       var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
       var remainingPaidAmount = totalPaidAmount;
       var count = 0;
       var studentIdNetPayableAmountMapSortByValue = sortMapByValue(studentIdNetPayableAmountMap);
       var studentIdPaidAmountMap = {};
       var studentIdRemianingAmountMap = {};
       var reminder = 0;
       var countOfSiblingWithRemainingAmount = 0;
       $.each(studentIdNetPayableAmountMapSortByValue, function(studentId, netPayableAmount) {

            reminder += (remainingPaidAmount % (noOfSiblings - count));
            var paidAmount = ((remainingPaidAmount - (remainingPaidAmount % (noOfSiblings - count))) / (noOfSiblings - count));
            if(paidAmount > netPayableAmount) {
              paidAmount = netPayableAmount;
            }
            studentIdPaidAmountMap[studentId] = paidAmount;
            studentIdRemianingAmountMap[studentId] = netPayableAmount - paidAmount;

            if(studentIdRemianingAmountMap[studentId] > 0) {
              countOfSiblingWithRemainingAmount += 1;
            }
            remainingPaidAmount -= paidAmount;
            count += 1;
        });

        var individualReminder = 0;
        var finalReminder = 0;
        if(remainingPaidAmount > 0) {
          if(remainingPaidAmount % countOfSiblingWithRemainingAmount == 0) {
            individualReminder = remainingPaidAmount / countOfSiblingWithRemainingAmount;
          } else {
            finalReminder = remainingPaidAmount % countOfSiblingWithRemainingAmount;
            individualReminder = (remainingPaidAmount - finalReminder) / countOfSiblingWithRemainingAmount;
          }

          $.each(studentIdPaidAmountMap, function(studentId, paidAmount) {
            studentIdPaidAmountMap[studentId] += individualReminder + finalReminder;
            finalReminder = 0;
          });
        }

       if(updateSiblingFeeDetails){
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
           var studentId = siblingDueFeeDetail.studentId;
           var paidAmount = studentIdPaidAmountMap[studentId]
           $(this).find(".sibling-student-paid-amount").val(paidAmount);
         });
        }

     },

     collectSiblingFees : function () {

       $("#sibling-fee-payment-confirm-modal").modal("toggle");

       var academicSessionId = JSON.parse($("#collect-sibling-fee-session-id").html());
       var transactionMode = $('#sibling-payment-mode-input').find(':selected').val().trim();
       var transactionReference = $('#sibling-payment-reference').val().trim();
       var transactionDate = getDate($('#sibling-payment-date').val());
       var transactionRemarks = $('#sibling-payment-remark').val();
       var invalid = validateMandatoryFields($("#sibling-collect-fee-tab"));
       if(invalid){
         showErrorDialogBox("Please fill required fields before making payment.");
         return;
       }
       var invalidAmount = validateSiblingPaymentAmounts();
       if(invalidAmount) {
         return;
       }
       if(transactionDate == null){
         showErrorDialogBox("Please enter the date of payment");
         return null;
       }
       var currentDate = new Date();
       if(transactionDate.getTime() > currentDate.getTime()){
         showErrorDialogBox("Invalid payment date. Future transactions not allowd");
         return null;
       }
       var feePaymentPayloadLiteList = [];
       $("tr.sibling-payment-details-tr").each(function() {
             var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
             if(siblingDueFeeDetail.feePaymentRestricted){
               return;
             }
             var studentId = siblingDueFeeDetail.studentId;

             var fineCollectionAmount = $(this).find(".sibling-student-fine-amount").val();
             if(fineCollectionAmount === "" || fineCollectionAmount === undefined || fineCollectionAmount === null) {
               fineCollectionAmount = 0;
             }
             fineCollectionAmount = parseFloat(fineCollectionAmount);

             var instantDiscountAmount = $(this).find(".sibling-student-instant-discount-amount").val();
             if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
               instantDiscountAmount = 0;
             }
             instantDiscountAmount = parseFloat(instantDiscountAmount);

             var walletAmount = 0;

             var paidAmount = $(this).find(".sibling-student-paid-amount").val();
             if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
               paidAmount = 0;
             }
             paidAmount = parseFloat(paidAmount);

             if(!(paidAmount === 0 && instantDiscountAmount === 0 && fineCollectionAmount === 0)) {
               feePaymentPayloadLiteList.push({'studentId' : studentId, 'academicSessionId' : academicSessionId, 'transactionMode' : transactionMode, 'transactionDate' : transactionDate.getTime()/1000, 'transactionReference' : transactionReference, 'fineCollectionAmount' : fineCollectionAmount, 'instantDiscountAmount' : instantDiscountAmount, 'walletAmount' : walletAmount, 'paidAmount' : paidAmount, 'transactionRemarks' : transactionRemarks});
             }
       });
       if(feePaymentPayloadLiteList.length <= 0) {
         showErrorDialogBox("No payment Details added to make payment!");
         return;
       }

       ajaxClient.post("/fees/collect-sibling-fees/"+academicSessionId, {'feePaymentPayloadLiteList' : JSON.stringify(feePaymentPayloadLiteList)}, function(data){
         returnToPaymentMainScreenDisplay();
         $("#fee-payment-status-modal-container").html(data);
         $("#collect-fee-payment-status-modal").modal({backdrop: 'static', keyboard: false});
         var studentId = $("#collect-fee-student-id").text().trim();
         feePayment.collectSiblingFeesPage(studentId, academicSessionId);
           $("#marks-feed-status-modal-container").html(data);
           $("#exam-config\\.status-modal").modal({backdrop: 'static', keyboard: false});
       });

     },
    
     prepareUpdateWalletTransaction : function (transactionId, studentId, amount) {
      // Store the current values
      feePayment.currentTransactionData = {
        transactionId: transactionId,
        studentId: studentId,
        amount: amount
      };
      
      // Close the wallet transactions modal properly
      $("#student-wallet-transactions-modal").modal('hide');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Set values in the modal fields
      $("#update-wallet-transaction-id").val(transactionId);
      $("#update-wallet-student-id").val(studentId);
      $("#update-wallet-amount").val(amount);
      
      // Show the update modal
        $("#update-wallet-transaction-modal").modal('toggle');
        
        // Bind event handlers for this specific instance
        $(".cancel-update-btn").off('click').on('click', function() {
          feePayment.cancelUpdateWalletTransaction();
        });
        
        $("#confirm-update-btn").off('click').on('click', function() {
          feePayment.updateWalletTransactionAmount();
        });
    },
    
    cancelUpdateWalletTransaction: function() {
      // Close the update modal properly
      $("#update-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Reopen the wallet transactions modal
        feePayment.reopenWalletTransactions();
    },
    
    updateWalletTransactionAmount : function () {
      var transactionId = $("#update-wallet-transaction-id").val();
      var studentId = $("#update-wallet-student-id").val();
      var amount = $("#update-wallet-amount").val();
      
      if (amount === "" || amount === undefined || amount === null) {
        showErrorDialogBox("Invalid Amount!");
        return;
      }
      
      var updatePayload = {
        "transactionId": transactionId,
        "studentId": studentId,
        "amount": parseFloat(amount)
      };
      
      // Close the update modal properly
      $("#update-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Show loading indicator if needed
      // ...
      
      ajaxClient.post("/fees/update-wallet-transaction-amount", {"updateWalletTransactionPayload": JSON.stringify(updatePayload)}, function(data) {
        // Add the status modal to the container
        $("#update-wallet-transaction-status-modal-container").html(data);
        
        
          $("#update-wallet-transaction-status-modal").modal('toggle');
          
          // Bind event handler for the close button
          $("#close-status-btn").off('click').on('click', function() {
            feePayment.closeStatusModal();
          });
        
        // Reload student payment details to reflect updated wallet balance
        loadStudentPaymentDetails(studentId);
      });
    },
    
    closeStatusModal: function() {
      // Close the status modal properly
      $("#update-wallet-transaction-status-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // // Reopen the wallet transactions modal
      //   feePayment.reopenWalletTransactions();
    },
    
    reopenWalletTransactions : function () {
      var studentId = feePayment.currentTransactionData ? feePayment.currentTransactionData.studentId : $("#wallet-recharge-student-id").text().trim();
      
      // Load wallet transactions again
      ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
        $("#student-wallet-transactions-modal-container").html(data);
        
        // Show the wallet transactions modal
          $("#student-wallet-transactions-modal").modal('toggle');
      });
    },

    prepareDeleteWalletTransaction : function (transactionId, studentId) {
      // Store the current values
      feePayment.currentDeleteTransactionData = {
        transactionId: transactionId,
        studentId: studentId,
      };
      
      // Close the wallet transactions modal properly
      $("#student-wallet-transactions-modal").modal('hide');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // // Set values in the modal fields
      $("#delete-wallet-transaction-id").val(transactionId);
      $("#delete-wallet-student-id").val(studentId);
      // $("#update-wallet-amount").val(amount);
      
      // Show the update modal
        $("#delete-wallet-transaction-modal").modal('toggle');
        
        // Bind event handlers for this specific instance
        $(".cancel-update-btn").off('click').on('click', function() {
          feePayment.cancelUpdateWalletTransaction();
        });
        
        $("#confirm-delete-btn").off('click').on('click', function() {
          feePayment.deleteWalletTransactionAmount();
        });
    },
    
    // cancelUpdateWalletTransaction: function() {
    //   // Close the update modal properly
    //   $("#delete-wallet-transaction-modal").modal('toggle');
    //   $('body').removeClass('modal-open');
    //   $('.modal-backdrop').remove();
      
    //   // Reopen the wallet transactions modal
    //     feePayment.reopenWalletTransactions();
    // },
    
    deleteWalletTransactionAmount : function () {
      var transactionId = $("#delete-wallet-transaction-id").val();
      var studentId = $("#delete-wallet-student-id").val();
      
      var deletePayload = {
        "transactionId": transactionId,
        "studentId": studentId,
      };
      
      // Close the update modal properly
      $("#delete-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Show loading indicator if needed
      // ...
      
      ajaxClient.post("/fees/delete-wallet-transaction", {"deleteWalletTransactionPayload": JSON.stringify(deletePayload)}, function(data) {
        // Add the status modal to the container
        $("#update-wallet-transaction-status-modal-container").html(data);
        
        
          $("#update-wallet-transaction-status-modal").modal('toggle');
          
          // Bind event handler for the close button
          $("#close-status-btn").off('click').on('click', function() {
            feePayment.closeStatusModal();
          });
        
        // Reload student payment details to reflect updated wallet balance
        loadStudentPaymentDetails(studentId);
      });
    },
    
    // closeStatusModal: function() {
    //   // Close the status modal properly
    //   $("#update-wallet-transaction-status-modal").modal('toggle');
    //   $('body').removeClass('modal-open');
    //   $('.modal-backdrop').remove();
      
    //   // // Reopen the wallet transactions modal
    //   //   feePayment.reopenWalletTransactions();
    // },
    
    // reopenWalletTransactions : function () {
    //   var studentId = feePayment.currentDeleteTransactionData ? feePayment.currentDeleteTransactionData.studentId : $("#wallet-recharge-student-id").text().trim();
      
    //   // Load wallet transactions again
    //   ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
    //     $("#student-wallet-transactions-modal-container").html(data);
        
    //     // Show the wallet transactions modal
    //       $("#student-wallet-transactions-modal").modal('toggle');
    //   });
    // }
};

function generateFeeChallan(studentId){
  var sessionId = academicSessionHandler.getSelectedSessionId();
  window.open(baseURL + "/fees/generate-student-fee-challan-pdf/" + sessionId + "/" + studentId, '_blank');
};

function useWalletAmountCallBack() {
  $('#use-wallet-amount').change(function() {
       if($(this).is(":checked")) {
         var walletAmount = parseFloat($("#student-wallet-amount-value").text().trim());
         if(walletAmount <= 0) {
           showErrorDialogBox("You donot have required credits in your wallet. Please recharge it to use wallet amount.");
           $(this).prop( "checked", false );
           return;
         }
          $("#student-used-wallet-amount").val(0);
          $("#wallet-amount-input-container").attr("style", "display:block");
       }
       else{
         // Transfer wallet amount to paid amount
         var usedWalletAmount = getUsedWalletAmount();
         $("#student-used-wallet-amount").val(0);
         $("#student-paid-amount").val(feePayment.getPaidAmountValue() + usedWalletAmount);

         $("#wallet-amount-input-container").attr("style", "display:none");
       }
   });
}
//
// function autoFillFeesPayment() {
//   var inputFeesAmountStr = $("#student-total-input-fees").val();
//   var inputFeesAmount = 0;
//   if(inputFeesAmountStr != ""){
//       inputFeesAmount = parseFloat(inputFeesAmountStr);
//   }
//   $("div.collected-fees-details").each(function() {
//         $(this).find("tr.feehead-payment-row").each(function() {
//               if(inputFeesAmount <= 0){
//                   return;
//               }
//               $(this).find('input.collected-amount').first().val(0);
//               feePayment.computePaymentDueAmount($(this));
//
//               var dueAmountStr = $(this).find('input.payment-due-amount').val();
//               var dueAmount = 0;
//               if(dueAmountStr != ""){
//                   dueAmount = parseFloat(dueAmountStr);
//               }
//
//               var fillAmount = 0;
//               if(inputFeesAmount >= dueAmount){
//                   fillAmount = dueAmount;
//                   inputFeesAmount -= dueAmount;
//               }else{
//                   fillAmount = inputFeesAmount;
//                   inputFeesAmount = 0;
//               }
//               $(this).find('input.collected-amount').first().val(fillAmount);
//               feePayment.computePaymentDueAmount($(this));
//         });
//   });
//   if(inputFeesAmount > 0){
//     showErrorDialogBox(inputFeesAmount + " Rs. amount is more than the assigned fees. Please select more fees or reduce the amount.")
//   }
//   feePayment.computeFeesPaymentAmounts();
// }


function bindPaymentSearchStudentEvent() {
  var resultArea = "#student-payment-search-result";
  $('#student-payment-search').on('click', function () {
       // loadStudentPaymentDetails();
       feePayment.doneStudentSearchTyping(resultArea);
  });
  $("#student-payment-search-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        // loadStudentPaymentDetails();
        feePayment.doneStudentSearchTyping(resultArea);
    }
  });
  liveSearchHandler.bindEvent('#student-payment-search-text',resultArea,feePayment.doneStudentSearchTyping);
}

function confirmCancelTransaction(transactionId, studentId) {
    $("#fee-payment-transaction-view-modal").modal('toggle');
    $("#cancel-transaction-student-id").text(studentId);
    $("#cancel-transaction-id").text(transactionId);
    $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');
}

function reviewCancelTransaction() {
    $("#cancel-transaction-student-id").text("");
    $("#cancel-transaction-id").text("");
    $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');
    $("#fee-payment-transaction-view-modal").modal('toggle');
}

function cancelStudentTransaction() {
  var studentId = $("#cancel-transaction-student-id").text().trim();
  var transactionId = $("#cancel-transaction-id").text().trim();
  if(transactionId == ""){
    showErrorDialogBox("Invalid Transaction to cancel");
    return;
  }

  var cancelAt = new Date().getTime() / 1000;
  var cancelRemarks = $("#payment-cancel-remarks").val().trim();
  var feeCancelledPayload = {'instituteId' : null, 'transactionId' : transactionId, 'cancelBy' : null, 'cancelAt' : cancelAt, 'cancelRemarks' : cancelRemarks};

  $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');

  ajaxClient.post( "/fees/cancel-payment-transaction", {'feeCancelledPayload' : JSON.stringify(feeCancelledPayload)}, function(data) {

      returnToPaymentMainScreenDisplay();
      $("#fee-payment-status-modal-container").html(data);
      $("#fee-payment-status-modal").modal('toggle');
      loadStudentPaymentDetails(studentId);
  });
}


function bindPaymentActionEvents() {
  $("#past-payment-button").on('click', function () {
      pastTransactionsDisplay();
  });
}


function pastTransactionsDisplay() {
  $(".payment-main-screen-info").attr("style", "display:none");
  $(".fee-payment-tab").attr("style", "display:none");
  $("#fee-transactions-tab").attr("style", "display:block");
}


function returnToPaymentMainScreenDisplay() {
  $(".payment-main-screen-info").attr("style", "display:block");
  $(".fee-payment-tab").attr("style", "display:none");
  $("#fee-payment-view-tab").attr("style", "display:block");

}



function generateFeesPaymentCard(cardId, feeId,feeDisplayName,feeHeadRows, feeFineRow) {
      return "<div class=\"card card-border collected-fees-details\" id=\""+ cardId + "\"> <p style=\"display:none\" class=\"collected-fee-id\">"+feeId+"</p>  <div class=\"card-header\" id=\""+"fee-heading-" +feeId + "\"> <h5 class=\"mb-0\"> <button class=\"btn btn-link\" data-toggle=\"collapse\" data-target=\"#" + "collapse-fee-" +feeId+ "\" aria-expanded=\"true\" aria-controls=\"" + "collapse-fee-" +feeId+ "\"> <strong>" +  feeDisplayName.toUpperCase() + " </strong> </button> </h5> </div> <div id=\"" + "collapse-fee-" +feeId+ "\" class=\"collapse show\" aria-labelledby=\""+"fee-heading-" +feeId + "\" data-parent=\"#selected-fees-payment-list\"> <div class=\"card-body\"> <div class=\"table-responsive\"> <table class=\"table table-hover\"> <thead> <tr> <th scope=\"col\" class=\"w-25\">Fee Head</th> <!-- <th scope=\"col\" >Fee Head</th> --> <th scope=\"col\" >Assigned Amount</th> <th scope=\"col\" >Given Discount</th> <th scope=\"col\" >Collected Amount</th> <th scope=\"col\" >Payable Amount</th> <th scope=\"col\" >Amount Paid</th> <th scope=\"col\" >Instant Discount</th> <th scope=\"col\" >Due Amount</th> </tr> </thead> <tbody> " + feeHeadRows + " </tbody> </table> </div> " + feeFineRow +" </div> </div> </div>";
}

function feeHeadPaymentDetailRow(feeHeadId, feeHeadName,assignedAmount,paidAmount,balanceAmount,totalDiscount) {
    return "<tr class=\"feehead-payment-row\"> <td class=\"payment-feehead-id\" style=\"display:none\" >" + feeHeadId+ "</td> <td>" + feeHeadName+ "</td> <td><input type=\"text\" class=\"form-control form-control-sm assigned-amount\" value=\"" + assignedAmount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm assigned-discount\" value=\"" + totalDiscount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm paid-amount\" value=\"" + paidAmount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm payment-balance-amount\" value=\"" + balanceAmount.toFixed(2) + "\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm collected-amount\" value=\"0\" ></td> <td><input type=\"number\" class=\"form-control form-control-sm instant-discount-amount\" value=\"0\" ></td> <td><input type=\"text\" class=\"form-control form-control-sm payment-due-amount\" value=\"" + balanceAmount.toFixed(2)+ "\" readonly></td> </tr>";
}

function getUsedWalletAmount() {
  var walletAmount = parseFloat($("#student-wallet-amount-value").text().trim());

  var usedWalletAmount = 0;
  if($('#use-wallet-amount').is(":checked")) {
    usedWalletAmount = parseFloat($("#student-used-wallet-amount").val());
    if(usedWalletAmount <= 0){
      usedWalletAmount = 0;
    }
    if(usedWalletAmount > walletAmount){
      usedWalletAmount = walletAmount;
    }
  }
  return usedWalletAmount;
}
//
// function updateWalletAmount() {
//   var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
//   var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
//   var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'];
//   var totalFineAmount = variousTotalFeesAmounts['totalFineAmount'];
//
//   var usedWalletAmount = getUsedWalletAmount();
//   if(usedWalletAmount > totalCollectableAmount){
//     usedWalletAmount = totalCollectableAmount;
//     $("#student-used-wallet-amount").val(usedWalletAmount);
//     $("#student-paid-amount").val(0);
//     feePayment.fillCollectedAmounts(usedWalletAmount - totalFineAmount);
//   }else{
//     $("#student-used-wallet-amount").val(usedWalletAmount);
//     // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
//     $("#student-paid-amount").val(totalCollectedAmount + totalFineAmount - usedWalletAmount);
//     feePayment.fillCollectedAmounts(totalCollectedAmount + usedWalletAmount - totalFineAmount);
//   }
// }


function getFeeTypeDisplayName(feeType) {
  var feeTypeDisplayName = "";
  if(feeType == "REGULAR"){
      feeTypeDisplayName = "Regular Fees";
  }
  else if(feeType == "SPECIAL"){
      feeTypeDisplayName = "Special Fees";
  }
  else if(feeType == "ONE_TIME"){
      feeTypeDisplayName = "One Time Fees";
  }
  return feeTypeDisplayName;
}

function loadStudentPaymentDetails(studentId) {
    loadStudentPaymentDetails(studentId, false);
}

// -------------------------------------------------------------------------------Fee Follow Up Trail FE--------------------------------------------------------------------------------------------------------------

// function to open the delete popup
function deleteFeeFollowUpDetails(followUpId,studentId){
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong> <font color=\"red\">Are you sure that you want to delete this followup trail ? </font> </strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button  type=\"button\" class=\"btn btn-danger\" onclick=\"deleteStudentFeeFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(followUpId);
      $("p#student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

// function to open the edit modal
function editFeeFollowUpDetails(value){
     initDateWithYearRange("-10:+10", true);
     initPastDateById("fee-transaction-follow-up-date", 4000);
     initPastDateById("fee-follow-up-date", 4000);
     var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
     $("#update-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
     $("#update-fee-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
     $("#update-follow-up-mode").html("<option>"+followUpPayload.followUpMode+"</option>");
     $("#update-contact-person-name").val(followUpPayload.contactPersonName);
     $("#update-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
     $("#update-conversation").val(followUpPayload.conversation);

     if(followUpPayload.nextFollowUpDate != null){
         $("#update-next-fee-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
     }
     else{
       $("#update-next-fee-follow-up-date").val("");
     }
     $("#update-amount").val(followUpPayload.amount);
     $("#update-student-follow-up-id").text(followUpPayload.followUpId);
     $("#student-id").text(followUpPayload.entityId);
     $("#update-fee-follow-up-modal").modal('toggle');
}

// modal for opening the close modal
function closeFeeFollowUpDetails(followUpId,studentId,isStudent){
      if(!isStudent){
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong>Are you sure that you want to close this followup trail ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentFeeFollowupTrail('false','INACTIVE')\">Yes, Close It</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      }
      else{
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close All Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong style=\"color:red\">Are you sure that you want to close the all followup trail for this student ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentFeeFollowupTrail('true','INACTIVE')\">Yes, Close All</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      }
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(followUpId);
      $("p#student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

// modal for adding the followup details
function addFeeFollowUpDetails(studentId){
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
      initPastDateById("fee-follow-up-date", 4000);
      $("p#student-follow-up-id").text(studentId);
      $("#next-fee-follow-up-date").val("");
      $("#add-fee-follow-up-modal").modal('toggle');
}

// ajax call for updating fee follow up trail
function updateStudentFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#update-fee-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#update-entity-name").val();
      var followUpDate = getDate($('#update-fee-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      var followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#update-contact-person-name").val();
      var followUpType = $("select#update-follow-up-type").val();
      var conversation = $("#update-conversation").val();
      var nextFollowUpDate = $('#update-next-fee-follow-up-date').val();
      if(nextFollowUpDate != ""){
          nextFollowUpDate = getDate($('#update-next-fee-follow-up-date').val());
          nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == null || nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }

      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#update-fee-follow-up-modal").modal('toggle');
        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#update-amount").val();
      var followUpStatus = status;
      var followUpId = $("#update-student-follow-up-id").text();
      var entityId = $("#student-id").text();
      var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode":"MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#update-fee-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/update-fee-followup-trail/"+sessionId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
          returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
          getFollowUpDetails();
 });
}

// add api ajax call
function addStudentFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#add-fee-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#entity-name").val();
      var followUpDate = getDate($('#fee-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      var followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#contact-person-name").val();
      var followUpType = $("select#follow-up-type").val();
      var conversation = $("#conversation").val();
      var nextFollowUpDate = getDate($('#next-fee-follow-up-date').val());
      if(nextFollowUpDate != null){
        nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == null || nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }

      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#add-fee-follow-up-modal").modal('toggle');

        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#amount").val();
      var followUpStatus = status;
      var entityId = $("#student-follow-up-id").text();
      var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#add-fee-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/add-fee-followup-trail/"+sessionId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
          returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
          getFollowUpDetails();
 });
}

function getFollowUpDetails() {
  var sessionId = academicSessionHandler.getSelectedSessionId();
  var studentId = $("#student-follow-up-id").text().trim();
  ajaxClient.get( "/fees/follow-up-details/" + sessionId + "/" + studentId, function(data) {
      $("#follow-up-details-container-div").html(data);
      $(".tab-pane").removeClass("fade");
  });
}

// ajax api for deleting the follow up trail
function deleteStudentFeeFollowupTrail(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
    $("#fee-follow-up-close-modal").modal('toggle');
    ajaxClient.post( "/fees/delete-fee-followup-trail/"+academicSessionId+"/"+followUpId, {}, function(data) {
        returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
        getFollowUpDetails();
    });
}

// ajax api for closing the fee followup
function closeStudentFeeFollowupTrail(isStudent,status){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var followUpId = $("#delete-student-follow-up-id").text().trim();
      var studentId = $("#student-follow-up-id").text().trim();
      if(followUpId == ""){
        showErrorDialogBox("Invalid Follow Up to cancel");
        return;
      }
      $("#fee-follow-up-close-modal").modal('toggle');
      ajaxClient.post("/fees/close-fee-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+followUpId, {}, function(data) {
            returnToPaymentMainScreenDisplay();
            $("#fee-followup-trail-status-modal-container").html(data);
            $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
            getFollowUpDetails();
        });
}

// -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

function loadStudentPaymentDetails(studentId, openDetailsPage) {
  var academicSessionId = academicSessionHandler.getSelectedSessionId();
  ajaxClient.get( "/fees/student-payment-details/"+academicSessionId+"/"+studentId, function(data) {
      $("#student-payment-screen").html(data);
      feePayment.initCache();

      bindPaymentActionEvents();
      bindPaymentTransactionClickEvents();
      bindCollectFeesPaymentEvent();
      initDate(pastFeePaymentTransactionDays);
      bindViewPaymentFeesEvent();
      useWalletAmountCallBack();
      feePayment.fillNetAmountDisplay();
      setOtherSessionTotalDueAmount();
      if(openDetailsPage) {
        feePayment.collectFeesDisplay(academicSessionId);
      }
      getFeeFollowUpDetails();
  });
}

function bindCollectFeesPaymentEvent() {
    $("#fee-payment-confirm").on('click', function () {
        $("#fee-payment-confirm-modal").modal('toggle');
        var feePaymentPayload = collectFeesInformation();
        if(feePaymentPayload == null){
          return;
        }
        ajaxClient.post("/fees/collect-fee-payment",{'feePaymentPayload':JSON.stringify(feePaymentPayload)}, function(data){

            returnToPaymentMainScreenDisplay();
            $("#fee-payment-status-modal-container").html(data);
            $("#collect-fee-payment-status-modal").modal({backdrop: 'static', keyboard: false});
            loadStudentPaymentDetails(feePaymentPayload.feePaymentTransactionMetaData.studentId);
       });
    });
}

function collectFeesInformation() {
    var sessionId = $('#collect-fee-session-id').text().trim();
    var studentId = $('#collect-fee-student-id').text().trim();
    var $bankAccountInput = $('#bank-account-input');
    var bankAccountId = '';
    if ($bankAccountInput.length > 0 && $bankAccountInput.find(':selected').length > 0) {
        bankAccountId = $bankAccountInput.find(':selected').val().trim();
    }    
    var paymentMode = $('#payment-mode-input').find(':selected').val().trim();
    var reference = $('#payment-reference').val().trim();
    var date = getDate($('#payment-date').val());
    var remark = $('#payment-remark').val();
    var invalid = validateMandatoryFields($("#collect-fee-tab"));
    if(invalid){
      showErrorDialogBox("Please fill required fields before making payment.");
      return;
    }
    var invalidAmount = validateAmounts();
    if(invalidAmount) {
      return;
    }
    if(date == null){
      showErrorDialogBox("Please enter the date of payment");
      return null;
    }
    var currentDate = new Date();
    if(date.getTime() > currentDate.getTime()){
      showErrorDialogBox("Invalid payment date. Future transactions not allowd");
      return null;
    }
    // Computing the fine per fees
    $("div.collected-fees-details").each(function() {

    });
    var feeIdFeeHeadTransactionList = []
    $("div.collected-fees-details").each(function() {
          var feeId = $(this).find('p.collected-fee-id').text().trim();
          var totalPaidFine = parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val());
          var feeHeadTransactionAmounts = [];
          $(this).find("tr.feehead-payment-row").each(function() {
                var feeHeadId = $(this).find('td.payment-feehead-id').text().trim();
                var collectedAmount = $(this).find('input.collected-amount').val();
                var instantDiscountAmount = $(this).find('input.instant-discount-amount').val();
                var feeHeadTransactionAmount = {'feeHeadId' :feeHeadId, 'paidAmount' :  collectedAmount, 'instantDiscount' : instantDiscountAmount};
                feeHeadTransactionAmounts.push(feeHeadTransactionAmount);
          });
          // Dividing the fine amount equally among all the feeheads
          if(feeHeadTransactionAmounts.length > 0){
            var feeHeadFineAmount = totalPaidFine/feeHeadTransactionAmounts.length;
            for(var i = 0; i < feeHeadTransactionAmounts.length; i++){
                feeHeadTransactionAmounts[i]['fineAmount'] = feeHeadFineAmount;
            }
          }
          var feeIdFeeHeadTransaction = {'feeId' : feeId, 'feeHeadTransactionAmounts' :feeHeadTransactionAmounts};
          feeIdFeeHeadTransactionList.push(feeIdFeeHeadTransaction);
    });
    var debitWalletAmount = getUsedWalletAmount();
    var feePaymentTransactionMetaData = {'studentId' : studentId,'bankAccountId' : bankAccountId, 'transactionMode' : paymentMode, 'transactionReference': reference,'transactionDate' : date.getTime()/1000, 'academicSessionId' : sessionId , 'debitWalletAmount' : debitWalletAmount, 'remark' : remark};
    return {'feePaymentTransactionMetaData' : feePaymentTransactionMetaData, 'feeIdFeeHeadTransactionList' : feeIdFeeHeadTransactionList};
}

function validateAmounts () {

  var totalFeeAssignment = parseFloat($("#net-total-fees-assigned-amount").text());
  var paidAmount = parseFloat($("#net-total-fees-collected-amount").text());
  var instantDiscount = parseFloat($("#net-total-fees-instant-discount-amount").text());

  if(totalFeeAssignment == "" || totalFeeAssignment == 0) {
    showErrorDialogBox("No amount added to collect fees.");
    return true;
  }

  if((paidAmount == "" || paidAmount == 0)) {
    if(confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")) {
      return false;
    }
    return true;
  }

  return false;
}

function bindViewPaymentFeesEvent() {
    // $('.view-unpaid-fee').on('click', function () {
    //     var assignedFeesJson = $(this).parent().parent().find('p.unpaid-fee-info').text().trim();
    //     var assignedFees = JSON.parse(assignedFeesJson);
    //     populateViewFeeAssignmentContent(assignedFees);
    // });
    $('.view-payment-fee-info').on('click', function () {
        var transactedFeesJson = $(this).parent().parent().find('p.payment-fee-info').text().trim();
        var transactedFees = JSON.parse(transactedFeesJson);
        populateViewFeePaymentContent(transactedFees);
    });
}

function setOtherSessionTotalDueAmount () {

  $('.totalDueAmount').each( function () {
    var i = 0;
    var totalAssignedAmount = 0;
    var sumTotalDiscount = 0;
    var totalPaidAmount = 0;
    var totalBalanceAmount = 0;
    var totalDueFineAmount = 0;
    $(this).parent().parent().parent().parent().find('.payment-fee-info').each( function () {

      var transactedFeesJson = $(this).text().trim();
      var transactedFees = JSON.parse(transactedFeesJson);
      for( i = 0 ; i < transactedFees.feeHeadPaymentDetails.length; i++){
        var feeHeadName = transactedFees.feeHeadPaymentDetails[i].feeHeadConfiguration.feeHead;
        var assignedAmount = transactedFees.feeHeadPaymentDetails[i].assignedAmount;
        var assignedDiscountAmount = transactedFees.feeHeadPaymentDetails[i].assignedDiscountAmount ;
        var instantDiscountAmount = transactedFees.feeHeadPaymentDetails[i].instantDiscountAmount;
        var paidAmount = transactedFees.feeHeadPaymentDetails[i].paidAmount;
        var balanceAmount = transactedFees.feeHeadPaymentDetails[i].balanceAmount;
        var dueFineAmount = transactedFees.feeHeadPaymentDetails[i].computedDueFineAmount;
        console.log(dueFineAmount);
        var totalDiscount = assignedDiscountAmount + instantDiscountAmount;

        totalAssignedAmount += assignedAmount;
        sumTotalDiscount += totalDiscount;
        totalPaidAmount += paidAmount;
        totalBalanceAmount += balanceAmount;
        totalDueFineAmount += dueFineAmount;
      }

    });
    totalDueAmounts = totalBalanceAmount + totalDueFineAmount;
    $(this).text(totalDueAmounts.toFixed(2) + "/-");
    $(this).css("font-weight","Bold");
  });
}

function populateViewFeePaymentContent(transactedFees) {
    $('#view-payment-fee-name').text(transactedFees.feeConfigurationBasicInfo.feeName);
    var feeHeadRow = "";
    var i = 0;
    var totalAssignedAmount = 0;
    var sumTotalDiscount = 0;
    var totalPaidAmount = 0;
    var totalBalanceAmount = 0;

    for( i = 0 ; i < transactedFees.feeHeadPaymentDetails.length; i++){
      var feeHeadName = transactedFees.feeHeadPaymentDetails[i].feeHeadConfiguration.feeHead;
      var assignedAmount = transactedFees.feeHeadPaymentDetails[i].assignedAmount;
      var assignedDiscountAmount = transactedFees.feeHeadPaymentDetails[i].assignedDiscountAmount ;
      var instantDiscountAmount = transactedFees.feeHeadPaymentDetails[i].instantDiscountAmount;
      var paidAmount = transactedFees.feeHeadPaymentDetails[i].paidAmount;
      var balanceAmount = transactedFees.feeHeadPaymentDetails[i].balanceAmount;
      var totalDiscount = assignedDiscountAmount + instantDiscountAmount;

      totalAssignedAmount += assignedAmount;
      sumTotalDiscount += totalDiscount;
      totalPaidAmount += paidAmount;
      totalBalanceAmount += balanceAmount;

      feeHeadRow = feeHeadRow + "<tr> <td>" + feeHeadName + "</td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + assignedAmount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + totalDiscount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + paidAmount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + balanceAmount.toFixed(2) + "\" readonly></td> </tr>";
    }

    feeHeadRow = feeHeadRow + "<tr> <td> <strong> TOTAL </strong></td> <td>"+totalAssignedAmount.toFixed(2)+"</td> <td>"+sumTotalDiscount.toFixed(2)+"</td> <td>"+totalPaidAmount.toFixed(2)+"</td> <td>"+totalBalanceAmount.toFixed(2)+"</td> </tr>";
    $('#view-payment-feehead-details').html(feeHeadRow);
    $("#fee-payment-details-view-modal").modal('toggle');
}


var reminders = {
  dataCache : {},

  loadSendReminderPage : function () {
    ajaxClient.get("/fees/due-fees-reminder-screen", function(data) {
        $("#main-content").html(data);
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits")
        reminders.bindSelectClassCheckboxEvent();
        reminders.dataCache['sendReminders'] = {};
        reminders.dataCache.selectedStudentCount = 0;
        reminders.dataCache.templateLoaded = false;
        initSelect2();
        academicSessionHandler.bindSessionChangeEvent(reminders.loadBulkStudentScreen);
    });
  },

  loadBulkStudentScreen : function (reload, sessionId, dueDate, includeFine, includePastSessions, requiredStandards) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/load-bulk-student-screen/"+academicSessionId, function(data) {
        $("#fee-reminder-screen").html(data);
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits")
        reminders.bindSelectClassCheckboxEvent();
        reminders.dataCache['sendReminders'] = {};
        reminders.dataCache.selectedStudentCount = 0;
        reminders.dataCache.templateLoaded = false;
        initSelect2();

        if(reload) {
          ajaxClient.get("/fees/due-fees-students/"+sessionId+"/"+dueDate.getTime()/1000 +"/"+includeFine+"?requiredStandards="+requiredStandards + "&includePastSessions="+includePastSessions, function(data) {
              $("#due-fees-student-list").html(data);
              reminders.dataCache['sendReminders']['dueFeesDate'] = dueDate.getTime()/1000;
              reminders.dataCache['sendReminders']['requiredStandards'] = requiredStandards;
              reminders.dataCache.selectedStudentCount = 0;
          });

        }

    });
  },

  loadDueFeeStudentsForInstitute : function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var dueDate = getDate($('#reminder-fee-due-date').val());
    if(dueDate == null){
      showErrorDialogBox("Please fill due date.");
      return;
    }

    reminders.dataCache.dueDate = getDate($('#reminder-fee-due-date').val());

    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    reminders.dataCache.includeFine = includeFine;
    reminders.dataCache.includePastSessions = includePastSessions;

    var requiredStandards = $(".select-reminder-class").val();
    if(requiredStandards != undefined && requiredStandards != null) {
      requiredStandards = requiredStandards.join(",");
    } else {
      requiredStandards = "";
    }
    reminders.dataCache.requiredStandards = requiredStandards;
    // var requiredStandards = reminders.getRequiredStandardsCSV();

    ajaxClient.get("/fees/due-fees-students/"+sessionId+"/"+dueDate.getTime()/1000 +"/"+includeFine+"?requiredStandards="+requiredStandards+"&includePastSessions="+includePastSessions, function(data) {
        $("#due-fees-student-list").html(data);
        reminders.dataCache['sendReminders']['dueFeesDate'] = dueDate.getTime()/1000;
        reminders.dataCache['sendReminders']['requiredStandards'] = requiredStandards;
        reminders.dataCache.selectedStudentCount = 0;
    });
  },

 selectBulkStudentList : function (selectAllCheckbox) {
      if(selectAllCheckbox.checked){
        $(".bulk-student-select-checkbox").prop('checked', true);
        var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
        $("#bulk-students-selected-count").html(selectedStudentCount);
        reminders.dataCache.selectedStudentCount = selectedStudentCount;
      }
      else{
          $(".bulk-student-select-checkbox").prop('checked', false);
          $("#bulk-students-selected-count").html(0);
          reminders.dataCache.selectedStudentCount = 0;
      }
  },

  bindSelectClassCheckboxEvent: function () {
    $('#reminder-select-all-class').change(function() {
         if($(this).is(":checked")) {
           $('.select-reminder-student-class-menu').attr("style", "display:none");
         }
         else{
            $('.select-reminder-student-class-menu').attr("style", "display:block");
         }
     });
  },

  getRequiredStandards: function () {
    var requiredStandards = [];
    if($("#reminder-select-all-class").is(":checked")){
      return requiredStandards;
    }

    $('.select-reminder-class').each( function () {
        if($(this).prop("checked")){
            requiredStandards.push($(this).attr("id"));
        }
    });
    return requiredStandards;
  },

  getRequiredStandardsCSV : function () {
    var requiredStandardsCSV = "";
    var requiredStandards =  reminders.getRequiredStandards();
    for(var i = 0;i < requiredStandards.length; i++){
      if(i == 0){
        requiredStandardsCSV = requiredStandards[i];
      }else{
        requiredStandardsCSV = requiredStandardsCSV + "," + requiredStandards[i];
      }
    }

    return requiredStandardsCSV;
  },

 bulkStudentSelectCheckbox : function(studentSelectCheckbox) {
      var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
      $("#bulk-students-selected-count").html(selectedStudentCount);
      reminders.dataCache.selectedStudentCount = selectedStudentCount;
  },

  bulkReminderConfirmModal : function () {
    var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
    if(selectedStudentCount == 0){
      showErrorDialogBox("Please select atleast one student for sending reminders.");
      return;
    }
    reminders.dataCache.selectedStudentCount = selectedStudentCount;

    var includeFine = $("#include-fee-fine").is(":checked");
    var includePastSessions = $("#include-past-sessions").is(":checked");

    if(reminders.dataCache.templateLoaded){
        reminders.updateReminderModalDisplay(includeFine, includePastSessions);
        $("#bulk-reminder-confirmation-modal").modal({backdrop: 'static', keyboard: false});
        // $("#bulk-notification-confirmation-modal").modal({backdrop: 'static', keyboard: false});
        $(".selected-student-count").text(selectedStudentCount);
    }else{
      ajaxClient.get("/fees/reminder-templates", function(data){
          $("#bulk-reminder-template-modal-container").html(data);
          reminders.updateReminderModalDisplay(includeFine, includePastSessions);
          $("#bulk-reminder-confirmation-modal").modal({backdrop: 'static', keyboard: false});
          var audioTemplates = readJson("#audio-templates-json");
          reminders.bindAudioTemplateClickAction(audioTemplates);
          $(".selected-student-count").text(selectedStudentCount);
          reminders.dataCache.templateLoaded = true;
     });
    }
  },

  updateReminderModalDisplay : function (includeFine, includePastSessions) {
      if(includeFine){
          $(".fine-included").text("Yes");
      }else{
          $(".fine-included").text("No");
      }

      if(includePastSessions){
        $(".past-session-included").text("Yes");
    }else{
        $(".past-session-included").text("No");
    }

      $("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count);
      $("#audio-voice-call-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count);

      $("#audio-template-selection-display").attr("style", "display:none");
      $('#audio-template-select').val("");
      $("#send-bulk-voice-calls-button").prop('disabled', true);
      $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:none;");

      if(reminders.dataCache.selectedStudentCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
        $("#send-bulk-sms-button").prop('disabled', true);
        $("#insufficient-sms-credits-message").attr("style", "color:red; display:block;");
      }else{
        $("#send-bulk-sms-button").prop('disabled', false);
        $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
      }
  },

  bindAudioTemplateClickAction : function (audioTemplates) {
    $('#audio-template-select').change(function(){
        var templateId = $(this).val();
        if(templateId == ""){
          $("#audio-template-selection-display").attr("style", "display:none");
          $("#send-bulk-voice-calls-button").prop('disabled', true);
          return;
        }

        $("#audio-template-selection-display").attr("style", "display:block");

        for(var i = 0; i < audioTemplates.length; i++){
          var audioTemplate = audioTemplates[i];
          if(templateId == audioTemplate.templateId){
            $("#template-name").text(audioTemplate.templateName);
            $("#template-duration").text(round(audioTemplate.metadata.audio_metadata["duration"], 0));
            $("#template-credits").text(audioTemplate.credits);

            var totalCredits = audioTemplate.credits * reminders.dataCache.selectedStudentCount;
            $("#total-student-credits").text(totalCredits);
            reminders.dataCache.selectedAudioCredits = audioTemplate.credits;

            if(totalCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
                $("#send-bulk-voice-calls-button").prop('disabled', true);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:block;");
            }else{
                $("#send-bulk-voice-calls-button").prop('disabled', false);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:none;");
            }

            break;
          }
        }
    });
  },

  bulkDemandNoticeConfirmModal : function () {
    var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
    if(selectedStudentCount == 0){
      showErrorDialogBox("Please select atleast one student for generating fee notice.");
      return;
    }
    var includeFineText = "<span style=\"color:red\">(FINE AMOUNT NOT INCLUDED)</span>";
    var demandNoticeButtonText = "Yes, Fee Notice Without Fine";
    if($("#include-fee-fine").is(":checked")){
      includeFineText = "<span style=\"color:green\">(FINE AMOUNT INCLUDED)</span>";
      demandNoticeButtonText = "Yes, Fee Notice With Fine";
    }

    $("#demand-notice-confirm-button").html(demandNoticeButtonText);

    $("#bulk-demand-notice-confirmation-modal-text").html("You have selected "+selectedStudentCount+ " students to generate fee notice. Do you want to proceed? <br>" + includeFineText);
    $("#bulk-demand-notice-confirmation-modal").modal({backdrop: 'static', keyboard: false});

  },

  sendBulkSMSReminders : function () {
    if(reminders.dataCache.selectedStudentCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + reminders.dataCache.selectedStudentCount + " SMS. Please recharge.");
      return;
    }
    var batchName = $("#sms-reminder-fee-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent reminders.")
      return;
    }

    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'SMS', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  sendAudioVoiceCall : function () {
    if(reminders.dataCache.selectedStudentCount *  reminders.dataCache.selectedAudioCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + reminders.dataCache.selectedStudentCount + " voice calls. Please recharge.");
      return;
    }

    var batchName = $("#voice-reminder-fee-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent reminders.")
      return;
    }
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var templateId = $('#audio-template-select').find(':selected').val().trim();
    if(templateId == ""){
      showErrorDialogBox("Please select audio clip for voice call");
      return;
    }

    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'CALL', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'audioTemplateId' : templateId, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  sendMobileAppNotifications : function () {
    var batchName = null;
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var templateId = null;
    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'APP', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'audioTemplateId' : templateId, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  generateDemandNotice : function () {
    $("#bulk-demand-notice-confirmation-modal").modal('toggle');
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }
    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }
    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    var studentIdsCSV = "";

    for(var i = 0; i < studentIds.length; i++){
        if(i == 0){
          studentIdsCSV += studentIds[i];
        }
        else{
          studentIdsCSV =  studentIdsCSV + "," + studentIds[i];
        }

    }
    demandNoticePayload = {'academicSessionId' : academicSessionId, 'dueFeesDate': dueFeesDate, 'includeFine' : includeFine, 'requiredStanardsCSV' : requiredStandards, 'students' : studentIds, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/submit-demand-notice-students",{'demandNoticePayload':JSON.stringify(demandNoticePayload)}, function(demand_notice_request_id){
        window.open(baseURL + "/fees/generate-demand-notice/"+demand_notice_request_id, '_blank');
   });
  },

  getSelectedBulkStudentIds :function () {
    var studentIds = [];
    $("input.bulk-student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-reminder-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    return studentIds;
  },

  loadNotificationHistoryHomePage : function () {
    ajaxClient.get("/fees/notification-history-homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(reminders.changeSession);
        reminders.getNotificationHistory(true, "SMS", "BATCH");
        reminders.dataCache['notificationHistoryTab'] = "BATCH";
        // reminders.loadNotificationHistoryEvents();
        //
        //
        // getPaymentTransactions(true, "ACTIVE");
        // academicSessionHandler.bindSessionChangeEvent(loadPaymentTransactionsForSession);
        // bindPaymentTransactionClickEvents();

    });
  },

  changeSession : function () {
    var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
    var deliveryModeTab = reminders.getDeliveryModeTab();
    reminders.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
  },

  getNotificationHistory : function (freshSearch, deliveryMode, notificationHistoryTab) {
    reminders.dataCache['notificationHistoryTab'] = notificationHistoryTab;
    reminders.dataCache['deliveryModeTab'] = deliveryMode;

    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;

    var sessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.get("/fees/notification-history/"+sessionId+"/"+deliveryMode+"/"+notificationHistoryTab+"/"+offset+"/"+itemsPerPage, function(data) {
        var screenHeight = 0;
        if(deliveryMode == "SMS"){
          $("#smsNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }else if(deliveryMode == "CALL"){
          $("#voiceNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        } else if(deliveryMode == "APP"){
          $("#mobileNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }

        $('.notification-history-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
        var response = JSON.parse($("#pagination-info").text().trim());
        $('#items-per-page').val(response.itemsPerPage);
        $('.page-item').removeClass('active');
        var pageNumber = (response.offset/response.itemsPerPage) + 1;
        $('#page-number-'+page_number).addClass('active');
        if(notificationHistoryTab == 'INDIVIDUAL'){
          $("#nav-individual-history-tab").addClass('active');
          $("#nav-batch-history-tab").removeClass('active');
          $("#individual-history-content").addClass('show active');
          $("#batch-history-content").removeClass('show active');
        }
        reminders.bindIndividualNotificationsContentView();
        reminders.initPagination();
    });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
      }
    );
  },

  getDeliveryModeTab : function () {
      var deliveryModeTab = reminders.dataCache['deliveryModeTab'];
      if(deliveryModeTab == null || deliveryModeTab == ""){
        return "SMS";
      }
      return deliveryModeTab;
  },

  loadBatchNotificationsDetails : function (batchId) {
    ajaxClient.get("/fees/batch-notification-details/"+batchId, function(data) {
        $("#batch-notification-detail-modal-container").html(data);
        $("#view-batch-notification-modal").modal('toggle');
    });
  },

  bindIndividualNotificationsContentView : function () {
    $(".view-individual-notification").click(function () {
        var notificationContent = $(this).parent().find("p.notification-content").text().trim();
        $("#individual-notification-detail-modal").find(".modal-body").html("<span style=\"white-space: pre-line;\">" + notificationContent + "</span>");
        $("#individual-notification-detail-modal").modal('toggle');
    });
  },

  // -----------------------------------------------For Reminder Follow Up FE --------------------------------------------


   loadReminderStudentFollowUpDetails :function(studentId){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/get-follow-up-remainder-details/"+academicSessionId+"/"+studentId,function(data){
            $("#fee-reminder-screen").html(data);
            $("#academic-session").attr('style','display:none;');
      });
  },

  closeReminderFeeFollowUpDetails: function(studentId){
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close All Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong style=\"color:red\">Are you sure that you want to close the all followup trail for this student ?</strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.closeStudentRemainderFeeFollowupTrail('true','INACTIVE')\">Yes, Close All</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(studentId);
      $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
  },

  closeStudentRemainderFeeFollowupTrail:  function(isStudent,status){
      $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var studentId = $("#delete-student-follow-up-id").text().trim();
      var followUpStudentId = studentId;
      if(studentId == ""){
        showErrorDialogBox("Invalid Follow Up to cancel");
        return;
      }


        ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+studentId+"/"+followUpStudentId, {}, function(data) {
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
            reminders.loadReminderStudentFollowUpDetails();
        });
  },

  closeFeeReminderFollowUpDetails : function (followUpId,studentId,isStudent){
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong>Are you sure that you want to close this followup trail ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.closeStudentReminderSingleFeeFollowupTrail('false','INACTIVE')\">Yes, Close It</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";

        $("#modal-header").html(modalHeader);
        $("#modal-body").html(modalBody);
        $("#modal-footer").html(modalFooter);
        $("p#delete-student-follow-up-id").text(followUpId);
        $("p#student-follow-up-id").text(studentId);
        $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
  },

   closeStudentReminderSingleFeeFollowupTrail :function(isStudent,status){
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var followUpId = $("#delete-student-follow-up-id").text().trim();
        var studentId = $("#student-follow-up-id").text().trim();
        if(followUpId == ""){
          showErrorDialogBox("Invalid Follow Up to cancel");
          return;
        }
          $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
          ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+followUpId+"/"+studentId, {}, function(data) {
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
            reminders.loadReminderStudentFollowUpDetails();
          });
  },

    addFeeReminderFollowUpDetails : function(studentId){
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        $("p#student-follow-up-id").text(studentId);
        $("#next-transaction-fee-follow-up-date").val("");
        $("#add-fee-reminder-follow-up-modal").modal('toggle');
  },

  backButton : function() {

      var sessionId = academicSessionHandler.getSelectedSessionId();
      var dueDate = reminders.dataCache.dueDate;
      var includeFine = reminders.dataCache.includeFine;
      var includePastSessions = reminders.dataCache.includePastSessions;
      var requiredStandards = reminders.dataCache.requiredStandards;
      reminders.loadBulkStudentScreen(true, sessionId, dueDate, includeFine, includePastSessions, requiredStandards);

  },


   addReminderStudentFeeFollowupTrail: function(status){
        var invalid = validateMandatoryFields($("#add-fee-reminder-follow-up-modal"));
        if(invalid){
          return;
        }
        var sessionId = academicSessionHandler.getSelectedSessionId();
        var entityName = $("select#entity-transaction-name").val();
        var followUpDate = getDate($('#fee-transaction-follow-up-date').val());
        if(followUpDate == null){
          showErrorDialogBox("Please fill follow up date.");
          return;
        }
        var followUpDate = followUpDate.getTime()/1000;

        var contactPersonName = $("#contact-person-transaction-name").val();
        var followUpType = $("select#follow-up-transaction-type").val();
        var conversation = $("#transaction-conversation").val();
        var nextFollowUpDate = $('#next-transaction-fee-follow-up-date').val();
        if(nextFollowUpDate != ""){
          nextFollowUpDate = getDate($('#next-transaction-fee-follow-up-date').val());
          nextFollowUpDate = nextFollowUpDate.getTime()/1000;
        }
        if(nextFollowUpDate == null || nextFollowUpDate == ""){
          nextFollowUpDate = null;
        }

        if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
          $("#add-fee-reminder-follow-up-modal").modal('toggle');
          showErrorDialogBox("Follow up date cannot exceed the next follow up date");
          return;
        }
        var amount = $("#amount-transaction").val();
        var followUpStatus = status;
        var entityId = $("#student-follow-up-id").text();
        var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
        "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
        $("#add-fee-reminder-follow-up-modal").modal('toggle');

        ajaxClient.post("/fees/add-fee-reminder-trail/"+sessionId+"/"+entityId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
       });
 },

 editReminderFeeFollowUpDetails : function(value){
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
      initPastDateById("fee-follow-up-date", 4000);
      var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
      $("#update-transaction-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
      $("#update-transaction-fee-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
      $("#update-transaction-contact-person-name").val(followUpPayload.contactPersonName);
      $("#update-transaction-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
      $("#update-transaction-conversation").val(followUpPayload.conversation);

      if(followUpPayload.nextFollowUpDate != null){
        $("#update-transaction-next-fee-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
      }
      else{
          $("#update-transaction-next-fee-follow-up-date").val("");
      }
      $("#update-transaction-amount").val(followUpPayload.amount);
      $("#update-student-follow-up-id").text(followUpPayload.followUpId);
      $("#student-id").text(followUpPayload.entityId);
      $("#update-reminder-fee-follow-up-modal").modal('toggle');
},

 updateStudentReminderFeeFollowupTrail: function(status){
     var invalid = validateMandatoryFields($("#update-reminder-fee-follow-up-modal"));
     if(invalid){
       return;
     }
     var sessionId = academicSessionHandler.getSelectedSessionId();
     var entityName = $("select#update-transaction-entity-name").val();
     var followUpDate = getDate($('#update-transaction-fee-follow-up-date').val());
     if(followUpDate == null){
       showErrorDialogBox("Please fill follow up date.");
       return;
     }
     var followUpDate = followUpDate.getTime()/1000;
     var followUpMode = $("select#update-transaction-follow-up-mode").val();
     var contactPersonName = $("#update-transaction-contact-person-name").val();
     var followUpType = $("select#update-transaction-follow-up-type").val();
     var conversation = $("#update-transaction-conversation").val();
     var nextFollowUpDate = $('#update-transaction-next-fee-follow-up-date').val();
     if(nextFollowUpDate != ""){
       nextFollowUpDate = getDate($('#update-transaction-next-fee-follow-up-date').val());
       nextFollowUpDate = nextFollowUpDate.getTime()/1000;
     }
     if(nextFollowUpDate == null || nextFollowUpDate == ""){
       nextFollowUpDate = null;
     }

     if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
       $("#update-reminder-fee-follow-up-modal").modal('toggle');
       showErrorDialogBox("Follow up date cannot exceed the next follow up date");
       return;
     }

     var amount = $("#update-transaction-amount").val();
     var followUpStatus = status;
     var followUpId = $("#update-student-follow-up-id").text();
     var entityId = $("#student-id").text();
     var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
     "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
     $("#update-reminder-fee-follow-up-modal").modal('toggle');
     ajaxClient.post("/fees/update-reminder-followup-trail/"+sessionId+"/"+entityId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
       $("#fee-payment-reminder-student-detail-modal-container").html(data);
       $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
       reminders.loadReminderStudentFollowUpDetails();
    });
},

 deleteReminderFeeFollowUpDetails: function(followUpId,studentId){
       var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
       var modalBody = "<strong color=\"red\">Are you sure that you want to Delete this followup trail ?</strong>";
       var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.deleteStudentReminderFeeFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
       $("#modal-header").html(modalHeader);
       $("#modal-body").html(modalBody);
       $("#modal-footer").html(modalFooter);
       $("p#delete-student-follow-up-id").text(followUpId);
       $("p#student-follow-up-id").text(studentId);
       $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
},

deleteStudentReminderFeeFollowupTrail: function (){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
    $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
    ajaxClient.post( "/fees/delete-fee-reminder-followup-trail/"+academicSessionId+"/"+followUpId+"/"+studentId, {}, function(data) {
        $("#fee-payment-reminder-student-detail-modal-container").html(data);
        $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
        reminders.loadReminderStudentFollowUpDetails();
    });
}
};


var studentWalletHandler = {
  dataCache : {},

  confirmWalletRecharge: function () {
    var studentId = $("#wallet-recharge-student-id").text().trim();
    var studentNameIdentifier = $("#wallet-recharge-student-name-identifier").text().trim();
    var invalid = validateMandatoryFields($("#student-wallet-recharge-modal").find(".modal-body"));
    if(invalid){
      return;
    }

    var rechargeAmount = getFloatValue($("#student-recharge-wallet-amount").val());
    if(rechargeAmount <= 0){
      $("#student-recharge-wallet-amount").css("border", "1px solid #ff8795");
      $("#student-recharge-wallet-amount").after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">Wallet amount should be > 0</span></p>");
      return;
    }
    var description = $("#student-recharge-wallet-description").val();
    var paymentMode = $('#student-recharge-payment-mode').find(':selected').val().trim();
    var rechargeWalletDate = getDate($('#student-rechage-wallet-date').val());

    studentWalletHandler.dataCache.studentId = studentId;
    studentWalletHandler.dataCache.studentNameIdentifier = studentNameIdentifier;
    studentWalletHandler.dataCache.amount = rechargeAmount;
    studentWalletHandler.dataCache.description = description;
    studentWalletHandler.dataCache.paymentMode = paymentMode;
    studentWalletHandler.dataCache.transactionDate = rechargeWalletDate.getTime()/1000;

    $("#student-wallet-recharge-modal").modal("toggle");

    var confirmText = "Do you want to recharge wallet of student " +studentNameIdentifier + " with amount = " + rechargeAmount + "/- ?";
    $("#student-wallet-recharge-confirm-modal").find(".student-wallet-recharge-confirm-text").text(confirmText);
    $("#student-wallet-recharge-confirm-modal").modal({backdrop: 'static', keyboard: false});

  },

  rechargeWallet : function () {
    $("#student-wallet-recharge-confirm-modal").modal("toggle");
    var walletRechargePayload = {"studentId" : studentWalletHandler.dataCache.studentId, "amount" : studentWalletHandler.dataCache.amount,
    "description" : studentWalletHandler.dataCache.description, "transactionMode" : studentWalletHandler.dataCache.paymentMode, "transactionDate" : studentWalletHandler.dataCache.transactionDate};

    ajaxClient.post("/fees/recharge-student-wallet", { "walletRechargePayload" : JSON.stringify(walletRechargePayload) }, function(data) {
        $("#student-wallet-recharge-status-modal-container").html(data);
        $("#student-wallet-recharge-status-modal").modal('toggle');
        loadStudentPaymentDetails(studentWalletHandler.dataCache.studentId);
    });
  },

  loadWalletTransactions : function () {
    var studentId = $("#wallet-recharge-student-id").text().trim();
    ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
        $("#student-wallet-transactions-modal-container").html(data);
        $("#student-wallet-transactions-modal").modal({backdrop: 'static', keyboard: false});
    });
  }
}

function roundOffNumber(number, decimalPlaces) {
  return parseFloat(number.toFixed(decimalPlaces));
}

function sortMapByValue(dict) {
  // Convert dictionary to array of arrays
  var items = Object.keys(dict).map(function(key) {
    return [key, dict[key]];
  });

  // Sort the array based on the second element (the value) in ascending order
  items.sort(function(first, second) {
    return first[1] - second[1];
  });

  // Convert back to dictionary
  var sortedDict = {};
  items.forEach(function(item) {
    sortedDict[item[0]] = item[1];
  });
  return sortedDict;
}

function validateSiblingPaymentAmounts () {

  var totalFeeAssignment = parseFloat($("#sibling-total-balance-amount-display").text());
  var paidAmount = parseFloat($("#sibling-student-paid-amount").text());
  var instantDiscount = parseFloat($("#sibling-total-instant-discount-amount").text());

  if(totalFeeAssignment == "" || totalFeeAssignment == 0) {
    showErrorDialogBox("No amount added to collect fees.");
    return true;
  }

  if((paidAmount == "" || paidAmount == 0)) {
    if(confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")) {
      return false;
    }
    return true;
  }

  var flag = false;
  var errorMessage = "";
  $("tr.sibling-payment-details-tr").each(function() {
    var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
    var studentFullName = siblingDueFeeDetail.studentFullName;
    // var fineCollectionAmount = $(".sibling-student-fine-amount").val();
    // var instantDiscountAmount = $(".sibling-student-instant-discount-amount").val();
    // var walletAmount = 0;
    var paidAmount = $(this).find(".sibling-student-paid-amount").val();
    var netPayableAmount = $(this).find(".sibling-student-net-payable-amount").val();
    if(parseFloat(paidAmount) > parseFloat(netPayableAmount)) {
      if(!(errorMessage === "")) {
        errorMessage += "<br/>";
      }
      errorMessage += "For " + studentFullName + " paid amount cannot be greater than net payable amount!";
      flag = true;
    }
  });

  if(flag) {
    showErrorDialogBox(errorMessage);
  }

  return flag;
}

pastReportAllowedDays = 365
var ALL = "ALL";
var ONLY_DUE_STUDENTS = "ONLY_DUE_STUDENTS";
var ONLY_NON_DUE_STUDENTS = "ONLY_NON_DUE_STUDENTS";

var feeReports = {

  dataCache : {},

  loadReportsMenu: function (){
      ajaxClient.get("/fees/reports", function(data) {
          $("#main-content").html(data);
          initDateWithYearRange("-5:+5", true);
          initSelect2("All");
          feeReports.initDataCache();
          feeReports.initMultiSelectAcademicSession();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          feeReports.bindGenerateReportEvent();
          feeReports.checkboxEvents();
          reportUtils.bindSelectClassCheckboxEvent();

          if(feeReports.dataCache.allSessions.length > 0){
            var feeDropDown = feeReports.getFeesDropDown(feeReports.dataCache.allSessions[0].academicSessionId);
            var dropDownElement = $(".reports-student-fees");
            dropDownElement.html(feeDropDown);
          }
      });
  },

  initMultiSelectAcademicSession : function(title = "") {
    $(".select2-multi-academic-session").each(function() {
        $(this)
          .wrap("<div class=\"position-relative\"></div>")
          .select2({
            placeholder: title,
            dropdownParent: $(this).parent(),
            closeOnSelect: false
          });
      });
  },

  initDataCache : function () {
    var allSessionFees = readJson("#all-session-fees");
    var allSessions = readJson("#all-sessions");
    var currentSession = readJson("#selected-academic-session-json");

    feeReports.dataCache.allSessionFees = allSessionFees;
    feeReports.dataCache.allSessions = allSessions;

    $(".report-academic-session").val(currentSession.academicSessionId);

  },

  getRequiredFeesCSV : function (containerElement) {
    var requiredFeesCSV = "";

    var requiredFees = $(".reports-student-fees").val();
    requiredFeesCSV = requiredFees.join();
  },

  getFeesDropDown : function (selectedSessionId) {
    var allSessionFees = feeReports.dataCache.allSessionFees;
    var sessionFees = allSessionFees[selectedSessionId];

    var feeDropDown = "";
    if(sessionFees == undefined || sessionFees == null){
      return feeDropDown;
    }

    for(var i = 0; i < sessionFees.length; i++){
      feeDropDown += "<option value=\""+ sessionFees[i].feeConfigurationBasicInfo.feeId + "\">" + sessionFees[i].feeConfigurationBasicInfo.feeName + "</option>"
    }
    return feeDropDown;
  },

  checkboxEvents : function () {
    $('.all-column-select').on('change', function() {
        const childCheckboxes = $(this).closest('.report-header-container').find('.select-report-column');

        if ($(this).is(':checked')) {
            childCheckboxes.prop('checked', true);
        } else {
            childCheckboxes.each(function() {
                if (!$(this).is(':disabled')) {
                    $(this).prop('checked', false);
                }
            });
        }
    });
    },


  bindGenerateReportEvent: function (){

    $(".report-academic-session").change(function() {
      var selectedSessionId = $(this).find(':selected').val().trim();
      if(selectedSessionId === undefined || selectedSessionId === "" || selectedSessionId === null) {
        return;
      }
      var feeDropDown = feeReports.getFeesDropDown(selectedSessionId);
      var dropDownElement = $(this).closest(".modal-body").find(".reports-student-fees");
      dropDownElement.html(feeDropDown);
      ajaxClient.get("/fees/standards/"+selectedSessionId, function(data) {
        $(".standard-with-section-options-wrapper").html(data);
        initSelect2("All");
      });
    });

    $(".reports-fee-report-data-type").on('change', function () {
      var selectedFeeReportDataType = $(this).find(':selected').val().trim();
      if(selectedFeeReportDataType === ALL) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:none;");
      } else if(selectedFeeReportDataType === ONLY_DUE_STUDENTS) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:inline-block;");
      } else if(selectedFeeReportDataType === ONLY_NON_DUE_STUDENTS) {
        $(this).parent().parent().parent().find("#fee-due-date-div").attr("style", "display:inline-block;");
      }
    });

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }

        var academicSession = "";
        if($(containerElement).find(".report-academic-session option:selected").length > 0){
          academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        }

        academicSession = academicSession === "" ? 0 : academicSession;

        var requiredMultiSession = "";
        if($(containerElement).find(".report-multi-academic-sessions").length > 0){
              requiredMultiSession = $(containerElement).find(".report-multi-academic-sessions").val().join();
        }

        var transactionModeArr = "";
        if($(containerElement).find(".reports-transaction-mode ").length > 0){
          transactionModeArr = $(containerElement).find(".reports-transaction-mode ").val();
        }
        var transactionMode=transactionModeArr.toString();

        var reportType = $(containerElement).find('p.report-type').text().trim();
        var reportStartDate = -1;
        var reportEndDate = -1;
        var feeDueDate = -1;

        if($(containerElement).find('input.report-start-date').length > 0){
            reportStartDate = getDate($(containerElement).find('input.report-start-date').val()).getTime()/1000;
        }
        if($(containerElement).find('input.report-end-date').length > 0){
            reportEndDate = getDate($(containerElement).find('input.report-end-date').val()).getTime()/1000;
        }

        var requiredStandards = "";
        if(reportType === "STUDENT_LEDGER") {
          requiredStandards = $(containerElement).find(".reports-student-class").val();
        } else {
          if($(containerElement).find(".reports-student-class").length > 0){
                requiredStandards = $(containerElement).find(".reports-student-class").val().join();
          }

        }

        var requiredFees = "";
        if($(containerElement).find(".reports-student-fees").length > 0){
              requiredFees = $(containerElement).find(".reports-student-fees").val().join();
        }

        var studentStatus = "";
          if($(containerElement).find(".reports-student-status").length > 0){
            studentStatus = $(containerElement).find(".reports-student-status").val().join();
        }

        var requiredFeeHeads = "";
        if($(containerElement).find(".reports-student-fee-heads").length > 0){
              requiredFeeHeads = $(containerElement).find(".reports-student-fee-heads").val().join();
        }

        var transactionType = $(containerElement).find('.transaction-type').val();
        if(transactionType === undefined) {
          transactionType = "";
        } else {
          transactionType = transactionType.trim();
        }

        if($(containerElement).find('input.fee-due-date').length > 0){
          feeDueDateObj =  getDate($(containerElement).find('input.fee-due-date').val());
          if(feeDueDateObj != null){
            feeDueDate = feeDueDateObj.getTime()/1000;
          }
        }

        var feeReportDataType = "";
        if($(containerElement).find(".reports-fee-report-data-type option:selected").length > 0){
          feeReportDataType = $(containerElement).find(".reports-fee-report-data-type option:selected").val();
          if(feeReportDataType === ALL) {
            feeDueDate = -1;
          } else if(feeReportDataType === ONLY_DUE_STUDENTS) {
              if(feeDueDate <= 0) {
                $(containerElement).find("input.fee-due-date").css("border", "1px solid #ff8795");
                return;
              }
          } else if(feeReportDataType === ONLY_NON_DUE_STUDENTS) {
            if(feeDueDate <= 0) {
              $(containerElement).find("input.fee-due-date").css("border", "1px solid #ff8795");
              return;
            }
          }
        }

        var reportDataVisibility = "";
        if($(containerElement).find(".reports-show-fee-head option:selected").length > 0){
           reportDataVisibility = $(containerElement).find(".reports-show-fee-head option:selected").val();
        }

        var hideStudentWithZeroFeesAssignment = $(containerElement).find('input.students-with-zero-fees-assignment').is(':checked');
        var showStudentWithZeroWalletBalance = $(containerElement).find('input.student-with-zero-wallet-balance').is(':checked');
        
        $(this).closest('div.modal').modal('toggle');
        window.open(baseURL+"/fees/generate-report/"+reportType+"?academic_session_id="+academicSession+"&requiredHeaders="+requiredHeadersCSV+"&requiredMultiSession="+requiredMultiSession+"&reportStartDate="+reportStartDate+"&reportEndDate="+reportEndDate+"&fee_ids="+requiredFees+"&requiredStandards="+requiredStandards+"&feeDueDate="+feeDueDate +"&studentStatus="+studentStatus+"&fee_head_ids="+requiredFeeHeads+"&transactionType="+transactionType+"&feeReportDataType="+feeReportDataType+"&transactionMode="+transactionMode+"&hideStudentWithZeroFeesAssignment="+hideStudentWithZeroFeesAssignment+"&showStudentWithZeroWalletBalance="+showStudentWithZeroWalletBalance+"&reportDataVisibility="+reportDataVisibility,'_blank');
    });
  }

};
