var pastFeePaymentTransactionDays = 4000;
var lastCollectedAmount = 0;
var lastInstantDiscountAmount = 0;
var lastTotalFineAmount = 0;
var lastFeeLevelPaidFineAmount = 0;

function loadPaymentPage(){
    ajaxClient.get("/fees/payment", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(feePayment.changeSession);
        bindPaymentSearchStudentEvent();
    });
}

var feePayment = {

    dataCache : {},

    initCache : function () {
      var studentFeePaymentDetails = readJson("#student-fee-payment-details-json");
      feePayment.dataCache['studentFeePaymentDetails'] = studentFeePaymentDetails;
      var sessionDueFees = {};
      var sessionData = {};
      var dueFeeDetailsMap = {};

      var selectedSessionId = studentFeePaymentDetails.selected_session.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
      sessionDueFees[selectedSessionId] = studentFeePaymentDetails.selected_session.due_fee_list;
      sessionData[selectedSessionId] = studentFeePaymentDetails.selected_session.student.studentAcademicSessionInfoResponse.academicSession;

      for(var i =0; i< studentFeePaymentDetails.selected_session.due_fee_list.length; i++){
        dueFeeDetailsMap[studentFeePaymentDetails.selected_session.due_fee_list[i].feeConfigurationBasicInfo.feeId] = studentFeePaymentDetails.selected_session.due_fee_list[i];
      }

      for(var i = 0; i < studentFeePaymentDetails.other_sessions.length; i++){
        var otherSession = studentFeePaymentDetails.other_sessions[i];
        var otherSessionId = otherSession.student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
        sessionDueFees[otherSessionId] = otherSession.due_fee_list;
        sessionData[otherSessionId] = otherSession.student.studentAcademicSessionInfoResponse.academicSession;

        for(var j =0; j < otherSession.due_fee_list.length; j++){
          dueFeeDetailsMap[otherSession.due_fee_list[j].feeConfigurationBasicInfo.feeId] = otherSession.due_fee_list[j];
        }

      }
      feePayment.dataCache['sessionDueFees'] = sessionDueFees;
      feePayment.dataCache['sessionData'] = sessionData;
      feePayment.dataCache['dueFeeDetailsMap'] = dueFeeDetailsMap;
    },


    doneStudentSearchTyping: function(resultArea) {
      var searchText = $('#student-payment-search-text').val().trim();
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var status = "ENROLLED,ENROLMENT_PENDING,RELIEVED,NSO";
      studentLiveSearchEvent(sessionId, searchText, resultArea, loadStudentPaymentDetails, status);
    },

    changeSession: function() {
      $("#student-payment-search-text").val("");
      $("#student-payment-screen").html("");
      feePayment.dataCache = {};
    },

    showFeeHeadPaymentDetails : function (arrowWrapper) {
      if($(arrowWrapper).hasClass("closed")){
          $(arrowWrapper).find(".arrow").attr("style", "display:none");
          $(arrowWrapper).find(".arrow.up").attr("style", "display:block");
          $(arrowWrapper).removeClass("closed");
          $(arrowWrapper).addClass("open");
      }else{
        $(arrowWrapper).find(".arrow").attr("style", "display:none");
        $(arrowWrapper).find(".arrow.down").attr("style", "display:block");
        $(arrowWrapper).removeClass("open");
        $(arrowWrapper).addClass("closed");
      }
    },

   populateCollectFeesScreen : function (sessionId) {
      var dueFeeList = feePayment.dataCache['sessionDueFees'][sessionId];
      var sessionData = feePayment.dataCache['sessionData'][sessionId];
      $("#collect-fee-session-display").html(sessionData.displayName);
      $("#collect-fee-session-id").html(sessionId);
      // $("#payment-mode-input").val("CASH");
      // $("#payment-date").val(getTodayDateInInputFormat());

      var dueFeeRows = "";
      for(var i = 0; i < dueFeeList.length; i++){
           dueFeeRows += " <tr class=\"collect-fee-row\" id=\""+dueFeeList[i].feeConfigurationBasicInfo.feeId+"\"> <td>   <div class=\"form-check\">  <input type=\"checkbox\" class=\"form-check-input collect-fee-enable\" >  <label class=\"form-check-label\"> "+dueFeeList[i].feeConfigurationBasicInfo.feeName+" </label>  </div> </td> </tr>";
      }

      $("#due-fee-list-container").html(dueFeeRows);
      $('.select-all-fee-enable').prop('checked', false);
      $("#selected-fees-end").siblings().remove();
      feePayment.bindFeesForPaymentCheckboxes();
      feePayment.computeFeesPaymentAmounts();
      feePayment.fillNetAmountDisplay();

    },

    collectFeesDisplay : function(sessionId) {
      var restrictSessionFeesPaymentJson = $("#restrict-session-fees-payment-json").text().trim();
      if(restrictSessionFeesPaymentJson != "None" && restrictSessionFeesPaymentJson != undefined) {
        var restrictSessionFeesPayment = JSON.parse(restrictSessionFeesPaymentJson);
        if(restrictSessionFeesPayment) {
          showErrorDialogBox("Student past session fees are still due, please clear them then take current session payment!");
          return;
        }
      }
      $(".payment-main-screen-info").attr("style", "display:none");
      $(".fee-payment-tab").attr("style", "display:none");
      feePayment.populateCollectFeesScreen(sessionId);

      $("#collect-fee-tab").attr("style", "display:block");
    },

    bindFeesForPaymentCheckboxes :function() {
        $('.select-all-fee-enable').change(function () {
           if($(this).is(":checked")) {
             $('.collect-fee-enable').prop('checked', true);
             $('.collect-fee-enable').trigger("change");
           }else{
             $('.collect-fee-enable').prop('checked', false);
             $('.collect-fee-enable').trigger("change");
           }
        });

        $('.collect-fee-row').on('click', function () {
          if($(this).find('.collect-fee-enable').is(":checked")) {
            $(this).find('.collect-fee-enable').prop('checked', false);
            $(this).find('.collect-fee-enable').trigger("change");
          }else{
            $(this).find('.collect-fee-enable').prop('checked', true);
            $(this).find('.collect-fee-enable').trigger("change");
          }
        });

        $('.collect-fee-enable').on('click', function () {
          if($(this).is(":checked")) {
            $(this).prop('checked', false);
            $(this).trigger("change");
          }else{
            $(this).prop('checked', true);
            $(this).trigger("change");
          }
        });

        // $('.collect-fee-row-label').on('click', function () {
        //   if($(this).siblings('.collect-fee-enable').is(":checked")) {
        //     $(this).siblings('.collect-fee-enable').prop('checked', false);
        //     $(this).siblings('.collect-fee-enable').trigger("change");
        //   }else{
        //     $(this).siblings('.collect-fee-enable').prop('checked', true);
        //     $(this).siblings('.collect-fee-enable').trigger("change");
        //   }
        // });


        $('.collect-fee-enable').change(function() {
             var feeName = $(this).parent().find("label").text();
             var feeId = $(this).closest("tr").attr("id").trim();
             var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];

             if($(this).is(":checked")) {
                if($("#fee-card-"+feeId).length == 0) {
                  feePayment.insertFeesDetails(feeDetails);
                }
             }
             else{
               $('.select-all-fee-enable').prop('checked', false);

               var oldVariousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();

               if($("#fee-card-"+feeId).length > 0) {
                  $("#fee-card-"+feeId).remove();
               }

               feePayment.computeFeesPaymentAmounts();
               feePayment.computeFineAmount();
               feePayment.fillNetAmountDisplay();

               var newVariousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
               var totalNewCollectedAmount = newVariousTotalFeesAmounts['totalCollectedAmount'] + newVariousTotalFeesAmounts['totalFineAmount'];
               var totalOldCollectedAmount = oldVariousTotalFeesAmounts['totalCollectedAmount'] + oldVariousTotalFeesAmounts['totalFineAmount'];
               // Fees is removed for which there was some amount present
               if(totalNewCollectedAmount < totalOldCollectedAmount){
                  var lessAmount = totalOldCollectedAmount - totalNewCollectedAmount;
                  // First Keep wallet amount fixed and decrease paid amount only.
                  // If paid amount is not sufficient reduce wallet amount
                  var existingPaidAmount = feePayment.getPaidAmountValue();
                  if(lessAmount <= existingPaidAmount){
                     $("#student-paid-amount").val(existingPaidAmount - lessAmount);
                  }else{
                    if(!feePayment.isWalletUsed()){
                       showErrorDialogBox("Invalid state. Fee amount was not included in paid amount");
                       return;
                    }
                    $("#student-paid-amount").val(0);
                    var existingWalletAmount = getUsedWalletAmount();
                    var newWalletAmount = existingWalletAmount - (lessAmount - existingPaidAmount);
                    if(newWalletAmount < 0){
                       showErrorDialogBox("Invalid state. Fee amount was not included in paid amount of wallet");
                       return;
                    }
                    $("#student-used-wallet-amount").val(newWalletAmount);
                  }
               }
             }
         });

         $("#student-used-wallet-amount").focus(function() {}).blur(function() {
           var walletAmount = feePayment.getStudentWalletAmount();
           var usedWalletAmount = feePayment.getUsedWalletAmountValue();
           if(usedWalletAmount > walletAmount){
             showErrorDialogBox("Used wallet amount "+usedWalletAmount+" cannot be greater then student available wallet amount "+walletAmount+". Decreasing used wallet amount");
             usedWalletAmount = walletAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }

           var paidAmount = feePayment.getPaidAmountValue();
           var totalFeeAmount = usedWalletAmount + paidAmount;

           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
           // Atleast fine amount has to be paid
           var minimumCollectableAmount = variousTotalFeesAmounts['totalFineAmount'];

           if(totalFeeAmount > totalCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be greater then payble amount " + totalCollectableAmount + ". Reducing the wallet amount.");
             usedWalletAmount = totalCollectableAmount - paidAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }else if(totalFeeAmount < minimumCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be less then fine payment amount " + minimumCollectableAmount + ". Increasing the wallet amount.");
             usedWalletAmount = minimumCollectableAmount - paidAmount;
             $("#student-used-wallet-amount").val(usedWalletAmount);
           }
           feePayment.fillCollectedAmounts(totalFeeAmount - minimumCollectableAmount);
         });

         $("#student-paid-amount").focus(function() {}).blur(function() {
           var paidAmount = feePayment.getPaidAmountValue();
           var usedWalletAmount = getUsedWalletAmount();
           var totalFeeAmount = usedWalletAmount + paidAmount;

           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
           // Atleast fine amount has to be paid
           var minimumCollectableAmount = variousTotalFeesAmounts['totalFineAmount'];

           if(totalFeeAmount > totalCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be greater then payble amount " + totalCollectableAmount + ". Reducing the paid amount.");
             paidAmount = totalCollectableAmount - usedWalletAmount;
             $("#student-paid-amount").val(paidAmount);
           }else if(totalFeeAmount < minimumCollectableAmount){
             showErrorDialogBox("Total paid fee amount "+totalFeeAmount+" cannot be less then fine payment amount " + minimumCollectableAmount + ". Increasing the paid amount.");
             paidAmount = minimumCollectableAmount - usedWalletAmount;
             $("#student-paid-amount").val(paidAmount);
           }
           feePayment.fillCollectedAmounts(totalFeeAmount - minimumCollectableAmount);
         });

         $("#student-total-instant-discount-amount").focus(function() {}).blur(function() {
           var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
           var totalBalanceAmount = variousTotalFeesAmounts['totalBalanceAmount'];
           var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'];

           var maxDiscountAmount = totalBalanceAmount - totalCollectedAmount;
           var discountedAmount = feePayment.getTotalInstantDiscountAmountValue();
           if(discountedAmount > maxDiscountAmount){
             showErrorDialogBox("Discounted amount "+discountedAmount+" cannot be greater then max discount amount " + maxDiscountAmount + ". Reducing the discounted amount.");
             discountedAmount = maxDiscountAmount;
           }
           $("#student-total-instant-discount-amount").val(discountedAmount);
            feePayment.fillInstantDiscountAmounts(discountedAmount);
         });

         $("#student-total-fine-collected-amount").focus(function() {
           lastTotalFineAmount = feePayment.getTotalFineAmountValue();
         }).blur(function() {
           var newTotalFeeAmount = feePayment.getTotalFineAmountValue();
           feePayment.fillFineAmounts(newTotalFeeAmount);
           feePayment.computeFineAmount();
           feePayment.updatePaidAmountDelta(lastTotalFineAmount, newTotalFeeAmount);
           feePayment.fillNetAmountDisplay();
         });
    },

    updatePaidAmountDelta: function (oldTotalAmount, newTotalAmount) {
      if(oldTotalAmount <= newTotalAmount){
        // Keeping wallet amount fixed and increasing paid amount only
        var excessAmount = newTotalAmount - oldTotalAmount;
        var existingPaidAmount = feePayment.getPaidAmountValue();
        $("#student-paid-amount").val(existingPaidAmount + excessAmount);
      }else{
        // First Keep wallet amount fixed and decrease paid amount only.
        // If paid amount is not sufficient reduce wallet amount
        var lessAmount = oldTotalAmount - newTotalAmount;
        var existingPaidAmount = feePayment.getPaidAmountValue();
        if(lessAmount <= existingPaidAmount){
           $("#student-paid-amount").val(existingPaidAmount - lessAmount);
        }else{
          if(!feePayment.isWalletUsed()){
             showErrorDialogBox("Invalid state. Changed amount was not included in paid amount");
             return;
          }
          $("#student-paid-amount").val(0);
          var existingWalletAmount = getUsedWalletAmount();
          var newWalletAmount = existingWalletAmount - (lessAmount - existingPaidAmount);
          if(newWalletAmount < 0){
             showErrorDialogBox("Invalid state. Changed amount was not included in paid amount of wallet");
             return;
          }
          $("#student-used-wallet-amount").val(newWalletAmount);
        }
      }
    },

    isWalletUsed : function () {
      return $('#use-wallet-amount').is(":checked");
    },

    getStudentWalletAmount : function () {
      return parseFloat($("#student-wallet-amount-value").text().trim());
    },

    getUsedWalletAmountValue : function () {
      var usedWalletAmountStr = $("#student-used-wallet-amount").val();
      if(usedWalletAmountStr != ""){
          return parseFloat(usedWalletAmountStr);
      }
      return 0;
    },

    insertFeesDetails : function(feeDetails) {
        var authorisedToUpdateFineAssignedAmount = $("#authorised-to-update-fine-assigned-amount").text();
        var readOnlyForFineAmount = "readonly";
        if(authorisedToUpdateFineAssignedAmount === "true") {
          readOnlyForFineAmount = "";
        }
        var feesDisplayDiv = "";
        var feeHeadRows = "";
        var feeId = ""
          var feeHeadPayments = feeDetails.feeHeadPaymentDetails;
          var isFineApplicable = feeDetails.feeConfigurationBasicInfo.fineApplicable;
          var totalFeeheadAssignedAmount = 0;
          var totalFeeheadAssignedDiscount = 0;
          var totalFeeheadPaidAmount = 0;
          var totalFeeheadBalanceAmount = 0;
          var totalFeeheadCollectedAmount = 0;
          var totalFeeheadInstantDiscountAmount = 0;
          var totalFeeheadDueAmount = 0;
          var totalDueFineAmount = feeDetails.totalDueFineAmount;
          var totalPaidFineAmount = feeDetails.totalPaidFineAmount;
          var totalRemainingFineAmount = 0
          if(isFineApplicable) totalRemainingFineAmount = totalDueFineAmount - totalPaidFineAmount;
          for(var i = 0; i < feeHeadPayments.length; i++){
            var feeHeadId = feeHeadPayments[i].feeHeadConfiguration.feeHeadId;
            var feeHeadName = feeHeadPayments[i].feeHeadConfiguration.feeHead;
            var assignedAmount = feeHeadPayments[i].assignedAmount;
            var paidAmount = feeHeadPayments[i].paidAmount;
            var instantDiscountAmount = feeHeadPayments[i].instantDiscountAmount;
            var assignedDiscountAmount = feeHeadPayments[i].assignedDiscountAmount;
            var balanceAmount = feeHeadPayments[i].balanceAmount;
            var totalDiscount = instantDiscountAmount + assignedDiscountAmount;
            feeHeadRows += feeHeadPaymentDetailRow(feeHeadId,feeHeadName,assignedAmount,paidAmount,balanceAmount,totalDiscount);
          }
          feeHeadRows += "<tr class=\"total-feehead-payment-row\"> <td> <strong> <span>TOTAL</span> </strong> </td> <td class=\"total-feeHead-assigned-amount\"><strong> <span>"+totalFeeheadAssignedAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-assigned-discount\"><strong> <span>"+totalFeeheadAssignedDiscount.toFixed(2)+"</span></td> <td class=\"total-feeHead-paid-amount\"><strong> <span>"+totalFeeheadPaidAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-payment-balance-amount\"><strong> <span>"+totalFeeheadBalanceAmount.toFixed(2)+"</span></td> <td class=\"total-feeHead-collected-amount\"><strong> <span>0</span></td> <td class=\"total-feeHead-instant-discount-amount\"><strong> <span>0</span></td> <td class=\"total-feeHead-payment-due-amount\"><strong> <span>"+totalFeeheadDueAmount.toFixed(2)+"</span></td> </tr>";

          feeId = feeDetails.feeConfigurationBasicInfo.feeId;
          var feeName = feeDetails.feeConfigurationBasicInfo.feeName + " ("+ getFeeTypeDisplayName(feeDetails.feeConfigurationBasicInfo.feeType) + ")";

          var feeFineRow = "<div class=\"table-responsive\"> <table class=\"table table-hover fine-table\"> <thead> <tr> <th scope=\"col\" class=\"w-25\"></th> <th scope=\"col\" >Computed Fine</th> <th scope=\"col\" >Collected Fine</th>  <th scope=\"col\" >Paid Fine</th> </tr> </thead> <tbody> <tr class=\"\"> <td> <strong>FINE</strong> </td> <td><input type=\"text\" class=\"form-control form-control-sm computed-fine-amount\" value=\"" + totalDueFineAmount+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm collected-fine-amount\" value=\"" + totalPaidFineAmount+ "\" readonly></td>  <td><input type=\"text\" class=\"form-control form-control-sm paid-fine-amount\" value=\"" + totalRemainingFineAmount + "\"" + readOnlyForFineAmount + "></td>  </tbody> </table> </div>";
          var cardId = "fee-card-" + feeId;
          feesDisplayDiv = generateFeesPaymentCard(cardId, feeId, feeName, feeHeadRows, feeFineRow);
          $("#selected-fees-end").before(feesDisplayDiv);
          feePayment.computeFeesPaymentAmounts();
          feePayment.computeFineAmount();
          feePayment.fillNetAmountDisplay();
          feePayment.updatePaidAmountDelta(0, totalRemainingFineAmount);
          feePayment.bindPaymentDueComputation($("#"+cardId));
    },

    getFloatValue : function (value) {
      if(value == null || value == undefined){
        return 0;
      }

      if(value == ""){
        return 0;
      }

      return parseFloat(value);
    },

    bindPaymentDueComputation : function(parentDiv) {
      $(parentDiv).find(".collected-amount").focus(function() {
        lastCollectedAmount = feePayment.getFloatValue($(this).val());
      }).blur(function() {
          var newValue = feePayment.getFloatValue($(this).val());
          var valid = newValue >= 0 && feePayment.computePaymentDueAmount($(this).parent().parent());
          if(!valid){
            $(this).val(lastCollectedAmount);
            return;
          }
        feePayment.computeFeesPaymentAmounts();
        feePayment.fillNetAmountDisplay();
        feePayment.updatePaidAmountDelta(lastCollectedAmount, feePayment.getFloatValue($(this).val()));
      });
      $(parentDiv).find(".instant-discount-amount").focus(function() {
        lastInstantDiscountAmount = $(this).val();
      }).blur(function() {
        var newValue = feePayment.getFloatValue($(this).val());
        var valid = newValue >= 0 && feePayment.computePaymentDueAmount($(this).parent().parent());
        if(!valid){
          $(this).val(lastInstantDiscountAmount);
          return;
        }
         feePayment.computeFeesPaymentAmounts();
         feePayment.fillNetAmountDisplay();
      });
      $(parentDiv).find(".paid-fine-amount").focus(function() {
        lastFeeLevelPaidFineAmount = feePayment.getFloatValue($(this).val());
      }).blur(function() {
        var newValue = feePayment.getFloatValue($(this).val());
        if(newValue < 0){
          $(this).val(lastFeeLevelPaidFineAmount);
          return;
        }
        feePayment.computeFineAmount();
        feePayment.fillNetAmountDisplay();
        feePayment.updatePaidAmountDelta(lastFeeLevelPaidFineAmount, feePayment.getFloatValue($(this).val()));

      });
    },

   computeFineAmount : function() {
        var totalComputedFine = 0;
        var totalCollectedFine = 0;
        var totalPaidFine = 0;
        $("div.collected-fees-details").each(function() {
           totalComputedFine +=parseFloat($(this).find(".fine-table").find(".computed-fine-amount").val());
           totalCollectedFine += parseFloat($(this).find(".fine-table").find(".collected-fine-amount").val());
           totalPaidFine += parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val());
        });
        $("#net-total-fine-computed-amount").find('span').first().text(totalComputedFine);
        $("#net-total-fine-collected-amount").find('span').first().text(totalCollectedFine);
        $("#net-total-fine-paid-amount").find('span').first().text(totalPaidFine);
    },

   getPaidAmountValue : function() {
      var inputFeesAmountStr = $("#student-paid-amount").val();
      var inputFeesAmount = 0;
      if(inputFeesAmountStr != ""){
          inputFeesAmount = parseFloat(inputFeesAmountStr);
      }
      return inputFeesAmount < 0 ? 0 : inputFeesAmount;
    },

    getTotalInstantDiscountAmountValue : function() {
       var inputDiscountAmountStr = $("#student-total-instant-discount-amount").val();
       var inputDiscountAmount = 0;
       if(inputDiscountAmountStr != ""){
           inputDiscountAmount = parseFloat(inputDiscountAmountStr);
       }
       return inputDiscountAmount < 0 ? 0 : inputDiscountAmount;
     },

     getTotalFineAmountValue : function() {
        var inputFineAmountStr = $("#student-total-fine-collected-amount").val();
        var inputFineAmount = 0;
        if(inputFineAmountStr != ""){
            inputFineAmount = parseFloat(inputFineAmountStr);
        }
        return inputFineAmount < 0 ? 0 : inputFineAmount ;
      },

    fillCollectedAmounts : function(collectedAmount) {
       feePayment.clearCollectedAmounts();
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                   if(collectedAmount <= 0){
                       return;
                   }
                   feePayment.computePaymentDueAmount($(this));
                   var dueAmountStr = $(this).find('input.payment-due-amount').val();
                   var dueAmount = 0;
                   if(dueAmountStr != ""){
                       dueAmount = parseFloat(dueAmountStr);
                   }

                   var fillAmount = 0;
                   if(collectedAmount >= dueAmount){
                       fillAmount = dueAmount;
                       collectedAmount -= dueAmount;
                   }else{
                       fillAmount = collectedAmount;
                       collectedAmount = 0;
                   }
                   $(this).find('input.collected-amount').first().val(fillAmount);
                   feePayment.computePaymentDueAmount($(this));
             });
       });
       if(collectedAmount > 0){
         showErrorDialogBox(collectedAmount + " Rs. amount is more than the assigned fees. Please select more fees or reduce the amount.")
         feePayment.clearCollectedAmounts();
         $("#student-paid-amount").val(0);
       }
       feePayment.computeFeesPaymentAmounts();
       feePayment.fillNetAmountDisplay();

     },

     fillInstantDiscountAmounts : function(discountedAmount) {
        feePayment.clearDiscountedAmounts();
        $("div.collected-fees-details").each(function() {
              $(this).find("tr.feehead-payment-row").each(function() {
                    if(discountedAmount <= 0){
                        return;
                    }
                    feePayment.computePaymentDueAmount($(this));
                    var dueAmountStr = $(this).find('input.payment-due-amount').val();
                    var dueAmount = 0;
                    if(dueAmountStr != ""){
                        dueAmount = parseFloat(dueAmountStr);
                    }

                    var fillAmount = 0;
                    if(discountedAmount >= dueAmount){
                        fillAmount = dueAmount;
                        discountedAmount -= dueAmount;
                    }else{
                        fillAmount = discountedAmount;
                        discountedAmount = 0;
                    }
                    $(this).find('input.instant-discount-amount').first().val(fillAmount);
                    feePayment.computePaymentDueAmount($(this));
              });
        });
        if(discountedAmount > 0){
          showErrorDialogBox(discountedAmount + " Rs. amount is more than the max applicable discount. Please select more fees or reduce the amount.")
        }
        feePayment.computeFeesPaymentAmounts();
        feePayment.fillNetAmountDisplay();
      },


      fillFineAmounts : function(totalFineAmount) {
          feePayment.clearFineAmounts();
          var currentTime = new Date().getTime() / 1000;
          //Iterating over only due fees which have computed amount available
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                 return;
                }

                var computedFineStr = $(this).find(".fine-table").find(".computed-fine-amount").val();
                var computedFine = 0;
                if(computedFineStr != ""){
                   computedFine = parseFloat(computedFineStr);
                }

                var collectedFineStr = $(this).find(".fine-table").find(".collected-fine-amount").val();
                var collectedFine = 0;
                if(collectedFineStr != ""){
                   collectedFine = parseFloat(collectedFineStr);
                }

                // Skip all due fees in first iteration which does not have any computed amount
                if(computedFine <= 0){
                 return;
                }
                var paybleFine = computedFine - collectedFine;
                if(totalFineAmount < paybleFine){
                    $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                    totalFineAmount = 0;
                }else{
                    $(this).find(".fine-table").find(".paid-fine-amount").val(paybleFine);
                    totalFineAmount = totalFineAmount - paybleFine;
                }
          });

          if(totalFineAmount <= 0){
            return;
          }

          //Iterating over only due fees which do not have computed amount available
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                   return;
                }

                var computedFineStr = $(this).find(".fine-table").find(".computed-fine-amount").val();
                var computedFine = 0;
                if(computedFineStr != ""){
                   computedFine = parseFloat(computedFineStr);
                }

                if(computedFine <= 0){
                  $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                  totalFineAmount = 0;
                  return;
                }
          });

          if(totalFineAmount <= 0){
            return;
          }

          //Iterating over non due fees and assigning all fine amount on first available fees
          $("div.collected-fees-details").each(function() {
                var feeId = $(this).find(".collected-fee-id").text().trim();
                var feeDetails = feePayment.dataCache.dueFeeDetailsMap[feeId];
                // Fee is not due
                if(feeDetails.feeConfigurationBasicInfo.dueDate == null || feeDetails.feeConfigurationBasicInfo.dueDate <= 0
                 || feeDetails.feeConfigurationBasicInfo.dueDate > currentTime){
                   $(this).find(".fine-table").find(".paid-fine-amount").val(totalFineAmount);
                   totalFineAmount = 0;
                   return;
                }
          });
       },

     clearCollectedAmounts : function() {
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                 $(this).find('input.collected-amount').val(0);
             });
       });
     },

     clearDiscountedAmounts : function() {
       $("div.collected-fees-details").each(function() {
             $(this).find("tr.feehead-payment-row").each(function() {
                 $(this).find('input.instant-discount-amount').val(0);
             });
       });
     },

     clearFineAmounts : function() {
       $("div.collected-fees-details").each(function() {
          $(this).find(".fine-table").find(".paid-fine-amount").val(0);
       });
     },

    computePaymentDueAmount : function(current) {
       var paidAmountString = $(current).find(".collected-amount").first().val();
       var paidAmount = 0;
       if (paidAmountString) {
           paidAmount = parseFloat(paidAmountString);
       }

       var instantDiscountString = $(current).find(".instant-discount-amount").first().val();
       var instantDiscount = 0;
       if (instantDiscountString) {
           instantDiscount = parseFloat(instantDiscountString);
       }

       var balanceAmountString = $(current).find(".payment-balance-amount").first().val();
       var balanceAmount = 0;
       if (balanceAmountString) {
           balanceAmount = parseFloat(balanceAmountString);
       }
       var collectedAmount = parseFloat(paidAmount.toFixed(2)) + parseFloat(instantDiscount.toFixed(2));
       var dueAmount = parseFloat(balanceAmount.toFixed(2)) - collectedAmount;
       if(dueAmount < 0){
         showErrorDialogBox("Collected amount = "+collectedAmount+ " is more than the Payable amount = "+balanceAmount +". Please review the collected and instant discount amounts.");
         return false;
         // dueAmount = 0;
         // $(current).find(".payment-due-amount").first().val(balanceAmount.toFixed(0));
         // $(current).find(".collected-amount").first().val(0);
         // $(current).find(".instant-discount-amount").first().val(0);

       }else{
         $(current).find(".payment-due-amount").first().val(dueAmount.toFixed(0));
         return true;
       }
     },


    computeFeesPaymentAmounts : function() {
       var totalFeesAssignedAmount = 0;
       var totalFeesAssignedDiscount = 0;
       var totalFeesPaidAmount = 0;
       var totalFeesBalanceAmount = 0;
       var totalFeesCollectedAmount = 0;
       var totalFeesInstantDiscountAmount = 0;
       var totalFeesDueAmount = 0;
       $("div.collected-fees-details").each(function() {
             var totalFeeheadAssignedAmount = 0;
             var totalFeeheadAssignedDiscount = 0;
             var totalFeeheadPaidAmount = 0;
             var totalFeeheadBalanceAmount = 0;
             var totalFeeheadCollectedAmount = 0;
             var totalFeeheadInstantDiscountAmount = 0;
             var totalFeeheadDueAmount = 0;
             $(this).find("tr.feehead-payment-row").each(function() {
                 feePayment.computePaymentDueAmount($(this));
                 totalFeeheadAssignedAmount += parseFloat($(this).find('input.assigned-amount').val());
                 totalFeeheadAssignedDiscount += parseFloat($(this).find('input.assigned-discount').val());
                 totalFeeheadPaidAmount += parseFloat($(this).find('input.paid-amount').val());
                 totalFeeheadBalanceAmount += parseFloat($(this).find('input.payment-balance-amount').val());
                 totalFeeheadCollectedAmount += parseFloat($(this).find('input.collected-amount').val());
                 totalFeeheadInstantDiscountAmount += parseFloat($(this).find('input.instant-discount-amount').val());
                 totalFeeheadDueAmount += parseFloat($(this).find('input.payment-due-amount').val());
             });
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-assigned-amount').find('span').first().text(totalFeeheadAssignedAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-assigned-discount').find('span').first().text(totalFeeheadAssignedDiscount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-paid-amount').find('span').first().text(totalFeeheadPaidAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-payment-balance-amount').find('span').first().text(totalFeeheadBalanceAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-collected-amount').find('span').first().text(totalFeeheadCollectedAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-instant-discount-amount').find('span').first().text(totalFeeheadInstantDiscountAmount);
             $(this).find('tr.total-feehead-payment-row > td.total-feeHead-payment-due-amount').find('span').first().text(totalFeeheadDueAmount);

             totalFeesAssignedAmount += totalFeeheadAssignedAmount;
             totalFeesAssignedDiscount += totalFeeheadAssignedDiscount;
             totalFeesPaidAmount += totalFeeheadPaidAmount;
             totalFeesBalanceAmount += totalFeeheadBalanceAmount;
             totalFeesCollectedAmount += totalFeeheadCollectedAmount;
             totalFeesInstantDiscountAmount += totalFeeheadInstantDiscountAmount;
             totalFeesDueAmount += totalFeeheadDueAmount;
       });
       $("#net-total-fees-assigned-amount").find('span').first().text(totalFeesAssignedAmount.toFixed(2));
       $("#net-total-fees-assigned-discount").find('span').first().text(totalFeesAssignedDiscount.toFixed(2));
       $("#net-total-fees-paid-amount").find('span').first().text(totalFeesPaidAmount.toFixed(2));
       $("#net-total-fees-payment-balance-amount").find('span').first().text(totalFeesBalanceAmount.toFixed(2));
       $("#net-total-fees-collected-amount").find('span').first().text(totalFeesCollectedAmount.toFixed(2));
       $("#net-total-fees-instant-discount-amount").find('span').first().text(totalFeesInstantDiscountAmount.toFixed(2));
       $("#net-total-fees-payment-due-amount").find('span').first().text(totalFeesDueAmount.toFixed(2));
     },

    fillNetAmountDisplay : function() {
       var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
       var totalBalanceAmount = variousTotalFeesAmounts['totalBalanceAmount'];
       var totalInstantDiscountAmount =  variousTotalFeesAmounts['totalInstantDiscountAmount'];
       var totalFineAmount =  variousTotalFeesAmounts['totalFineAmount'];
       var totalCollectableAmount = totalBalanceAmount +  totalFineAmount - totalInstantDiscountAmount;

       $("#student-total-balance-amount-display").val(totalBalanceAmount.toFixed(2));
       $("#student-total-fine-collected-amount").val(totalFineAmount.toFixed(2));
       $("#student-total-instant-discount-amount").val(totalInstantDiscountAmount.toFixed(2));
       $("#student-net-payble-amount-display").val(totalCollectableAmount.toFixed(2));

       // var usedWalletAmount = getUsedWalletAmount();
       // var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'] + totalFineAmount;
       // if(totalCollectedAmount > usedWalletAmount){
       //   // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
       //   $("#student-paid-amount").val(totalCollectedAmount - usedWalletAmount);
       // }else if(totalCollectedAmount <= usedWalletAmount){
       //   usedWalletAmount = totalCollectedAmount;
       //   $("#student-used-wallet-amount").val(usedWalletAmount);
       //   // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
       //   $("#student-paid-amount").val(0);
       // }else{
       //   showErrorDialogBox("Total collected amount " + totalCollectedAmount + " is more then payble amount " + totalCollectableAmount);
       // }
     },

    computeVariousTotalFeesAmounts : function() {
       var totalBalanceAmount = 0;
       var totalInstantDiscountAmount = 0;
       var totalCollectedAmount = 0;
       var totalFineAmount = 0;
       $("div.collected-fees-details").each(function() {
             var fineAmountStr = $(this).find(".fine-table").find(".paid-fine-amount").val();
             if(fineAmountStr != ""){
                 totalFineAmount += parseFloat(fineAmountStr);
             }
             $(this).find("tr.feehead-payment-row").each(function() {
                 var balanceAmountStr = $(this).find('input.payment-balance-amount').val();
                 if(balanceAmountStr != ""){
                     totalBalanceAmount += parseFloat(balanceAmountStr);
                 }
                 var instantDiscountAmountStr = $(this).find('input.instant-discount-amount').val();
                 if(instantDiscountAmountStr != ""){
                     totalInstantDiscountAmount += parseFloat(instantDiscountAmountStr);
                 }
                 var collectedAmountStr = $(this).find('input.collected-amount').val();
                 if(collectedAmountStr != ""){
                     totalCollectedAmount += parseFloat(collectedAmountStr);
                 }
             });
       });
       return {'totalBalanceAmount':totalBalanceAmount ,'totalInstantDiscountAmount': totalInstantDiscountAmount,'totalCollectedAmount': totalCollectedAmount, 'totalFineAmount' : totalFineAmount, 'totalCollectableAmount' : totalBalanceAmount + totalFineAmount - totalInstantDiscountAmount};
     },


     collectSiblingFeesPage : function (studentId, sessionId) {

       ajaxClient.get( "/fees/sibling-due-fee-details/" + sessionId + "/" + studentId, function(data) {

         $("#sibling-collect-fee-tab").html(data);

         $(".payment-main-screen-info").attr("style", "display:none");
         $(".fee-payment-tab").attr("style", "display:none");
         $("#sibling-collect-fee-tab").attr("style", "display:block");

         feePayment.populateSiblingCollectFeesScreen(studentId, sessionId);
         feePayment.registerSummaryAmountInput();
         feePayment.registerIndividualAmountInput();

       });
     },

     populateSiblingCollectFeesScreen : function (studentId, sessionId) {
       var sessionData = feePayment.dataCache['sessionData'][sessionId];
       $("#collect-sibling-fee-session-display").html(sessionData.displayName);
       $("#collect-sibling-fee-session-id").html(sessionId);
       initDate(pastFeePaymentTransactionDays);

       var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

       var totalBalanceAmount = 0;
       var totalFineAmount = 0;
       var totalInstantDiscountAmount = 0;
       for(var i = 0; i < siblingDueFeeDetails.length; i++) {
         var siblingDueFeeDetail = siblingDueFeeDetails[i];
         if(siblingDueFeeDetail.feePaymentRestricted){
         continue;
         }
         totalBalanceAmount += siblingDueFeeDetail.dueAmount;
         totalFineAmount += siblingDueFeeDetail.dueFineAmount;
       }

       var totalCollectableAmount = totalBalanceAmount +  totalFineAmount - totalInstantDiscountAmount;

       $("#sibling-total-balance-amount-display").val(totalBalanceAmount);
       $("#sibling-total-fine-collected-amount").val(totalFineAmount);
       $("#sibling-total-instant-discount-amount").val(totalInstantDiscountAmount);
       $("#sibling-net-payble-amount-display").val(totalCollectableAmount);
       $("#sibling-student-paid-amount").val(totalFineAmount);

       $("tr.sibling-payment-details-tr").each(function() {
         var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
         var fineAmount = siblingDueFeeDetail.dueFineAmount;
         $(this).find(".sibling-student-fine-amount").val(fineAmount);

         // We must pay fine amount
         $(this).find(".sibling-student-paid-amount").val(fineAmount);

         var dueAmount = siblingDueFeeDetail.dueAmount;
         var instantDiscountAmount = $("#sibling-student-instant-discount-amount").val();
         if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
           instantDiscountAmount = 0;
         }
         var netPayableAmount = parseFloat(dueAmount) + parseFloat(fineAmount) - parseFloat(instantDiscountAmount);

         $(this).find(".sibling-student-net-payable-amount").val(roundOffNumber(netPayableAmount, 2));

       });

     },

     registerSummaryAmountInput : function () {

       $("#sibling-student-paid-amount").focus(function() {}).blur(function() {

         var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

         var totalPaidAmount = $(this).val();
         if(totalPaidAmount === "" || totalPaidAmount === undefined || totalPaidAmount === null) {
           totalPaidAmount = 0;
         }
         totalPaidAmount = parseFloat(totalPaidAmount);

         var totalCollectableAmount = parseFloat($("#sibling-net-payble-amount-display").val());
         if(totalCollectableAmount === "" || totalCollectableAmount === undefined || totalCollectableAmount === null) {
           totalCollectableAmount = 0;
         }
         totalCollectableAmount = parseFloat(totalCollectableAmount);
         var totalFineAmount = $("#sibling-total-fine-collected-amount").val()

         if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
           totalFineAmount = 0;
         }
         totalFineAmount = parseFloat(totalFineAmount);
         if(totalPaidAmount > totalCollectableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than new payable amount!")
           $(this).val(0);
           return;
         }

         if(totalPaidAmount < totalFineAmount) {
           showErrorDialogBox("Paid Amount cannot be lesser than total fine amount!")
           return;
         }

         feePayment.updateIndividualPaidAmount(true);

      });


      $("#sibling-total-fine-collected-amount").focus(function() {}).blur(function() {


          var valid = feePayment.setNetPayableAmount();
          if(!valid) {
            return;
          }

          var totalFineAmount = $("#sibling-total-fine-collected-amount").val();
          if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
            totalFineAmount = 0;
          }
          totalFineAmount = parseFloat(totalFineAmount);

          var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());
          var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
          var individualFineAmount = 0;
          var reminder = 0;
          if(totalFineAmount % noOfSiblings == 0) {
            individualFineAmount = totalFineAmount / noOfSiblings;
          } else {
            reminder = totalFineAmount % noOfSiblings;
            individualFineAmount = (totalFineAmount - reminder) / noOfSiblings;
          }
          var count = 0;
          $("tr.sibling-payment-details-tr").each(function() {
            var siblingDetailJson = $(this).find(".sibling-due-fee-detail").text();
            var siblingDetail = JSON.parse(siblingDetailJson);
            if (siblingDetail.feePaymentRestricted) {
                    return;
            }
            $(this).find(".sibling-student-fine-amount").val(individualFineAmount + reminder);
            reminder = 0;
            var valid = feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-fine-amount"));
            if(!valid) {
              return;
            }

          });

          var paidAmount = $("#sibling-student-paid-amount").val();
          if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
            paidAmount = 0;
          }
          paidAmount = parseFloat(paidAmount);

          if(paidAmount < totalFineAmount) {
            $("#sibling-student-paid-amount").val(totalFineAmount);
          }

          feePayment.updateIndividualPaidAmount(true);

        });


     $("#sibling-total-instant-discount-amount").focus(function() {}).blur(function() {

         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }

         var totalInstantDiscountAmount = $("#sibling-total-instant-discount-amount").val();
         if(totalInstantDiscountAmount === "" || totalInstantDiscountAmount === undefined || totalInstantDiscountAmount === null) {
           totalInstantDiscountAmount = 0;
         }
         totalInstantDiscountAmount = parseFloat(totalInstantDiscountAmount);

         var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());
         var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
         var individualInstantDiscountAmount = 0;
         var reminder = 0;
         if(totalInstantDiscountAmount % noOfSiblings == 0) {
           individualInstantDiscountAmount = totalInstantDiscountAmount / noOfSiblings;
         } else {
           reminder = totalInstantDiscountAmount % noOfSiblings;
           individualInstantDiscountAmount = (totalInstantDiscountAmount - reminder) / noOfSiblings;
         }
         var count = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDetailJson = $(this).find(".sibling-due-fee-detail").text();
           var siblingDetail = JSON.parse(siblingDetailJson);
           if (siblingDetail.feePaymentRestricted) {
                return; // Skip this iteration
           }
           $(this).find(".sibling-student-instant-discount-amount").val(individualInstantDiscountAmount + reminder);
           reminder = 0;
           var valid = feePayment.setIndividualNetPayableAmount($(this).find(".sibling-student-instant-discount-amount"));
           if(!valid) {
             return;
           }
         });
         feePayment.updateIndividualPaidAmount(true);
       });
     },

    countSiblingsWithFeePaymentAllowed :  function (siblingDueFeeDetails) {
         return siblingDueFeeDetails.filter(sibling => !sibling.feePaymentRestricted).length;
     },

     registerIndividualAmountInput : function () {
       $('.sibling-student-paid-amount').focus(function() {}).blur(function() {
         var totalPaidAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var paidAmount = $(this).find(".sibling-student-paid-amount").val();
           if(paidAmount != undefined && paidAmount != null && paidAmount != "") {
             totalPaidAmount += parseFloat(paidAmount);
           }
         });
         var row = $(this).closest("tr");
         var fineAmount = parseFloat(row.find(".sibling-student-fine-amount").val()) || 0;
         var discountAmount = parseFloat(row.find(".sibling-student-instant-discount-amount").val()) || 0;
         var paidAmount = parseFloat($(this).val()) || 0;
         var netPayableAmount = parseFloat(row.find(".sibling-student-net-payable-amount").val()) || 0;

         var totalCollectableAmount = parseFloat($("#sibling-net-payble-amount-display").val());

         if(totalPaidAmount > totalCollectableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than new payable amount!");
           return;
         }

         if(paidAmount > netPayableAmount) {
           showErrorDialogBox("Paid Amount cannot be greater than net payable amount!");
           return;
         }

         if(fineAmount > paidAmount && netPayableAmount > fineAmount) {
           showErrorDialogBox("Paid Amount cannot be lesser than fine amount!");
           return;
         }
         $("#sibling-student-paid-amount").val(totalPaidAmount);
       });



       $('.sibling-student-fine-amount').focus(function() {}).blur(function() {
         var totalFineAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var fineAmount = $(this).find(".sibling-student-fine-amount").val();
           if(fineAmount != undefined && fineAmount != null && fineAmount != "") {
             totalFineAmount += parseFloat(fineAmount);
           }
         });
         $("#sibling-total-fine-collected-amount").val(totalFineAmount);
         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }
         valid = feePayment.setIndividualNetPayableAmount(this);
         if(!valid) {
           return;
         }

         var paidAmount = $("#sibling-student-paid-amount").val();
         if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
           paidAmount = 0;
         }
         paidAmount = parseFloat(paidAmount);
         var row = $(this).closest("tr");
         var fineAmount = parseFloat($(this).val()) || 0;
         var individualPaidAmount = parseFloat(row.find(".sibling-student-paid-amount").val()) || 0;

         if(individualPaidAmount < fineAmount) {
           row.find(".sibling-student-paid-amount").val(fineAmount);
         }

         feePayment.updateIndividualPaidAmount(false);
         feePayment.updateTotalPaidAmount();

       });


       $('.sibling-student-instant-discount-amount').focus(function() {}).blur(function() {
         var totalInstantDiscountAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var instantDiscountAmount = $(this).find(".sibling-student-instant-discount-amount").val();
           if(instantDiscountAmount != undefined && instantDiscountAmount != null && instantDiscountAmount != "") {
             totalInstantDiscountAmount += parseFloat(instantDiscountAmount);
           }
         });

         $("#sibling-total-instant-discount-amount").val(totalInstantDiscountAmount);
         var valid = feePayment.setNetPayableAmount();
         if(!valid) {
           return;
         }
         valid = feePayment.setIndividualNetPayableAmount(this);
         if(!valid) {
           return;
         }

         feePayment.updateIndividualPaidAmount(false);

       });

     },

     updateTotalPaidAmount : function() {
         var totalPaidAmount = 0;
         $("tr.sibling-payment-details-tr").each(function() {
           var paidAmount = $(this).find(".sibling-student-paid-amount").val();
           if(paidAmount != undefined && paidAmount != null && paidAmount != "") {
             totalPaidAmount += parseFloat(paidAmount);
           }
         });
         $("#sibling-student-paid-amount").val(totalPaidAmount);
     },

     setNetPayableAmount : function () {
       var totalFineAmount = $("#sibling-total-fine-collected-amount").val();
       if(totalFineAmount === "" || totalFineAmount === undefined || totalFineAmount === null) {
         totalFineAmount = 0;
       }
       totalFineAmount = parseFloat(totalFineAmount);


       var totalBalanceAmount = $("#sibling-total-balance-amount-display").val();
       if(totalBalanceAmount === "" || totalBalanceAmount === undefined || totalBalanceAmount === null) {
         totalBalanceAmount = 0;
       }
       totalBalanceAmount = parseFloat(totalBalanceAmount);
       var totalInstantDiscountAmount = $("#sibling-total-instant-discount-amount").val();
       if(totalInstantDiscountAmount === "" || totalInstantDiscountAmount === undefined || totalInstantDiscountAmount === null) {
         totalInstantDiscountAmount = 0;
       }
       totalInstantDiscountAmount = parseFloat(totalInstantDiscountAmount);
       var totalCollectableAmount = parseFloat(totalBalanceAmount) +  parseFloat(totalFineAmount) - parseFloat(totalInstantDiscountAmount);

       if(totalCollectableAmount < 0) {
         showErrorDialogBox("Net Payable amount cannot be negative!");
         return false;
       }
       $("#sibling-net-payble-amount-display").val(totalCollectableAmount);
       return true;
     },

     setIndividualNetPayableAmount : function (ref) {
       var instantDiscountAmount = $(ref).parent().parent().find(".sibling-student-instant-discount-amount").val();
       if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
         instantDiscountAmount = 0;
       }
       instantDiscountAmount = parseFloat(instantDiscountAmount);
       var fineAmount = $(ref).parent().parent().find(".sibling-student-fine-amount").val();
       if(fineAmount === "" || fineAmount === undefined || fineAmount === null) {
         fineAmount = 0;
       }
       fineAmount = parseFloat(fineAmount);
       var siblingDueFeeDetail = JSON.parse($(ref).parent().parent().find(".sibling-due-fee-detail").text());
       var dueAmount = siblingDueFeeDetail.dueAmount;
       var netPayableAmount = parseFloat(dueAmount) + parseFloat(fineAmount) - parseFloat(instantDiscountAmount);
       if(netPayableAmount < 0) {
         showErrorDialogBox("Net Payable amount cannot be negative!");
         return false;
       }
       $(ref).parent().parent().find(".sibling-student-net-payable-amount").val(roundOffNumber(netPayableAmount, 2));
       return true;
     },

     updateIndividualPaidAmount : function (updateSiblingFeeDetails) {
       var siblingDueFeeDetails = JSON.parse($("#sibling-due-fee-details").text());

       var totalPaidAmount = $("#sibling-student-paid-amount").val();
       if(totalPaidAmount === "" || totalPaidAmount === undefined || totalPaidAmount === null) {
         totalPaidAmount = 0;
       }
       totalPaidAmount = parseFloat(totalPaidAmount);

       var studentIdNetPayableAmountMap = {};
       if(updateSiblingFeeDetails){
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
           if(!siblingDueFeeDetail.feePaymentRestricted){
               var studentId = siblingDueFeeDetail.studentId;
               var netPayableAmount = $(this).find(".sibling-student-net-payable-amount").val();
               studentIdNetPayableAmountMap[studentId] = parseFloat(netPayableAmount);
           }
         });
       }

//       var noOfSiblings = siblingDueFeeDetails.length;
       var noOfSiblings = feePayment.countSiblingsWithFeePaymentAllowed(siblingDueFeeDetails);
       var remainingPaidAmount = totalPaidAmount;
       var count = 0;
       var studentIdNetPayableAmountMapSortByValue = sortMapByValue(studentIdNetPayableAmountMap);
       var studentIdPaidAmountMap = {};
       var studentIdRemianingAmountMap = {};
       var reminder = 0;
       var countOfSiblingWithRemainingAmount = 0;
       $.each(studentIdNetPayableAmountMapSortByValue, function(studentId, netPayableAmount) {

            reminder += (remainingPaidAmount % (noOfSiblings - count));
            var paidAmount = ((remainingPaidAmount - (remainingPaidAmount % (noOfSiblings - count))) / (noOfSiblings - count));
            if(paidAmount > netPayableAmount) {
              paidAmount = netPayableAmount;
            }
            studentIdPaidAmountMap[studentId] = paidAmount;
            studentIdRemianingAmountMap[studentId] = netPayableAmount - paidAmount;

            if(studentIdRemianingAmountMap[studentId] > 0) {
              countOfSiblingWithRemainingAmount += 1;
            }
            remainingPaidAmount -= paidAmount;
            count += 1;
        });

        var individualReminder = 0;
        var finalReminder = 0;
        if(remainingPaidAmount > 0) {
          if(remainingPaidAmount % countOfSiblingWithRemainingAmount == 0) {
            individualReminder = remainingPaidAmount / countOfSiblingWithRemainingAmount;
          } else {
            finalReminder = remainingPaidAmount % countOfSiblingWithRemainingAmount;
            individualReminder = (remainingPaidAmount - finalReminder) / countOfSiblingWithRemainingAmount;
          }

          $.each(studentIdPaidAmountMap, function(studentId, paidAmount) {
            studentIdPaidAmountMap[studentId] += individualReminder + finalReminder;
            finalReminder = 0;
          });
        }

       if(updateSiblingFeeDetails){
         $("tr.sibling-payment-details-tr").each(function() {
           var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
           var studentId = siblingDueFeeDetail.studentId;
           var paidAmount = studentIdPaidAmountMap[studentId]
           $(this).find(".sibling-student-paid-amount").val(paidAmount);
         });
        }

     },

     collectSiblingFees : function () {

       $("#sibling-fee-payment-confirm-modal").modal("toggle");

       var academicSessionId = JSON.parse($("#collect-sibling-fee-session-id").html());
       var transactionMode = $('#sibling-payment-mode-input').find(':selected').val().trim();
       var transactionReference = $('#sibling-payment-reference').val().trim();
       var transactionDate = getDate($('#sibling-payment-date').val());
       var transactionRemarks = $('#sibling-payment-remark').val();
       var invalid = validateMandatoryFields($("#sibling-collect-fee-tab"));
       if(invalid){
         showErrorDialogBox("Please fill required fields before making payment.");
         return;
       }
       var invalidAmount = validateSiblingPaymentAmounts();
       if(invalidAmount) {
         return;
       }
       if(transactionDate == null){
         showErrorDialogBox("Please enter the date of payment");
         return null;
       }
       var currentDate = new Date();
       if(transactionDate.getTime() > currentDate.getTime()){
         showErrorDialogBox("Invalid payment date. Future transactions not allowd");
         return null;
       }
       var feePaymentPayloadLiteList = [];
       $("tr.sibling-payment-details-tr").each(function() {
             var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
             if(siblingDueFeeDetail.feePaymentRestricted){
               return;
             }
             var studentId = siblingDueFeeDetail.studentId;

             var fineCollectionAmount = $(this).find(".sibling-student-fine-amount").val();
             if(fineCollectionAmount === "" || fineCollectionAmount === undefined || fineCollectionAmount === null) {
               fineCollectionAmount = 0;
             }
             fineCollectionAmount = parseFloat(fineCollectionAmount);

             var instantDiscountAmount = $(this).find(".sibling-student-instant-discount-amount").val();
             if(instantDiscountAmount === "" || instantDiscountAmount === undefined || instantDiscountAmount === null) {
               instantDiscountAmount = 0;
             }
             instantDiscountAmount = parseFloat(instantDiscountAmount);

             var walletAmount = 0;

             var paidAmount = $(this).find(".sibling-student-paid-amount").val();
             if(paidAmount === "" || paidAmount === undefined || paidAmount === null) {
               paidAmount = 0;
             }
             paidAmount = parseFloat(paidAmount);

             if(!(paidAmount === 0 && instantDiscountAmount === 0 && fineCollectionAmount === 0)) {
               feePaymentPayloadLiteList.push({'studentId' : studentId, 'academicSessionId' : academicSessionId, 'transactionMode' : transactionMode, 'transactionDate' : transactionDate.getTime()/1000, 'transactionReference' : transactionReference, 'fineCollectionAmount' : fineCollectionAmount, 'instantDiscountAmount' : instantDiscountAmount, 'walletAmount' : walletAmount, 'paidAmount' : paidAmount, 'transactionRemarks' : transactionRemarks});
             }
       });
       if(feePaymentPayloadLiteList.length <= 0) {
         showErrorDialogBox("No payment Details added to make payment!");
         return;
       }

       ajaxClient.post("/fees/collect-sibling-fees/"+academicSessionId, {'feePaymentPayloadLiteList' : JSON.stringify(feePaymentPayloadLiteList)}, function(data){
         returnToPaymentMainScreenDisplay();
         $("#fee-payment-status-modal-container").html(data);
         $("#collect-fee-payment-status-modal").modal({backdrop: 'static', keyboard: false});
         var studentId = $("#collect-fee-student-id").text().trim();
         feePayment.collectSiblingFeesPage(studentId, academicSessionId);
           $("#marks-feed-status-modal-container").html(data);
           $("#exam-config\\.status-modal").modal({backdrop: 'static', keyboard: false});
       });

     },
    
     prepareUpdateWalletTransaction : function (transactionId, studentId, amount) {
      // Store the current values
      feePayment.currentTransactionData = {
        transactionId: transactionId,
        studentId: studentId,
        amount: amount
      };
      
      // Close the wallet transactions modal properly
      $("#student-wallet-transactions-modal").modal('hide');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Set values in the modal fields
      $("#update-wallet-transaction-id").val(transactionId);
      $("#update-wallet-student-id").val(studentId);
      $("#update-wallet-amount").val(amount);
      
      // Show the update modal
        $("#update-wallet-transaction-modal").modal('toggle');
        
        // Bind event handlers for this specific instance
        $(".cancel-update-btn").off('click').on('click', function() {
          feePayment.cancelUpdateWalletTransaction();
        });
        
        $("#confirm-update-btn").off('click').on('click', function() {
          feePayment.updateWalletTransactionAmount();
        });
    },
    
    cancelUpdateWalletTransaction: function() {
      // Close the update modal properly
      $("#update-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Reopen the wallet transactions modal
        feePayment.reopenWalletTransactions();
    },
    
    updateWalletTransactionAmount : function () {
      var transactionId = $("#update-wallet-transaction-id").val();
      var studentId = $("#update-wallet-student-id").val();
      var amount = $("#update-wallet-amount").val();
      
      if (amount === "" || amount === undefined || amount === null) {
        showErrorDialogBox("Invalid Amount!");
        return;
      }
      
      var updatePayload = {
        "transactionId": transactionId,
        "studentId": studentId,
        "amount": parseFloat(amount)
      };
      
      // Close the update modal properly
      $("#update-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Show loading indicator if needed
      // ...
      
      ajaxClient.post("/fees/update-wallet-transaction-amount", {"updateWalletTransactionPayload": JSON.stringify(updatePayload)}, function(data) {
        // Add the status modal to the container
        $("#update-wallet-transaction-status-modal-container").html(data);
        
        
          $("#update-wallet-transaction-status-modal").modal('toggle');
          
          // Bind event handler for the close button
          $("#close-status-btn").off('click').on('click', function() {
            feePayment.closeStatusModal();
          });
        
        // Reload student payment details to reflect updated wallet balance
        loadStudentPaymentDetails(studentId);
      });
    },
    
    closeStatusModal: function() {
      // Close the status modal properly
      $("#update-wallet-transaction-status-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // // Reopen the wallet transactions modal
      //   feePayment.reopenWalletTransactions();
    },
    
    reopenWalletTransactions : function () {
      var studentId = feePayment.currentTransactionData ? feePayment.currentTransactionData.studentId : $("#wallet-recharge-student-id").text().trim();
      
      // Load wallet transactions again
      ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
        $("#student-wallet-transactions-modal-container").html(data);
        
        // Show the wallet transactions modal
          $("#student-wallet-transactions-modal").modal('toggle');
      });
    },

    prepareDeleteWalletTransaction : function (transactionId, studentId) {
      // Store the current values
      feePayment.currentDeleteTransactionData = {
        transactionId: transactionId,
        studentId: studentId,
      };
      
      // Close the wallet transactions modal properly
      $("#student-wallet-transactions-modal").modal('hide');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // // Set values in the modal fields
      $("#delete-wallet-transaction-id").val(transactionId);
      $("#delete-wallet-student-id").val(studentId);
      // $("#update-wallet-amount").val(amount);
      
      // Show the update modal
        $("#delete-wallet-transaction-modal").modal('toggle');
        
        // Bind event handlers for this specific instance
        $(".cancel-update-btn").off('click').on('click', function() {
          feePayment.cancelUpdateWalletTransaction();
        });
        
        $("#confirm-delete-btn").off('click').on('click', function() {
          feePayment.deleteWalletTransactionAmount();
        });
    },
    
    // cancelUpdateWalletTransaction: function() {
    //   // Close the update modal properly
    //   $("#delete-wallet-transaction-modal").modal('toggle');
    //   $('body').removeClass('modal-open');
    //   $('.modal-backdrop').remove();
      
    //   // Reopen the wallet transactions modal
    //     feePayment.reopenWalletTransactions();
    // },
    
    deleteWalletTransactionAmount : function () {
      var transactionId = $("#delete-wallet-transaction-id").val();
      var studentId = $("#delete-wallet-student-id").val();
      
      var deletePayload = {
        "transactionId": transactionId,
        "studentId": studentId,
      };
      
      // Close the update modal properly
      $("#delete-wallet-transaction-modal").modal('toggle');
      $('body').removeClass('modal-open');
      $('.modal-backdrop').remove();
      
      // Show loading indicator if needed
      // ...
      
      ajaxClient.post("/fees/delete-wallet-transaction", {"deleteWalletTransactionPayload": JSON.stringify(deletePayload)}, function(data) {
        // Add the status modal to the container
        $("#update-wallet-transaction-status-modal-container").html(data);
        
        
          $("#update-wallet-transaction-status-modal").modal('toggle');
          
          // Bind event handler for the close button
          $("#close-status-btn").off('click').on('click', function() {
            feePayment.closeStatusModal();
          });
        
        // Reload student payment details to reflect updated wallet balance
        loadStudentPaymentDetails(studentId);
      });
    },
    
    // closeStatusModal: function() {
    //   // Close the status modal properly
    //   $("#update-wallet-transaction-status-modal").modal('toggle');
    //   $('body').removeClass('modal-open');
    //   $('.modal-backdrop').remove();
      
    //   // // Reopen the wallet transactions modal
    //   //   feePayment.reopenWalletTransactions();
    // },
    
    // reopenWalletTransactions : function () {
    //   var studentId = feePayment.currentDeleteTransactionData ? feePayment.currentDeleteTransactionData.studentId : $("#wallet-recharge-student-id").text().trim();
      
    //   // Load wallet transactions again
    //   ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
    //     $("#student-wallet-transactions-modal-container").html(data);
        
    //     // Show the wallet transactions modal
    //       $("#student-wallet-transactions-modal").modal('toggle');
    //   });
    // }
};

function generateFeeChallan(studentId){
  var sessionId = academicSessionHandler.getSelectedSessionId();
  window.open(baseURL + "/fees/generate-student-fee-challan-pdf/" + sessionId + "/" + studentId, '_blank');
};

function useWalletAmountCallBack() {
  $('#use-wallet-amount').change(function() {
       if($(this).is(":checked")) {
         var walletAmount = parseFloat($("#student-wallet-amount-value").text().trim());
         if(walletAmount <= 0) {
           showErrorDialogBox("You donot have required credits in your wallet. Please recharge it to use wallet amount.");
           $(this).prop( "checked", false );
           return;
         }
          $("#student-used-wallet-amount").val(0);
          $("#wallet-amount-input-container").attr("style", "display:block");
       }
       else{
         // Transfer wallet amount to paid amount
         var usedWalletAmount = getUsedWalletAmount();
         $("#student-used-wallet-amount").val(0);
         $("#student-paid-amount").val(feePayment.getPaidAmountValue() + usedWalletAmount);

         $("#wallet-amount-input-container").attr("style", "display:none");
       }
   });
}
//
// function autoFillFeesPayment() {
//   var inputFeesAmountStr = $("#student-total-input-fees").val();
//   var inputFeesAmount = 0;
//   if(inputFeesAmountStr != ""){
//       inputFeesAmount = parseFloat(inputFeesAmountStr);
//   }
//   $("div.collected-fees-details").each(function() {
//         $(this).find("tr.feehead-payment-row").each(function() {
//               if(inputFeesAmount <= 0){
//                   return;
//               }
//               $(this).find('input.collected-amount').first().val(0);
//               feePayment.computePaymentDueAmount($(this));
//
//               var dueAmountStr = $(this).find('input.payment-due-amount').val();
//               var dueAmount = 0;
//               if(dueAmountStr != ""){
//                   dueAmount = parseFloat(dueAmountStr);
//               }
//
//               var fillAmount = 0;
//               if(inputFeesAmount >= dueAmount){
//                   fillAmount = dueAmount;
//                   inputFeesAmount -= dueAmount;
//               }else{
//                   fillAmount = inputFeesAmount;
//                   inputFeesAmount = 0;
//               }
//               $(this).find('input.collected-amount').first().val(fillAmount);
//               feePayment.computePaymentDueAmount($(this));
//         });
//   });
//   if(inputFeesAmount > 0){
//     showErrorDialogBox(inputFeesAmount + " Rs. amount is more than the assigned fees. Please select more fees or reduce the amount.")
//   }
//   feePayment.computeFeesPaymentAmounts();
// }


function bindPaymentSearchStudentEvent() {
  var resultArea = "#student-payment-search-result";
  $('#student-payment-search').on('click', function () {
       // loadStudentPaymentDetails();
       feePayment.doneStudentSearchTyping(resultArea);
  });
  $("#student-payment-search-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        // loadStudentPaymentDetails();
        feePayment.doneStudentSearchTyping(resultArea);
    }
  });
  liveSearchHandler.bindEvent('#student-payment-search-text',resultArea,feePayment.doneStudentSearchTyping);
}

function confirmCancelTransaction(transactionId, studentId) {
    $("#fee-payment-transaction-view-modal").modal('toggle');
    $("#cancel-transaction-student-id").text(studentId);
    $("#cancel-transaction-id").text(transactionId);
    $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');
}

function reviewCancelTransaction() {
    $("#cancel-transaction-student-id").text("");
    $("#cancel-transaction-id").text("");
    $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');
    $("#fee-payment-transaction-view-modal").modal('toggle');
}

function cancelStudentTransaction() {
  var studentId = $("#cancel-transaction-student-id").text().trim();
  var transactionId = $("#cancel-transaction-id").text().trim();
  if(transactionId == ""){
    showErrorDialogBox("Invalid Transaction to cancel");
    return;
  }

  var cancelAt = new Date().getTime() / 1000;
  var cancelRemarks = $("#payment-cancel-remarks").val().trim();
  var feeCancelledPayload = {'instituteId' : null, 'transactionId' : transactionId, 'cancelBy' : null, 'cancelAt' : cancelAt, 'cancelRemarks' : cancelRemarks};

  $("#fee-payment-cancel-transaction-confirm-modal").modal('toggle');

  ajaxClient.post( "/fees/cancel-payment-transaction", {'feeCancelledPayload' : JSON.stringify(feeCancelledPayload)}, function(data) {

      returnToPaymentMainScreenDisplay();
      $("#fee-payment-status-modal-container").html(data);
      $("#fee-payment-status-modal").modal('toggle');
      loadStudentPaymentDetails(studentId);
  });
}


function bindPaymentActionEvents() {
  $("#past-payment-button").on('click', function () {
      pastTransactionsDisplay();
  });
}


function pastTransactionsDisplay() {
  $(".payment-main-screen-info").attr("style", "display:none");
  $(".fee-payment-tab").attr("style", "display:none");
  $("#fee-transactions-tab").attr("style", "display:block");
}


function returnToPaymentMainScreenDisplay() {
  $(".payment-main-screen-info").attr("style", "display:block");
  $(".fee-payment-tab").attr("style", "display:none");
  $("#fee-payment-view-tab").attr("style", "display:block");

}



function generateFeesPaymentCard(cardId, feeId,feeDisplayName,feeHeadRows, feeFineRow) {
      return "<div class=\"card card-border collected-fees-details\" id=\""+ cardId + "\"> <p style=\"display:none\" class=\"collected-fee-id\">"+feeId+"</p>  <div class=\"card-header\" id=\""+"fee-heading-" +feeId + "\"> <h5 class=\"mb-0\"> <button class=\"btn btn-link\" data-toggle=\"collapse\" data-target=\"#" + "collapse-fee-" +feeId+ "\" aria-expanded=\"true\" aria-controls=\"" + "collapse-fee-" +feeId+ "\"> <strong>" +  feeDisplayName.toUpperCase() + " </strong> </button> </h5> </div> <div id=\"" + "collapse-fee-" +feeId+ "\" class=\"collapse show\" aria-labelledby=\""+"fee-heading-" +feeId + "\" data-parent=\"#selected-fees-payment-list\"> <div class=\"card-body\"> <div class=\"table-responsive\"> <table class=\"table table-hover\"> <thead> <tr> <th scope=\"col\" class=\"w-25\">Fee Head</th> <!-- <th scope=\"col\" >Fee Head</th> --> <th scope=\"col\" >Assigned Amount</th> <th scope=\"col\" >Given Discount</th> <th scope=\"col\" >Collected Amount</th> <th scope=\"col\" >Payable Amount</th> <th scope=\"col\" >Amount Paid</th> <th scope=\"col\" >Instant Discount</th> <th scope=\"col\" >Due Amount</th> </tr> </thead> <tbody> " + feeHeadRows + " </tbody> </table> </div> " + feeFineRow +" </div> </div> </div>";
}

function feeHeadPaymentDetailRow(feeHeadId, feeHeadName,assignedAmount,paidAmount,balanceAmount,totalDiscount) {
    return "<tr class=\"feehead-payment-row\"> <td class=\"payment-feehead-id\" style=\"display:none\" >" + feeHeadId+ "</td> <td>" + feeHeadName+ "</td> <td><input type=\"text\" class=\"form-control form-control-sm assigned-amount\" value=\"" + assignedAmount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm assigned-discount\" value=\"" + totalDiscount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm paid-amount\" value=\"" + paidAmount.toFixed(2)+ "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm payment-balance-amount\" value=\"" + balanceAmount.toFixed(2) + "\" readonly></td> <td><input type=\"number\" class=\"form-control form-control-sm collected-amount\" value=\"0\" ></td> <td><input type=\"number\" class=\"form-control form-control-sm instant-discount-amount\" value=\"0\" ></td> <td><input type=\"text\" class=\"form-control form-control-sm payment-due-amount\" value=\"" + balanceAmount.toFixed(2)+ "\" readonly></td> </tr>";
}

function getUsedWalletAmount() {
  var walletAmount = parseFloat($("#student-wallet-amount-value").text().trim());

  var usedWalletAmount = 0;
  if($('#use-wallet-amount').is(":checked")) {
    usedWalletAmount = parseFloat($("#student-used-wallet-amount").val());
    if(usedWalletAmount <= 0){
      usedWalletAmount = 0;
    }
    if(usedWalletAmount > walletAmount){
      usedWalletAmount = walletAmount;
    }
  }
  return usedWalletAmount;
}
//
// function updateWalletAmount() {
//   var variousTotalFeesAmounts = feePayment.computeVariousTotalFeesAmounts();
//   var totalCollectableAmount = variousTotalFeesAmounts['totalCollectableAmount'];
//   var totalCollectedAmount = variousTotalFeesAmounts['totalCollectedAmount'];
//   var totalFineAmount = variousTotalFeesAmounts['totalFineAmount'];
//
//   var usedWalletAmount = getUsedWalletAmount();
//   if(usedWalletAmount > totalCollectableAmount){
//     usedWalletAmount = totalCollectableAmount;
//     $("#student-used-wallet-amount").val(usedWalletAmount);
//     $("#student-paid-amount").val(0);
//     feePayment.fillCollectedAmounts(usedWalletAmount - totalFineAmount);
//   }else{
//     $("#student-used-wallet-amount").val(usedWalletAmount);
//     // $("#student-payble-amount-display").val(totalCollectableAmount - usedWalletAmount);
//     $("#student-paid-amount").val(totalCollectedAmount + totalFineAmount - usedWalletAmount);
//     feePayment.fillCollectedAmounts(totalCollectedAmount + usedWalletAmount - totalFineAmount);
//   }
// }


function getFeeTypeDisplayName(feeType) {
  var feeTypeDisplayName = "";
  if(feeType == "REGULAR"){
      feeTypeDisplayName = "Regular Fees";
  }
  else if(feeType == "SPECIAL"){
      feeTypeDisplayName = "Special Fees";
  }
  else if(feeType == "ONE_TIME"){
      feeTypeDisplayName = "One Time Fees";
  }
  return feeTypeDisplayName;
}

function loadStudentPaymentDetails(studentId) {
    loadStudentPaymentDetails(studentId, false);
}

// -------------------------------------------------------------------------------Fee Follow Up Trail FE--------------------------------------------------------------------------------------------------------------

// function to open the delete popup
function deleteFeeFollowUpDetails(followUpId,studentId){
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong> <font color=\"red\">Are you sure that you want to delete this followup trail ? </font> </strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button  type=\"button\" class=\"btn btn-danger\" onclick=\"deleteStudentFeeFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(followUpId);
      $("p#student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

// function to open the edit modal
function editFeeFollowUpDetails(value){
     initDateWithYearRange("-10:+10", true);
     initPastDateById("fee-transaction-follow-up-date", 4000);
     initPastDateById("fee-follow-up-date", 4000);
     var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
     $("#update-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
     $("#update-fee-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
     $("#update-follow-up-mode").html("<option>"+followUpPayload.followUpMode+"</option>");
     $("#update-contact-person-name").val(followUpPayload.contactPersonName);
     $("#update-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
     $("#update-conversation").val(followUpPayload.conversation);

     if(followUpPayload.nextFollowUpDate != null){
         $("#update-next-fee-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
     }
     else{
       $("#update-next-fee-follow-up-date").val("");
     }
     $("#update-amount").val(followUpPayload.amount);
     $("#update-student-follow-up-id").text(followUpPayload.followUpId);
     $("#student-id").text(followUpPayload.entityId);
     $("#update-fee-follow-up-modal").modal('toggle');
}

// modal for opening the close modal
function closeFeeFollowUpDetails(followUpId,studentId,isStudent){
      if(!isStudent){
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong>Are you sure that you want to close this followup trail ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentFeeFollowupTrail('false','INACTIVE')\">Yes, Close It</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      }
      else{
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close All Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong style=\"color:red\">Are you sure that you want to close the all followup trail for this student ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"closeStudentFeeFollowupTrail('true','INACTIVE')\">Yes, Close All</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      }
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(followUpId);
      $("p#student-follow-up-id").text(studentId);
      $("#fee-follow-up-close-modal").modal('toggle');
}

// modal for adding the followup details
function addFeeFollowUpDetails(studentId){
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
      initPastDateById("fee-follow-up-date", 4000);
      $("p#student-follow-up-id").text(studentId);
      $("#next-fee-follow-up-date").val("");
      $("#add-fee-follow-up-modal").modal('toggle');
}

// ajax call for updating fee follow up trail
function updateStudentFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#update-fee-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#update-entity-name").val();
      var followUpDate = getDate($('#update-fee-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      var followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#update-contact-person-name").val();
      var followUpType = $("select#update-follow-up-type").val();
      var conversation = $("#update-conversation").val();
      var nextFollowUpDate = $('#update-next-fee-follow-up-date').val();
      if(nextFollowUpDate != ""){
          nextFollowUpDate = getDate($('#update-next-fee-follow-up-date').val());
          nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == null || nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }

      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#update-fee-follow-up-modal").modal('toggle');
        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#update-amount").val();
      var followUpStatus = status;
      var followUpId = $("#update-student-follow-up-id").text();
      var entityId = $("#student-id").text();
      var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode":"MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#update-fee-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/update-fee-followup-trail/"+sessionId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
          returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
          getFollowUpDetails();
 });
}

// add api ajax call
function addStudentFeeFollowupTrail(status){
      var invalid = validateMandatoryFields($("#add-fee-follow-up-modal"));
      if(invalid){
        return;
      }
      var sessionId = academicSessionHandler.getSelectedSessionId();
      var entityName = $("select#entity-name").val();
      var followUpDate = getDate($('#fee-follow-up-date').val());
      if(followUpDate == null){
        showErrorDialogBox("Please fill follow up date.");
        return;
      }
      var followUpDate = followUpDate.getTime()/1000;
      var contactPersonName = $("#contact-person-name").val();
      var followUpType = $("select#follow-up-type").val();
      var conversation = $("#conversation").val();
      var nextFollowUpDate = getDate($('#next-fee-follow-up-date').val());
      if(nextFollowUpDate != null){
        nextFollowUpDate = nextFollowUpDate.getTime()/1000;
      }
      if(nextFollowUpDate == null || nextFollowUpDate == ""){
        nextFollowUpDate = null;
      }

      if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
        $("#add-fee-follow-up-modal").modal('toggle');

        showErrorDialogBox("Follow up date cannot exceed the next follow up date");
        return;
      }
      var amount = $("#amount").val();
      var followUpStatus = status;
      var entityId = $("#student-follow-up-id").text();
      var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
      "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
      $("#add-fee-follow-up-modal").modal('toggle');
      ajaxClient.post("/fees/add-fee-followup-trail/"+sessionId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
          returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
          getFollowUpDetails();
 });
}

function getFollowUpDetails() {
  var sessionId = academicSessionHandler.getSelectedSessionId();
  var studentId = $("#student-follow-up-id").text().trim();
  ajaxClient.get( "/fees/follow-up-details/" + sessionId + "/" + studentId, function(data) {
      $("#follow-up-details-container-div").html(data);
      $(".tab-pane").removeClass("fade");
  });
}

// ajax api for deleting the follow up trail
function deleteStudentFeeFollowupTrail(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
    $("#fee-follow-up-close-modal").modal('toggle');
    ajaxClient.post( "/fees/delete-fee-followup-trail/"+academicSessionId+"/"+followUpId, {}, function(data) {
        returnToPaymentMainScreenDisplay();
          $("#fee-followup-trail-status-modal-container").html(data);
          $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
        getFollowUpDetails();
    });
}

// ajax api for closing the fee followup
function closeStudentFeeFollowupTrail(isStudent,status){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var followUpId = $("#delete-student-follow-up-id").text().trim();
      var studentId = $("#student-follow-up-id").text().trim();
      if(followUpId == ""){
        showErrorDialogBox("Invalid Follow Up to cancel");
        return;
      }
      $("#fee-follow-up-close-modal").modal('toggle');
      ajaxClient.post("/fees/close-fee-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+followUpId, {}, function(data) {
            returnToPaymentMainScreenDisplay();
            $("#fee-followup-trail-status-modal-container").html(data);
            $("#fee-followup-trail-status-modal").modal({backdrop: 'static', keyboard: false});
            getFollowUpDetails();
        });
}

// -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

function loadStudentPaymentDetails(studentId, openDetailsPage) {
  var academicSessionId = academicSessionHandler.getSelectedSessionId();
  ajaxClient.get( "/fees/student-payment-details/"+academicSessionId+"/"+studentId, function(data) {
      $("#student-payment-screen").html(data);
      feePayment.initCache();

      bindPaymentActionEvents();
      bindPaymentTransactionClickEvents();
      bindCollectFeesPaymentEvent();
      initDate(pastFeePaymentTransactionDays);
      bindViewPaymentFeesEvent();
      useWalletAmountCallBack();
      feePayment.fillNetAmountDisplay();
      setOtherSessionTotalDueAmount();
      if(openDetailsPage) {
        feePayment.collectFeesDisplay(academicSessionId);
      }
      getFeeFollowUpDetails();
  });
}

function bindCollectFeesPaymentEvent() {
    $("#fee-payment-confirm").on('click', function () {
        $("#fee-payment-confirm-modal").modal('toggle');
        var feePaymentPayload = collectFeesInformation();
        if(feePaymentPayload == null){
          return;
        }
        ajaxClient.post("/fees/collect-fee-payment",{'feePaymentPayload':JSON.stringify(feePaymentPayload)}, function(data){

            returnToPaymentMainScreenDisplay();
            $("#fee-payment-status-modal-container").html(data);
            $("#collect-fee-payment-status-modal").modal({backdrop: 'static', keyboard: false});
            loadStudentPaymentDetails(feePaymentPayload.feePaymentTransactionMetaData.studentId);
       });
    });
}

function collectFeesInformation() {
    var sessionId = $('#collect-fee-session-id').text().trim();
    var studentId = $('#collect-fee-student-id').text().trim();
    var $bankAccountInput = $('#bank-account-input');
    var bankAccountId = '';
    if ($bankAccountInput.length > 0 && $bankAccountInput.find(':selected').length > 0) {
        bankAccountId = $bankAccountInput.find(':selected').val().trim();
    }    
    var paymentMode = $('#payment-mode-input').find(':selected').val().trim();
    var reference = $('#payment-reference').val().trim();
    var date = getDate($('#payment-date').val());
    var remark = $('#payment-remark').val();
    var invalid = validateMandatoryFields($("#collect-fee-tab"));
    if(invalid){
      showErrorDialogBox("Please fill required fields before making payment.");
      return;
    }
    var invalidAmount = validateAmounts();
    if(invalidAmount) {
      return;
    }
    if(date == null){
      showErrorDialogBox("Please enter the date of payment");
      return null;
    }
    var currentDate = new Date();
    if(date.getTime() > currentDate.getTime()){
      showErrorDialogBox("Invalid payment date. Future transactions not allowd");
      return null;
    }
    // Computing the fine per fees
    $("div.collected-fees-details").each(function() {

    });
    var feeIdFeeHeadTransactionList = []
    $("div.collected-fees-details").each(function() {
          var feeId = $(this).find('p.collected-fee-id').text().trim();
          var totalPaidFine = parseFloat($(this).find(".fine-table").find(".paid-fine-amount").val());
          var feeHeadTransactionAmounts = [];
          $(this).find("tr.feehead-payment-row").each(function() {
                var feeHeadId = $(this).find('td.payment-feehead-id').text().trim();
                var collectedAmount = $(this).find('input.collected-amount').val();
                var instantDiscountAmount = $(this).find('input.instant-discount-amount').val();
                var feeHeadTransactionAmount = {'feeHeadId' :feeHeadId, 'paidAmount' :  collectedAmount, 'instantDiscount' : instantDiscountAmount};
                feeHeadTransactionAmounts.push(feeHeadTransactionAmount);
          });
          // Dividing the fine amount equally among all the feeheads
          if(feeHeadTransactionAmounts.length > 0){
            var feeHeadFineAmount = totalPaidFine/feeHeadTransactionAmounts.length;
            for(var i = 0; i < feeHeadTransactionAmounts.length; i++){
                feeHeadTransactionAmounts[i]['fineAmount'] = feeHeadFineAmount;
            }
          }
          var feeIdFeeHeadTransaction = {'feeId' : feeId, 'feeHeadTransactionAmounts' :feeHeadTransactionAmounts};
          feeIdFeeHeadTransactionList.push(feeIdFeeHeadTransaction);
    });
    var debitWalletAmount = getUsedWalletAmount();
    var feePaymentTransactionMetaData = {'studentId' : studentId,'bankAccountId' : bankAccountId, 'transactionMode' : paymentMode, 'transactionReference': reference,'transactionDate' : date.getTime()/1000, 'academicSessionId' : sessionId , 'debitWalletAmount' : debitWalletAmount, 'remark' : remark};
    return {'feePaymentTransactionMetaData' : feePaymentTransactionMetaData, 'feeIdFeeHeadTransactionList' : feeIdFeeHeadTransactionList};
}

function validateAmounts () {

  var totalFeeAssignment = parseFloat($("#net-total-fees-assigned-amount").text());
  var paidAmount = parseFloat($("#net-total-fees-collected-amount").text());
  var instantDiscount = parseFloat($("#net-total-fees-instant-discount-amount").text());

  if(totalFeeAssignment == "" || totalFeeAssignment == 0) {
    showErrorDialogBox("No amount added to collect fees.");
    return true;
  }

  if((paidAmount == "" || paidAmount == 0)) {
    if(confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")) {
      return false;
    }
    return true;
  }

  return false;
}

function bindViewPaymentFeesEvent() {
    // $('.view-unpaid-fee').on('click', function () {
    //     var assignedFeesJson = $(this).parent().parent().find('p.unpaid-fee-info').text().trim();
    //     var assignedFees = JSON.parse(assignedFeesJson);
    //     populateViewFeeAssignmentContent(assignedFees);
    // });
    $('.view-payment-fee-info').on('click', function () {
        var transactedFeesJson = $(this).parent().parent().find('p.payment-fee-info').text().trim();
        var transactedFees = JSON.parse(transactedFeesJson);
        populateViewFeePaymentContent(transactedFees);
    });
}

function setOtherSessionTotalDueAmount () {

  $('.totalDueAmount').each( function () {
    var i = 0;
    var totalAssignedAmount = 0;
    var sumTotalDiscount = 0;
    var totalPaidAmount = 0;
    var totalBalanceAmount = 0;
    var totalDueFineAmount = 0;
    $(this).parent().parent().parent().parent().find('.payment-fee-info').each( function () {

      var transactedFeesJson = $(this).text().trim();
      var transactedFees = JSON.parse(transactedFeesJson);
      for( i = 0 ; i < transactedFees.feeHeadPaymentDetails.length; i++){
        var feeHeadName = transactedFees.feeHeadPaymentDetails[i].feeHeadConfiguration.feeHead;
        var assignedAmount = transactedFees.feeHeadPaymentDetails[i].assignedAmount;
        var assignedDiscountAmount = transactedFees.feeHeadPaymentDetails[i].assignedDiscountAmount ;
        var instantDiscountAmount = transactedFees.feeHeadPaymentDetails[i].instantDiscountAmount;
        var paidAmount = transactedFees.feeHeadPaymentDetails[i].paidAmount;
        var balanceAmount = transactedFees.feeHeadPaymentDetails[i].balanceAmount;
        var dueFineAmount = transactedFees.feeHeadPaymentDetails[i].computedDueFineAmount;
        console.log(dueFineAmount);
        var totalDiscount = assignedDiscountAmount + instantDiscountAmount;

        totalAssignedAmount += assignedAmount;
        sumTotalDiscount += totalDiscount;
        totalPaidAmount += paidAmount;
        totalBalanceAmount += balanceAmount;
        totalDueFineAmount += dueFineAmount;
      }

    });
    totalDueAmounts = totalBalanceAmount + totalDueFineAmount;
    $(this).text(totalDueAmounts.toFixed(2) + "/-");
    $(this).css("font-weight","Bold");
  });
}

function populateViewFeePaymentContent(transactedFees) {
    $('#view-payment-fee-name').text(transactedFees.feeConfigurationBasicInfo.feeName);
    var feeHeadRow = "";
    var i = 0;
    var totalAssignedAmount = 0;
    var sumTotalDiscount = 0;
    var totalPaidAmount = 0;
    var totalBalanceAmount = 0;

    for( i = 0 ; i < transactedFees.feeHeadPaymentDetails.length; i++){
      var feeHeadName = transactedFees.feeHeadPaymentDetails[i].feeHeadConfiguration.feeHead;
      var assignedAmount = transactedFees.feeHeadPaymentDetails[i].assignedAmount;
      var assignedDiscountAmount = transactedFees.feeHeadPaymentDetails[i].assignedDiscountAmount ;
      var instantDiscountAmount = transactedFees.feeHeadPaymentDetails[i].instantDiscountAmount;
      var paidAmount = transactedFees.feeHeadPaymentDetails[i].paidAmount;
      var balanceAmount = transactedFees.feeHeadPaymentDetails[i].balanceAmount;
      var totalDiscount = assignedDiscountAmount + instantDiscountAmount;

      totalAssignedAmount += assignedAmount;
      sumTotalDiscount += totalDiscount;
      totalPaidAmount += paidAmount;
      totalBalanceAmount += balanceAmount;

      feeHeadRow = feeHeadRow + "<tr> <td>" + feeHeadName + "</td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + assignedAmount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + totalDiscount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + paidAmount.toFixed(2) + "\" readonly></td> <td><input type=\"text\" class=\"form-control form-control-sm\" value=\"" + balanceAmount.toFixed(2) + "\" readonly></td> </tr>";
    }

    feeHeadRow = feeHeadRow + "<tr> <td> <strong> TOTAL </strong></td> <td>"+totalAssignedAmount.toFixed(2)+"</td> <td>"+sumTotalDiscount.toFixed(2)+"</td> <td>"+totalPaidAmount.toFixed(2)+"</td> <td>"+totalBalanceAmount.toFixed(2)+"</td> </tr>";
    $('#view-payment-feehead-details').html(feeHeadRow);
    $("#fee-payment-details-view-modal").modal('toggle');
}


var reminders = {
  dataCache : {},

  loadSendReminderPage : function () {
    ajaxClient.get("/fees/due-fees-reminder-screen", function(data) {
        $("#main-content").html(data);
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits")
        reminders.bindSelectClassCheckboxEvent();
        reminders.dataCache['sendReminders'] = {};
        reminders.dataCache.selectedStudentCount = 0;
        reminders.dataCache.templateLoaded = false;
        initSelect2();
        academicSessionHandler.bindSessionChangeEvent(reminders.loadBulkStudentScreen);
    });
  },

  loadBulkStudentScreen : function (reload, sessionId, dueDate, includeFine, includePastSessions, requiredStandards) {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/fees/load-bulk-student-screen/"+academicSessionId, function(data) {
        $("#fee-reminder-screen").html(data);
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/fees/get-channel-credits")
        reminders.bindSelectClassCheckboxEvent();
        reminders.dataCache['sendReminders'] = {};
        reminders.dataCache.selectedStudentCount = 0;
        reminders.dataCache.templateLoaded = false;
        initSelect2();

        if(reload) {
          ajaxClient.get("/fees/due-fees-students/"+sessionId+"/"+dueDate.getTime()/1000 +"/"+includeFine+"?requiredStandards="+requiredStandards + "&includePastSessions="+includePastSessions, function(data) {
              $("#due-fees-student-list").html(data);
              reminders.dataCache['sendReminders']['dueFeesDate'] = dueDate.getTime()/1000;
              reminders.dataCache['sendReminders']['requiredStandards'] = requiredStandards;
              reminders.dataCache.selectedStudentCount = 0;
          });

        }

    });
  },

  loadDueFeeStudentsForInstitute : function () {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var dueDate = getDate($('#reminder-fee-due-date').val());
    if(dueDate == null){
      showErrorDialogBox("Please fill due date.");
      return;
    }

    reminders.dataCache.dueDate = getDate($('#reminder-fee-due-date').val());

    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    reminders.dataCache.includeFine = includeFine;
    reminders.dataCache.includePastSessions = includePastSessions;

    var requiredStandards = $(".select-reminder-class").val();
    if(requiredStandards != undefined && requiredStandards != null) {
      requiredStandards = requiredStandards.join(",");
    } else {
      requiredStandards = "";
    }
    reminders.dataCache.requiredStandards = requiredStandards;
    // var requiredStandards = reminders.getRequiredStandardsCSV();

    ajaxClient.get("/fees/due-fees-students/"+sessionId+"/"+dueDate.getTime()/1000 +"/"+includeFine+"?requiredStandards="+requiredStandards+"&includePastSessions="+includePastSessions, function(data) {
        $("#due-fees-student-list").html(data);
        reminders.dataCache['sendReminders']['dueFeesDate'] = dueDate.getTime()/1000;
        reminders.dataCache['sendReminders']['requiredStandards'] = requiredStandards;
        reminders.dataCache.selectedStudentCount = 0;
    });
  },

 selectBulkStudentList : function (selectAllCheckbox) {
      if(selectAllCheckbox.checked){
        $(".bulk-student-select-checkbox").prop('checked', true);
        var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
        $("#bulk-students-selected-count").html(selectedStudentCount);
        reminders.dataCache.selectedStudentCount = selectedStudentCount;
      }
      else{
          $(".bulk-student-select-checkbox").prop('checked', false);
          $("#bulk-students-selected-count").html(0);
          reminders.dataCache.selectedStudentCount = 0;
      }
  },

  bindSelectClassCheckboxEvent: function () {
    $('#reminder-select-all-class').change(function() {
         if($(this).is(":checked")) {
           $('.select-reminder-student-class-menu').attr("style", "display:none");
         }
         else{
            $('.select-reminder-student-class-menu').attr("style", "display:block");
         }
     });
  },

  getRequiredStandards: function () {
    var requiredStandards = [];
    if($("#reminder-select-all-class").is(":checked")){
      return requiredStandards;
    }

    $('.select-reminder-class').each( function () {
        if($(this).prop("checked")){
            requiredStandards.push($(this).attr("id"));
        }
    });
    return requiredStandards;
  },

  getRequiredStandardsCSV : function () {
    var requiredStandardsCSV = "";
    var requiredStandards =  reminders.getRequiredStandards();
    for(var i = 0;i < requiredStandards.length; i++){
      if(i == 0){
        requiredStandardsCSV = requiredStandards[i];
      }else{
        requiredStandardsCSV = requiredStandardsCSV + "," + requiredStandards[i];
      }
    }

    return requiredStandardsCSV;
  },

 bulkStudentSelectCheckbox : function(studentSelectCheckbox) {
      var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
      $("#bulk-students-selected-count").html(selectedStudentCount);
      reminders.dataCache.selectedStudentCount = selectedStudentCount;
  },

  bulkReminderConfirmModal : function () {
    var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
    if(selectedStudentCount == 0){
      showErrorDialogBox("Please select atleast one student for sending reminders.");
      return;
    }
    reminders.dataCache.selectedStudentCount = selectedStudentCount;

    var includeFine = $("#include-fee-fine").is(":checked");
    var includePastSessions = $("#include-past-sessions").is(":checked");

    if(reminders.dataCache.templateLoaded){
        reminders.updateReminderModalDisplay(includeFine, includePastSessions);
        $("#bulk-reminder-confirmation-modal").modal({backdrop: 'static', keyboard: false});
        // $("#bulk-notification-confirmation-modal").modal({backdrop: 'static', keyboard: false});
        $(".selected-student-count").text(selectedStudentCount);
    }else{
      ajaxClient.get("/fees/reminder-templates", function(data){
          $("#bulk-reminder-template-modal-container").html(data);
          reminders.updateReminderModalDisplay(includeFine, includePastSessions);
          $("#bulk-reminder-confirmation-modal").modal({backdrop: 'static', keyboard: false});
          var audioTemplates = readJson("#audio-templates-json");
          reminders.bindAudioTemplateClickAction(audioTemplates);
          $(".selected-student-count").text(selectedStudentCount);
          reminders.dataCache.templateLoaded = true;
     });
    }
  },

  updateReminderModalDisplay : function (includeFine, includePastSessions) {
      if(includeFine){
          $(".fine-included").text("Yes");
      }else{
          $(".fine-included").text("No");
      }

      if(includePastSessions){
        $(".past-session-included").text("Yes");
    }else{
        $(".past-session-included").text("No");
    }

      $("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count);
      $("#audio-voice-call-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count);

      $("#audio-template-selection-display").attr("style", "display:none");
      $('#audio-template-select').val("");
      $("#send-bulk-voice-calls-button").prop('disabled', true);
      $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:none;");

      if(reminders.dataCache.selectedStudentCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
        $("#send-bulk-sms-button").prop('disabled', true);
        $("#insufficient-sms-credits-message").attr("style", "color:red; display:block;");
      }else{
        $("#send-bulk-sms-button").prop('disabled', false);
        $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
      }
  },

  bindAudioTemplateClickAction : function (audioTemplates) {
    $('#audio-template-select').change(function(){
        var templateId = $(this).val();
        if(templateId == ""){
          $("#audio-template-selection-display").attr("style", "display:none");
          $("#send-bulk-voice-calls-button").prop('disabled', true);
          return;
        }

        $("#audio-template-selection-display").attr("style", "display:block");

        for(var i = 0; i < audioTemplates.length; i++){
          var audioTemplate = audioTemplates[i];
          if(templateId == audioTemplate.templateId){
            $("#template-name").text(audioTemplate.templateName);
            $("#template-duration").text(round(audioTemplate.metadata.audio_metadata["duration"], 0));
            $("#template-credits").text(audioTemplate.credits);

            var totalCredits = audioTemplate.credits * reminders.dataCache.selectedStudentCount;
            $("#total-student-credits").text(totalCredits);
            reminders.dataCache.selectedAudioCredits = audioTemplate.credits;

            if(totalCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
                $("#send-bulk-voice-calls-button").prop('disabled', true);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:block;");
            }else{
                $("#send-bulk-voice-calls-button").prop('disabled', false);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:none;");
            }

            break;
          }
        }
    });
  },

  bulkDemandNoticeConfirmModal : function () {
    var selectedStudentCount = $('input.bulk-student-select-checkbox:checkbox:checked').length;
    if(selectedStudentCount == 0){
      showErrorDialogBox("Please select atleast one student for generating fee notice.");
      return;
    }
    var includeFineText = "<span style=\"color:red\">(FINE AMOUNT NOT INCLUDED)</span>";
    var demandNoticeButtonText = "Yes, Fee Notice Without Fine";
    if($("#include-fee-fine").is(":checked")){
      includeFineText = "<span style=\"color:green\">(FINE AMOUNT INCLUDED)</span>";
      demandNoticeButtonText = "Yes, Fee Notice With Fine";
    }

    $("#demand-notice-confirm-button").html(demandNoticeButtonText);

    $("#bulk-demand-notice-confirmation-modal-text").html("You have selected "+selectedStudentCount+ " students to generate fee notice. Do you want to proceed? <br>" + includeFineText);
    $("#bulk-demand-notice-confirmation-modal").modal({backdrop: 'static', keyboard: false});

  },

  sendBulkSMSReminders : function () {
    if(reminders.dataCache.selectedStudentCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + reminders.dataCache.selectedStudentCount + " SMS. Please recharge.");
      return;
    }
    var batchName = $("#sms-reminder-fee-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent reminders.")
      return;
    }

    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'SMS', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  sendAudioVoiceCall : function () {
    if(reminders.dataCache.selectedStudentCount *  reminders.dataCache.selectedAudioCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + reminders.dataCache.selectedStudentCount + " voice calls. Please recharge.");
      return;
    }

    var batchName = $("#voice-reminder-fee-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent reminders.")
      return;
    }
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var templateId = $('#audio-template-select').find(':selected').val().trim();
    if(templateId == ""){
      showErrorDialogBox("Please select audio clip for voice call");
      return;
    }

    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'CALL', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'audioTemplateId' : templateId, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  sendMobileAppNotifications : function () {
    var batchName = null;
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }

    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }

    var templateId = null;
    $("#bulk-reminder-confirmation-modal").modal('toggle');

    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var data = {'batchName':batchName, 'dueDate': dueFeesDate, 'computeFine' : includeFine, 'academicSessionId' : null,'deliveryMode':'APP', 'requiredStanardsCSV' : requiredStandards , 'studentIds':studentIds, 'audioTemplateId' : templateId, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/send-due-payment-reminders",{'sendRemindersPayload':JSON.stringify(data)}, function(data){
        $("#fee-reminder-status-modal-container").html(data);
        $("#fee-reminder-status-modal").modal('toggle');
   });
  },

  generateDemandNotice : function () {
    $("#bulk-demand-notice-confirmation-modal").modal('toggle');
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }
    var includePastSessions = false;
    if($("#include-past-sessions").is(":checked")){
      includePastSessions = true;
    }
    var studentIds = reminders.getSelectedBulkStudentIds();
    var dueFeesDate = reminders.dataCache['sendReminders']['dueFeesDate'];
    var requiredStandards = reminders.dataCache['sendReminders']['requiredStandards'];
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    var studentIdsCSV = "";

    for(var i = 0; i < studentIds.length; i++){
        if(i == 0){
          studentIdsCSV += studentIds[i];
        }
        else{
          studentIdsCSV =  studentIdsCSV + "," + studentIds[i];
        }

    }
    demandNoticePayload = {'academicSessionId' : academicSessionId, 'dueFeesDate': dueFeesDate, 'includeFine' : includeFine, 'requiredStanardsCSV' : requiredStandards, 'students' : studentIds, 'includePastSessions' : includePastSessions};
    ajaxClient.post("/fees/submit-demand-notice-students",{'demandNoticePayload':JSON.stringify(demandNoticePayload)}, function(demand_notice_request_id){
        window.open(baseURL + "/fees/generate-demand-notice/"+demand_notice_request_id, '_blank');
   });
  },

  getSelectedBulkStudentIds :function () {
    var studentIds = [];
    $("input.bulk-student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-reminder-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    return studentIds;
  },

  loadNotificationHistoryHomePage : function () {
    ajaxClient.get("/fees/notification-history-homepage", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(reminders.changeSession);
        reminders.getNotificationHistory(true, "SMS", "BATCH");
        reminders.dataCache['notificationHistoryTab'] = "BATCH";
        // reminders.loadNotificationHistoryEvents();
        //
        //
        // getPaymentTransactions(true, "ACTIVE");
        // academicSessionHandler.bindSessionChangeEvent(loadPaymentTransactionsForSession);
        // bindPaymentTransactionClickEvents();

    });
  },

  changeSession : function () {
    var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
    var deliveryModeTab = reminders.getDeliveryModeTab();
    reminders.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
  },

  getNotificationHistory : function (freshSearch, deliveryMode, notificationHistoryTab) {
    reminders.dataCache['notificationHistoryTab'] = notificationHistoryTab;
    reminders.dataCache['deliveryModeTab'] = deliveryMode;

    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;

    var sessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.get("/fees/notification-history/"+sessionId+"/"+deliveryMode+"/"+notificationHistoryTab+"/"+offset+"/"+itemsPerPage, function(data) {
        var screenHeight = 0;
        if(deliveryMode == "SMS"){
          $("#smsNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }else if(deliveryMode == "CALL"){
          $("#voiceNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        } else if(deliveryMode == "APP"){
          $("#mobileNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }

        $('.notification-history-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
        var response = JSON.parse($("#pagination-info").text().trim());
        $('#items-per-page').val(response.itemsPerPage);
        $('.page-item').removeClass('active');
        var pageNumber = (response.offset/response.itemsPerPage) + 1;
        $('#page-number-'+page_number).addClass('active');
        if(notificationHistoryTab == 'INDIVIDUAL'){
          $("#nav-individual-history-tab").addClass('active');
          $("#nav-batch-history-tab").removeClass('active');
          $("#individual-history-content").addClass('show active');
          $("#batch-history-content").removeClass('show active');
        }
        reminders.bindIndividualNotificationsContentView();
        reminders.initPagination();
    });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = reminders.dataCache['notificationHistoryTab'];
        var deliveryModeTab = reminders.getDeliveryModeTab();
        reminders.getNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
      }
    );
  },

  getDeliveryModeTab : function () {
      var deliveryModeTab = reminders.dataCache['deliveryModeTab'];
      if(deliveryModeTab == null || deliveryModeTab == ""){
        return "SMS";
      }
      return deliveryModeTab;
  },

  loadBatchNotificationsDetails : function (batchId) {
    ajaxClient.get("/fees/batch-notification-details/"+batchId, function(data) {
        $("#batch-notification-detail-modal-container").html(data);
        $("#view-batch-notification-modal").modal('toggle');
    });
  },

  bindIndividualNotificationsContentView : function () {
    $(".view-individual-notification").click(function () {
        var notificationContent = $(this).parent().find("p.notification-content").text().trim();
        $("#individual-notification-detail-modal").find(".modal-body").html("<span style=\"white-space: pre-line;\">" + notificationContent + "</span>");
        $("#individual-notification-detail-modal").modal('toggle');
    });
  },

  // -----------------------------------------------For Reminder Follow Up FE --------------------------------------------


   loadReminderStudentFollowUpDetails :function(studentId){
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/fees/get-follow-up-remainder-details/"+academicSessionId+"/"+studentId,function(data){
            $("#fee-reminder-screen").html(data);
            $("#academic-session").attr('style','display:none;');
      });
  },

  closeReminderFeeFollowUpDetails: function(studentId){
      var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close All Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
      var modalBody = "<strong style=\"color:red\">Are you sure that you want to close the all followup trail for this student ?</strong>";
      var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.closeStudentRemainderFeeFollowupTrail('true','INACTIVE')\">Yes, Close All</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
      $("#modal-header").html(modalHeader);
      $("#modal-body").html(modalBody);
      $("#modal-footer").html(modalFooter);
      $("p#delete-student-follow-up-id").text(studentId);
      $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
  },

  closeStudentRemainderFeeFollowupTrail:  function(isStudent,status){
      $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var studentId = $("#delete-student-follow-up-id").text().trim();
      var followUpStudentId = studentId;
      if(studentId == ""){
        showErrorDialogBox("Invalid Follow Up to cancel");
        return;
      }


        ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+studentId+"/"+followUpStudentId, {}, function(data) {
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
            reminders.loadReminderStudentFollowUpDetails();
        });
  },

  closeFeeReminderFollowUpDetails : function (followUpId,studentId,isStudent){
        var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Close Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
        var modalBody = "<strong>Are you sure that you want to close this followup trail ?</strong>";
        var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.closeStudentReminderSingleFeeFollowupTrail('false','INACTIVE')\">Yes, Close It</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";

        $("#modal-header").html(modalHeader);
        $("#modal-body").html(modalBody);
        $("#modal-footer").html(modalFooter);
        $("p#delete-student-follow-up-id").text(followUpId);
        $("p#student-follow-up-id").text(studentId);
        $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
  },

   closeStudentReminderSingleFeeFollowupTrail :function(isStudent,status){
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var followUpId = $("#delete-student-follow-up-id").text().trim();
        var studentId = $("#student-follow-up-id").text().trim();
        if(followUpId == ""){
          showErrorDialogBox("Invalid Follow Up to cancel");
          return;
        }
          $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
          ajaxClient.post("/fees/close-fee-remainder-followup-trail/"+academicSessionId+"/"+isStudent+"/"+status+"/"+followUpId+"/"+studentId, {}, function(data) {
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
            reminders.loadReminderStudentFollowUpDetails();
          });
  },

    addFeeReminderFollowUpDetails : function(studentId){
        initDateWithYearRange("-10:+10", true);
        initPastDateById("fee-transaction-follow-up-date", 4000);
        initPastDateById("fee-follow-up-date", 4000);
        $("p#student-follow-up-id").text(studentId);
        $("#next-transaction-fee-follow-up-date").val("");
        $("#add-fee-reminder-follow-up-modal").modal('toggle');
  },

  backButton : function() {

      var sessionId = academicSessionHandler.getSelectedSessionId();
      var dueDate = reminders.dataCache.dueDate;
      var includeFine = reminders.dataCache.includeFine;
      var includePastSessions = reminders.dataCache.includePastSessions;
      var requiredStandards = reminders.dataCache.requiredStandards;
      reminders.loadBulkStudentScreen(true, sessionId, dueDate, includeFine, includePastSessions, requiredStandards);

  },


   addReminderStudentFeeFollowupTrail: function(status){
        var invalid = validateMandatoryFields($("#add-fee-reminder-follow-up-modal"));
        if(invalid){
          return;
        }
        var sessionId = academicSessionHandler.getSelectedSessionId();
        var entityName = $("select#entity-transaction-name").val();
        var followUpDate = getDate($('#fee-transaction-follow-up-date').val());
        if(followUpDate == null){
          showErrorDialogBox("Please fill follow up date.");
          return;
        }
        var followUpDate = followUpDate.getTime()/1000;

        var contactPersonName = $("#contact-person-transaction-name").val();
        var followUpType = $("select#follow-up-transaction-type").val();
        var conversation = $("#transaction-conversation").val();
        var nextFollowUpDate = $('#next-transaction-fee-follow-up-date').val();
        if(nextFollowUpDate != ""){
          nextFollowUpDate = getDate($('#next-transaction-fee-follow-up-date').val());
          nextFollowUpDate = nextFollowUpDate.getTime()/1000;
        }
        if(nextFollowUpDate == null || nextFollowUpDate == ""){
          nextFollowUpDate = null;
        }

        if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
          $("#add-fee-reminder-follow-up-modal").modal('toggle');
          showErrorDialogBox("Follow up date cannot exceed the next follow up date");
          return;
        }
        var amount = $("#amount-transaction").val();
        var followUpStatus = status;
        var entityId = $("#student-follow-up-id").text();
        var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
        "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
        $("#add-fee-reminder-follow-up-modal").modal('toggle');

        ajaxClient.post("/fees/add-fee-reminder-trail/"+sessionId+"/"+entityId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
            $("#fee-payment-reminder-student-detail-modal-container").html(data);
            $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
       });
 },

 editReminderFeeFollowUpDetails : function(value){
      initDateWithYearRange("-10:+10", true);
      initPastDateById("fee-transaction-follow-up-date", 4000);
      initPastDateById("fee-follow-up-date", 4000);
      var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
      $("#update-transaction-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
      $("#update-transaction-fee-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
      $("#update-transaction-contact-person-name").val(followUpPayload.contactPersonName);
      $("#update-transaction-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
      $("#update-transaction-conversation").val(followUpPayload.conversation);

      if(followUpPayload.nextFollowUpDate != null){
        $("#update-transaction-next-fee-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
      }
      else{
          $("#update-transaction-next-fee-follow-up-date").val("");
      }
      $("#update-transaction-amount").val(followUpPayload.amount);
      $("#update-student-follow-up-id").text(followUpPayload.followUpId);
      $("#student-id").text(followUpPayload.entityId);
      $("#update-reminder-fee-follow-up-modal").modal('toggle');
},

 updateStudentReminderFeeFollowupTrail: function(status){
     var invalid = validateMandatoryFields($("#update-reminder-fee-follow-up-modal"));
     if(invalid){
       return;
     }
     var sessionId = academicSessionHandler.getSelectedSessionId();
     var entityName = $("select#update-transaction-entity-name").val();
     var followUpDate = getDate($('#update-transaction-fee-follow-up-date').val());
     if(followUpDate == null){
       showErrorDialogBox("Please fill follow up date.");
       return;
     }
     var followUpDate = followUpDate.getTime()/1000;
     var followUpMode = $("select#update-transaction-follow-up-mode").val();
     var contactPersonName = $("#update-transaction-contact-person-name").val();
     var followUpType = $("select#update-transaction-follow-up-type").val();
     var conversation = $("#update-transaction-conversation").val();
     var nextFollowUpDate = $('#update-transaction-next-fee-follow-up-date').val();
     if(nextFollowUpDate != ""){
       nextFollowUpDate = getDate($('#update-transaction-next-fee-follow-up-date').val());
       nextFollowUpDate = nextFollowUpDate.getTime()/1000;
     }
     if(nextFollowUpDate == null || nextFollowUpDate == ""){
       nextFollowUpDate = null;
     }

     if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
       $("#update-reminder-fee-follow-up-modal").modal('toggle');
       showErrorDialogBox("Follow up date cannot exceed the next follow up date");
       return;
     }

     var amount = $("#update-transaction-amount").val();
     var followUpStatus = status;
     var followUpId = $("#update-student-follow-up-id").text();
     var entityId = $("#student-id").text();
     var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
     "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
     $("#update-reminder-fee-follow-up-modal").modal('toggle');
     ajaxClient.post("/fees/update-reminder-followup-trail/"+sessionId+"/"+entityId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
       $("#fee-payment-reminder-student-detail-modal-container").html(data);
       $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
       reminders.loadReminderStudentFollowUpDetails();
    });
},

 deleteReminderFeeFollowUpDetails: function(followUpId,studentId){
       var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Fee Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
       var modalBody = "<strong color=\"red\">Are you sure that you want to Delete this followup trail ?</strong>";
       var modalFooter = "<p style=\"display:none\" id=\"delete-student-follow-up-id\"></p><p style=\"display:none\" id=\"student-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"reminders.deleteStudentReminderFeeFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
       $("#modal-header").html(modalHeader);
       $("#modal-body").html(modalBody);
       $("#modal-footer").html(modalFooter);
       $("p#delete-student-follow-up-id").text(followUpId);
       $("p#student-follow-up-id").text(studentId);
       $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
},

deleteStudentReminderFeeFollowupTrail: function (){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var followUpId = $("#delete-student-follow-up-id").text().trim();
    var studentId = $("#student-follow-up-id").text().trim();
    if(followUpId == ""){
      showErrorDialogBox("Invalid Follow Up to cancel");
      return;
    }
    $("#all-fee-follow-reminder-up-close-modal").modal('toggle');
    ajaxClient.post( "/fees/delete-fee-reminder-followup-trail/"+academicSessionId+"/"+followUpId+"/"+studentId, {}, function(data) {
        $("#fee-payment-reminder-student-detail-modal-container").html(data);
        $("#fee-reminder-follow-up-student-status-modal").modal({backdrop: 'static', keyboard: false});
        reminders.loadReminderStudentFollowUpDetails();
    });
}
};


var studentWalletHandler = {
  dataCache : {},

  confirmWalletRecharge: function () {
    var studentId = $("#wallet-recharge-student-id").text().trim();
    var studentNameIdentifier = $("#wallet-recharge-student-name-identifier").text().trim();
    var invalid = validateMandatoryFields($("#student-wallet-recharge-modal").find(".modal-body"));
    if(invalid){
      return;
    }

    var rechargeAmount = getFloatValue($("#student-recharge-wallet-amount").val());
    if(rechargeAmount <= 0){
      $("#student-recharge-wallet-amount").css("border", "1px solid #ff8795");
      $("#student-recharge-wallet-amount").after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">Wallet amount should be > 0</span></p>");
      return;
    }
    var description = $("#student-recharge-wallet-description").val();
    var paymentMode = $('#student-recharge-payment-mode').find(':selected').val().trim();
    var rechargeWalletDate = getDate($('#student-rechage-wallet-date').val());

    studentWalletHandler.dataCache.studentId = studentId;
    studentWalletHandler.dataCache.studentNameIdentifier = studentNameIdentifier;
    studentWalletHandler.dataCache.amount = rechargeAmount;
    studentWalletHandler.dataCache.description = description;
    studentWalletHandler.dataCache.paymentMode = paymentMode;
    studentWalletHandler.dataCache.transactionDate = rechargeWalletDate.getTime()/1000;

    $("#student-wallet-recharge-modal").modal("toggle");

    var confirmText = "Do you want to recharge wallet of student " +studentNameIdentifier + " with amount = " + rechargeAmount + "/- ?";
    $("#student-wallet-recharge-confirm-modal").find(".student-wallet-recharge-confirm-text").text(confirmText);
    $("#student-wallet-recharge-confirm-modal").modal({backdrop: 'static', keyboard: false});

  },

  rechargeWallet : function () {
    $("#student-wallet-recharge-confirm-modal").modal("toggle");
    var walletRechargePayload = {"studentId" : studentWalletHandler.dataCache.studentId, "amount" : studentWalletHandler.dataCache.amount,
    "description" : studentWalletHandler.dataCache.description, "transactionMode" : studentWalletHandler.dataCache.paymentMode, "transactionDate" : studentWalletHandler.dataCache.transactionDate};

    ajaxClient.post("/fees/recharge-student-wallet", { "walletRechargePayload" : JSON.stringify(walletRechargePayload) }, function(data) {
        $("#student-wallet-recharge-status-modal-container").html(data);
        $("#student-wallet-recharge-status-modal").modal('toggle');
        loadStudentPaymentDetails(studentWalletHandler.dataCache.studentId);
    });
  },

  loadWalletTransactions : function () {
    var studentId = $("#wallet-recharge-student-id").text().trim();
    ajaxClient.get("/fees/wallet-transactions/"+studentId, function(data) {
        $("#student-wallet-transactions-modal-container").html(data);
        $("#student-wallet-transactions-modal").modal({backdrop: 'static', keyboard: false});
    });
  }
}

function roundOffNumber(number, decimalPlaces) {
  return parseFloat(number.toFixed(decimalPlaces));
}

function sortMapByValue(dict) {
  // Convert dictionary to array of arrays
  var items = Object.keys(dict).map(function(key) {
    return [key, dict[key]];
  });

  // Sort the array based on the second element (the value) in ascending order
  items.sort(function(first, second) {
    return first[1] - second[1];
  });

  // Convert back to dictionary
  var sortedDict = {};
  items.forEach(function(item) {
    sortedDict[item[0]] = item[1];
  });
  return sortedDict;
}

function validateSiblingPaymentAmounts () {

  var totalFeeAssignment = parseFloat($("#sibling-total-balance-amount-display").text());
  var paidAmount = parseFloat($("#sibling-student-paid-amount").text());
  var instantDiscount = parseFloat($("#sibling-total-instant-discount-amount").text());

  if(totalFeeAssignment == "" || totalFeeAssignment == 0) {
    showErrorDialogBox("No amount added to collect fees.");
    return true;
  }

  if((paidAmount == "" || paidAmount == 0)) {
    if(confirm("You are about to make payment with 0 paid amount. Press cancel to go back.")) {
      return false;
    }
    return true;
  }

  var flag = false;
  var errorMessage = "";
  $("tr.sibling-payment-details-tr").each(function() {
    var siblingDueFeeDetail = JSON.parse($(this).find(".sibling-due-fee-detail").text());
    var studentFullName = siblingDueFeeDetail.studentFullName;
    // var fineCollectionAmount = $(".sibling-student-fine-amount").val();
    // var instantDiscountAmount = $(".sibling-student-instant-discount-amount").val();
    // var walletAmount = 0;
    var paidAmount = $(this).find(".sibling-student-paid-amount").val();
    var netPayableAmount = $(this).find(".sibling-student-net-payable-amount").val();
    if(parseFloat(paidAmount) > parseFloat(netPayableAmount)) {
      if(!(errorMessage === "")) {
        errorMessage += "<br/>";
      }
      errorMessage += "For " + studentFullName + " paid amount cannot be greater than net payable amount!";
      flag = true;
    }
  });

  if(flag) {
    showErrorDialogBox(errorMessage);
  }

  return flag;
}
