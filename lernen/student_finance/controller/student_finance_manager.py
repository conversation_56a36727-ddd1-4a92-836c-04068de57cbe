import time
import json
import uuid
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *
from core.controller.utils.cache_provider import *
from django.core.cache import cache


restclient = getrestclient()

def get_services(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student-finance/expense-services?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()

def add_student_expence_transaction(user_login_view, institute_unique_code, serviceExpensePayload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    serviceExpensePayload["instituteId"] = institute_id
    response = restclient.post(user_login_view, "/2.0/student-finance/transaction?institute_id="+str(institute_id)+"&user_id="+str(user['uuid']), serviceExpensePayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Expense Added Successfully!!!"}

def load_student_finance_preferences(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    if 'user_preferences' in user_login_view and 'student_finance' in user_login_view['user_preferences'] and institute_id in user_login_view['user_preferences']['student_finance']:
        return user_login_view['user_preferences']['student_finance'][institute_id]

    student_finance_preferences = get_student_finance_preferences(user_login_view, institute_unique_code)
    if 'user_preferences' not in user_login_view:
        user_login_view['user_preferences'] = {'student_finance' : { institute_id : student_finance_preferences}}
    elif 'student_finance' not in user_login_view['user_preferences']:
        user_login_view['user_preferences']['student_finance'] = { institute_id : student_finance_preferences}
    else:
        user_login_view['user_preferences']['student_finance'][institute_id] = student_finance_preferences

    return student_finance_preferences

def get_student_finance_preferences(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response =  restclient.get(user_login_view, "/2.0/user-preferences/student-finance?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()

def get_report(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/student-finance/reports/generate-report/"+str(institute_id)+ "/" +report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&start="+str(report_data['startDate'])+"&end="+str(report_data['endDate'])+"&student_id="+str(report_data['student_id'])+"&download_format="+str(report_data['download_format'])+"&user_id="+user['uuid'])

def get_report_data(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/student-finance/reports/view-report/"+str(institute_id)+ "/" +report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&start="+str(report_data['startDate'])+"&end="+str(report_data['endDate'])+"&student_id="+str(report_data['student_id'])+"&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}