$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerstudyTrackerMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerstudyTrackerMenu:function(){$("#studyTrackerReportNav").on("click",function(){studyTrackerReport.loadReportsMenu()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/study-tracker/home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/study-tracker/session-home/"+e,function(e){$("#study-tracker-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()}},studyTrackerReport={loadReportsMenu:function(){ajaxClient.get("/study-tracker/reports",function(e){$("#main-content").html(e),initDateInput(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),studyTrackerReport.bindGenerateReportEvent(),studyTrackerReport.bindPopupClassSubjectEvent(),reportUtils.bindSelectClassCheckboxEvent(),initSelect2("Select Section ....","study-tracker-section"),$(".select2-search__field").attr("style","width:100%"),initSelect2("Select Course Type ....","course-type");var t=JSON.parse($(".currect-session-json").text());$(".report-academic-session").val(t.academicSessionId)})},bindGenerateReportEvent:function(){$(".report-academic-session").on("change",function(){var e=this,t=$(e).val();ajaxClient.get("/study-tracker/report-session-change/"+t,function(t){$(e).parent().parent().parent().find(".class-study-tracker-div").html(t),initSelect2("Select Section ....","study-tracker-section"),studyTrackerReport.bindPopupClassSubjectEvent()})}),$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(e))){var t=$(e).find(".report-academic-session option:selected").val();t=""===t?0:t;var n,a=$(e).find("p.report-type").text().trim(),r="",o="",i=-1,s=-1;if(n=$(e).find(".study-tracker-student-class").val(),r=$(e).find(".study-tracker-section").val(),o=$(e).find(".course-type").val(),$(e).find("input.study-tracker-start-date").length>0&&""!=$(e).find("input.study-tracker-start-date").val()&&(i=getDate($(e).find("input.study-tracker-start-date").val()).getTime()/1e3),"MONTHLY_AVG_STUDY_TRACKER"==a){$(e).find("input.study-tracker-end-date").length>0&&""!=$(e).find("input.study-tracker-end-date").val()&&(s=getDate($(e).find("input.study-tracker-end-date").val()).getTime()/1e3);var d=(s-i)/86400;if(d<0||d>=31)return void alert("Total number of days cannot be greater than 31. Please reduce the range!")}r=r.join(),o=o.join(),$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/study-tracker/generate-report/"+a+"?academic_session_id="+t+"&standard_id="+n+"&section_ids="+r+"&course_type="+o+"&startDate="+i+"&endDate="+s,"_blank")}})},bindPopupClassSubjectEvent:function(){$(".study-tracker-student-class").change(function(){var e=$(this).find(":selected").val().trim();if(""!=e&&null!=e){$(".study-tracker-section").html("");var t=$(this).closest("div.report-field-container");studyTrackerReport.updatePopupStandardSections(t,e)}else showErrorDialogBox("Please select correct standard.")})},updatePopupStandardSections:function(e,t){for(var n=JSON.parse($(e).find(".standards-json").text()),a={},r=0;r<n.length;r++){a[(i=n[r]).standardId]=i}var o="";if(t in a){var i=a[t];for(r=0;r<i.standardSectionList.length;r++)o+='<option value="'+i.standardSectionList[r].sectionId+'">'+i.standardSectionList[r].sectionName+"</option>";$(".study-tracker-section").html(o)}else $(".study-tracker-section").html(o)},resetPopUp:function(){$(".study-tracker-start-date").val(""),$(".study-tracker-start-end-date").val("")}};