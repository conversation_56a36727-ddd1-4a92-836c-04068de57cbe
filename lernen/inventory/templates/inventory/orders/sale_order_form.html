<div class="container-fluid border rounded card-body">
  <div class="container-fluid">
    <div style="float:right">
      <div class="form-row" id="student-wallet-amount-display-div" style="display:none;">
        <h4><strong><span style="color:green">Available Wallet Amount :</span> </strong> <span id="student-wallet-amount-display" style="color:green"> </span></h4>
      </div>
    </div>
  </div>
  <br>
  <br>
  <div class="form-row">
    {%if is_return_order %}
      <div class="form-group col-md-4">
        <label for="return-supplier">Supplier*</label>
        <select id="return-supplier" class="form-control mandatory-field">
         <option selected></option>
         {% for supplier in suppliers %}
              <option value={{supplier.supplierId}}>{{supplier.supplierName}}</option>
          {% endfor %}
        </select>
      </div>
    {%else%}
      {% if exchange == True %}
        <div class="form-group col-md-3">
          <label for="inputState">Customer Type</label>
          <select id="customer-type" class="form-control" disabled>
            {% if invoice_data.inventoryUserType == "STUDENT" %}
              <option value="STUDENT">Student</option>
            {% else %}
              <option value="OTHER" selected>Other</option>
            {% endif %}
          </select>
        </div>
        {% if invoice_data.inventoryUserType == "STUDENT" %}
          <div class="form-group col-md-3 customer-name-input" id="student-name-input-div" style="display:block;">
            <label for="inputEmail4">Student Name*</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <button class="btn btn-outline-warning" type="button" id="edit-sutdent-entry" disabled>Edit</button>
              </div>
              <input type="text" class="form-control" id="edit-sutdent-entry-name" placeholder="" aria-label="Student Name" aria-describedby="edit-sutdent-entry" value="{{invoice_data.studentLite.name}} - ({{invoice_data.studentLite.admissionNumber}})" readonly>
            </div>
          </div>
        {% else %}
          <div class="form-group col-md-3 customer-name-input" id="customer-name-input-div">
            <label for="inputEmail4">Customer Name*</label>
            <input type="text" class="form-control no-special-chars" id="sale-customer-name" placeholder="Customer Name" value="{{invoice_data.buyerName}}" disabled>
          </div>
        {% endif %}
      {% else %}
        <div class="form-group col-md-3">
          <label for="inputState">Customer Type</label>
          <select id="customer-type" class="form-control">
            <option value="OTHER" selected>Other</option>
            <option value="STUDENT">Student</option>
          </select>
        </div>
        <div class="form-group col-md-3 customer-name-input" id="customer-name-input-div" style="display:block;">
          <label for="inputEmail4">Customer Name*</label>
          <input type="text" class="form-control no-special-chars" id="sale-customer-name" placeholder="Customer Name">
        </div>
        <div class="form-group col-md-3 customer-name-input" id="student-name-input-div" style="display:none;">
          <label for="inputEmail4">Student Name*</label>
          <div class="input-group">
            <div class="input-group-prepend">
              <button class="btn btn-outline-warning" type="button" id="edit-sutdent-entry">Edit</button>
            </div>
            <input type="text" class="form-control" id="edit-sutdent-entry-name" placeholder="" aria-label="Student Name" aria-describedby="edit-sutdent-entry" readonly>
          </div>
            <div style="color: red; font-size: smaller;" id="inventory-sale-tag">
            </div>
        </div>
      {% endif %}
    {%endif%}
    <div class="form-group col-md-3">
      <label for="inputEmail4">Email</label>
      <input type="email" class="form-control validate-email" id="sale-customer-email" placeholder="Customer Email">
    </div>
    <div class="form-group col-md-3">
      <label for="inputPassword4">Transaction Date*</label>
      <input type="text" class="form-control mandatory-field select-date" id="sale-date" placeholder="Enter date ..." value="{{current_date}}">
    </div>
  </div>
  <div class="form-row">
    {% if transaction_type == "SALES_RETURN" %}
      <div class="form-group col-md-3">
        <label>Return Payment Mode*</label>
        <select id="sale-payment-mode" class="form-control mandatory-field">
          <option value="">select</option>
          <option value="WALLET">Embrate Wallet</option>
          <option value="CASH">Cash</option>
          <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
          <option value="CHEQUE"> Cheque</option>
          <option value="CARD">Card</option>
          <option value="QR_PAYMENT">QR Payment</option>
          <option value="GOOGLE_PAY_UPI">GPay UPI</option>
          <option value="PHONE_PE_UPI">PhonePe UPI</option>
          <option value="PAYTM">Paytm</option>
          <option value="BHIM_UPI">BHIM UPI</option>
          <option value="OTHER_UPI">Other UPI</option>
        </select>
      </div>
    {% else %}
      <!-- <div class="form-group col-md-3">
        <label>Payment Mode*</label>
        <select id="sale-payment-mode" class="form-control mandatory-field">
          <option value="">select</option>
          <option value="CASH">Cash</option>
          <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
          <option value="CHEQUE"> Cheque</option>
          <option value="CARD">Card</option>
          <option value="QR_PAYMENT">QR Payment</option>
          <option value="GOOGLE_PAY_UPI">GPay UPI</option>
          <option value="PHONE_PE_UPI">PhonePe UPI</option>
          <option value="PAYTM">Paytm</option>
          <option value="BHIM_UPI">BHIM UPI</option>
          <option value="OTHER_UPI">Other UPI</option>
        </select>
      </div> -->
    {%endif%}
   

    <div class="form-group col-md-3">
        <label>Reference</label>
        <input type="email" class="form-control no-special-chars" id="sale-reference-number" placeholder="Reference">
    </div>
    <div class="form-group col-md-6">
      <label>Description</label>
      <textarea class="form-control" id="sale-description" rows="1" placeholder="Remarks..."></textarea>
    </div>
  </div>
    <br>
    <p> <span style="color:red">*Available quantities are as of today.</span></p>
    <table class="table">
        <thead>
          <tr>
            <th scope="col" class="w-25">Product Name*</th>
            <th scope="col">Batch*</th>
            <th scope="col"></th>
            <th scope="col">Quantity*</th>
            <th scope="col">Price* </th>
            <th scope="col">Amount</th>
            <th scope="col">Discount(%)</th>
            <th scope="col">Tax(%)</th>
            <th scope="col">Total (INR) </th>
            <th scope="col"> </th>
          </tr>
        </thead>
        <tbody>
           <tr id="live-search-row">
            <td > <input type="text" class="form-control" id="live-search-product" placeholder="Type product name to add . . .">
                <div id="live-search-product-result"></div>
            </td>
            <td style="width:15%;">
              <select class="custom-select product-batch-select" disabled>
                <option selected>select...</option>
              </select>
            </td>
            <td></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
            <td><input type="number" class="form-control form-control-sm" placeholder="0" readonly></td>
          </tr>

        </tbody>
        <p id="mandatory-row" style="display: none;"> <span style="color:#e65f76; ">Sell atleast one product</span></p>
    </table>
    <br>
    {%if is_return_order %}
    {%else%}
       <div class="form-check">
         <input type="checkbox" class="form-check-input show-product-group-check" id="sell-product-group-check">
         <label class="form-check-label" for="sell-product-group">Sell Product Groups</label>
       </div>
        <div id = "sell-product-group" class="product-group-transation-area" style="display:none;">
          <p> <span style="color:red">*Available quantities are as of today.</span></p>
          <table class="table">
              <thead>
                <tr>
                  <th scope="col" class="w-25">Product Group*</th>
                  <th scope="col">Quantity*</th>
                  <th scope="col"></th>
                  <th scope="col">Available</th>
                  <th scope="col">Selling Price* </th>
                  <th scope="col">Discounted Price* </th>
                  <th scope="col">Total Amount</th>
                  <th scope="col">Additional Discount(%)</th>
                  <!-- <th scope="col">Tax(%)</th> -->
                  <th scope="col">Net Amount</th>
                  <th scope="col"> </th>
                </tr>
              </thead>
              <tbody>
                 <tr id="sales.search-group-row">
                   <td > <input type="text" class="form-control" id="sales.search-product-group" placeholder="Type Group Name to Add . . .">
                      <div id="sales.search-product-result"></div>
                   </td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Quanity" readonly></td>
                  <td></td>
                  <td><span class="badge badge-secondary">NA</span></td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Price" readonly></td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Net Price" readonly></td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Amount" readonly></td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Amount" readonly></td>
                  <td><input type="number" class="form-control form-control-sm" placeholder="Total" readonly></td>
                </tr>

              </tbody>
              <p id="mandatory-row" style="display: none;"> <span style="color:#e65f76; ">Sell atleast one product group</span></p>
          </table>
        </div>

        <br>
  {%endif%}


    <div class="row">
        <div class="col-md-6">
          <form>
            <div class="form-group row">
              <label for="staticEmail" class="col-sm-5 col-form-label"><h6><strong>Additional Cost(Rs)</strong></h6></label>
              <div class="col-sm-3">
                <input type="number" class="form-control " id="additional-cost-input" value="0">
              </div>
            </div>
            <div class="form-group row">
              <label for="inputPassword" class="col-sm-5 col-form-label"><h6><strong>Additional Discount(Rs)</strong></h6></label>
              <div class="col-sm-3">
                <input type="number" class="form-control" id="additional-discount-input"value="0">
              </div>
            </div>
          </form>
        </div>
        <div class="col-md-6">
          <table class="table table-sm">
              <tbody>
                  <tr>
                      <th scope="row" >Sub Total</th>
                      <td></td>
                      <td id="sub-total">0</td>
                  </tr>
                  <tr>
                      <th scope="row">Total Discount</th>
                      <td></td>
                      <td id="total-discount">0</td>
                  </tr>
                  <tr>
                      <th scope="row">Total Tax</th>
                      <td></td>
                      <td id="total-tax">0</td>
                  </tr>
                  <tr>
                      <th scope="row">Additional Cost</th>
                      <td></td>
                      <td id="additional-cost">0</td>
                  </tr>
                  <tr>
                      <th scope="row">Additional Discount</th>
                      <td></td>
                      <td id="additional-discount">0</td>
                  </tr>
                  {% if exchange == True %}
                    <tr>
                      <th scope="row">Net Refundable Amount</th>
                      <td></td>
                      <td id="net-returned-product-amount">0</td>
                    </tr>
                  {% endif %}
                  <tr>
                      <th scope="row" style="font-size: medium;">Net Amount Payble</th>
                      <td></td>
                      <td id="net-total" style="font-size: medium;">0</td>
                  </tr>
              </tbody>
          </table>
        </div>
      </div>
    <br>
    <div style="display: flex;  align-items: center;  justify-content: center;">
      {%if transaction_type == "RETURN" %}
          <button style="width:20%;" type="submit" class="btn btn-primary" data-toggle="modal"  data-target="#new-sale-confirm-modal" onclick="inventorySale.updateConfirmModal()">Purchase Return</button>
      {%elif transaction_type == "SALES_RETURN" %}
          <button style="width:20%;" type="submit" class="btn btn-primary" data-toggle="modal"  data-target="#new-sale-confirm-modal" onclick="inventorySale.updateConfirmModal()">Sale Return</button>
      {%else%}
          {% if exchange == True%}
           <button style="width:20%;" type="submit" class="btn btn-primary" data-toggle="modal"  data-target="#new-sale-confirm-modal" onclick="inventorySale.updateConfirmModal()">Exchange Products</button>

            <!-- <button style="width:20%;" type="submit" class="btn btn-primary" data-toggle="modal"  data-target="#new-sale-confirm-modal" onclick="inventoryExchangeProduct.addExchangeTransaction()">Exchange Products</button> -->
          {% else %}
            <button style="width:20%;" type="submit" class="btn btn-primary" data-toggle="modal"  data-target="#new-sale-confirm-modal" onclick="inventorySale.updateConfirmModal()">Sell</button>
          {% endif %}
          
      {%endif%}
  </div>
</div>
