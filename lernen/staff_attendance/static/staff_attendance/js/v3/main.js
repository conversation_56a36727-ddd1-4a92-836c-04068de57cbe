$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    homePage.initHomePage();
    paymentReminder.readPaymentState();
});

var menuLoader = {

  registerSidebarMenu : function () {
    sideBarHoverEventCallback();
    activateMenuItem();
    menuLoader.registerHomeMenu();
    menuLoader.registerMarkAttendanceMenu();
    menuLoader.registerAttendanceReportsMenu();
  },

  registerHomeMenu : function () {
      $('#homeNav').on('click', function () {
          homePage.loadHomePage();
      });
  },

  registerMarkAttendanceMenu : function () {
      $('#markAttendanceNav').on('click', function() {
          attendanceRegister.loadMainScreen(true);
      });
  },

  registerAttendanceReportsMenu : function () {
      $('#attendanceReportNav').on('click', function() {
          attendanceReports.loadReportsMenu();
      });
  }

};

 function parseTimeToMinutes(timeStr) {
   if (!timeStr || !timeStr.includes(":")) return null;
   const [hours, minutes] = timeStr.split(":").map(Number);
   return hours * 60 + minutes;
 }

function showError(input) {
  $(input).css("border", "1px solid red");
}

function clearError(input) {
  $(input).css("border", "");
}

var homePage =  {
    initHomePage : function () {
        homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/staff-attendance/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

   displayDashboardContent : function () {

   },
};

var attendanceRegister = {
  dataCache : {},

  loadMainScreen : function () {
    ajaxClient.get("/staff-attendance/register/main-screen", function(data) {
        $("#main-content").html(data);
        attendanceRegister.initPage();
    });
  },

  initPage : function () {
    initDate(365);
    initDateInput();
  },

  loadAttendanceSheet : function () {
    var date =  getDate($('#attendance-date').val());
    if(date == null){
      showErrorDialogBox("please select date for attendance");
      return;
    }
    attendanceRegister.dataCache["selectedDate"] = date;

    ajaxClient.get("/staff-attendance/register/get-sheet/"+date.getTime()/1000, function(data) {
        $("#staff-attendance-feed-container").html(data);
        $('#datatables-reponsive').DataTable( {
          "paging":   false,
          searching: false,
          columnDefs: [
            { orderable: false, targets: "no-sort" },
          ],
          order: [[3, 'asc']]
        });
        $('.clockpicker').clockpicker({
          autoclose: true,
          'default': 'now'
        });
        attendanceRegister.onClickDisableClockpicker();
        var staffAttendanceRegister = readJson("#attendance-register");
        var staffAttendanceRegisterMap = {};
        if(staffAttendanceRegister != null && staffAttendanceRegister.length > 0){
          for(var i = 0; i < staffAttendanceRegister.length; i++){
            var entry = staffAttendanceRegister[i];
            var staffId = entry.staff.staffId;
            staffAttendanceRegisterMap[staffId] = entry;
          }
        }
        attendanceRegister.dataCache['staffAttendanceRegisterMap'] = staffAttendanceRegisterMap;
        attendanceRegister.bindFillDefaultStaffTime();
        attendanceRegister.registerOnChangeTime();
        attendanceRegister.overrideTab();
    });
  },

  onClickDisableClockpicker : function () {
    $('.disable-clockpicker-click').on('click', function () {
      if($("#authorised_to_edit_staff_attendance_time").text().toLowerCase() === "true") {
        // $(".clockpicker-popover").show();
      } else {
        $(".clockpicker-popover").hide();
      }
      var dt = new Date();
      var time = dt.getHours() + ":" + dt.getMinutes();
      $(this).parent().find('.clockpicker-input').val(time);
    });
  },

  bindFillDefaultStaffTime: function() {
    $("#fill-default-staff-in-time").change(function() {
        if (this.checked) {
            var staffAttendanceRegisterMap = attendanceRegister.dataCache['staffAttendanceRegisterMap'];
            attendanceRegister.fillTimeForStaffRows("IN", staffAttendanceRegisterMap);
        } else {
          attendanceRegister.resetTimeForStaffRows("IN");
        }
    });

    $("#fill-default-staff-out-time").change(function() {
        if (this.checked) {
            var staffAttendanceRegisterMap = attendanceRegister.dataCache['staffAttendanceRegisterMap'];
            attendanceRegister.fillTimeForStaffRows("OUT", staffAttendanceRegisterMap);
        } else {
          attendanceRegister.resetTimeForStaffRows("OUT");
        }
    });
  },

  resetTimeForStaffRows : function(attendanceType) {
      $("tr.staff-attendance-row").each(function() {
          $(this).find('.clockpicker-input').each(function() {
              if (attendanceType == "IN" && $(this).hasClass('in-time')) {
                  $(this).val("");
              }

              if (attendanceType == "OUT" && $(this).hasClass('out-time')) {
                  $(this).val("");
              }
          });
      });
  },

  fillTimeForStaffRows : function(attendanceType, staffAttendanceRegisterMap) {
     $("tr.staff-attendance-row").each(function() {
            var staffId = $(this).attr("id");
            if (!(staffId in staffAttendanceRegisterMap)) {
                return;
            }
            var staffEntry = staffAttendanceRegisterMap[staffId];

            $(this).find('.clockpicker-input').each(function() {

                if (attendanceType == "IN" && $(this).hasClass('in-time')) {
                    var inTime = staffEntry.staff.staffTimingDetails.inTime;
                    if (inTime == null) {
                        $(this).val("");
                    } else {
                        var time = attendanceRegister.formatDuration(inTime);
                        $(this).val(time);
                    }
                }
                if (attendanceType == "OUT" && $(this).hasClass('out-time')) {
                    var outTime = staffEntry.staff.staffTimingDetails.outTime;
                    if (outTime == null) {
                        $(this).val("");
                    } else {
                        var time = attendanceRegister.formatDuration(outTime);
                        $(this).val(time);
                    }
                }
            });
        });
  },

  formatDuration : function(timeObj) {
    return timeObj.hour.toString().padStart(2, '0') + ":" + timeObj.minute.toString().padStart(2, '0');
  },

  submitAttendance: function () {
      var staffAttendanceInputList = [];
      var date = attendanceRegister.dataCache["selectedDate"];
      var dataIncorrect = false;

      $("tr.staff-attendance-row").each(function () {
        var row = $(this);
        var staffId = row.attr("id");

        var timePairs = [];
        var inEntries = row.find(".in-times .time-pair-entry");
        var outEntries = row.find(".out-times .time-pair-entry");

        inEntries.each(function (index) {
          var inInput = $(this).find("input");
          var outInput = outEntries.eq(index).find("input");

          var inVal = inInput.val();
          var outVal = outInput.val();
          if(!inVal && outVal) {

          }
          if (!inVal && !outVal) return;

          if (attendanceRegister.validateOutTime(outInput) || attendanceRegister.validateInTime(inInput)) {
          dataIncorrect = true;
              return;
          }


          var timeObj = {};

          if (inVal) {
            var inParts = inVal.split(":");
            timeObj.inTime = {
              hour: inParts[0],
              minute: inParts[1],
              second: '00'
            };
          }

          if (outVal) {
            var outParts = outVal.split(":");
            timeObj.outTime = {
              hour: outParts[0],
              minute: outParts[1],
              second: '00'
            };
          }

          timePairs.push(timeObj);
        });

        if (timePairs.length > 0) {
          staffAttendanceInputList.push({
            staffId: staffId,
            staffTimeDurationList: timePairs
          });
        }
      });

      var payload = {
        attendanceDate: date.getTime() / 1000,
        staffAttendanceInputList: staffAttendanceInputList
      };

      if(dataIncorrect){
              return ;
      }
      ajaxClient.post("/staff-attendance/register/submit-attendance", { staffAttendancePayload: JSON.stringify(payload) }, function (data) {
        $("#attendance-feed-status-modal-container").html(data);
        $("#attendance-default-status-modal").modal({ backdrop: 'static', keyboard: false });
        attendanceRegister.loadAttendanceSheet();
      });
    },



  addAttendanceRow: function (ref) {
    const row = $(ref).closest('tr');
    const inContainer = row.find('.in-times');
    const outContainer = row.find('.out-times');

    const lastInEntry = inContainer.find('.time-pair-entry').last();
    const lastOutEntry = outContainer.find('.time-pair-entry').last();

    const inInput = lastInEntry.find('input');
    const outInput = lastOutEntry.find('input');

    if (!inInput.val() || !outInput.val()) {
      if (!inInput.val()) showError(inInput);
      if (!outInput.val()) showError(outInput);
      showErrorDialogBox('Please fill this attendance row in order to add new attendance row')
      return;
    }
    if (attendanceRegister.validateOutTime(outInput) || attendanceRegister.validateInTime(inInput)) {
       return;
    }

    const inTemplate = lastInEntry.clone();
    const outTemplate = lastOutEntry.clone();

    inTemplate.find('input').val('');
    outTemplate.find('input').val('');

    inTemplate.find('.add-time-btn').removeClass('add-time-btn btn-success').addClass('remove-time-btn')  .attr('onclick', 'attendanceRegister.deleteAttendanceRow(this)').html('<i class="bi bi-x"></i>');

    outTemplate.find('.add-time-btn').removeClass('add-time-btn btn-success') .addClass('remove-time-btn').attr('onclick', 'attendanceRegister.deleteAttendanceRow(this)').html('<i class="bi bi-x"></i>');

    inContainer.append(inTemplate);
    outContainer.append(outTemplate);


    $('.clockpicker').clockpicker({
          autoclose: true,
          'default': 'now'
    });
    attendanceRegister.registerOnChangeTime();
  },

  deleteAttendanceRow: function (ref) {
    const row = $(ref).closest('tr');
    const inContainer = row.find('.in-times');
    const outContainer = row.find('.out-times');
    const index = inContainer.find('.remove-time-btn').index(ref);
    inContainer.find('.time-pair-entry').eq(index).remove();
    outContainer.find('.time-pair-entry').eq(index).remove();
  },

 registerOnChangeTime : function(){
  $('.in-time').on('change', function () {
    attendanceRegister.validateInTime(this);
  });

  $('.out-time').on('change', function () {
    attendanceRegister.validateOutTime(this);
  });
  },

  validateOutTime: function (outTimeInput) {
      const outTime = $(outTimeInput);
      const pairEntry = outTime.closest('.time-pair-entry');
      const row = outTime.closest('tr');
      const inTime = row.find('.in-times .time-pair-entry').eq(pairEntry.index()).find('.in-time');

      const inTimeVal = inTime.val();
      const outTimeVal = outTime.val();
      if (outTimeVal && !inTimeVal) {
          showError(outTime);
          showError(inTime);
          showErrorDialogBox("In time cannot be empty if out time is filled. Note: it is a 24-hour clock system, please fill accordingly (e.g., 2:00 PM should be written as 14:00).");
          return true;
      }

      const inTimeMin = parseTimeToMinutes(inTimeVal);
      const outTimeMin = parseTimeToMinutes(outTimeVal);

      if (inTimeMin === null || outTimeMin === null) return;

      if (outTimeMin <= inTimeMin) {
        showError(outTime);
               showError(inTime);
               showErrorDialogBox("In time should not be greater or equal to the out time. Note: it is 24 hours clock system, please fill accordingly eg: 02:00 pm should be written as 14:00");
               return true;
      }
      clearError(outTime);
      clearError(inTime)

      return false;
    },

   validateInTime: function (inTimeInput) {
       const inTime = $(inTimeInput);
       const pairEntry = inTime.closest('.time-pair-entry');
       const index = pairEntry.index();

       const row = inTime.closest('tr');
       const prevOutTime = row.find('.out-times .time-pair-entry').eq(index - 1).find('.out-time');
       const currOutTime = row.find('.out-times .time-pair-entry').eq(index).find('.out-time');

       const prevOutTimeVal = prevOutTime.val();
       const currOutTimeVal = currOutTime.val();
       const inTimeVal = inTime.val();

       const inTimeMin = parseTimeToMinutes(inTimeVal);
       const prevOutMin = parseTimeToMinutes(prevOutTimeVal);
       const currOutMin = parseTimeToMinutes(currOutTimeVal);

       if (inTimeVal && inTimeMin !== null) {
           if (index > 0 && prevOutMin !== null && inTimeMin < prevOutMin) {
               showError(inTime);
               showError(prevOutTime);
               showErrorDialogBox("Current in time should not be earlier than previous out time. Note: it is a 24-hour clock system, e.g., 02:00 PM should be written as 14:00.");
               return true;
           }

           if (currOutTimeVal && currOutMin !== null && inTimeMin >= currOutMin) {
               showError(inTime);
               showError(currOutTime);
               showErrorDialogBox("In time should be earlier than its corresponding out time. Note: it is a 24-hour clock system, e.g., 02:00 PM should be written as 14:00.");
               return true;
           }

           clearError(inTime);
           clearError(prevOutTime);
           clearError(currOutTime);
       }

       return false;
   },

    updateStaffAttendanceStatus: function(buttonRef) {

        $("#staff-update-attendance-status-modal").modal('show');
        var row = $(buttonRef).closest('tr');
        var staffId = row.attr('id');
        var td = $(buttonRef).closest('td');
        var attendanceDetailsJson =row.find('td.attendance_register_entry').text();
        var attendanceDetails = JSON.parse(attendanceDetailsJson);
        $('#updated-staff-id').text(staffId);

        var status = ''
        var remarks = ''
        if (attendanceDetails && attendanceDetails.staffAttendanceDaySummaryList && attendanceDetails.staffAttendanceDaySummaryList.length > 0) {
            var staffAttendanceDaySummary = attendanceDetails.staffAttendanceDaySummaryList[0];

            status = staffAttendanceDaySummary .staffAttendanceStatus;
            remarks = staffAttendanceDaySummary.remarks;

        }

        $('#attendance-remarks').val(remarks);
        $('#staff-attendance-status').val(status).trigger('change');

    },

    updateStaffAttendance : function(){
         var remarks = $("#attendance-remarks").val();
         var staffId = $('#updated-staff-id').text();
         var status = $('#staff-attendance-status').val();
         var date = attendanceRegister.dataCache["selectedDate"];
         if(status == '' || status == null) {
               $("#staff-update-attendance-status-modal").modal('toggle');
               showErrorDialogBox("Please fill staff attendance status in order to update")
         }
         var staffAttendanceStatusInputs = [{
              staffId: staffId,
              staffAttendanceStatus: status,
              remarks: remarks
         }];

         var staffMarkAttendancePayload = {
             attendanceDate: date.getTime() / 1000,
             staffAttendanceStatusInputs: staffAttendanceStatusInputs
         };
         $("#staff-update-attendance-status-modal").modal('toggle');
         ajaxClient.post("/staff-attendance/register/update-staff-attendance-status", { staffMarkAttendancePayload: JSON.stringify(staffMarkAttendancePayload) }, function (data) {
            $("#attendance-feed-status-modal-container").html(data);
            $("#attendance-staff-status-modal").modal({backdrop: 'static', keyboard: false});
            attendanceRegister.loadAttendanceSheet();
           }
         );
    },

    overrideTab : function () {
           $(".staff-attendance-row").on('keydown', function (e) {
               if (e.key === 'Tab') {
                   const customTabOrder = [];
                   $('tr.staff-attendance-row').each(function () {
                       const inEntries = $(this).find('.in-times .time-pair-entry');
                       const outEntries = $(this).find('.out-times .time-pair-entry');
                       const maxLength = Math.max(inEntries.length, outEntries.length);
                       for (let i = 0; i < maxLength; i++) {
                           const inInput = inEntries.eq(i).find('.in-time');
                           const outInput = outEntries.eq(i).find('.out-time');

                           if (inInput.length) customTabOrder.push(inInput[0]);
                           if (outInput.length) customTabOrder.push(outInput[0]);
                       }
                   });

                   const active = document.activeElement;
                   const index = customTabOrder.indexOf(active);

                   if (index !== -1) {
                       e.preventDefault();
                       const nextIndex = e.shiftKey ? index - 1 : index + 1;
                       if (nextIndex >= 0 && nextIndex < customTabOrder.length) {
                           $(customTabOrder[nextIndex]).focus();
                       }
                   }
               }
           });
       },
};

var attendanceReports = {

  loadReportsMenu: function (){
      ajaxClient.get("/staff-attendance/reports", function(data) {
          $("#main-content").html(data);
          initSelect2("All");
          initDateInput();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          attendanceReports.bindGenerateReportEvent();
          initSelect2();
      });
  },

  bindGenerateReportEvent : function () {

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }
        var attendanceReportType = $(containerElement).find('p.report-type').text().trim();
        var attendanceStartDate = -1;
        var attendanceEndDate = -1;
        var attendanceStatus = "";

        attendanceStatus = $(containerElement).find('#staff-attendance-status').val();
        if(attendanceStatus === undefined) {
          attendanceStatus = "";
        }

        if($(containerElement).find('input.staff-attendance-start-date').length > 0){
          if($(containerElement).find('input.staff-attendance-start-date').val() != "") {
              attendanceStartDate = getDate($(containerElement).find('input.staff-attendance-start-date').val()).getTime()/1000;
          }
        }

        if($(containerElement).find('input.staff-attendance-end-date').length > 0){
          if($(containerElement).find('input.staff-attendance-end-date').val() != "") {
            attendanceEndDate = getDate($(containerElement).find('input.staff-attendance-end-date').val()).getTime()/1000;
          }
        }

        if (attendanceEndDate != -1 && attendanceStartDate != -1 && attendanceEndDate < attendanceStartDate) {
          showErrorDialogBox("End date cannot be less than start date");
          return;
        }

        var staffStatusStr = "";
        if($(containerElement).find("select.filter-staff-status").length > 0){
          staffStatusStr = $(containerElement).find("select.filter-staff-status").val().join();
        }

        var staffCategory = "";
        if(attendanceReportType === "STAFF_MONTHLY_ATTENDANCE_SUMMARY" || attendanceReportType === "STAFF_ATTENDANCE_DETAILS") {
          if($(containerElement).find("select.filter-staff-category").length > 0){
            staffCategory = $(containerElement).find("select.filter-staff-category").val().join();
          }

          var difference = attendanceEndDate - attendanceStartDate;
          // To calculate the no. of days between two dates
          var differenceInDays = difference / (3600 * 24);
          if(differenceInDays < 0 || differenceInDays >= 31) {
            alert("Total number of days cannot be greater than 31. Please reduce the range!");
            return;
          }
        }

        $(this).closest('div.modal').modal('toggle');

        window.open(baseURL+"/staff-attendance/generate-report/"+attendanceReportType
        + "?attendance_status="+ attendanceStatus
        +"&startDate="+attendanceStartDate
        +"&endDate="+attendanceEndDate+"&staffCategory="+staffCategory+"&staffStatusStr="+staffStatusStr, '_blank');
    });
  },

  resetPopUp : function () {
    // $("#staff-attendance-status").selectpicker('deselectAll');
    $(".staff-attendance-start-date").val("");
    $(".staff-attendance-end-date").val("");
  },

};
