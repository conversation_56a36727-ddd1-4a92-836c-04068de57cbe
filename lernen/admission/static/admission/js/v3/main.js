var inputDatePickerFormat = "DD-MMM-YYYY";
var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;
var ALL_ENROLLED_STUDENT_SCREEN_ID = "all-enrolled-student-screen";
var NSO_STUDENTS_LIST_SCREEN_ID = "nso-student-list-screen";
var NEW_ADMISSION_MALE_COLOR = "#93c47d";
var NEW_ADMISSION_FEMALE_COLOR = "#cbe6c0";
var NEW_ADMISSION_TRANSGENDER_COLOR = "#E69138";
var LOGO_COLOR = "#43a2ad";
var OLD_STUDENTS_FEMALE_COLOR = "#bfe9ef";
var LIGHT_GREY_COLOR = "#F3F3F3";
var STUDENT_DOCUMENT_TYPE = ["TRANSFER_CERTIFICATE", "AADHAR_CARD", "STUDENT_PROFILE_IMAGE", "FATHER_PROFILE_IMAGE", "MOTHER_PROFILE_IMAGE", "RATION_CARD", "PREVIOUS_SCHOOL_STUDY_CERTIFICATE", "BIRTH_CERTIFICATE", "STUDENT_PAN_CARD", "BIRTH_CERTIFICATE_AND_AFFIDAVIT"];

$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    var urlParams = new URLSearchParams(window.location.search);
    actionType = urlParams.get(ACTION_TYPE);

    if(actionType != null && actionType != undefined && actionType != "") {
      if (actionType === "admit-student"){
        $("#admitStudentNav").parent().addClass("active");
        loadAdmitStudentMenu();
      }

      //remove params from url
      var url_string = window.location.href;
      url = removeURLParameter(url_string, ACTION_TYPE)
      let stateObj = { id: "100" };
      window.history.replaceState(stateObj, url_string, url);
    }

    paymentReminder.readPaymentState();
});

var menuLoader = {

  registerSidebarMenu : function () {
    sideBarHoverEventCallback();
    activateMenuItem();
    menuLoader.registerHomeMenu();
    menuLoader.registerAdmitStudentMenu();
    menuLoader.registerPendingEnrolmentsMenu();
    menuLoader.registerAllStudentMenu();
    menuLoader.registerStudentDetailsMenu();
    menuLoader.registerNSOStudentDetailsMenu();
    menuLoader.registerRelievedStudentDetailsMenu();
    menuLoader.registerSiblingDetailsNav();
    menuLoader.registerStudentRegistrationDetailsMenu();
    menuLoader.registerReportsDetailsMenu();
    menuLoader.registerIdentityCardsMenu();
    menuLoader.registerAdmissionEnquiryMenu();
  },

  registerHomeMenu : function () {
    $('#statisticsNav').on('click', function() {
        statistics.loadHomePage();
    });
  },

  registerAdmitStudentMenu : function () {
    $('#admitStudentNav').on('click', function() {
        loadAdmitStudentMenu();
    });
  },

  registerPendingEnrolmentsMenu : function () {
      $('#pendingEnrolmentsNav').on('click', function() {
          loadPendingEnrolmentsPage();
      });
  },

  registerStudentDetailsMenu : function () {
    $('#studentDetailsNav').on('click', function() {
        loadStudentDetailsPage();
    });
  },

  registerAllStudentMenu : function () {
    $('#relieveStudentNav').on('click', function() {
        loadAllStudentsPage();
    });
  },

  registerRelievedStudentDetailsMenu : function () {
    $('#relievedStudentDetailsNav').on('click', function() {
        loadRelievedStudentDetailsPage();
    });
  },

  registerNSOStudentDetailsMenu : function () {
    $('#nsoStudentDetailsNav').on('click', function() {
        loadNSOStudentDetailsPage();
    });
  },

  registerStudentRegistrationDetailsMenu : function () {
    $('#registrationStudentsDetailsNav').on('click', function() {
        studentRegistration.loadHomePage();
    });
  },

  registerSiblingDetailsNav : function () {
    $('#siblingDetailsNav').on('click', function() {
        siblingDetails.loadHomePage();
    });
  },

  registerReportsDetailsMenu : function () {
    $('#reportsNav').on('click', function() {
        admissionReports.loadMainScreen();
    });
  },

  registerIdentityCardsMenu : function () {
    $('#identityCardsNav').on('click', function() {
        identityCards.loadMainScreen();
    });
  },

  registerAdmissionEnquiryMenu : function () {
    $('#admissionEnquiryDetailsNav').on('click', function() {
        admissionEnquiry.loadMainScreen();
    });
  }
};


var statistics =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(statistics.loadHomePageForSession);
        statistics.displayDashboardContent();
        statistics.onClickAdmissionStatsViewSwitchButton();
    },

    loadHomePage : function () {
      ajaxClient.get("/admission/home", function(data) {
          $("#main-content").html(data);
          statistics.initHomePage();
      });
    },

    loadStudentList : function(inputAdmissionStatus) {
      var admissionStats = readJson("#admission-home-stats");
      if(admissionStats == null){
        return;
      }

      var tabularViewHtml = "<table id=\"datatables-reponsive\" class=\"table table-bordered table-striped datatables-reponsive-table\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Sr No.</th><th scope=\"col\">Admission Date</th><th scope=\"col\">Admission Number</th><th scope=\"col\">Student Name</th><th scope=\"col\">Father Name</th><th scope=\"col\">Class</th><th scope=\"col\">Gender</th><th scope=\"col\">DOB</th></tr></thead><tbody>";
      
      var studentDetails;
      if(inputAdmissionStatus == "NEW_ADMISSION"){
        studentDetails = admissionStats.newAdmissionStudentList;
      }
      else if(inputAdmissionStatus == "RTE_STUDENT"){
        studentDetails = admissionStats.rteStudentList;
      }

      var srNo = 1;
      for( var i = 0 ; i < studentDetails.length ; i++){
        var date = studentDetails[i].studentBasicInfo.admissionDate;
        var admissionDate = "";
        if(date != null && date > 0){
          admissionDate = getFormattedDate(date);
        }
        var admissionNumber = studentDetails[i].studentBasicInfo.admissionNumber;
        var studentName = studentDetails[i].studentBasicInfo.name;
        var fatherName = "";
        if(studentDetails[i].studentFamilyInfo.fathersName != null){
          fatherName = studentDetails[i].studentFamilyInfo.fathersName
        }
        var standard = studentDetails[i].studentAcademicSessionInfoResponse.standard.displayNameWithSection;
        var gender = studentDetails[i].studentBasicInfo.gender;
        var dob = "";
        if(studentDetails[i].studentBasicInfo.dateOfBirth != null){
          var dobDate = studentDetails[i].studentBasicInfo.dateOfBirth;
          var dob = "";
          if(dobDate != null && dobDate > 0){
            dob = getFormattedDate(dobDate);
          }
        }
        
        tabularViewHtml += "<tr><th scope=\"row\">" + srNo++ + "</th><td>" + admissionDate + "</td><td>" + admissionNumber +"</td><td>" + studentName + "</td> <td>" + fatherName + "</td> <td>" + standard + "</td> <td>" + gender + "</td> <td>" + dob + "</td></tr>";

      }

      tabularViewHtml += "</tbody></table>";
      $("#admission-student-data-display").html(tabularViewHtml);
      $('#datatables-reponsive').DataTable({
        searching: true,
        bPaginate: true,
        columnDefs: [
            { orderable: false, targets: "no-sort" },
        ],
        order: [[0, 'asc']]
    });
      $("#display-admission-student-data-modal").modal({backdrop: 'static', keyboard: false});
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/admission/session-home/"+academicSessionId, function(data) {
          $("#admission-dashboard-session-content").html(data);
          var currentCardViewType = $("#admission-stats-view-type").val();
          if(currentCardViewType === TABULAR) {
            $("#current-card-view-type").text(TABULAR);
            statistics.displayTabularDashboardContent();
          } else {
            statistics.displayDashboardContent();
            $("#current-card-view-type").text(GRAPHICAL);
          }
          statistics.onClickAdmissionStatsViewSwitchButton();
      });
    },

    refreshHomePage : function () {
        statistics.loadHomePageForSession();
    },

    onClickAdmissionStatsViewSwitchButton : function () {
      $("#admission-stats-view-type").on('change', function () {
        var currentCardViewType = $("#admission-stats-view-type").val();
        if(currentCardViewType === TABULAR) {
          $("#current-card-view-type").text(TABULAR);
          statistics.displayTabularDashboardContent();
        } else {
          statistics.displayDashboardContent();
          $("#current-card-view-type").text(GRAPHICAL);
        }
      });
    },

    displayTabularDashboardContent : function () {
      var admission_home_stats_json = readJson("#admission-home-stats");
      var standards = readJson("#home-page-standards-stats");

      admission_home_stats = statistics.initAdmissionHomeStats(admission_home_stats_json, standards)

      statistics.renderTopBoxesData(admission_home_stats);
      statistics.renderStudentGenderCountTable(admission_home_stats);
      statistics.renderRTECountTable(admission_home_stats);
      statistics.renderNewAdmissionCountTable(admission_home_stats);
      statistics.renderCategoryCountTable(admission_home_stats);

    },

    initAdmissionHomeStats : function (admission_home_stats, standards) {
      var classNameList = [];
      for( var i = 0 ; i < standards.length; i++){
        classNameList.push(standards[i].displayName);
      }

      var totalStudents = admission_home_stats.totalStudents;
      var oldStudents = admission_home_stats.oldStudents;
      var newRegistrationStudents = admission_home_stats.newRegistrationStudents;
      var newAdmissionStudents = admission_home_stats.newAdmissionStudents;
      var totalLastYearStudents = admission_home_stats.totalLastYearStudents;
      var oldLastYearStudents = admission_home_stats.oldLastYearStudents;
      var newRegistrationLastYearStudents = admission_home_stats.newRegistrationLastYearStudents;
      var newAdmissionLastYearStudents = admission_home_stats.newAdmissionLastYearStudents;

      var classWiseOldStudentGenderCountMapObj = admission_home_stats.classWiseOldStudentGenderCountMap;
      var classWiseNewAdmissionGenderCountMapObj = admission_home_stats.classWiseNewAdmissionGenderCountMap;
      var classWiseNewAdmissionCountMapObj = admission_home_stats.classWiseNewAdmissionCountMap;
      var classWiseLastYearNewAdmissionCountMapObj = admission_home_stats.classWiseLastYearNewAdmissionCountMap;
      var classWiseRteCountMapObj = admission_home_stats.classWiseRteCountMap;
      var classWiseStudentCountMapObj = admission_home_stats.classWiseStudentCountMap;
      var classWiseCategoryCountMapObj = admission_home_stats.classWiseCategoryCountMap;

      let classWiseOldStudentGenderCountMap = new Map();
      let classWiseNewAdmissionGenderCountMap = new Map();
      let classWiseNewAdmissionCountMap = new Map();
      let classWiseLastYearNewAdmissionCountMap = new Map();
      let classWiseRteCountMap = new Map();
      let classWiseStudentCountMap = new Map();
      let classWiseCategoryCountMap = new Map();

      for(var i = 0; i < classNameList.length; i++) {
        var className = classNameList[i];
        if(!isObjectEmpty(classWiseOldStudentGenderCountMapObj)) {
            classWiseOldStudentGenderCountMap.set(className,classWiseOldStudentGenderCountMapObj[className]);
        }

        if(!isObjectEmpty(classWiseNewAdmissionGenderCountMapObj)) {
            classWiseNewAdmissionGenderCountMap.set(className, classWiseNewAdmissionGenderCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseNewAdmissionCountMapObj)) {
            classWiseNewAdmissionCountMap.set(className, classWiseNewAdmissionCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseLastYearNewAdmissionCountMapObj)) {
            classWiseLastYearNewAdmissionCountMap.set(className, classWiseLastYearNewAdmissionCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseRteCountMapObj)) {
            classWiseRteCountMap.set(className, classWiseRteCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseStudentCountMapObj)) {
            classWiseStudentCountMap.set(className, classWiseStudentCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseCategoryCountMapObj)) {
            classWiseCategoryCountMap.set(className, classWiseCategoryCountMapObj[className]);
        }
      }

      admission_home_stats = {'totalStudents' : totalStudents, 'oldStudents' : oldStudents, 'newRegistrationStudents' : newRegistrationStudents, 'newAdmissionStudents' : newAdmissionStudents, 'totalLastYearStudents' : totalLastYearStudents, 'oldLastYearStudents' : oldLastYearStudents, 'newRegistrationLastYearStudents' : newRegistrationLastYearStudents, 'newAdmissionLastYearStudents' : newAdmissionLastYearStudents, 'classWiseOldStudentGenderCountMap' : classWiseOldStudentGenderCountMap, 'classWiseNewAdmissionGenderCountMap' : classWiseNewAdmissionGenderCountMap, 'classWiseNewAdmissionCountMap' : classWiseNewAdmissionCountMap, 'classWiseLastYearNewAdmissionCountMap' : classWiseLastYearNewAdmissionCountMap, 'classWiseRteCountMap' : classWiseRteCountMap, 'classWiseStudentCountMap' : classWiseStudentCountMap, 'classWiseCategoryCountMap' : classWiseCategoryCountMap};

      return admission_home_stats;
    },

    renderCategoryCountTable : function (admission_home_stats) {
      $("#categorywise-graph-header-text").text("Categorywise Student Distribution (Tabular View)");
      $("#chartjs-categorywise-student-bar-distribution").attr("style", "display:none;");
      $("#tabularview-categorywise-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseCategoryCountMap = admission_home_stats.classWiseCategoryCountMap;
      var totalStudents = admission_home_stats.totalStudents;

      var length = classWiseCategoryCountMap.size;

      var classNameList = [];
      var bcStudentCountList = [];
      var genralStudentCountList = [];
      var minStudentCountList = [];
      var obcStudentCountList = [];
      var otherStudentCountList = [];
      var sbcStudentCountList = [];
      var scStudentCountList = [];
      var stStudentCountList = [];
      var classTotalList = [];
      for (let [className, categoryCountMap] of  classWiseCategoryCountMap.entries()) {
        classNameList.push(className);
        var classTotal = 0;
        $.each(categoryCountMap, function(category, count) {
          if(category === "BC") {
            bcStudentCountList.push(count);
          } else if(category === "GENERAL") {
            genralStudentCountList.push(count);
          } else if(category === "MIN") {
            minStudentCountList.push(count);
          } else if(category === "OBC") {
            obcStudentCountList.push(count);
          } else if(category === "OTHER") {
            otherStudentCountList.push(count);
          } else if(category === "SBC") {
            sbcStudentCountList.push(count);
          } else if(category === "SC") {
            scStudentCountList.push(count);
          } else if(category === "ST") {
            stStudentCountList.push(count);
          }
          classTotal += count;
        });
        classTotalList.push(classTotal);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">BC</th><th scope=\"col\">General</th><th scope=\"col\">MIN</th><th scope=\"col\">OBC</th><th scope=\"col\">OTHER</th><th scope=\"col\">SBC</th><th scope=\"col\">SC</th><th scope=\"col\">ST</th><th scope=\"col\">Total</th></tr></thead><tbody>";
      var grandBcStudentCountList = 0;
      var grandGeneralStudentCountList = 0;
      var grandMinStudentCountList = 0;
      var grandObcStudentCountList =0;
      var grandOtherStudentCountList = 0;
      var grandSbcStudentCountList = 0;
      var grandScStudentCountList = 0;
      var grandStStudentCountList = 0;
      var grandClassTotalList = 0;
      for(var i = 0; i < length; i++) {
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + bcStudentCountList[i] +"</td><td scope=\"row\">" + genralStudentCountList[i] +"</td><td scope=\"row\">" + minStudentCountList[i] +"</td><td scope=\"row\">" + obcStudentCountList[i] +"</td><td scope=\"row\">" + otherStudentCountList[i] +"</td><td scope=\"row\">" + sbcStudentCountList[i] +"</td><td scope=\"row\">" + scStudentCountList[i] +"</td><td scope=\"row\">" + stStudentCountList[i] +"</td><td scope=\"row\">" + classTotalList[i] +"</td></tr>";
        grandBcStudentCountList += bcStudentCountList[i];
        grandGeneralStudentCountList += genralStudentCountList[i];
        grandMinStudentCountList += minStudentCountList[i];
        grandObcStudentCountList += obcStudentCountList[i];
        grandOtherStudentCountList += otherStudentCountList[i];
        grandSbcStudentCountList += sbcStudentCountList[i];
        grandScStudentCountList += scStudentCountList[i];
        grandStStudentCountList += stStudentCountList[i];
        grandClassTotalList += classTotalList[i];
      }

      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandBcStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandGeneralStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandMinStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandObcStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" +  grandOtherStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandSbcStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandScStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandStStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassTotalList + "</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-categorywise-student-distribution").html(genderwiseTableHTML);

    },

    renderNewAdmissionCountTable : function (admission_home_stats) {
      $("#new-admission-graph-header-text").text("New Admissions (Tabular View)");
      $("#chartjs-new-admission-student-bar-distribution").attr("style", "display:none;");
      $("#chartjs-new-admission").attr("style", "display:none;");
      $("#percentage-change-new-admission").attr("style", "display:none;float:right;");
      $("#tabularview-new-admission-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseNewAdmissionCountMap = admission_home_stats.classWiseNewAdmissionCountMap;
      var classWiseLastYearNewAdmissionCountMap = admission_home_stats.classWiseLastYearNewAdmissionCountMap;

      var length = classWiseNewAdmissionCountMap.size;

      var classNameList = [];
      var totalNewAdmissionCountList = [];
      for (let [className, count] of  classWiseNewAdmissionCountMap.entries()) {
        classNameList.push(className);
        totalNewAdmissionCountList.push(count);
      }

      var totalLastYearNewAdmissionCountList = [];
      var length2 = classWiseLastYearNewAdmissionCountMap.size;
      if(length2 <= 0) {
        for(var i = 0; i < length; i++) {
          totalLastYearNewAdmissionCountList.push(0);
        }
      } else {
        for (let [className, count] of  classWiseLastYearNewAdmissionCountMap.entries()) {
          totalLastYearNewAdmissionCountList.push(count);
        }
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">New Admissions</th><th scope=\"col\">%</th></tr></thead><tbody>";
      var grandTotalNewAdmissionCountList = 0;
      var grandPercentageChangeHTML = 0;
      for(var i = 0; i < length; i++) {
        var percentageChangeHTML = "";
        if(totalLastYearNewAdmissionCountList[i] == 0) {
          percentageChangeHTML = "<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span>";
        } else {
          var percentageChange = getPercentageChange(totalLastYearNewAdmissionCountList[i], totalNewAdmissionCountList[i]);
          percentageChangeHTML = percentageChange >= 0 ? "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "<i class=\"bi bi-arrow-up\"></i></span>"
           : "<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "<i class=\"bi bi-arrow-down\"></i></span>";
        }
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + totalNewAdmissionCountList[i] +"</td><td scope=\"row\">" + percentageChangeHTML +"</td></tr>";
        grandTotalNewAdmissionCountList += parseFloat(totalNewAdmissionCountList[i]);
        grandPercentageChangeHTML += parseFloat(percentageChangeHTML);
      }

      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalNewAdmissionCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + (isNaN(grandPercentageChangeHTML)?"-":grandPercentageChangeHTML) +"</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-new-admission-student-distribution").html(genderwiseTableHTML);

    },

    renderRTECountTable : function (admission_home_stats) {
      $("#rte-graph-header-text").text("RTE (Tabular View)");
      $("#chartjs-rte-student-pie").attr("style", "display:none;");
      $("#percentage-rte-students").attr("style", "display:none;");
      $("#tabularview-rte-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseRteCountMap = admission_home_stats.classWiseRteCountMap;
      var classWiseStudentCountMap = admission_home_stats.classWiseStudentCountMap;

      var length = classWiseRteCountMap.size;

      var classNameList = [];
      var allStudentsClassWiseCount = [];
      for (let [className, count] of  classWiseStudentCountMap.entries()) {
        classNameList.push(className);
        allStudentsClassWiseCount.push(count);
      }
      var rteClassWiseCount = [];
      for (let [className, count] of  classWiseRteCountMap.entries()) {
        rteClassWiseCount.push(count);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">RTE Students</th><th scope=\"col\">%</th></tr></thead><tbody>";

      var grandRteClassWiseCount = 0;
      var grandPercentage = 0;
      for(var i = 0; i < length; i++) {
        if(allStudentsClassWiseCount[i] == 0) {
          percentageChangeHTML = "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + rteClassWiseCount[i] +"</td><td scope=\"row\">-</td></tr>";
        } else {
          var percentage = getPercentage(rteClassWiseCount[i], allStudentsClassWiseCount[i]);
          genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + rteClassWiseCount[i] +"</td><td scope=\"row\">" + percentage +"</td></tr>";
        }
        grandRteClassWiseCount += parseFloat(rteClassWiseCount[i]);
        grandPercentage += parseFloat(percentage);
      }
      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandRteClassWiseCount + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandPercentage + "</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-rte-student-distribution").html(genderwiseTableHTML);

    },

    renderStudentGenderCountTable : function (admission_home_stats) {
      $("#genderwise-graph-header-text").text("Genderwise Student Distribution (Tabular View)");
      $("#chartjs-genderwise-student-bar-distribution").attr("style", "display:none;");
      $("#tabularview-genderwise-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseNewAdmissionGenderCountMap = admission_home_stats.classWiseNewAdmissionGenderCountMap;
      var classWiseOldStudentGenderCountMap = admission_home_stats.classWiseOldStudentGenderCountMap;

      var length = classWiseNewAdmissionGenderCountMap.size;

      var classNameList = [];
      var classWiseNewAdmissionMaleCount = [];
      var classWiseNewAdmissionFemaleCount = [];
      // var classWiseNewAdmissionTransgenderCount = [];
      var classWiseNewAdmissionTotalCount = [];

      for (let [className, genderCountMap] of  classWiseNewAdmissionGenderCountMap.entries()) {
        classNameList.push(className);
        var classTotal = 0;
        $.each(genderCountMap, function(gender, count) {
          if(gender === "MALE") {
            classWiseNewAdmissionMaleCount.push(count);
            classTotal += count;
          } else if(gender === "FEMALE") {
            classWiseNewAdmissionFemaleCount.push(count);
            classTotal += count;
          }
          // else if(gender === "TRANSGENDER") {
          //   classWiseNewAdmissionTransgenderCount.push(count);
          //   classTotal += count;
          // }
        });
        classWiseNewAdmissionTotalCount.push(classTotal);
      }

      var classWiseOldStudentsMaleCount = [];
      var classWiseOldStudentsFemaleCount = [];
      // var classWiseOldStudentsTransgenderCount = [];
      var classWiseOldStudentsTotalCount = [];

      for (let [className, genderCountMap] of  classWiseOldStudentGenderCountMap.entries()) {
        var classTotal = 0;
        $.each(genderCountMap, function(gender, count) {
          if(gender === "MALE") {
            classWiseOldStudentsMaleCount.push(count);
            classTotal += count;
          } else if(gender === "FEMALE") {
            classWiseOldStudentsFemaleCount.push(count);
            classTotal += count;
          }
          // else if(gender === "TRANSGENDER") {
          //   classWiseOldStudentsTransgenderCount.push(count);
          //   classTotal += count;
          // }
        });
        classWiseOldStudentsTotalCount.push(classTotal);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\" rowspan=\"2\">Class</th><th scope=\"col\" colspan=\"3\">New Students</th><th scope=\"col\" colspan=\"3\">Old Students</th><th scope=\"col\" colspan=\"3\">Total Students</th></tr><tr><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th></tr></thead><tbody>";

      var grandClassWiseNewAdmissionMaleCount = 0;
      var grandClassWiseNewAdmissionFemaleCount = 0;
      var grandClassWiseNewAdmissionTotalCount = 0;
      var grandClassWiseOldStudentsMaleCount = 0;
      var grandClassWiseOldStudentsFemaleCount = 0;
      var grandClassWiseOldStudentsTotalCount = 0;
      var grandTotalAdmissionStudentMaleCount = 0;
      var grandTotalAdmissionStudentFemaleCount = 0;
      var grandTotalAdmissionStudentTotalCount = 0;
      for(var i = 0; i < length; i++) {
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionMaleCount[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionFemaleCount[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionTotalCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsMaleCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsFemaleCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsTotalCount[i] +"</td><td scope=\"row\">" + (classWiseNewAdmissionMaleCount[i] + classWiseOldStudentsMaleCount[i]) +"</td><td scope=\"row\">" + (classWiseNewAdmissionFemaleCount[i] + classWiseOldStudentsFemaleCount[i]) +"</td><td scope=\"row\">" + (classWiseNewAdmissionTotalCount[i] + classWiseOldStudentsTotalCount[i]) +"</td></tr>";
        grandClassWiseNewAdmissionMaleCount += classWiseNewAdmissionMaleCount[i];
        grandClassWiseNewAdmissionFemaleCount +=  classWiseNewAdmissionFemaleCount[i];
        grandClassWiseNewAdmissionTotalCount += classWiseNewAdmissionTotalCount[i];
        grandClassWiseOldStudentsMaleCount += classWiseOldStudentsMaleCount[i];
        grandClassWiseOldStudentsFemaleCount += classWiseOldStudentsFemaleCount[i];
        grandClassWiseOldStudentsTotalCount += classWiseOldStudentsTotalCount[i];
        grandTotalAdmissionStudentMaleCount += (classWiseNewAdmissionMaleCount[i] + classWiseOldStudentsMaleCount[i]);
        grandTotalAdmissionStudentFemaleCount += (classWiseNewAdmissionFemaleCount[i] + classWiseOldStudentsFemaleCount[i]);
        grandTotalAdmissionStudentTotalCount += (classWiseNewAdmissionTotalCount[i] + classWiseOldStudentsTotalCount[i]);
      }

      genderwiseTableHTML+="<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionMaleCount +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionTotalCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsMaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsTotalCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentMaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentTotalCount  +"</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-genderwise-student-distribution").html(genderwiseTableHTML);
    },

    displayDashboardContent : function () {
     var admission_home_stats_json = readJson("#admission-home-stats");
     var standards = readJson("#home-page-standards-stats");

     admission_home_stats = statistics.initAdmissionHomeStats(admission_home_stats_json, standards)

     statistics.renderTopBoxesData(admission_home_stats);
     statistics.renderGenderCountChartData(admission_home_stats);
     statistics.renderRTECountChartData(admission_home_stats);
     statistics.renderNewAdmissionCountChartData(admission_home_stats);
     statistics.renderCategoryCountChartData(admission_home_stats);

   },

   renderCategoryCountChartData : function (admission_home_stats) {
     $("#categorywise-graph-header-text").text("Categorywise Student Distribution (Graphical View)");
     $("#chartjs-categorywise-student-bar-distribution").attr("style", "display:block;");
     $("#tabularview-categorywise-student-distribution").attr("style", "display:none;");
     var classWiseCategoryCountMap = admission_home_stats.classWiseCategoryCountMap;
     var totalStudents = admission_home_stats.totalStudents;

     var labelArr = ["BC", "GENERAL", "MIN", "OBC", "OTHER", "SBC", "SC", "ST"];
     var bcStudentCount = 0;
     var genralStudentCount = 0;
     var minStudentCount = 0;
     var obcStudentCount = 0;
     var otherStudentCount = 0;
     var sbcStudentCount = 0;
     var scStudentCount = 0;
     var stStudentCount = 0;

     for (let [className, categoryCountMap] of  classWiseCategoryCountMap.entries()) {
       $.each(categoryCountMap, function(category, count) {
         if(category === "BC") {
           bcStudentCount += count;
         } else if(category === "GENERAL") {
           genralStudentCount += count;
         } else if(category === "MIN") {
           minStudentCount += count;
         } else if(category === "OBC") {
           obcStudentCount += count;
         } else if(category === "OTHER") {
           otherStudentCount += count;
         } else if(category === "SBC") {
           sbcStudentCount += count;
         } else if(category === "SC") {
           scStudentCount += count;
         } else if(category === "ST") {
           stStudentCount += count;
         }
       });
     }

     var studentCategoryList = [bcStudentCount, genralStudentCount, minStudentCount, obcStudentCount, otherStudentCount, sbcStudentCount, scStudentCount, stStudentCount];
     var totalStudentList = [totalStudents - bcStudentCount, totalStudents - genralStudentCount, totalStudents - minStudentCount, totalStudents - obcStudentCount, totalStudents - otherStudentCount, totalStudents - sbcStudentCount, totalStudents - scStudentCount, totalStudents - stStudentCount];
     statistics.renderStudentCategoryCountChart(labelArr, studentCategoryList, totalStudentList);
   },

   renderNewAdmissionCountChartData : function (admission_home_stats) {
     $("#new-admission-graph-header-text").text("New Admissions (Graphical View)");
     $("#chartjs-new-admission-student-bar-distribution").attr("style", "display:block;");
     $("#chartjs-new-admission").attr("style", "display:block;");
     $("#percentage-change-new-admission").attr("style", "display:block;float:right;");
     $("#tabularview-new-admission-student-distribution").attr("style", "display:none;");
     var classWiseNewAdmissionCountMap = admission_home_stats.classWiseNewAdmissionCountMap;
     var classWiseLastYearNewAdmissionCountMap = admission_home_stats.classWiseLastYearNewAdmissionCountMap;

     var totalNewAdmissionCount = 0;
     for (let [className, count] of  classWiseNewAdmissionCountMap.entries()) {
       totalNewAdmissionCount += count;
     }

     var totalLastYearNewAdmissionCount = 0;
     var length2 = classWiseLastYearNewAdmissionCountMap.size;
     if(length2 <= 0) {
     } else {
       for (let [className, count] of  classWiseLastYearNewAdmissionCountMap.entries()) {
         totalLastYearNewAdmissionCount += count;
       }
     }

     var labelArr = ["Previous Session", "This Session"];
     var newAdmissionCount = [];
     newAdmissionCount.push(totalLastYearNewAdmissionCount);
     newAdmissionCount.push(totalNewAdmissionCount);

     var html = "";
     if(totalLastYearNewAdmissionCount == 0) {
       html = "<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span>";
     } else {
       var percentageChange = getPercentageChange(totalLastYearNewAdmissionCount, totalNewAdmissionCount);
       html = percentageChange >= 0 ? "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>"
        : "<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>";
      }
     $("#percentage-change-new-admission").html(html);

     statistics.renderNewAdmissionStudentChart(labelArr, newAdmissionCount);
   },

   renderRTECountChartData : function (admission_home_stats) {
     $("#rte-graph-header-text").text("RTE (Graphical View)");
     $("#chartjs-rte-student-pie").attr("style", "display:block;");
     $("#percentage-rte-students").attr("style", "display:block;");
     $("#tabularview-rte-student-distribution").attr("style", "display:none;");
     var classWiseRteCountMap = admission_home_stats.classWiseRteCountMap;
     var classWiseStudentCountMap = admission_home_stats.classWiseStudentCountMap;

     var allStudentsClassWiseCount = [];
     for (let [className, count] of  classWiseStudentCountMap.entries()) {
       allStudentsClassWiseCount.push(count);
     }
     var rteClassWiseCount = [];
     for (let [className, count] of  classWiseRteCountMap.entries()) {
       rteClassWiseCount.push(count);
     }
     var nonRTEStudentsClassWiseCount = [];
     for(var i = 0; i < allStudentsClassWiseCount.length; i++) {
       nonRTEStudentsClassWiseCount.push(allStudentsClassWiseCount[i] - rteClassWiseCount[i]);
     }

     var totalRTEStudent = 0;
     var totalNonRTEStudent = 0;
     for(var i = 0; i < rteClassWiseCount.length; i++) {
       totalRTEStudent += rteClassWiseCount[i];
     }
     for(var i = 0; i < nonRTEStudentsClassWiseCount.length; i++) {
       totalNonRTEStudent += nonRTEStudentsClassWiseCount[i];
     }

     var html = "";
     if((totalRTEStudent + totalNonRTEStudent) == 0) {
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>0%</span><span class=\"text-muted\"> of total students</span>";
     } else {
       var percentageChange = getPercentage(totalRTEStudent, (totalRTEStudent + totalNonRTEStudent));
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%</span><span class=\"text-muted\"> of total students</span>";
     }
     $("#percentage-rte-students").html(html);

     var pieDataArr = [totalRTEStudent, totalNonRTEStudent]
     var pielabelArr = ["RTE", "Non RTE"];
     statistics.renderRTEStudentsPieChart(pielabelArr, pieDataArr);
   },

   renderGenderCountChartData : function (admission_home_stats) {
     $("#genderwise-graph-header-text").text("Genderwise Student Distribution (Graphical View)");
     $("#chartjs-genderwise-student-bar-distribution").attr("style", "display:block;");
     $("#tabularview-genderwise-student-distribution").attr("style", "display:none;");
     var classWiseNewAdmissionGenderCountMap = admission_home_stats.classWiseNewAdmissionGenderCountMap;
     var classWiseOldStudentGenderCountMap = admission_home_stats.classWiseOldStudentGenderCountMap;

     var labelArr = [];
     var newAdmissionMale = [];
     var newAdmissionFemale = [];
     // var newAdmissionTransgender = [];
     var oldStudentsMale = [];
     var oldStudentsFemale = [];
     // var oldStudentsTransgender = [];

      for (let [className, genderCountMap] of  classWiseNewAdmissionGenderCountMap.entries()) {
       labelArr.push(className);
       $.each(genderCountMap, function(gender, count) {
         if(gender === "MALE") {
           newAdmissionMale.push(count);
         } else if(gender === "FEMALE") {
           newAdmissionFemale.push(count);
         }
         // else if(gender === "TRANSGENDER") {
         //   newAdmissionTransgender.push(count);
         // }
       });
     }

     for (let [className, genderCountMap] of  classWiseOldStudentGenderCountMap.entries()) {
       $.each(genderCountMap, function(gender, count) {
         if(gender === "MALE") {
           oldStudentsMale.push(count);
         } else if(gender === "FEMALE") {
           oldStudentsFemale.push(count);
         }
         // else if(gender === "TRANSGENDER") {
         //   oldStudentsTransgender.push(count);
         // }
       });
     }

    statistics.renderStudentGenderCountChart(labelArr, newAdmissionMale, newAdmissionFemale, oldStudentsMale, oldStudentsFemale);
   },

   renderTopBoxesData : function(admission_home_stats) {
     var totalStudents = admission_home_stats.totalStudents;
     var oldStudents = admission_home_stats.oldStudents;
     var newRegistrationStudents = admission_home_stats.newRegistrationStudents;
     var newAdmissionStudents = admission_home_stats.newAdmissionStudents;
     $("#total-student-count").text(totalStudents);
     console.log("newRegistrationStudents = " + newRegistrationStudents);
     $("#total-new-registration-count").text(newRegistrationStudents);
     $("#total-new-admissions-count").text(newAdmissionStudents);
     $("#total-old-students-count").text(oldStudents);

     var totalLastYearStudents = admission_home_stats.totalLastYearStudents;
     var oldLastYearStudents = admission_home_stats.oldLastYearStudents;
     var newRegistrationLastYearStudents = admission_home_stats.newRegistrationLastYearStudents;
     var newAdmissionLastYearStudents = admission_home_stats.newAdmissionLastYearStudents;

     if(totalLastYearStudents == 0) {
       $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var totalStudentPercentageChange = getPercentageChange(totalLastYearStudents, totalStudents);
       if(totalStudentPercentageChange >= 0) {
         $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(totalStudentPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(totalStudentPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }

     console.log("newRegistrationLastYearStudents : " + newRegistrationLastYearStudents);
     if(newRegistrationLastYearStudents == 0) {
       $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var newRegistrationPercentageChange = getPercentageChange(newRegistrationLastYearStudents, newRegistrationStudents);
       if(newRegistrationPercentageChange >= 0) {
         $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newRegistrationPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newRegistrationPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }

     if(newAdmissionLastYearStudents == 0) {
       $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var newAdmissionPercentageChange = getPercentageChange(newAdmissionLastYearStudents, newAdmissionStudents);
       if(newAdmissionPercentageChange >= 0) {
         $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newAdmissionPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newAdmissionPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }
   },

   renderNewAdmissionStudentChart : function (labelArr, newAdmissionCount) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 20;
     };
     // Bar chart
     var newAdmissionChart = new Chart($("#chartjs-new-admission-student-bar-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [
           {
            label: "New Admission",
            backgroundColor: LOGO_COLOR,
            borderColor: LOGO_COLOR,
            hoverBackgroundColor: LOGO_COLOR,
            hoverBorderColor: LOGO_COLOR,
            data: newAdmissionCount,
            barPercentage: .325,
            categoryPercentage: .5
          }
    ]},
   options: {
     responsive: !window.MSInputMethodContext,
     maintainAspectRatio: false,
     legend: {
       display: false,
     },
     scales: {
       yAxes: [{
         ticks: {
              beginAtZero: true
         },
         gridLines: {
           display: false
         },
         stacked: false,
         stacked: true,
       }],
       xAxes: [{
         stacked: false,
         gridLines: {
           color: "transparent"
         },
         stacked: true,
       }]
     }
   }
  });
  newAdmissionChart.canvas.parentNode.style.height = '311px';
  newAdmissionChart.canvas.parentNode.style.width = '311px';
   },

   renderStudentCategoryCountChart : function (labelArr, studentCategoryList, totalStudentList) {

      new Chart($("#chartjs-categorywise-student-bar-distribution"), {
        type: 'horizontalBar',
        data: {
          labels: labelArr,
          datasets: [
            {
             label: "Category Student",
             backgroundColor: LOGO_COLOR,
             borderColor: LOGO_COLOR,
             hoverBackgroundColor: LOGO_COLOR,
             hoverBorderColor: LOGO_COLOR,
             data: studentCategoryList,
             barPercentage: .325,
             categoryPercentage: .5
           },
           {
            label: "Other Students",
            backgroundColor: LIGHT_GREY_COLOR,
            borderColor: LIGHT_GREY_COLOR,
            hoverBackgroundColor: LIGHT_GREY_COLOR,
            hoverBorderColor: LIGHT_GREY_COLOR,
            data: totalStudentList,
            barPercentage: .325,
            categoryPercentage: .5
          }
     ]},
        options: {
            responsive: !window.MSInputMethodContext,
            maintainAspectRatio: true,
            legend: {
              display: false
            },
            tooltips: {
             enabled: true,
             mode: 'single',
             displayColors: false,
             callbacks: {
               label: function(tooltipItems, data) {
                 return  " : " + data.datasets[0].data[tooltipItems.index] + '';
               }
             }
           },
            scales: {
              yAxes: [{
                ticks: {
                     beginAtZero: true
                },
                gridLines: {
                  display: false
                },
                stacked: false,
                stacked: true,
              }],
              xAxes: [{
                stacked: false,
                gridLines: {
                  color: "transparent"
                },
                stacked: true,
              }]
            }
        }
      });
   },

   renderStudentGenderCountChart : function (labelArr, newAdmissionMale, newAdmissionFemale, oldStudentsMale, oldStudentsFemale) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 20;
     };
     // Bar chart
     new Chart($("#chartjs-genderwise-student-bar-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [
           {
            label: "Old Student Male",
            backgroundColor: LOGO_COLOR,
            borderColor: LOGO_COLOR,
            hoverBackgroundColor: LOGO_COLOR,
            hoverBorderColor: LOGO_COLOR,
            data: oldStudentsMale,
            barPercentage: .325,
            categoryPercentage: .5
          },
          {
           label: "Old Student Female",
           backgroundColor: OLD_STUDENTS_FEMALE_COLOR,
           borderColor: OLD_STUDENTS_FEMALE_COLOR,
           hoverBackgroundColor: OLD_STUDENTS_FEMALE_COLOR,
           hoverBorderColor: OLD_STUDENTS_FEMALE_COLOR,
           data: oldStudentsFemale,
           barPercentage: .325,
           categoryPercentage: .5
         },
         {
					label: "New Admission Male",
					backgroundColor: NEW_ADMISSION_MALE_COLOR,
					borderColor: NEW_ADMISSION_MALE_COLOR,
					hoverBackgroundColor: NEW_ADMISSION_MALE_COLOR,
					hoverBorderColor: NEW_ADMISSION_MALE_COLOR,
					data: newAdmissionMale,
					barPercentage: .325,
					categoryPercentage: .5
				},
        {
         label: "New Admission Female",
         backgroundColor: NEW_ADMISSION_FEMALE_COLOR,
         borderColor: NEW_ADMISSION_FEMALE_COLOR,
         hoverBackgroundColor: NEW_ADMISSION_FEMALE_COLOR,
         hoverBorderColor: NEW_ADMISSION_FEMALE_COLOR,
         data: newAdmissionFemale,
         barPercentage: .325,
         categoryPercentage: .5
       }
    ]},
   options: {
     responsive: !window.MSInputMethodContext,
     maintainAspectRatio: true,
     cornerRadius: 15,
     legend: {
       display: true,
       position: 'top',
       align: 'start',
       labels:{
         boxWidth: 12,
       }
     },
     scales: {
       yAxes: [{
         ticks: {
              beginAtZero: true
         },
         gridLines: {
           display: false
         },
         stacked: false,
         stacked: true,
       }],
       xAxes: [{
         stacked: false,
         gridLines: {
           color: "transparent"
         },
         stacked: true,
       }]
     }
   }
  });
   },

   renderRTEStudentsPieChart : function (labelArr, dataArr) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 40;
     };
     var rteChart = new Chart($("#chartjs-rte-student-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: [
             LOGO_COLOR,
             LIGHT_GREY_COLOR
           ],
           borderWidth: 1,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: false,
         cutoutPercentage: 75,
         legend: {
           display: true,
           position: 'top',
           align: 'center',
           labels:{
             boxWidth: 12,
             padding: 10,
           }
         },
       }
     });
     rteChart.canvas.parentNode.style.height = '250px';
     rteChart.canvas.parentNode.style.width = '311px';
   },
};

function previousTabSwtichingEvent(){
  $('.previous-switch-tab').on('click', function () {
    var tabPrev = $('.switch-tab.active').prev();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabPrev.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabPrev.removeClass('disabled').addClass('active');
  });
}

function nextTabSwtichingEvent(){
  $('.next-switch-tab').on('click', function () {
    switchToNextTab();
  });
}

function switchToNextTab(){
    var tabNext = $('.switch-tab.active').next();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabNext.removeClass('disabled').addClass('active');
}

function swtichingTostudentUpdateFormFirstTab(){
    var tabNext = $('.switch-tab').first();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabNext.removeClass('disabled').addClass('active');
}

function closeModal(){
  clearMandatoryFieldsErrorDisplay();
}

function returnToMainScreen() {
  var screenVal = $('#screen-name').text().trim();
  if(screenVal == 'ADMIT-STUDENT'){
    loadAdmitStudentMenu();
  } else{
    searchStudents(true);
    $('.student-details-screen').attr('style','display:none');
    $('#student-details').attr('style','display:block');
    swtichingTostudentUpdateFormFirstTab();
  }
}

function fillStudentSections(classId) {
  var standardsJson = $('p.student-standard-json').first().text().trim();
  var standards = JSON.parse(standardsJson);
  var classSectionMap = {}

  for(var i = 0 ; i < standards.length ; i++){
      var standard = standards[i]
      classSectionMap[standard.standardId] = standard
  }

  if(!(classId in classSectionMap)){
      return;
  }
  var options = "<option value=\"\"></option>"
  var standard = classSectionMap[classId];
  for(var i = 0 ; i < standard.standardSectionList.length; i++){
      options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
  }
  $('.student-section').html(options);
}

function fillStudentAcademicSession(academicSession) {
  var options = "<option value=\""+academicSession.academicSessionId+"\">"+academicSession.displayName+"</option>"
  $('#update-student-academic-session').html(options);
}

function fillStudentInformation(studentInfo) {
    fillStudentAcademicSession(studentInfo.studentAcademicSessionInfoResponse.academicSession);
    $("#update-admission-student-id").text(studentInfo.studentId);
    $("#update\\.student-registration-number").val(studentInfo.studentBasicInfo.registrationNumber);
    $("#update\\.student-admission-number").val(studentInfo.studentBasicInfo.admissionNumber);
    $("#update\\.pen-number").val(studentInfo.studentBasicInfo.penNumber);
    $("#update\\.apaar-id-number").val(studentInfo.studentBasicInfo.apaarIdNo);
    // $("#update-student-academic-session").val(studentInfo.studentAcademicSessionInfoResponse.academicSession.academicSessionId);
    $("#update-student-class").val(studentInfo.studentAcademicSessionInfoResponse.standard.standardId);
    fillStudentSections(studentInfo.studentAcademicSessionInfoResponse.standard.standardId);
    if(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList != null && studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList.length > 0){
      $("#update-student-section").val(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionId);
    }
    $('#update\\.student-name').val(studentInfo.studentBasicInfo.name);
    $('#update\\.student-gender').val(studentInfo.studentBasicInfo.gender);
    setFormattedDate(studentInfo.studentBasicInfo.dateOfBirth, "#update\\.student-date-of-birth");
    $('#update\\.student-birth-place').val(studentInfo.studentBasicInfo.birthPlace);
    $('#update\\.student-category').val(studentInfo.studentBasicInfo.userCategory);
    $('#update\\.student-religion').val(studentInfo.studentBasicInfo.religion);
    $('#update\\.student-caste').val(studentInfo.studentBasicInfo.caste);
    $('#update\\.student-rte').prop("checked", studentInfo.studentBasicInfo.rte);
    $("#update\\.student-mother-tongue").val(studentInfo.studentBasicInfo.motherTongue);
    $("#update\\.student-area-type").val(studentInfo.studentBasicInfo.areaType);
    $("#update\\.student-specially-abled").prop("checked", studentInfo.studentBasicInfo.speciallyAbled);
    $("#update\\.student-bpl").prop("checked", studentInfo.studentBasicInfo.bpl);
    $('#update\\.student-present-address').val(studentInfo.studentBasicInfo.presentAddress);
    $('#update\\.student-present-city').val(studentInfo.studentBasicInfo.presentCity);
    $('#update\\.student-present-state').find(".country-states").val(studentInfo.studentBasicInfo.presentState);
    $('#update\\.student-present-post-office').val(studentInfo.studentBasicInfo.presentPostOffice);
    $('#update\\.student-present-police-station').val(studentInfo.studentBasicInfo.presentPoliceStation);
    $('#update\\.student-present-zipcode').val(studentInfo.studentBasicInfo.presentZipcode);
    $('#update\\.student-permanent-address').val(studentInfo.studentBasicInfo.permanentAddress);
    $('#update\\.student-city').val(studentInfo.studentBasicInfo.permanentCity);
    $('#update\\.student-state').find(".country-states").val(studentInfo.studentBasicInfo.permanentState);
    $('#update\\.student-post-office').val(studentInfo.studentBasicInfo.permanentPostOffice);
    $('#update\\.student-police-station').val(studentInfo.studentBasicInfo.permanentPoliceStation);
    $('#update\\.student-zipcode').val(studentInfo.studentBasicInfo.permanentZipcode);
    $('#update\\.student-aadhar-number').val(studentInfo.studentBasicInfo.aadharNumber);
    setFormattedDate(studentInfo.studentBasicInfo.registrationDate, "#update\\.student-registration-date");
    setFormattedDate(studentInfo.studentBasicInfo.admissionDate, "#update\\.student-admission-date");
    $('#update\\.student-primary-contact-number').val(studentInfo.studentBasicInfo.primaryContactNumber);
    $('#update\\.student-primary-email').val(studentInfo.studentBasicInfo.primaryEmail);
    $('#update\\.student-whatsapp-number').val(studentInfo.studentBasicInfo.whatsappNumber);
    $("#update\\.student-is-sponsored").prop("checked", studentInfo.studentBasicInfo.sponsored);
    $("#update\\.student-new-admission").prop("checked", studentInfo.newAdmission);
    $("#update\\.student-hosteller").prop("checked", studentInfo.studentBasicInfo.hosteller);
    $('#update\\.student-nationality').val(studentInfo.studentBasicInfo.nationality);

    // if(studentInfo.studentImage != null){
    //     $('#update-student-photo-label').html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>");
    // }else{
    //     $('#update-student-photo-label').html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>");
    // }

    $('#update\\.student-house').val(studentInfo.studentBasicInfo.instituteHouse == null ? "" : studentInfo.studentBasicInfo.instituteHouse.houseId);
    $('#update\\.student-admission-in-class').val(studentInfo.studentBasicInfo.admissionInClass);
    $('#update\\.student-name-as-per-aadhar').val(studentInfo.studentBasicInfo.studentNameAsPerAadhar);
    var childCategoryCriteria = "NA";
    if(!(studentInfo.studentBasicInfo.childCategoryCriteria === "" || studentInfo.studentBasicInfo.childCategoryCriteria === null || studentInfo.studentBasicInfo.childCategoryCriteria === undefined)) {
      childCategoryCriteria = studentInfo.studentBasicInfo.childCategoryCriteria;
    }
    $('#update\\.child-category-criteria').val();
    $('#update\\.specially-abled-type').val(studentInfo.studentBasicInfo.speciallyAbledType);

    $('#student-mother-name').val(studentInfo.studentFamilyInfo.mothersName);
    $('#student-father-name').val(studentInfo.studentFamilyInfo.fathersName);
    $('#student-mother-qualification').val(studentInfo.studentFamilyInfo.mothersQualification);
    $('#student-father-qualification').val(studentInfo.studentFamilyInfo.fathersQualification);
    $('#mother-contact-number').val(studentInfo.studentFamilyInfo.mothersContactNumber);
    $('#father-contact-number').val(studentInfo.studentFamilyInfo.fathersContactNumber);
    $('#mother-occupation').val(studentInfo.studentFamilyInfo.mothersOccupation);
    $('#mother-annual-income').val(studentInfo.studentFamilyInfo.mothersAnnualIncome);
    $('#father-occupation').val(studentInfo.studentFamilyInfo.fathersOccupation);
    $('#father-annual-income').val(studentInfo.studentFamilyInfo.fathersAnnualIncome);
    $('#mother-aadhar-number').val(studentInfo.studentFamilyInfo.mothersAadharNumber);
    $('#father-aadhar-number').val(studentInfo.studentFamilyInfo.fathersAadharNumber);
    $('#mother-pan-card-details').val(studentInfo.studentFamilyInfo.mothersPanCardDetails);
    $('#father-pan-card-details').val(studentInfo.studentFamilyInfo.fathersPanCardDetails);
    $('#approx-family-income').val(studentInfo.studentFamilyInfo.approxFamilyIncome);

    if(studentInfo.studentGuardianInfoList != null && studentInfo.studentGuardianInfoList.length > 0){
      $('#guardian-name').val(studentInfo.studentGuardianInfoList[0].guardianName);
      $('#guardian-relation').val(studentInfo.studentGuardianInfoList[0].relation);
      $('#guardian-age').val(studentInfo.studentGuardianInfoList[0].age);
      $('#guardian-gender').val(studentInfo.studentGuardianInfoList[0].gender);
      $('#guardian-occupation').val(studentInfo.studentGuardianInfoList[0].occupation);
      $('#guardian-email').val(studentInfo.studentGuardianInfoList[0].email);
      $('#guardian-contact-number').val(studentInfo.studentGuardianInfoList[0].contactNumber);
      $('#guardian-address').val(studentInfo.studentGuardianInfoList[0].address);
      $('#guardian-city').val(studentInfo.studentGuardianInfoList[0].city);
      $('#guardian-state').find(".country-states").val(studentInfo.studentGuardianInfoList[0].state);
      $('#guardian-zipcode').val(studentInfo.studentGuardianInfoList[0].zipcode);
    }
    $('#previous-school-name').val(studentInfo.studentPreviousSchoolInfo.schoolName);
    $('#previous-school-medium').val(studentInfo.studentPreviousSchoolInfo.medium);
    $('#class-passed').val(studentInfo.studentPreviousSchoolInfo.classPassed);
    $('#year-of-passing').val(studentInfo.studentPreviousSchoolInfo.yearOfPassing);
    $('#result').val(studentInfo.studentPreviousSchoolInfo.result);
    $('#percentage').val(studentInfo.studentPreviousSchoolInfo.percentage);
    $("#student-is-admission-tc-based").prop("checked", studentInfo.studentPreviousSchoolInfo.admissionTcBased);
    $('#previous-school-tc-number').val(studentInfo.studentPreviousSchoolInfo.tcNumber);

    $('#blood-group').val(studentInfo.studentMedicalInfo.bloodGroup);
    $('#blood-pressure').val(studentInfo.studentMedicalInfo.bloodPressure);
    $('#pulse').val(studentInfo.studentMedicalInfo.pulse);
    $('#height').val(studentInfo.studentMedicalInfo.height);
    $('#weight').val(studentInfo.studentMedicalInfo.weight);
    setFormattedDate(studentInfo.studentMedicalInfo.dateOfPhysicalExamination, '#date-of-physical-examination');

    var studentDocumentsMap = {};
    if(studentInfo.studentDocuments != null && studentInfo.studentDocuments.length > 0){
      for(var i = 0 ; i < studentInfo.studentDocuments.length; i++){
        var studentDocument = studentInfo.studentDocuments[i];
        studentDocumentsMap[studentDocument.documentType] = studentDocument;
      }
    }

    fillStudentDocumentsInformation(studentDocumentsMap);

}

function fillStudentDocumentsInformation(studentDocumentsMap) {
  var container = $("#student-document-information-table");
  var html = "";
  for(var i = 0 ; i < STUDENT_DOCUMENT_TYPE.length; i++){
    var documentType = STUDENT_DOCUMENT_TYPE[i];
    var studentDocument = studentDocumentsMap[documentType];

    html += "<tr class=\"student-document-row\" id=" + documentType + "><td>" + documentType + "</td>";

    var documentUploadedText = "No";
    var documentCollected = false;
    var checked = "";
    var disabled = "";
    if(studentDocument != null && studentDocument != undefined) {
      documentCollected = true;
      checked = "checked";
      disabled = studentDocument.documentNotUploaded ? "" : "disabled";
      documentUploadedText = studentDocument.documentNotUploaded ? "No" : "Yes";
    }
    html += "<td><div class=\"custom-control custom-switch\"><input type=\"checkbox\" class=\"custom-control-input student-document-collected-checkbox\" id=\"student-document-checkbox-" + i + "\"" + checked + " " + disabled + "><label class=\"custom-control-label\" for=\"student-document-checkbox-" + i + "\"></label></div></td><td>" + documentUploadedText + "</td>";
    html += "</tr>";
  }

  $(container).html(html);
}

function studentDocumentWarningPopup() {
  $("#student-document-warning-popup").modal('toggle');
}



var admissionReports = {

    dataCache : {
    },

    loadMainScreen: function() {
      ajaxClient.get("/admission/reports", function(data) {
          $("#main-content").html(data);
          // $('.selectpicker').selectpicker();
          initSelect2();
          initDateInput();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          reportUtils.bindSelectClassCheckboxEvent();
          admissionReports.bindGenerateReportEvent();
          admissionReports.loadOnChangeEventsFilter();
          admissionReports.checkboxEvents();
          admissionReports.dataCache.filterationCriteria = new Object();
      });
    },

    loadOnChangeEventsFilter : function () {
      $('select.filter-gender').on('change', function(){
          admissionReports.dataCache.filterationCriteria.gender = $(this).val();
      });
      $('select.filter-religion').on('change', function(){
          admissionReports.dataCache.filterationCriteria.religion = $(this).val();
      });
      $('select.filter-category').on('change', function(){
          admissionReports.dataCache.filterationCriteria.category = $(this).val();
      });
      $('select.filter-area-type').on('change', function(){
          admissionReports.dataCache.filterationCriteria.areaType = $(this).val();
      });
      $('select.filter-specially-abled').on('change', function(){
          admissionReports.dataCache.filterationCriteria.speciallyAbled = $(this).val();
      });
      $('select.filter-rte').on('change', function(){
          admissionReports.dataCache.filterationCriteria.rte = $(this).val();
      });
      $('select.filter-bpl').on('change', function(){
          admissionReports.dataCache.filterationCriteria.bpl = $(this).val();
      });
      $('select.filter-states').on('change', function(){
          admissionReports.dataCache.filterationCriteria.states = $(this).val();
      });
      $('select.reports-student-class').on('change', function(){
          admissionReports.dataCache.filterationCriteria.standards = $(this).val();
      });
      $('select.filter-house').on('change', function(){
          admissionReports.dataCache.filterationCriteria.instituteHouse = $(this).val();
      });
    },

    bindGenerateReportEvent: function() {
      $('.report-academic-session').on('change', function(){
        var ref = this;
        var academicSessionId = $(ref).val();
        ajaxClient.get("/admission/report-session-change/" + academicSessionId , function(data) {
            $(ref).parent().parent().parent().find(".class-attendance-type-div").html(data);
            initSelect2();
            admissionReports.loadOnChangeEventsFilter();
        });
      });

      // $(".generate-student-report").on('click', function() {
      //   $(this).closest('div.modal').modal('toggle');
      //   var containerElement = $(this).closest('div.report-field-container');
      //   var academicSession = 0;
      //   if($(containerElement).find(".report-academic-session option:selected").length != 0){
      //     academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      //   };
      //   var reportType = $(containerElement).find('p.report-type').text().trim();
      //   var downloadFormat = "EXCEL";
      //   var hasDownloadFormatClass = $(containerElement).find('.download-format').length > 0;
      //   if (hasDownloadFormatClass){
      //     downloadFormat = $(containerElement).find('.download-format').val();
      //   }
      //
      //   window.open(baseURL+"/admission/generate-student-report?academicSession="+academicSession+"&reportType="+reportType+"&downloadFormat="+downloadFormat, '_blank')
      //
      // });

      // $(".admission-generate-report").on('click', function() {
      //
      //   var gender = "";
      //   if(admissionReports.dataCache.filterationCriteria.gender != undefined && admissionReports.dataCache.filterationCriteria.gender != null) {
      //     gender = admissionReports.dataCache.filterationCriteria.gender.join(",");
      //   }
      //
      //   var religion = "";
      //   if(admissionReports.dataCache.filterationCriteria.religion != undefined && admissionReports.dataCache.filterationCriteria.religion != null) {
      //     religion = admissionReports.dataCache.filterationCriteria.religion.join(",");
      //   }
      //
      //   var category = "";
      //   if(admissionReports.dataCache.filterationCriteria.category != undefined && admissionReports.dataCache.filterationCriteria.category != null) {
      //     category = admissionReports.dataCache.filterationCriteria.category.join(",");
      //   }
      //
      //   var areaType = "";
      //   if(admissionReports.dataCache.filterationCriteria.areaType != undefined && admissionReports.dataCache.filterationCriteria.areaType != null) {
      //     areaType = admissionReports.dataCache.filterationCriteria.areaType.join(",");
      //   }
      //
      //   var speciallyAbled = "";
      //   if(admissionReports.dataCache.filterationCriteria.speciallyAbled != undefined && admissionReports.dataCache.filterationCriteria.speciallyAbled  != null) {
      //     speciallyAbled = admissionReports.dataCache.filterationCriteria.speciallyAbled;
      //   }
      //
      //   var rte = "";
      //   if(admissionReports.dataCache.filterationCriteria.rte != undefined && admissionReports.dataCache.filterationCriteria.rte != null) {
      //     rte = admissionReports.dataCache.filterationCriteria.rte;
      //   }
      //
      //   var bpl = "";
      //   if(admissionReports.dataCache.filterationCriteria.bpl != undefined && admissionReports.dataCache.filterationCriteria.bpl != null) {
      //     bpl = admissionReports.dataCache.filterationCriteria.bpl;
      //   }
      //
      //   var states = "";
      //   if(admissionReports.dataCache.filterationCriteria.states != undefined && admissionReports.dataCache.filterationCriteria.states != null) {
      //     states = admissionReports.dataCache.filterationCriteria.states.join(",");
      //   }
      //
      //   var requiredStandards = "";
      //   if(admissionReports.dataCache.filterationCriteria.standards != undefined && admissionReports.dataCache.filterationCriteria.standards != null) {
      //     requiredStandards = admissionReports.dataCache.filterationCriteria.standards.join(",");
      //   }
      //
      //   var house = "";
      //   if(admissionReports.dataCache.filterationCriteria.instituteHouse != undefined && admissionReports.dataCache.filterationCriteria.instituteHouse != null) {
      //       house = admissionReports.dataCache.filterationCriteria.instituteHouse.join(",");
      //   }
      //
      //   $(this).closest('div.modal').modal('toggle');
      //   var containerElement = $(this).closest('div.report-field-container');
      //
      //   var reportType = $(containerElement).find('p.report-type').text().trim();
      //
      //   var studentStatus = "";
      //   if($(containerElement).find(".student-status").length > 0){
      //         studentStatus = $(containerElement).find(".student-status").val().join();
      //   }
      //
      //   var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
      //   var academicSession = 0;
      //   if($(containerElement).find(".report-academic-session option:selected").length != 0){
      //     academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      //   };
      //
      //   admissionReports.dataCache.filterationCriteria = new Object();
      //   var downloadFormat = $(containerElement).find('.download-format').val();
      //   if(downloadFormat === undefined) {
      //     downloadFormat = "";
      //   } else {
      //     downloadFormat = downloadFormat.trim();
      //   }
      //
      //   var newAdmission = false;
      //   if($(containerElement).find("input.new-admission").is(":checked")){
      //     newAdmission = true;
      //   }
      //
      //   if(downloadFormat == "PDF" && requiredHeadersCSV.split(",").length > 20){
      //     showErrorDialogBox("Please select atleast 20 columns for the Student Details PDF report and try again.")
      //     return;
      //   }
      //
      //   window.open(baseURL+"/admission/generate-report?reportType="+reportType+"&academicSession="+academicSession+
      //   "&requiredHeaders="+requiredHeadersCSV+"&requiredStandards="+requiredStandards+"&rte="+
      //   rte+"&category="+category+"&gender="+gender+"&religion="+religion+"&area_type="+areaType+
      //   "&specially_abled="+speciallyAbled+"&bpl="+bpl+"&state="+states+"&instituteHouseId="+house+"&downloadFormat="+downloadFormat+"&studentStatus="+studentStatus+"&newAdmission="+newAdmission, '_blank');
      // });

      $(".generate-report").on('click', function() {
        var gender = "";
        if(admissionReports.dataCache.filterationCriteria.gender != undefined && admissionReports.dataCache.filterationCriteria.gender != null) {
          gender = admissionReports.dataCache.filterationCriteria.gender.join(",");
        }

        var religion = "";
        if(admissionReports.dataCache.filterationCriteria.religion != undefined && admissionReports.dataCache.filterationCriteria.religion != null) {
          religion = admissionReports.dataCache.filterationCriteria.religion.join(",");
        }

        var category = "";
        if(admissionReports.dataCache.filterationCriteria.category != undefined && admissionReports.dataCache.filterationCriteria.category != null) {
          category = admissionReports.dataCache.filterationCriteria.category.join(",");
        }

        var areaType = "";
        if(admissionReports.dataCache.filterationCriteria.areaType != undefined && admissionReports.dataCache.filterationCriteria.areaType != null) {
          areaType = admissionReports.dataCache.filterationCriteria.areaType.join(",");
        }

        var speciallyAbled = "";
        if(admissionReports.dataCache.filterationCriteria.speciallyAbled != undefined && admissionReports.dataCache.filterationCriteria.speciallyAbled  != null) {
          speciallyAbled = admissionReports.dataCache.filterationCriteria.speciallyAbled;
        }

        var rte = "";
        if(admissionReports.dataCache.filterationCriteria.rte != undefined && admissionReports.dataCache.filterationCriteria.rte != null) {
          rte = admissionReports.dataCache.filterationCriteria.rte;
        }

        var bpl = "";
        if(admissionReports.dataCache.filterationCriteria.bpl != undefined && admissionReports.dataCache.filterationCriteria.bpl != null) {
          bpl = admissionReports.dataCache.filterationCriteria.bpl;
        }

        var states = "";
        if(admissionReports.dataCache.filterationCriteria.states != undefined && admissionReports.dataCache.filterationCriteria.states != null) {
          states = admissionReports.dataCache.filterationCriteria.states.join(",");
        }

        var house = "";
        if(admissionReports.dataCache.filterationCriteria.instituteHouse != undefined && admissionReports.dataCache.filterationCriteria.instituteHouse != null) {
            house = admissionReports.dataCache.filterationCriteria.instituteHouse.join(",");
        }

        $(this).closest('div.modal').modal('toggle');
        var containerElement = $(this).closest('div.report-field-container');
        var reportType = $(containerElement).find('p.report-type').text().trim();

        var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
        admissionReports.dataCache.filterationCriteria = new Object();
        var downloadFormat = $(containerElement).find('.download-format').val();
        if(downloadFormat === undefined) {
          downloadFormat = "";
        } else {
          downloadFormat = downloadFormat.trim();
        }

        var newAdmission = false;
        if($(containerElement).find("input.new-admission").is(":checked")){
          newAdmission = true;
        }

        if(downloadFormat == "PDF" && requiredHeadersCSV.split(",").length > 20){
          showErrorDialogBox("Please select less than 20 columns for the Student Details PDF report and try again.")
          return;
        }

        var academicSession = 0;
        if($(containerElement).find(".report-academic-session option:selected").length != 0) {
          academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        };

        var requiredStandards = "";
        if($(containerElement).find(".reports-student-class").length > 0) {
          requiredStandards = $(containerElement).find(".reports-student-class").val().join();
        }

        var studentStatus = "";
        if($(containerElement).find(".student-status").length > 0) {
              studentStatus = $(containerElement).find(".student-status").val().join();
        }

        var finalStudentStatus = "";
        if($(containerElement).find(".final-student-status").length > 0) {
          finalStudentStatus = $(containerElement).find(".final-student-status").val().join();
        }

        var documentTypes = "";
        if($(containerElement).find(".document-types").length > 0) {
            documentTypes = $(containerElement).find(".document-types").val().join();
        }

        var documentStatus = "";
        if($(containerElement).find(".document-status option:selected").length != 0) {
          documentStatus = $(containerElement).find(".document-status option:selected").val();
        };

        var startDate = 0;
        var endDate = 0;
        if($(containerElement).find("input#date-range-filter").length > 0){
         [startDate, endDate] = getFormattedDateRange($(containerElement).find('input#date-range-filter').val(), " to ")
        }

        var sortBy = "";
        if($(containerElement).find(".student-sort-by-filter").length > 0){
          sortBy = $(containerElement).find(".student-sort-by-filter").val();

        }

        window.open(baseURL+"/admission/generate-report/" + academicSession + "?requiredStandards="+requiredStandards+"&studentStatus="+studentStatus+"&finalStudentStatus="+finalStudentStatus+"&documentTypes="+documentTypes+"&documentStatus="+documentStatus + "&reportType="+reportType + "&requiredHeaders="+requiredHeadersCSV+"&rte="+
        rte+"&category="+category+"&gender="+gender+"&religion="+religion+"&area_type="+areaType+
        "&specially_abled="+speciallyAbled+"&bpl="+bpl+"&state="+states+"&instituteHouseId="+house+"&newAdmission="+newAdmission+"&startDate="+startDate+"&endDate="+endDate+"&sortBy="+sortBy, '_blank');

      });

    },

    resetPopup : function (ref) {
      // Did not find the way to deselect all
      // $(".filter-gender").select2("val", "").trigger();
      // $(".filter-religion").selectpicker('deselectAll');
      // $(".filter-category").selectpicker('deselectAll');
      // $(".filter-area-type").selectpicker('deselectAll');
      // $(".filter-specially-abled").selectpicker('val', '');
      // $(".filter-rte").selectpicker('val', '');
      // $(".filter-bpl").selectpicker('val', '');
      // $(".filter-states").selectpicker('deselectAll');
      admissionReports.resetCheckBoxes();
      const buttonId = $(ref).attr("id");
      var containerElement = $("#" + buttonId + "-modal").find('div.report-field-container');
      if ($(containerElement).find("input#date-range-filter").length > 0) {
        initMultipleDateRange("date-range-filter", 365 * 5, ' to ', inputDatePickerFormat);
        $("input#date-range-filter").val('')
        $(".student-sort-by-filter").val('')
      }
    },

    resetCheckBoxes : function () {
      $( ".parent" ).prop( "checked", false );
      $( ".child" ).prop( "checked", false );
      $( ".basic" ).prop( "checked", true );
      $( ".basic-report-column" ).prop( "checked", true );

    },

    checkboxEvents : function () {
      $('.child').on('change', function(){
        $(this).parent().parent().parent().find($(".parent")).prop( "checked", false );
      });
      $('.parent').on('change', function(){
        if($(this).prop("checked")){
          $(this).parent().parent().find($(".child")).prop( "checked", true );
        }
        else if(!($(this).prop("checked"))){
          $(this).parent().parent().find($(".child")).each(function() {
            if (!$(this).is(':disabled')) {
                $(this).prop( "checked", false );
            }
          });
        }
      });
    }
}

var transferCertificate = {

  removeTCStudentCourses : function(ref) {
    $(ref).parent().remove();
  },

  addTCStudentCourses : function() {
    var textFieldHTML = "<div class=\"input-group mb-3 col-md-4\"><input type=\"text\" class=\"form-control tc-student-courses\" placeholder=\"Subject...\"><div class=\"input-group-prepend\" style=\"cursor:pointer;\" onclick=\"transferCertificate.removeTCStudentCourses(this);\"><span class=\"input-group-text\" aria-hidden=\"true\">x</span></div></div>";
    $("#tc-student-courses-div").append(textFieldHTML);
  },

  getTCSubject : function() {
    var course_list = [];
    $(".tc-student-courses").each(function() {
      course_list.push($(this).val());
    });
    return course_list.join(",")
  },

  isValidData : function (data) {
      return data != null && data != undefined && data != "";
  },

  getTcStudentSessionDetails : function() {
  var tcPreviousSessionDetailSummaryList = [];
   $(".tc-previous-session-detail-summary-list").each(function() {

        var previousAdmissionClass = $(this).find("#tc-previous-admission-class").val();
        var previousAdmissionDate = "";

         var admissionDateVal =  getDate($(this).find("#tc-previous-admission-date").val());
       if (transferCertificate.isValidData(admissionDateVal)) {
           previousAdmissionDate = admissionDateVal.getTime() / 1000;
       }

        var admissionPromotionClass = $(this).find("#tc-admission-promotion-class").val();
        var admissionPromotionDate = "";
        var admissionPromotionDateVal =  getDate($(this).find("#tc-admission-promotion-date").val());
       if (transferCertificate.isValidData(admissionPromotionDateVal)) {
           admissionPromotionDate = admissionPromotionDateVal.getTime() / 1000;
       }
        var dateOfPassingFromSchool = "";
        var dateOfPassingFromSchoolVal = getDate($(this).find("#tc-date-of-passing-from-school").val());
        if (transferCertificate.isValidData(dateOfPassingFromSchoolVal)) {
                     dateOfPassingFromSchool = dateOfPassingFromSchoolVal.getTime()/1000;
        }
        var noOfMeeting = $(this).find("#tc-no-of-meeting").val();
        var noOfPresent = $(this).find("#tc-no-of-present").val();
        var rankInClass = $(this).find("#tc-rank-in-class").val();
        var resultDivisionFinalExam = $(this).find("#tc-result-division-final-exam").val();
        var medium = $(this).find("#tc-medium").val();
        var subjectsTaken = $(this).find("#tc-subjects-taken").val();
        var conductAndWorkInSession = $(this).find("#tc-conduct-and-work-in-session").val();
        var duration = $(this).find("#tc-duration-in-session").val();


       tcPreviousSessionDetailSummaryList.push({'previousAdmissionClass' : previousAdmissionClass, 'previousAdmissionDate' : previousAdmissionDate,'admissionPromotionClass': admissionPromotionClass, 'admissionPromotionDate': admissionPromotionDate, 'dateOfPassingFromSchool': dateOfPassingFromSchool, 'noOfMeeting': noOfMeeting, 'noOfPresent': noOfPresent,
        'rankInClass': rankInClass, 'resultDivisionFinalExam': resultDivisionFinalExam,  'medium': medium, 'subjectsTaken': subjectsTaken,
       'conductAndWorkInSession': conductAndWorkInSession, 'duration' : duration
       });
      });

      return tcPreviousSessionDetailSummaryList ;
  },


  generateStaticTC : function(studentId) {
    window.open(baseURL + "/admission/generate-pdf-tc/" + studentId, '_blank');
    returnToMainScreen();
  },

addTCStudentPrevSessionDetails: function() {
    var relievingVariablesHTML =
    "<div class=\"col-md-12 mb-3 tc-previous-session-detail-summary-list\">" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Previous Admission Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Previous AdmissionClass...\" id=\"tc-previous-admission-class\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Previous Admission Date</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Ad./Promo. Date...\" id=\"tc-previous-admission-date\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Promotion Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Promotion Class...\" id=\"tc-admission-promotion-class\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Promotion Date</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Promotion Date...\" id=\"tc-admission-promotion-date\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Date of Passing from School</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Date of Passing from School...\" id=\"tc-date-of-passing-from-school\">" +
            "</div>" +
        "</div>" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>No. of Meeting</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"No. of Meeting...\" id=\"tc-no-of-meeting\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>No of Present</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"No. of Present...\" id=\"tc-no-of-present\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Result and Division in Final Exam</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Result and Division in Final Exam...\" id=\"tc-result-division-final-exam\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Subjects taken</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Subjects taken...\" id=\"tc-subjects-taken\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Rank in Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Rank in Class...\" id=\"tc-rank-in-class\">" +
            "</div>" +
        "</div>" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Medium</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Medium...\" id=\"tc-medium\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-4\">" +
                "<label>Duration: Admission date to the end of this session</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Admission date to the end of this session...\" id=\"tc-duration-in-session\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Conduct and Work in session</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Conduct and Work in Session...\" id=\"tc-conduct-and-work-in-session\">" +
            "</div>" +
        "</div>" +
        "<div class=\"col-md-12\" style=\"cursor:pointer;\" onclick=\"transferCertificate.removeTCStudentCourses(this);\">" +
            "<p style=\"color: #FF0000; text-align: right;\"><u>Delete Session Detail</u></p>" +
        "</div>" +
    "</div>";

    $("#tc-previous-session-detail-summary-list-div").append(relievingVariablesHTML);
    initDateWithYearRange("-20:+5", false);
},

  generateTC : function(studentId){
    var book_number = $("#tc-book-no").val().trim();
    var tc_number = $("#tc-serial-no").val().trim();
    var is_tc_number_fixed = $("#tc-generation-is-tc-number-fixed").text().trim();
    var tc_generation_date = 0;
    var tc_generation_date_val = getDate($("#tc-generation-date").val());
    if(tc_generation_date_val != null && tc_generation_date_val != undefined && tc_generation_date_val != "") {
      tc_generation_date = tc_generation_date_val.getTime()/1000;
    }
    var affiliation_number = $("#tc-affiliation-no").val().trim();
    var dise_code = $("#tc-dise-code").val().trim();
    var school_code = $("#tc-school-code").val().trim();
    var primary_education_affiliation_number = $("#tc-pri-edu-affli-no").val().trim();
    var apaar_id_number = $("#tc-apaar-id-no").val().trim();
    var rte_affiliation_number = $("#tc-rte-affli-no").val().trim();

    var admission_number = $("#tc-admission-no").val().trim();
    var student_name = $("#tc-student-name").val().trim();
    var admission_date = 0;
    var admission_date_val = getDate($("#tc-admission-date").val());
    if(admission_date_val != null && admission_date_val != undefined && admission_date_val != "") {
      admission_date = admission_date_val.getTime()/1000;
    }
    var admission_class = $("#tc-admission-class").val().trim();
    var father_guardian_name = $("#tc-father-name").val().trim();
    var mother_name = $("#tc-mother-name").val().trim();
    var proofOfDoBAtTheTimeOfAdmission = $("#tc-proof-of-dob-at-the-time-of-admission").val().trim();
    var living_time_in_currect_city = $("#tc-living-time-in-currect-city").val().trim();
    var dob = 0;
    var dob_val = getDate($("#tc-dob").val());
    if(dob_val != null && dob_val != undefined && dob_val != "") {
      dob = dob_val.getTime()/1000;
    }
    var category = $("#tc-category").val().trim();
    var nationality = $("#tc-nationality").val().trim();

    var last_active_session_class = $("#tc-last-session-class").val().trim();
    var lastActiveSessionClassInWords = $("#tc-last-session-class-name-in-words").val().trim();
    var lastActiveSessionClassInFigures = $("#tc-last-session-class-name-in-figures").val().trim();
    var last_exam_taken_with_result = $("#tc-last-exam").val().trim();
    var number_of_time_exam_failed = $("#tc-last-exam-failed").val().trim();
    var scholastic_courses_last_active_session_str = transferCertificate.getTCSubject();
    var promotion_to_higher_class = $("#tc-getting-promotion").val().trim();
    var promoting_class_name = $("#tc-promoting-class").val().trim();
    var promotingClassNameInWords = $("#tc-promoting-class-name-in-words").val().trim();
    var promotingClassNameInFigures = $("#tc-promoting-class-name-in-figures").val().trim();
    var last_fees_paid = $("#tc-last-fees-paid").val().trim();
    var discount_with_nature = $("#tc-discount").val().trim();
    var total_working_days = $("#tc-total-working-days").val().trim();
    var total_attended_days = $("#tc-total-attended-days").val().trim();

    var ncc_cadet_boy_scout_girl_guide_with_details = $("#tc-ncc-cadet").val().trim();
    var co_curricular_activities = $("#tc-cocurricular-activities").val().trim();

    var code_of_conduct = $("#tc-code-of-conduct").val();
    var relieve_date = 0;
    var relieve_date_val = getDate($("#tc-relieve-date").val());
    if(relieve_date_val != null && relieve_date_val != undefined && relieve_date_val != "") {
      relieve_date = relieve_date_val.getTime()/1000;
      if(relieve_date > tc_generation_date) {
        showErrorDialogBox("Relieved Date cannot be greater than TC Generation Date.");
        return;
      }
    }
    var relieve_reason = $("#tc-relieve-reason").val().trim();
    var remarks = $("#tc-remarks").val().trim();
    var dateOfApplicationOfCertificate = 0;
    var date_of_application_of_certificate = getDate($("#tc-date-of-application-of-certificate").val());
    if(date_of_application_of_certificate != null && date_of_application_of_certificate != undefined && date_of_application_of_certificate != "") {
      dateOfApplicationOfCertificate = date_of_application_of_certificate.getTime()/1000;
    }
    var datePupilsNameStuckOffTheRolls = 0;
    var date_pupils_name_stuck_off_the_rolls = getDate($("#tc-date-pupils-name-stuck-off-the-rolls").val());
    if(date_pupils_name_stuck_off_the_rolls != null && date_pupils_name_stuck_off_the_rolls != undefined && date_pupils_name_stuck_off_the_rolls != "") {
      datePupilsNameStuckOffTheRolls = date_pupils_name_stuck_off_the_rolls.getTime()/1000;
    }

    var prev_session_details_list = JSON.stringify(transferCertificate.getTcStudentSessionDetails());
    var queryParamsStr = "?book_number="+book_number+"&tc_number="+tc_number+"&is_tc_number_fixed="+is_tc_number_fixed+"&tc_generation_date="+tc_generation_date+"&affiliation_number="+affiliation_number+"&dise_code="+dise_code+"&school_code="+school_code+"&primary_education_affiliation_number="+primary_education_affiliation_number+"&apaar_id_number="+apaar_id_number+"&rte_affiliation_number="+rte_affiliation_number+"&admission_number="+admission_number+"&student_name="+student_name+"&admission_date="+admission_date+"&admission_class="+admission_class+"&father_guardian_name="+father_guardian_name+"&mother_name="+mother_name+"&dob="+dob+"&category="+category+"&nationality="+nationality+"&last_active_session_class="+last_active_session_class+"&last_exam_taken_with_result="+last_exam_taken_with_result+"&number_of_time_exam_failed="+number_of_time_exam_failed+"&scholastic_courses_last_active_session_str="+scholastic_courses_last_active_session_str+"&promotion_to_higher_class="+promotion_to_higher_class+"&promoting_class_name="+promoting_class_name+"&last_fees_paid="+last_fees_paid+"&discount_with_nature="+discount_with_nature+"&total_working_days="+total_working_days+"&total_attended_days="+total_attended_days+"&ncc_cadet_boy_scout_girl_guide_with_details="+ncc_cadet_boy_scout_girl_guide_with_details+"&co_curricular_activities="+co_curricular_activities+"&code_of_conduct="+code_of_conduct+"&relieve_date="+relieve_date+"&relieve_reason="+relieve_reason+"&remarks="+remarks+"&proof_of_dob_at_the_time_of_admission="+proofOfDoBAtTheTimeOfAdmission+"&last_active_session_class_in_figures="+lastActiveSessionClassInFigures+"&last_active_session_class_in_words="+lastActiveSessionClassInWords+"&promoting_class_name_in_figures="+promotingClassNameInFigures+"&promoting_class_name_in_words="+promotingClassNameInWords+"&date_of_application_of_certificate="+dateOfApplicationOfCertificate+"&date_pupils_name_stuck_off_the_rolls="+datePupilsNameStuckOffTheRolls+"&prev_session_details_list="+prev_session_details_list+"&living_time_in_currect_city="+living_time_in_currect_city;
    console.log(queryParamsStr)
      window.open(baseURL + "/admission/pdf-tc/" + studentId + queryParamsStr, '_blank');
      returnToMainScreen();
  },

  clearTCVariables : function () {
    var isTCCounterEnabled = $("#tc-counter-enable").text();
    $("#tc-affiliation-no").val("");
    if(isTCCounterEnabled === 'false') {
      $("#tc-serial-no").val("");
    }
    $("#tc-book-no").val("");
    $("#tc-generation-date").val("");
    $("#tc-affiliation-no").val("");
    $("#tc-dise-code").val("");
    $("#tc-school-code").val("");
    $("#tc-pri-edu-affli-no").val("");
    $("#tc-apaar-id-no").val("");
    $("#tc-living-time-in-currect-city").val("");
    $("#tc-rte-affli-no").val("");
    $("#tc-admission-no").val("");
    $("#tc-student-name").val("");
    $("#tc-admission-date").val("");
    $("#tc-admission-class").val("");
    $("#tc-father-name").val("");
    $("#tc-mother-name").val("");
    $("#tc-proof-of-dob-at-the-time-of-admission").val("");
    $("#tc-dob").val("");
    $("#tc-category").val("");
    $("#tc-nationality").val("");
    $("#tc-last-session-class").val("");
    $("#tc-last-exam").val("");
    $("#tc-last-exam-failed").val("");
    $(".tc-student-courses").val("");
    $("#tc-getting-promotion").val("");
    $("#tc-promoting-class").val("");
    $("#tc-last-fees-paid").val("");
    $("#tc-discount").val("");
    $("#tc-total-working-days").val("");
    $("#tc-total-attended-days").val("");
    $("#tc-ncc-cadet").val("");
    $("#tc-cocurricular-activities").val("");
    $("#tc-code-of-conduct").val("");
    $("#tc-relieve-date").val("");
    $("#tc-relieve-reason").val("");
    $("#tc-remarks").val("");
  },

  resetTCVariables : function (studentId) {
    ajaxClient.get("/admission/student-tc-field-details/"+studentId, function(data) {
      $('#tc-outer-div').html(data);
      $("#tc-generation-student-id").text(studentId);
      initDateWithYearRange("-20:+5", false);
      initPastDateById("tc-generation-date", 2000);
      initPastDateById("tc-relieve-date", 2000);
    });
  },

};


var certificate = {

  loadStudentPaymentDetails : function (studentId) {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    window.open(baseURL + "/admission/generate-study-certificate/" + sessionId + "/" + studentId, '_blank');
  }
}

function advanceSearch () {
  $("#advance-search-option-div").toggle();
}

/**
 * Calculates in percent, the change between 2 numbers.
 * e.g from 1000 to 500 = 50%
 *
 * @param oldNumber The initial value
 * @param newNumber The value that changed
 */
function getPercentageChange(oldNumber, newNumber){
  return parseFloat((((newNumber - oldNumber) * 100) / oldNumber).toFixed(1));
}

function getPercentage(number, total){
  return parseFloat(((number / total) * 100).toFixed(1));
}
function isObjectEmpty(object) {
  return Object.keys(object).length === 0 && object.constructor === Object;
}

var admissionEnquiry = {

    dataCache : {
       },

    loadMainScreen : function(actionType) {
        ajaxClient.get("/admission/enquiry-home", function(data) {
           $("#main-content").html(data);
           admissionEnquiry.viewEnquiryDetails(true, "SUBMITTED");
           enquiryPagination.dataCache['status'] = "SUBMITTED";
           academicSessionHandler.bindSessionChangeEvent(admissionEnquiry.loadEnquiryDetailsForSession);
           admissionEnquiry.bindEnquiryClickEvents();
           admissionEnquiry.dataCache = {};
           initDate(5 * 365);
        });
    },

    loadEnquiryDetailsForSession : function() {
         admissionEnquiry.viewEnquiryDetails(true, "SUBMITTED");
    },

    backButton: function(){
       $(".student-details-screen").css("display", "block");
       var status = admissionEnquiry.dataCache.screen
       var sessionId = academicSessionHandler.getSelectedSessionId();
       var filterDate = admissionEnquiry.dataCache.latestFollowupDate;

       var pageNumber = admissionEnquiry.dataCache.pageNumber;
       var itemsPerPage = admissionEnquiry.dataCache.itemsPerPage;
       if(filterDate == null) {
         filterDate = 0;
       }

       ajaxClient.get("/admission/enquiry-details/session/" + sessionId + "/" + status + "/" + 0 + "/" + 0  + "?filter_date=" + filterDate, function(data) {
             $("#enquiryDetailsResult").html(data);
             var screenHeight = $("#sidebar").height() - $("#enquiryDetailsResult").position().top - 300;
             $('#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper').attr("style", "height:" + screenHeight + "px;  overflow-y: scroll; cursor: pointer;");
            //  var response = JSON.parse($("#pagination-info").text().trim());
            //  $('#items-per-page').val(response.itemsPerPage);
            //  $('.page-item').removeClass('active');
            //  var pageNumber = (response.offset / response.itemsPerPage) + 1;
            //  $('#page-number-' + 1).addClass('active');
            initDateWithYearRange("-2:+2", false);
            if(filterDate != 0){
              $("#latest-followup-date").val(getFormattedDate(filterDate))
            }

            if ($.fn.DataTable.isDataTable("." + status.toLowerCase() + "-datatables-reponsive")) {
               $("." + status.toLowerCase() + "-datatables-reponsive").DataTable().destroy();
            }
            $('#DataTables_Table_3_length select').val(itemsPerPage).trigger('change')


             admissionEnquiry.highlightActiveTab(status);
             admissionEnquiry.bindEnquiryClickEvents();
             enquiryPagination.initPagination();
             var dataTable = $("." + status.toLowerCase() + "-datatables-reponsive").DataTable({
                     searching: true,
                     columnDefs: [
                         { orderable: false, targets: "no-sort" },
                     ],
                     order: [[1, 'asc']],
                     pageLength: parseInt(itemsPerPage),
                     initComplete: function() {
                         var api = this.api();
                         var totalPages = api.page.info().pages;
                         if (pageNumber > totalPages) {
                             pageNumber = Math.max(1, totalPages);
                         }
                         api.page(pageNumber - 1).draw('page');
                         $('.pagination .page-item').removeClass('active');
                         $('.pagination .page-item a[data-dt-idx="' + pageNumber + '"]').parent().addClass('active');
                     }
             });
       });
    },


    viewEnquiryDetails : function(freshSearch, status) {
        enquiryPagination.dataCache['status'] = status;
        var filterDate = getDate($("#latest-followup-date").val());
        if(filterDate != null){
          filterDate = filterDate.getTime()/1000;
        } else {
          filterDate = 0;
        }
        if(status != "ACCEPTED"){
          filterDate = 0;
        }
        var page_number = $('.page-item.active').find('.page-number').text().trim();
        if (freshSearch) {
          page_number = 1;
        }
        var itemsPerPage = $('#items-per-page').val();
        itemsPerPage = 0;
        if (!page_number) {
          page_number = 1;
        }
        var offset = (page_number - 1) * itemsPerPage;
        var sessionId = academicSessionHandler.getSelectedSessionId();
        $("#enquiryDetailsResult").html("");
        ajaxClient.get("/admission/enquiry-details/session/" + sessionId + "/" + status + "/" + offset + "/" + itemsPerPage + "?filter_date=" + filterDate, function(data) {
          $("#enquiryDetailsResult").html(data);
          var screenHeight = $("#sidebar").height() - $("#enquiryDetailsResult").position().top - 300;
          $('#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper').attr("style", "height:" + screenHeight + "px;  overflow-y: scroll; cursor: pointer;");
          // var response = JSON.parse($("#pagination-info").text().trim());
          // $('#items-per-page').val(response.itemsPerPage);
          // $('.page-item').removeClass('active');
          // var pageNumber = (response.offset / response.itemsPerPage) + 1;
          // $('#page-number-' + page_number).addClass('active');

          admissionEnquiry.highlightActiveTab(status);
          admissionEnquiry.bindEnquiryClickEvents();
          admissionEnquiry.dataCache = {};
          admissionEnquiry.dataCache['screen'] = status;
          enquiryPagination.initPagination();
          initDateWithYearRange("-2:+2", false);
          if(filterDate !=0){
            $("#latest-followup-date").val(getFormattedDate(filterDate))
          }

           $("." + status.toLowerCase() + "-datatables-reponsive").DataTable({
              searching: true,
              columnDefs: [
                { orderable: false, targets: "no-sort" },
            ],
             order: [[1, 'asc']]
          });
          
        });
      },


      highlightActiveTab: function(status) {
          $("#nav-submitted-enquiry-tab, #nav-accepted-enquiry-tab, #nav-rejected-enquiry-tab, #nav-closed-enquiry-tab").removeClass('active');
          $("#submitted-enquiry-content, #accepted-enquiry-content, #rejected-enquiry-content, #closed-enquiry-content").removeClass('active');
          switch (status) {
            case 'SUBMITTED':
              $("#nav-submitted-enquiry-tab").addClass('active');
              $("#submitted-enquiry-content").addClass('active');
              break;
            case 'ACCEPTED':
              $("#nav-accepted-enquiry-tab").addClass('active');
              $("#accepted-enquiry-content").addClass('active');
              break;
            case 'REJECTED':
              $("#nav-rejected-enquiry-tab").addClass('active');
              $("#rejected-enquiry-content").addClass('active');
              break;
            case 'CLOSED':
              $("#nav-closed-enquiry-tab").addClass('active');
              $("#closed-enquiry-content").addClass('active');
              break;
          }
      },


      bindEnquiryClickEvents: function() {
          $(".view-enquiry-details").on('click', function() {
            var enquiryId = $(this).attr("id").trim();
            admissionEnquiry.showEnquiryDetails(enquiryId);
          });
      },

      showEnquiryDetails: function(enquiryId) {
          var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
          var enquiry = JSON.parse(enquiryDetails);
          admissionEnquiry.populateEnquiryDetails(enquiry);
          $("#view-enquiry-details-modal").modal('toggle');
      },

      populateEnquiryDetails: function(enquiry) {
          admissionEnquiry.resetFields();
          $('#tracking-id').text(enquiry.trackingId);
          $('#parent-name').text(enquiry.guardianName);
          $('#child-name').text(enquiry.childName);
          $('#standard').text(enquiry.standardName);
          $('#phone-number').text(enquiry.guardianContactInfo);
          $('#email-id').text(enquiry.guardianEmailId);
          $('#message').text(enquiry.message);
          $('#successful').text(enquiry.successful);
          if(enquiry.studentDetailedRow != null){
             $('#student').text(enquiry.studentDetailedRow.studentBasicInfo.name)
             $('#student-adm-no').text(enquiry.studentDetailedRow.studentBasicInfo.admissionNumber)
          }
          if(enquiry.enquiryStatus !== "CLOSED"){
            $('#successful').closest('tr').css('display', 'none');
            $('#student').closest('tr').css('display', 'none');
            $('#student-adm-no').closest('tr').css('display', 'none');

          }
      },

      resetFields: function(){
         $('#tracking-id').text('');
         $('#parent-name').text('');
         $('#child-name').text('');
         $('#standard').text('');
         $('#phone-number').text('');
         $('#email-id').text('');
         $('#message').text('');
         $('#successful').text('');
         $('#student').text('')
         $('#student-adm-no').text('')
         $('#successful').closest('tr').css('display', '');
         $('#student').closest('tr').css('display', '');
         $('#student-adm-no').closest('tr').css('display', '');

      },

       showEnquiry: function(actionType, buttonElement) {
         var enquiryId = $(buttonElement).attr("id").trim();
         var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
         var enquiry = JSON.parse(enquiryDetails);
         $('#accept-action-button').hide();
         $('#reject-action-button').hide();
         $('#complete-action-button').hide();
         $('#enquiry-id-action-div').text(enquiry.enquiryId);

         if (actionType === 'ACCEPTED') {
           $('#accept-action-button').show();
         } else if (actionType === 'REJECTED') {
           $('#reject-action-button').show();
         } else if (actionType === 'COMPLETED') {
           $('#complete-action-button').show();
         }
         $('#reason-outcome').val('');
         $("#enquiry-details-action-modal").modal('toggle');
       },

      updateEnquiryStatus: function(status, toggleElement, reason, enquiryId) {
         var reasonValue = $("#" + reason).val();
         var enquiry_id = $('#' + enquiryId).text();
         admissionEnquiry.dataCache = {
           reason: reasonValue,
           status: status,
           enquiryId: enquiry_id,
           screen: admissionEnquiry.dataCache.screen
         };

         $("#" + toggleElement).modal('toggle');
         admissionEnquiry.updateActionModalContent(status);
         $("#enquiry-action-modal").modal('toggle');
       },

      updateActionModalContent: function(status) {
          var title = "";
          var html = "";

          if (status === 'REJECTED') {
            title = "Reject Enquiry";
            html = "Do you wish to reject the enquiry?";
          } else if (status === 'CLOSED') {
            title = "Close Enquiry";
            html = "Do you wish to close the enquiry?";
          } else {
            title = "Accept Enquiry";
            html = "Do you wish to accept the enquiry?";
          }

          $("#enquiry-title").text(title);
          $("#enquiry-details-manage-id").html(html);
      },

      confirmAction: function() {
          var reason = admissionEnquiry.dataCache.reason;
          var status = admissionEnquiry.dataCache.status;
          var enquiry_id = admissionEnquiry.dataCache.enquiryId;
          var isSuccessful = false;
          var studentId = ""
          if(admissionEnquiry.dataCache.status == "CLOSED"){
          isSuccessful = admissionEnquiry.dataCache.isSuccessful
          if(admissionEnquiry.dataCache.studentId != undefined || admissionEnquiry.dataCache.studentId === ""){
          studentId = admissionEnquiry.dataCache.studentId}
          }
          var academicSessionId = academicSessionHandler.getSelectedSessionId();

          $("#enquiry-action-modal").modal('toggle');
          var enquiryDetailsPayload = { 'academicSessionId' : academicSessionId ,'enquiryId' : enquiry_id, 'studentId' : studentId, 'message': reason, 'enquiryStatus' : status, 'successful' : isSuccessful};

          ajaxClient.post("/admission/update-enquiry-status", {'enquiryDetailsPayload' : JSON.stringify(enquiryDetailsPayload)}, function(data) {
            $("#enquiry-status-modal-container").html(data);
            $("#update-enquiry-status-modal").modal({ backdrop: 'static', keyboard: false });
            admissionEnquiry.viewEnquiryDetails(true, admissionEnquiry.dataCache.screen)
          });
      },

     showDetails : function(enquiryId, update = false){
     if(!update) {
        var latestFollowupDate = getDate($("#latest-followup-date").val());
        if(latestFollowupDate != null){
          latestFollowupDate = latestFollowupDate.getTime()/1000;
        }
        var page_number =  $('.dataTables_paginate .paginate_button.active a').text();
        var itemsPerPage = $('div.dataTables_length select').val();
        admissionEnquiry.dataCache['latestFollowupDate'] = latestFollowupDate
        admissionEnquiry.dataCache['pageNumber'] = page_number
        admissionEnquiry.dataCache['itemsPerPage'] = itemsPerPage
     }

        var sessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/admission/enquiry-detail/session/" + sessionId + "/" + enquiryId, function(data) {
                  $("#enquiryDetailsResult").html(data);
                  $(".student-details-screen").css("display", "none");
                  admissionEnquiry.bindEnquiryClickEvents();
                  enquiryPagination.initPagination();
        });

     },

     editFollowUpDetails : function(value){
          initDateWithYearRange("-10:+10", true);
          initPastDateById("enquiry-follow-up-date", 4000);
          var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
          $("#update-transaction-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
          $("#update-enquiry-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
          $("#update-transaction-follow-up-mode").html("<option>"+followUpPayload.followUpMode+"</option>");
          $("#update-transaction-contact-person-name").val(followUpPayload.contactPersonName);
          $("#update-transaction-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
          $("#update-transaction-conversation").val(followUpPayload.conversation);

          if(followUpPayload.nextFollowUpDate != null){
            $("#update-next-enquiry-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
          }
          else{
             $("#update-next-enquiry-follow-up-date").val("");
          }

          $("#update-transaction-amount").val(followUpPayload.amount);
          $("#update-enquiry-follow-up-id").text(followUpPayload.followUpId);
          $("#student-id").text(followUpPayload.entityId);
          $("#update-enquiry-follow-up-modal").modal('toggle');
     },

     closeEnquiry : function(buttonElement){
           var enquiryId = $(buttonElement).attr("id").trim();
           var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
           var enquiry = JSON.parse(enquiryDetails);
           $('#enquiry-outcome-id-action-div').text(enquiry.enquiryId);
           $('#outcome').val('');
           $('#is-successful').prop('checked', false);
           $(".enquiry_selected_student_row").remove();
           $('#enquiry-students-search').prop('disabled', true);
           $("#close-details-action-modal").modal('toggle');
           var academicSessionId = academicSessionHandler.getSelectedSessionId();
           admissionEnquiry.bindSearchStudentEvent();
     },

     bindSearchStudentEvent : function () {
           var resultArea = "#enquiry-students-search-result";
           $("#enquiry-students-search").on('keyup', function (e) {
             if (e.keyCode == 13) {
                 admissionEnquiry.doneStudentSearchTyping(resultArea);
             }
           });
           liveSearchHandler.bindEvent('#enquiry-students-search', resultArea, admissionEnquiry.doneStudentSearchTyping);
         },

     doneStudentSearchTyping: function(resultArea) {
           var searchText = $('#enquiry-students-search').val().trim();
           var status = "ENROLLED";
           admissionEnquiry.studentLiveSearchEvent(searchText, resultArea, admissionEnquiry.loadStudentDetails, status);
     },

     studentLiveSearchEvent : function (searchText, resultArea, triggerMethod, status){
           var academicSessionId =  academicSessionHandler.getSelectedSessionId();
           ajaxClient.get("/admission/enquiry-student-live-search/" + academicSessionId + "?searchText=" + searchText + "&status=" + status, function(data) {
                 $(resultArea).html(data);
                 admissionEnquiry.loadStudentDetails();
           });
     },

     loadStudentDetails: function () {
           $("#live-search-student-results .student-result-row .student-details").on("click", function () {
                var studentDetails = JSON.parse($(this).closest(".student-result-row").find(".student-info-div").text().trim());
                admissionEnquiry.addSelectedStudentRow(studentDetails);
           });
     },


     addSelectedStudentRow : function (studentDetails) {
            var name = studentDetails.name;
            var admissionNumber = studentDetails.admissionNumber;
            var fatherName = siblingDetails.checkIfStringEmpty(studentDetails.fathersName) ? "-" : studentDetails.fathersName;
            var standardName = studentDetails.studentSessionData.standardNameWithSection;
            var studentId = studentDetails.studentId;
             closeButtonHTML = " <button type=\"button\" class=\"close delete-student-row \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";

            $(".enquiry_selected_student_row").remove();
            var divHTML = "<div class=\"enquiry_selected_student_row " +
                            "\" id=\"" + studentId + "\" style=\"display: flex; justify-content: space-between; align-items: center; padding: 10px;\">" +
                            "<div class=\"student-name-div\" style=\"; \">Student: " +
                            name + " (" + admissionNumber + ", "+ standardName +", " + fatherName + ")" +
                            "</div>" +
                            "<div class=\"close-button-div\" style=\"margin-right: 300px; cursor: pointer;\">" +
                            closeButtonHTML +
                            "</div></div>";

            $("#enquiry-students-search-row").after(divHTML);
            $("#enquiry-students-search-result").html("");
            $("#enquiry-students-search").val("");
            siblingDetails.deletePurchaseItemEntry();
     },


     toggleStudentSelect : function() {
            const isSuccessful = $('#is-successful').is(':checked');
            $('#enquiry-students-search').prop('disabled', !isSuccessful);
            if(!isSuccessful){
                 $(".enquiry_selected_student_row").remove();
            }
     },

    closeEnquiryDetail : function(){
            var outcome = $("#outcome").val();
            var enquiry_id = $('#enquiry-outcome-id-action-div').text();
            var isSuccessful = $('#is-successful').is(':checked');
            if (isSuccessful) {
                        var studentId = $(".enquiry_selected_student_row").attr('id');
            }
            admissionEnquiry.dataCache = {
                  reason: outcome,
                  status: "CLOSED",
                  enquiryId: enquiry_id,
                  isSuccessful: isSuccessful,
                  studentId: studentId,
                  screen: admissionEnquiry.dataCache.screen
              };

             $("#close-details-action-modal").modal('toggle');
             admissionEnquiry.updateActionModalContent("CLOSED");
             $("#enquiry-action-modal").modal('toggle');
    }
};

var enquiryPagination = {
  dataCache : {},

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(true, status);
      }
    );
  },
};
