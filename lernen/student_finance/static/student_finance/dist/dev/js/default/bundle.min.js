$(document).ready(function(){menuLoader.sidebarMenu(),homePage.initHomePage()});var menuLoader={sidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.homeMenu(),menuLoader.studentFinanceDetailsMenu()},homeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},studentFinanceDetailsMenu:function(){$("#studentFinanceManagementNav").on("click",function(){manageStudentExpenseDetails.loadStudentFinanceDetailsPage("")})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/student-finance/home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/student-finance/session-home/"+e,function(e){$("#student-finance-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()}},manageStudentExpenseDetailsReport={bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(e))){var t=$(e).find("p.report-type").text().trim(),n=-1,a=-1,s=academicSessionHandler.getSelectedSessionId(),d=$(e).find("p.report-type-view-student-id").text().trim();$(e).find("input.student-expense-start-date").length>0&&""!=$(e).find("input.student-expense-start-date").val()&&(n=getDate($(e).find("input.student-expense-start-date").val()).getTime()/1e3),$(e).find("input.student-expense-end-date").length>0&&""!=$(e).find("input.student-expense-end-date").val()&&(a=getDate($(e).find("input.student-expense-end-date").val()).getTime()/1e3),-1!=a&&-1!=n?-1!=a&&-1!=n&&a<n?showErrorDialogBox("End date cannot be less than start date"):($(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/student-finance/generate-report/"+t+"?sessionId="+s+"&startDate="+n+"&endDate="+a+"&studentId="+d,"_blank")):showErrorDialogBox("Please Select Both Start Date and End Date")}})}},manageStudentExpenseDetails={dataCache:{},loadStudentFinanceDetailsPage:function(){ajaxClient.get("/student-finance/student-finance-details",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(manageStudentExpenseDetails.changeSession),bindSearchStudentEvent()})},changeSession:function(){$("#student-expense-search-text").val(""),$("#student-expense-screen").html(""),manageStudentExpenseDetails.dataCache={}},doneStudentSearchTyping:function(e){var t=$("#student-expense-search-text").val().trim(),n=academicSessionHandler.getSelectedSessionId();manageStudentExpenseDetails.studentLiveSearchEvent(n,t,e,loadStudentExpenseDetails,"ENROLLED")},studentLiveSearchEvent:function(e,t,n,a,s){ajaxClient.get("/student-finance/student-live-search/"+e+"?searchText="+t+"&status="+s,function(e){$(n).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(n,a)})},addStudentExpense:function(){$("#add-expense-action-modal").modal("toggle"),addStudentExpenseHandler.init()}};function bindSearchStudentEvent(){var e="#student-expense-search-result";$("#student-expense-search").on("click",function(){manageStudentExpenseDetails.doneStudentSearchTyping(e)}),$("#student-expense-search-text").on("keyup",function(t){13==t.keyCode&&manageStudentExpenseDetails.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#student-expense-search-text",e,manageStudentExpenseDetails.doneStudentSearchTyping)}function loadStudentExpenseDetails(e){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/student-finance/student-expense-details/"+t+"/"+e,function(e){$("#student-expense-screen").html(e),initDate(1825);var t=Date.now();$("#expense-transaction-date").val(getFormattedDate(t/1e3)),$("#generate-student-finance-popup").on("click",function(){$("#student-expense-report-with-date-range-modal").modal("toggle")}),manageStudentExpenseDetailsReport.bindGenerateReportEvent()})}var addStudentExpenseHandler={servicesByCategory:{},init:function(){addStudentExpenseHandler.cacheServicesByCategory(),addStudentExpenseHandler.bindCategoryChangeEvent()},cacheServicesByCategory:function(){addStudentExpenseHandler.servicesByCategory={};var e=$("#services-json").text(),t=[];try{t=JSON.parse(e)}catch(e){return void console.error("Invalid services JSON",e)}t.forEach(function(e){var t=e.serviceCategory;addStudentExpenseHandler.servicesByCategory[t]||(addStudentExpenseHandler.servicesByCategory[t]=[]),addStudentExpenseHandler.servicesByCategory[t].push(e)})},bindCategoryChangeEvent:function(){$("#student-service-category").on("change",function(){var e=$(this).val(),t=$("#service");t.empty().append('<option value=""></option>'),(addStudentExpenseHandler.servicesByCategory[e]||[]).forEach(function(e){t.append($("<option>",{value:e.serviceId,text:e.serviceName}))})})},confirmAddExpance:function(){if(!validations($("#add-expense-action-modal"))){var e=readJson("#student-finance-preferences-json"),t=readJson("#student-wallet-preferences-json"),n=academicSessionHandler.getSelectedSessionId(),a=$("#fee-assign-view-student-id").text().trim(),s=$("#service").val(),d=$("#payment-mode").val(),i=parseFloat($("#expense-amount").val())||0;if(isCreditAllowed(i,parseFloat($("#available-wallet-amount-display").text())||0,e,t)){var o=getDate($("#expense-transaction-date").val()),r=null;o&&(r=o.getTime()/1e3);var l={academicSessionId:n,serviceId:s,studentId:a,transactionMode:d,paidAmount:i,transactionDate:r,description:$("#description").val(),reference:$("#transaction-refrence").val()};$("#add-expense-action-modal").modal("toggle"),ajaxClient.post("/student-finance/add-student-expense-transaction",{serviceExpensePayload:JSON.stringify(l)},function(e){$("#student-expense-status-modal-container").html(e),$("#student-expense-status-modal").modal("toggle")})}}}};function isCreditAllowed(e,t,n,a){const s=Math.max(t,0),d=e-s;return t>=e||(n.allowWalletBasedCredit?!(t-s-d<-a.walletBasedCreditLimit)||(showErrorDialogBox("Cannot give more credit then allowed max limit"),$("#add-expense-action-modal").modal("toggle"),resetAddExpenseModal(),!1):(showErrorDialogBox("Credit on wallet for student finance is not enabled for institute."),$("#add-expense-action-modal").modal("toggle"),resetAddExpenseModal(),!1))}function resetAddExpenseModal(){$("#student-service-category").val(""),$("#service").empty().append('<option selected value=""></option>'),$("#expense-amount").val(""),$("#description").val(""),$("#transaction-refrence").val(""),$("#payment-mode").val("WALLET")}function validations(e){clearValidationErrorDisplay();var t=!1;return $(e).find("input.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),t=!0)}),$(e).find("select.mandatory-field").each(function(){0!=$(this).find(":selected").length&&""!=$(this).find(":selected").val().trim()||($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),t=!0)}),$(e).find("input.mandatory-table-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-table-field-text"> <span style="color:#e65f76;">Mandatory</span></p>'),t=!0)}),$(e).find("input.positive-number").each(function(){$(this).val()<0&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-text"> <span style="color:#e65f76;">Invalid</span></p>'),t=!0)}),t}function clearValidationErrorDisplay(){$(".hour-range").css("border",""),$(".invalid-input-text").remove(),$(".mandatory-field").css("border",""),$(".mandatory-field-text").remove(),$(".mandatory-table-field").css("border",""),$(".mandatory-table-field-text").remove()}