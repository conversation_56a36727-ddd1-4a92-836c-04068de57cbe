  var typingTimer; //timer identifier
var doneTypingInterval = 500; //time in ms, 5 second for example
var baseURL = getBaseURL();
var GET_CALL_TIMEOUT = 6000000;
var POST_CALL_TIMEOUT = 6000000;
var FILE_UPLOAD_TIMEOUT = 6000000;
// var SMS_CHAR_LIMIT_ONE_CREDIT = 160;
// var SMS_UNICODE_CHAR_LIMIT_ONE_CREDIT = 70;
var STANDARD_SMS_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT = 153;
var SMS_UNICODE_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT = 67;
var ONLINE_LECTURE_DISCUSSION = "ONLINE_LECTURE_DISCUSSION";
var HOMEWORK_DISCUSSION = "HOMEWORK_DISCUSSION";
var ADMIN = "ADMIN"
var SYSTEM = "SYSTEM"
var INTERNAL = "INTERNAL"
var STUDENT = "STUDENT"
var ONLINE_LECTURE = "ONLINE_LECTURE"
var HOMEWORK = "HOMEWORK";
var NOTICE = "NOTICE";
var STUDENT_LEAVE_APPLICATION = "STUDENT_LEAVE_APPLICATION";
var STAFF_LEAVE_APPLICATION = "STAFF_LEAVE_APPLICATION";
var PRIMARY_LOGO_COLOR = "#43a2ad";
var STAFF = "STAFF";
var TABULAR = "TABULAR";
var GRAPHICAL = "GRAPHICAL";
var RUPEE_SYMBOL = "₹";
var ACTION_TYPE = 'action_type';


function getBaseURL() {
    return getRawBaseURL(true);
}

function getRawBaseURL(includeInstituteId) {
    var getUrl = window.location;
    var basePath = getUrl.protocol + "//" + getUrl.host;
    var pathTokens = getUrl.pathname.split("/")

    if(!includeInstituteId){
       return basePath
    }

    if(pathTokens.length > 1){
      var institue_code = pathTokens[1]
      var uuidMatch = institue_code.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
      if(uuidMatch != null){
        return basePath + "/"+institue_code
      }
    }
    return basePath
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}


var themeHandler = {

    loadTheme : function (themeType) {
        var yes = confirm("Updating the theme will reload the entire page. All unsaved work will be lost. Do you want to proceed?");
        if(!yes){
          return;
        }
        ajaxClient.getWithoutInstituteCode("/core/update-theme/"+themeType, function(data){location.reload();});
    }
}

var paymentReminder = {
  reminderRequired : false,

  readPaymentState : function () {
    var json  = $("#payment-status").text().trim();
    if(json == ""){
      paymentReminder.reminderRequired = false;
      return;
    }

    var paymentStatus = JSON.parse(json);
    if(paymentStatus.status == "PAID" || paymentStatus.reminder_level != "RED"){
      paymentReminder.reminderRequired = false;
      return;
    }else{
      paymentReminder.reminderRequired = true;
      paymentReminder.scheduleReminder();
      return;
    }
  },

  scheduleReminder : function () {
    if(paymentReminder.reminderRequired){
      setInterval(function() {
        $("#payment-reminder-modal").modal({backdrop: 'static', keyboard: false});
      }, 5000);
    }
  }
}

var ajaxClient = {

    get: function(path,successMethod, skipLoader = false) {
        displayLoader(skipLoader);
        $.ajax({
            url: baseURL + path,
            type: "GET",
            // dataType: "json",
            timeout: GET_CALL_TIMEOUT,
            success: function(response) {
                // await new Promise(r => setTimeout(r, 2000));
                hideLoader(skipLoader);
                successMethod(response);
                // This is required for loading feather and font awesome icons after ajax call (html rendered via ajax)
                feather.replace();
            },
            error: function(jqXHR, textstatus, message) {
                // console.log(jqXHR);
                // console.log(jqXHR.status);
                // console.log(textstatus);
                // console.log(message);
                if(textstatus==="timeout") {
                    hideLoader(skipLoader);
                    $("#request-timeout-modal").modal({backdrop: 'static', keyboard: false});
                } else {
                    //Handle various error code properly
                    if(jqXHR.status == "403"){
                      hideLoader(skipLoader);
                      $("#session-expired-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "404"){
                      hideLoader(skipLoader);
                      $("#invalid-request-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "0"){
                      hideLoader(skipLoader);
                      $("#request-offline-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else{
                      hideLoader(skipLoader);
                      $("#server-not-responding-modal").modal({backdrop: 'static', keyboard: false});
                    }
                }
            }
        })
    },

    getWithoutInstituteCode: function(path,successMethod) {
        displayLoader();
        $.ajax({
            url: getRawBaseURL(false) + path,
            type: "GET",
            // dataType: "json",
            timeout: GET_CALL_TIMEOUT,
            success: function(response) {
                hideLoader();
                successMethod(response);
                // This is required for loading feather and font awesome icons after ajax call (html rendered via ajax)
                feather.replace();
            },
            error: function(jqXHR, textstatus, message) {
                // console.log(jqXHR);
                // console.log(jqXHR.status);
                // console.log(textstatus);
                // console.log(message);
                if(textstatus==="timeout") {
                    hideLoader();
                    $("#request-timeout-modal").modal({backdrop: 'static', keyboard: false});
                } else {
                    //Handle various error code properly
                    if(jqXHR.status == "403"){
                      hideLoader();
                      $("#session-expired-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "404"){
                      hideLoader();
                      $("#invalid-request-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "0"){
                      hideLoader();
                      $("#request-offline-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else{
                      hideLoader();
                      $("#server-not-responding-modal").modal({backdrop: 'static', keyboard: false});
                    }
                }
            }
        })
    },

    post: function(path, payload, successMethod) {
        displayLoader();
        $.ajax({
            url: baseURL + path,
            type: "POST",
            data: payload,
            // dataType: "json",
            timeout: POST_CALL_TIMEOUT,
            success: function(response) {
                hideLoader();
                successMethod(response);
                // This is required for loading feather and font awesome icons after ajax call (html rendered via ajax)
                feather.replace();
            },
            error: function(jqXHR, textstatus, message) {
                // console.log(jqXHR);
                // console.log(jqXHR.status);
                // console.log(textstatus);
                // console.log(message);
                if(textstatus==="timeout") {
                    hideLoader();
                    $("#request-timeout-modal").modal({backdrop: 'static', keyboard: false});
                } else {
                    //Handle various error code properly
                    if(jqXHR.status == "403"){
                      hideLoader();
                      $("#session-expired-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "404"){
                      hideLoader();
                      $("#invalid-request-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else if(jqXHR.status == "0"){
                      hideLoader();
                      $("#request-offline-modal").modal({backdrop: 'static', keyboard: false});
                    }
                    else{
                      hideLoader();
                      $("#server-not-responding-modal").modal({backdrop: 'static', keyboard: false});
                    }
                }
            }
        })
     },

    uploadFile: function(path, payload, successMethod) {
         displayLoader();
         $.ajax({
             url: baseURL + path,
             type: "POST",
             enctype: 'multipart/form-data',
             data: payload,
             processData: false,
             contentType: false,
             cache: false,
             timeout: FILE_UPLOAD_TIMEOUT,
             success: function(response) {
                 hideLoader();
                 successMethod(response);
                 // This is required for loading feather and font awesome icons after ajax call (html rendered via ajax)
                 feather.replace();
             },
             error: function(jqXHR, textstatus, message) {
                 // console.log(jqXHR);
                 // console.log(jqXHR.status);
                 // console.log(textstatus);
                 // console.log(message);
                 if(textstatus==="timeout") {
                     hideLoader();
                     $("#request-timeout-modal").modal({backdrop: 'static', keyboard: false});
                 } else {
                     //Handle various error code properly
                     if(jqXHR.status == "403"){
                       hideLoader();
                       $("#session-expired-modal").modal({backdrop: 'static', keyboard: false});
                     }
                     else if(jqXHR.status == "404"){
                       hideLoader();
                       $("#invalid-request-modal").modal({backdrop: 'static', keyboard: false});
                     }
                     else if(jqXHR.status == "0"){
                       hideLoader();
                       $("#request-offline-modal").modal({backdrop: 'static', keyboard: false});
                     }
                     else{
                       hideLoader();
                       $("#server-not-responding-modal").modal({backdrop: 'static', keyboard: false});
                     }
                 }
             }
         })
      }
};

var commonUtils = {
    bindCardHoverEvent: function() {
      $(".card-hover")
        .mouseout(function() {
          $(this).removeClass("card-hover-active");
        })
        .mouseover(function() {
          $(this).addClass("card-hover-active");
      });
    },

    bindReportCardClickEvent: function() {
      $(".report-card").on('click', function() {
          $("#"+$(this).attr("id")+"-modal").modal({backdrop: 'static', keyboard: false});
          if($('.report-select-all-class').length > 0){
            $('.report-select-all-class').prop("checked",true);
            $('.select-report-student-class-menu').attr("style", "display:none");
          }
      });
    },

    bindCardClickEvent: function(element) {
      $(element).on('click', function() {
          $("#"+$(this).attr("id")+"-modal").modal({backdrop: 'static', keyboard: false});
          if($('.report-select-all-class').length > 0){
            $('.report-select-all-class').prop("checked",true);
            $('.select-report-student-class-menu').attr("style", "display:none");
          }
      });
    }

};

var reportUtils = {
    getReportHeaders : function (containerElement) {
      var requiredHeaders = [];
      $(containerElement).find('input.select-report-column').each( function () {
        var columnJson = $(this).parent().find('p.column-json').text().trim();
        var column = JSON.parse(columnJson);
        if(column.mandatory){
            requiredHeaders.push(column.key);
        }else{
          if($(this).prop("checked")){
             requiredHeaders.push(column.key);
          }
        }
      });
      return requiredHeaders;
    },

    getReportHeadersCSV : function (containerElement) {
      var requiredHeadersCSV = "";
      var requiredHeaders =  reportUtils.getReportHeaders(containerElement);
      for(var i = 0;i < requiredHeaders.length; i++){
        if(i == 0){
          requiredHeadersCSV = requiredHeaders[i];
        }else{
          requiredHeadersCSV = requiredHeadersCSV + "," + requiredHeaders[i];
        }
      }

      return requiredHeadersCSV;
    },

    getRequiredStandards: function (containerElement) {
      var requiredStandards = [];
      if($(containerElement).find(".select-report-student-class-menu").length == 0){
          return requiredStandards;
      }
      $(containerElement).find('input.select-report-class').each( function () {
            if($(this).prop("checked")){
                requiredStandards.push($(this).attr("id"));
            }
      });
      return requiredStandards;
    },

    getRequiredStandardsCSV : function (containerElement) {
      var requiredStandardsCSV = "";
      var requiredStandards =  reportUtils.getRequiredStandards(containerElement);
      for(var i = 0;i < requiredStandards.length; i++){
        if(i == 0){
          requiredStandardsCSV = requiredStandards[i];
        }else{
          requiredStandardsCSV = requiredStandardsCSV + "," + requiredStandards[i];
        }
      }

      return requiredStandardsCSV;
    },

    bindSelectClassCheckboxEvent: function () {
      $('.report-select-all-class').change(function() {
           if($(this).is(":checked")) {
             $('.select-report-student-class-menu').attr("style", "display:none");
           }
           else{
              $('.select-report-student-class-menu').attr("style", "display:block");
           }
       });
    }

};

var academicSessionHandler = {
   bindSessionChangeEvent: function(triggerMethod){
      $('.academic-year-option-button').on('click', function () {
          var academicYearJson = $(this).parent().find('p.academic-year-info').text().trim();
          var academicYear = JSON.parse(academicYearJson);
          $("#academic-year-display").text(academicYear.displayName);
          $("#selected-academic-session-json").text(academicYearJson);

          ajaxClient.getWithoutInstituteCode("/core/update-session/"+academicYear.academicSessionId, function(data){});
          // $("#original-academic-year-display-name").text(academicYear.displayName);
          // $("#academic-year-id").text(academicYear.academicSessionId);
          triggerMethod();
      });
   },

   getSelectedSession: function(){
       var academicYearJson = $("#selected-academic-session-json").text().trim();
       return JSON.parse(academicYearJson);
   },

   getSelectedSessionId: function(){
      return academicSessionHandler.getSelectedSession().academicSessionId;
   },

   getSelectedSessionDisplayName: function(){
      return academicSessionHandler.getSelectedSession().displayName;
   }
};

var liveSearchHandler = {
      bindEvent: function(search_element,resultArea, doneTyping) {
      //on keyup, start the countdown
        $(search_element).on('keyup', function(e) {
            clearTimeout(typingTimer);
            var code = e.which; // recommended to use e.which, it's normalized across browsers
            if(code == 13){
              //Assuming enter key events are handled by caller
              e.preventDefault();
              return;
            }
            typingTimer = setTimeout( function (){ doneTyping(resultArea); } , doneTypingInterval);
        });

        //on keydown, clear the countdown
        $(search_element).on('keydown', function() {
            clearTimeout(typingTimer);
        });

        $(search_element).focus(function() {
          $(resultArea).html("");
        }).blur(function() { });
     }
};

var studentLiveSearchHandler = {
  bindStudentSearchClickEvent : function(resultArea, triggerMethod) {
     studentLiveSearchHandler.bindStudentSearchClickEventMethod(resultArea, triggerMethod, false);
  },
  
  bindStudentSearchClickEventWithDisplayName : function(resultArea, triggerMethod) {
    studentLiveSearchHandler.bindStudentSearchClickEventMethod(resultArea, triggerMethod, true);
  },

  bindStudentSearchClickEventMethod : function(resultArea, triggerMethod, displayNameArg) {
    $("#live-search-student-results tbody tr").on("click", function() {
        var studentId = $(this).attr("id");
        $(resultArea).html("");
        if(displayNameArg){
          var displayName = $(this).find("td").first().text().trim();
          triggerMethod(studentId, displayName);
        }else{
          triggerMethod(studentId);
        }
    });
  }
};

var staffLiveSearchHandler = {
  bindStaffSearchClickEvent : function(resultArea, triggerMethod) {
     staffLiveSearchHandler.bindStaffSearchClickEventMethod(resultArea, triggerMethod, false);
  },

  bindStaffSearchClickEventWithDisplayName : function(resultArea, triggerMethod) {
    staffLiveSearchHandler.bindStaffSearchClickEventMethod(resultArea, triggerMethod, true);
  },

  bindStaffSearchClickEventMethod : function(resultArea, triggerMethod, displayNameArg) {
    $("#live-search-staff-results tbody tr").on("click", function() {
        var staffId = $(this).attr("id");
        $(resultArea).html("");
        if(displayNameArg){
          var displayName = $(this).find("td").first().text().trim();
          triggerMethod(staffId, displayName);
        }else{
          triggerMethod(staffId);
        }
    });
  }
};

var studentDetails = {

  fillStudentViewDetails : function(studentInfo){
    $('#detail-view\\.student-name').text(studentInfo.studentBasicInfo.name);
    $('#detail-view\\.registration-number').text(studentInfo.studentBasicInfo.registrationNumber);
    $('#detail-view\\.admission-number').text(studentInfo.studentBasicInfo.admissionNumber);
    $('#detail-view\\.pen-number').text(studentInfo.studentBasicInfo.penNumber);
    $('#detail-view\\.apaar-id-number').text(studentInfo.studentBasicInfo.apaarIdNo);
    if(studentInfo.studentAcademicSessionInfoResponse != null){
        $('#detail-view\\.standard').text(studentInfo.studentAcademicSessionInfoResponse.standard.displayName);
        if(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList != null && studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList.length > 0){
          $("#detail-view\\.section").text(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionName);
        }else{
          $("#detail-view\\.section").text("");
        }
    }else{
        $('#detail-view\\.standard').text("");
    }
    $('#detail-view\\.gender').text(studentInfo.studentBasicInfo.gender);
    if(studentInfo.studentBasicInfo.dateOfBirth != null && studentInfo.studentBasicInfo.dateOfBirth > 0){
      // dobVal = new Date(studentInfo.studentBasicInfo.dateOfBirth*1000);
      // var formattedDOB = dobVal.getUTCDate() + '-' + (dobVal.getUTCMonth() + 1)+ '-' + dobVal.getUTCFullYear()
      $('#detail-view\\.dob').text(getFormattedDate(studentInfo.studentBasicInfo.dateOfBirth));
    }else{
      $('#detail-view\\.dob').text("");

    }
    $('#detail-view\\.birth-place').text(studentInfo.studentBasicInfo.birthPlace);
    $('#detail-view\\.category').text(studentInfo.studentBasicInfo.userCategory);
    $('#detail-view\\.religion').text(studentInfo.studentBasicInfo.religion);
    $('#detail-view\\.caste').text(studentInfo.studentBasicInfo.caste);
    $('#detail-view\\.rte').text(studentInfo.studentBasicInfo.rteDisplay);
    $("#detail-view\\.mother-tongue").text(studentInfo.studentBasicInfo.motherTongue);
    $("#detail-view\\.area-type").text(studentInfo.studentBasicInfo.areaType);
    $("#detail-view\\.specially-abled").text(studentInfo.studentBasicInfo.speciallyAbledDisplay);
    $("#detail-view\\.bpl").text(studentInfo.studentBasicInfo.bplDisplay);
    $('#detail-view\\.present-address').text(getStudentPresentAddress(studentInfo.studentBasicInfo));
    $('#detail-view\\.address').text(getStudentPermanentAddress(studentInfo.studentBasicInfo));
    $('#detail-view\\.aadhar-number').text(studentInfo.studentBasicInfo.aadharNumber);
    $('#detail-view\\.registration-date').text(getFormattedDate(studentInfo.studentBasicInfo.registrationDate));
    $('#detail-view\\.admission-date').text(getFormattedDate(studentInfo.studentBasicInfo.admissionDate));
    $('#detail-view\\.primary-contact-number').text(studentInfo.studentBasicInfo.primaryContactNumber);
    $('#detail-view\\.primary-email').text(studentInfo.studentBasicInfo.primaryEmail);
    $('#detail-view\\.whatsapp-number').text(studentInfo.studentBasicInfo.whatsappNumber);
    $("#detail-view\\.is-sponsored").text(studentInfo.studentBasicInfo.sponsoredDisplay);
    $("#detail-view\\.student-new-admission").text(studentInfo.newAdmission ? "Yes" : "No");
    $("#detail-view\\.student-hosteller").text(studentInfo.studentBasicInfo.hostellerDisplay);
    $('#detail-view\\.nationality').text(studentInfo.studentBasicInfo.nationality);

    if(studentInfo.studentImage != null){
      $('#detail-view\\.student-image').text("Uploaded");
    }else{
      $('#detail-view\\.student-image').text("Not present");
    }
    $('#detail-view\\.student-house').text(studentInfo.studentBasicInfo.instituteHouse == null ? "" : studentInfo.studentBasicInfo.instituteHouse.houseName);
    $('#detail-view\\.student-admission-in-class').text(studentInfo.studentBasicInfo.admissionInClass);
    $('#detail-view\\.student-name-as-per-aadhar').text(studentInfo.studentBasicInfo.studentNameAsPerAadhar);
    $('#detail-view\\.child-category-criteria').text(studentInfo.studentBasicInfo.childCategoryCriteria);
    $('#detail-view\\.specially-abled-type').text(studentInfo.studentBasicInfo.speciallyAbledType);

    $('#detail-view\\.student-mother-name').text(studentInfo.studentFamilyInfo.mothersName);
    $('#detail-view\\.student-father-name').text(studentInfo.studentFamilyInfo.fathersName);
    $('#detail-view\\.student-mother-qualification').text(studentInfo.studentFamilyInfo.mothersQualification);
    $('#detail-view\\.student-father-qualification').text(studentInfo.studentFamilyInfo.fathersQualification);
    $('#detail-view\\.mother-contact-number').text(studentInfo.studentFamilyInfo.mothersContactNumber);
    $('#detail-view\\.father-contact-number').text(studentInfo.studentFamilyInfo.fathersContactNumber);
    $('#detail-view\\.mother-occupation').text(studentInfo.studentFamilyInfo.mothersOccupation);
    $('#detail-view\\.mother-annual-income').text(studentInfo.studentFamilyInfo.mothersAnnualIncome);
    $('#detail-view\\.father-occupation').text(studentInfo.studentFamilyInfo.fathersOccupation);
    $('#detail-view\\.father-annual-income').text(studentInfo.studentFamilyInfo.fathersAnnualIncome);
    $('#detail-view\\.mother-aadhar-number').text(studentInfo.studentFamilyInfo.mothersAadharNumber);
    $('#detail-view\\.father-aadhar-number').text(studentInfo.studentFamilyInfo.fathersAadharNumber);
    $('#detail-view\\.mother-pan-card-details').text(studentInfo.studentFamilyInfo.mothersPanCardDetails);
    $('#detail-view\\.father-pan-card-details').text(studentInfo.studentFamilyInfo.fathersPanCardDetails);
    $('#detail-view\\.annual-family-income').text(studentInfo.studentFamilyInfo.approxFamilyIncome);

    if(studentInfo.studentGuardianInfoList != null && studentInfo.studentGuardianInfoList.length > 0){
      $('#detail-view\\.guardian-name').text(studentInfo.studentGuardianInfoList[0].guardianName);
      $('#detail-view\\.guardian-relation').text(studentInfo.studentGuardianInfoList[0].relation);
      $('#detail-view\\.guardian-age').text(studentInfo.studentGuardianInfoList[0].age);
      $('#detail-view\\.guardian-gender').text(studentInfo.studentGuardianInfoList[0].gender);
      $('#detail-view\\.guardian-occupation').text(studentInfo.studentGuardianInfoList[0].occupation);
      $('#detail-view\\.guardian-email').text(studentInfo.studentGuardianInfoList[0].email);
      $('#detail-view\\.guardian-contact-number').text(studentInfo.studentGuardianInfoList[0].contactNumber);
      $('#detail-view\\.guardian-address').text(getGuardianAddress(studentInfo.studentGuardianInfoList[0]));
    }else{
      $('#detail-view\\.guardian-name').text("");
      $('#detail-view\\.guardian-relation').text("");
      $('#detail-view\\.guardian-age').text("");
      $('#detail-view\\.guardian-gender').text("");
      $('#detail-view\\.guardian-occupation').text("");
      $('#detail-view\\.guardian-email').text("");
      $('#detail-view\\.guardian-contact-number').text("");
      $('#detail-view\\.guardian-address').text("")
    }

    $('#detail-view\\.previous-school-name').text(studentInfo.studentPreviousSchoolInfo.schoolName);
    $('#detail-view\\.medium').text(studentInfo.studentPreviousSchoolInfo.medium);
    $('#detail-view\\.class-passed').text(studentInfo.studentPreviousSchoolInfo.classPassed);
    $('#detail-view\\.year-of-passing').text(studentInfo.studentPreviousSchoolInfo.yearOfPassing);
    $('#detail-view\\.result').text(studentInfo.studentPreviousSchoolInfo.result);
    $('#detail-view\\.percentage-grade').text(studentInfo.studentPreviousSchoolInfo.percentage);
    $('#detail-view\\.student-is-admission-tc-based').text(studentInfo.studentPreviousSchoolInfo.admissionTcBasedDisplay);
    $('#detail-view\\.previous-school-tc-number').text(studentInfo.studentPreviousSchoolInfo.tcNumber);

    $('#detail-view\\.blood-group').text(studentInfo.studentMedicalInfo.bloodGroup);
    $('#detail-view\\.blood-pressure').text(studentInfo.studentMedicalInfo.bloodPressure);
    $('#detail-view\\.pulse').text(studentInfo.studentMedicalInfo.pulse);
    $('#detail-view\\.height').text(studentInfo.studentMedicalInfo.height);
    $('#detail-view\\.weight').text(studentInfo.studentMedicalInfo.weight);
    $('#detail-view\\.date-of-physical-examination').text(getFormattedDate(studentInfo.studentMedicalInfo.dateOfPhysicalExamination));
  },

  fillStudentPreviewDetails : function(studentInfo){
    $('#preview\\.student-name').text(studentInfo.studentBasicInfo.name);
    $('#preview\\.registration-number').text(studentInfo.studentBasicInfo.registrationNumber);
    $('#preview\\.student-id').text(studentInfo.studentId);
    if(studentInfo.studentAcademicSessionInfoResponse != null){
        $('#preview\\.standard').text(studentInfo.studentAcademicSessionInfoResponse.standard.displayName);
        if(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList != null && studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList.length > 0){
          $("#preview\\.section").text(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionName);
        }else{
          $("#preview\\.section").text("");
        }
    }else{
      $('#preview\\.standard').text("");

    }

    if(studentInfo.studentImage != null){
      $('#preview\\.student-image').text("Uploaded");
    }else{
      $('#preview\\.student-image').text("Not present");
    }

    $('#preview\\.gender').text(studentInfo.studentBasicInfo.gender);
    if(studentInfo.studentBasicInfo.dateOfBirth != null && studentInfo.studentBasicInfo.dateOfBirth > 0){
      $('#preview\\.dob').text(getFormattedDate(studentInfo.studentBasicInfo.dateOfBirth));
    }else{
      $('#preview\\.dob').text("");
    }
    $('#preview\\.birth-place').text(studentInfo.studentBasicInfo.birthPlace);
    $('#preview\\.category').text(studentInfo.studentBasicInfo.userCategory);
    $('#preview\\.religion').text(studentInfo.studentBasicInfo.religion);
    $('#preview\\.caste').text(studentInfo.studentBasicInfo.caste);
    $('#preview\\.rte').text(studentInfo.studentBasicInfo.rteDisplay);
    $("#preview\\.mother-tongue").text(studentInfo.studentBasicInfo.motherTongue);
    $("#preview\\.area-type").text(studentInfo.studentBasicInfo.areaType);
    $("#preview\\.specially-abled").text(studentInfo.studentBasicInfo.speciallyAbledDisplay);
    $("#preview\\.bpl").text(studentInfo.studentBasicInfo.bplDisplay);
    $('#preview\\.present-address').text(getStudentPresentAddress(studentInfo.studentBasicInfo));
    $('#preview\\.address').text(getStudentPermanentAddress(studentInfo.studentBasicInfo));
    $('#preview\\.aadhar-number').text(studentInfo.studentBasicInfo.aadharNumber);
    $('#preview\\.registration-date').text(getFormattedDate(studentInfo.studentBasicInfo.registrationDate));
    $('#preview\\.admission-date').text(getFormattedDate(studentInfo.studentBasicInfo.admissionDate));
    $('#preview\\.primary-contact-number').text(studentInfo.studentBasicInfo.primaryContactNumber);
    $('#preview\\.primary-email').text(studentInfo.studentBasicInfo.primaryEmail);
    $('#preview\\.whatsapp-number').text(studentInfo.studentBasicInfo.whatsappNumber);
    $("#preview\\.is-sponsored").text(studentInfo.studentBasicInfo.sponsoredDisplay);
    $("#preview\\.student-new-admission").text(studentInfo.studentBasicInfo.newAdmissionDisplay);
    $('#preview\\.nationality').text(studentInfo.studentBasicInfo.nationality);
    $('#preview\\.student-house').text(studentInfo.studentBasicInfo.instituteHouse == null ? "" : studentInfo.studentBasicInfo.instituteHouse.houseName);
    $('#preview\\.student-admission-in-class').text(studentInfo.studentBasicInfo.admissionInClass);
    $('#preview\\.student-name-as-per-aadhar').text(studentInfo.studentBasicInfo.studentNameAsPerAadhar);
    $('#preview\\.child-category-criteria').text(studentInfo.studentBasicInfo.childCategoryCriteria);
    $('#preview\\.specially-abled-type').text(studentInfo.studentBasicInfo.speciallyAbledType);

    $('#preview\\.student-mother-name').text(studentInfo.studentFamilyInfo.mothersName);
    $('#preview\\.student-father-name').text(studentInfo.studentFamilyInfo.fathersName);
    $('#preview\\.student-mother-qualification').text(studentInfo.studentFamilyInfo.mothersQualification);
    $('#preview\\.student-father-qualification').text(studentInfo.studentFamilyInfo.fathersQualification);
    $('#preview\\.mother-contact-number').text(studentInfo.studentFamilyInfo.mothersContactNumber);
    $('#preview\\.father-contact-number').text(studentInfo.studentFamilyInfo.fathersContactNumber);
    $('#preview\\.mother-occupation').text(studentInfo.studentFamilyInfo.mothersOccupation);
    $('#preview\\.mother-annual-income').text(studentInfo.studentFamilyInfo.mothersAnnualIncome);
    $('#preview\\.father-occupation').text(studentInfo.studentFamilyInfo.fathersOccupation);
    $('#preview\\.father-annual-income').text(studentInfo.studentFamilyInfo.fathersAnnualIncome);
    $('#preview\\.mother-aadhar-number').text(studentInfo.studentFamilyInfo.mothersAadharNumber);
    $('#preview\\.father-aadhar-number').text(studentInfo.studentFamilyInfo.fathersAadharNumber);
    $('#preview\\.annual-family-income').text(studentInfo.studentFamilyInfo.approxFamilyIncome);

    if(studentInfo.studentGuardianInfoList != null && studentInfo.studentGuardianInfoList.length > 0){
      $('#preview\\.guardian-name').text(studentInfo.studentGuardianInfoList[0].guardianName);
      $('#preview\\.guardian-relation').text(studentInfo.studentGuardianInfoList[0].relation);
      $('#preview\\.guardian-age').text(studentInfo.studentGuardianInfoList[0].age);
      $('#preview\\.guardian-gender').text(studentInfo.studentGuardianInfoList[0].gender);
      $('#preview\\.guardian-occupation').text(studentInfo.studentGuardianInfoList[0].occupation);
      $('#preview\\.guardian-email').text(studentInfo.studentGuardianInfoList[0].email);
      $('#preview\\.guardian-contact-number').text(studentInfo.studentGuardianInfoList[0].contactNumber);
      $('#preview\\.guardian-address').text(getGuardianAddress(studentInfo.studentGuardianInfoList[0]));
    }else{
      $('#preview\\.guardian-name').text("");
      $('#preview\\.guardian-relation').text("");
      $('#preview\\.guardian-age').text("");
      $('#preview\\.guardian-gender').text("");
      $('#preview\\.guardian-occupation').text("");
      $('#preview\\.guardian-email').text("");
      $('#preview\\.guardian-contact-number').text("");
      $('#preview\\.guardian-address').text("");
    }

    $('#preview\\.previous-school-name').text(studentInfo.studentPreviousSchoolInfo.schoolName);
    $('#preview\\.medium').text(studentInfo.studentPreviousSchoolInfo.medium);
    $('#preview\\.class-passed').text(studentInfo.studentPreviousSchoolInfo.classPassed);
    $('#preview\\.year-of-passing').text(studentInfo.studentPreviousSchoolInfo.yearOfPassing);
    $('#preview\\.result').text(studentInfo.studentPreviousSchoolInfo.result);
    $('#preview\\.percentage-grade').text(studentInfo.studentPreviousSchoolInfo.percentage);
    $('#preview\\.student-is-admission-tc-based').text(studentInfo.studentPreviousSchoolInfo.admissionTcBasedDisplay);
    $('#preview\\.previous-school-tc-number').text(studentInfo.studentPreviousSchoolInfo.tcNumber);

    $('#preview\\.blood-group').text(studentInfo.studentMedicalInfo.bloodGroup);
    $('#preview\\.blood-pressure').text(studentInfo.studentMedicalInfo.bloodPressure);
    $('#preview\\.pulse').text(studentInfo.studentMedicalInfo.pulse);
    $('#preview\\.height').text(studentInfo.studentMedicalInfo.height);
    $('#preview\\.weight').text(studentInfo.studentMedicalInfo.weight);
    $('#preview\\.date-of-physical-examination').text(getFormattedDate(studentInfo.studentMedicalInfo.dateOfPhysicalExamination));
  }
};

var studentSearchWithoutAcademicSession = {
    search : function(text, successMethod){
      ajaxClient.get("/admission/student-search-without-session?text="+text, function(data) {
          $(".search-students-without-academic-session-results").html(data);
          successMethod(data);
          studentSearchWithoutAcademicSession.registerViewCallback();
          studentSearchWithoutAcademicSession.registerPreviewCallback();
      });
    },

    registerViewCallback : function(){
      $('.view-student-info').on('click', function () {
        $('#view-student-details-modal').modal('toggle');
        var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
        var studentInfo = JSON.parse(studentInfoJson);
        studentDetails.fillStudentViewDetails(studentInfo);
      });
    },

    registerPreviewCallback : function(){
      $('.preview-student-info').on('click', function () {
        $('#preview-student-details-modal').modal('toggle');
        var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
        var studentInfo = JSON.parse(studentInfoJson);
        studentDetails.fillStudentPreviewDetails(studentInfo);
      });
    }
};

function registerStudentWithoutAcademicSessionSearchCallback(successMethod){
    $('.search-students-without-academic-session').on('click', function () {
        var searchText = $(".search-students-without-academic-session-input").first().val();
        studentSearchWithoutAcademicSession.search(searchText,successMethod);
    });
    $(".search-students-without-academic-session-input").on('keyup', function (e) {
        if (e.keyCode == 13) {
          var searchText = $(this).val()
          studentSearchWithoutAcademicSession.search(searchText,successMethod);
        }
    });
}

function getStudentPresentAddress(studentBasicInfo){
  var address = "";
  if(studentBasicInfo.presentAddress != null && studentBasicInfo.presentAddress != ''){
    address = address + studentBasicInfo.presentAddress + ", ";
  }
  if(studentBasicInfo.presentCity != null && studentBasicInfo.presentCity != ''){
    address = address + studentBasicInfo.presentCity + ", "
  }
  if(studentBasicInfo.presentState != null && studentBasicInfo.presentState != ''){
    address = address + studentBasicInfo.presentState + ", "
  }
  if(studentBasicInfo.presentPostOffice != null && studentBasicInfo.presentPostOffice != ''){
    address = address + studentBasicInfo.presentPostOffice + ", "
  }
  if(studentBasicInfo.presentPoliceStation != null && studentBasicInfo.presentPoliceStation != ''){
    address = address + studentBasicInfo.presentPoliceStation + ", "
  }
  if(studentBasicInfo.presentZipcode != null && studentBasicInfo.presentZipcode != ''){
    address = address + studentBasicInfo.presentZipcode + ", "
  }
  if(address != ''){
    address = address.substring(0, address.length - 2);
  }
  return address;
}

function getStudentPermanentAddress(studentBasicInfo){
  var address = "";
  if(studentBasicInfo.permanentAddress != null && studentBasicInfo.permanentAddress != ''){
    address = address + studentBasicInfo.permanentAddress + ", ";
  }
  if(studentBasicInfo.permanentCity != null && studentBasicInfo.permanentCity != ''){
    address = address + studentBasicInfo.permanentCity + ", "
  }
  if(studentBasicInfo.permanentState != null && studentBasicInfo.permanentState != ''){
    address = address + studentBasicInfo.permanentState + ", "
  }
  if(studentBasicInfo.permanentPostOffice != null && studentBasicInfo.permanentPostOffice != ''){
    address = address + studentBasicInfo.permanentPostOffice + ", "
  }
  if(studentBasicInfo.permanentPoliceStation != null && studentBasicInfo.permanentPoliceStation != ''){
    address = address + studentBasicInfo.permanentPoliceStation + ", "
  }
  if(studentBasicInfo.permanentZipcode != null && studentBasicInfo.permanentZipcode != ''){
    address = address + studentBasicInfo.permanentZipcode + ", "
  }
  if(address != ''){
    address = address.substring(0, address.length - 2);
  }
  return address;
}

function getGuardianAddress(studentGuardianInfo){
  var address = "";
  if(studentGuardianInfo.address != null && studentGuardianInfo.address != ''){
    address = address + studentGuardianInfo.address + ", ";
  }
  if(studentGuardianInfo.city != null && studentGuardianInfo.city != ''){
    address = address + studentGuardianInfo.city + ", "
  }
  if(studentGuardianInfo.state != null && studentGuardianInfo.state != ''){
    address = address + studentGuardianInfo.state + ", "
  }
  if(studentGuardianInfo.zipcode != null && studentGuardianInfo.zipcode != ''){
    address = address + studentGuardianInfo.zipcode + ", "
  }
  if(address != ''){
    address = address.substring(0, address.length - 2);
  }
  return address;
}

function redirectToMainPage() {
    window.location.replace(baseURL);
}

function titleCase(str) {
    if (str == null || str == undefined) {
        return "";
    }
    var splitStr = str.toLowerCase().split(' ');
    for (var i = 0; i < splitStr.length; i++) {
        // You do not need to check if i is larger than splitStr length, as your for does that for you
        // Assign it back to the array
        splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    // Directly return the joined string
    return splitStr.join(' ');
}


function displayLoader(skipLoader = false) {
    if(skipLoader){
      return;
    }
    $('.mask').addClass('ajax');
}

function hideLoader(skipLoader = false) {
    if(skipLoader){
        return;
    }
    $('.mask').removeClass('ajax');
}

function enforceConstraints() {
  maxDigitsLengthConstraints();
  preventSpecialChars();
}

function maxDigitsLengthConstraints() {
    $('.max-length-10').keypress(function(e) {
        var k = e.which;
        var valid = (e.which >= 48 && e.which <= 57)
        if (!valid) {
            e.preventDefault();
        }
        if ($(this).val().length >= 10) {
            e.preventDefault();
        }
    });

    $('.max-length-6').keypress(function(e) {
        var k = e.which;
        var valid = (e.which >= 48 && e.which <= 57)
        if (!valid) {
            e.preventDefault();
        }
        if ($(this).val().length >= 6) {
            e.preventDefault();
        }
    });
}

var attachmentTileUtils={
  
  displayAttachmentTile: function(attachment, downloadDocument){
    var attachments = "";
    if (!(typeof attachment === 'undefined') && !(attachment === null)) {
    for(var i = 0; i < attachment.length; i++) {
      var p = "<p class=\"document-details-json\" style=\"display:none;\">" + JSON.stringify(attachment[i]) + "</p>";
      if(attachment[i].fileExtension == "pdf"){
        var icon = "<div class=\"text-left\"><img class=\"sidebar_icon\" src=\"https://assetsv5.embrate.com/static/core/images/Icons/icons8-adobe-acrobat-reader-64.png\" style=\"width:40px\"></div>";
      }
      else if(attachment[i].fileExtension == "png" || attachment[i].fileExtension == "jpg"){
        var icon = "<div class=\"text-left\"><img class=\"sidebar_icon\" src=\"https://assetsv5.embrate.com/static/core/images/Icons/icons8-image-64.png\" style=\"width:40px\"></div>";
      }
      else{
        var icon = "<div class=\"text-left\"><img class=\"sidebar_icon\" src=\"https://assetsv5.embrate.com/static/core/images/Icons/icons8-microsoft-excel-64.png\" style=\"width:40px\"></div>";
      }
      var h6 = "<div class=\"col-sm-7\"><p style=\"margin-bottom:0%;color:#212121;\">"  + attachment[i].documentName +"." +attachment[i].fileExtension +"</p><p style=\"cursor:pointer;color:#43a2ad;\" class=\"mt-2\" onclick=\"" + downloadDocument + "\">Click here to download</p></div>";
      var button =  "<div class=\"col-sm-4 mt-2\"><p style=\"cursor: pointer;float:right;\" onclick=\"" + downloadDocument + "\"><img class=\"sidebar_icon\" src=\"https://assetsv5.embrate.com/static/core/images/download-icon-20.png\" ></p></div>"
      attachments = attachments + "<div class=\"row row mt-2 ml-2\">" + p+ icon + h6  + button + "</div><hr style=\"border: 2px solid #f9f9f9;border-width: 1px;\"/>"
    }
    return attachments;
    }
    return "<p>No attachments!</p>";
  },
};


function preventSpecialChars() {
    $('.no-special-chars').keypress(function(e) {
        var valid = (e.which >= 48 && e.which <= 57) || (e.which >= 65 && e.which <= 90) || (e.which >= 97 && e.which <= 122) || e.which == 08 || e.which == 95 || e.which == 45 || e.which == 09 || e.which == 32;
        if (!valid) {
            e.preventDefault();
        }
    });
}

function validateEmail(email) {
    if (!email) {
        return true;
    } else if (email.trim().match(/^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{1,5}|[0-9]{1,3})(\]?)$/) == null) {
        return false;
    } else {
        return true;
    }
}

function registerUploadFileCallback() {
  $('.upload-file-action').on('change',function(){
       //get the file name
       var fileName = $(this).val();
       var values = fileName.split("\\");
       if(values.length <= 0){
         showErrorDialogBox("Invalid file");
         return;
       }
       //replace the "Choose a file" label
       $(this).next('.custom-file-label').html(values[values.length-1]);
   });
}

function round(value, decimalPoints) {
    var tens = Math.pow(10, decimalPoints)
    return (Math.round(value*tens))/tens;
}

function previousPageClickingEvent(){
  $('.previous-page-link').on('click', function () {
    var tabPrev = $('.page-item.active').prev();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabPrev.trigger("click");
    $('.switch-tab').addClass('disabled');
    tabPrev.removeClass('disabled').addClass('active');
  });
}

function nextPageClickingEvent(){
  $('.next-page-link').on('click', function () {
    var tabNext = $('.switch-tab.active').next();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    $('.switch-tab').addClass('disabled');
    tabNext.removeClass('disabled').addClass('active');
  });
}

function readJson(jsonStrId) {
    var json  = $(jsonStrId).text().trim();
    if(json == undefined || json == null || json === "None"|| json === "") {
      return "";
    }
    return JSON.parse(json);
}


function registerDropDownSelectEvent() {
    $(".add-label").change(function() {
        var selectedVal = $(this).find(':selected').val().trim();
        var selectedText = $(this).find(':selected').text().trim();
        addLabel(this, selectedVal, selectedText);
        $(this).val("")
        // var label =  $(this).parent().find("p.label-container");
        // var existingContent = label.html();
        // var exists = false;
        // label.children('span').each(function(i) {
        //   if($(this).find(".label-text").length > 0){
        //       var labelVal = $(this).find(".label-text").text().trim();
        //       var labelValId = $(this).find(".label-id").text().trim();
        //       if(labelValId.toLowerCase() == selectedVal.toLowerCase()){
        //          exists = true;
        //          return false;
        //       }
        //   }
        //
        // });
        // if(!exists && selectedVal!="" && selectedText!=""){
        //   existingContent = existingContent + getLabel(selectedVal,selectedText);
        //   label.html(existingContent);
        //   registerLableCloseActions();
        // }
    });
}

function addLabel(input_element, selectedVal, selectedText) {
    var label = $(input_element).parent().find("p.label-container");
    var existingContent = label.html();
    var exists = false;
    label.children('span').each(function(i) {
        if ($(this).find(".label-text").length > 0) {
            var labelVal = $(this).find(".label-text").text().trim();
            var labelValId = $(this).find(".label-id").text().trim();
            if (labelValId.toLowerCase() == selectedVal.toLowerCase()) {
                exists = true;
                return false;
            }
        }

    });
    if (!exists && selectedVal != "" && selectedText != "") {
        existingContent = existingContent + getLabelText(selectedVal, selectedText);
        label.html(existingContent);
        registerLableCloseActions();
    }
}


function getLabelText(text_id, text_value) {
    return " <span class=\"badge badge-success label-area\"> <span style=\"display:none;\" class=\"label-id\">" + text_id + "</span> <span class=\"label-text\" >" + text_value + "</span> <button type=\"button\" class=\"close label-close\" data-dismiss=\"showErrorDialogBox\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button></span> "
}


function registerLableCloseActions() {
    $('.label-close').on('click', function() {
        $(this).parent().remove();
    });
}


function getDropdownSelectedAttributes(id) {
    var label = $(id).parent().find("p.label-container");
    var attributes = []
    label.children('span').each(function(i) {
        if ($(this).find(".label-id").length > 0) {
            var labelVal = $(this).find(".label-id").text().trim();
            attributes.push(labelVal);
        }
    });
    return attributes;
}


// Give the parent element inside which all the children are to be scanned
function validateMandatoryFields(element) {
    clearMandatoryFieldsErrorDisplayInsideElement(element);
    bindRemoveErrorDisplayEvent();
    var invalid = false;
    $(element).find("input.mandatory-field").each(function() {
        if ($(this).val() == "") {
            $(this).css("border", "1px solid #ff8795");
            // $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
            invalid = true;
        }
    });

    $(element).find("textarea.mandatory-field").each(function() {
        if ($(this).val() == "") {
            $(this).css("border", "1px solid #ff8795");
            // $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
            invalid = true;
        }
    });

    $(element).find("select.mandatory-field").each(function() {
      if ($(this).find(':selected').length == 0 || $(this).find(':selected').val().trim() == "") {
          if($(this).hasClass('selectpicker')) {
            $(this).parent().css("border", "1px solid #ff8795");
            // $(this).parent().after("<p class=\"mandatory-field-text error\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          }
          else {
            $(this).css("border", "1px solid #ff8795");
            // $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          }
          invalid = true;
        }
    });

    $(element).find("input.fix-length-6").each(function() {
        if ($(this).val().trim() != "" && $(this).val().trim().length != 6) {
            $(this).css("border", "1px solid #ff8795");
            // $(this).after("<p class=\"fix-length-text\"> <span style=\"color:#e65f76;\">Must contain 6 characters</span></p>");
            invalid = true;
        }
    });
    return invalid;
}

function bindRemoveErrorDisplayEvent() {
    $(".mandatory-field").focus(function() {
        $(this).css("border", "");
        if($(this).next().hasClass('mandatory-field-text')){
          $(this).next().remove();
        }
    }).blur(function() {});

    $(".fix-length-6").focus(function() {
        $(this).css("border", "");
        if($(this).next().hasClass('fix-length-text')){
          $(this).next().remove();
        }
    }).blur(function() {});

    $('select.selectpicker').parent().find("button").on('click', function () {
      $(this).parent().parent().find('.error').remove();
      $(this).parent().css("border", "none");
    });
}

function clearMandatoryFieldsErrorDisplay() {
    $(".fix-length-6").css("border", "");
    $(".fix-length-text").remove();

    $(".mandatory-field").css("border", "");
    $(".mandatory-field-text").remove();
}

function clearMandatoryFieldsErrorDisplayInsideElement(element) {
    $(element).find(".fix-length-6").css("border", "");
    $(element).find(".fix-length-text").remove();

    $(element).find(".mandatory-field").css("border", "");
    $(element).find(".mandatory-field-text").remove();
}

function closeModal(){
  clearMandatoryFieldsErrorDisplay();
}

function containsUnicodeCharacter(str) {
    for (var i = 0, n = str.length; i < n; i++) {
        if (str.charCodeAt( i ) > 255) { return true; }
    }
    return false;
}


var pagination = {
  bindEvents: function(onPageTriggerMethod, prevPageTriggerMethod, nextPageTriggerMethod, pageCountChangeTriggerMethod){
    $('.page-number-link').on('click', function () {
      $('.page-item').removeClass('active');
      $(this).parent().addClass('active');
      onPageTriggerMethod();
    });

    $('.previous-page-link').on('click', function () {
      var pageNumber = parseFloat($('.page-item.active').find('.page-number').text().trim());
      if(pageNumber - 1 <= 0){
        return;
      }
      var currentPage = pageNumber - 1;
      $('.page-item').removeClass('active');
      $("#page-number-"+currentPage).addClass('active');
      prevPageTriggerMethod();
    });

    $('.next-page-link').on('click', function () {
      var totalPages = parseFloat($(this).parent().find(".total-pages").text().trim());
      var pageNumber = parseFloat($('.page-item.active').find('.page-number').text().trim());
      if(pageNumber + 1 > totalPages){
        return;
      }
      var currentPage = pageNumber + 1;
      $('.page-item').removeClass('active');
      $("#page-number-"+currentPage).addClass('active');
      nextPageTriggerMethod();
    });

    $("#items-per-page").change(function() {
        pageCountChangeTriggerMethod();
    });
  },

  updatePaginationDetails : function (resultDivAreaId, resultListId) {
    var screenHeight = $('#sidebar').height() - $('#'+resultDivAreaId).position().top - 250;
    $('#'+resultListId).attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
    var response = JSON.parse($("#pagination-info").text().trim());
    $('#items-per-page').val(response.itemsPerPage);
    $('.page-item').removeClass('active');
    var pageNumber = (response.offset/response.itemsPerPage) + 1;
    $('#page-number-'+pageNumber).addClass('active');
  }

};

var paginationWithContainer = {
  bindEvents: function(container, onPageTriggerMethod, prevPageTriggerMethod, nextPageTriggerMethod, pageCountChangeTriggerMethod){
    $("#" + container).find('.page-number-link').on('click', function () {
      $("#" + container).find('.page-item').removeClass('active');
      $(this).parent().addClass('active');
      onPageTriggerMethod();
    });

    $("#" + container).find('.previous-page-link').on('click', function () {
      var pageNumber = parseFloat($("#" + container).find('.page-item.active').find('.page-number').text().trim());
      if(pageNumber - 1 <= 0){
        return;
      }
      var currentPage = pageNumber - 1;
      $("#" + container).find('.page-item').removeClass('active');
      $("#" + container).find("#page-number-"+currentPage).addClass('active');
      prevPageTriggerMethod();
    });

    $("#" + container).find('.next-page-link').on('click', function () {
      var totalPages = parseFloat($(this).parent().find(".total-pages").text().trim());
      var pageNumber = parseFloat($("#" + container).find('.page-item.active').find('.page-number').text().trim());
      if(pageNumber + 1 > totalPages){
        return;
      }
      var currentPage = pageNumber + 1;
      $("#" + container).find('.page-item').removeClass('active');
      $("#" + container).find("#page-number-"+currentPage).addClass('active');
      nextPageTriggerMethod();
    });

    $("#" + container).find("#items-per-page").change(function() {
        // console.log("is called");
        // itemPerPage = $(this).val();
        // $(this).val(itemPerPage);
        pageCountChangeTriggerMethod();
    });
  },

  updatePaginationDetails : function (resultDivAreaId, resultListId, container) {
    var screenHeight = $("#" + container).find('#sidebar').height() - $("#" + container).find('#'+resultDivAreaId).position().top - 250;
    $("#" + container).find('#'+resultListId).attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
    var response = JSON.parse($("#" + container).find("#pagination-info").text().trim());
    $("#" + container).find('#items-per-page').val(response.itemsPerPage);
    $("#" + container).find('.page-item').removeClass('active');
    var pageNumber = (response.offset/response.itemsPerPage) + 1;
    $("#" + container).find('#page-number-'+pageNumber).addClass('active');
  }

};


function isEmpty(obj) {
  return Object.keys(obj).length === 0;
}

function getFloatValue(value) {
  if(value == null || value == undefined){
    return 0;
  }

  if(value == ""){
    return 0;
  }

  return parseFloat(value);
}

var channelCreditsHandler = {

  dataCache : {
  },

  initDataCache: function () {
    channelCreditsHandler.dataCache.channelCreditsMap = {};
  },

  loadChannelCredits : function (path) {
    ajaxClient.get(path, function(data) {
        $("#channel-credits-container").html(data);
        var channelCreditsList = readJson("#channel-credits-list");
        var channelCreditsMap = {};
        for(var i = 0; i < channelCreditsList.length; i++){
          var channelCredits = channelCreditsList[i];
          channelCreditsMap[channelCredits.counterType] = channelCredits;
        }
        channelCreditsHandler.dataCache.channelCreditsMap = channelCreditsMap;
    });
  },

  getChannelCredits : function (channelType) {
    if(channelType in channelCreditsHandler.dataCache.channelCreditsMap){
      return channelCreditsHandler.dataCache.channelCreditsMap[channelType].count;
    }else{
      return 0;
    }
  }
}

function permanentAddressUpdate(permanentSamePercentage, permanentAddress, permanentCity, permanentState, permanentPostOffice, permanentPoliceStation,
  permanentZipcode, presentAddress, presentCity, presentState, presentPostOffice, presentPoliceStation, presentZipcode) {
  // update as per checkbox value
  $('#' + permanentSamePercentage).on('change', function(){

    if($(this).prop("checked")){

      $("#" + permanentAddress).val($("#" + presentAddress).val());
      $("#" + permanentCity).val($("#" + presentCity).val());
      $("#" + permanentState).find('.country-states').val($("#" + presentState).find(".country-states").find(':selected').val() == undefined ? '' : $("#" + presentState).find(".country-states").find(':selected').val().trim());
      if(permanentPostOffice != null && presentPostOffice != null) {
          $("#" + permanentPostOffice).val($("#" + presentPostOffice).val());
      }
      if(permanentPoliceStation != null && presentPoliceStation != null) {
        $("#" + permanentPoliceStation).val($("#" + presentPoliceStation).val());
      }
      $("#" + permanentZipcode).val($("#" + presentZipcode).val());

      $(".address-field").find('.country-states').attr("disabled", "disabled");
      $(".address-field").attr("disabled", "disabled");

    }
    else {

      $("#" + permanentAddress).val("");
      $("#" + permanentCity).val("");
      $("#" + permanentState).find('.country-states').val("");
      $("#" + permanentZipcode).val("");
      $("#" + permanentPostOffice).val("");
      $("#" + permanentPoliceStation).val("");

      $(".address-field").find('.country-states').removeAttr("disabled");
      $(".address-field").removeAttr("disabled");

    }

  });

  //update permanent as per present
  $("#" + presentAddress).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentAddress).val($("#" + presentAddress).val());
    }
  });

  $("#" + presentCity).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentCity).val($("#" + presentCity).val());
    }
  });

  $("#" + presentState).find(".country-states").on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentState).find('.country-states').val($("#" + presentState).find(".country-states").find(':selected').val() == undefined ? '' : $("#" + presentState).find(".country-states").find(':selected').val().trim());
    }
  });

  $("#" + presentZipcode).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentZipcode).val($("#" + presentZipcode).val());
    }
  });

}

function staffPermanentAddressUpdate(permanentSamePercentage, permanentAddress1, permanentAddress2,
  permanentCity, permanentState, permanentCountry, permanentZipcode, presentAddress1, presentAddress2,
  presentCity, presentState, presentCountry, presentZipcode) {
  // update as per checkbox value
  $('#' + permanentSamePercentage).on('change', function(){

    if($(this).prop("checked")){

      $("#" + permanentAddress1).val($("#" + presentAddress1).val());
      $("#" + permanentAddress2).val($("#" + presentAddress2).val());
      $("#" + permanentCity).val($("#" + presentCity).val());
      $("#" + permanentState).find('.country-states').val($("#" + presentState).find(".country-states").find(':selected').val() == undefined ? '' : $("#" + presentState).find(".country-states").find(':selected').val().trim());
      $("#" + permanentCountry).find('.country').val($("#" + presentCountry).find(".country").find(':selected').val() == undefined ? '' : $("#" + presentCountry).find(".country").find(':selected').val().trim());
      $("#" + permanentZipcode).val($("#" + presentZipcode).val());

      $(".address-field").find('.country-states').attr("disabled", "disabled");
      $(".address-field").find('.country').attr("disabled", "disabled");
      $(".address-field").attr("disabled", "disabled");

    }
    else {

      $("#" + permanentAddress1).val("");
      $("#" + permanentAddress2).val("");
      $("#" + permanentCity).val("");
      $("#" + permanentState).find('.country-states').val("");
      $("#" + permanentCountry).find('.country').val("");
      $("#" + permanentZipcode).val("");

      $(".address-field").find('.country-states').removeAttr("disabled");
      $(".address-field").find('.country').removeAttr("disabled");
      $(".address-field").removeAttr("disabled");

    }

  });

  //update permanent as per present
  $("#" + presentAddress1).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentAddress1).val($("#" + presentAddress1).val());
    }
  });

  $("#" + presentAddress2).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentAddress2).val($("#" + presentAddress2).val());
    }
  });

  $("#" + presentCity).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentCity).val($("#" + presentCity).val());
    }
  });

  $("#" + presentState).find(".country-states").on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentState).find('.country-states').val($("#" + presentState).find(".country-states").find(':selected').val() == undefined ? '' : $("#" + presentState).find(".country-states").find(':selected').val().trim());
    }
  });

  $("#" + presentCountry).find(".country").on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentCountry).find('.country').val($("#" + presentCountry).find(".country").find(':selected').val() == undefined ? '' : $("#" + presentCountry).find(".country").find(':selected').val().trim());
    }
  });


  $("#" + presentZipcode).on('change', function(){
    if($('#' + permanentSamePercentage).prop("checked")){
      $("#" + permanentZipcode).val($("#" + presentZipcode).val());
    }
  });

}

/**
	 *
	 * https://www.world-text.com/docs/sms-length.php#:~:text=Throw%20in%20Unicode%2C%20for%20languages,needed%20for%20encoding%20each%20character
	 *
	 * GSM encoding:
	 *
	 * 1 standard SMS message = up to 160 characters, 2 concatenated SMS
	 * messages = up to 306 characters, 3 concatenated SMS messages = up to 459
	 * characters, 4 concatenated SMS messages = up to 612 characters, 5
	 * concatenated SMS messages = up to 765 characters, through to 9
	 * concatenated SMS messages = up to 1,377 characters
	 *
	 * UCS-2 encoding:
	 *
	 * 1 standard SMS message = up to 70 characters, 2 concatenated SMS messages
	 * = up to 134 characters, 3 concatenated SMS messages = up to 201
	 * characters, 4 concatenated SMS messages = up to 268 characters. 5
	 * concatenated SMS messages = up to 335 characters, through to 9
	 * concatenated SMS messages = up to 603 characters
	 *
	 * @param notificationContent
	 */
	function getSmsCredits(notificationContent) {
		if (notificationContent == null || notificationContent == undefined || notificationContent == "") {
			return 0;
		}

		var length = parseInt(notificationContent.length);

		if (containsUnicodeCharacter(notificationContent)) {
			if (length <= 70) {
				return 1;
			} else if (length <= 134) {
				return 2;
			} else {
				return 3 + Math.floor((length - 134 - 1)
						/ SMS_UNICODE_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT);
			}
		} else {
			if (length <= 160) {
				return 1;
			} else if (length <= 306) {
				return 2;
			} else {
				return 3 + Math.floor((length - 306 - 1)
						/ STANDARD_SMS_INCREMENTAL_CHAR_LIMIT_ONE_CREDIT);
			}
		}
	}

$(document).ready(function() {
  registerChangePassword();
  bellNotification.initNotificationSetup();
});

var bellNotification = {

  initNotificationSetup : function () {
    var bellNotificationCount = $("#bell-notification-count").text();
    if(bellNotificationCount <= 0) {
      $('#noti_Counter').hide();
    }
    else {
        $('#noti_Counter').show();
    }

    $(document).click(function () {
        $('#notifications').hide();
    });

    bellNotification.onClickNotificationButton();
  },

  onClickNotificationButton : function () {
    $('#noti_Button').click(function () {
        var userTypeForNotification = $("#user-type-for-notification").text();
        if(userTypeForNotification === STAFF) {
          return;
        }
        $('#notifications').fadeToggle('fast', 'linear', function () {
          if($("#notifications").is(":visible")) {
            $(".seeAll").show();
            offset = 0;
            limit = 10;
            var url = bellNotification.getBellNotificationListURL();
            ajaxClient.get(url + offset + "/" + limit, function(data) {
                $("#notifications").html(data);
                bellNotification.onClickNotification();
                bellNotification.markListAsViewed();
                bellNotification.markAllNotificationRead();
                bellNotification.onClickSeeAllButton();
            });
          }
        });
        $('#noti_Counter').fadeOut('slow');
        return false;
    });
  },

  getBellNotificationListURL : function () {
    var userTypeForNotification = $("#user-type-for-notification").text();
    if(userTypeForNotification === ADMIN || userTypeForNotification === SYSTEM || userTypeForNotification === INTERNAL) {
      return "/adminportal/get-bell-notification-list/";
    } else if(userTypeForNotification === STUDENT) {
      return "/studentportal/get-bell-notification-list/";
    } else if(userTypeForNotification === STAFF) {
      return "/staffportal/get-bell-notification-list/";
    }
  },

  markListAsViewed : function () {
    var url = bellNotification.getMarkListOpenedURL();
    ajaxClient.get(url, function(data) {
    });
  },

  getMarkListOpenedURL : function () {
    var userTypeForNotification = $("#user-type-for-notification").text();
    if(userTypeForNotification === ADMIN || userTypeForNotification === SYSTEM || userTypeForNotification === INTERNAL) {
      return "/adminportal/mark-list-opened";
    } else if(userTypeForNotification === STUDENT) {
      return "/studentportal/mark-list-opened";
    } else if(userTypeForNotification === STAFF) {
      return "/staffportal/mark-list-opened/";
    }
  },

  markAllNotificationRead : function () {
    $('.markAllRead').click(function () {
      var url = bellNotification.getMarkNotificationReadURL();
      ajaxClient.get(url, function(data) {
        $("#notifications").html(data);
        bellNotification.onClickNotification();
        $("#notifications").show();
        $(".seeAll").show();
      });
    });
  },

  getMarkNotificationReadURL : function () {
    var userTypeForNotification = $("#user-type-for-notification").text();
    if(userTypeForNotification === ADMIN || userTypeForNotification === SYSTEM || userTypeForNotification === INTERNAL) {
      return "/adminportal/mark-as-read";
    } else if(userTypeForNotification === STUDENT) {
      return "/studentportal/mark-as-read";
    } else if(userTypeForNotification === STAFF) {
      return "/staffportal/mark-as-read/";
    }
  },

  onClickSeeAllButton : function () {
    $('.seeAll').click(function () {
      offset = 0;
      limit = 0;
      var url = bellNotification.getBellNotificationListURL();
      ajaxClient.get(url + offset + "/" + limit, function(data) {
        $("#notifications").html(data);
        $(".seeAll").hide();
        bellNotification.onClickNotification();
        $("#notifications").show();
      });
    });
  },

  onClickNotification : function () {
    $('.notification-bar').click(function () {
      $("#notifications").hide();
      var userTypeForNotification = $("#user-type-for-notification").text();
      if(userTypeForNotification === ADMIN || userTypeForNotification === SYSTEM || userTypeForNotification === INTERNAL) {
          bellNotification.onClickNotificationAdmin(this);
      } else if(userTypeForNotification === STUDENT) {
          bellNotification.onClickNotificationStudent(this);
      } else if(userTypeForNotification === STAFF) {
          bellNotification.onClickNotificationStaff(this);
      }
    });
  },

  onClickNotificationAdmin : function (ref) {
    notificationDetailsJson = $(ref).find(".notification-details-json").text();
    notificationDetails = JSON.parse(notificationDetailsJson);
    var notificationId = notificationDetails.notificationId;

    if((notificationDetails.clickedOn === null)) {
      ajaxClient.get("/adminportal/mark-as-read?notification_id=" + notificationId, function(data) {
        $("#notifications").html(data);
      });
    }

    var entityName = notificationDetails.entityName;
    var institute_unique_code = $("#institute-unique-code").text();

    var threadId = "";
    if(!(typeof(notificationDetails.metaData.threadId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
    }

    var conversationId = "";
    if(!(typeof(notificationDetails.metaData.conversationId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
        conversationId = notificationDetails.metaData.conversationId;
    }

    if(entityName === ONLINE_LECTURE_DISCUSSION) {

      var lectureId = notificationDetails.metaData.lectureId;

      window.open("/" + institute_unique_code
      + "/lecture-management/dashboard?action_type=discussion_board&lecture_id="
      + lectureId + "&thread_id=" + threadId + "&conversation_id=" + conversationId, "_self")

    } else if(entityName === HOMEWORK_DISCUSSION) {

      var homeworkId = notificationDetails.metaData.homeworkId;

      window.open("/" + institute_unique_code
      + "/homework-management/dashboard?action_type=discussion_board&homework_id="
      + homeworkId + "&thread_id=" + threadId + "&conversation_id=" + conversationId, "_self")

    } else if(entityName === NOTICE) {

      var noticeId = notificationDetails.metaData.noticeId;
      window.open("/" + institute_unique_code
      + "/noticeboard/dashboard?action_type=notice&notice_id=" + noticeId, "_self")

    } else if(entityName === STUDENT_LEAVE_APPLICATION) {
       var transactionId = notificationDetails.metaData.transactionId;
       window.open("/" + institute_unique_code
        + "/attendance/dashboard?action_type=student_leave_application&transaction_id=" + transactionId, "_self")

    }  else if(entityName === "STUDENT_ADD_COMPLAIN") {
    
       var complainId = notificationDetails.metaData.complainId;
       window.open("/" + institute_unique_code
       + "/complaint-box/dashboard?action_type=student_add_complain&complain_id=" + complainId, "_self")

    } else if(entityName === "STUDENT_ADD_APPOINTMENT") {

       bellNotification.handleStudentAddAppointmentNotification(notificationDetails, institute_unique_code);

    } 
    else if(entityName === "ADD_VISITOR") {

      bellNotification.handleVisitorAddAppointmentNotification(notificationDetails, institute_unique_code);

   }else if(entityName === STAFF_LEAVE_APPLICATION){
      var transactionId = notificationDetails.metaData.transactionId;
      window.open("/"+ institute_unique_code 
        + "/leave-management/dashboard?action_type=staff_leave_application&transaction_id=" + transactionId, "_self")
    }
  },

  onClickNotificationStaff : function (ref) {
    notificationDetailsJson = $(ref).find(".notification-details-json").text();
    notificationDetails = JSON.parse(notificationDetailsJson);
    var notificationId = notificationDetails.notificationId;

    if((notificationDetails.clickedOn === null)) {
      ajaxClient.get("/staffportal/mark-as-read?notification_id=" + notificationId, function(data) {
        $("#notifications").html(data);
      });
    }

    var entityName = notificationDetails.entityName;
    var institute_unique_code = $("#institute-unique-code").text();

    var threadId = "";
    if(!(typeof(notificationDetails.metaData.threadId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
    }

    var conversationId = "";
    if(!(typeof(notificationDetails.metaData.conversationId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
        conversationId = notificationDetails.metaData.conversationId;
    }

    if(entityName === ONLINE_LECTURE_DISCUSSION) {

      var lectureId = notificationDetails.metaData.lectureId;

      window.open("/" + institute_unique_code
      + "/lecture-management/dashboard?action_type=discussion_board&lecture_id="
      + lectureId + "&thread_id=" + threadId + "&conversation_id=" + conversationId, "_self")

    } else if(entityName === HOMEWORK_DISCUSSION) {

      var homeworkId = notificationDetails.metaData.homeworkId;

      window.open("/" + institute_unique_code
      + "/homework-management/dashboard?action_type=discussion_board&homework_id="
      + homeworkId + "&thread_id=" + threadId + "&conversation_id=" + conversationId, "_self")

    } else if(entityName === NOTICE) {

      var noticeId = notificationDetails.metaData.noticeId;
      window.open("/" + institute_unique_code
      + "/noticeboard/dashboard?action_type=notice&notice_id=" + noticeId, "_self")

    } else if(entityName === STUDENT_LEAVE_APPLICATION) {

      var transactionId = notificationDetails.metaData.transactionId;
      window.open("/" + institute_unique_code
      + "/attendance/dashboard?action_type=student_leave_application&transaction_id=" + transactionId, "_self")

    } else if(entityName === "STUDENT_ADD_COMPLAIN") {

      var complainId = notificationDetails.metaData.complainId;
      window.open("/" + institute_unique_code
      + "/complaint-box/dashboard?action_type=student_add_complain&complain_id=" + complainId, "_self")

    } else if(entityName === "STUDENT_ADD_APPOINTMENT") {

       bellNotification.handleStudentAddAppointmentNotification(notificationDetails, institute_unique_code);

    }else if(entityName === STAFF_LEAVE_APPLICATION){
      var transactionId = notificationDetails.metaData.transactionId;
      window.open("/"+ institute_unique_code 
        + "/leave-management/dashboard?action_type=staff_leave_application&transaction_id=" + transactionId, "_self")
    }
  },

  onClickNotificationStudent : function (ref) {
    notificationDetailsJson = $(ref).find(".notification-details-json").text();
    notificationDetails = JSON.parse(notificationDetailsJson);
    var notificationId = notificationDetails.notificationId;

    if((notificationDetails.clickedOn === null)) {
      ajaxClient.get("/studentportal/mark-as-read?notification_id=" + notificationId, function(data) {
        $("#notifications").html(data);
      });
    }
    var entityName = notificationDetails.entityName;
    var institute_unique_code = $("#institute-unique-code").text();
    var threadId = "";
    if(!(typeof(notificationDetails.metaData.threadId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
    }
    var conversationId = "";
    if(!(typeof(notificationDetails.metaData.conversationId) === "undefined")) {
        threadId = notificationDetails.metaData.threadId;
        conversationId = notificationDetails.metaData.conversationId;
    }

    if(entityName === ONLINE_LECTURE_DISCUSSION || entityName === ONLINE_LECTURE) {
      $(".sidebar-menu").find('.nav-item').each(function() {
        $(this).removeClass("active");
        $(this).find(".sidebar_icon").css("display", "inline-block");
        $(this).find(".sidebar_icon_active").css("display", "none");
      });
      $("#lectureDetailsNav").parent().addClass("active");
      $("#lectureDetailsNav").find(".sidebar_icon").css("display", "none");
      $("#lectureDetailsNav").find(".sidebar_icon_active").css("display", "inline-block");
      if(entityName === ONLINE_LECTURE) {
        lectureDetails.loadLectureDetailsPage();
      } else if(entityName === ONLINE_LECTURE_DISCUSSION) {
        var lectureId = notificationDetails.metaData.lectureId;
        lectureDetails.getLectureDetailsPage(lectureId, entityName, threadId, conversationId);
      }
    } else if(entityName === HOMEWORK_DISCUSSION || entityName === HOMEWORK) {
      $(".sidebar-menu").find('.nav-item').each(function() {
        $(this).removeClass("active");
        $(this).find(".sidebar_icon").css("display", "inline-block");
        $(this).find(".sidebar_icon_active").css("display", "none");
      });
      $("#homeworkDetailsNav").parent().addClass("active");
      $("#homeworkDetailsNav").find(".sidebar_icon").css("display", "none");
      $("#homeworkDetailsNav").find(".sidebar_icon_active").css("display", "inline-block");
      if(entityName === HOMEWORK) {
        homeworkDetails.loadHomeworkDetailsPage();
      }
      else if(entityName === HOMEWORK_DISCUSSION) {
        var homeworkId = notificationDetails.metaData.homeworkId;
        homeworkDetails.getHomeworkDetailsPage(homeworkId, entityName, threadId, conversationId);
      }
    } else if(entityName === NOTICE) {
      $(".sidebar-menu").find('.nav-item').each(function() {
        $(this).removeClass("active");
        $(this).find(".sidebar_icon").css("display", "inline-block");
        $(this).find(".sidebar_icon_active").css("display", "none");
      });
      $("#noticesNav").parent().addClass("active");
      $("#noticesNav").find(".sidebar_icon").css("display", "none");
      $("#noticesNav").find(".sidebar_icon_active").css("display", "inline-block");
      notices.loadNoticesDetailsPage();
    }
  },

  handleStudentAddAppointmentNotification : function(notificationDetails, institute_unique_code) {
      if (notificationDetails.metaData && notificationDetails.metaData.appointmentId) {
          var appointmentId = notificationDetails.metaData.appointmentId;
          window.open("/" + institute_unique_code +
              "/parents-appointment/dashboard?action_type=student_add_appointment&appointment_id=" + appointmentId, "_self");
      }
  },
  handleVisitorAddAppointmentNotification : function(notificationDetails, institute_unique_code) {
    if (notificationDetails.metaData && notificationDetails.metaData.visitorId) {
        var visitorId = notificationDetails.metaData.visitorId;
        window.open("/" + institute_unique_code +
            "/visitors-desk/dashboard?action_type=visitor_add_appointment&visitor_id=" + visitorId, "_self");
    }
},

};


function removeURLParameter(url, parameter) {
    //prefer to use l.search if you have a location/link object
    var urlparts= url.split('?');
    if (urlparts.length>=2) {

        var prefix= encodeURIComponent(parameter)+'=';
        var pars= urlparts[1].split(/[&;]/g);

        //reverse iteration as may be destructive
        for (var i= pars.length; i-- > 0;) {
            //idiom for string.startsWith
            if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                pars.splice(i, 1);
            }
        }

        url= urlparts[0]+'?'+pars.join('&');
        return url;
    } else {
        return url;
    }
}



function forgotPasswordScreen () {
  ajaxClient.post("/core/forgot-password", {}, function(data){
    $(".limiter").html(data);
  });
}

function sendLinkToEmail () {
  var username = $("#username").val();
  if(username === "" || username === "undefined") {
    showErrorDialogBox("Please enter a username!")
    return;
  }
  ajaxClient.post("/core/forgot-password/" + username, {}, function(data){
      $(".outer-div").html(data);
  });
}

function getCapitalizeText (string) {
  return string.charAt(0).toUpperCase() + string.substr(1).toLowerCase()
}

function sideBarHoverEventCallback() {
    $(".sidebar-link").hover(
        function() {
            sidebarHoverOnState(this);
        },
        function() {
            if ($(this).parent().hasClass("active")) {
                sidebarHoverOnState(this);
            } else {
                sidebarHoverOutState(this);
            }
        });
}

function activateMenuItem() {
    $('.sidebar-link').click(function(e) {
        $('.sidebar-nav li.active').removeClass('active');
        var $parent = $(this).parent();
        $parent.addClass('active');

        sidebarHoverOutState($('.sidebar-nav').find('li').not('.active').find('.sidebar-link'));
        e.preventDefault();
    });
}

function sidebarHoverOutState(current) {
    $(current).find('img.sidebar_icon').attr("style", "");
    $(current).find('img.sidebar_icon_active').attr("style", "display:none");
}

function sidebarHoverOnState(current) {
    $(current).find('img.sidebar_icon').attr("style", "display:none");
    $(current).find('img.sidebar_icon_active').attr("style", "");
}

function showErrorDialogBox (msg) {
  $("#dialog-box-title").text("Error");
  $("#dialog-box-body").text(msg);
  $('#dialog-box-body').attr('style','color:red');
  $("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal">Ok</button>');
  $("#dialog-box-modal").modal({backdrop: 'static', keyboard: false});
}

function showWaringDialogBoxWithExistingModalDetails (msg, existingModal) {
  $("#dialog-box-title").html(`<i class="fas fa-exclamation-triangle"></i>Warning`);
  $("#dialog-box-body").text(msg);
  $('#dialog-box-body').attr('style','color:red');
  $("#modal-footer").html(`
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
    <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="togglePopup('${existingModal}')">Yes, Proceed</button>`);
  $("#dialog-box-modal").modal('toggle');
}

function showErrorDialogBoxWithExistingModalDetails (msg, existingModal) {
  // closing existingModal
  togglePopup(existingModal);
  $("#dialog-box-title").text("Error");
  $("#dialog-box-body").text(msg);
  $('#dialog-box-body').attr('style','color:red');
  // when ok is clicked opening existingModal
  $("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal" onclick="togglePopup(\'' + existingModal + '\')">Ok</button>');
  $("#dialog-box-modal").modal('toggle');
}

function togglePopup(existingModal) {
  $(existingModal).modal('toggle');
  $(".modal").each(function() {
      this.style.setProperty("overflow-y", "auto", "important");
  });
}

function showInformationDialogBox  (msg) {
  $("#dialog-box-title").text("Information");
  $("#dialog-box-body").text(msg);
  $('#dialog-box-body').attr('style','color:black');
  $("#modal-footer").html('<button type="button" class="btn btn-primary" data-dismiss="modal">Ok</button>');
  $("#dialog-box-modal").modal({backdrop: 'static', keyboard: false});
}


function initSelect2(title = "All", className = "select2") {
  $("." + className).each(function() {
      $(this)
        .wrap("<div class=\"position-relative\"></div>")
        .select2({
          placeholder: title,
          dropdownParent: $(this).parent(),
          closeOnSelect: false
        });
    });
}

function initSingleSelect2(ref, title = "All") {
  $(ref)
    .wrap("<div class=\"position-relative\"></div>")
    .select2({
      placeholder: title,
      dropdownParent: $(this).parent(),
      closeOnSelect: false
  });
}

function selectAllInSelect2() {
  $(".select2 > option").prop("selected", "selected");
  initSelect2('All');
}

function removeAllInSelect2() {
  $(".select2 > option").prop("selected", "");
  initSelect2('None');
}

function registerChangePassword() {
    $('#changePasswordNav').on('click', function() {
        changePassword.loadChangePasswordPage();
    });
}

var changePassword =  {

  loadChangePasswordPage : function () {
    var url = "";
    var userTypeForNotification = $("#user-type-for-notification").text();
    if(userTypeForNotification === ADMIN || userTypeForNotification === SYSTEM || userTypeForNotification === INTERNAL) {
      url = "/adminportal";
    } else if(userTypeForNotification === STUDENT) {
      url = "/studentportal";
    } else if(userTypeForNotification === STAFF) {
      url = "/staffportal";
    }
      ajaxClient.get(url + "/change-password", function(data) {
          $("#main-content").html(data);
      });
  },

  changePassword : function (){
    var oldPassword = $("#old-password").val();
    if(oldPassword === "") {
      showErrorDialogBox("Old Password cannot be empty");
      return;
    }
    var newPassword = $("#new-password").val();
    if(newPassword === "") {
      showErrorDialogBox("New Password cannot be empty");
      return;
    }
    var confirmNewPassword = $("#confirm-new-password").val();
    if(confirmNewPassword === "") {
      showErrorDialogBox("Confirm New Password cannot be empty");
      return;
    }
    var changePasswordInfo = {"oldPassword" : oldPassword, "newPassword" : newPassword};
    if(newPassword != confirmNewPassword){
      showErrorDialogBox("Password don't match!!");
    } else{
      ajaxClient.post("/adminportal/update-password",{'changePasswordInfo':JSON.stringify(changePasswordInfo)},
          function(data) {
            $("#change-password\\.status-modal-container").html(data);
            $("#change-password-status-modal").modal('toggle');
            changePassword.loadChangePasswordPage();
      });
    }
  },
};

var compressFileUtils = {

  compress : async function(file) {
    try {
      const originalSize = file.size;
      console.log("Original file size: " + (originalSize / 1024).toFixed(2) + "KB");
      const compressedBlob = await compressFileUtils.compressFile(file);
      const compressedBlobSize = compressedBlob.size;
      console.log("Compressed file size: " + (compressedBlobSize / 1024).toFixed(2) + "KB");
      const compressedFile = new File([compressedBlob], file.name);
      return compressedFile;

    } catch (error) {
      console.log("Error compressing file:" + error);
    }
    return file;
  },

  compressPDF : async function(file) {
        const pdfDoc = await PDFLib.PDFDocument.load(await file.arrayBuffer());
        const pdfBytes = await pdfDoc.save({ useObjectStreams: false });
        return new Blob([pdfBytes], { type: 'application/pdf' });
  },

  // Function to compress a DOC file
  compressDOC : async function(file) {
      const result = await mammoth.convertToHtml({ arrayBuffer: await file.arrayBuffer() });
      const blob = new Blob([result.value], { type: 'text/html' });
      return blob;
  },

  // Function to compress an Excel file
  compressExcel : function (file) {
      const reader = new FileReader();
      return new Promise((resolve, reject) => {
          reader.onload = (e) => {
              const data = new Uint8Array(e.target.result);
              const workbook = XLSX.read(data, { type: 'array' });
              const newFile = XLSX.write(workbook, { bookType: 'xlsx', type: 'array', compression: true });
              resolve(new Blob([newFile], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
          };
          reader.onerror = reject;
          reader.readAsArrayBuffer(file);
      });
  },

  // Function to compress a PowerPoint file
  compressPPT : function(file) {
      return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => {
              const data = new Uint8Array(e.target.result);
              const presentation = new PptxGenJS();
              presentation.load(data);
              const compressed = presentation.save('arraybuffer');
              resolve(new Blob([compressed], { type: file.type }));
          };
          reader.onerror = reject;
          reader.readAsArrayBuffer(file);
      });
  },

  // Function to compress an image
  compressImage : async function (file) {
      const options = {
          maxSizeMB: 1, // Maximum size in MB
          maxWidthOrHeight: 1920, // Max width or height
          useWebWorker: true // Use web worker for faster compression
      };
      try {
          const compressedFile = await imageCompression(file, options);
          return compressedFile;
      } catch (error) {
          console.log(error);
      }
  },

  // Function to compress a text file
  compressText : function (file) {
      return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => {
              const compressed = new Blob([e.target.result], { type: 'text/plain' });
              resolve(compressed);
          };
          reader.readAsText(file);
      });
  },

  // Function to compress files based on type
  compressFile : async function (file) {
      const type = file.type;
      if (type === 'application/pdf') {
          return await compressFileUtils.compressPDF(file);
      } else if (type === 'application/msword' || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          return await compressFileUtils.compressDOC(file);
      } else if (type === 'application/vnd.ms-excel' || type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          return await compressFileUtils.compressExcel(file);
      } else if (type === 'application/vnd.ms-powerpoint' || type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
          return await compressFileUtils.compressPPT(file);
      } else if (type.startsWith('image/')) {
          return await compressFileUtils.compressImage(file);
      } else if (type === 'text/plain') {
          return await compressText(file);
      } else {
          throw new Error('Unsupported file type');
      }
  }
};

function autofillRollNumbers() {
  var startingRollNumber = parseInt($("#roll-number-start").val());

  if (isNaN(startingRollNumber) || startingRollNumber <= 0) {
      showErrorDialogBox("Please enter a valid number greater than zero");
      return;
  }

  $('.roll-number-input').each(function(index) {
      $(this).val(startingRollNumber + index);
  });
}

var followup = {
  addFollowUpDetails : function (enquiryId, screen){
           var latestFollowupDate = getDate($("#latest-followup-date").val());
           if(latestFollowupDate != null){
             latestFollowupDate = latestFollowupDate.getTime()/1000;
           }
           initDateWithYearRange("-10:+10", true);
           $("#latest-followup-date").val(getFormattedDate(latestFollowupDate))
           initPastDateById("fee-transaction-follow-up-date", 4000);
           $("p#enquiry-follow-up-id").text(enquiryId);
           $("p#screen").text(screen);
           $("#contact-person-transaction-name").val("");
           $("#transaction-conversation").val("");
           $("#next-enquiry-follow-up-date").val("");
           $("#add-enquiry-follow-up-modal").modal('toggle');
      },

  addFollowupTrail :function (status){
           var invalid = validateMandatoryFields($("#add-enquiry-follow-up-modal"));
           if(invalid){
             return;
           }
           var sessionId = academicSessionHandler.getSelectedSessionId();
           var entityName = $("select#entity-transaction-name").val();
           var followUpDate = getDate($('#enquiry-follow-up-date').val());
           if(followUpDate == null){
             showErrorDialogBox("Please fill follow up date.");
             return;
           }
           followUpDate = followUpDate.getTime()/1000;
           var contactPersonName = $("#contact-person-transaction-name").val();
           var followUpType = $("select#follow-up-transaction-type").val();
           var conversation = $("#transaction-conversation").val();
           var nextFollowUpDate = getDate($('#next-enquiry-follow-up-date').val());
           if(nextFollowUpDate != null){
             nextFollowUpDate = nextFollowUpDate.getTime()/1000;
           }
           if(nextFollowUpDate == ""){
             nextFollowUpDate = null;
           }
           if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
             $("#add-enquiry-follow-up-modal").modal('toggle');
             showErrorDialogBox("Follow up date cannot exceed the next follow up date");
             return;
           }
           var screen = $("#screen").text()

           var followUpStatus = status;
           var entityId = $("#enquiry-follow-up-id").text();
           var followUpPayload = {"entityId" : entityId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
           "nextFollowUpDate":nextFollowUpDate,"amount": '',"followUpType":followUpType,"followUpStatus":followUpStatus}
           $("#add-enquiry-follow-up-modal").modal('toggle');

           ajaxClient.post("/admission/add-followup-trail/"+sessionId,{'followUpPayload':JSON.stringify(followUpPayload)}, function(data){
              $("#enquiry-status-modal-container").html(data);
              $("#update-enquiry-status-modal").modal({ backdrop: 'static', keyboard: false });

              if(screen === 'HOME'){
                admissionEnquiry.viewEnquiryDetails(true, "ACCEPTED")
              } else {
                admissionEnquiry.showDetails(entityId, true)
              }
          });
      },

    updateFollowupTrail : function(status){
             var invalid = validateMandatoryFields($("#update-enquiry-follow-up-modal"));
             if(invalid){
               return;
             }
             var sessionId = academicSessionHandler.getSelectedSessionId();
             var entityName = $("select#update-transaction-entity-name").val();
             var followUpDate = getDate($('#update-enquiry-follow-up-date').val());
             if(followUpDate == null){
               showErrorDialogBox("Please fill follow up date.");
               return;
             }
             followUpDate = followUpDate.getTime()/1000;
             var contactPersonName = $("#update-transaction-contact-person-name").val();
             var followUpType = $("select#update-transaction-follow-up-type").val();
             var conversation = $("#update-transaction-conversation").val();
             var nextFollowUpDate = $('#update-next-enquiry-follow-up-date').val();
             if(nextFollowUpDate!=""){
                nextFollowUpDate = getDate($('#update-next-enquiry-follow-up-date').val());
                nextFollowUpDate = nextFollowUpDate.getTime()/1000;
             }
             if(nextFollowUpDate == ""){
               nextFollowUpDate = null;
             }
             if(nextFollowUpDate!=null && followUpDate > nextFollowUpDate){
               $("#update-enquiry-follow-up-modal").modal('toggle');
               showErrorDialogBox("Follow up date cannot exceed the next follow up date");
               return;
             }
             var amount = $("#update-transaction-amount").val();
             var followUpStatus = status;
             var followUpId = $("#update-enquiry-follow-up-id").text();
             var entityId = $("#student-id").text();
             var updateFollowUpPayload = {"entityId" : entityId,"followUpId":followUpId,"entityName" : entityName, "followUpDate": followUpDate, "followUpMode": "MANUAL", "contactPersonName":contactPersonName, "conversation":conversation,
             "nextFollowUpDate":nextFollowUpDate,"amount":amount,"followUpType":followUpType,"followUpStatus":followUpStatus}
             $("#update-enquiry-follow-up-modal").modal('toggle');
             ajaxClient.post("/admission/update-followup-trail/"+sessionId,{'updateFollowUpPayload':JSON.stringify(updateFollowUpPayload)}, function(data){
                $("#enquiry-status-modal-container").html(data);
                $("#update-enquiry-status-modal").modal({ backdrop: 'static', keyboard: false });;
                admissionEnquiry.showDetails(entityId, true)
            });
       },

       deleteFollowUpDetails : function (followUpId,enquiryId){

             var modalHeader = "<h5 class=\"modal-title\" id=\"exampleModalLongTitle\">Delete Enquiry Follow Up Trail</h5><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>";
             var modalBody = "<strong color=\"red\">Are you sure that you want to Delete this followup trail ?</strong>";
             var modalFooter = "<p style=\"display:none\" id=\"delete-enquiry-follow-up-id\"></p><p style=\"display:none\" id=\"enquiry-follow-up-id\"></p><button type=\"button\" class=\"btn btn-danger\" onclick=\"followup.deleteFollowupTrail()\">Yes, Delete</button><button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\"> Cancel</button>";
             $("#modal-header").html(modalHeader);
             $("#modal-body").html(modalBody);
             $("#modal-footer").html(modalFooter);
             $("p#delete-enquiry-follow-up-id").text(followUpId);
             $("p#enquiry-follow-up-id").text(enquiryId);
             $("#enquiry-follow-up-close-modal").modal('toggle');
       },

       deleteFollowupTrail : function(){
           var academicSessionId = academicSessionHandler.getSelectedSessionId();
           var followUpId = $("#delete-enquiry-follow-up-id").text().trim();
           var entityId = $("#enquiry-follow-up-id").text().trim();
           if(followUpId == ""){
             showErrorDialogBox("Invalid Follow Up to cancel");
             return;
           }
           $("#enquiry-follow-up-close-modal").modal('toggle');
           ajaxClient.post( "/admission/delete-followup-trail/"+academicSessionId+"/"+followUpId, {}, function(data) {
              $("#enquiry-status-modal-container").html(data);
              $("#update-enquiry-status-modal").modal({ backdrop: 'static', keyboard: false });;
              admissionEnquiry.showDetails(entityId, true)
           });
       },



}

var graphicalReportGenerator = {
    loadGraphicalData : function(docContext, newWindow){
        var instituteStatsJson = $(docContext).find("#graphical-report-data").text();
        if (!instituteStatsJson || instituteStatsJson.trim() === ""  || instituteStatsJson.trim().toLowerCase() === "none") {
          return;
        }
        var instituteStats = JSON.parse(instituteStatsJson);
        //add chart type as per requirement currently using pie chart, bar graph and line graph
        instituteStats.chartDetailsList.forEach((chartDetails, index) => {
        var divId = `graphical-data-${index}`;

        if (chartDetails.chartType === "PIE_CHART") {
            graphicalReportGenerator.renderPieChart(chartDetails, docContext, newWindow, divId);
        } else {
           var chartType = chartDetails.chartType === "BAR_GRAPH" ? "bar" : "line";
           graphicalReportGenerator.renderBarGraph(chartDetails, docContext, newWindow, divId, chartType, chartDetails.stacked);
        }
        });

    },

    renderPieChart: function(chartDetails, docContext, newWindow, divId) {
       var pieChartData =  chartDetails.twoDChartDetails;
       var groupLabels = Object.keys(pieChartData);
       var groupColors = groupLabels.map(function(label) {
         return pieChartData[label].colorHexCode;
       });
       var values = groupLabels.map(function(label) {
         return pieChartData[label].value || 0;
       });

       var $canvas = $(docContext).find("#"+divId);
       if (!$canvas.length && newWindow.jQuery) {
         $canvas = newWindow.jQuery("#"+divId);
       }

       var ctx = $canvas.get(0).getContext("2d");
       new newWindow.Chart(ctx, {
           type: 'pie',
           data: {
             labels: groupLabels,
             datasets: [{
               data: values,
               backgroundColor: groupColors,
               borderColor: '#ffffff',
               borderWidth: 1
             }]
           },
           options: {
             responsive: true,
             maintainAspectRatio: false,
             plugins: {
               legend: {
                 display: true

               },
               title: {
                 display: true,
                 text: chartDetails.title


               }
             }
           }
         });
     },

    renderBarGraph: function(chartDetails, docContext, newWindow, divId, chartType, isStacked) {
           var xAxis = chartDetails.threeDChartDetails;
           var yAxis = Object.keys(xAxis);
           var firstItem = yAxis[0];
           var groupLabels = xAxis[firstItem] ? Object.keys(xAxis[firstItem]) : [];

           var groupColors = {};
           groupLabels.forEach(function(group) {
             groupColors[group] = (xAxis[firstItem] && xAxis[firstItem][group] && xAxis[firstItem][group].colorHexCode) || '#000000';
           });

           var datasets = groupLabels.map(function(group) {
             return {
               label: group,
               data: yAxis.map(function(label) {
                 return xAxis[label][group] ? xAxis[label][group].value : 0;
               }),
               backgroundColor: groupColors[group],
               borderColor: groupColors[group],
               hoverBackgroundColor: groupColors[group],
               hoverBorderColor: groupColors[group],
               barPercentage: 0.5
             };
           });

           var $canvas = $(docContext).find("#" + divId);
           if (!$canvas.length && newWindow.jQuery) {
             $canvas = newWindow.jQuery("#" + divId);
           }

           var ctx = $canvas.get(0).getContext("2d");
           new newWindow.Chart(ctx, {
             type: chartType,
             data: {
               labels: yAxis,
               datasets: datasets
             },
             options: {
               responsive: true,
               maintainAspectRatio: false,
               plugins: {
                 legend: {
                   display: true
                 },
                 title: {
                   display: true,
                   text: chartDetails.title
                 }
               },
               scales: {
                 x: {
                   stacked: isStacked,
                   title: {
                     display: true,
                     text: chartDetails.xAxisLabel
                   },
                   grid: {
                     display: true,
                     color: "#ccc"
                   }
                 },
                 y: {
                   stacked: isStacked,
                   beginAtZero: true,
                   title: {
                     display: true,
                     text: chartDetails.yAxisLabel
                   },
                   grid: {
                     display: true,
                     color: "#eee"
                   }
                 }
               }
             }
           });
         }
 };

