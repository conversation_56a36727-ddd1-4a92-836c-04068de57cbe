var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;
var NOTIFICATION = "NOTIFICATION";
var ACTION_TYPE = 'action_type';
var NOTICE_ID = 'notice_id';
var NOTIFICATION = "NOTIFICATION";
var INSTITUTE = "INSTITUTE";
var STUDENT = "STUDENT";
var STAFF = "STAFF";
var SAVED = "SAVED";
var BROADCASTED = "BROADCASTED";
var DESCRIPTION_COUNT = 2000;

$(document).ready(function() {

    menuLoader.registerSidebarMenu();

    var urlParams = new URLSearchParams(window.location.search);
    noticeDetails.dataCache.actionType = urlParams.get(ACTION_TYPE);
    noticeDetails.dataCache.noticeId = urlParams.get(NOTICE_ID);

    if(noticeDetails.dataCache.noticeId === "" || noticeDetails.dataCache.noticeId === null) {
        homePage.initHomePage();
    }
    else {
      $(".sidebar-nav").find('.sidebar-item').each(function() {
        $(this).removeClass("active");
      });
      $("#noticeDetailsNav").parent().addClass("active");
      noticeDetails.loadNoticeDetailsMainPage(NOTIFICATION);

      //remove params from url
      var url_string = window.location.href;
      url = removeURLParameter(url_string, ACTION_TYPE)
      url = removeURLParameter(url, NOTICE_ID)
      let stateObj = { id: "100" };
      window.history.replaceState(stateObj, url_string, url);

    }

    paymentReminder.readPaymentState();

});

var menuLoader = {

  registerSidebarMenu : function() {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.registerHomeMenu();
      menuLoader.registerAddNoticeMenu();
      menuLoader.registerNoticeDetailsMenu();
  },

  registerHomeMenu : function () {
    $('#homeNav').on('click', function() {
        homePage.loadHomePage();
    });
  },

  registerAddNoticeMenu : function () {
    $('#addNoticeNav').on('click', function() {
      addNotice.loadAddNoticeMainPage();
    });
  },

  registerNoticeDetailsMenu : function () {
    $('#noticeDetailsNav').on('click', function() {
       noticeDetails.loadNoticeDetailsMainPage("MAIN");
   });
  },

};

var homePage =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        homePage.displayDashboardContent();
    },

    loadHomePage : function () {
      ajaxClient.get("/noticeboard/home", function(data) {
          $("#main-content").html(data);
          homePage.initHomePage();
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/noticeboard/session-home/"+academicSessionId, function(data) {
          $("#noticeboard-management-dashboard-session-content").html(data);
          homePage.displayDashboardContent();
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

   displayDashboardContent : function () {

   },
};

var addNotice = {

  dataCache : {},

  loadAddNoticeMainPage : function () {
    ajaxClient.get("/noticeboard/add-notice-page", function(data) {
        $("#main-content").html(data);
        initSelect2('None');
        addNotice.bindStudentClassEvent();
        initFutureDate(365*5);
        addNotice.dataCache.attachments = [];
        addNotice.makeFileList();
        addNotice.bindDescriptionCount();
    });
  },

  bindDescriptionCount : function () {
    var maxLength = DESCRIPTION_COUNT;
    $('#add-notice-description').keyup(function() {
      var length = $(this).val().length;
      var length = maxLength-length;
      $('#chars').text(length);
    });
  },

  bindStudentClassEvent : function () {
    $("#add-notice-send-to").change(function() {
        var sendTo = $(this).find(':selected').val().trim();
        if(sendTo === STUDENT || sendTo === INSTITUTE) {
          $("#add-notice-class").attr("disabled", false);
          $("#add-notice-class").parent().find('button').removeClass('disabled');
          selectAllInSelect2();
        } else {
          removeAllInSelect2();
          $("#add-notice-class").parent().find('button').addClass('disabled');
          $("#add-notice-class").attr("disabled", true);
        }
    });
  },

  populateNoticeDetailsSaved : function (ref) {
    $("#save-notice-button-text").html("Yes, Save It");
    $("#action-value").text(SAVED)
    $("#save-notice-modal-text").html("Save Notice Details?");
  },

  populateNoticeDetailsBroadcasted : function (ref) {
    $("#save-notice-button-text").html("Yes, Broadcast It");
    $("#action-value").text(BROADCASTED)
    $("#save-notice-modal-text").html("Save and Broadcast Notice Details?");
  },

  addNotice : function () {
    $("#save-notice-modal").modal('toggle');
    var invalid = validateMandatoryFields($("#add-notice-modal"));
    if(invalid){
      showErrorDialogBox("Please fill all mandatory fields.");
      return;
    }

    var academicSessionId = $('#add-notice-academic-session').find(':selected').val().trim();

    var status = $("#action-value").text();
    if(status == BROADCASTED) {
      saveOnly = false;
    } else {
      saveOnly = true;
    }

    var title = $("#add-notice-title").val();
    var expiryDate =  getDate($("#add-notice-expiry-date").val());
    var expiryDateInt = null;
    if(expiryDate != null){
      expiryDateInt = expiryDate.getTime()/1000;
    }

    var sendTo = $('#add-notice-send-to').find(':selected').val().trim();
    var entityNameAndIdsMapping = {};
    if(sendTo === INSTITUTE) {
      entityNameAndIdsMapping[STAFF] = null;
      entityNameAndIdsMapping[STUDENT] = $("#add-notice-class").val();
    } else if (sendTo === STUDENT) {
      entityNameAndIdsMapping[STUDENT] = $("#add-notice-class").val();
    } else if (sendTo === STAFF) {
      entityNameAndIdsMapping[STAFF] = null;
    }

    var description = $("#add-notice-description").val();
    var isPinned = $("#add-notice-pin-it").is(":checked");

    var formData = new FormData();
    Array.from(addNotice.dataCache.attachments).forEach(file => formData.append('document', file));

     var noticePaylaodJson = { 'instituteId' : null, 'academicSessionId' : academicSessionId, 'title' : title, 'body' : description,
        'expiryDate' : expiryDateInt, 'pinned' : isPinned, 'status' : status,
        'entityNameAndIdsMapping' : entityNameAndIdsMapping };

    formData.append('noticePaylaodJson', JSON.stringify(noticePaylaodJson));

    ajaxClient.uploadFile("/noticeboard/add-notice/" + saveOnly, formData, function(data){
      $("#add-notice-status-modal-container").html(data);
      $("#add-notice-status-modal").modal({backdrop: 'static', keyboard: false});
      var response = JSON.parse($("#notice-data-response").text().trim());
      if(response.success){
        addNotice.resetAddNoticeModal();
      }
    });
  },

  resetAddNoticeModal : function () {
    $("#add-notice-title").val("");
    $("#add-notice-expiry-date").val("");
    $('#add-notice-send-to').val("");
    removeAllInSelect2();
    $("#add-notice-description").val("");
    $("#file-list").html("");
    $("#add-notice-pin-it").prop( "checked", false);
  },

  makeFileList : function () {
    $('#attachments').on('change',function(){
      var fileList = $(this)[0].files;
      var html = "";
      for (var i = 0; i < fileList.length; i++) {
        addNotice.dataCache.attachments.push(fileList[i]);
        var fileListStr = "<div class=\"col-sm-8\"><h6 style=\"margin-bottom:0%;color:blue;\">" + fileList[i].name + "</h6></div>";
        var button =  "<div class=\"col-sm-4\"><button type=\"button\" class=\"close remove-image\" aria-label=\"Close\"> <span aria-hidden=\"true\">×</span> </button></div>";
        html += "<div class=\"file-details-div row\" style=\"margin:1%;padding:1.5%;background-color:#f3f3f3ff;\">" + fileListStr + button + "</div>"
      }
      $("#file-list").append(html);
      addNotice.onClickRemoveImage();
    });
  },

  onClickRemoveImage : function () {
    $('.remove-image').on('click', function() {
      var fileName = $(this).parent().parent().find('h6').text();
      for(var i = 0; i < addNotice.dataCache.attachments.length; i++) {
        if(addNotice.dataCache.attachments[i].name === fileName) {
          addNotice.dataCache.attachments.splice(i, 1);
        }
      }
      $(this).parent().parent().remove();
    });
  },
}

var noticeDetails = {

  dataCache : {},

  changeSession: function() {
    noticeDetails.dataCache = {};
    noticeDetails.loadList(SAVED);
  },

  loadNoticeDetailsMainPage : function (screen) {
    ajaxClient.get("/noticeboard/notice-details-page", function(data) {
        $("#main-content").html(data);
        initDateWithYearRange("-2:+2", true);
        noticeDetails.dataCache.attachments = [];
        noticeDetails.dataCache.editAttachments = false;
        academicSessionHandler.bindSessionChangeEvent(noticeDetails.changeSession);
        if(screen === NOTIFICATION) {
          noticeDetails.loadNoticeDetailsModal(SAVED, true);
        } else {
          noticeDetails.loadNoticeDetailsModal(SAVED, true);
        }
    });
  },

  loadNoticeDetailsModal : function (screen, update) {
    var filterDate = getDate($("#notice-broadcasted-date").val());
    if(filterDate != null){
      filterDate = filterDate.getTime()/1000;
    } else {
      filterDate = 0;
    }
    if(screen == ""){
      screen = $("#screen").text()
    };
    var savedActive = true;
    if(!($('#notice-details-view-container').is(':empty'))){
      savedActive = $("#nav-after-enrollment-structure-tab").hasClass("active");
    }
    var page_number = 1;
    var itemsPerPage = 10;
    var offset = 0;
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/noticeboard/notice-details-list/" + filterDate + "/" + offset + "/" + itemsPerPage + "/" + screen + "/" + academicSessionId, function(data) {
        $("#notice-details-view-container").html(data);
        noticeDetails.loadActiveCancelledButton();
        noticeDetails.initNoticeListPage(page_number, screen);

        if(savedActive) {
          $("#nav-after-enrollment-structure-tab").addClass("active");
          $("#nav-after-enrollment-structure").addClass("active");
          $("#nav-after-enrollment-structure").addClass("show");
          $("#nav-before-registration-structure-tab").removeClass("active");
          $("#before-registration-structure").removeClass("active");
          $("#before-registration-structure").removeClass("show");
        }
        else {
          $("#nav-after-enrollment-structure-tab").removeClass("active");
          $("#nav-after-enrollment-structure").removeClass("active");
          $("#nav-after-enrollment-structure").removeClass("show");
          $("#nav-before-registration-structure-tab").addClass("active");
          $("#before-registration-structure").addClass("active");
          $("#before-registration-structure").addClass("show");
        }

        if(update) {
          noticeDetails.dataCache.attachments = [];
          noticeDetails.dataCache.editAttachments = false;
          noticeDetails.makeFileList();
        }

    });

  },

  loadActiveCancelledButton : function () {
    $('#nav-after-enrollment-structure-tab').on('click', function () {
      $('.page-item').removeClass('active');
      var page_number = 1;
      $('#page-number-'+page_number).addClass('active');
      noticeDetails.loadList(SAVED);
    });
    $('#nav-before-registration-structure-tab').on('click', function () {
      $('.page-item').removeClass('active');
      var page_number = 1;
      $('#page-number-'+page_number).addClass('active');
      noticeDetails.loadList(BROADCASTED);
    });
  },

  loadList : function (screen) {

    var filterDate = getDate($("#notice-broadcasted-date").val());
    if(filterDate != null){
      filterDate = filterDate.getTime()/1000;
    } else {
      filterDate = 0;
    }

    var page_number = $('.page-item.active').find('.page-number').text().trim();
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;
    $("#nav-tabContent-configured-staff-advance-list").html("");

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/noticeboard/notice-list/" + filterDate + "/" + offset + "/" + itemsPerPage + "/" + screen + "/" + academicSessionId, function(data) {
      $("#nav-tabContent-configured-staff-advance-list").html(data);
      noticeDetails.initNoticeListPage(page_number, screen);

    });
  },

  initNoticeListPage : function (page_number, screen) {
    var screenHeight = $("#sidebar").height() - $("#nav-tabContent-configured-staff-advance-list").position().top - 50;
    $('#nav-active-advance').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
    var response = JSON.parse($("#pagination-info").text().trim());
    $('#items-per-page').val(response.itemsPerPage);
    $('.page-item').removeClass('active');
    var pageNumber = (response.offset/response.itemsPerPage) + 1;
    $('#page-number-'+page_number).addClass('active');
    $("#screen").text(screen);
    noticeDetails.bindCheckboxEvents();
    noticeDetails.updateNoticeDetails();
    noticeDetails.viewNoticeDetails();
    noticeDetails.bindStudentClassEvent();
    noticeDetails.initPagination();
    noticeDetails.viewedDetails();

    // var currentDate = new Date();
    // $("#update-notice-expiry-date").datepicker({
    //     dateFormat: inputDatePickerFormat,
    //     changeMonth: true,
    //     changeYear: true,
    //     yearRange: "-100:+0"
    // });
    // $("#update-notice-expiry-date").datepicker("option", "minDate", currentDate);
    // var maxDate = new Date();
    // maxDate.setDate(maxDate.getDate() + 365);
    // $("#update-notice-expiry-date").datepicker("option", "maxDate", maxDate);

    initSelect2('None');

    $('.send-to-field').each(function () {
        var noticeJson = JSON.parse($(this).parent().find('.notice-json-info').text());
        var sendTo = "";
        var count = 0;
        $.each(noticeJson.entityNameAndIdsMapping, function( key, value ) {
          if(count == 1) {
           sendTo = INSTITUTE;
           return;
          }
          sendTo = key;
          count = count + 1;
        });
        $(this).text(sendTo);
    });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var screenName = $("#screen").text();
        if(screenName == BROADCASTED) {
          noticeDetails.loadList(BROADCASTED);
        }else {
            noticeDetails.loadList(SAVED);
        }
      },
      function () {
        var screenName = $("#screen").text();
        if(screenName == BROADCASTED) {
          noticeDetails.loadList(BROADCASTED);
        }else {
            noticeDetails.loadList(SAVED);
        }
      },
      function () {
        var screenName = $("#screen").text();
        if(screenName == BROADCASTED) {
          noticeDetails.loadList(BROADCASTED);
        }else {
            noticeDetails.loadList(SAVED);
        }
      },
      function () {
        var screenName = $("#screen").text();
        if(screenName == BROADCASTED) {
          noticeDetails.loadList(BROADCASTED);
        }else {
            noticeDetails.loadList(SAVED);
        }

      }
    );
  },

  bindStudentClassEvent : function () {
    $("#update-notice-send-to").change(function() {
        var sendTo = $(this).find(':selected').val().trim();
        if(sendTo === STUDENT || sendTo === INSTITUTE) {
          selectAllInSelect2();
          $("#update-notice-class").attr("disabled", false);
          $("#update-notice-class").parent().find('button').removeClass('disabled');
        } else {
          removeAllInSelect2();
          $("#update-notice-class").parent().find('button').addClass('disabled');
          $("#update-notice-class").attr("disabled", true);
        }
    });
  },

  makeFileList : function () {
    $('#attachments').on('change',function(){
      var fileList = $(this)[0].files;
      var html = "";
      for (var i = 0; i < fileList.length; i++) {
        noticeDetails.dataCache.attachments.push(fileList[i]);
        var fileListStr = "<div class=\"col-sm-8\"><h6 style=\"padding-right:8.5%;margin-bottom:0%;color:blue;\" class=\"new-upload-file-name\">" + fileList[i].name + "</h6></div>";
        var button =  "<div class=\"col-sm-4\"><h6 style=\"display:inline-block;cursor: pointer;float:right;\"><button type=\"button\" class=\"close remove-image\" aria-label=\"Close\"> <span aria-hidden=\"true\">×</span> </button></h6></div>";
        html += "<div class=\"file-details-div row\" style=\"margin:1%;padding:1.5%;background-color:#f3f3f3ff\">" + fileListStr + button + "</div>"
      }
      $("#update-file-list").append(html);
      noticeDetails.onClickRemoveImage();
    });
  },

  onClickRemoveImage : function () {
    $('.remove-image').on('click', function() {
      var fileName = $(this).parent().parent().parent().find('.new-upload-file-name').text();
      for(var i = 0; i < noticeDetails.dataCache.attachments.length; i++) {
        if(noticeDetails.dataCache.attachments[i].name === fileName) {
          noticeDetails.dataCache.attachments.splice(i, 1);
        }
      }
      $(this).parent().parent().parent().remove();
    });
  },

  bindCheckboxEvents : function () {

    $('.child').on('change', function(){
      $(this).parent().parent().parent().parent().find($(".parent")).prop( "checked", false );
    });

    $('.parent').on('change', function(){
      if($(this).prop("checked")){
        $(this).parent().parent().parent().parent().find($(".child")).prop( "checked", true );
      }
      else if(!($(this).prop("checked"))){
        $(this).parent().parent().parent().parent().find($(".child")).each(function() {
          $(this).prop( "checked", false);
        });
      }
    });
  },

  populateBroadcastNoticeDetailsModal : function (ref) {
    $("#broadcast-notice-modal-text").html("Broadcast all the selected notices?");
    var noticeIds = []
    $(ref).parent().parent().find($(".child")).each(function() {
      if($(this).prop("checked")){
        var noticeJson = JSON.parse($(this).parent().parent().find('.notice-json-info').text());
        noticeIds.push(noticeJson.noticeId);
      }
    });
    $("#notice-ids").text(JSON.stringify(noticeIds));
  },

  broadcastNotices : function (ref) {
    $("#broadcast-notice-modal").modal('toggle');
    noticeIds = JSON.parse($("#notice-ids").text());
    // the array is defined and has at least one element
    if (typeof noticeIds == 'undefined' || noticeIds.length <= 0) {
      showErrorDialogBox("No Notice selected");
      return;
    }
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.post("/noticeboard/broadcast-notice-status/" + academicSessionId, {'noticeIds' : JSON.stringify(noticeIds)}, function(data){
        $("#notice-status-modal-container").html(data);
        $("#notice-status-modal").modal({backdrop: 'static', keyboard: false});
        var screen  = $("#screen").text();
        noticeDetails.loadNoticeDetailsModal(screen, false);
   });
  },

  updateNoticeDetails : function () {
    $('.update-notice-details-button').on('click', function() {
      noticeDetails.resetNoticeDetailsPopup();
      noticeDetails.bindDescriptionCount();
      noticeDetails.fillNoticeDetails(this, false);
    });
  },

  bindDescriptionCount : function () {
    var maxLength = DESCRIPTION_COUNT;
    $('#update-notice-description').keyup(function() {
      var length = $(this).val().length;
      var length = maxLength-length;
      $('#chars').text(length);
    });
  },

  viewNoticeDetails : function () {
    $('.view-notice-details-button').on('click', function() {
      noticeDetails.resetNoticeDetailsPopup();
      noticeDetails.fillNoticeDetails(this, true);
    });
  },

  resetNoticeDetailsPopup : function () {
    $("#update-notice-title").val("");
    $("#update-notice-expiry-date").val("");
    $("#update-notice-send-to").val("");
    removeAllInSelect2();
    $("#update-notice-pin-it").prop( "checked", false);
    $("#update-notice-description").val("");
    $("#update-file-list").html("");
  },

  fillNoticeDetails : function (ref, viewOnly) {

    $(".modal-title").html("Update Notice Details");
    if(viewOnly) {
      $(".view-notice-modal-footer").html('<p style="display:none" id="selected-notice-json"></p><button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>');
    } else {
      $(".view-notice-modal-footer").html('<p style="display:none" id="selected-notice-json"></p><button type="button" class="btn btn-danger" id="cancel-button" data-dismiss="modal" onclick="closeModal()">Cancel</button><button id="update-notice-details" onclick="noticeDetails.updateNoticeDetailsButton(this)" type="button" class="btn btn-primary"  >Update Notice Details</button>');
    }

    var notice_details = $(ref).parent().find('.notice-details-info').text();
    $("#selected-notice-json").text(notice_details);
    var noticeDetails = JSON.parse(notice_details);

    $("#update-notice-title").val(noticeDetails.title);
    $("#update-notice-expiry-date").val(getFormattedDate(noticeDetails.expiryDate));

    var status = noticeDetails.status;
    var isBroadcastedNotice = false;
    if(status === "BROADCASTED") {
      isBroadcastedNotice = true;
    }

    var count = 0;
    $.each(noticeDetails.entityNameAndIdsMapping, function( key, value ) {
      if(count == 1) {
        sendTo = INSTITUTE;
        return;
      }
      sendTo = key;
      count = count + 1;
    });
    $("#update-notice-send-to").val(sendTo);

    if(sendTo === INSTITUTE || sendTo === STUDENT) {
      $("#update-notice-class").parent().find('button').removeClass('disabled');
      $("#update-notice-class").attr("disabled", false);
      $('#update-notice-class').val(noticeDetails.entityNameAndIdsMapping['STUDENT']);
      $('#update-notice-class').select2();
    } else {
      removeAllInSelect2();
      $("#update-notice-class").parent().find('button').addClass('disabled');
      $("#update-notice-class").attr("disabled", true);
    }

    $("#update-notice-pin-it").prop("checked", noticeDetails.pinned);
    $("#update-notice-description").val(noticeDetails.description);

    var characters = $.trim(noticeDetails.description).length;
    if (characters <= DESCRIPTION_COUNT) {
      $('#chars').text(DESCRIPTION_COUNT - characters);
    }


    $("#cancel-button").html("Cancel");
    var attachments = "";
    if (!(typeof noticeDetails.noticeBoardAttachments === 'undefined') && !(noticeDetails.noticeBoardAttachments === null)) {
      for(var i = 0; i < noticeDetails.noticeBoardAttachments.length; i++) {
        var p = "<p class=\"document-details-json\" style=\"display:none;\">" + JSON.stringify(noticeDetails.noticeBoardAttachments[i]) + "</p>";
        var h6 = "<div class=\"col-sm-8\"><h6 style=\"padding-right:5%;margin-bottom:0%;color:blue;\">" + noticeDetails.noticeBoardAttachments[i].documentName + "</h6></div>"
        var button =  "<div class=\"col-sm-4\"><h6 style=\"cursor: pointer;float:right;\" ><img class=\"sidebar_icon\" src=\"https://assetsv5.embrate.com/static/core/images/download-icon-20.png\" onclick=\"noticeDetails.downloadUpdateNoticeAttachment(this);\"></h6></div>"
        attachments = attachments + "<div class=\"row\" style=\"margin:1%;padding:1.5%;background-color:#f3f3f3ff;\">" + p + h6 + button + "</div>"
      }
    }
    if(attachments === "") {
      attachments = "<h5>No Attachments!</h5>"
    }
    $("#update-file-list").append(attachments);

    $("#update-notice-title").attr("disabled", false);
    $("#update-notice-expiry-date").attr("disabled", false);
    $("#update-notice-send-to").attr("disabled", false);
    $("#update-notice-pin-it").attr("disabled", false);
    $(".custom-file").css("display", "none");
    $(".remove-image").css("display", "none");
    $("#update-notice-description").attr("disabled", false);
    $("#edit-attachments").show();
    $("#edit-attachments").css("margin-top", "12%");

    if(viewOnly) {
      $("#update-notice-title").attr("disabled", true);
      $("#update-notice-expiry-date").attr("disabled", true);
      $("#update-notice-send-to").attr("disabled", true);
      $("#update-notice-pin-it").attr("disabled", true);
      $(".custom-file").css("display", "none");
      $(".remove-image").css("display", "none");
      $("#update-notice-description").attr("disabled", true);
      $("#edit-attachments").css("display", "none");
      $("#update-notice-class").parent().find('button').addClass('disabled');
      $("#update-notice-class").attr("disabled", true);
      $(".modal-title").html("View Notice Details");
    }

    if(isBroadcastedNotice && !viewOnly) {
      $("#update-notice-title").attr("disabled", true);
      $("#update-notice-expiry-date").attr("disabled", false);
      $("#update-notice-send-to").attr("disabled", true);
      $("#update-notice-pin-it").attr("disabled", false);
      $(".custom-file").css("display", "none");
      $(".remove-image").css("display", "none");
      $("#update-notice-description").attr("disabled", true);
      $("#edit-attachments").css("display", "none");
      $("#update-notice-class").parent().find('button').addClass('disabled');
      $("#update-notice-class").attr("disabled", true);
    }
  },

  editAttachments : function () {
    $(".custom-file").show();
    $("#update-file-list").html("");
    $("#edit-attachments").css("margin-top", "9.5%");
    noticeDetails.dataCache.editAttachments = true;
  },

  downloadUpdateNoticeAttachment : function (ref) {
    var documentDetailsJson = JSON.parse($(ref).parent().parent().parent().find('.document-details-json').text());
    var noticeDetailJson = JSON.parse($("#selected-notice-json").text());
    var documentId = documentDetailsJson.documentId;
    var userId = noticeDetailJson.createdBy;
    var documentType = 'NOTICE_BOARD_ATTACHMENTS';
    var noticeId = noticeDetailJson.noticeId;
    window.open(baseURL+"/noticeboard/download-notice-attachment/"
      + documentId + "/" + noticeId, '_blank');
  },

  updateNoticeDetailsButton : function (ref) {
    var invalid = validateMandatoryFields($("#notice-details-modal"));
    if(invalid){
      showErrorDialogBox("Please fill the mandatory fields.")
      return;
    }
    var notice_details = $(ref).parent().find('#selected-notice-json').text();
    var notice_details_json = JSON.parse(notice_details);
    var noticeId = notice_details_json.noticeId;
    var academicSessionId = notice_details_json.academicSessionId;
    var title = $("#update-notice-title").val();
    var expiryDate =  getDate($("#update-notice-expiry-date").val());
    var expiryDateInt = null;
    if(expiryDate != null){
      expiryDateInt = expiryDate.getTime()/1000;
    }
    var sendTo = $('#update-notice-send-to').find(':selected').val().trim();
    var entityNameAndIdsMapping = {};
    if(sendTo === INSTITUTE) {
      entityNameAndIdsMapping[STAFF] = null;
      entityNameAndIdsMapping[STUDENT] = $("#update-notice-class").val();
    } else if (sendTo === STUDENT) {
      entityNameAndIdsMapping[STUDENT] = $("#update-notice-class").val();
    } else if (sendTo === STAFF) {
      entityNameAndIdsMapping[STAFF] = null;
    }

    var description = $("#update-notice-description").val();
    var isPinned = $("#update-notice-pin-it").is(":checked");

    var formData = new FormData();
    Array.from(noticeDetails.dataCache.attachments).forEach(file => formData.append('document', file));

     var noticePaylaodJson = { 'instituteId' : null, 'academicSessionId' : academicSessionId, 'noticeId' : noticeId, 'title' : title, 'body' : description,
        'expiryDate' : expiryDateInt, 'pinned' : isPinned, 'entityNameAndIdsMapping' : entityNameAndIdsMapping };

    formData.append('noticePaylaodJson', JSON.stringify(noticePaylaodJson));
    $("#notice-details-modal").modal('toggle');

    ajaxClient.uploadFile("/noticeboard/update-notice/" + noticeDetails.dataCache.editAttachments, formData, function(data){
      $("#notice-status-modal-container").html(data);
      $("#notice-status-modal").modal({backdrop: 'static', keyboard: false});
      noticeDetails.dataCache.attachments = [];
      noticeDetails.dataCache.editAttachments = false;
      var screen  = $("#screen").text();
      noticeDetails.loadNoticeDetailsModal(screen, false);
    });
  },

  populateDeleteNoticeDetailsModal : function (ref) {
    var notice_details = $(ref).parent().find('.notice-details-info').text();
    $("#delete-selected-notice-json").text(notice_details);
    $("#delete-notice-details-modal-text").html("Are you sure you want to delete Notice Details?");
  },

  deleteNoticeDetails : function (ref) {
     $("#delete-notice-modal").modal('toggle');
     var notice_details = $(ref).parent().find('#delete-selected-notice-json').text();
     var notice_details_json = JSON.parse(notice_details);
     var noticeId = notice_details_json.noticeId;
     var screen  = $("#screen").text();

     ajaxClient.post("/noticeboard/delete-notice-details/"+noticeId, {}, function(data){
       $("#notice-status-modal-container").html(data);
       $("#notice-status-modal").modal({backdrop: 'static', keyboard: false});
       noticeDetails.loadNoticeDetailsModal(screen, false);
     });
   },

   viewedDetails : function () {
     $('.viewed-details-button').on('click', function() {
       var notice_details = JSON.parse($(this).parent().find('.notice-details-info').text());
       var noticeId = notice_details.noticeId;
       ajaxClient.get("/noticeboard/notice-viewed-details/" + noticeId, function(data) {
         $("#notice-details-main-container").html(data);
       });
     });
   }
};
