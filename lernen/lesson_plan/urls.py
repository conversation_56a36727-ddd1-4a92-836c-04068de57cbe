from django.contrib import admin
from django.urls import path
from django.urls import include
from lesson_plan.views import *

urlpatterns = [
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),
    path('lesson-plan-view-page', lesson_plan_main_page_view, name='lesson_plan_main_page_view'),
    path('standards/<academic_session_id>', get_session_standards_view, name='get_session_standards_view'),
    path('course-lesson-plan-structure/<standard_id>/<section_id>/<academic_session_id>', course_lesson_plan_view, name='course_lesson_plan_view'),
    path('lesson-plan-structure/<lesson_plan_id>/<lesson_plan_status>', lesson_plan_structure, name="lesson_plan_structure"),
    path('lesson-plan-details/<academic_session_id>', save_lesson_plan_data, name="save_lesson_plan_data"),
    path('staff-response-details/<academic_session_id>', save_staff_lesson_plan_response, name="save_staff_lesson_plan_response"),
    path('lesson-plan-clone-details/<academic_session_id>', clone_lesson_plan_details, name="clone_lesson_plan_details"),
    path('lesson-plan-delete-details/<academic_session_id>/<lesson_plan_id>', delete_lesson_plan_details, name="delete_lesson_plan_details"),
]