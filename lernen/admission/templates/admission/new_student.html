<div class="row add-student-screen" id="add-new-student-screen">
  <div class="col-12">
    <div class="card">
      <div class="card-header" id="add-new-student-screen">
        <p style="display: none;" class="latest-session-id">{{latest_session.academicSessionId}}</p>
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="#">Admission</a></li>
            <li class="breadcrumb-item">Add New Student</li>
            <li class="ml-auto"><a href="#" onclick="documentPage.generateStaticAdmissionForm();"><u>Download Form</u></a></li>
          </ol>
        </nav>
      </div>
      <div class="card-body">
          <nav>
            <div class="nav nav-tabs" id="add.nav-tab" role="tablist">
              <a class="nav-item nav-link active" id="add-student-basic-info" data-bs-toggle="tab" data-bs-target="#add.basic-info-content" role="tab" aria-controls="" aria-selected="true">Basic Information</a>
              <a class="nav-item nav-link disabled" data-bs-toggle="tab" data-bs-target="#" role="tab" aria-controls="" aria-selected="false">Family Information</a>
              <a class="nav-item nav-link disabled" data-bs-toggle="tab" data-bs-target="#" role="tab" aria-controls="" aria-selected="false">Guardian Information</a>
              <a class="nav-item nav-link disabled" data-bs-toggle="tab" data-bs-target="#" role="tab" aria-controls="" aria-selected="false">Previous School Information</a>
              <a class="nav-item nav-link disabled" data-bs-toggle="tab" data-bs-target="#" role="tab" aria-controls="" aria-selected="false">Medical Information</a>
              <a class="nav-item nav-link disabled" data-bs-toggle="tab" data-bs-target="#" role="tab" aria-controls="" aria-selected="false">Documents Information</a>
            </div>
          </nav>

          <div class="tab-content">
            <div class="tab-pane fade show active" id="add.basic-info-content" role="tabpanel" aria-labelledby="nav-basic-info-tab">
              <br>
              {% include 'admission/student_basic_info.html'%}
              <div style="display: flex;  align-items: center;  justify-content: center;">
                   <button style="width:30%;" type="submit" class="btn btn-primary" onclick="loadCreateStudentConfirmModal()" >Register Student</button>
               </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</div>

<div class="row update-student-screen" id="add.update-student-screen" style="display:none">
  <div class="col-12">
    <div class="card">
      <div class="card-header" id="add-new-student-screen">
      </div>
      <div class="card-body">
          {% include 'admission/update_student.html'%}
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="student-document-warning-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
   <div class="modal-dialog modal-dialog-centered" role="document">
     <div class="modal-content">
       <div class="modal-header">
         <h5 class="modal-title" id="exampleModalLongTitle">Update Student Infomation </h5>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close">
           <span aria-hidden="true">&times;</span>
         </button>
       </div>
       <div class="modal-body">
         <span style="color:red" id="student-document-warning-popup-text">Do you want to save student details?</span>
       </div>
       <div class="modal-footer">
           <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
           <button type="button" class="btn btn-danger" onclick="updateStudentWithScreen(true, 'ADMIT-STUDENT')">Yes, update</button>
       </div>
     </div>
   </div>
 </div>

<div id="create-student-confirm-modal-container"></div>
<div id="admission-status-modal-container"></div>
