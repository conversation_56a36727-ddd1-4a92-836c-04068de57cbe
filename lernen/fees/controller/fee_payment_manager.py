import time
import json
import uuid
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *
from core.controller.utils.cache_provider import *
from django.core.cache import cache


restclient = getrestclient()

DEMAND_NOTICE_CACHE_NAMESPACE = "demand-notice-student-store-cache"

DEFAULT_SESSION_COUNT_FOR_DUE_FEE_REMINDERS = 3

def get_report_data(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_status = report_data['studentStatus']
    response =  restclient.get(user_login_view, "/2.0/fees/reports/"+str(institute_id)+"/view-report/"+report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&requiredHeaders="+str(report_data["required_headers"])+"&multiple_academic_session_ids="+str(report_data['multiple_academic_session_ids'])+"&start="+str(report_data['start_date'])+"&end="+str(report_data['end_date'])+"&fee_ids="+report_data["fee_ids"]+"&required_standards="+report_data['required_standards']+"&fee_due_date="+str(report_data['fee_due_date'])+ "&student_status="+student_status + "&user_id="+user['uuid'] + "&fee_head_ids="+report_data["fee_head_ids"]+"&fee_payment_transaction_status="+str(report_data['fee_payment_transaction_status'])+"&fee_report_data_type="+str(report_data['fee_report_data_type'])+"&transaction_mode="+str(report_data['transaction_mode'])+"&hide_student_with_zero_fees_assignment="+str(report_data['hide_student_with_zero_fees_assignment'])+"&show_student_with_zero_wallet_balance="+str(report_data['show_student_with_zero_wallet_balance'])+"&report_row_column_data_visibility="+str(report_data['report_data_visibility']))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}

def get_report(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_status = report_data['studentStatus']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/fees/reports/"+str(institute_id)+"/generate-report/"+report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&requiredHeaders="+str(report_data["required_headers"])+"&multiple_academic_session_ids="+str(report_data['multiple_academic_session_ids'])+"&start="+str(report_data['start_date'])+"&end="+str(report_data['end_date'])+"&fee_ids="+report_data["fee_ids"]+"&required_standards="+report_data['required_standards']+"&fee_due_date="+str(report_data['fee_due_date'])+ "&student_status="+student_status + "&user_id="+user['uuid'] + "&download_format="+str(report_data['download_format'])+"&fee_head_ids="+report_data["fee_head_ids"]+"&fee_payment_transaction_status="+str(report_data['fee_payment_transaction_status'])+"&fee_report_data_type="+str(report_data['fee_report_data_type'])+"&transaction_mode="+str(report_data['transaction_mode'])+"&hide_student_with_zero_fees_assignment="+str(report_data['hide_student_with_zero_fees_assignment'])+"&report_row_column_data_visibility="+str(report_data['report_data_visibility']))

def collect_fee_payment(user_login_view, institute_unique_code, fee_payment_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    fee_payment_payload['feePaymentTransactionMetaData']['instituteId'] = institute_id

    response = restclient.post(user_login_view, "/2.0/fee-payment?user_id="+user['uuid'],fee_payment_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'fee_payment_response' :  response.get_data(), 'message' : "Fee Payment Collected Successfully!!!"}

def add_fee_followup(user_login_view,institute_unique_code,academic_session_id,follow_up_payload):
        institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
        user = user_login_view['user']
        follow_up_payload['addedBy'] = user['uuid']
        follow_up_payload['updatedBy'] = user['uuid']
        response = restclient.post(user_login_view, "/2.0/follow-up/add-follow-up?institute_id="+str(institute_id)+"&user_id="+user['uuid'],follow_up_payload)
        if not response.is_success():
            return {'success' : False , 'message' : response.get_error_message()}
        return {'success' : True ,  'message' : "Fee Follow Up Trail Added Successfully!!!"}

def update_fee_followup(user_login_view,institute_unique_code,academic_session_id,update_follow_up_payload):
        institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
        user = user_login_view['user']
        update_follow_up_payload['addedBy'] = user['uuid']
        update_follow_up_payload['updatedBy'] = user['uuid']
        response = restclient.put(user_login_view, "/2.0/follow-up/update-followup?institute_id="+str(institute_id)+"&user_id="+user['uuid'],update_follow_up_payload)
        if not response.is_success():
            return {'success' : False , 'message' : response.get_error_message()}
        return {'success' : True ,  'message' : "Fee Follow Up Trail Updated Successfully!!!"}


def cancel_fee_payment(user_login_view, institute_unique_code, fee_cancelled_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    fee_cancelled_payload['instituteId'] = institute_id
    fee_cancelled_payload['cancelBy'] = user['uuid']
    response = restclient.post(user_login_view, "/2.0/fee-payment/cancel-transaction?institute_id="+str(institute_id) + "&user_id="+user['uuid'], fee_cancelled_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Fee Payment Cancelled Successfully!!!"}

def delete_fee_followup(user_login_view, institute_unique_code, academic_session_id, follow_up_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/follow-up/delete-follow-up/"+str(follow_up_id)+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Fee Followup Trail Deleted Successfully!!!"}

def close_fee_followup(user_login_view, institute_unique_code, academic_session_id,is_student,status, follow_up_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/follow-up/update-follow-up-status/"+str(follow_up_id)+"/"+str(status)+"?is_student="+str(is_student)+"&institute_id="+str(institute_id)+ "&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Fee Followup Trail Closed Successfully!!!"}


def get_fee_payment_details(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/summary/" + str(student_id) +"?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    all_session_fee_payment_details_list = response.get_data()
    other_sessions_due_amount = 0
    other_sessions_due_amount_till_today = 0
    session_fee_payment_details = {'other_sessions' : [], 'other_sessions_due_amount' : other_sessions_due_amount, 'other_sessions_due_amount_till_today' : other_sessions_due_amount_till_today}
    for session_fee_payment_details_entry in all_session_fee_payment_details_list:
        
        fee_payment_details_list = session_fee_payment_details_entry['feePaymentDetailsList']
        total_fee_head_payment_details_list = session_fee_payment_details_entry['totalFeeHeadPaymentDetailsList']
        session_id = session_fee_payment_details_entry['student']['studentAcademicSessionInfoResponse']['academicSession']['academicSessionId']
        unpaid_fee_list = []
        full_paid_fee_list = []
        partially_paid_fee_list = []
        due_fee_list = []

        for fee_payment_details in fee_payment_details_list:
            if(fee_payment_details['feePaymentStatus']== 'PAID'):
                full_paid_fee_list.append(fee_payment_details)
                continue
            elif(fee_payment_details['feePaymentStatus']== 'PARTIALLY_PAID'):
                partially_paid_fee_list.append(fee_payment_details)
            elif(fee_payment_details['feePaymentStatus']== 'UNPAID'):
                unpaid_fee_list.append(fee_payment_details)

            due_fee_list.append(fee_payment_details)

        fee_details = {'student' : session_fee_payment_details_entry['student'], 'total_fee_head_payment_details_list' : total_fee_head_payment_details_list, 'total_due_amount' : session_fee_payment_details_entry['totalDueAmount'], 'total_due_amount_till_today' : session_fee_payment_details_entry['totalDueAmountTillToday'], 'total_due_fine_amount' :session_fee_payment_details_entry['totalDueFineAmount'], 'total_paid_fine_amount':session_fee_payment_details_entry['totalPaidFineAmount'], 'total_assigned_amount' : session_fee_payment_details_entry['totalAssignedAmount'], 'total_paid_amount' : session_fee_payment_details_entry['totalPaidAmount'], 'total_instant_discount_amount':session_fee_payment_details_entry['totalInstantDiscountAmount'], 'total_assigned_discountAmount' : session_fee_payment_details_entry['totalAssignedDiscountAmount'], 'total_given_discount' : session_fee_payment_details_entry['totalGivenDiscount'],  'full_paid_fee_list' : full_paid_fee_list, 'partially_paid_fee_list' : partially_paid_fee_list , 'unpaid_fee_list' : unpaid_fee_list, 'due_fee_list' : due_fee_list}
        if str(session_id) == str(academic_session_id):
            session_fee_payment_details['selected_session'] = fee_details
        else:
            existing_session_details = session_fee_payment_details['other_sessions']
            existing_session_details.append(fee_details)
            session_fee_payment_details['other_sessions'] = existing_session_details
            other_sessions_due_amount = other_sessions_due_amount + session_fee_payment_details_entry['totalDueAmount'] + session_fee_payment_details_entry['totalDueFineAmount']
            other_sessions_due_amount_till_today = other_sessions_due_amount_till_today + session_fee_payment_details_entry['totalDueAmountTillToday'] + session_fee_payment_details_entry['totalDueFineAmount']


    session_fee_payment_details['other_sessions_due_amount'] = other_sessions_due_amount
    session_fee_payment_details['other_sessions_due_amount_till_today'] = other_sessions_due_amount_till_today

    return session_fee_payment_details

def get_fee_followup_details_by_student_id(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    entity_name = "STUDENT"
    response = restclient.get(user_login_view, "/2.0/follow-up/follow-up/"+ str(institute_id)+ "?entity_id="+ str(student_id)+"&entity_name="+str(entity_name))
    if not response.is_success():
        return None
    return response.get_data()

def get_fee_payment_transactions(user_login_view, institute_unique_code, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/transactions/" + str(student_id) +"?institute_id="+str(institute_id))
    if not response.is_success():
        return None

    transactions = response.get_data()
    transactions = [ add_payment_display_date(transaction) for transaction in transactions ]
    transactions = [ add_json_value(transaction) for transaction in transactions ]

    active_transactions = []
    cancelled_transactions = []
    for transaction in transactions:
        if transaction['feePaymentTransactionStatus'] == 'ACTIVE':
            active_transactions.append(transaction)
        elif transaction['feePaymentTransactionStatus'] == 'CANCELLED':
            cancelled_transactions.append(transaction)
    return {'transactions' : transactions, 'active_transactions' : active_transactions, 'cancelled_transactions':cancelled_transactions}

def get_all_fee_payment_transactions(user_login_view, institute_unique_code, status, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/transactions/?institute_id="+str(institute_id)+"&status="+str(status)+"&offset="+str(offset)+"&limit="+str(limit))
    if not response.is_success():
        return None

    return _create_fee_payment_transactions_response(response.get_data())

def get_session_fee_payment_transactions(user_login_view, institute_unique_code, academic_session_id, status, offset=None, limit=None, text=""):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    if offset is None or limit is None or text is None:
        response = restclient.get(user_login_view, "/2.0/fee-payment/transactions-search?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&status="+str(status))
    else:
        response = restclient.get(user_login_view, "/2.0/fee-payment/transactions-search?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&status="+str(status)+"&search_text="+text+"&limit="+str(limit)+"&offset="+str(offset))

    if not response.is_success():
        return None
    return _create_fee_payment_transactions_response(response.get_data())

def get_student_fee_details(user_login_view,institute_unique_code,academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/follow-up/student-follow-up-details?institute_id=" + str(institute_id))
    return _get_fee_followup_details_response(response.get_data())

def _get_fee_followup_details_response(studentFollowUpDetailsList):
    follow_up_type = {}
    active_follow_up = []
    inactive_follow_up = []
    inactive_list = []
    student_id_list = []
    active_list = []
    student_id_active_list = []
    for studentFollowUpDetails in studentFollowUpDetailsList:
        studentId = studentFollowUpDetails['studentDetailedRow']['studentId']
        followUpType = studentFollowUpDetails['studentFollowUpDetailsRowList']
        if studentId not in follow_up_type:
            for followUp in followUpType:
                if followUp['followUpStatus'] == "ACTIVE":
                    active_follow_up.append(followUp)
                elif followUp['followUpStatus'] == "INACTIVE":
                    inactive_follow_up.append(followUp)

    for inactive in inactive_follow_up:
         if inactive['studentDetailedRow']['studentId'] not in student_id_list:
             inactive_list.append(inactive)
             student_id_list.append(inactive['studentDetailedRow']['studentId'])

    for  active in active_follow_up:
         if active['studentDetailedRow']['studentId'] not in student_id_active_list:
             active_list.append(active)
             student_id_active_list.append(active['studentDetailedRow']['studentId'])

    active_students = {student['studentDetailedRow']['studentId'] for student in active_list}
    inactive_students = [student for student in inactive_list if student['studentDetailedRow']['studentId'] not in active_students]
    return {'active_follow_up':active_list,'inactive_follow_up':inactive_students}


def _create_fee_payment_transactions_response(transactions_with_pagination):
    active_transactions = []
    cancelled_transactions = []
    for transaction in transactions_with_pagination['result']:
        if transaction['feePaymentTransactionStatus'] == 'ACTIVE':
            active_transactions.append(transaction)
        elif transaction['feePaymentTransactionStatus'] == 'CANCELLED':
            cancelled_transactions.append(transaction)
    return {'transactions' : transactions_with_pagination['result'], 'active_transactions' : active_transactions, 'cancelled_transactions':cancelled_transactions, 'paginationInfo' : transactions_with_pagination['paginationInfo']}


def get_fee_payment_transaction_details(user_login_view, institute_unique_code, transaction_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/student-transaction-details/" + str(transaction_id) +"?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()
    # transaction_details = response.get_data()
    # add_payment_display_date(transaction_details['feePaymentTransactionMetaData'])
    # return add_json_value(transaction_details)

def get_fee_payment_invoice_summary(user_login_view, institute_unique_code, transaction_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/invoice/" + str(transaction_id) +"/summary?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()

def get_fee_payment_pdf_invoice_summary(user_login_view, institute_unique_code, transaction_id, office_copy=True):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/fee-payment/invoice/"+transaction_id+"/pdf-summary?institute_id="+str(institute_id)+"&office_copy="+str(office_copy))

def get_bulk_pdf_invoice_summary(user_login_view, institute_unique_code, report_data, office_copy=False):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/fee-payment/bulk-invoice-pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(report_data['academic_session_id'])+"&start_date="+str(report_data['start_date'])+"&end_date="+str(report_data['end_date'])+"&fee_transaction_status="+report_data["fee_transaction_status"]+"&office_copy="+str(office_copy)+"&user_id="+user['uuid'])

# def get_pdf_demand_notice(request, user):
#     institute_id = user['instituteId']
#     return restclient.get_file(user_login_view, "/2.0/fee-payment/generate-demand-notice?institute_id="+str(institute_id)+"&academic_session_id="+str(request.GET.get("academic_session_id",0))+"&due_date="+str(request.GET.get("due_date",0))+"&compute_fine="+str(request.GET.get("compute_fine",False))+"&student_ids="+str(request.GET.get("student_ids","")))

def store_demand_notice_students(demand_notice_payload):
    demand_notice_request_id = str(uuid.uuid1())
    cache.set(demand_notice_request_id, json.dumps(demand_notice_payload), 300)
    return demand_notice_request_id

def get_pdf_demand_notice(user_login_view, institute_unique_code, demand_notice_request_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    demand_notice_payload_json = cache.get(demand_notice_request_id)
    if demand_notice_payload_json is None:
        return None
    demand_notice_payload = json.loads(demand_notice_payload_json)

    total_session_count = 1
    if demand_notice_payload['includePastSessions']:
        total_session_count = DEFAULT_SESSION_COUNT_FOR_DUE_FEE_REMINDERS

    payload = {'instituteId': institute_id, 'academicSessionId': demand_notice_payload['academicSessionId'], 'dueDate' : demand_notice_payload['dueFeesDate'], 'computeFine': demand_notice_payload['includeFine'], 'requiredStanardsCSV' : demand_notice_payload['requiredStanardsCSV'], 'studentIds' : demand_notice_payload['students'], 'totalSessionCount' : total_session_count}
    return restclient.get_file_with_payload(user_login_view, "/2.0/fee-payment/generate-demand-notice?institute_id="+str(institute_id)+ "&user_id="+user['uuid'], payload)

def get_fee_payment_invoice(user_login_view, institute_unique_code, transaction_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/fee-payment/invoice/" + str(transaction_id) +"?institute_id="+str(institute_id))
    if not response.is_success():
        return None

    return response.get_data()

def get_class_payment_stats(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/fee-payment/class-stats/" + str(institute_id) +"?academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return None
    return get_fee_stats_formatted_data(response.get_data())

def get_date_wise_class_payment_stats(user_login_view, institute_unique_code, academic_session_id, date):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/fee-payment/date-wise-class-stats/" + str(institute_id)+"?academic_session_id="+str(academic_session_id) + "&date="+str(date))
    if not response.is_success():
        return None
    return get_fee_stats_formatted_data(response.get_data())

def get_fee_stats_formatted_data(data):
    if not data:
        return None
    feeHeadAssignedAmountMap = data['feeHeadAssignedAmountMap']
    feeHeadDiscountAmountMap = data['feeHeadDiscountAmountMap']
    feeHeadCollectedAmountMap = data['feeHeadCollectedAmountMap']
    feeHeadDueAmountMap = data['feeHeadDueAmountMap']

    feeHeadAssignedAmountStr = ""
    feeHeadDiscountAmountStr = ""
    feeHeadCollectedAmountStr = ""
    feeHeadDueAmountStr = ""

    if feeHeadAssignedAmountMap is not None:
        for key, value in feeHeadAssignedAmountMap.items():
            if key is not None and value is not None:
                feeHeadAssignedAmountStr = feeHeadAssignedAmountStr + key + " : ₹ " + str(round(value, 2)) + "\n"

    if feeHeadDiscountAmountMap is not None:
        for key, value in feeHeadDiscountAmountMap.items():
            if key is not None and value is not None:
                feeHeadDiscountAmountStr = feeHeadDiscountAmountStr + key + " : ₹ " + str(round(value, 2)) + "\n"

    if feeHeadCollectedAmountMap is not None:
        for key, value in feeHeadCollectedAmountMap.items():
            if key is not None and value is not None:
                feeHeadCollectedAmountStr = feeHeadCollectedAmountStr + key + " : ₹ " + str(round(value, 2)) + "\n"

    if feeHeadDueAmountMap is not None:
        for key, value in feeHeadDueAmountMap.items():
            if key is not None and value is not None:
                feeHeadDueAmountStr = feeHeadDueAmountStr + key + " : ₹ " + str(round(value, 2)) + "\n"

    data['feeHeadAssignedAmountStr'] = feeHeadAssignedAmountStr
    data['feeHeadDiscountAmountStr'] = feeHeadDiscountAmountStr
    data['feeHeadCollectedAmountStr'] = feeHeadCollectedAmountStr
    data['feeHeadDueAmountStr'] = feeHeadDueAmountStr

    return data

def get_due_fees_students(user_login_view, institute_unique_code, academic_session_id, due_date, include_fine, required_standards, include_past_sessions):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    session_count = 1
    # query param is string
    if include_past_sessions == "true":
        # Total session count to compute the due amount
        session_count = DEFAULT_SESSION_COUNT_FOR_DUE_FEE_REMINDERS

    return restclient.get(user_login_view, "/2.0/fee-payment/due-fees-students?institute_id=" + str(institute_id) +"&academic_session_id="+str(academic_session_id)+"&due_date="+str(due_date)+"&include_fine="+str(include_fine)+"&required_standards="+str(required_standards)+"&session_count="+str(session_count)).get_data()

def send_due_payment_reminders(user_login_view, institute_unique_code, send_reminder_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    send_reminder_payload['instituteId'] = institute_id
    if send_reminder_payload['includePastSessions']:
        # Total session count to compute the due amount
        send_reminder_payload['totalSessionCount'] = DEFAULT_SESSION_COUNT_FOR_DUE_FEE_REMINDERS
    response = restclient.post(user_login_view, "/2.0/fee-payment/send-due-payment-reminder?institute_id="+str(institute_id) + "&user_id="+user['uuid'],send_reminder_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Reminder request submitted successfully. Reminders are being sent. Please check history tab for status."}

# def get_due_fees_notification_history(user_login_view, institute_unique_code, academic_session_id):
#     institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
#     user = user_login_view['user']
#     return restclient.get(user_login_view, "/2.0/fee-payment/due-reminder-history/SMS?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_batch_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/fee-payment/batch-notification-history/"+delivery_mode+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&offset="+str(offset)+"&limit="+str(limit)).get_data()

def get_individual_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/fee-payment/individual-notification-history/"+delivery_mode+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&offset="+str(offset)+"&limit="+str(limit)).get_data()

def get_batch_notification_details(user_login_view, institute_unique_code, user_type, batch_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/fee-payment/batch-notification-detail/"+user_type+"/"+batch_id+"?institute_id="+str(institute_id)).get_data()


def recharge_student_wallet(user_login_view, institute_unique_code, wallet_recharge_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_wallet_recharge_paylaod = {'instituteId' : institute_id, 'userId' : wallet_recharge_payload['studentId'], 'userType' : 'STUDENT', 'walletTransactionCategory' : 'RECHARGE', 'amount' : wallet_recharge_payload['amount'], 'transactionBy' : user['uuid'], 'description' : wallet_recharge_payload['description'], 'transactionMode' : wallet_recharge_payload['transactionMode'], 'transactionDate' : wallet_recharge_payload['transactionDate']}
    response = restclient.post(user_login_view, "/2.0/user-wallet/recharge?institute_id="+str(institute_id), student_wallet_recharge_paylaod)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'fee_payment_response' :  response.get_data(), 'message' : "Wallet Recharged Successfully!!!"}

def get_wallet_transactions(user_login_view, institute_unique_code, user_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/user-wallet/"+user_id+"/history?institute_id="+str(institute_id)).get_data()

def add_json_value(obj):
    obj['json_value'] = json.dumps(obj)
    return obj

def add_payment_display_date(transaction):
    payment_date_value = transaction['transactionDate']
    payment_date_str = ""
    if(payment_date_value is not None):
        payment_date = get_datetime_in_timezone(int(float(payment_date_value)),INDIA_TIMEZONE)
        payment_date_str = payment_date.strftime('%d-%b-%Y')
    transaction['paymentDisplayDate'] = payment_date_str
    return transaction

def get_sibling_due_fee_details(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/fee-payment/siblings-fee-detail/" + str(student_id) +"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_mobile_notification_history(user_login_view, institute_unique_code, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/fee-payment/bell-notifications-with-pagination?institute_id="+str(institute_id)+"&offset="+str(offset)+"&limit="+str(limit)).get_data()

def collect_sibling_fees(user_login_view, institute_unique_code, academic_session_id, fee_payment_payload_lite_list):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/fee-payment/collect-bulk-student-fees?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'], fee_payment_payload_lite_list)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'fee_payment_response' :  response.get_data(), 'message' : "Fee Payment Collected Successfully!!!"}

def get_fee_payment_pdf_bulk_invoice_summary(user_login_view, institute_unique_code, transaction_ids_str, academic_session_id, office_copy=True):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/fee-payment/bulk-invoice/pdf-summary?institute_id="+str(institute_id)+"&transaction_ids_str="+str(transaction_ids_str)+"&office_copy="+str(office_copy)+"&academic_session_id="+str(academic_session_id))

def fetch_restrict_session_fees_payment(user_login_view, institute_unique_code, session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/fee-payment/restrict-session-fee-payment/" + str(student_id) +"?institute_id="+str(institute_id)+"&academic_session_id="+str(session_id)).get_data()

def get_wallet_pdf_invoice(user_login_view, institute_unique_code, transaction_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/user-wallet/invoice/"+transaction_id+"/pdf?institute_id="+str(institute_id))

def get_student_fee_challan(user_login_view, institute_unique_code, academic_session_id, student_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/fees/fee-challan/"+str(student_id)+"/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid'])

def update_wallet_transaction_amount(user_login_view, institute_unique_code, update_wallet_transaction_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    transaction_id = update_wallet_transaction_payload['transactionId']
    student_id = update_wallet_transaction_payload['studentId']
    amount = update_wallet_transaction_payload['amount']
    
    response = restclient.put(user_login_view, "/2.0/user-wallet/transaction/"+transaction_id+"/update-amount?institute_id="+str(institute_id)+"&user_id=" + str(student_id) + "&amount=" + str(amount)+"&updated_by="+user['uuid'], {})
    
    if not response.is_success():
        return {'success': False, 'message': response.get_error_message()}
    return {'success': True, 'message': "Wallet Transaction Amount Updated Successfully!!!"}


def delete_wallet_transaction(user_login_view, institute_unique_code, delete_wallet_transaction_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    transaction_id = delete_wallet_transaction_payload['transactionId']
    student_id = delete_wallet_transaction_payload['studentId']
    
    response = restclient.delete(user_login_view, "/2.0/user-wallet/transaction/"+transaction_id+"?institute_id="+str(institute_id)+"&user_id=" + str(student_id)+"&deleted_by="+user['uuid'], {})
    
    if not response.is_success():
        return {'success': False, 'message': response.get_error_message()}
    return {'success': True, 'message': "Wallet Transaction Deleted Successfully!!!"}