import time
import json
from core.controller.utils.restclient import *
from core.controller.utils.cache_provider import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

restclient = getrestclient()
cache_provider = get_cache_provider()

def get_report(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/study-tracker/reports/generate-report/"+str(institute_id)+ "/" +report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&standard_id="+str(report_data['standard_id'])+"&section_ids="+str(report_data['section_ids'])+"&course_type_str="+str(report_data['course_type_str'])+"&start="+str(report_data['startDate'])+"&end="+str(report_data['endDate'])+"&download_format="+str(report_data['download_format'])+"&user_id="+user['uuid'])

def get_report_data(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/study-tracker/reports/view-report/"+str(institute_id)+"/"+report_data['report_type']+"?academic_session_id="+str(report_data['academic_session_id'])+"&standard_id="+str(report_data['standard_id'])+"&section_ids="+str(report_data['section_ids'])+"&course_type_str="+str(report_data['course_type_str'])+"&start="+str(report_data['startDate'])+"&end="+str(report_data['endDate'])+"&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}