$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    homePage.initHomePage();
    paymentReminder.readPaymentState();
});

var menuLoader = {

    registerSidebarMenu : function () {
        sideBarHoverEventCallback();
        activateMenuItem();
        menuLoader.registerHomeMenu();
        menuLoader.registerstudyTrackerMenu();
    },
  
    registerHomeMenu : function () {
        $('#homeNav').on('click', function() {
            homePage.loadHomePage();
        });
    },
    registerstudyTrackerMenu : function(){
         $('#studyTrackerReportNav').on('click', function(){
            studyTrackerReport.loadReportsMenu();
        });
    },
};

var homePage =  {
    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);

    },

    loadHomePage : function () {
      ajaxClient.get("/study-tracker/home", function(data) {
          $("#main-content").html(data);
          academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
      });
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/study-tracker/session-home/"+academicSessionId, function(data) {
          $("#study-tracker-dashboard-session-content").html(data);
      });
    },

    refreshHomePage : function () {
        homePage.loadHomePageForSession();
    },

};

var studyTrackerReport={
    loadReportsMenu: function (){
      ajaxClient.get("/study-tracker/reports", function(data) {
          $("#main-content").html(data);
          initDateInput();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          studyTrackerReport.bindGenerateReportEvent();
          studyTrackerReport.bindPopupClassSubjectEvent();
          reportUtils.bindSelectClassCheckboxEvent();
          // $('.selectpicker').selectpicker();
          initSelect2("Select Section ....", "study-tracker-section");
          $('.select2-search__field').attr("style", "width:100%");
          initSelect2("Select Course Type ....", "course-type");
          var currectSession = JSON.parse($(".currect-session-json").text());
          $('.report-academic-session').val(currectSession.academicSessionId);
      });
    },
    bindGenerateReportEvent : function () {
        $('.report-academic-session').on('change', function(){
        var ref = this;
        var academicSessionId = $(ref).val();
        ajaxClient.get("/study-tracker/report-session-change/" + academicSessionId , function(data) {
            $(ref).parent().parent().parent().find(".class-study-tracker-div").html(data);
            initSelect2("Select Section ....", "study-tracker-section");
            studyTrackerReport.bindPopupClassSubjectEvent();
        });
    });

    $('.generate-report').on('click', function () {
        var containerElement = $(this).closest('div.report-field-container');
        var invalid = validateMandatoryFields($(containerElement));
        if(invalid){
          return;
        }
        var academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        academicSession = academicSession === "" ? 0 : academicSession;
        var studyTrackerReportType = $(containerElement).find('p.report-type').text().trim();
        var standardId = "";
        var sectionIds = "";
        var courseTypes = "";
        var attendanceStartDate = -1;
        var attendanceEndDate = -1;
        var attendanceStatus = "";

        standardId = $(containerElement).find('.study-tracker-student-class').val();
        sectionIds = $(containerElement).find('.study-tracker-section').val();
        courseTypes = $(containerElement).find('.course-type').val();


        if($(containerElement).find('input.study-tracker-start-date').length > 0){
          if($(containerElement).find('input.study-tracker-start-date').val() != "") {
            attendanceStartDate = getDate($(containerElement).find('input.study-tracker-start-date').val()).getTime()/1000;
          }
        }
        if(studyTrackerReportType == "MONTHLY_AVG_STUDY_TRACKER"){
            if($(containerElement).find('input.study-tracker-end-date').length > 0){
            if($(containerElement).find('input.study-tracker-end-date').val() != "") {
                attendanceEndDate = getDate($(containerElement).find('input.study-tracker-end-date').val()).getTime()/1000;
            }
            }
            var difference = attendanceEndDate - attendanceStartDate;
            // To calculate the no. of days between two dates
            var differenceInDays = difference / (3600 * 24);
            if(differenceInDays < 0 || differenceInDays >= 31) {
                alert("Total number of days cannot be greater than 31. Please reduce the range!");
                return;
            }
        }
        sectionIds = sectionIds.join();
        courseTypes= courseTypes.join();
       
        $(this).closest('div.modal').modal('toggle');
        window.open(baseURL+"/study-tracker/generate-report/"+studyTrackerReportType
        + "?academic_session_id="+academicSession + "&standard_id="+standardId+"&section_ids="
        + sectionIds+"&course_type="
        + courseTypes+"&startDate="+attendanceStartDate+"&endDate="+attendanceEndDate, '_blank');
    });
  },
  bindPopupClassSubjectEvent : function () {
      $(".study-tracker-student-class").change(function() {
          var classId = $(this).find(':selected').val().trim();
          if(classId == "" || classId == undefined) {
            showErrorDialogBox("Please select correct standard.");
            return;
          }
          $('.study-tracker-section').html("");
          var containerElement = $(this).closest('div.report-field-container');
          studyTrackerReport.updatePopupStandardSections(containerElement, classId);
      });
    },
    updatePopupStandardSections : function (containerElement, standardId) {
        var standards = JSON.parse($(containerElement).find(".standards-json").text());
        var classSectionMap = {}
        for(var i = 0 ; i < standards.length ; i++){
            var standard = standards[i]
            classSectionMap[standard.standardId] = standard
        }
        var options = "";
        if(!(standardId in classSectionMap)){
            $('.study-tracker-section').html(options);
            return ;
        }
        var standard = classSectionMap[standardId];
        for(var i = 0 ; i < standard.standardSectionList.length; i++){
            options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
        }
        $('.study-tracker-section').html(options);
    },

  resetPopUp : function () {
    $(".study-tracker-start-date").val("");
    $(".study-tracker-start-end-date").val("");
  },
};