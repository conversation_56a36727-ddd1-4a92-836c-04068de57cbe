// inputDatePickerFormat = "dd-mm-yy";
inputDatePickerFormat = "DD-MMM-YYYY";
inputDateParserFormat = 'DD-MMM-YYYY' // These both format must be same for validation
pastTransactionDays = 365;
var weekday = new Array(7);
weekday[0] = "Sunday";
weekday[1] = "Monday";
weekday[2] = "Tuesday";
weekday[3] = "Wednesday";
weekday[4] = "Thursday";
weekday[5] = "Friday";
weekday[6] = "Saturday";

var shortWeekday = new Array(7);
shortWeekday[0] = "Sun";
shortWeekday[1] = "Mon";
shortWeekday[2] = "Tue";
shortWeekday[3] = "Wed";
shortWeekday[4] = "Thu";
shortWeekday[5] = "Fri";
shortWeekday[6] = "Sat";
//Offset as  '+5.5'

var time_formats = [
  [60, 'seconds', 1], // 60
  [120, '1 minute ago', '1 minute from now'], // 60*2
  [3600, 'minutes', 60], // 60*60, 60
  [7200, '1 hour ago', '1 hour from now'], // 60*60*2
  [86400, 'hours', 3600], // 60*60*24, 60*60
  [172800, 'Yesterday', 'Tomorrow'], // 60*60*24*2
  [604800, 'days', 86400], // 60*60*24*7, 60*60*24
  [1209600, 'Last week', 'Next week'], // 60*60*24*7*4*2
  [2419200, 'weeks', 604800], // 60*60*24*7*4, 60*60*24*7
  [4838400, 'Last month', 'Next month'], // 60*60*24*7*4*2
  [29030400, 'months', 2419200], // 60*60*24*7*4*12, 60*60*24*7*4
  [58060800, 'Last year', 'Next year'], // 60*60*24*7*4*12*2
  [2903040000, 'years', 29030400], // 60*60*24*7*4*12*100, 60*60*24*7*4*12
  [5806080000, 'Last century', 'Next century'], // 60*60*24*7*4*12*100*2
  [58060800000, 'centuries', 2903040000] // 60*60*24*7*4*12*100*20, 60*60*24*7*4*12*100
];
var month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

function getCurrentDateInTimezone(offset) {
    // create Date object for current location
    var d = new Date();
    // convert to msec
    // subtract local time zone offset
    // get UTC time in msec
    var utc = d.getTime() + (d.getTimezoneOffset() * 60000);
    // create new Date object for different city
    // using supplied offset
    var nd = new Date(utc + (3600000 * offset));
    return nd;
}

function getCurrentDate() {
    return getCurrentDateInTimezone('+5.5');
}

function getDayStart(date) {
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    date.setMilliseconds(0);

    return date;
}

function getDayEnd(date) {
    date.setHours(23);
    date.setMinutes(59);
    date.setSeconds(59);
    date.setMilliseconds(0);

    return date;
}

function formatDateToDDMMYYYY(date) {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}
function initDateRangePicker() {
    $("input[name=\"daterange\"]").daterangepicker({
      opens: "left"
    });
    $("input[name=\"datetimes\"]").daterangepicker({
      timePicker: true,
      opens: "left",
      startDate: moment().startOf("hour"),
      endDate: moment().startOf("hour").add(32, "hour"),
      locale: {
        format: "M/DD hh:mm A"
      }
    });
    $("input[name=\"datesingle\"]").daterangepicker({
      singleDatePicker: true,
      showDropdowns: true,
      minDate: moment().startOf("hour").subtract(40, "days"),
      maxDate: moment().startOf("hour"),
      locale: {
        format: "DD/MMM/YYYY"
      }
    });
    var start = moment().subtract(29, "days");
    var end = moment();

    function cb(start, end) {
      $("#reportrange span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"));
    }

    $("#reportrange").daterangepicker({
      startDate: start,
      endDate: end,
      ranges: {
        "Today": [moment(), moment()],
        "Yesterday": [moment().subtract(1, "days"), moment().subtract(1, "days")],
        "Last 7 Days": [moment().subtract(6, "days"), moment()],
        "Last 30 Days": [moment().subtract(29, "days"), moment()],
        "This Month": [moment().startOf("month"), moment().endOf("month")],
        "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
      }
    }, cb);
    cb(start, end);
}

function initDateInput() {
    $(".select-date").daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      showDropdowns: true,
      maxDate: moment().startOf("day"),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      }
    }, bind_daterangepicker_apply_event);

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}

function initDateWithYearRange(yearRangeArg) {
  initDateWithYearRange(yearRangeArg, false);
}

function initDateWithYearRange(yearRangeArg, setTodaysDate) {
    var range = yearRangeArg.split(":");
    var pastYears = parseInt(range[0].substr(1))
    var futureYears = parseInt(range[1].substr(1))
    $(".select-date").daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      defaultDate: null,
      showDropdowns: true,
      minDate: moment().startOf("day").subtract(pastYears, "year"),
      maxDate: moment().startOf("day").add(futureYears, "year"),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      }
    }, bind_daterangepicker_apply_event);

    if(setTodaysDate) {
      $(".select-date").val(getTodayDateInInputFormat());
    }

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}

function initUpdateDateInId(id, selectedDate){
  $('#'+id).val(selectedDate);
  const picker = $('#' + id).data('daterangepicker');
  if (picker && selectedDate) {
    const momentDate = moment(selectedDate, inputDatePickerFormat);
    picker.setStartDate(momentDate);
    picker.setEndDate(momentDate);
  }
}

function initDateById(id, yearRangeArg, setTodaysDate) {
  var range = yearRangeArg.split(":");
  var pastYears = parseInt(range[0].substr(1))
  var futureYears = parseInt(range[1].substr(1))
  $("#" + id).daterangepicker({
    autoUpdateInput: false,
    autoApply : true,
    singleDatePicker: true,
    defaultDate: null,
    showDropdowns: true,
    minDate: moment().startOf("day").subtract(pastYears, "year"),
    maxDate: moment().startOf("day").add(futureYears, "year"),
    locale: {
      format: inputDatePickerFormat,
      cancelLabel: 'Clear'
    }
  }, bind_daterangepicker_apply_event);

  if(setTodaysDate) {
    $(".select-date").val(getTodayDateInInputFormat());
  }

  initOnChangeDateFeature();

  bind_daterangepicker_clear_event();
}

function initOnChangeDateFeature(){
  $(".select-date").on('apply.daterangepicker', function (ev, picker) {
      const formattedDate = picker.startDate.format(inputDatePickerFormat);
      $(this).val(formattedDate).trigger('change');
    });

    $(".select-date").on('show.daterangepicker', function (ev, picker) {
      // Ensure the calendar always re-applies the current selection
      picker.setStartDate(picker.startDate.clone());
      picker.setEndDate(picker.endDate.clone());
    });
}

function initDate(pastDays) {
    $(".select-date").daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      showDropdowns: true,
      minDate: moment().startOf("day").subtract(pastDays, "days"),
      maxDate: moment().startOf("day"),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      }
    }, bind_daterangepicker_apply_event);

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}

function initDateWithChange(pastDays, triggerMethod) {
    $(".select-date").daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      showDropdowns: true,
      minDate: moment().startOf("day").subtract(pastDays, "days"),
      maxDate: moment().startOf("day"),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      },
      onSelect: function(dateText) {
        console.log("Selected date: " + dateText + ", Current Selected Value= " + this.value);
        $(this).change();
      }
    }, bind_daterangepicker_apply_event).on("change", function() {
      triggerMethod
    });

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}

function initDateDuration(startDateInSec, endDateInSec) {
    $(".select-date").daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      showDropdowns: true,
      minDate: new Date(startDateInSec * 1000),
      maxDate: new Date(endDateInSec * 1000),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      }
    }, bind_daterangepicker_apply_event);

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}

function initPastDateById(id, pastDays) {
    $("#" + id).daterangepicker({
      autoUpdateInput: false,
      autoApply : true,
      singleDatePicker: true,
      showDropdowns: true,
      minDate: moment().startOf("day").subtract(pastDays, "days"),
      maxDate: moment().startOf("day"),
      locale: {
        format: inputDatePickerFormat,
        cancelLabel: 'Clear'
      }
    }, bind_daterangepicker_apply_event);

    initOnChangeDateFeature();

    bind_daterangepicker_clear_event();
}


function initFutureDate(futureDays) {
  $(".select-date").daterangepicker({
    autoUpdateInput: false,
    autoApply : true,
    singleDatePicker: true,
    showDropdowns: true,
    minDate: moment().startOf("day"),
    maxDate: moment().startOf("day").add(futureDays, "days"),
    locale: {
      format: inputDatePickerFormat,
      cancelLabel: 'Clear'
    }
  }, bind_daterangepicker_apply_event);

  initOnChangeDateFeature();

  bind_daterangepicker_clear_event();
}


// https://github.com/dangrossman/daterangepicker/issues/815
function bind_daterangepicker_apply_event(start_date, end_date) {
  $(this.element[0]).val(start_date.format(inputDatePickerFormat));
}

function bind_daterangepicker_clear_event() {
  $(".select-date").on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
  });
}

function getPreviousDate(days){
    var currentDate = new Date();
    currentDate.setDate(currentDate.getDate() - days);
    return currentDate;
}

function getDate(inputDate) {
    var momentDate = moment(inputDate, inputDateParserFormat);
    if (momentDate.isValid()) {
        return momentDate.toDate();
    }
    return null;
}

function convertToEpochTime(date, time) {
  if (time == "" && time == undefined && time == null) return null;
  var currentDate = new Date(date * 1000);

  var token = time.split(":");
  var hours = token[0];
  var minutes = token[1];

  currentDate.setHours(hours, minutes, 0, 0);

  return Math.floor(currentDate.getTime() / 1000);
}

function convertMinutesToTime(minutes) {
  if(minutes != "" && minutes != undefined && minutes != null) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  }
  return null;
}

function getHourMinuteTime (timeVal) {
  if(timeVal != "" && timeVal != undefined && timeVal != null) {
    var token = timeVal.split(":");
    var hour = token[0];
    var minute = token[1];
    return {'hour' : hour, 'minute' : minute};
  }
  return {'hour' : 0, 'minute' : 0};
}

//Epoch time in seconds
function getFormattedDate(epochTime){
  if(epochTime != null && epochTime > 0){
    var date = new Date(epochTime * 1000); // Convert epoch time to milliseconds
    return formatDateToString(date);
  }
  return null;
}

function convertEpochTimeToTime(epochTime){
  if(epochTime != null && epochTime > 0){
    var date = new Date(epochTime * 1000); // Convert epoch time to milliseconds
    return formatTimeToString(date);
  }
  return null;
}

function convertTimeToMinute(timeVal){
  if(timeVal != undefined && timeVal != null){
    var hour = timeVal.hour;
    var minute = timeVal.minute;
    var totalMinute = hour * 60 + minute;
    return totalMinute;
  }
  return 0
}

function formatDateToString(date) {
  var day = String(date.getDate()).padStart(2, '0'); // Get day and pad with leading zero
  var month = month_names[date.getMonth()]; // Get month name
  var year = date.getFullYear(); // Get full year
  // Construct the formatted date string
  return (day + "-" + month + "-" + year);
}

function formatDateToStringWithYearFirst(date) {
  var day = String(date.getDate()).padStart(2, '0'); // Get day and pad with leading zero
  var month = month_names[date.getMonth()]; // Get month name
  var year = date.getFullYear(); // Get full year
  // Construct the formatted date string
  return (year + "-" + month + "-" + day);
}

function formatTimeToString(date) {
  return formatTimeToStringWithShowSecondsFlag(date, false);
}

function formatTimeToStringWithShowSecondsFlag(date, showSeconds) {
  // Format the time
  var hours = String(date.getHours()).padStart(2, '0');
  var minutes = String(date.getMinutes()).padStart(2, '0');
  var seconds = String(date.getSeconds()).padStart(2, '0');
  // Construct the formatted date string
  if(!showSeconds) {
    return (hours + ":" + minutes);
  }
  return (hours + ":" + minutes + ":" + seconds);
}

function getFormattedDateInNumber(epochTime){
  if(epochTime != null && epochTime > 0){
    dateVal = new Date(epochTime*1000);
    return formatDateToString(dateVal);
  }
  return null;
}

function setFormattedDate(epochTime, id){
  var date = getFormattedDate(epochTime);
  if(date == null) {
    return null;
  }
  var momentDate = moment(date, 'DD-MMM-YYYY');
  var picker = $(id).data('daterangepicker');
  picker.setStartDate(momentDate);
  picker.setEndDate(momentDate); // Not necessary for singleDatePicker, but for consistency
  $(id).val(momentDate.format(picker.locale.format)); // Update input value

  return date;
}

function getWeekDay(epochTime){
  if(epochTime != null && epochTime > 0){
    dateVal = new Date(epochTime*1000);
    return weekday[dateVal.getDay()];
  }
  return null;
}

function getShortWeekDay(epochTime){
  if(epochTime != null && epochTime > 0){
    dateVal = new Date(epochTime*1000);
    return shortWeekday[dateVal.getDay()];
  }
  return null;
}

function getFormattedDateWithTime(epochTime){
  if(epochTime != null && epochTime > 0){
    dateVal = new Date(epochTime*1000);
    return formatDateToString(dateVal) + " " + formatTimeToString(dateVal);
  }
  return null;
}

function getFormattedDateWithTimeWithYearFirst(epochTime){
  if(epochTime != null && epochTime > 0){
    dateVal = new Date(epochTime*1000);
    return formatDateToStringWithYearFirst(dateVal) + " " + formatTimeToString(dateVal);
  }
  return null;
}

function getFormattedTime(time) {
  return ("0" + time).slice(-2);
}

function getTodayDateInInputFormat() {
  var today = new Date();
  return formatDateToString(today);

}

function getTodayDateInInputFormatWithYearFirst() {
  var today = new Date();
  return formatDateToStringWithYearFirst(today);
}


function time_ago(time1) {
  var time =  new Date(time1*1000);
  switch (typeof time) {
    case 'number':
      break;
    case 'string':
      time = +new Date(time);
      break;
    case 'object':
      if (time.constructor === Date) time = time.getTime();
      break;
    default:
      time = +new Date();
  }

  var seconds = (+new Date() - time) / 1000,
    token = 'ago',
    list_choice = 1;

  if (seconds == 0) {
    return 'Just now'
  }
  if (seconds < 0) {
    seconds = Math.abs(seconds);
    token = 'from now';
    list_choice = 2;
  }
  var i = 0,
    format;
  while (format = time_formats[i++])
    if (seconds < format[0]) {
      if (typeof format[2] == 'string')
        return format[list_choice];
      else
        return Math.floor(seconds / format[2]) + ' ' + format[1] + ' ' + token;
    }
  return time;
}

function initMultipleDateRange(id, pastDays, separator, datePickerFormat) {
  $("#" + id).daterangepicker({
      autoApply: true,
      autoUpdateInput: false,
      singleDatePicker: false,
      showDropdowns: true,
      minDate: moment().startOf("day").subtract(pastDays, "days"),
      maxDate: moment().startOf("day"),
      locale: {
          format: datePickerFormat,
          separator: separator,
          cancelLabel: 'Clear'
      }
  });

  $("#" + id).on('apply.daterangepicker', function (ev, picker) {
      $(this).val(picker.startDate.format(datePickerFormat) + separator + picker.endDate.format(datePickerFormat));
  });

}

function getFormattedDateRange(date, separator) {
  let startDate = 0;
  let endDate = 0;

  if (date != null || date !== "") {
    const [startDay, endDay] = date.split(separator);
       if (startDay) {
         startDate = getDate(startDay).getTime() / 1000;
       }

       if (endDay) {
         endDate = getDate(endDay).getTime() / 1000;
       }
  }
  return [startDate, endDate];
}
