{% load json %}
<div class="row mb-2 mb-xl-3">
    {% include 'core/utils/v2/academic_session_display.html'%}
    <div class="col-auto ml-auto text-right mt-n1">
        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#add-event-modal" onclick="eventManagement.resetFields();">Configure New Event</button>
        {% include 'core/utils/v2/academic_session_dropdown.html'%}
    </div>
</div>

<div id="event-list" class="container-fluid p-0 card">
    {% include 'holiday_calendar/event_management/event_list.html' %}
</div>

<div id="add-event-modal" class="modal fade bd-example-modal-md" tabindex="-1" role="dialog" aria-labelledby="addEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEventModalLabel">Configure New Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="add-event-form">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Title*</label>
                            <input type="text" class="form-control mandatory-field" id="event-name" placeholder="Event Name...">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Event Category*</label>
                            <select class="form-control mandatory-field" id="event-category">
                                <option value="">Select Category...</option>
                                <option value="COMPETITION">COMPETITION</option>
                                <option value="TRIP">TRIP</option>
                                <option value="ASSEMBLY">ASSEMBLY</option>
                                <option value="FESTIVAL">FESTIVAL</option>
                                <option value="PTM">PTM</option>
                                <option value="EXAM">EXAM</option>
                                <option value="OTHER">OTHER</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Event Date*</label>
                            <input type="text" class="form-control select-date mandatory-field" id="event-date" placeholder="Event Date..." >
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Description</label>
                        <textarea class="form-control" id="description" rows="2" placeholder="Description..."></textarea>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button id="add-event" type="button" class="btn btn-primary" onclick="eventManagement.addNewEvent()">Configure New Event</button>
            </div>
        </div>
    </div>
</div>

<div id="update-event-modal" class="modal fade bd-example-modal-md" tabindex="-1" role="dialog" aria-labelledby="addEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateEventModalLabel">Update Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="update-event-form">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Title*</label>
                            <input type="text" class="form-control mandatory-field" id="update-event-name" placeholder="Event Name...">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label>Event Category*</label>
                            <select class="form-control mandatory-field" id="update-event-category">
                                <option value="">Select Category...</option>
                                <option value="COMPETITION">COMPETITION</option>
                                <option value="TRIP">TRIP</option>
                                <option value="ASSEMBLY">ASSEMBLY</option>
                                <option value="FESTIVAL">FESTIVAL</option>
                                <option value="PTM">PTM</option>
                                <option value="EXAM">EXAM</option>
                                <option value="OTHER">OTHER</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Event Date*</label>
                            <input type="text" class="form-control select-date mandatory-field" id="update-event-date" placeholder="Event Date..." >
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Description</label>
                        <textarea class="form-control" id="update-description" rows="2" placeholder="Description..."></textarea>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <p id="update-event-id" style="display : none"></p>
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button id="update-event" type="button" class="btn btn-primary" onclick="eventManagement.updateEvent()">Update Event</button>
            </div>
        </div>
    </div>
</div>


<div id="delete-event-confirm-modal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Event</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p> <strong> Are you sure to remove event? </strong></p>
            </div>
            <div class="modal-footer">
                <p id="delete-event-id" style="display:none;"></p>
                <button type="button" class="btn btn-danger" id="delete-fee-head-confirm" onclick="eventManagement.deleteEvent()">Yes, Delete</button>
                <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
            </div>
        </div>
    </div>
</div>

<div id="event-status-modal-container"></div>