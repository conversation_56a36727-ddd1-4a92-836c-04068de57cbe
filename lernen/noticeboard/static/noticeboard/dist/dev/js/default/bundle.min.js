var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1,NOTIFICATION="NOTIFICATION",ACTION_TYPE="action_type",NOTICE_ID="notice_id",INSTITUTE=(NOTIFICATION="NOTIFICATION","INSTITUTE"),STUDENT="STUDENT",STAFF="STAFF",SAVED="SAVED",BROADCASTED="BROADCASTED",DESCRIPTION_COUNT=2e3;$(document).ready(function(){menuLoader.registerSidebarMenu();var e=new URLSearchParams(window.location.search);if(noticeDetails.dataCache.actionType=e.get(ACTION_TYPE),noticeDetails.dataCache.noticeId=e.get(NOTICE_ID),""===noticeDetails.dataCache.noticeId||null===noticeDetails.dataCache.noticeId)homePage.initHomePage();else{$(".sidebar-nav").find(".sidebar-item").each(function(){$(this).removeClass("active")}),$("#noticeDetailsNav").parent().addClass("active"),noticeDetails.loadNoticeDetailsMainPage(NOTIFICATION);var t=window.location.href;url=removeURLParameter(t,ACTION_TYPE),url=removeURLParameter(url,NOTICE_ID);let e={id:"100"};window.history.replaceState(e,t,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerAddNoticeMenu(),menuLoader.registerNoticeDetailsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerAddNoticeMenu:function(){$("#addNoticeNav").on("click",function(){addNotice.loadAddNoticeMainPage()})},registerNoticeDetailsMenu:function(){$("#noticeDetailsNav").on("click",function(){noticeDetails.loadNoticeDetailsMainPage("MAIN")})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession),homePage.displayDashboardContent()},loadHomePage:function(){ajaxClient.get("/noticeboard/home",function(e){$("#main-content").html(e),homePage.initHomePage()})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/noticeboard/session-home/"+e,function(e){$("#noticeboard-management-dashboard-session-content").html(e),homePage.displayDashboardContent()})},refreshHomePage:function(){homePage.loadHomePageForSession()},displayDashboardContent:function(){}},addNotice={dataCache:{},loadAddNoticeMainPage:function(){ajaxClient.get("/noticeboard/add-notice-page",function(e){$("#main-content").html(e),initSelect2("None"),addNotice.bindStudentClassEvent(),initFutureDate(1825),addNotice.dataCache.attachments=[],addNotice.makeFileList(),addNotice.bindDescriptionCount()})},bindDescriptionCount:function(){var e=DESCRIPTION_COUNT;$("#add-notice-description").keyup(function(){var t=$(this).val().length;t=e-t;$("#chars").text(t)})},bindStudentClassEvent:function(){$("#add-notice-send-to").change(function(){var e=$(this).find(":selected").val().trim();e===STUDENT||e===INSTITUTE?($("#add-notice-class").attr("disabled",!1),$("#add-notice-class").parent().find("button").removeClass("disabled"),selectAllInSelect2()):(removeAllInSelect2(),$("#add-notice-class").parent().find("button").addClass("disabled"),$("#add-notice-class").attr("disabled",!0))})},populateNoticeDetailsSaved:function(e){$("#save-notice-button-text").html("Yes, Save It"),$("#action-value").text(SAVED),$("#save-notice-modal-text").html("Save Notice Details?")},populateNoticeDetailsBroadcasted:function(e){$("#save-notice-button-text").html("Yes, Broadcast It"),$("#action-value").text(BROADCASTED),$("#save-notice-modal-text").html("Save and Broadcast Notice Details?")},addNotice:function(){if($("#save-notice-modal").modal("toggle"),validateMandatoryFields($("#add-notice-modal")))showErrorDialogBox("Please fill all mandatory fields.");else{var e=$("#add-notice-academic-session").find(":selected").val().trim(),t=$("#action-value").text();saveOnly=t!=BROADCASTED;var a=$("#add-notice-title").val(),i=getDate($("#add-notice-expiry-date").val()),n=null;null!=i&&(n=i.getTime()/1e3);var o=$("#add-notice-send-to").find(":selected").val().trim(),s={};o===INSTITUTE?(s[STAFF]=null,s[STUDENT]=$("#add-notice-class").val()):o===STUDENT?s[STUDENT]=$("#add-notice-class").val():o===STAFF&&(s[STAFF]=null);var c=$("#add-notice-description").val(),d=$("#add-notice-pin-it").is(":checked"),l=new FormData;Array.from(addNotice.dataCache.attachments).forEach(e=>l.append("document",e));var r={instituteId:null,academicSessionId:e,title:a,body:c,expiryDate:n,pinned:d,status:t,entityNameAndIdsMapping:s};l.append("noticePaylaodJson",JSON.stringify(r)),ajaxClient.uploadFile("/noticeboard/add-notice/"+saveOnly,l,function(e){$("#add-notice-status-modal-container").html(e),$("#add-notice-status-modal").modal({backdrop:"static",keyboard:!1}),JSON.parse($("#notice-data-response").text().trim()).success&&addNotice.resetAddNoticeModal()})}},resetAddNoticeModal:function(){$("#add-notice-title").val(""),$("#add-notice-expiry-date").val(""),$("#add-notice-send-to").val(""),removeAllInSelect2(),$("#add-notice-description").val(""),$("#file-list").html(""),$("#add-notice-pin-it").prop("checked",!1)},makeFileList:function(){$("#attachments").on("change",function(){for(var e=$(this)[0].files,t="",a=0;a<e.length;a++){addNotice.dataCache.attachments.push(e[a]);t+='<div class="file-details-div row" style="margin:1%;padding:1.5%;background-color:#f3f3f3ff;">'+('<div class="col-sm-8"><h6 style="margin-bottom:0%;color:blue;">'+e[a].name+"</h6></div>")+'<div class="col-sm-4"><button type="button" class="close remove-image" aria-label="Close"> <span aria-hidden="true">×</span> </button></div></div>'}$("#file-list").append(t),addNotice.onClickRemoveImage()})},onClickRemoveImage:function(){$(".remove-image").on("click",function(){for(var e=$(this).parent().parent().find("h6").text(),t=0;t<addNotice.dataCache.attachments.length;t++)addNotice.dataCache.attachments[t].name===e&&addNotice.dataCache.attachments.splice(t,1);$(this).parent().parent().remove()})}},noticeDetails={dataCache:{},changeSession:function(){noticeDetails.dataCache={},noticeDetails.loadList(SAVED)},loadNoticeDetailsMainPage:function(e){ajaxClient.get("/noticeboard/notice-details-page",function(e){$("#main-content").html(e),initDateWithYearRange("-2:+2",!0),noticeDetails.dataCache.attachments=[],noticeDetails.dataCache.editAttachments=!1,academicSessionHandler.bindSessionChangeEvent(noticeDetails.changeSession),noticeDetails.loadNoticeDetailsModal(SAVED,!0)})},loadNoticeDetailsModal:function(e,t){var a=getDate($("#notice-broadcasted-date").val());a=null!=a?a.getTime()/1e3:0,""==e&&(e=$("#screen").text());var i=!0;$("#notice-details-view-container").is(":empty")||(i=$("#nav-after-enrollment-structure-tab").hasClass("active"));var n=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/noticeboard/notice-details-list/"+a+"/0/10/"+e+"/"+n,function(a){$("#notice-details-view-container").html(a),noticeDetails.loadActiveCancelledButton(),noticeDetails.initNoticeListPage(1,e),i?($("#nav-after-enrollment-structure-tab").addClass("active"),$("#nav-after-enrollment-structure").addClass("active"),$("#nav-after-enrollment-structure").addClass("show"),$("#nav-before-registration-structure-tab").removeClass("active"),$("#before-registration-structure").removeClass("active"),$("#before-registration-structure").removeClass("show")):($("#nav-after-enrollment-structure-tab").removeClass("active"),$("#nav-after-enrollment-structure").removeClass("active"),$("#nav-after-enrollment-structure").removeClass("show"),$("#nav-before-registration-structure-tab").addClass("active"),$("#before-registration-structure").addClass("active"),$("#before-registration-structure").addClass("show")),t&&(noticeDetails.dataCache.attachments=[],noticeDetails.dataCache.editAttachments=!1,noticeDetails.makeFileList())})},loadActiveCancelledButton:function(){$("#nav-after-enrollment-structure-tab").on("click",function(){$(".page-item").removeClass("active");$("#page-number-1").addClass("active"),noticeDetails.loadList(SAVED)}),$("#nav-before-registration-structure-tab").on("click",function(){$(".page-item").removeClass("active");$("#page-number-1").addClass("active"),noticeDetails.loadList(BROADCASTED)})},loadList:function(e){var t=getDate($("#notice-broadcasted-date").val());t=null!=t?t.getTime()/1e3:0;var a=$(".page-item.active").find(".page-number").text().trim(),i=$("#items-per-page").val();null!=a&&""!=a||(a=1);var n=(a-1)*i;$("#nav-tabContent-configured-staff-advance-list").html("");var o=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/noticeboard/notice-list/"+t+"/"+n+"/"+i+"/"+e+"/"+o,function(t){$("#nav-tabContent-configured-staff-advance-list").html(t),noticeDetails.initNoticeListPage(a,e)})},initNoticeListPage:function(e,t){var a=$("#sidebar").height()-$("#nav-tabContent-configured-staff-advance-list").position().top-50;$("#nav-active-advance").attr("style","height:"+a+"px;  overflow-y: scroll; cursor: pointer;");var i=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(i.itemsPerPage),$(".page-item").removeClass("active");i.offset,i.itemsPerPage;$("#page-number-"+e).addClass("active"),$("#screen").text(t),noticeDetails.bindCheckboxEvents(),noticeDetails.updateNoticeDetails(),noticeDetails.viewNoticeDetails(),noticeDetails.bindStudentClassEvent(),noticeDetails.initPagination(),noticeDetails.viewedDetails(),initSelect2("None"),$(".send-to-field").each(function(){var e=JSON.parse($(this).parent().find(".notice-json-info").text()),t="",a=0;$.each(e.entityNameAndIdsMapping,function(e,i){1!=a?(t=e,a+=1):t=INSTITUTE}),$(this).text(t)})},initPagination:function(){pagination.bindEvents(function(){$("#screen").text()==BROADCASTED?noticeDetails.loadList(BROADCASTED):noticeDetails.loadList(SAVED)},function(){$("#screen").text()==BROADCASTED?noticeDetails.loadList(BROADCASTED):noticeDetails.loadList(SAVED)},function(){$("#screen").text()==BROADCASTED?noticeDetails.loadList(BROADCASTED):noticeDetails.loadList(SAVED)},function(){$("#screen").text()==BROADCASTED?noticeDetails.loadList(BROADCASTED):noticeDetails.loadList(SAVED)})},bindStudentClassEvent:function(){$("#update-notice-send-to").change(function(){var e=$(this).find(":selected").val().trim();e===STUDENT||e===INSTITUTE?(selectAllInSelect2(),$("#update-notice-class").attr("disabled",!1),$("#update-notice-class").parent().find("button").removeClass("disabled")):(removeAllInSelect2(),$("#update-notice-class").parent().find("button").addClass("disabled"),$("#update-notice-class").attr("disabled",!0))})},makeFileList:function(){$("#attachments").on("change",function(){for(var e=$(this)[0].files,t="",a=0;a<e.length;a++){noticeDetails.dataCache.attachments.push(e[a]);t+='<div class="file-details-div row" style="margin:1%;padding:1.5%;background-color:#f3f3f3ff">'+('<div class="col-sm-8"><h6 style="padding-right:8.5%;margin-bottom:0%;color:blue;" class="new-upload-file-name">'+e[a].name+"</h6></div>")+'<div class="col-sm-4"><h6 style="display:inline-block;cursor: pointer;float:right;"><button type="button" class="close remove-image" aria-label="Close"> <span aria-hidden="true">×</span> </button></h6></div></div>'}$("#update-file-list").append(t),noticeDetails.onClickRemoveImage()})},onClickRemoveImage:function(){$(".remove-image").on("click",function(){for(var e=$(this).parent().parent().parent().find(".new-upload-file-name").text(),t=0;t<noticeDetails.dataCache.attachments.length;t++)noticeDetails.dataCache.attachments[t].name===e&&noticeDetails.dataCache.attachments.splice(t,1);$(this).parent().parent().parent().remove()})},bindCheckboxEvents:function(){$(".child").on("change",function(){$(this).parent().parent().parent().parent().find($(".parent")).prop("checked",!1)}),$(".parent").on("change",function(){$(this).prop("checked")?$(this).parent().parent().parent().parent().find($(".child")).prop("checked",!0):$(this).prop("checked")||$(this).parent().parent().parent().parent().find($(".child")).each(function(){$(this).prop("checked",!1)})})},populateBroadcastNoticeDetailsModal:function(e){$("#broadcast-notice-modal-text").html("Broadcast all the selected notices?");var t=[];$(e).parent().parent().find($(".child")).each(function(){if($(this).prop("checked")){var e=JSON.parse($(this).parent().parent().find(".notice-json-info").text());t.push(e.noticeId)}}),$("#notice-ids").text(JSON.stringify(t))},broadcastNotices:function(e){if($("#broadcast-notice-modal").modal("toggle"),noticeIds=JSON.parse($("#notice-ids").text()),"undefined"==typeof noticeIds||noticeIds.length<=0)showErrorDialogBox("No Notice selected");else{var t=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/noticeboard/broadcast-notice-status/"+t,{noticeIds:JSON.stringify(noticeIds)},function(e){$("#notice-status-modal-container").html(e),$("#notice-status-modal").modal({backdrop:"static",keyboard:!1});var t=$("#screen").text();noticeDetails.loadNoticeDetailsModal(t,!1)})}},updateNoticeDetails:function(){$(".update-notice-details-button").on("click",function(){noticeDetails.resetNoticeDetailsPopup(),noticeDetails.bindDescriptionCount(),noticeDetails.fillNoticeDetails(this,!1)})},bindDescriptionCount:function(){var e=DESCRIPTION_COUNT;$("#update-notice-description").keyup(function(){var t=$(this).val().length;t=e-t;$("#chars").text(t)})},viewNoticeDetails:function(){$(".view-notice-details-button").on("click",function(){noticeDetails.resetNoticeDetailsPopup(),noticeDetails.fillNoticeDetails(this,!0)})},resetNoticeDetailsPopup:function(){$("#update-notice-title").val(""),$("#update-notice-expiry-date").val(""),$("#update-notice-send-to").val(""),removeAllInSelect2(),$("#update-notice-pin-it").prop("checked",!1),$("#update-notice-description").val(""),$("#update-file-list").html("")},fillNoticeDetails:function(e,t){$(".modal-title").html("Update Notice Details"),t?$(".view-notice-modal-footer").html('<p style="display:none" id="selected-notice-json"></p><button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>'):$(".view-notice-modal-footer").html('<p style="display:none" id="selected-notice-json"></p><button type="button" class="btn btn-danger" id="cancel-button" data-dismiss="modal" onclick="closeModal()">Cancel</button><button id="update-notice-details" onclick="noticeDetails.updateNoticeDetailsButton(this)" type="button" class="btn btn-primary"  >Update Notice Details</button>');var a=$(e).parent().find(".notice-details-info").text();$("#selected-notice-json").text(a);var i=JSON.parse(a);$("#update-notice-title").val(i.title),$("#update-notice-expiry-date").val(getFormattedDate(i.expiryDate));var n=!1;"BROADCASTED"===i.status&&(n=!0);var o=0;$.each(i.entityNameAndIdsMapping,function(e,t){1!=o?(sendTo=e,o+=1):sendTo=INSTITUTE}),$("#update-notice-send-to").val(sendTo),sendTo===INSTITUTE||sendTo===STUDENT?($("#update-notice-class").parent().find("button").removeClass("disabled"),$("#update-notice-class").attr("disabled",!1),$("#update-notice-class").val(i.entityNameAndIdsMapping.STUDENT),$("#update-notice-class").select2()):(removeAllInSelect2(),$("#update-notice-class").parent().find("button").addClass("disabled"),$("#update-notice-class").attr("disabled",!0)),$("#update-notice-pin-it").prop("checked",i.pinned),$("#update-notice-description").val(i.description);var s=$.trim(i.description).length;s<=DESCRIPTION_COUNT&&$("#chars").text(DESCRIPTION_COUNT-s),$("#cancel-button").html("Cancel");var c="";if(void 0!==i.noticeBoardAttachments&&null!==i.noticeBoardAttachments)for(var d=0;d<i.noticeBoardAttachments.length;d++){c=c+'<div class="row" style="margin:1%;padding:1.5%;background-color:#f3f3f3ff;">'+('<p class="document-details-json" style="display:none;">'+JSON.stringify(i.noticeBoardAttachments[d])+"</p>")+('<div class="col-sm-8"><h6 style="padding-right:5%;margin-bottom:0%;color:blue;">'+i.noticeBoardAttachments[d].documentName+"</h6></div>")+'<div class="col-sm-4"><h6 style="cursor: pointer;float:right;" ><img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/download-icon-20.png" onclick="noticeDetails.downloadUpdateNoticeAttachment(this);"></h6></div></div>'}""===c&&(c="<h5>No Attachments!</h5>"),$("#update-file-list").append(c),$("#update-notice-title").attr("disabled",!1),$("#update-notice-expiry-date").attr("disabled",!1),$("#update-notice-send-to").attr("disabled",!1),$("#update-notice-pin-it").attr("disabled",!1),$(".custom-file").css("display","none"),$(".remove-image").css("display","none"),$("#update-notice-description").attr("disabled",!1),$("#edit-attachments").show(),$("#edit-attachments").css("margin-top","12%"),t&&($("#update-notice-title").attr("disabled",!0),$("#update-notice-expiry-date").attr("disabled",!0),$("#update-notice-send-to").attr("disabled",!0),$("#update-notice-pin-it").attr("disabled",!0),$(".custom-file").css("display","none"),$(".remove-image").css("display","none"),$("#update-notice-description").attr("disabled",!0),$("#edit-attachments").css("display","none"),$("#update-notice-class").parent().find("button").addClass("disabled"),$("#update-notice-class").attr("disabled",!0),$(".modal-title").html("View Notice Details")),n&&!t&&($("#update-notice-title").attr("disabled",!0),$("#update-notice-expiry-date").attr("disabled",!1),$("#update-notice-send-to").attr("disabled",!0),$("#update-notice-pin-it").attr("disabled",!1),$(".custom-file").css("display","none"),$(".remove-image").css("display","none"),$("#update-notice-description").attr("disabled",!0),$("#edit-attachments").css("display","none"),$("#update-notice-class").parent().find("button").addClass("disabled"),$("#update-notice-class").attr("disabled",!0))},editAttachments:function(){$(".custom-file").show(),$("#update-file-list").html(""),$("#edit-attachments").css("margin-top","9.5%"),noticeDetails.dataCache.editAttachments=!0},downloadUpdateNoticeAttachment:function(e){var t=JSON.parse($(e).parent().parent().parent().find(".document-details-json").text()),a=JSON.parse($("#selected-notice-json").text()),i=t.documentId,n=(a.createdBy,a.noticeId);window.open(baseURL+"/noticeboard/download-notice-attachment/"+i+"/"+n,"_blank")},updateNoticeDetailsButton:function(e){if(validateMandatoryFields($("#notice-details-modal")))showErrorDialogBox("Please fill the mandatory fields.");else{var t=$(e).parent().find("#selected-notice-json").text(),a=JSON.parse(t),i=a.noticeId,n=a.academicSessionId,o=$("#update-notice-title").val(),s=getDate($("#update-notice-expiry-date").val()),c=null;null!=s&&(c=s.getTime()/1e3);var d=$("#update-notice-send-to").find(":selected").val().trim(),l={};d===INSTITUTE?(l[STAFF]=null,l[STUDENT]=$("#update-notice-class").val()):d===STUDENT?l[STUDENT]=$("#update-notice-class").val():d===STAFF&&(l[STAFF]=null);var r=$("#update-notice-description").val(),u=$("#update-notice-pin-it").is(":checked"),p=new FormData;Array.from(noticeDetails.dataCache.attachments).forEach(e=>p.append("document",e));var m={instituteId:null,academicSessionId:n,noticeId:i,title:o,body:r,expiryDate:c,pinned:u,entityNameAndIdsMapping:l};p.append("noticePaylaodJson",JSON.stringify(m)),$("#notice-details-modal").modal("toggle"),ajaxClient.uploadFile("/noticeboard/update-notice/"+noticeDetails.dataCache.editAttachments,p,function(e){$("#notice-status-modal-container").html(e),$("#notice-status-modal").modal({backdrop:"static",keyboard:!1}),noticeDetails.dataCache.attachments=[],noticeDetails.dataCache.editAttachments=!1;var t=$("#screen").text();noticeDetails.loadNoticeDetailsModal(t,!1)})}},populateDeleteNoticeDetailsModal:function(e){var t=$(e).parent().find(".notice-details-info").text();$("#delete-selected-notice-json").text(t),$("#delete-notice-details-modal-text").html("Are you sure you want to delete Notice Details?")},deleteNoticeDetails:function(e){$("#delete-notice-modal").modal("toggle");var t=$(e).parent().find("#delete-selected-notice-json").text(),a=JSON.parse(t).noticeId,i=$("#screen").text();ajaxClient.post("/noticeboard/delete-notice-details/"+a,{},function(e){$("#notice-status-modal-container").html(e),$("#notice-status-modal").modal({backdrop:"static",keyboard:!1}),noticeDetails.loadNoticeDetailsModal(i,!1)})},viewedDetails:function(){$(".viewed-details-button").on("click",function(){var e=JSON.parse($(this).parent().find(".notice-details-info").text()).noticeId;ajaxClient.get("/noticeboard/notice-viewed-details/"+e,function(e){$("#notice-details-main-container").html(e)})})}};