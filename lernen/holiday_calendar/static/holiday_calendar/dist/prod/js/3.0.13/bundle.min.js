!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).FullCalendar={})}(this,function(e){"use strict";var t={className:!0,colSpan:!0,rowSpan:!0},n={"<tr":"tbody","<td":"tr"};function r(e,n,r){var i=document.createElement(e);if(n)for(var o in n)"style"===o?m(i,n[o]):t[o]?i[o]=n[o]:i.setAttribute(o,n[o]);return"string"==typeof r?i.innerHTML=r:null!=r&&s(i,r),i}function i(e){e=e.trim();var t=document.createElement(a(e));return t.innerHTML=e,t.firstChild}function o(e){return Array.prototype.slice.call(function(e){e=e.trim();var t=document.createElement(a(e));return t.innerHTML=e,t.childNodes}(e))}function a(e){return n[e.substr(0,3)]||"div"}function s(e,t){for(var n=d(t),r=0;r<n.length;r++)e.appendChild(n[r])}function l(e,t){for(var n=d(t),r=e.firstChild||null,i=0;i<n.length;i++)e.insertBefore(n[i],r)}function d(e){return"string"==typeof e?o(e):e instanceof Node?[e]:Array.prototype.slice.call(e)}function c(e){e.parentNode&&e.parentNode.removeChild(e)}var u=Element.prototype.matches||Element.prototype.matchesSelector||Element.prototype.msMatchesSelector,p=Element.prototype.closest||function(e){var t=this;if(!document.documentElement.contains(t))return null;do{if(f(t,e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null};function h(e,t){return p.call(e,t)}function f(e,t){return u.call(e,t)}function g(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],i=0;i<n.length;i++)for(var o=n[i].querySelectorAll(t),a=0;a<o.length;a++)r.push(o[a]);return r}var v=/(top|left|right|bottom|width|height)$/i;function m(e,t){for(var n in t)y(e,n,t[n])}function y(e,t,n){null==n?e.style[t]="":"number"==typeof n&&v.test(t)?e.style[t]=n+"px":e.style[t]=n}function E(e,t){var n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}var S=null;function b(){return null===S&&(S=function(){var e=r("div",{style:{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}},"<div></div>");document.body.appendChild(e);var t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left;return c(e),t}()),S}function D(e){return e=Math.max(0,e),e=Math.round(e)}function T(e,t){void 0===t&&(t=!1);var n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,i=parseInt(n.borderRightWidth,10)||0,o=parseInt(n.borderTopWidth,10)||0,a=parseInt(n.borderBottomWidth,10)||0,s=D(e.offsetWidth-e.clientWidth-r-i),l={borderLeft:r,borderRight:i,borderTop:o,borderBottom:a,scrollbarBottom:D(e.offsetHeight-e.clientHeight-o-a),scrollbarLeft:0,scrollbarRight:0};return b()&&"rtl"===n.direction?l.scrollbarLeft=s:l.scrollbarRight=s,t&&(l.paddingLeft=parseInt(n.paddingLeft,10)||0,l.paddingRight=parseInt(n.paddingRight,10)||0,l.paddingTop=parseInt(n.paddingTop,10)||0,l.paddingBottom=parseInt(n.paddingBottom,10)||0),l}function w(e,t){void 0===t&&(t=!1);var n=C(e),r=T(e,t),i={left:n.left+r.borderLeft+r.scrollbarLeft,right:n.right-r.borderRight-r.scrollbarRight,top:n.top+r.borderTop,bottom:n.bottom-r.borderBottom-r.scrollbarBottom};return t&&(i.left+=r.paddingLeft,i.right-=r.paddingRight,i.top+=r.paddingTop,i.bottom-=r.paddingBottom),i}function C(e){if(null!=e){var t=e.getBoundingClientRect();return{left:t.left+window.pageXOffset,top:t.top+window.pageYOffset,right:t.right+window.pageXOffset,bottom:t.bottom+window.pageYOffset}}}function R(e){return e.getBoundingClientRect().height+M(e)}function M(e){var t=window.getComputedStyle(e);return parseInt(t.marginTop,10)+parseInt(t.marginBottom,10)}function I(e){for(var t=[];e instanceof HTMLElement;){var n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}function k(e){e.preventDefault()}function P(e,t,n,r){function i(e){var t=h(e.target,n);t&&r.call(t,e,t)}return e.addEventListener(t,i),function(){e.removeEventListener(t,i)}}var H=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];var O=["sun","mon","tue","wed","thu","fri","sat"];function x(e,t){var n=j(e);return n[2]+=t,W(n)}function N(e,t){var n=j(e);return n[6]+=t,W(n)}function _(e,t){return(t.valueOf()-e.valueOf())/864e5}function L(e,t){var n=A(e),r=A(t);return{years:0,months:0,days:Math.round(_(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}function z(e,t){var n=U(e,t);return null!==n&&n%7==0?n/7:null}function U(e,t){return Y(e)===Y(t)?Math.round(_(e,t)):null}function A(e){return W([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function V(e,t,n,r){var i=W([t,0,1+$(t,n,r)]),o=A(e),a=Math.round(_(i,o));return Math.floor(a/7)+1}function $(e,t,n){var r=7+t-n;return-((7+W([e,0,r]).getUTCDay()-t)%7)+r-1}function F(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function B(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function j(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function W(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC.apply(Date,e))}function G(e){return!isNaN(e.valueOf())}function Y(e){return 1e3*e.getUTCHours()*60*60+1e3*e.getUTCMinutes()*60+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}var q=["years","months","days","milliseconds"],Z=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function X(e,t){var n;return"string"==typeof e?function(e){var t=Z.exec(e);if(t){var n=t[1]?-1:1;return{years:0,months:0,days:n*(t[2]?parseInt(t[2],10):0),milliseconds:n*(60*(t[3]?parseInt(t[3],10):0)*60*1e3+60*(t[4]?parseInt(t[4],10):0)*1e3+1e3*(t[5]?parseInt(t[5],10):0)+(t[6]?parseInt(t[6],10):0))}}return null}(e):"object"==typeof e&&e?J(e):"number"==typeof e?J(((n={})[t||"milliseconds"]=e,n)):null}function J(e){return{years:e.years||e.year||0,months:e.months||e.month||0,days:(e.days||e.day||0)+7*K(e),milliseconds:60*(e.hours||e.hour||0)*60*1e3+60*(e.minutes||e.minute||0)*1e3+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)}}function K(e){return e.weeks||e.week||0}function Q(e,t){return e.years===t.years&&e.months===t.months&&e.days===t.days&&e.milliseconds===t.milliseconds}function ee(e){return te(e)/864e5}function te(e){return 31536e6*e.years+2592e6*e.months+864e5*e.days+e.milliseconds}function ne(e,t){var n=e.milliseconds;if(n){if(n%1e3!=0)return{unit:"millisecond",value:n};if(n%6e4!=0)return{unit:"second",value:n/1e3};if(n%36e5!=0)return{unit:"minute",value:n/6e4};if(n)return{unit:"hour",value:n/36e5}}return e.days?t||e.days%7!=0?{unit:"day",value:e.days}:{unit:"week",value:e.days/7}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function re(e){e.forEach(function(e){e.style.height=""})}function ie(e){var t,n,r=[],i=[];for("string"==typeof e?i=e.split(/\s*,\s*/):"function"==typeof e?i=[e]:Array.isArray(e)&&(i=e),t=0;t<i.length;t++)"string"==typeof(n=i[t])?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r}function oe(e,t,n){var r,i;for(r=0;r<n.length;r++)if(i=ae(e,t,n[r]))return i;return 0}function ae(e,t,n){return n.func?n.func(e,t):se(e[n.field],t[n.field])*(n.order||1)}function se(e,t){return e||t?null==t?-1:null==e?1:"string"==typeof e||"string"==typeof t?String(e).localeCompare(String(t)):e-t:0}function le(e){return e.charAt(0).toUpperCase()+e.slice(1)}function de(e,t){var n=String(e);return"000".substr(0,t-n.length)+n}function ce(e){return e%1==0}function ue(e,t,n){if("function"==typeof e&&(e=[e]),e){var r=void 0,i=void 0;for(r=0;r<e.length;r++)i=e[r].apply(t,n)||i;return i}}function pe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0;n<e.length;n++)if(void 0!==e[n])return e[n]}function he(e,t){var n,r,i,o,a,s=function(){var l=(new Date).valueOf()-o;l<t?n=setTimeout(s,t-l):(n=null,a=e.apply(i,r),i=r=null)};return function(){return i=this,r=arguments,o=(new Date).valueOf(),n||(n=setTimeout(s,t)),a}}function fe(e,t,n,r){void 0===n&&(n={});var i={};for(var o in t){var a=t[o];void 0!==e[o]?a===Function?i[o]="function"==typeof e[o]?e[o]:null:i[o]=a?a(e[o]):e[o]:void 0!==n[o]?i[o]=n[o]:a===String?i[o]="":a&&a!==Number&&a!==Boolean&&a!==Function?i[o]=a(null):i[o]=null}if(r)for(var o in e)void 0===t[o]&&(r[o]=e[o]);return i}function ge(e){var t=Math.floor(_(e.start,e.end))||1,n=A(e.start);return{start:n,end:x(n,t)}}function ve(e,t){void 0===t&&(t=X(0));var n=null,r=null;if(e.end){r=A(e.end);var i=e.end.valueOf()-r.valueOf();i&&i>=te(t)&&(r=x(r,1))}return e.start&&(n=A(e.start),r&&r<=n&&(r=x(n,1))),{start:n,end:r}}function me(e,t,n,r){return"year"===r?X(n.diffWholeYears(e,t),"year"):"month"===r?X(n.diffWholeMonths(e,t),"month"):L(e,t)}var ye=function(e,t){return(ye=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function Ee(e,t){function n(){this.constructor=e}ye(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var Se=function(){return(Se=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function be(e,t,n,r,i){var o=i[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(o=o.map(A)),o}var De=Object.prototype.hasOwnProperty;function Te(e,t){var n,r,i,o,a,s,l={};if(t)for(n=0;n<t.length;n++){for(r=t[n],i=[],o=e.length-1;o>=0;o--)if("object"==typeof(a=e[o][r])&&a)i.unshift(a);else if(void 0!==a){l[r]=a;break}i.length&&(l[r]=Te(i))}for(n=e.length-1;n>=0;n--)for(r in s=e[n])r in l||(l[r]=s[r]);return l}function we(e,t){var n={};for(var r in e)t(e[r],r)&&(n[r]=e[r]);return n}function Ce(e,t){var n={};for(var r in e)n[r]=t(e[r],r);return n}function Re(e){for(var t={},n=0,r=e;n<r.length;n++){t[r[n]]=!0}return t}function Me(e){var t=[];for(var n in e)t.push(e[n]);return t}function Ie(e,t){for(var n in e)if(De.call(e,n)&&!(n in t))return!1;for(var n in t)if(De.call(t,n)&&e[n]!==t[n])return!1;return!0}function ke(e,t,n,r){for(var i={defs:{},instances:{}},o=0,a=e;o<a.length;o++){var s=$t(a[o],t,n,r);s&&Pe(s,i)}return i}function Pe(e,t){return void 0===t&&(t={defs:{},instances:{}}),t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function He(e,t,n){var r=n.dateEnv,i=e.defs,o=e.instances;for(var a in o=we(o,function(e){return!i[e.defId].recurringDef}),i){var s=i[a];if(s.recurringDef){var l=s.recurringDef.duration;l||(l=s.allDay?n.defaultAllDayEventDuration:n.defaultTimedEventDuration);for(var d=0,c=be(s,l,t,n.dateEnv,n.pluginSystem.hooks.recurringTypes);d<c.length;d++){var u=c[d],p=Bt(a,{start:u,end:r.add(u,l)});o[p.instanceId]=p}}}return{defs:i,instances:o}}function Oe(e,t){var n=e.instances[t];if(n){var r=e.defs[n.defId],i=Le(e,function(e){return t=r,n=e,Boolean(t.groupId&&t.groupId===n.groupId);var t,n});return i.defs[r.defId]=r,i.instances[n.instanceId]=n,i}return{defs:{},instances:{}}}function xe(e,t){var n;if(t){n=[];for(var r=0,i=e;r<i.length;r++){var o=i[r],a=t(o);a?n.push(a):null==a&&n.push(o)}}else n=e;return n}function Ne(){return{defs:{},instances:{}}}function _e(e,t){return{defs:Se({},e.defs,t.defs),instances:Se({},e.instances,t.instances)}}function Le(e,t){var n=we(e.defs,t),r=we(e.instances,function(e){return n[e.defId]});return{defs:n,instances:r}}function ze(e,t){var n,r,i=[],o=t.start;for(e.sort(Ue),n=0;n<e.length;n++)(r=e[n]).start>o&&i.push({start:o,end:r.start}),r.end>o&&(o=r.end);return o<t.end&&i.push({start:o,end:t.end}),i}function Ue(e,t){return e.start.valueOf()-t.start.valueOf()}function Ae(e,t){var n=e.start,r=e.end,i=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(i={start:n,end:r}),i}function Ve(e,t){return(null===e.start?null:e.start.valueOf())===(null===t.start?null:t.start.valueOf())&&(null===e.end?null:e.end.valueOf())===(null===t.end?null:t.end.valueOf())}function $e(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function Fe(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function Be(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function je(e,t){var n,r=e.length;if(r!==t.length)return!1;for(n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}function We(e){var t,n;return function(){return t&&je(t,arguments)||(t=arguments,n=e.apply(this,arguments)),n}}function Ge(e,t){var n=null;return function(){var r=e.apply(this,arguments);return(null===n||n!==r&&!t(n,r))&&(n=r),n}}var Ye={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},qe={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Ze=/\s*([ap])\.?m\.?/i,Xe=/,/g,Je=/\s+/g,Ke=/\u200e/g,Qe=/UTC|GMT/,et=function(){function e(e){var t={},n={},r=0;for(var i in e)i in Ye?(n[i]=e[i],r=Math.max(Ye[i],r)):(t[i]=e[i],i in qe&&(r=Math.max(qe[i],r)));this.standardDateProps=t,this.extendedSettings=n,this.severity=r,this.buildFormattingFunc=We(tt)}return e.prototype.format=function(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)},e.prototype.formatRange=function(e,t,n){var r=this.standardDateProps,i=this.extendedSettings,o=function(e,t,n){if(n.getMarkerYear(e)!==n.getMarkerYear(t))return 5;if(n.getMarkerMonth(e)!==n.getMarkerMonth(t))return 4;if(n.getMarkerDay(e)!==n.getMarkerDay(t))return 2;if(Y(e)!==Y(t))return 1;return 0}(e.marker,t.marker,n.calendarSystem);if(!o)return this.format(e,n);var a=o;!(a>1)||"numeric"!==r.year&&"2-digit"!==r.year||"numeric"!==r.month&&"2-digit"!==r.month||"numeric"!==r.day&&"2-digit"!==r.day||(a=1);var s=this.format(e,n),l=this.format(t,n);if(s===l)return s;var d=tt(function(e,t){var n={};for(var r in e)r in qe&&!(qe[r]<=t)||(n[r]=e[r]);return n}(r,a),i,n),c=d(e),u=d(t),p=function(e,t,n,r){var i=0;for(;i<e.length;){var o=e.indexOf(t,i);if(-1===o)break;var a=e.substr(0,o);i=o+t.length;for(var s=e.substr(i),l=0;l<n.length;){var d=n.indexOf(r,l);if(-1===d)break;var c=n.substr(0,d);l=d+r.length;var u=n.substr(l);if(a===c&&s===u)return{before:a,after:s}}}return null}(s,c,l,u),h=i.separator||"";return p?p.before+c+h+u+p.after:s+h+l},e.prototype.getLargestUnit=function(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";default:return"day"}},e}();function tt(e,t,n){var r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?function(e){return ot(e.timeZoneOffset)}:0===r&&t.week?function(e){return function(e,t,n,r){var i=[];"narrow"===r?i.push(t):"short"===r&&i.push(t," ");i.push(n.simpleNumberFormat.format(e)),n.options.isRtl&&i.reverse();return i.join("")}(n.computeWeekNumber(e.marker),n.weekLabel,n.locale,t.week)}:function(e,t,n){e=Se({},e),t=Se({},t),function(e,t){e.timeZoneName&&(e.hour||(e.hour="2-digit"),e.minute||(e.minute="2-digit"));"long"===e.timeZoneName&&(e.timeZoneName="short");t.omitZeroMinute&&(e.second||e.millisecond)&&delete t.omitZeroMinute}(e,t),e.timeZone="UTC";var r,i=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){var o=Se({},e);delete o.minute,r=new Intl.DateTimeFormat(n.locale.codes,o)}return function(o){var a=o.marker,s=(r&&!a.getUTCMinutes()?r:i).format(a);return function(e,t,n,r,i){e=e.replace(Ke,""),"short"===n.timeZoneName&&(e=function(e,t){var n=!1;e=e.replace(Qe,function(){return n=!0,t}),n||(e+=" "+t);return e}(e,"UTC"===i.timeZone||null==t.timeZoneOffset?"UTC":ot(t.timeZoneOffset)));r.omitCommas&&(e=e.replace(Xe,"").trim());r.omitZeroMinute&&(e=e.replace(":00",""));!1===r.meridiem?e=e.replace(Ze,"").trim():"narrow"===r.meridiem?e=e.replace(Ze,function(e,t){return t.toLocaleLowerCase()}):"short"===r.meridiem?e=e.replace(Ze,function(e,t){return t.toLocaleLowerCase()+"m"}):"lowercase"===r.meridiem&&(e=e.replace(Ze,function(e){return e.toLocaleLowerCase()}));return e=(e=e.replace(Je," ")).trim()}(s,o,e,t,n)}}(e,t,n)}var nt=function(){function e(e,t){this.cmdStr=e,this.separator=t}return e.prototype.format=function(e,t){return t.cmdFormatter(this.cmdStr,at(e,null,t,this.separator))},e.prototype.formatRange=function(e,t,n){return n.cmdFormatter(this.cmdStr,at(e,t,n,this.separator))},e}(),rt=function(){function e(e){this.func=e}return e.prototype.format=function(e,t){return this.func(at(e,null,t))},e.prototype.formatRange=function(e,t,n){return this.func(at(e,t,n))},e}();function it(e,t){return"object"==typeof e&&e?("string"==typeof t&&(e=Se({separator:t},e)),new et(e)):"string"==typeof e?new nt(e,t):"function"==typeof e?new rt(e):void 0}function ot(e,t){void 0===t&&(t=!1);var n=e<0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),o=Math.round(r%60);return t?n+de(i,2)+":"+de(o,2):"GMT"+n+i+(o?":"+de(o,2):"")}function at(e,t,n,r){var i=st(e,n.calendarSystem);return{date:i,start:i,end:t?st(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,separator:r}}function st(e,t){var n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}var lt=function(){function e(e,t){this.calendar=e,this.internalEventSource=t}return e.prototype.remove=function(){this.calendar.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})},e.prototype.refetch=function(){this.calendar.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId]})},Object.defineProperty(e.prototype,"id",{get:function(){return this.internalEventSource.publicId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this.internalEventSource.meta.url},enumerable:!0,configurable:!0}),e}(),dt=function(){function e(e,t,n){this._calendar=e,this._def=t,this._instance=n||null}return e.prototype.setProp=function(e,t){var n,r;if(e in At);else if(e in Ut)"function"==typeof Ut[e]&&(t=Ut[e](t)),this.mutate({standardProps:(n={},n[e]=t,n)});else if(e in Ot){var i=void 0;"function"==typeof Ot[e]&&(t=Ot[e](t)),"color"===e?i={backgroundColor:t,borderColor:t}:"editable"===e?i={startEditable:t,durationEditable:t}:((r={})[e]=t,i=r),this.mutate({standardProps:{ui:i}})}},e.prototype.setExtendedProp=function(e,t){var n;this.mutate({extendedProps:(n={},n[e]=t,n)})},e.prototype.setStart=function(e,t){void 0===t&&(t={});var n=this._calendar.dateEnv,r=n.createMarker(e);if(r&&this._instance){var i=me(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:i}):this.mutate({startDelta:i})}},e.prototype.setEnd=function(e,t){void 0===t&&(t={});var n,r=this._calendar.dateEnv;if((null==e||(n=r.createMarker(e)))&&this._instance)if(n){var i=me(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:i})}else this.mutate({standardProps:{hasEnd:!1}})},e.prototype.setDates=function(e,t,n){void 0===n&&(n={});var r,i=this._calendar.dateEnv,o={allDay:n.allDay},a=i.createMarker(e);if(a&&(null==t||(r=i.createMarker(t)))&&this._instance){var s=this._instance.range;!0===n.allDay&&(s=ge(s));var l=me(s.start,a,i,n.granularity);if(r){var d=me(s.end,r,i,n.granularity);Q(l,d)?this.mutate({datesDelta:l,standardProps:o}):this.mutate({startDelta:l,endDelta:d,standardProps:o})}else o.hasEnd=!1,this.mutate({datesDelta:l,standardProps:o})}},e.prototype.moveStart=function(e){var t=X(e);t&&this.mutate({startDelta:t})},e.prototype.moveEnd=function(e){var t=X(e);t&&this.mutate({endDelta:t})},e.prototype.moveDates=function(e){var t=X(e);t&&this.mutate({datesDelta:t})},e.prototype.setAllDay=function(e,t){void 0===t&&(t={});var n={allDay:e},r=t.maintainDuration;null==r&&(r=this._calendar.opt("allDayMaintainDuration")),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})},e.prototype.formatRange=function(e){var t=this._calendar.dateEnv,n=this._instance,r=it(e,this._calendar.opt("defaultRangeSeparator"));return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})},e.prototype.mutate=function(e){var t=this._def,n=this._instance;if(n){this._calendar.dispatch({type:"MUTATE_EVENTS",instanceId:n.instanceId,mutation:e,fromApi:!0});var r=this._calendar.state.eventStore;this._def=r.defs[t.defId],this._instance=r.instances[n.instanceId]}},e.prototype.remove=function(){this._calendar.dispatch({type:"REMOVE_EVENT_DEF",defId:this._def.defId})},Object.defineProperty(e.prototype,"source",{get:function(){var e=this._def.sourceId;return e?new lt(this._calendar,this._calendar.state.eventSources[e]):null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"start",{get:function(){return this._instance?this._calendar.dateEnv.toDate(this._instance.range.start):null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){return this._instance&&this._def.hasEnd?this._calendar.dateEnv.toDate(this._instance.range.end):null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._def.publicId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"groupId",{get:function(){return this._def.groupId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"allDay",{get:function(){return this._def.allDay},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._def.title},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this._def.url},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"rendering",{get:function(){return this._def.rendering},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"startEditable",{get:function(){return this._def.ui.startEditable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"durationEditable",{get:function(){return this._def.ui.durationEditable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"constraint",{get:function(){return this._def.ui.constraints[0]||null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"overlap",{get:function(){return this._def.ui.overlap},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"allow",{get:function(){return this._def.ui.allows[0]||null},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"backgroundColor",{get:function(){return this._def.ui.backgroundColor},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._def.ui.borderColor},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"textColor",{get:function(){return this._def.ui.textColor},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"classNames",{get:function(){return this._def.ui.classNames},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"extendedProps",{get:function(){return this._def.extendedProps},enumerable:!0,configurable:!0}),e}();function ct(e,t,n,r){var i={},o={},a={},s=[],l=[],d=ft(e.defs,t);for(var c in e.defs){"inverse-background"===(S=e.defs[c]).rendering&&(S.groupId?(i[S.groupId]=[],a[S.groupId]||(a[S.groupId]=S)):o[c]=[])}for(var u in e.instances){var p=e.instances[u],h=d[(S=e.defs[p.defId]).defId],f=p.range,g=!S.allDay&&r?ve(f,r):f,v=Ae(g,n);v&&("inverse-background"===S.rendering?S.groupId?i[S.groupId].push(v):o[p.defId].push(v):("background"===S.rendering?s:l).push({def:S,ui:h,instance:p,range:v,isStart:g.start&&g.start.valueOf()===v.start.valueOf(),isEnd:g.end&&g.end.valueOf()===v.end.valueOf()}))}for(var m in i)for(var y=0,E=ze(i[m],n);y<E.length;y++){var S,b=E[y];h=d[(S=a[m]).defId];s.push({def:S,ui:h,instance:null,range:b,isStart:!1,isEnd:!1})}for(var c in o)for(var D=0,T=ze(o[c],n);D<T.length;D++){b=T[D];s.push({def:e.defs[c],ui:d[c],instance:null,range:b,isStart:!1,isEnd:!1})}return{bg:s,fg:l}}function ut(e,t,n){e.hasPublicHandlers("eventRender")&&(t=t.filter(function(t){var r=e.publiclyTrigger("eventRender",[{event:new dt(e.calendar,t.eventRange.def,t.eventRange.instance),isMirror:n,isStart:t.isStart,isEnd:t.isEnd,el:t.el,view:e}]);return!1!==r&&(r&&!0!==r&&(t.el=r),!0)}));for(var r=0,i=t;r<i.length;r++){var o=i[r];pt(o.el,o)}return t}function pt(e,t){e.fcSeg=t}function ht(e){return e.fcSeg||null}function ft(e,t){return Ce(e,function(e){return gt(e,t)})}function gt(e,t){var n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),Lt(n)}function vt(e,t,n,r){var i=ft(e.defs,t),o={defs:{},instances:{}};for(var a in e.defs){var s=e.defs[a];o.defs[a]=mt(s,i[a],n,r.pluginSystem.hooks.eventDefMutationAppliers,r)}for(var l in e.instances){var d=e.instances[l];s=o.defs[d.defId];o.instances[l]=yt(d,s,i[d.defId],n,r)}return o}function mt(e,t,n,r,i){var o=n.standardProps||{};null==o.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(o.hasEnd=!0);var a=Se({},e,o,{ui:Se({},e.ui,o.ui)});n.extendedProps&&(a.extendedProps=Se({},a.extendedProps,n.extendedProps));for(var s=0,l=r;s<l.length;s++){(0,l[s])(a,n,i)}return!a.hasEnd&&i.opt("forceEventDuration")&&(a.hasEnd=!0),a}function yt(e,t,n,r,i){var o=i.dateEnv,a=r.standardProps&&!0===r.standardProps.allDay,s=r.standardProps&&!1===r.standardProps.hasEnd,l=Se({},e);return a&&(l.range=ge(l.range)),r.datesDelta&&n.startEditable&&(l.range={start:o.add(l.range.start,r.datesDelta),end:o.add(l.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(l.range={start:o.add(l.range.start,r.startDelta),end:l.range.end}),r.endDelta&&n.durationEditable&&(l.range={start:l.range.start,end:o.add(l.range.end,r.endDelta)}),s&&(l.range={start:l.range.start,end:i.getDefaultEventEnd(t.allDay,l.range.start)}),t.allDay&&(l.range={start:A(l.range.start),end:A(l.range.end)}),l.range.end<l.range.start&&(l.range.end=i.getDefaultEventEnd(t.allDay,l.range.start)),l}function Et(e,t,n,r,i){switch(t.type){case"RECEIVE_EVENTS":return function(e,t,n,r,i,o){if(t&&n===t.latestFetchId){var a=ke(function(e,t,n){var r=n.opt("eventDataTransform"),i=t?t.eventDataTransform:null;return i&&(e=xe(e,i)),r&&(e=xe(e,r)),e}(i,t,o),t.sourceId,o);return r&&(a=He(a,r,o)),_e(St(e,t.sourceId),a)}return e}(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,i);case"ADD_EVENTS":return function(e,t,n,r){n&&(t=He(t,n,r));return _e(e,t)}(e,t.eventStore,r?r.activeRange:null,i);case"MERGE_EVENTS":return _e(e,t.eventStore);case"PREV":case"NEXT":case"SET_DATE":case"SET_VIEW_TYPE":return r?He(e,r.activeRange,i):e;case"CHANGE_TIMEZONE":return function(e,t,n){var r=e.defs,i=Ce(e.instances,function(e){var i=r[e.defId];return i.allDay||i.recurringDef?e:Se({},e,{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo})});return{defs:r,instances:i}}(e,t.oldDateEnv,i.dateEnv);case"MUTATE_EVENTS":return function(e,t,n,r,i){var o=Oe(e,t),a=r?{"":{startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}}:i.eventUiBases;return o=vt(o,a,n,i),_e(e,o)}(e,t.instanceId,t.mutation,t.fromApi,i);case"REMOVE_EVENT_INSTANCES":return bt(e,t.instances);case"REMOVE_EVENT_DEF":return Le(e,function(e){return e.defId!==t.defId});case"REMOVE_EVENT_SOURCE":return St(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return Le(e,function(e){return!e.sourceId});case"REMOVE_ALL_EVENTS":return{defs:{},instances:{}};case"RESET_EVENTS":return{defs:e.defs,instances:e.instances};default:return e}}function St(e,t){return Le(e,function(e){return e.sourceId!==t})}function bt(e,t){return{defs:e.defs,instances:we(e.instances,function(e){return!t[e.instanceId]})}}function Dt(e,t){return Tt({eventDrag:e},t)}function Tt(e,t){var n=t.view,r=Se({businessHours:n?n.props.businessHours:{defs:{},instances:{}},dateSelection:"",eventStore:t.state.eventStore,eventUiBases:t.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginSystem.hooks.isPropsValid||wt)(r,t)}function wt(e,t,n,r){return void 0===n&&(n={}),!(e.eventDrag&&!function(e,t,n,r){var i=e.eventDrag,o=i.mutatedEvents,a=o.defs,s=o.instances,l=ft(a,i.isEvent?e.eventUiBases:{"":t.selectionConfig});r&&(l=Ce(l,r));var d=bt(e.eventStore,i.affectedEvents.instances),c=d.defs,u=d.instances,p=ft(c,e.eventUiBases);for(var h in s){var f=s[h],g=f.range,v=l[f.defId],m=a[f.defId];if(!Ct(v.constraints,g,d,e.businessHours,t))return!1;var y=t.opt("eventOverlap");for(var E in"function"!=typeof y&&(y=null),u){var S=u[E];if($e(g,S.range)){var b=p[S.defId].overlap;if(!1===b&&i.isEvent)return!1;if(!1===v.overlap)return!1;if(y&&!y(new dt(t,c[S.defId],S),new dt(t,m,f)))return!1}}for(var D=t.state.eventStore,T=0,w=v.allows;T<w.length;T++){var C=w[T],R=Se({},n,{range:f.range,allDay:m.allDay}),M=D.defs[m.defId],I=D.instances[h],k=void 0;if(k=M?new dt(t,M,I):new dt(t,m),!C(t.buildDateSpanApi(R),k))return!1}}return!0}(e,t,n,r))&&!(e.dateSelection&&!function(e,t,n,r){var i=e.eventStore,o=i.defs,a=i.instances,s=e.dateSelection,l=s.range,d=t.selectionConfig;r&&(d=r(d));if(!Ct(d.constraints,l,i,e.businessHours,t))return!1;var c=t.opt("selectOverlap");"function"!=typeof c&&(c=null);for(var u in a){var p=a[u];if($e(l,p.range)){if(!1===d.overlap)return!1;if(c&&!c(new dt(t,o[p.defId],p)))return!1}}for(var h=0,f=d.allows;h<f.length;h++){var g=f[h],v=Se({},n,s);if(!g(t.buildDateSpanApi(v),null))return!1}return!0}(e,t,n,r))}function Ct(e,t,n,r,i){for(var o=0,a=e;o<a.length;o++){if(!It(Rt(a[o],t,n,r,i),t))return!1}return!0}function Rt(e,t,n,r,i){return"businessHours"===e?Mt(He(r,t,i)):"string"==typeof e?Mt(Le(n,function(t){return t.groupId===e})):"object"==typeof e&&e?Mt(He(e,t,i)):[]}function Mt(e){var t=e.instances,n=[];for(var r in t)n.push(t[r].range);return n}function It(e,t){for(var n=0,r=e;n<r.length;n++){if(Fe(r[n],t))return!0}return!1}function kt(e){return(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#039;").replace(/"/g,"&quot;").replace(/\n/g,"<br />")}function Pt(e){var t=[];for(var n in e){var r=e[n];null!=r&&""!==r&&t.push(n+":"+r)}return t.join(";")}function Ht(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}var Ot={editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:null,overlap:null,allow:null,className:Ht,classNames:Ht,color:String,backgroundColor:String,borderColor:String,textColor:String};function xt(e,t,n){var r=fe(e,Ot,{},n),i=function(e,t){return Array.isArray(e)?ke(e,"",t,!0):"object"==typeof e&&e?ke([e],"",t,!0):null!=e?String(e):null}(r.constraint,t);return{startEditable:null!=r.startEditable?r.startEditable:r.editable,durationEditable:null!=r.durationEditable?r.durationEditable:r.editable,constraints:null!=i?[i]:[],overlap:r.overlap,allows:null!=r.allow?[r.allow]:[],backgroundColor:r.backgroundColor||r.color,borderColor:r.borderColor||r.color,textColor:r.textColor,classNames:r.classNames.concat(r.className)}}function Nt(e,t,n,r){var i={},o={};for(var a in Ot){var s=e+le(a);i[a]=t[s],o[s]=!0}if("event"===e&&(i.editable=t.editable),r)for(var a in t)o[a]||(r[a]=t[a]);return xt(i,n)}var _t={startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function Lt(e){return e.reduce(zt,_t)}function zt(e,t){return{startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}var Ut={id:String,groupId:String,title:String,url:String,rendering:String,extendedProps:null},At={start:null,date:null,end:null,allDay:null},Vt=0;function $t(e,t,n,r){var i=function(e,t){var n=null;if(e){var r=t.state.eventSources[e];n=r.allDayDefault}null==n&&(n=t.opt("allDayDefault"));return n}(t,n),o={},a=function(e,t,n,r,i){for(var o=0;o<r.length;o++){var a={},s=r[o].parse(e,a,n);if(s){var l=a.allDay;return delete a.allDay,null==l&&null==(l=t)&&null==(l=s.allDayGuess)&&(l=!1),Se(i,a),{allDay:l,duration:s.duration,typeData:s.typeData,typeId:o}}}return null}(e,i,n.dateEnv,n.pluginSystem.hooks.recurringTypes,o);if(a)return(s=Ft(o,t,a.allDay,Boolean(a.duration),n)).recurringDef={typeId:a.typeId,typeData:a.typeData,duration:a.duration},{def:s,instance:null};var s,l={},d=function(e,t,n,r,i){var o,a,s=function(e,t){var n=fe(e,At,{},t);return n.start=null!==n.start?n.start:n.date,delete n.date,n}(e,r),l=s.allDay,d=null,c=!1,u=null;if(o=n.dateEnv.createMarkerMeta(s.start))d=o.marker;else if(!i)return null;null!=s.end&&(a=n.dateEnv.createMarkerMeta(s.end));null==l&&(l=null!=t?t:(!o||o.isTimeUnspecified)&&(!a||a.isTimeUnspecified));l&&d&&(d=A(d));a&&(u=a.marker,l&&(u=A(u)),d&&u<=d&&(u=null));u?c=!0:i||(c=n.opt("forceEventDuration")||!1,u=n.dateEnv.add(d,l?n.defaultAllDayEventDuration:n.defaultTimedEventDuration));return{allDay:l,hasEnd:c,range:{start:d,end:u},forcedStartTzo:o?o.forcedTzo:null,forcedEndTzo:a?a.forcedTzo:null}}(e,i,n,l,r);return d?{def:s=Ft(l,t,d.allDay,d.hasEnd,n),instance:Bt(s.defId,d.range,d.forcedStartTzo,d.forcedEndTzo)}:null}function Ft(e,t,n,r,i){var o={},a=function(e,t,n){var r={},i=fe(e,Ut,{},r),o=xt(r,t,n);return i.publicId=i.id,delete i.id,i.ui=o,i}(e,i,o);a.defId=String(Vt++),a.sourceId=t,a.allDay=n,a.hasEnd=r;for(var s=0,l=i.pluginSystem.hooks.eventDefParsers;s<l.length;s++){var d={};(0,l[s])(a,o,d),o=d}return a.extendedProps=Se(o,a.extendedProps||{}),Object.freeze(a.ui.classNames),Object.freeze(a.extendedProps),a}function Bt(e,t,n,r){return{instanceId:String(Vt++),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}var jt={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],rendering:"inverse-background",classNames:"fc-nonbusiness",groupId:"_businessHours"};function Wt(e,t){return ke(function(e){var t;t=!0===e?[{}]:Array.isArray(e)?e.filter(function(e){return e.daysOfWeek}):"object"==typeof e&&e?[e]:[];return t=t.map(function(e){return Se({},jt,e)})}(e),"",t)}function Gt(e,t,n){void 0===n&&(n=[]);var r,i,o=[];function a(){if(i){for(var e=0,n=o;e<n.length;e++){n[e].unrender()}t&&t.apply(r,i),i=null}}function s(){i&&je(i,arguments)||(a(),r=this,i=arguments,e.apply(this,arguments))}s.dependents=o,s.unrender=a;for(var l=0,d=n;l<d.length;l++){d[l].dependents.push(s)}return s}var Yt={defs:{},instances:{}},qt=function(){function e(){this.getKeysForEventDefs=We(this._getKeysForEventDefs),this.splitDateSelection=We(this._splitDateSpan),this.splitEventStore=We(this._splitEventStore),this.splitIndividualUi=We(this._splitIndividualUi),this.splitEventDrag=We(this._splitInteraction),this.splitEventResize=We(this._splitInteraction),this.eventUiBuilders={}}return e.prototype.splitProps=function(e){var t=this,n=this.getKeyInfo(e),r=this.getKeysForEventDefs(e.eventStore),i=this.splitDateSelection(e.dateSelection),o=this.splitIndividualUi(e.eventUiBases,r),a=this.splitEventStore(e.eventStore,r),s=this.splitEventDrag(e.eventDrag),l=this.splitEventResize(e.eventResize),d={};for(var c in this.eventUiBuilders=Ce(n,function(e,n){return t.eventUiBuilders[n]||We(Zt)}),n){var u=n[c],p=a[c]||Yt,h=this.eventUiBuilders[c];d[c]={businessHours:u.businessHours||e.businessHours,dateSelection:i[c]||null,eventStore:p,eventUiBases:h(e.eventUiBases[""],u.ui,o[c]),eventSelection:p.instances[e.eventSelection]?e.eventSelection:"",eventDrag:s[c]||null,eventResize:l[c]||null}}return d},e.prototype._splitDateSpan=function(e){var t={};if(e)for(var n=0,r=this.getKeysForDateSpan(e);n<r.length;n++){t[r[n]]=e}return t},e.prototype._getKeysForEventDefs=function(e){var t=this;return Ce(e.defs,function(e){return t.getKeysForEventDef(e)})},e.prototype._splitEventStore=function(e,t){var n=e.defs,r=e.instances,i={};for(var o in n)for(var a=0,s=t[o];a<s.length;a++){i[p=s[a]]||(i[p]={defs:{},instances:{}}),i[p].defs[o]=n[o]}for(var l in r)for(var d=r[l],c=0,u=t[d.defId];c<u.length;c++){var p;i[p=u[c]]&&(i[p].instances[l]=d)}return i},e.prototype._splitIndividualUi=function(e,t){var n={};for(var r in e)if(r)for(var i=0,o=t[r];i<o.length;i++){var a=o[i];n[a]||(n[a]={}),n[a][r]=e[r]}return n},e.prototype._splitInteraction=function(e){var t={};if(e){var n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),i=this._splitEventStore(e.mutatedEvents,r),o=function(r){t[r]||(t[r]={affectedEvents:n[r]||Yt,mutatedEvents:i[r]||Yt,isEvent:e.isEvent,origSeg:e.origSeg})};for(var a in n)o(a);for(var a in i)o(a)}return t},e}();function Zt(e,t,n){var r=[];e&&r.push(e),t&&r.push(t);var i={"":Lt(r)};return n&&Se(i,n),i}function Xt(e,t,n,r){var i,o,a,s,l=e.dateEnv;return t instanceof Date?i=t:(i=t.date,o=t.type,a=t.forceOff),s={date:l.formatIso(i,{omitTime:!0}),type:o||"day"},"string"==typeof n&&(r=n,n=null),n=n?" "+function(e){var t=[];for(var n in e){var r=e[n];null!=r&&t.push(n+'="'+kt(r)+'"')}return t.join(" ")}(n):"",r=r||"",!a&&e.opt("navLinks")?"<a"+n+' data-goto="'+kt(JSON.stringify(s))+'">'+r+"</a>":"<span"+n+">"+r+"</span>"}function Jt(e,t,n,r){var i,o,a=n.calendar,s=n.view,l=n.theme,d=n.dateEnv,c=[];return Be(t.activeRange,e)?(c.push("fc-"+O[e.getUTCDay()]),s.opt("monthMode")&&d.getMonth(e)!==d.getMonth(t.currentRange.start)&&c.push("fc-other-month"),o=x(i=A(a.getNow()),1),e<i?c.push("fc-past"):e>=o?c.push("fc-future"):(c.push("fc-today"),!0!==r&&c.push(l.getClass("today")))):c.push("fc-disabled-day"),c}function Kt(e,t,n){var r=!1,i=function(){r||(r=!0,t.apply(this,arguments))},o=function(){r||(r=!0,n&&n.apply(this,arguments))},a=e(i,o);a&&"function"==typeof a.then&&a.then(i,o)}var Qt=function(){function e(){}return e.mixInto=function(e){this.mixIntoObj(e.prototype)},e.mixIntoObj=function(e){var t=this;Object.getOwnPropertyNames(this.prototype).forEach(function(n){e[n]||(e[n]=t.prototype[n])})},e.mixOver=function(e){var t=this;Object.getOwnPropertyNames(this.prototype).forEach(function(n){e.prototype[n]=t.prototype[n]})},e}(),en=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ee(t,e),t.prototype.on=function(e,t){return tn(this._handlers||(this._handlers={}),e,t),this},t.prototype.one=function(e,t){return tn(this._oneHandlers||(this._oneHandlers={}),e,t),this},t.prototype.off=function(e,t){return this._handlers&&nn(this._handlers,e,t),this._oneHandlers&&nn(this._oneHandlers,e,t),this},t.prototype.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return this.triggerWith(e,this,t),this},t.prototype.triggerWith=function(e,t,n){return this._handlers&&ue(this._handlers[e],t,n),this._oneHandlers&&(ue(this._oneHandlers[e],t,n),delete this._oneHandlers[e]),this},t.prototype.hasHandlers=function(e){return this._handlers&&this._handlers[e]&&this._handlers[e].length||this._oneHandlers&&this._oneHandlers[e]&&this._oneHandlers[e].length},t}(Qt);function tn(e,t,n){(e[t]||(e[t]=[])).push(n)}function nn(e,t,n){n?e[t]&&(e[t]=e[t].filter(function(e){return e!==n})):delete e[t]}var rn=function(){function e(e,t,n,r){this.originEl=e,this.els=t,this.isHorizontal=n,this.isVertical=r}return e.prototype.build=function(){var e=this.originEl,t=this.originClientRect=e.getBoundingClientRect();this.isHorizontal&&this.buildElHorizontals(t.left),this.isVertical&&this.buildElVerticals(t.top)},e.prototype.buildElHorizontals=function(e){for(var t=[],n=[],r=0,i=this.els;r<i.length;r++){var o=i[r].getBoundingClientRect();t.push(o.left-e),n.push(o.right-e)}this.lefts=t,this.rights=n},e.prototype.buildElVerticals=function(e){for(var t=[],n=[],r=0,i=this.els;r<i.length;r++){var o=i[r].getBoundingClientRect();t.push(o.top-e),n.push(o.bottom-e)}this.tops=t,this.bottoms=n},e.prototype.leftToIndex=function(e){var t,n=this.lefts,r=this.rights,i=n.length;for(t=0;t<i;t++)if(e>=n[t]&&e<r[t])return t},e.prototype.topToIndex=function(e){var t,n=this.tops,r=this.bottoms,i=n.length;for(t=0;t<i;t++)if(e>=n[t]&&e<r[t])return t},e.prototype.getWidth=function(e){return this.rights[e]-this.lefts[e]},e.prototype.getHeight=function(e){return this.bottoms[e]-this.tops[e]},e}(),on=function(){function e(){}return e.prototype.getMaxScrollTop=function(){return this.getScrollHeight()-this.getClientHeight()},e.prototype.getMaxScrollLeft=function(){return this.getScrollWidth()-this.getClientWidth()},e.prototype.canScrollVertically=function(){return this.getMaxScrollTop()>0},e.prototype.canScrollHorizontally=function(){return this.getMaxScrollLeft()>0},e.prototype.canScrollUp=function(){return this.getScrollTop()>0},e.prototype.canScrollDown=function(){return this.getScrollTop()<this.getMaxScrollTop()},e.prototype.canScrollLeft=function(){return this.getScrollLeft()>0},e.prototype.canScrollRight=function(){return this.getScrollLeft()<this.getMaxScrollLeft()},e}(),an=function(e){function t(t){var n=e.call(this)||this;return n.el=t,n}return Ee(t,e),t.prototype.getScrollTop=function(){return this.el.scrollTop},t.prototype.getScrollLeft=function(){return this.el.scrollLeft},t.prototype.setScrollTop=function(e){this.el.scrollTop=e},t.prototype.setScrollLeft=function(e){this.el.scrollLeft=e},t.prototype.getScrollWidth=function(){return this.el.scrollWidth},t.prototype.getScrollHeight=function(){return this.el.scrollHeight},t.prototype.getClientHeight=function(){return this.el.clientHeight},t.prototype.getClientWidth=function(){return this.el.clientWidth},t}(on),sn=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ee(t,e),t.prototype.getScrollTop=function(){return window.pageYOffset},t.prototype.getScrollLeft=function(){return window.pageXOffset},t.prototype.setScrollTop=function(e){window.scroll(window.pageXOffset,e)},t.prototype.setScrollLeft=function(e){window.scroll(e,window.pageYOffset)},t.prototype.getScrollWidth=function(){return document.documentElement.scrollWidth},t.prototype.getScrollHeight=function(){return document.documentElement.scrollHeight},t.prototype.getClientHeight=function(){return document.documentElement.clientHeight},t.prototype.getClientWidth=function(){return document.documentElement.clientWidth},t}(on),ln=function(e){function t(t,n){var i=e.call(this,r("div",{className:"fc-scroller"}))||this;return i.overflowX=t,i.overflowY=n,i.applyOverflow(),i}return Ee(t,e),t.prototype.clear=function(){this.setHeight("auto"),this.applyOverflow()},t.prototype.destroy=function(){c(this.el)},t.prototype.applyOverflow=function(){m(this.el,{overflowX:this.overflowX,overflowY:this.overflowY})},t.prototype.lockOverflow=function(e){var t=this.overflowX,n=this.overflowY;e=e||this.getScrollbarWidths(),"auto"===t&&(t=e.bottom||this.canScrollHorizontally()?"scroll":"hidden"),"auto"===n&&(n=e.left||e.right||this.canScrollVertically()?"scroll":"hidden"),m(this.el,{overflowX:t,overflowY:n})},t.prototype.setHeight=function(e){y(this.el,"height",e)},t.prototype.getScrollbarWidths=function(){var e=T(this.el);return{left:e.scrollbarLeft,right:e.scrollbarRight,bottom:e.scrollbarBottom}},t}(an),dn=function(){function e(e){this.calendarOptions=e,this.processIconOverride()}return e.prototype.processIconOverride=function(){this.iconOverrideOption&&this.setIconOverride(this.calendarOptions[this.iconOverrideOption])},e.prototype.setIconOverride=function(e){var t,n;if("object"==typeof e&&e){for(n in t=Se({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})},e.prototype.applyIconOverridePrefix=function(e){var t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e},e.prototype.getClass=function(e){return this.classes[e]||""},e.prototype.getIconClass=function(e){var t=this.iconClasses[e];return t?this.baseIconClass+" "+t:""},e.prototype.getCustomButtonIconClass=function(e){var t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption])?this.baseIconClass+" "+this.applyIconOverridePrefix(t):""},e}();dn.prototype.classes={},dn.prototype.iconClasses={},dn.prototype.baseIconClass="",dn.prototype.iconOverridePrefix="";var cn=0,un=function(){function e(e,t){t&&(e.view=this),this.uid=String(cn++),this.context=e,this.dateEnv=e.dateEnv,this.theme=e.theme,this.view=e.view,this.calendar=e.calendar,this.isRtl="rtl"===this.opt("dir")}return e.addEqualityFuncs=function(e){this.prototype.equalityFuncs=Se({},this.prototype.equalityFuncs,e)},e.prototype.opt=function(e){return this.context.options[e]},e.prototype.receiveProps=function(e){var t=function(e,t,n){var r={},i=!1;for(var o in t)o in e&&(e[o]===t[o]||n[o]&&n[o](e[o],t[o]))?r[o]=e[o]:(r[o]=t[o],i=!0);for(var o in e)if(!(o in t)){i=!0;break}return{anyChanges:i,comboProps:r}}(this.props||{},e,this.equalityFuncs),n=t.anyChanges,r=t.comboProps;this.props=r,n&&this.render(r)},e.prototype.render=function(e){},e.prototype.destroy=function(){},e}();un.prototype.equalityFuncs={};var pn=function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.el=n,i}return Ee(t,e),t.prototype.destroy=function(){e.prototype.destroy.call(this),c(this.el)},t.prototype.buildPositionCaches=function(){},t.prototype.queryHit=function(e,t,n,r){return null},t.prototype.isInteractionValid=function(e){var t=this.calendar,n=this.props.dateProfile,r=e.mutatedEvents.instances;if(n)for(var i in r)if(!Fe(n.validRange,r[i].range))return!1;return Dt(e,t)},t.prototype.isDateSelectionValid=function(e){var t,n,r=this.props.dateProfile;return!(r&&!Fe(r.validRange,e.range))&&(t=e,n=this.calendar,Tt({dateSelection:t},n))},t.prototype.publiclyTrigger=function(e,t){return this.calendar.publiclyTrigger(e,t)},t.prototype.publiclyTriggerAfterSizing=function(e,t){return this.calendar.publiclyTriggerAfterSizing(e,t)},t.prototype.hasPublicHandlers=function(e){return this.calendar.hasPublicHandlers(e)},t.prototype.triggerRenderedSegs=function(e,t){var n=this.calendar;if(this.hasPublicHandlers("eventPositioned"))for(var r=0,i=e;r<i.length;r++){var o=i[r];this.publiclyTriggerAfterSizing("eventPositioned",[{event:new dt(n,o.eventRange.def,o.eventRange.instance),isMirror:t,isStart:o.isStart,isEnd:o.isEnd,el:o.el,view:this}])}n.state.loadingLevel||(n.afterSizingTriggers._eventsPositioned=[null])},t.prototype.triggerWillRemoveSegs=function(e,t){for(var n=this.calendar,r=0,i=e;r<i.length;r++){var o=i[r];n.trigger("eventElRemove",o.el)}if(this.hasPublicHandlers("eventDestroy"))for(var a=0,s=e;a<s.length;a++){o=s[a];this.publiclyTrigger("eventDestroy",[{event:new dt(n,o.eventRange.def,o.eventRange.instance),isMirror:t,el:o.el,view:this}])}},t.prototype.isValidSegDownEl=function(e){return!this.props.eventDrag&&!this.props.eventResize&&!h(e,".fc-mirror")&&(this.isPopover()||!this.isInPopover(e))},t.prototype.isValidDateDownEl=function(e){var t=h(e,this.fgSegSelector);return(!t||t.classList.contains("fc-mirror"))&&!h(e,".fc-more")&&!h(e,"a[data-goto]")&&!this.isInPopover(e)},t.prototype.isPopover=function(){return this.el.classList.contains("fc-popover")},t.prototype.isInPopover=function(e){return Boolean(h(e,".fc-popover"))},t}(un);pn.prototype.fgSegSelector=".fc-event-container > *",pn.prototype.bgSegSelector=".fc-bgevent:not(.fc-nonbusiness)";var hn=0;function fn(e){return{id:String(hn++),deps:e.deps||[],reducers:e.reducers||[],eventDefParsers:e.eventDefParsers||[],isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],eventResizeJoinTransforms:e.eventResizeJoinTransforms||[],viewContainerModifiers:e.viewContainerModifiers||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,defaultView:e.defaultView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{}}}var gn=function(){function e(){this.hooks={reducers:[],eventDefParsers:[],isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],eventResizeJoinTransforms:[],viewContainerModifiers:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,defaultView:"",elementDraggingImpl:null,optionChangeHandlers:{}},this.addedHash={}}return e.prototype.add=function(e){if(!this.addedHash[e.id]){this.addedHash[e.id]=!0;for(var t=0,n=e.deps;t<n.length;t++){var r=n[t];this.add(r)}this.hooks=(i=this.hooks,o=e,{reducers:i.reducers.concat(o.reducers),eventDefParsers:i.eventDefParsers.concat(o.eventDefParsers),isDraggableTransformers:i.isDraggableTransformers.concat(o.isDraggableTransformers),eventDragMutationMassagers:i.eventDragMutationMassagers.concat(o.eventDragMutationMassagers),eventDefMutationAppliers:i.eventDefMutationAppliers.concat(o.eventDefMutationAppliers),dateSelectionTransformers:i.dateSelectionTransformers.concat(o.dateSelectionTransformers),datePointTransforms:i.datePointTransforms.concat(o.datePointTransforms),dateSpanTransforms:i.dateSpanTransforms.concat(o.dateSpanTransforms),views:Se({},i.views,o.views),viewPropsTransformers:i.viewPropsTransformers.concat(o.viewPropsTransformers),isPropsValid:o.isPropsValid||i.isPropsValid,externalDefTransforms:i.externalDefTransforms.concat(o.externalDefTransforms),eventResizeJoinTransforms:i.eventResizeJoinTransforms.concat(o.eventResizeJoinTransforms),viewContainerModifiers:i.viewContainerModifiers.concat(o.viewContainerModifiers),eventDropTransformers:i.eventDropTransformers.concat(o.eventDropTransformers),calendarInteractions:i.calendarInteractions.concat(o.calendarInteractions),componentInteractions:i.componentInteractions.concat(o.componentInteractions),themeClasses:Se({},i.themeClasses,o.themeClasses),eventSourceDefs:i.eventSourceDefs.concat(o.eventSourceDefs),cmdFormatter:o.cmdFormatter||i.cmdFormatter,recurringTypes:i.recurringTypes.concat(o.recurringTypes),namedTimeZonedImpl:o.namedTimeZonedImpl||i.namedTimeZonedImpl,defaultView:i.defaultView||o.defaultView,elementDraggingImpl:i.elementDraggingImpl||o.elementDraggingImpl,optionChangeHandlers:Se({},i.optionChangeHandlers,o.optionChangeHandlers)})}var i,o},e}();var vn=fn({eventSourceDefs:[{ignoreRange:!0,parseMeta:function(e){return Array.isArray(e)?e:Array.isArray(e.events)?e.events:null},fetch:function(e,t){t({rawEvents:e.eventSource.meta})}}]}),mn=fn({eventSourceDefs:[{parseMeta:function(e){return"function"==typeof e?e:"function"==typeof e.events?e.events:null},fetch:function(e,t,n){var r=e.calendar.dateEnv;Kt(e.eventSource.meta.bind(null,{start:r.toDate(e.range.start),end:r.toDate(e.range.end),startStr:r.formatIso(e.range.start),endStr:r.formatIso(e.range.end),timeZone:r.timeZone}),function(e){t({rawEvents:e})},n)}}]});function yn(e,t,n,r,i){var o=null;"GET"===(e=e.toUpperCase())?t=function(e,t){return e+(-1===e.indexOf("?")?"?":"&")+En(t)}(t,n):o=En(n);var a=new XMLHttpRequest;a.open(e,t,!0),"GET"!==e&&a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),a.onload=function(){if(a.status>=200&&a.status<400)try{var e=JSON.parse(a.responseText);r(e,a)}catch(e){i("Failure parsing JSON",a)}else i("Request failed",a)},a.onerror=function(){i("Request failed",a)},a.send(o)}function En(e){var t=[];for(var n in e)t.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t.join("&")}var Sn=fn({eventSourceDefs:[{parseMeta:function(e){if("string"==typeof e)e={url:e};else if(!e||"object"!=typeof e||!e.url)return null;return{url:e.url,method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}},fetch:function(e,t,n){var r=e.eventSource.meta,i=function(e,t,n){var r,i,o,a,s=n.dateEnv,l={};null==(r=e.startParam)&&(r=n.opt("startParam"));null==(i=e.endParam)&&(i=n.opt("endParam"));null==(o=e.timeZoneParam)&&(o=n.opt("timeZoneParam"));a="function"==typeof e.extraParams?e.extraParams():e.extraParams||{};Se(l,a),l[r]=s.formatIso(t.start),l[i]=s.formatIso(t.end),"local"!==s.timeZone&&(l[o]=s.timeZone);return l}(r,e.range,e.calendar);yn(r.method,r.url,i,function(e,n){t({rawEvents:e,xhr:n})},function(e,t){n({message:e,xhr:t})})}}]});var bn=fn({recurringTypes:[{parse:function(e,t,n){var r,i,o=n.createMarker.bind(n),a=fe(e,{daysOfWeek:null,startTime:X,endTime:X,startRecur:o,endRecur:o},{},t),s=!1;for(var l in a)if(null!=a[l]){s=!0;break}if(s){var d=null;return"duration"in t&&(d=X(t.duration),delete t.duration),!d&&a.startTime&&a.endTime&&(r=a.endTime,i=a.startTime,d={years:r.years-i.years,months:r.months-i.months,days:r.days-i.days,milliseconds:r.milliseconds-i.milliseconds}),{allDayGuess:Boolean(!a.startTime&&!a.endTime),duration:d,typeData:a}}return null},expand:function(e,t,n){var r=Ae(t,{start:e.startRecur,end:e.endRecur});return r?function(e,t,n,r){var i=e?Re(e):null,o=A(n.start),a=n.end,s=[];for(;o<a;){var l=void 0;i&&!i[o.getUTCDay()]||(l=t?r.add(o,t):o,s.push(l)),o=x(o,1)}return s}(e.daysOfWeek,e.startTime,r,n):[]}}]});var Dn=fn({optionChangeHandlers:{events:function(e,t,n){Tn([e],t,n)},eventSources:Tn,plugins:function(e,t){t.addPluginInputs(e)}}});function Tn(e,t,n){for(var r=Me(t.state.eventSources),i=[],o=0,a=e;o<a.length;o++){for(var s=a[o],l=!1,d=0;d<r.length;d++)if(n(r[d]._raw,s)){r.splice(d,1),l=!0;break}l||i.push(s)}for(var c=0,u=r;c<u.length;c++){var p=u[c];t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:p.sourceId})}for(var h=0,f=i;h<f.length;h++){var g=f[h];t.addEventSource(g)}}var wn={defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",columnHeader:!0,defaultView:"",aspectRatio:1.35,header:{left:"title",center:"",right:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,scrollTime:"06:00:00",minTime:"00:00:00",maxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",timeGridEventMinHeight:0,themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",eventLimit:!1,eventLimitClick:"popover",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5},Cn={header:{left:"next,prev today",center:"",right:"title"},buttonIcons:{prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"}},Rn=["header","footer","buttonText","buttonIcons"];var Mn=[vn,mn,Sn,bn,Dn];var In={code:"en",week:{dow:0,doy:4},dir:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekLabel:"W",allDayText:"all-day",eventLimitText:"more",noEventsMessage:"No events to display"};function kn(e){for(var t=e.length>0?e[0].code:"en",n=window.FullCalendarLocalesAll||[],r=window.FullCalendarLocales||{},i=n.concat(Me(r),e),o={en:In},a=0,s=i;a<s.length;a++){var l=s[a];o[l.code]=l}return{map:o,defaultCode:t}}function Pn(e,t){return"object"!=typeof e||Array.isArray(e)?function(e,t){var n=[].concat(e||[]),r=function(e,t){for(var n=0;n<e.length;n++)for(var r=e[n].toLocaleLowerCase().split("-"),i=r.length;i>0;i--){var o=r.slice(0,i).join("-");if(t[o])return t[o]}return null}(n,t)||In;return Hn(e,n,r)}(e,t):Hn(e.code,[e.code],e)}function Hn(e,t,n){var r=Te([In,n],["buttonText"]);delete r.code;var i=r.week;return delete r.week,{codeArg:e,codes:t,week:i,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}var On=function(){function e(e){this.overrides=Se({},e),this.dynamicOverrides={},this.compute()}return e.prototype.mutate=function(e,t,n){var r=n?this.dynamicOverrides:this.overrides;Se(r,e);for(var i=0,o=t;i<o.length;i++){delete r[o[i]]}this.compute()},e.prototype.compute=function(){var e=pe(this.dynamicOverrides.locales,this.overrides.locales,wn.locales),t=pe(this.dynamicOverrides.locale,this.overrides.locale,wn.locale),n=kn(e),r=Pn(t||n.defaultCode,n.map).options,i="rtl"===pe(this.dynamicOverrides.dir,this.overrides.dir,r.dir)?Cn:{};this.dirDefaults=i,this.localeDefaults=r,this.computed=Te([wn,i,r,this.overrides,this.dynamicOverrides],Rn)},e}(),xn={};var Nn,_n=function(){function e(){}return e.prototype.getMarkerYear=function(e){return e.getUTCFullYear()},e.prototype.getMarkerMonth=function(e){return e.getUTCMonth()},e.prototype.getMarkerDay=function(e){return e.getUTCDate()},e.prototype.arrayToMarker=function(e){return W(e)},e.prototype.markerToArray=function(e){return j(e)},e}();Nn=_n,xn["gregory"]=Nn;var Ln=/^\s*(\d{4})(-(\d{2})(-(\d{2})([T ](\d{2}):(\d{2})(:(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function zn(e){var t=Ln.exec(e);if(t){var n=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number("0."+t[12]):0));if(G(n)){var r=null;return t[13]&&(r=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:n,isTimeUnspecified:!t[6],timeZoneOffset:r}}}return null}var Un=function(){function e(e){var t,n=this.timeZone=e.timeZone,r="local"!==n&&"UTC"!==n;e.namedTimeZoneImpl&&r&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(n)),this.canComputeOffset=Boolean(!r||this.namedTimeZoneImpl),this.calendarSystem=(t=e.calendarSystem,new xn[t]),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekLabel=null!=e.weekLabel?e.weekLabel:e.locale.options.weekLabel,this.cmdFormatter=e.cmdFormatter}return e.prototype.createMarker=function(e){var t=this.createMarkerMeta(e);return null===t?null:t.marker},e.prototype.createNowMarker=function(){return this.canComputeOffset?this.timestampToMarker((new Date).valueOf()):W(F(new Date))},e.prototype.createMarkerMeta=function(e){if("string"==typeof e)return this.parse(e);var t=null;return"number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(t=this.timestampToMarker(e))):Array.isArray(e)&&(t=W(e)),null!==t&&G(t)?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null},e.prototype.parse=function(e){var t=zn(e);if(null===t)return null;var n=t.marker,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-60*t.timeZoneOffset*1e3):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}},e.prototype.getYear=function(e){return this.calendarSystem.getMarkerYear(e)},e.prototype.getMonth=function(e){return this.calendarSystem.getMarkerMonth(e)},e.prototype.add=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.subtract=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.addYears=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.addMonths=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.diffWholeYears=function(e,t){var n=this.calendarSystem;return Y(e)===Y(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null},e.prototype.diffWholeMonths=function(e,t){var n=this.calendarSystem;return Y(e)===Y(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+12*(n.getMarkerYear(t)-n.getMarkerYear(e)):null},e.prototype.greatestWholeUnit=function(e,t){var n=this.diffWholeYears(e,t);return null!==n?{unit:"year",value:n}:null!==(n=this.diffWholeMonths(e,t))?{unit:"month",value:n}:null!==(n=z(e,t))?{unit:"week",value:n}:null!==(n=U(e,t))?{unit:"day",value:n}:ce(n=function(e,t){return(t.valueOf()-e.valueOf())/36e5}(e,t))?{unit:"hour",value:n}:ce(n=function(e,t){return(t.valueOf()-e.valueOf())/6e4}(e,t))?{unit:"minute",value:n}:ce(n=function(e,t){return(t.valueOf()-e.valueOf())/1e3}(e,t))?{unit:"second",value:n}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}},e.prototype.countDurationsBetween=function(e,t,n){var r;return n.years&&null!==(r=this.diffWholeYears(e,t))?r/(ee(n)/365):n.months&&null!==(r=this.diffWholeMonths(e,t))?r/function(e){return ee(e)/30}(n):n.days&&null!==(r=U(e,t))?r/ee(n):(t.valueOf()-e.valueOf())/te(n)},e.prototype.startOf=function(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?A(e):"hour"===t?function(e){return W([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()])}(e):"minute"===t?function(e){return W([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()])}(e):"second"===t?function(e){return W([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()])}(e):void 0},e.prototype.startOfYear=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])},e.prototype.startOfMonth=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])},e.prototype.startOfWeek=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])},e.prototype.computeWeekNumber=function(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function(e,t,n){var r=e.getUTCFullYear(),i=V(e,r,t,n);if(i<1)return V(e,r-1,t,n);var o=V(e,r+1,t,n);return o>=1?Math.min(i,o):i}(e,this.weekDow,this.weekDoy)},e.prototype.format=function(e,t,n){return void 0===n&&(n={}),t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)},e.prototype.formatRange=function(e,t,n,r){return void 0===r&&(r={}),r.isEndExclusive&&(t=N(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this)},e.prototype.formatIso=function(e,t){void 0===t&&(t={});var n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),function(e,t,n){void 0===n&&(n=!1);var r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",ot(t,!0)))),r}(e,n,t.omitTime)},e.prototype.timestampToMarker=function(e){return"local"===this.timeZone?W(F(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?W(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)},e.prototype.offsetForMarker=function(e){return"local"===this.timeZone?-B(j(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(j(e)):null},e.prototype.toDate=function(e,t){return"local"===this.timeZone?B(j(e)):"UTC"===this.timeZone?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-1e3*this.namedTimeZoneImpl.offsetForArray(j(e))*60):new Date(e.valueOf()-(t||0))},e}(),An={id:String,allDayDefault:Boolean,eventDataTransform:Function,success:Function,failure:Function},Vn=0;function $n(e,t){return!t.pluginSystem.hooks.eventSourceDefs[e.sourceDefId].ignoreRange}function Fn(e,t){for(var n=t.pluginSystem.hooks.eventSourceDefs,r=n.length-1;r>=0;r--){var i=n[r].parseMeta(e);if(i){var o=Bn("object"==typeof e?e:{},i,r,t);return o._raw=e,o}}return null}function Bn(e,t,n,r){var i={},o=fe(e,An,{},i),a={},s=xt(i,r,a);return o.isFetching=!1,o.latestFetchId="",o.fetchRange=null,o.publicId=String(e.id||""),o.sourceId=String(Vn++),o.sourceDefId=n,o.meta=t,o.ui=s,o.extendedProps=a,o}function jn(e,t,n,r){switch(t.type){case"ADD_EVENT_SOURCES":return function(e,t,n,r){for(var i={},o=0,a=t;o<a.length;o++){var s=a[o];i[s.sourceId]=s}n&&(i=Gn(i,n,r));return Se({},e,i)}(e,t.sources,n?n.activeRange:null,r);case"REMOVE_EVENT_SOURCE":return i=e,o=t.sourceId,we(i,function(e){return e.sourceId!==o});case"PREV":case"NEXT":case"SET_DATE":case"SET_VIEW_TYPE":return n?Gn(e,n.activeRange,r):e;case"FETCH_EVENT_SOURCES":case"CHANGE_TIMEZONE":return Yn(e,t.sourceIds?Re(t.sourceIds):function(e,t){return we(e,function(e){return $n(e,t)})}(e,r),n?n.activeRange:null,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return function(e,t,n,r){var i,o=e[t];if(o&&n===o.latestFetchId)return Se({},e,((i={})[t]=Se({},o,{isFetching:!1,fetchRange:r}),i));return e}(e,t.sourceId,t.fetchId,t.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}var i,o}var Wn=0;function Gn(e,t,n){return Yn(e,we(e,function(e){return function(e,t,n){return $n(e,n)?!n.opt("lazyFetching")||!e.fetchRange||t.start<e.fetchRange.start||t.end>e.fetchRange.end:!e.latestFetchId}(e,t,n)}),t,n)}function Yn(e,t,n,r){var i={};for(var o in e){var a=e[o];t[o]?i[o]=qn(a,n,r):i[o]=a}return i}function qn(e,t,n){var r=n.pluginSystem.hooks.eventSourceDefs[e.sourceDefId],i=String(Wn++);return r.fetch({eventSource:e,calendar:n,range:t},function(r){var o,a,s=r.rawEvents,l=n.opt("eventSourceSuccess");e.success&&(a=e.success(s,r.xhr)),l&&(o=l(s,r.xhr)),s=a||o||s,n.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:i,fetchRange:t,rawEvents:s})},function(r){var o=n.opt("eventSourceFailure");console.warn(r.message,r),e.failure&&e.failure(r),o&&o(r),n.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:i,fetchRange:t,error:r})}),Se({},e,{isFetching:!0,latestFetchId:i})}var Zn=function(){function e(e,t){this.viewSpec=e,this.options=e.options,this.dateEnv=t.dateEnv,this.calendar=t,this.initHiddenDays()}return e.prototype.buildPrev=function(e,t){var n=this.dateEnv,r=n.subtract(n.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(r,-1)},e.prototype.buildNext=function(e,t){var n=this.dateEnv,r=n.add(n.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(r,1)},e.prototype.build=function(e,t,n){var r;void 0===n&&(n=!1);var i,o,a,s,l,d,c,u,p;return r=this.buildValidRange(),r=this.trimHiddenDays(r),n&&(u=e,e=null!=(p=r).start&&u<p.start?p.start:null!=p.end&&u>=p.end?new Date(p.end.valueOf()-1):u),a=this.buildCurrentRangeInfo(e,t),s=/^(year|month|week|day)$/.test(a.unit),l=this.buildRenderRange(this.trimHiddenDays(a.range),a.unit,s),d=l=this.trimHiddenDays(l),this.options.showNonCurrentDates||(d=Ae(d,a.range)),i=X(this.options.minTime),o=X(this.options.maxTime),d=Ae(d=this.adjustActiveRange(d,i,o),r),c=$e(a.range,r),{validRange:r,currentRange:a.range,currentRangeUnit:a.unit,isRangeAllDay:s,activeRange:d,renderRange:l,minTime:i,maxTime:o,isValid:c,dateIncrement:this.buildDateIncrement(a.duration)}},e.prototype.buildValidRange=function(){return this.getRangeOption("validRange",this.calendar.getNow())||{start:null,end:null}},e.prototype.buildCurrentRangeInfo=function(e,t){var n,r=this.viewSpec,i=this.dateEnv,o=null,a=null,s=null;return r.duration?(o=r.duration,a=r.durationUnit,s=this.buildRangeFromDuration(e,t,o,a)):(n=this.options.dayCount)?(a="day",s=this.buildRangeFromDayCount(e,t,n)):(s=this.buildCustomVisibleRange(e))?a=i.greatestWholeUnit(s.start,s.end).unit:(a=ne(o=this.getFallbackDuration()).unit,s=this.buildRangeFromDuration(e,t,o,a)),{duration:o,unit:a,range:s}},e.prototype.getFallbackDuration=function(){return X({day:1})},e.prototype.adjustActiveRange=function(e,t,n){var r=this.dateEnv,i=e.start,o=e.end;return this.viewSpec.class.prototype.usesMinMaxTime&&(ee(t)<0&&(i=A(i),i=r.add(i,t)),ee(n)>1&&(o=x(o=A(o),-1),o=r.add(o,n))),{start:i,end:o}},e.prototype.buildRangeFromDuration=function(e,t,n,r){var i,o,a,s,l,d=this.dateEnv,c=this.options.dateAlignment;function u(){a=d.startOf(e,c),s=d.add(a,n),l={start:a,end:s}}return c||((i=this.options.dateIncrement)?(o=X(i),c=te(o)<te(n)?ne(o,!K(i)).unit:r):c=r),ee(n)<=1&&this.isHiddenDay(a)&&(a=A(a=this.skipHiddenDays(a,t))),u(),this.trimHiddenDays(l)||(e=this.skipHiddenDays(e,t),u()),l},e.prototype.buildRangeFromDayCount=function(e,t,n){var r,i=this.dateEnv,o=this.options.dateAlignment,a=0,s=e;o&&(s=i.startOf(s,o)),s=A(s),r=s=this.skipHiddenDays(s,t);do{r=x(r,1),this.isHiddenDay(r)||a++}while(a<n);return{start:s,end:r}},e.prototype.buildCustomVisibleRange=function(e){var t=this.dateEnv,n=this.getRangeOption("visibleRange",t.toDate(e));return!n||null!=n.start&&null!=n.end?n:null},e.prototype.buildRenderRange=function(e,t,n){return e},e.prototype.buildDateIncrement=function(e){var t,n=this.options.dateIncrement;return n?X(n):(t=this.options.dateAlignment)?X(1,t):e||X({days:1})},e.prototype.getRangeOption=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,i,o,a,s=this.options[e];return"function"==typeof s&&(s=s.apply(null,t)),s&&(r=s,i=this.dateEnv,o=null,a=null,r.start&&(o=i.createMarker(r.start)),r.end&&(a=i.createMarker(r.end)),s=o||a?o&&a&&a<o?null:{start:o,end:a}:null),s&&(s=ve(s)),s},e.prototype.initHiddenDays=function(){var e,t=this.options.hiddenDays||[],n=[],r=0;for(!1===this.options.weekends&&t.push(0,6),e=0;e<7;e++)(n[e]=-1!==t.indexOf(e))||r++;if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n},e.prototype.trimHiddenDays=function(e){var t=e.start,n=e.end;return t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n?{start:t,end:n}:null},e.prototype.isHiddenDay=function(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]},e.prototype.skipHiddenDays=function(e,t,n){for(void 0===t&&(t=1),void 0===n&&(n=!1);this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=x(e,t);return e},e}();function Xn(e,t,n){for(var r=function(e,t){switch(t.type){case"SET_VIEW_TYPE":return t.viewType;default:return e}}(e.viewType,t),i=function(e,t,n,r,i){var o;switch(t.type){case"PREV":o=i.dateProfileGenerators[r].buildPrev(e,n);break;case"NEXT":o=i.dateProfileGenerators[r].buildNext(e,n);break;case"SET_DATE":e.activeRange&&Be(e.currentRange,t.dateMarker)||(o=i.dateProfileGenerators[r].build(t.dateMarker,void 0,!0));break;case"SET_VIEW_TYPE":var a=i.dateProfileGenerators[r];if(!a)throw new Error(r?'The FullCalendar view "'+r+'" does not exist. Make sure your plugins are loaded correctly.':"No available FullCalendar view plugins.");o=a.build(t.dateMarker||n,void 0,!0)}return!o||!o.isValid||e&&(s=e,l=o,Ve(s.validRange,l.validRange)&&Ve(s.activeRange,l.activeRange)&&Ve(s.renderRange,l.renderRange)&&Q(s.minTime,l.minTime)&&Q(s.maxTime,l.maxTime))?e:o;var s,l}(e.dateProfile,t,e.currentDate,r,n),o=jn(e.eventSources,t,i,n),a=Se({},e,{viewType:r,dateProfile:i,currentDate:Jn(e.currentDate,t,i),eventSources:o,eventStore:Et(e.eventStore,t,o,i,n),dateSelection:Kn(e.dateSelection,t,n),eventSelection:Qn(e.eventSelection,t),eventDrag:er(e.eventDrag,t,o,n),eventResize:tr(e.eventResize,t,o,n),eventSourceLoadingLevel:nr(o),loadingLevel:nr(o)}),s=0,l=n.pluginSystem.hooks.reducers;s<l.length;s++){a=(0,l[s])(a,t,n)}return a}function Jn(e,t,n){switch(t.type){case"PREV":case"NEXT":return Be(n.currentRange,e)?e:n.currentRange.start;case"SET_DATE":case"SET_VIEW_TYPE":var r=t.dateMarker||e;return n.activeRange&&!Be(n.activeRange,r)?n.currentRange.start:r;default:return e}}function Kn(e,t,n){switch(t.type){case"SELECT_DATES":return t.selection;case"UNSELECT_DATES":return null;default:return e}}function Qn(e,t){switch(t.type){case"SELECT_EVENT":return t.eventInstanceId;case"UNSELECT_EVENT":return"";default:return e}}function er(e,t,n,r){switch(t.type){case"SET_EVENT_DRAG":var i=t.state;return{affectedEvents:i.affectedEvents,mutatedEvents:i.mutatedEvents,isEvent:i.isEvent,origSeg:i.origSeg};case"UNSET_EVENT_DRAG":return null;default:return e}}function tr(e,t,n,r){switch(t.type){case"SET_EVENT_RESIZE":var i=t.state;return{affectedEvents:i.affectedEvents,mutatedEvents:i.mutatedEvents,isEvent:i.isEvent,origSeg:i.origSeg};case"UNSET_EVENT_RESIZE":return null;default:return e}}function nr(e){var t=0;for(var n in e)e[n].isFetching&&t++;return t}var rr={start:null,end:null,allDay:Boolean};function ir(e,t,n){var r=function(e,t){var n={},r=fe(e,rr,{},n),i=r.start?t.createMarkerMeta(r.start):null,o=r.end?t.createMarkerMeta(r.end):null,a=r.allDay;null==a&&(a=i&&i.isTimeUnspecified&&(!o||o.isTimeUnspecified));return n.range={start:i?i.marker:null,end:o?o.marker:null},n.allDay=a,n}(e,t),i=r.range;if(!i.start)return null;if(!i.end){if(null==n)return null;i.end=t.add(i.start,n)}return r}function or(e,t,n,r){if(t[e])return t[e];var i=function(e,t,n,r){var i=n[e],o=r[e],a=function(e){return i&&null!==i[e]?i[e]:o&&null!==o[e]?o[e]:null},s=a("class"),l=a("superType");!l&&s&&(l=ar(s,r)||ar(s,n));var d=null;if(l){if(l===e)throw new Error("Can't have a custom view type that references itself");d=or(l,t,n,r)}!s&&d&&(s=d.class);if(!s)return null;return{type:e,class:s,defaults:Se({},d?d.defaults:{},i?i.options:{}),overrides:Se({},d?d.overrides:{},o?o.options:{})}}(e,t,n,r);return i&&(t[e]=i),i}function ar(e,t){var n=Object.getPrototypeOf(e.prototype);for(var r in t){var i=t[r];if(i.class&&i.class.prototype===n)return r}return""}function sr(e){return Ce(e,dr)}var lr={type:String,class:null};function dr(e){"function"==typeof e&&(e={class:e});var t={},n=fe(e,lr,{},t);return{superType:n.type,class:n.class,options:t}}function cr(e,t){var n=sr(e),r=sr(t.overrides.views);return Ce(function(e,t){var n,r={};for(n in e)or(n,r,e,t);for(n in t)or(n,r,e,t);return r}(n,r),function(e){return function(e,t,n){var r=e.overrides.duration||e.defaults.duration||n.dynamicOverrides.duration||n.overrides.duration,i=null,o="",a="",s={};if(r&&(i=X(r))){var l=ne(i,!K(r));o=l.unit,1===l.value&&(a=o,s=t[o]?t[o].options:{})}var d=function(t){var n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[a]?n[a]:void 0};return{type:e.type,class:e.class,duration:i,durationUnit:o,singleUnit:a,options:Se({},wn,e.defaults,n.dirDefaults,n.localeDefaults,n.overrides,s,e.overrides,n.dynamicOverrides),buttonTextOverride:d(n.dynamicOverrides)||d(n.overrides)||e.overrides.buttonText,buttonTextDefault:d(n.localeDefaults)||d(n.dirDefaults)||e.defaults.buttonText||d(wn)||e.type}}(e,r,t)})}var ur=function(e){function t(t,n){var i=e.call(this,t)||this;return i._renderLayout=Gt(i.renderLayout,i.unrenderLayout),i._updateTitle=Gt(i.updateTitle,null,[i._renderLayout]),i._updateActiveButton=Gt(i.updateActiveButton,null,[i._renderLayout]),i._updateToday=Gt(i.updateToday,null,[i._renderLayout]),i._updatePrev=Gt(i.updatePrev,null,[i._renderLayout]),i._updateNext=Gt(i.updateNext,null,[i._renderLayout]),i.el=r("div",{className:"fc-toolbar "+n}),i}return Ee(t,e),t.prototype.destroy=function(){e.prototype.destroy.call(this),this._renderLayout.unrender(),c(this.el)},t.prototype.render=function(e){this._renderLayout(e.layout),this._updateTitle(e.title),this._updateActiveButton(e.activeButton),this._updateToday(e.isTodayEnabled),this._updatePrev(e.isPrevEnabled),this._updateNext(e.isNextEnabled)},t.prototype.renderLayout=function(e){var t=this.el;this.viewsWithButtons=[],s(t,this.renderSection("left",e.left)),s(t,this.renderSection("center",e.center)),s(t,this.renderSection("right",e.right))},t.prototype.unrenderLayout=function(){this.el.innerHTML=""},t.prototype.renderSection=function(e,t){var n=this,o=this.theme,a=this.calendar,l=a.optionsManager,d=a.viewSpecs,c=r("div",{className:"fc-"+e}),u=l.computed.customButtons||{},p=l.overrides.buttonText||{},h=l.computed.buttonText||{};return t&&t.split(" ").forEach(function(e,t){var r,l=[],f=!0;if(e.split(",").forEach(function(e,t){var r,s,c,g,v,m,y,E,S;"title"===e?(l.push(i("<h2>&nbsp;</h2>")),f=!1):((r=u[e])?(c=function(e){r.click&&r.click.call(E,e)},(g=o.getCustomButtonIconClass(r))||(g=o.getIconClass(e))||(v=r.text)):(s=d[e])?(n.viewsWithButtons.push(e),c=function(){a.changeView(e)},(v=s.buttonTextOverride)||(g=o.getIconClass(e))||(v=s.buttonTextDefault)):a[e]&&(c=function(){a[e]()},(v=p[e])||(g=o.getIconClass(e))||(v=h[e])),c&&(y=["fc-"+e+"-button",o.getClass("button")],v?(m=kt(v),S=""):g&&(m="<span class='"+g+"'></span>",S=' aria-label="'+e+'"'),(E=i('<button type="button" class="'+y.join(" ")+'"'+S+">"+m+"</button>")).addEventListener("click",c),l.push(E)))}),l.length>1){r=document.createElement("div");var g=o.getClass("buttonGroup");f&&g&&r.classList.add(g),s(r,l),c.appendChild(r)}else s(c,l)}),c},t.prototype.updateToday=function(e){this.toggleButtonEnabled("today",e)},t.prototype.updatePrev=function(e){this.toggleButtonEnabled("prev",e)},t.prototype.updateNext=function(e){this.toggleButtonEnabled("next",e)},t.prototype.updateTitle=function(e){g(this.el,"h2").forEach(function(t){t.innerText=e})},t.prototype.updateActiveButton=function(e){var t=this.theme.getClass("buttonActive");g(this.el,"button").forEach(function(n){e&&n.classList.contains("fc-"+e+"-button")?n.classList.add(t):n.classList.remove(t)})},t.prototype.toggleButtonEnabled=function(e,t){g(this.el,".fc-"+e+"-button").forEach(function(e){e.disabled=!t})},t}(un),pr=function(e){function t(t,n){var i=e.call(this,t)||this;i._renderToolbars=Gt(i.renderToolbars),i.buildViewPropTransformers=We(fr),i.el=n,l(n,i.contentEl=r("div",{className:"fc-view-container"}));for(var o=i.calendar,a=0,s=o.pluginSystem.hooks.viewContainerModifiers;a<s.length;a++){(0,s[a])(i.contentEl,o)}return i.toggleElClassNames(!0),i.computeTitle=We(hr),i.parseBusinessHours=We(function(e){return Wt(e,i.calendar)}),i}return Ee(t,e),t.prototype.destroy=function(){this.header&&this.header.destroy(),this.footer&&this.footer.destroy(),this.view&&this.view.destroy(),c(this.contentEl),this.toggleElClassNames(!1),e.prototype.destroy.call(this)},t.prototype.toggleElClassNames=function(e){var t=this.el.classList,n="fc-"+this.opt("dir"),r=this.theme.getClass("widget");e?(t.add("fc"),t.add(n),t.add(r)):(t.remove("fc"),t.remove(n),t.remove(r))},t.prototype.render=function(e){this.freezeHeight();var t=this.computeTitle(e.dateProfile,e.viewSpec.options);this._renderToolbars(e.viewSpec,e.dateProfile,e.currentDate,e.dateProfileGenerator,t),this.renderView(e,t),this.updateSize(),this.thawHeight()},t.prototype.renderToolbars=function(e,t,n,r,i){var o=this.opt("header"),a=this.opt("footer"),d=this.calendar.getNow(),c=r.build(d),u=r.buildPrev(t,n),p=r.buildNext(t,n),h={title:i,activeButton:e.type,isTodayEnabled:c.isValid&&!Be(t.currentRange,d),isPrevEnabled:u.isValid,isNextEnabled:p.isValid};o?(this.header||(this.header=new ur(this.context,"fc-header-toolbar"),l(this.el,this.header.el)),this.header.receiveProps(Se({layout:o},h))):this.header&&(this.header.destroy(),this.header=null),a?(this.footer||(this.footer=new ur(this.context,"fc-footer-toolbar"),s(this.el,this.footer.el)),this.footer.receiveProps(Se({layout:a},h))):this.footer&&(this.footer.destroy(),this.footer=null)},t.prototype.renderView=function(e,t){var n=this.view,r=e.viewSpec,i=e.dateProfileGenerator;n&&n.viewSpec===r?n.addScroll(n.queryScroll()):(n&&n.destroy(),n=this.view=new r.class({calendar:this.calendar,view:null,dateEnv:this.dateEnv,theme:this.theme,options:r.options},r,i,this.contentEl)),n.title=t;for(var o={dateProfile:e.dateProfile,businessHours:this.parseBusinessHours(r.options.businessHours),eventStore:e.eventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize},a=0,s=this.buildViewPropTransformers(this.calendar.pluginSystem.hooks.viewPropsTransformers);a<s.length;a++){var l=s[a];Se(o,l.transform(o,r,e,n))}n.receiveProps(o)},t.prototype.updateSize=function(e){void 0===e&&(e=!1);var t=this.view;e&&t.addScroll(t.queryScroll()),(e||null==this.isHeightAuto)&&this.computeHeightVars(),t.updateSize(e,this.viewHeight,this.isHeightAuto),t.updateNowIndicator(),t.popScroll(e)},t.prototype.computeHeightVars=function(){var e=this.calendar,t=e.opt("height"),n=e.opt("contentHeight");if(this.isHeightAuto="auto"===t||"auto"===n,"number"==typeof n)this.viewHeight=n;else if("function"==typeof n)this.viewHeight=n();else if("number"==typeof t)this.viewHeight=t-this.queryToolbarsHeight();else if("function"==typeof t)this.viewHeight=t()-this.queryToolbarsHeight();else if("parent"===t){var r=this.el.parentNode;this.viewHeight=r.getBoundingClientRect().height-this.queryToolbarsHeight()}else this.viewHeight=Math.round(this.contentEl.getBoundingClientRect().width/Math.max(e.opt("aspectRatio"),.5))},t.prototype.queryToolbarsHeight=function(){var e=0;return this.header&&(e+=R(this.header.el)),this.footer&&(e+=R(this.footer.el)),e},t.prototype.freezeHeight=function(){m(this.el,{height:this.el.getBoundingClientRect().height,overflow:"hidden"})},t.prototype.thawHeight=function(){m(this.el,{height:"",overflow:""})},t}(un);function hr(e,t){var n;return n=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,this.dateEnv.formatRange(n.start,n.end,it(t.titleFormat||function(e){var t=e.currentRangeUnit;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};var n=U(e.currentRange.start,e.currentRange.end);return null!==n&&n>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}(e),t.titleRangeSeparator),{isEndExclusive:e.isRangeAllDay})}function fr(e){return e.map(function(e){return new e})}var gr=function(){function e(e){this.component=e.component}return e.prototype.destroy=function(){},e}();var vr={},mr=function(e){function t(t){var n=e.call(this,t)||this;n.handleSegClick=function(e,t){var r=n.component,i=ht(t);if(i&&r.isValidSegDownEl(e.target)){var o=h(e.target,".fc-has-url"),a=o?o.querySelector("a[href]").href:"";r.publiclyTrigger("eventClick",[{el:t,event:new dt(r.calendar,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:r.view}]),a&&!e.defaultPrevented&&(window.location.href=a)}};var r=t.component;return n.destroy=P(r.el,"click",r.fgSegSelector+","+r.bgSegSelector,n.handleSegClick),n}return Ee(t,e),t}(gr),yr=function(e){function t(t){var n=e.call(this,t)||this;n.handleEventElRemove=function(e){e===n.currentSegEl&&n.handleSegLeave(null,n.currentSegEl)},n.handleSegEnter=function(e,t){ht(t)&&(t.classList.add("fc-allow-mouse-resize"),n.currentSegEl=t,n.triggerEvent("eventMouseEnter",e,t))},n.handleSegLeave=function(e,t){n.currentSegEl&&(t.classList.remove("fc-allow-mouse-resize"),n.currentSegEl=null,n.triggerEvent("eventMouseLeave",e,t))};var r,i,o,a,s,l=t.component;return n.removeHoverListeners=(r=l.el,i=l.fgSegSelector+","+l.bgSegSelector,o=n.handleSegEnter,a=n.handleSegLeave,P(r,"mouseover",i,function(e,t){if(t!==s){s=t,o(e,t);var n=function(e){s=null,a(e,t),t.removeEventListener("mouseleave",n)};t.addEventListener("mouseleave",n)}})),l.calendar.on("eventElRemove",n.handleEventElRemove),n}return Ee(t,e),t.prototype.destroy=function(){this.removeHoverListeners(),this.component.calendar.off("eventElRemove",this.handleEventElRemove)},t.prototype.triggerEvent=function(e,t,n){var r=this.component,i=ht(n);t&&!r.isValidSegDownEl(t.target)||r.publiclyTrigger(e,[{el:n,event:new dt(this.component.calendar,i.eventRange.def,i.eventRange.instance),jsEvent:t,view:r.view}])},t}(gr),Er=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ee(t,e),t}(dn);Er.prototype.classes={widget:"fc-unthemed",widgetHeader:"fc-widget-header",widgetContent:"fc-widget-content",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active",popoverHeader:"fc-widget-header",popoverContent:"fc-widget-content",headerRow:"fc-widget-header",dayRow:"fc-widget-content",listView:"fc-widget-content"},Er.prototype.baseIconClass="fc-icon",Er.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},Er.prototype.iconOverrideOption="buttonIcons",Er.prototype.iconOverrideCustomButtonOption="icon",Er.prototype.iconOverridePrefix="fc-icon-";var Sr=function(){function e(e,t){var n=this;this.parseRawLocales=We(kn),this.buildLocale=We(Pn),this.buildDateEnv=We(br),this.buildTheme=We(Dr),this.buildEventUiSingleBase=We(this._buildEventUiSingleBase),this.buildSelectionConfig=We(this._buildSelectionConfig),this.buildEventUiBySource=Ge(wr,Ie),this.buildEventUiBases=We(Cr),this.interactionsStore={},this.actionQueue=[],this.isReducing=!1,this.needsRerender=!1,this.needsFullRerender=!1,this.isRendering=!1,this.renderingPauseDepth=0,this.buildDelayedRerender=We(Tr),this.afterSizingTriggers={},this.isViewUpdated=!1,this.isDatesUpdated=!1,this.isEventsUpdated=!1,this.el=e,this.optionsManager=new On(t||{}),this.pluginSystem=new gn,this.addPluginInputs(this.optionsManager.computed.plugins||[]),this.handleOptions(this.optionsManager.computed),this.publiclyTrigger("_init"),this.hydrate(),this.calendarInteractions=this.pluginSystem.hooks.calendarInteractions.map(function(e){return new e(n)})}return e.prototype.addPluginInputs=function(e){for(var t=function(e){for(var t=[],n=0,r=e;n<r.length;n++){var i=r[n];if("string"==typeof i){var o="FullCalendar"+le(i);window[o]?t.push(window[o].default):console.warn("Plugin file not loaded for "+i)}else t.push(i)}return Mn.concat(t)}(e),n=0,r=t;n<r.length;n++){var i=r[n];this.pluginSystem.add(i)}},Object.defineProperty(e.prototype,"view",{get:function(){return this.component?this.component.view:null},enumerable:!0,configurable:!0}),e.prototype.render=function(){this.component?this.requestRerender(!0):(this.renderableEventStore={defs:{},instances:{}},this.bindHandlers(),this.executeRender())},e.prototype.destroy=function(){if(this.component){this.unbindHandlers(),this.component.destroy(),this.component=null;for(var e=0,t=this.calendarInteractions;e<t.length;e++){t[e].destroy()}this.publiclyTrigger("_destroyed")}},e.prototype.bindHandlers=function(){var e=this;this.removeNavLinkListener=P(this.el,"click","a[data-goto]",function(t,n){var r=n.getAttribute("data-goto");r=r?JSON.parse(r):{};var i=e.dateEnv,o=i.createMarker(r.date),a=r.type,s=e.viewOpt("navLink"+le(a)+"Click");"function"==typeof s?s(i.toDate(o),t):("string"==typeof s&&(a=s),e.zoomTo(o,a))}),this.opt("handleWindowResize")&&window.addEventListener("resize",this.windowResizeProxy=he(this.windowResize.bind(this),this.opt("windowResizeDelay")))},e.prototype.unbindHandlers=function(){this.removeNavLinkListener(),this.windowResizeProxy&&(window.removeEventListener("resize",this.windowResizeProxy),this.windowResizeProxy=null)},e.prototype.hydrate=function(){var e=this;this.state=this.buildInitialState();var t=this.opt("eventSources")||[],n=this.opt("events"),r=[];n&&t.unshift(n);for(var i=0,o=t;i<o.length;i++){var a=Fn(o[i],this);a&&r.push(a)}this.batchRendering(function(){e.dispatch({type:"INIT"}),e.dispatch({type:"ADD_EVENT_SOURCES",sources:r}),e.dispatch({type:"SET_VIEW_TYPE",viewType:e.opt("defaultView")||e.pluginSystem.hooks.defaultView})})},e.prototype.buildInitialState=function(){return{viewType:null,loadingLevel:0,eventSourceLoadingLevel:0,currentDate:this.getInitialDate(),dateProfile:null,eventSources:{},eventStore:{defs:{},instances:{}},dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null}},e.prototype.dispatch=function(e){if(this.actionQueue.push(e),!this.isReducing){this.isReducing=!0;for(var t=this.state;this.actionQueue.length;)this.state=this.reduce(this.state,this.actionQueue.shift(),this);var n=this.state;this.isReducing=!1,!t.loadingLevel&&n.loadingLevel?this.publiclyTrigger("loading",[!0]):t.loadingLevel&&!n.loadingLevel&&this.publiclyTrigger("loading",[!1]);var r=this.component&&this.component.view;(t.eventStore!==n.eventStore||this.needsFullRerender)&&t.eventStore&&(this.isEventsUpdated=!0),(t.dateProfile!==n.dateProfile||this.needsFullRerender)&&(t.dateProfile&&r&&this.publiclyTrigger("datesDestroy",[{view:r,el:r.el}]),this.isDatesUpdated=!0),(t.viewType!==n.viewType||this.needsFullRerender)&&(t.viewType&&r&&this.publiclyTrigger("viewSkeletonDestroy",[{view:r,el:r.el}]),this.isViewUpdated=!0),this.requestRerender()}},e.prototype.reduce=function(e,t,n){return Xn(e,t,n)},e.prototype.requestRerender=function(e){void 0===e&&(e=!1),this.needsRerender=!0,this.needsFullRerender=this.needsFullRerender||e,this.delayedRerender()},e.prototype.tryRerender=function(){this.component&&this.needsRerender&&!this.renderingPauseDepth&&!this.isRendering&&this.executeRender()},e.prototype.batchRendering=function(e){this.renderingPauseDepth++,e(),this.renderingPauseDepth--,this.needsRerender&&this.requestRerender()},e.prototype.executeRender=function(){var e=this.needsFullRerender;this.needsRerender=!1,this.needsFullRerender=!1,this.isRendering=!0,this.renderComponent(e),this.isRendering=!1,this.needsRerender&&this.delayedRerender()},e.prototype.renderComponent=function(e){var t=this.state,n=this.component,r=t.viewType,i=this.viewSpecs[r],o=e&&n?n.view.queryScroll():null;if(!i)throw new Error('View type "'+r+'" is not valid');var a=this.renderableEventStore=t.eventSourceLoadingLevel&&!this.opt("progressiveEventRendering")?this.renderableEventStore:t.eventStore,s=this.buildEventUiSingleBase(i.options),l=this.buildEventUiBySource(t.eventSources),d=this.eventUiBases=this.buildEventUiBases(a.defs,s,l);!e&&n||(n&&(n.freezeHeight(),n.destroy()),n=this.component=new pr({calendar:this,view:null,dateEnv:this.dateEnv,theme:this.theme,options:this.optionsManager.computed},this.el),this.isViewUpdated=!0,this.isDatesUpdated=!0,this.isEventsUpdated=!0),n.receiveProps(Se({},t,{viewSpec:i,dateProfile:t.dateProfile,dateProfileGenerator:this.dateProfileGenerators[r],eventStore:a,eventUiBases:d,dateSelection:t.dateSelection,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize})),o&&n.view.applyScroll(o,!1),this.isViewUpdated&&(this.isViewUpdated=!1,this.publiclyTrigger("viewSkeletonRender",[{view:n.view,el:n.view.el}])),this.isDatesUpdated&&(this.isDatesUpdated=!1,this.publiclyTrigger("datesRender",[{view:n.view,el:n.view.el}])),this.isEventsUpdated&&(this.isEventsUpdated=!1),this.releaseAfterSizingTriggers()},e.prototype.setOption=function(e,t){var n;this.mutateOptions(((n={})[e]=t,n),[],!0)},e.prototype.getOption=function(e){return this.optionsManager.computed[e]},e.prototype.opt=function(e){return this.optionsManager.computed[e]},e.prototype.viewOpt=function(e){return this.viewOpts()[e]},e.prototype.viewOpts=function(){return this.viewSpecs[this.state.viewType].options},e.prototype.mutateOptions=function(e,t,n,r){var i=this,o=this.pluginSystem.hooks.optionChangeHandlers,a={},s={},l=this.dateEnv,d=!1,c=!1,u=Boolean(t.length);for(var p in e)o[p]?s[p]=e[p]:a[p]=e[p];for(var h in a)/^(height|contentHeight|aspectRatio)$/.test(h)?c=!0:/^(defaultDate|defaultView)$/.test(h)||(u=!0,"timeZone"===h&&(d=!0));this.optionsManager.mutate(a,t,n),u&&(this.handleOptions(this.optionsManager.computed),this.needsFullRerender=!0),this.batchRendering(function(){if(u?(d&&i.dispatch({type:"CHANGE_TIMEZONE",oldDateEnv:l}),i.dispatch({type:"SET_VIEW_TYPE",viewType:i.state.viewType})):c&&i.updateSize(),r)for(var e in s)o[e](s[e],i,r)})},e.prototype.handleOptions=function(e){var t=this,n=this.pluginSystem.hooks;this.defaultAllDayEventDuration=X(e.defaultAllDayEventDuration),this.defaultTimedEventDuration=X(e.defaultTimedEventDuration),this.delayedRerender=this.buildDelayedRerender(e.rerenderDelay),this.theme=this.buildTheme(e);var r=this.parseRawLocales(e.locales);this.availableRawLocales=r.map;var i=this.buildLocale(e.locale||r.defaultCode,r.map);this.dateEnv=this.buildDateEnv(i,e.timeZone,n.namedTimeZonedImpl,e.firstDay,e.weekNumberCalculation,e.weekLabel,n.cmdFormatter),this.selectionConfig=this.buildSelectionConfig(e),this.viewSpecs=cr(n.views,this.optionsManager),this.dateProfileGenerators=Ce(this.viewSpecs,function(e){return new e.class.prototype.dateProfileGeneratorClass(e,t)})},e.prototype.getAvailableLocaleCodes=function(){return Object.keys(this.availableRawLocales)},e.prototype._buildSelectionConfig=function(e){return Nt("select",e,this)},e.prototype._buildEventUiSingleBase=function(e){return e.editable&&(e=Se({},e,{eventEditable:!0})),Nt("event",e,this)},e.prototype.hasPublicHandlers=function(e){return this.hasHandlers(e)||this.opt(e)},e.prototype.publiclyTrigger=function(e,t){var n=this.opt(e);if(this.triggerWith(e,this,t),n)return n.apply(this,t)},e.prototype.publiclyTriggerAfterSizing=function(e,t){var n=this.afterSizingTriggers;(n[e]||(n[e]=[])).push(t)},e.prototype.releaseAfterSizingTriggers=function(){var e=this.afterSizingTriggers;for(var t in e)for(var n=0,r=e[t];n<r.length;n++){var i=r[n];this.publiclyTrigger(t,i)}this.afterSizingTriggers={}},e.prototype.isValidViewType=function(e){return Boolean(this.viewSpecs[e])},e.prototype.changeView=function(e,t){var n=null;t&&(t.start&&t.end?(this.optionsManager.mutate({visibleRange:t},[]),this.handleOptions(this.optionsManager.computed)):n=this.dateEnv.createMarker(t)),this.unselect(),this.dispatch({type:"SET_VIEW_TYPE",viewType:e,dateMarker:n})},e.prototype.zoomTo=function(e,t){var n;t=t||"day",n=this.viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"SET_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"SET_DATE",dateMarker:e})},e.prototype.getUnitViewSpec=function(e){var t,n,r=this.component,i=[];for(var o in r.header&&i.push.apply(i,r.header.viewsWithButtons),r.footer&&i.push.apply(i,r.footer.viewsWithButtons),this.viewSpecs)i.push(o);for(t=0;t<i.length;t++)if((n=this.viewSpecs[i[t]])&&n.singleUnit===e)return n},e.prototype.getInitialDate=function(){var e=this.opt("defaultDate");return null!=e?this.dateEnv.createMarker(e):this.getNow()},e.prototype.prev=function(){this.unselect(),this.dispatch({type:"PREV"})},e.prototype.next=function(){this.unselect(),this.dispatch({type:"NEXT"})},e.prototype.prevYear=function(){this.unselect(),this.dispatch({type:"SET_DATE",dateMarker:this.dateEnv.addYears(this.state.currentDate,-1)})},e.prototype.nextYear=function(){this.unselect(),this.dispatch({type:"SET_DATE",dateMarker:this.dateEnv.addYears(this.state.currentDate,1)})},e.prototype.today=function(){this.unselect(),this.dispatch({type:"SET_DATE",dateMarker:this.getNow()})},e.prototype.gotoDate=function(e){this.unselect(),this.dispatch({type:"SET_DATE",dateMarker:this.dateEnv.createMarker(e)})},e.prototype.incrementDate=function(e){var t=X(e);t&&(this.unselect(),this.dispatch({type:"SET_DATE",dateMarker:this.dateEnv.add(this.state.currentDate,t)}))},e.prototype.getDate=function(){return this.dateEnv.toDate(this.state.currentDate)},e.prototype.formatDate=function(e,t){var n=this.dateEnv;return n.format(n.createMarker(e),it(t))},e.prototype.formatRange=function(e,t,n){var r=this.dateEnv;return r.formatRange(r.createMarker(e),r.createMarker(t),it(n,this.opt("defaultRangeSeparator")),n)},e.prototype.formatIso=function(e,t){var n=this.dateEnv;return n.formatIso(n.createMarker(e),{omitTime:t})},e.prototype.windowResize=function(e){!this.isHandlingWindowResize&&this.component&&e.target===window&&(this.isHandlingWindowResize=!0,this.updateSize(),this.publiclyTrigger("windowResize",[this.view]),this.isHandlingWindowResize=!1)},e.prototype.updateSize=function(){this.component&&this.component.updateSize(!0)},e.prototype.registerInteractiveComponent=function(e,t){var n=function(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter}}(e,t),r=[mr,yr].concat(this.pluginSystem.hooks.componentInteractions).map(function(e){return new e(n)});this.interactionsStore[e.uid]=r,vr[e.uid]=n},e.prototype.unregisterInteractiveComponent=function(e){for(var t=0,n=this.interactionsStore[e.uid];t<n.length;t++){n[t].destroy()}delete this.interactionsStore[e.uid],delete vr[e.uid]},e.prototype.select=function(e,t){var n=ir(null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t},this.dateEnv,X({days:1}));n&&(this.dispatch({type:"SELECT_DATES",selection:n}),this.triggerDateSelect(n))},e.prototype.unselect=function(e){this.state.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),this.triggerDateUnselect(e))},e.prototype.triggerDateSelect=function(e,t){var n=Se({},this.buildDateSpanApi(e),{jsEvent:t?t.origEvent:null,view:this.view});this.publiclyTrigger("select",[n])},e.prototype.triggerDateUnselect=function(e){this.publiclyTrigger("unselect",[{jsEvent:e?e.origEvent:null,view:this.view}])},e.prototype.triggerDateClick=function(e,t,n,r){var i=Se({},this.buildDatePointApi(e),{dayEl:t,jsEvent:r,view:n});this.publiclyTrigger("dateClick",[i])},e.prototype.buildDatePointApi=function(e){for(var t,n,r={},i=0,o=this.pluginSystem.hooks.datePointTransforms;i<o.length;i++){var a=o[i];Se(r,a(e,this))}return Se(r,(t=e,{date:(n=this.dateEnv).toDate(t.range.start),dateStr:n.formatIso(t.range.start,{omitTime:t.allDay}),allDay:t.allDay})),r},e.prototype.buildDateSpanApi=function(e){for(var t,n,r={},i=0,o=this.pluginSystem.hooks.dateSpanTransforms;i<o.length;i++){var a=o[i];Se(r,a(e,this))}return Se(r,(t=e,{start:(n=this.dateEnv).toDate(t.range.start),end:n.toDate(t.range.end),startStr:n.formatIso(t.range.start,{omitTime:t.allDay}),endStr:n.formatIso(t.range.end,{omitTime:t.allDay}),allDay:t.allDay})),r},e.prototype.getNow=function(){var e=this.opt("now");return"function"==typeof e&&(e=e()),null==e?this.dateEnv.createNowMarker():this.dateEnv.createMarker(e)},e.prototype.getDefaultEventEnd=function(e,t){var n=t;return e?(n=A(n),n=this.dateEnv.add(n,this.defaultAllDayEventDuration)):n=this.dateEnv.add(n,this.defaultTimedEventDuration),n},e.prototype.addEvent=function(e,t){if(e instanceof dt){var n=e._def,r=e._instance;return this.state.eventStore.defs[n.defId]||this.dispatch({type:"ADD_EVENTS",eventStore:Pe({def:n,instance:r})}),e}var i;if(t instanceof lt)i=t.internalEventSource.sourceId;else if(null!=t){var o=this.getEventSourceById(t);if(!o)return console.warn('Could not find an event source with ID "'+t+'"'),null;i=o.internalEventSource.sourceId}var a=$t(e,i,this);return a?(this.dispatch({type:"ADD_EVENTS",eventStore:Pe(a)}),new dt(this,a.def,a.def.recurringDef?null:a.instance)):null},e.prototype.getEventById=function(e){var t=this.state.eventStore,n=t.defs,r=t.instances;for(var i in e=String(e),n){var o=n[i];if(o.publicId===e){if(o.recurringDef)return new dt(this,o,null);for(var a in r){var s=r[a];if(s.defId===o.defId)return new dt(this,o,s)}}}return null},e.prototype.getEvents=function(){var e=this.state.eventStore,t=e.defs,n=e.instances,r=[];for(var i in n){var o=n[i],a=t[o.defId];r.push(new dt(this,a,o))}return r},e.prototype.removeAllEvents=function(){this.dispatch({type:"REMOVE_ALL_EVENTS"})},e.prototype.rerenderEvents=function(){this.dispatch({type:"RESET_EVENTS"})},e.prototype.getEventSources=function(){var e=this.state.eventSources,t=[];for(var n in e)t.push(new lt(this,e[n]));return t},e.prototype.getEventSourceById=function(e){var t=this.state.eventSources;for(var n in e=String(e),t)if(t[n].publicId===e)return new lt(this,t[n]);return null},e.prototype.addEventSource=function(e){if(e instanceof lt)return this.state.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;var t=Fn(e,this);return t?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[t]}),new lt(this,t)):null},e.prototype.removeAllEventSources=function(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})},e.prototype.refetchEvents=function(){this.dispatch({type:"FETCH_EVENT_SOURCES"})},e.prototype.scrollToTime=function(e){var t=X(e);t&&this.component.view.scrollToDuration(t)},e}();function br(e,t,n,r,i,o,a){return new Un({calendarSystem:"gregory",timeZone:t,namedTimeZoneImpl:n,locale:e,weekNumberCalculation:i,firstDay:r,weekLabel:o,cmdFormatter:a})}function Dr(e){return new(this.pluginSystem.hooks.themeClasses[e.themeSystem]||Er)(e)}function Tr(e){var t=this.tryRerender.bind(this);return null!=e&&(t=he(t,e)),t}function wr(e){return Ce(e,function(e){return e.ui})}function Cr(e,t,n){var r={"":t};for(var i in e){var o=e[i];o.sourceId&&n[o.sourceId]&&(r[i]=n[o.sourceId])}return r}en.mixInto(Sr);var Rr=function(e){function t(t,n,i,o){var a=e.call(this,t,r("div",{className:"fc-view fc-"+n.type+"-view"}),!0)||this;return a.renderDatesMem=Gt(a.renderDatesWrap,a.unrenderDatesWrap),a.renderBusinessHoursMem=Gt(a.renderBusinessHours,a.unrenderBusinessHours,[a.renderDatesMem]),a.renderDateSelectionMem=Gt(a.renderDateSelectionWrap,a.unrenderDateSelectionWrap,[a.renderDatesMem]),a.renderEventsMem=Gt(a.renderEvents,a.unrenderEvents,[a.renderDatesMem]),a.renderEventSelectionMem=Gt(a.renderEventSelectionWrap,a.unrenderEventSelectionWrap,[a.renderEventsMem]),a.renderEventDragMem=Gt(a.renderEventDragWrap,a.unrenderEventDragWrap,[a.renderDatesMem]),a.renderEventResizeMem=Gt(a.renderEventResizeWrap,a.unrenderEventResizeWrap,[a.renderDatesMem]),a.viewSpec=n,a.dateProfileGenerator=i,a.type=n.type,a.eventOrderSpecs=ie(a.opt("eventOrder")),a.nextDayThreshold=X(a.opt("nextDayThreshold")),o.appendChild(a.el),a.initialize(),a}return Ee(t,e),t.prototype.initialize=function(){},Object.defineProperty(t.prototype,"activeStart",{get:function(){return this.dateEnv.toDate(this.props.dateProfile.activeRange.start)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"activeEnd",{get:function(){return this.dateEnv.toDate(this.props.dateProfile.activeRange.end)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentStart",{get:function(){return this.dateEnv.toDate(this.props.dateProfile.currentRange.start)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentEnd",{get:function(){return this.dateEnv.toDate(this.props.dateProfile.currentRange.end)},enumerable:!0,configurable:!0}),t.prototype.render=function(e){this.renderDatesMem(e.dateProfile),this.renderBusinessHoursMem(e.businessHours),this.renderDateSelectionMem(e.dateSelection),this.renderEventsMem(e.eventStore),this.renderEventSelectionMem(e.eventSelection),this.renderEventDragMem(e.eventDrag),this.renderEventResizeMem(e.eventResize)},t.prototype.destroy=function(){e.prototype.destroy.call(this),this.renderDatesMem.unrender()},t.prototype.updateSize=function(e,t,n){var r=this.calendar;(e||r.isViewUpdated||r.isDatesUpdated||r.isEventsUpdated)&&this.updateBaseSize(e,t,n)},t.prototype.updateBaseSize=function(e,t,n){},t.prototype.renderDatesWrap=function(e){this.renderDates(e),this.addScroll({duration:X(this.opt("scrollTime"))}),this.startNowIndicator(e)},t.prototype.unrenderDatesWrap=function(){this.stopNowIndicator(),this.unrenderDates()},t.prototype.renderDates=function(e){},t.prototype.unrenderDates=function(){},t.prototype.renderBusinessHours=function(e){},t.prototype.unrenderBusinessHours=function(){},t.prototype.renderDateSelectionWrap=function(e){e&&this.renderDateSelection(e)},t.prototype.unrenderDateSelectionWrap=function(e){e&&this.unrenderDateSelection(e)},t.prototype.renderDateSelection=function(e){},t.prototype.unrenderDateSelection=function(e){},t.prototype.renderEvents=function(e){},t.prototype.unrenderEvents=function(){},t.prototype.sliceEvents=function(e,t){var n=this.props;return ct(e,n.eventUiBases,n.dateProfile.activeRange,t?this.nextDayThreshold:null).fg},t.prototype.computeEventDraggable=function(e,t){return!1},t.prototype.computeEventStartResizable=function(e,t){return t.durationEditable&&this.opt("eventResizableFromStart")},t.prototype.computeEventEndResizable=function(e,t){return t.durationEditable},t.prototype.renderEventSelectionWrap=function(e){e&&this.renderEventSelection(e)},t.prototype.unrenderEventSelectionWrap=function(e){e&&this.unrenderEventSelection(e)},t.prototype.renderEventSelection=function(e){},t.prototype.unrenderEventSelection=function(e){},t.prototype.renderEventDragWrap=function(e){e&&this.renderEventDrag(e)},t.prototype.unrenderEventDragWrap=function(e){e&&this.unrenderEventDrag(e)},t.prototype.renderEventDrag=function(e){},t.prototype.unrenderEventDrag=function(e){},t.prototype.renderEventResizeWrap=function(e){e&&this.renderEventResize(e)},t.prototype.unrenderEventResizeWrap=function(e){e&&this.unrenderEventResize(e)},t.prototype.renderEventResize=function(e){},t.prototype.unrenderEventResize=function(e){},t.prototype.startNowIndicator=function(e){var t,n,r,i=this,o=this.dateEnv;this.opt("nowIndicator")&&(t=this.getNowIndicatorUnit(e))&&(n=this.updateNowIndicator.bind(this),this.initialNowDate=this.calendar.getNow(),this.initialNowQueriedMs=(new Date).valueOf(),r=o.add(o.startOf(this.initialNowDate,t),X(1,t)).valueOf()-this.initialNowDate.valueOf(),this.nowIndicatorTimeoutID=setTimeout(function(){i.nowIndicatorTimeoutID=null,n(),r="second"===t?1e3:6e4,i.nowIndicatorIntervalID=setInterval(n,r)},r))},t.prototype.updateNowIndicator=function(){this.props.dateProfile&&this.initialNowDate&&(this.unrenderNowIndicator(),this.renderNowIndicator(N(this.initialNowDate,(new Date).valueOf()-this.initialNowQueriedMs)),this.isNowIndicatorRendered=!0)},t.prototype.stopNowIndicator=function(){this.isNowIndicatorRendered&&(this.nowIndicatorTimeoutID&&(clearTimeout(this.nowIndicatorTimeoutID),this.nowIndicatorTimeoutID=null),this.nowIndicatorIntervalID&&(clearInterval(this.nowIndicatorIntervalID),this.nowIndicatorIntervalID=null),this.unrenderNowIndicator(),this.isNowIndicatorRendered=!1)},t.prototype.getNowIndicatorUnit=function(e){},t.prototype.renderNowIndicator=function(e){},t.prototype.unrenderNowIndicator=function(){},t.prototype.addScroll=function(e){var t=this.queuedScroll||(this.queuedScroll={});Se(t,e)},t.prototype.popScroll=function(e){this.applyQueuedScroll(e),this.queuedScroll=null},t.prototype.applyQueuedScroll=function(e){this.applyScroll(this.queuedScroll||{},e)},t.prototype.queryScroll=function(){var e={};return this.props.dateProfile&&Se(e,this.queryDateScroll()),e},t.prototype.applyScroll=function(e,t){var n=e.duration;null!=n&&(delete e.duration,this.props.dateProfile&&Se(e,this.computeDateScroll(n))),this.props.dateProfile&&this.applyDateScroll(e)},t.prototype.computeDateScroll=function(e){return{}},t.prototype.queryDateScroll=function(){return{}},t.prototype.applyDateScroll=function(e){},t.prototype.scrollToDuration=function(e){this.applyScroll({duration:e},!1)},t}(pn);en.mixInto(Rr),Rr.prototype.usesMinMaxTime=!1,Rr.prototype.dateProfileGeneratorClass=Zn;var Mr=function(){function e(e){this.segs=[],this.isSizeDirty=!1,this.context=e}return e.prototype.renderSegs=function(e,t){this.rangeUpdated(),e=this.renderSegEls(e,t),this.segs=e,this.attachSegs(e,t),this.isSizeDirty=!0,this.context.view.triggerRenderedSegs(this.segs,Boolean(t))},e.prototype.unrender=function(e,t){this.context.view.triggerWillRemoveSegs(this.segs,Boolean(t)),this.detachSegs(this.segs),this.segs=[]},e.prototype.rangeUpdated=function(){var e,t,n=this.context.options;this.eventTimeFormat=it(n.eventTimeFormat||this.computeEventTimeFormat(),n.defaultRangeSeparator),null==(e=n.displayEventTime)&&(e=this.computeDisplayEventTime()),null==(t=n.displayEventEnd)&&(t=this.computeDisplayEventEnd()),this.displayEventTime=e,this.displayEventEnd=t},e.prototype.renderSegEls=function(e,t){var n,r="";if(e.length){for(n=0;n<e.length;n++)r+=this.renderSegHtml(e[n],t);o(r).forEach(function(t,n){var r=e[n];t&&(r.el=t)}),e=ut(this.context.view,e,Boolean(t))}return e},e.prototype.getSegClasses=function(e,t,n,r){var i=["fc-event",e.isStart?"fc-start":"fc-not-start",e.isEnd?"fc-end":"fc-not-end"].concat(e.eventRange.ui.classNames);return t&&i.push("fc-draggable"),n&&i.push("fc-resizable"),r&&(i.push("fc-mirror"),r.isDragging&&i.push("fc-dragging"),r.isResizing&&i.push("fc-resizing")),i},e.prototype.getTimeText=function(e,t,n){var r=e.def,i=e.instance;return this._getTimeText(i.range.start,r.hasEnd?i.range.end:null,r.allDay,t,n,i.forcedStartTzo,i.forcedEndTzo)},e.prototype._getTimeText=function(e,t,n,r,i,o,a){var s=this.context.dateEnv;return null==r&&(r=this.eventTimeFormat),null==i&&(i=this.displayEventEnd),this.displayEventTime&&!n?i&&t?s.formatRange(e,t,r,{forcedStartTzo:o,forcedEndTzo:a}):s.format(e,r,{forcedTzo:o}):""},e.prototype.computeEventTimeFormat=function(){return{hour:"numeric",minute:"2-digit",omitZeroMinute:!0}},e.prototype.computeDisplayEventTime=function(){return!0},e.prototype.computeDisplayEventEnd=function(){return!0},e.prototype.getSkinCss=function(e){return{"background-color":e.backgroundColor,"border-color":e.borderColor,color:e.textColor}},e.prototype.sortEventSegs=function(e){var t=this.context.view.eventOrderSpecs,n=e.map(Ir);return n.sort(function(e,n){return oe(e,n,t)}),n.map(function(e){return e._seg})},e.prototype.computeSizes=function(e){(e||this.isSizeDirty)&&this.computeSegSizes(this.segs)},e.prototype.assignSizes=function(e){(e||this.isSizeDirty)&&(this.assignSegSizes(this.segs),this.isSizeDirty=!1)},e.prototype.computeSegSizes=function(e){},e.prototype.assignSegSizes=function(e){},e.prototype.hideByHash=function(e){if(e)for(var t=0,n=this.segs;t<n.length;t++){var r=n[t];e[r.eventRange.instance.instanceId]&&(r.el.style.visibility="hidden")}},e.prototype.showByHash=function(e){if(e)for(var t=0,n=this.segs;t<n.length;t++){var r=n[t];e[r.eventRange.instance.instanceId]&&(r.el.style.visibility="")}},e.prototype.selectByInstanceId=function(e){if(e)for(var t=0,n=this.segs;t<n.length;t++){var r=n[t],i=r.eventRange.instance;i&&i.instanceId===e&&r.el&&r.el.classList.add("fc-selected")}},e.prototype.unselectByInstanceId=function(e){if(e)for(var t=0,n=this.segs;t<n.length;t++){var r=n[t];r.el&&r.el.classList.remove("fc-selected")}},e}();function Ir(e){var t=e.eventRange.def,n=e.eventRange.instance.range,r=n.start?n.start.valueOf():0,i=n.end?n.end.valueOf():0;return Se({},t.extendedProps,t,{id:t.publicId,start:r,end:i,duration:i-r,allDay:Number(t.allDay),_seg:e})}var kr=function(){function e(e){this.fillSegTag="div",this.dirtySizeFlags={},this.context=e,this.containerElsByType={},this.segsByType={}}return e.prototype.getSegsByType=function(e){return this.segsByType[e]||[]},e.prototype.renderSegs=function(e,t){var n,r=this.renderSegEls(e,t),i=this.attachSegs(e,r);i&&(n=this.containerElsByType[e]||(this.containerElsByType[e]=[])).push.apply(n,i),this.segsByType[e]=r,"bgEvent"===e&&this.context.view.triggerRenderedSegs(r,!1),this.dirtySizeFlags[e]=!0},e.prototype.unrender=function(e){var t=this.segsByType[e];t&&("bgEvent"===e&&this.context.view.triggerWillRemoveSegs(t,!1),this.detachSegs(e,t))},e.prototype.renderSegEls=function(e,t){var n,r=this,i="";if(t.length){for(n=0;n<t.length;n++)i+=this.renderSegHtml(e,t[n]);o(i).forEach(function(e,n){var r=t[n];e&&(r.el=e)}),"bgEvent"===e&&(t=ut(this.context.view,t,!1)),t=t.filter(function(e){return f(e.el,r.fillSegTag)})}return t},e.prototype.renderSegHtml=function(e,t){var n=null,r=[];return"highlight"!==e&&"businessHours"!==e&&(n={"background-color":t.eventRange.ui.backgroundColor}),"highlight"!==e&&(r=r.concat(t.eventRange.ui.classNames)),"businessHours"===e?r.push("fc-bgevent"):r.push("fc-"+e.toLowerCase()),"<"+this.fillSegTag+(r.length?' class="'+r.join(" ")+'"':"")+(n?' style="'+Pt(n)+'"':"")+"></"+this.fillSegTag+">"},e.prototype.detachSegs=function(e,t){var n=this.containerElsByType[e];n&&(n.forEach(c),delete this.containerElsByType[e])},e.prototype.computeSizes=function(e){for(var t in this.segsByType)(e||this.dirtySizeFlags[t])&&this.computeSegSizes(this.segsByType[t])},e.prototype.assignSizes=function(e){for(var t in this.segsByType)(e||this.dirtySizeFlags[t])&&this.assignSegSizes(this.segsByType[t]);this.dirtySizeFlags={}},e.prototype.computeSegSizes=function(e){},e.prototype.assignSegSizes=function(e){},e}(),Pr=function(){return function(e){this.timeZoneName=e}}(),Hr=function(){function e(e){this.emitter=new en}return e.prototype.destroy=function(){},e.prototype.setMirrorIsVisible=function(e){},e.prototype.setMirrorNeedsRevert=function(e){},e.prototype.setAutoScrollEnabled=function(e){},e}();function Or(e){var t=Pn(e.locale||"en",kn([]).map);return e=Se({timeZone:wn.timeZone,calendarSystem:"gregory"},e,{locale:t}),new Un(e)}var xr={startTime:X,duration:X,create:Boolean,sourceId:String},Nr={create:!0};function _r(e,t){return!e||t>10?{weekday:"short"}:t>1?{weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}:{weekday:"long"}}function Lr(e,t,n,r,i,o,a,s){var l,d=o.view,c=o.dateEnv,u=o.theme,p=o.options,h=Be(t.activeRange,e),f=["fc-day-header",u.getClass("widgetHeader")];return l="function"==typeof p.columnHeaderHtml?p.columnHeaderHtml(c.toDate(e)):"function"==typeof p.columnHeaderText?kt(p.columnHeaderText(c.toDate(e))):kt(c.format(e,i)),n?f=f.concat(Jt(e,t,o,!0)):f.push("fc-"+O[e.getUTCDay()]),'<th class="'+f.join(" ")+'"'+(h&&n?' data-date="'+c.formatIso(e,{omitTime:!0})+'"':"")+(a>1?' colspan="'+a+'"':"")+(s?" "+s:"")+">"+(h?Xt(d,{date:e,forceOff:!n||1===r},l):l)+"</th>"}var zr=function(e){function t(t,n){var r=e.call(this,t)||this;return n.innerHTML="",n.appendChild(r.el=i('<div class="fc-row '+r.theme.getClass("headerRow")+'"><table class="'+r.theme.getClass("tableGrid")+'"><thead></thead></table></div>')),r.thead=r.el.querySelector("thead"),r}return Ee(t,e),t.prototype.destroy=function(){c(this.el)},t.prototype.render=function(e){var t=e.dates,n=e.datesRepDistinctDays,r=[];e.renderIntroHtml&&r.push(e.renderIntroHtml());for(var i=it(this.opt("columnHeaderFormat")||_r(n,t.length)),o=0,a=t;o<a.length;o++){var s=a[o];r.push(Lr(s,e.dateProfile,n,t.length,i,this.context))}this.isRtl&&r.reverse(),this.thead.innerHTML="<tr>"+r.join("")+"</tr>"},t}(un),Ur=function(){function e(e,t){for(var n=e.start,r=e.end,i=[],o=[],a=-1;n<r;)t.isHiddenDay(n)?i.push(a+.5):(a++,i.push(a),o.push(n)),n=x(n,1);this.dates=o,this.indices=i,this.cnt=o.length}return e.prototype.sliceRange=function(e){var t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(x(e.end,-1)),r=Math.max(0,t),i=Math.min(this.cnt-1,n);return(r=Math.ceil(r))<=(i=Math.floor(i))?{firstIndex:r,lastIndex:i,isStart:t===r,isEnd:n===i}:null},e.prototype.getDateDayIndex=function(e){var t=this.indices,n=Math.floor(_(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]},e}(),Ar=function(){function e(e,t){var n,r,i,o=e.dates;if(t){for(r=o[0].getUTCDay(),n=1;n<o.length&&o[n].getUTCDay()!==r;n++);i=Math.ceil(o.length/n)}else i=1,n=o.length;this.rowCnt=i,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}return e.prototype.buildCells=function(){for(var e=[],t=0;t<this.rowCnt;t++){for(var n=[],r=0;r<this.colCnt;r++)n.push(this.buildCell(t,r));e.push(n)}return e},e.prototype.buildCell=function(e,t){return{date:this.daySeries.dates[e*this.colCnt+t]}},e.prototype.buildHeaderDates=function(){for(var e=[],t=0;t<this.colCnt;t++)e.push(this.cells[0][t].date);return e},e.prototype.sliceRange=function(e){var t=this.colCnt,n=this.daySeries.sliceRange(e),r=[];if(n)for(var i=n.firstIndex,o=n.lastIndex,a=i;a<=o;){var s=Math.floor(a/t),l=Math.min((s+1)*t,o+1);r.push({row:s,firstCol:a%t,lastCol:(l-1)%t,isStart:n.isStart&&a===i,isEnd:n.isEnd&&l-1===o}),a=l}return r},e}(),Vr=function(){function e(){this.sliceBusinessHours=We(this._sliceBusinessHours),this.sliceDateSelection=We(this._sliceDateSpan),this.sliceEventStore=We(this._sliceEventStore),this.sliceEventDrag=We(this._sliceInteraction),this.sliceEventResize=We(this._sliceInteraction)}return e.prototype.sliceProps=function(e,t,n,r){for(var i=[],o=4;o<arguments.length;o++)i[o-4]=arguments[o];var a=e.eventUiBases,s=this.sliceEventStore.apply(this,[e.eventStore,a,t,n,r].concat(i));return{dateSelectionSegs:this.sliceDateSelection.apply(this,[e.dateSelection,a,r].concat(i)),businessHourSegs:this.sliceBusinessHours.apply(this,[e.businessHours,t,n,r].concat(i)),fgEventSegs:s.fg,bgEventSegs:s.bg,eventDrag:this.sliceEventDrag.apply(this,[e.eventDrag,a,t,n,r].concat(i)),eventResize:this.sliceEventResize.apply(this,[e.eventResize,a,t,n,r].concat(i)),eventSelection:e.eventSelection}},e.prototype.sliceNowDate=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this._sliceDateSpan.apply(this,[{range:{start:e,end:N(e,1)},allDay:!1},{},t].concat(n))},e.prototype._sliceBusinessHours=function(e,t,n,r){for(var i=[],o=4;o<arguments.length;o++)i[o-4]=arguments[o];return e?this._sliceEventStore.apply(this,[He(e,$r(t,Boolean(n)),r.calendar),{},t,n,r].concat(i)).bg:[]},e.prototype._sliceEventStore=function(e,t,n,r,i){for(var o=[],a=5;a<arguments.length;a++)o[a-5]=arguments[a];if(e){var s=ct(e,t,$r(n,Boolean(r)),r);return{bg:this.sliceEventRanges(s.bg,i,o),fg:this.sliceEventRanges(s.fg,i,o)}}return{bg:[],fg:[]}},e.prototype._sliceInteraction=function(e,t,n,r,i){for(var o=[],a=5;a<arguments.length;a++)o[a-5]=arguments[a];if(!e)return null;var s=ct(e.mutatedEvents,t,$r(n,Boolean(r)),r);return{segs:this.sliceEventRanges(s.fg,i,o),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent,sourceSeg:e.origSeg}},e.prototype._sliceDateSpan=function(e,t,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];if(!e)return[];for(var o=function(e,t,n){var r=Ft({editable:!1},"",e.allDay,!0,n);return{def:r,ui:gt(r,t),instance:Bt(r.defId,e.range),range:e.range,isStart:!0,isEnd:!0}}(e,t,n.calendar),a=this.sliceRange.apply(this,[e.range].concat(r)),s=0,l=a;s<l.length;s++){var d=l[s];d.component=n,d.eventRange=o}return a},e.prototype.sliceEventRanges=function(e,t,n){for(var r=[],i=0,o=e;i<o.length;i++){var a=o[i];r.push.apply(r,this.sliceEventRange(a,t,n))}return r},e.prototype.sliceEventRange=function(e,t,n){for(var r=this.sliceRange.apply(this,[e.range].concat(n)),i=0,o=r;i<o.length;i++){var a=o[i];a.component=t,a.eventRange=e,a.isStart=e.isStart&&a.isStart,a.isEnd=e.isEnd&&a.isEnd}return r},e}();function $r(e,t){var n=e.activeRange;return t?n:{start:N(n.start,e.minTime.milliseconds),end:N(n.end,e.maxTime.milliseconds-864e5)}}e.Calendar=Sr,e.Component=un,e.DateComponent=pn,e.DateEnv=Un,e.DateProfileGenerator=Zn,e.DayHeader=zr,e.DaySeries=Ur,e.DayTable=Ar,e.ElementDragging=Hr,e.ElementScrollController=an,e.EmitterMixin=en,e.EventApi=dt,e.FgEventRenderer=Mr,e.FillRenderer=kr,e.Interaction=gr,e.Mixin=Qt,e.NamedTimeZoneImpl=Pr,e.PositionCache=rn,e.ScrollComponent=ln,e.ScrollController=on,e.Slicer=Vr,e.Splitter=qt,e.Theme=dn,e.View=Rr,e.WindowScrollController=sn,e.addDays=x,e.addDurations=function(e,t){return{years:e.years+t.years,months:e.months+t.months,days:e.days+t.days,milliseconds:e.milliseconds+t.milliseconds}},e.addMs=N,e.addWeeks=function(e,t){var n=j(e);return n[2]+=7*t,W(n)},e.allowContextMenu=function(e){e.removeEventListener("contextmenu",k)},e.allowSelection=function(e){e.classList.remove("fc-unselectable"),e.removeEventListener("selectstart",k)},e.appendToElement=s,e.applyAll=ue,e.applyMutationToEventStore=vt,e.applyStyle=m,e.applyStyleProp=y,e.asRoughMinutes=function(e){return te(e)/6e4},e.asRoughMs=te,e.asRoughSeconds=function(e){return te(e)/1e3},e.buildGotoAnchorHtml=Xt,e.buildSegCompareObj=Ir,e.capitaliseFirstLetter=le,e.combineEventUis=Lt,e.compareByFieldSpec=ae,e.compareByFieldSpecs=oe,e.compareNumbers=function(e,t){return e-t},e.compensateScroll=function(e,t){t.left&&m(e,{borderLeftWidth:1,marginLeft:t.left-1}),t.right&&m(e,{borderRightWidth:1,marginRight:t.right-1})},e.computeClippingRect=function(e){return I(e).map(function(e){return w(e)}).concat({left:window.pageXOffset,right:window.pageXOffset+document.documentElement.clientWidth,top:window.pageYOffset,bottom:window.pageYOffset+document.documentElement.clientHeight}).reduce(function(e,t){return E(e,t)||t})},e.computeEdges=T,e.computeFallbackHeaderFormat=_r,e.computeHeightAndMargins=R,e.computeInnerRect=w,e.computeRect=C,e.computeVisibleDayRange=ve,e.config={},e.constrainPoint=function(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}},e.createDuration=X,e.createElement=r,e.createEmptyEventStore=Ne,e.createEventInstance=Bt,e.createFormatter=it,e.createPlugin=fn,e.cssToStr=Pt,e.debounce=he,e.diffDates=me,e.diffDayAndTime=L,e.diffDays=_,e.diffPoints=function(e,t){return{left:e.left-t.left,top:e.top-t.top}},e.diffWeeks=function(e,t){return _(e,t)/7},e.diffWholeDays=U,e.diffWholeWeeks=z,e.disableCursor=function(){document.body.classList.add("fc-not-allowed")},e.distributeHeight=function(e,t,n){var r=Math.floor(t/e.length),i=Math.floor(t-r*(e.length-1)),o=[],a=[],s=[],l=0;re(e),e.forEach(function(t,n){var d=n===e.length-1?i:r,c=t.getBoundingClientRect().height,u=c+M(t);u<d?(o.push(t),a.push(u),s.push(c)):l+=u}),n&&(t-=l,r=Math.floor(t/o.length),i=Math.floor(t-r*(o.length-1))),o.forEach(function(e,t){var n=t===o.length-1?i:r,l=a[t],d=n-(l-s[t]);l<n&&(e.style.height=d+"px")})},e.elementClosest=h,e.elementMatches=f,e.enableCursor=function(){document.body.classList.remove("fc-not-allowed")},e.eventTupleToStore=Pe,e.filterEventStoreDefs=Le,e.filterHash=we,e.findChildren=function(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],i=0;i<n.length;i++)for(var o=n[i].children,a=0;a<o.length;a++){var s=o[a];t&&!f(s,t)||r.push(s)}return r},e.findElements=g,e.flexibleCompare=se,e.forceClassName=function(e,t,n){n?e.classList.add(t):e.classList.remove(t)},e.formatDate=function(e,t){void 0===t&&(t={});var n=Or(t),r=it(t),i=n.createMarkerMeta(e);return i?n.format(i.marker,r,{forcedTzo:i.forcedTzo}):""},e.formatIsoTimeString=function(e){return de(e.getUTCHours(),2)+":"+de(e.getUTCMinutes(),2)+":"+de(e.getUTCSeconds(),2)},e.formatRange=function(e,t,n){var r=Or("object"==typeof n&&n?n:{}),i=it(n,wn.defaultRangeSeparator),o=r.createMarkerMeta(e),a=r.createMarkerMeta(t);return o&&a?r.formatRange(o.marker,a.marker,i,{forcedStartTzo:o.forcedTzo,forcedEndTzo:a.forcedTzo,isEndExclusive:n.isEndExclusive}):""},e.getAllDayHtml=function(e){return e.opt("allDayHtml")||kt(e.opt("allDayText"))},e.getClippingParents=I,e.getDayClasses=Jt,e.getElSeg=ht,e.getRectCenter=function(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}},e.getRelevantEvents=Oe,e.globalDefaults=wn,e.greatestDurationDenominator=ne,e.hasBgRendering=function(e){return"background"===e.rendering||"inverse-background"===e.rendering},e.htmlEscape=kt,e.htmlToElement=i,e.insertAfterElement=function(e,t){for(var n=d(t),r=e.nextSibling||null,i=0;i<n.length;i++)e.parentNode.insertBefore(n[i],r)},e.interactionSettingsStore=vr,e.interactionSettingsToStore=function(e){var t;return(t={})[e.component.uid]=e,t},e.intersectRanges=Ae,e.intersectRects=E,e.isArraysEqual=je,e.isDateSpansEqual=function(e,t){return Ve(e.range,t.range)&&e.allDay===t.allDay&&function(e,t){for(var n in t)if("range"!==n&&"allDay"!==n&&e[n]!==t[n])return!1;for(var n in e)if(!(n in t))return!1;return!0}(e,t)},e.isInt=ce,e.isInteractionValid=Dt,e.isMultiDayRange=function(e){var t=ve(e);return _(t.start,t.end)>1},e.isPropsEqual=Ie,e.isPropsValid=wt,e.isSingleDay=function(e){return 0===e.years&&0===e.months&&1===e.days&&0===e.milliseconds},e.isValidDate=G,e.listenBySelector=P,e.mapHash=Ce,e.matchCellWidths=function(e){var t=0;return e.forEach(function(e){var n=e.firstChild;if(n instanceof HTMLElement){var r=n.getBoundingClientRect().width;r>t&&(t=r)}}),t++,e.forEach(function(e){e.style.width=t+"px"}),t},e.memoize=We,e.memoizeOutput=Ge,e.memoizeRendering=Gt,e.mergeEventStores=_e,e.multiplyDuration=function(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}},e.padStart=de,e.parseBusinessHours=Wt,e.parseDragMeta=function(e){var t={},n=fe(e,xr,Nr,t);return n.leftoverProps=t,n},e.parseEventDef=Ft,e.parseFieldSpecs=ie,e.parseMarker=zn,e.pointInsideRect=function(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom},e.prependToElement=l,e.preventContextMenu=function(e){e.addEventListener("contextmenu",k)},e.preventDefault=k,e.preventSelection=function(e){e.classList.add("fc-unselectable"),e.addEventListener("selectstart",k)},e.processScopedUiProps=Nt,e.rangeContainsMarker=Be,e.rangeContainsRange=Fe,e.rangesEqual=Ve,e.rangesIntersect=$e,e.refineProps=fe,e.removeElement=c,e.removeExact=function(e,t){for(var n=0,r=0;r<e.length;)e[r]===t?(e.splice(r,1),n++):r++;return n},e.renderDateCell=Lr,e.requestJson=yn,e.sliceEventStore=ct,e.startOfDay=A,e.subtractInnerElHeight=function(e,t){var n={position:"relative",left:-1};m(e,n),m(t,n);var r=e.getBoundingClientRect().height-t.getBoundingClientRect().height,i={position:"",left:""};return m(e,i),m(t,i),r},e.translateRect=function(e,t,n){return{left:e.left+t,right:e.right+t,top:e.top+n,bottom:e.bottom+n}},e.uncompensateScroll=function(e){m(e,{marginLeft:"",marginRight:"",borderLeftWidth:"",borderRightWidth:""})},e.undistributeHeight=re,e.unpromisify=Kt,e.version="4.3.1",e.whenTransitionDone=function(e,t){var n=function(r){t(r),H.forEach(function(t){e.removeEventListener(t,n)})};H.forEach(function(t){e.addEventListener(t,n)})},e.wholeDivideDurations=function(e,t){for(var n=null,r=0;r<q.length;r++){var i=q[r];if(t[i]){var o=e[i]/t[i];if(!ce(o)||null!==n&&n!==o)return null;n=o}else if(e[i])return null}return n},Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","@fullcalendar/core"],t):t((e=e||self).FullCalendarDayGrid={},e.FullCalendar)}(this,function(e,t){"use strict";var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function r(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},o=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.buildRenderRange=function(n,r,i){var o,a=this.dateEnv,s=e.prototype.buildRenderRange.call(this,n,r,i),l=s.start,d=s.end;if(/^(year|month)$/.test(r)&&(l=a.startOfWeek(l),(o=a.startOfWeek(d)).valueOf()!==d.valueOf()&&(d=t.addWeeks(o,1))),this.options.monthMode&&this.options.fixedWeekCount){var c=Math.ceil(t.diffWeeks(l,d));d=t.addWeeks(d,6-c)}return{start:l,end:d}},n}(t.DateProfileGenerator),a=function(){function e(e){var t=this;this.isHidden=!0,this.margin=10,this.documentMousedown=function(e){t.el&&!t.el.contains(e.target)&&t.hide()},this.options=e}return e.prototype.show=function(){this.isHidden&&(this.el||this.render(),this.el.style.display="",this.position(),this.isHidden=!1,this.trigger("show"))},e.prototype.hide=function(){this.isHidden||(this.el.style.display="none",this.isHidden=!0,this.trigger("hide"))},e.prototype.render=function(){var e=this,n=this.options,r=this.el=t.createElement("div",{className:"fc-popover "+(n.className||""),style:{top:"0",left:"0"}});"function"==typeof n.content&&n.content(r),n.parentEl.appendChild(r),t.listenBySelector(r,"click",".fc-close",function(t){e.hide()}),n.autoHide&&document.addEventListener("mousedown",this.documentMousedown)},e.prototype.destroy=function(){this.hide(),this.el&&(t.removeElement(this.el),this.el=null),document.removeEventListener("mousedown",this.documentMousedown)},e.prototype.position=function(){var e,n,r=this.options,i=this.el,o=i.getBoundingClientRect(),a=t.computeRect(i.offsetParent),s=t.computeClippingRect(r.parentEl);e=r.top||0,n=void 0!==r.left?r.left:void 0!==r.right?r.right-o.width:0,e=Math.min(e,s.bottom-o.height-this.margin),e=Math.max(e,s.top+this.margin),n=Math.min(n,s.right-o.width-this.margin),n=Math.max(n,s.left+this.margin),null!=a&&null!=a&&null!=i&&null!=i&&t.applyStyle(i,{top:e-a.top,left:n-a.left})},e.prototype.trigger=function(e){this.options[e]&&this.options[e].apply(this,Array.prototype.slice.call(arguments,1))},e}(),s=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.renderSegHtml=function(e,n){var r,i,o=this.context,a=o.view,s=o.options,l=e.eventRange,d=l.def,c=l.ui,u=d.allDay,p=a.computeEventDraggable(d,c),h=u&&e.isStart&&a.computeEventStartResizable(d,c),f=u&&e.isEnd&&a.computeEventEndResizable(d,c),g=this.getSegClasses(e,p,h||f,n),v=t.cssToStr(this.getSkinCss(c)),m="";return g.unshift("fc-day-grid-event","fc-h-event"),e.isStart&&(r=this.getTimeText(l))&&(m='<span class="fc-time">'+t.htmlEscape(r)+"</span>"),i='<span class="fc-title">'+(t.htmlEscape(d.title||"")||"&nbsp;")+"</span>",'<a class="'+g.join(" ")+'"'+(d.url?' href="'+t.htmlEscape(d.url)+'"':"")+(v?' style="'+v+'"':"")+'><div class="fc-content '+d.groupId+'" id="'+d.groupId+'">'+("rtl"===s.dir?i+" "+m:m+" "+i)+"</div>"+(h?'<div class="fc-resizer fc-start-resizer"></div>':"")+(f?'<div class="fc-resizer fc-end-resizer"></div>':"")+"</a>"},n.prototype.computeEventTimeFormat=function(){return{hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"}},n.prototype.computeDisplayEventEnd=function(){return!1},n}(t.FgEventRenderer),l=function(e){function n(t){var n=e.call(this,t.context)||this;return n.dayGrid=t,n}return r(n,e),n.prototype.attachSegs=function(e,t){var n=this.rowStructs=this.renderSegRows(e);this.dayGrid.rowEls.forEach(function(e,t){e.querySelector(".fc-content-skeleton > table").appendChild(n[t].tbodyEl)}),t||this.dayGrid.removeSegPopover()},n.prototype.detachSegs=function(){for(var e,n=this.rowStructs||[];e=n.pop();)t.removeElement(e.tbodyEl);this.rowStructs=null},n.prototype.renderSegRows=function(e){var t,n,r=[];for(t=this.groupSegRows(e),n=0;n<t.length;n++)r.push(this.renderSegRow(n,t[n]));return r},n.prototype.renderSegRow=function(e,n){var r,i,o,a,s,l,d,c=this.dayGrid,u=c.colCnt,p=c.isRtl,h=this.buildSegLevels(n),f=Math.max(1,h.length),g=document.createElement("tbody"),v=[],m=[],y=[];function E(e){for(;o<e;)(d=(y[r-1]||[])[o])?d.rowSpan=(d.rowSpan||1)+1:(d=document.createElement("td"),a.appendChild(d)),m[r][o]=d,y[r][o]=d,o++}for(r=0;r<f;r++){if(i=h[r],o=0,a=document.createElement("tr"),v.push([]),m.push([]),y.push([]),i)for(s=0;s<i.length;s++){l=i[s];var S=p?u-1-l.lastCol:l.firstCol,b=p?u-1-l.firstCol:l.lastCol;for(E(S),d=t.createElement("td",{className:"fc-event-container"},l.el),S!==b?d.colSpan=b-S+1:y[r][o]=d;o<=b;)m[r][o]=d,v[r][o]=l,o++;a.appendChild(d)}E(u);var D=c.renderProps.renderIntroHtml();D&&(c.isRtl?t.appendToElement(a,D):t.prependToElement(a,D)),g.appendChild(a)}return{row:e,tbodyEl:g,cellMatrix:m,segMatrix:v,segLevels:h,segs:n}},n.prototype.buildSegLevels=function(e){var t,n,r,i=this.dayGrid,o=i.isRtl,a=i.colCnt,s=[];for(e=this.sortEventSegs(e),t=0;t<e.length;t++){for(n=e[t],r=0;r<s.length&&d(n,s[r]);r++);n.level=r,n.leftCol=o?a-1-n.lastCol:n.firstCol,n.rightCol=o?a-1-n.firstCol:n.lastCol,(s[r]||(s[r]=[])).push(n)}for(r=0;r<s.length;r++)s[r].sort(c);return s},n.prototype.groupSegRows=function(e){var t,n=[];for(t=0;t<this.dayGrid.rowCnt;t++)n.push([]);for(t=0;t<e.length;t++)n[e[t].row].push(e[t]);return n},n.prototype.computeDisplayEventEnd=function(){return 1===this.dayGrid.colCnt},n}(s);function d(e,t){var n,r;for(n=0;n<t.length;n++)if((r=t[n]).firstCol<=e.lastCol&&r.lastCol>=e.firstCol)return!0;return!1}function c(e,t){return e.leftCol-t.leftCol}var u=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.attachSegs=function(e,n){var r=n.sourceSeg,i=this.rowStructs=this.renderSegRows(e);this.dayGrid.rowEls.forEach(function(e,n){var o,a,s=t.htmlToElement('<div class="fc-mirror-skeleton"><table></table></div>');r&&r.row===n?o=r.el:(o=e.querySelector(".fc-content-skeleton tbody"))||(o=e.querySelector(".fc-content-skeleton table")),a=o.getBoundingClientRect().top-e.getBoundingClientRect().top,s.style.top=a+"px",s.querySelector("table").appendChild(i[n].tbodyEl),e.appendChild(s)})},n}(l),p=function(e){function n(t){var n=e.call(this,t.context)||this;return n.fillSegTag="td",n.dayGrid=t,n}return r(n,e),n.prototype.renderSegs=function(t,n){"bgEvent"===t&&(n=n.filter(function(e){return e.eventRange.def.allDay})),e.prototype.renderSegs.call(this,t,n)},n.prototype.attachSegs=function(e,t){var n,r,i,o=[];for(n=0;n<t.length;n++)r=t[n],i=this.renderFillRow(e,r),this.dayGrid.rowEls[r.row].appendChild(i),o.push(i);return o},n.prototype.renderFillRow=function(e,n){var r,i,o,a=this.dayGrid,s=a.colCnt,l=a.isRtl,d=l?s-1-n.lastCol:n.firstCol,c=(l?s-1-n.firstCol:n.lastCol)+1;r="businessHours"===e?"bgevent":e.toLowerCase(),o=(i=t.htmlToElement('<div class="fc-'+r+'-skeleton"><table><tr></tr></table></div>')).getElementsByTagName("tr")[0],d>0&&t.appendToElement(o,new Array(d+1).join('<td style="pointer-events:none"></td>')),n.el.colSpan=c-d,o.appendChild(n.el),c<s&&t.appendToElement(o,new Array(s-c+1).join('<td style="pointer-events:none"></td>'));var u=a.renderProps.renderIntroHtml();return u&&(a.isRtl?t.appendToElement(o,u):t.prependToElement(o,u)),i},n}(t.FillRenderer),h=function(e){function n(n,r){var i=e.call(this,n,r)||this,o=i.eventRenderer=new f(i),a=i.renderFrame=t.memoizeRendering(i._renderFrame);return i.renderFgEvents=t.memoizeRendering(o.renderSegs.bind(o),o.unrender.bind(o),[a]),i.renderEventSelection=t.memoizeRendering(o.selectByInstanceId.bind(o),o.unselectByInstanceId.bind(o),[i.renderFgEvents]),i.renderEventDrag=t.memoizeRendering(o.hideByHash.bind(o),o.showByHash.bind(o),[a]),i.renderEventResize=t.memoizeRendering(o.hideByHash.bind(o),o.showByHash.bind(o),[a]),n.calendar.registerInteractiveComponent(i,{el:i.el,useEventCenter:!1}),i}return r(n,e),n.prototype.render=function(e){this.renderFrame(e.date),this.renderFgEvents(e.fgSegs),this.renderEventSelection(e.eventSelection),this.renderEventDrag(e.eventDragInstances),this.renderEventResize(e.eventResizeInstances)},n.prototype.destroy=function(){e.prototype.destroy.call(this),this.renderFrame.unrender(),this.calendar.unregisterInteractiveComponent(this)},n.prototype._renderFrame=function(e){var n=this.theme,r=this.dateEnv.format(e,t.createFormatter(this.opt("dayPopoverFormat")));this.el.innerHTML='<div class="fc-header '+n.getClass("popoverHeader")+'"><span class="fc-title">'+t.htmlEscape(r)+'</span><span class="fc-close '+n.getIconClass("close")+'"></span></div><div class="fc-body '+n.getClass("popoverContent")+'"><div class="fc-event-container"></div></div>',this.segContainerEl=this.el.querySelector(".fc-event-container")},n.prototype.queryHit=function(e,n,r,i){var o=this.props.date;if(e<r&&n<i)return{component:this,dateSpan:{allDay:!0,range:{start:o,end:t.addDays(o,1)}},dayEl:this.el,rect:{left:0,top:0,right:r,bottom:i},layer:1}},n}(t.DateComponent),f=function(e){function n(t){var n=e.call(this,t.context)||this;return n.dayTile=t,n}return r(n,e),n.prototype.attachSegs=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.dayTile.segContainerEl.appendChild(r.el)}},n.prototype.detachSegs=function(e){for(var n=0,r=e;n<r.length;n++){var i=r[n];t.removeElement(i.el)}},n}(s),g=function(){function e(e){this.context=e}return e.prototype.renderHtml=function(e){var t=[];e.renderIntroHtml&&t.push(e.renderIntroHtml());for(var n=0,r=e.cells;n<r.length;n++){var i=r[n];t.push(v(i.date,e.dateProfile,this.context,i.htmlAttrs))}return e.cells.length||t.push('<td class="fc-day '+this.context.theme.getClass("widgetContent")+'"></td>'),"rtl"===this.context.options.dir&&t.reverse(),"<tr>"+t.join("")+"</tr>"},e}();function v(e,n,r,i){var o=r.dateEnv,a=r.theme,s=t.rangeContainsMarker(n.activeRange,e),l=t.getDayClasses(e,n,r);return l.unshift("fc-day",a.getClass("widgetContent")),'<td class="'+l.join(" ")+'"'+(s?' data-date="'+o.formatIso(e,{omitTime:!0})+'"':"")+(i?" "+i:"")+"></td>"}var m=t.createFormatter({day:"numeric"}),y=t.createFormatter({week:"numeric"}),E=function(e){function n(n,r,i){var o=e.call(this,n,r)||this;o.bottomCoordPadding=0,o.isCellSizesDirty=!1;var a=o.eventRenderer=new l(o),s=o.fillRenderer=new p(o);o.mirrorRenderer=new u(o);var d=o.renderCells=t.memoizeRendering(o._renderCells,o._unrenderCells);return o.renderBusinessHours=t.memoizeRendering(s.renderSegs.bind(s,"businessHours"),s.unrender.bind(s,"businessHours"),[d]),o.renderDateSelection=t.memoizeRendering(s.renderSegs.bind(s,"highlight"),s.unrender.bind(s,"highlight"),[d]),o.renderBgEvents=t.memoizeRendering(s.renderSegs.bind(s,"bgEvent"),s.unrender.bind(s,"bgEvent"),[d]),o.renderFgEvents=t.memoizeRendering(a.renderSegs.bind(a),a.unrender.bind(a),[d]),o.renderEventSelection=t.memoizeRendering(a.selectByInstanceId.bind(a),a.unselectByInstanceId.bind(a),[o.renderFgEvents]),o.renderEventDrag=t.memoizeRendering(o._renderEventDrag,o._unrenderEventDrag,[d]),o.renderEventResize=t.memoizeRendering(o._renderEventResize,o._unrenderEventResize,[d]),o.renderProps=i,o}return r(n,e),n.prototype.render=function(e){var t=e.cells;this.rowCnt=t.length,this.colCnt=t[0].length,this.renderCells(t,e.isRigid),this.renderBusinessHours(e.businessHourSegs),this.renderDateSelection(e.dateSelectionSegs),this.renderBgEvents(e.bgEventSegs),this.renderFgEvents(e.fgEventSegs),this.renderEventSelection(e.eventSelection),this.renderEventDrag(e.eventDrag),this.renderEventResize(e.eventResize),this.segPopoverTile&&this.updateSegPopoverTile()},n.prototype.destroy=function(){e.prototype.destroy.call(this),this.renderCells.unrender()},n.prototype.getCellRange=function(e,n){var r=this.props.cells[e][n].date;return{start:r,end:t.addDays(r,1)}},n.prototype.updateSegPopoverTile=function(e,t){var n=this.props;this.segPopoverTile.receiveProps({date:e||this.segPopoverTile.props.date,fgSegs:t||this.segPopoverTile.props.fgSegs,eventSelection:n.eventSelection,eventDragInstances:n.eventDrag?n.eventDrag.affectedInstances:null,eventResizeInstances:n.eventResize?n.eventResize.affectedInstances:null})},n.prototype._renderCells=function(e,n){var r,i,o=this.view,a=this.dateEnv,s=this.rowCnt,l=this.colCnt,d="";for(r=0;r<s;r++)d+=this.renderDayRowHtml(r,n);for(this.el.innerHTML=d,this.rowEls=t.findElements(this.el,".fc-row"),this.cellEls=t.findElements(this.el,".fc-day, .fc-disabled-day"),this.isRtl&&this.cellEls.reverse(),this.rowPositions=new t.PositionCache(this.el,this.rowEls,!1,!0),this.colPositions=new t.PositionCache(this.el,this.cellEls.slice(0,l),!0,!1),r=0;r<s;r++)for(i=0;i<l;i++)this.publiclyTrigger("dayRender",[{date:a.toDate(e[r][i].date),el:this.getCellEl(r,i),view:o}]);this.isCellSizesDirty=!0},n.prototype._unrenderCells=function(){this.removeSegPopover()},n.prototype.renderDayRowHtml=function(e,t){var n=this.theme,r=["fc-row","fc-week",n.getClass("dayRow")];t&&r.push("fc-rigid");var i=new g(this.context);return'<div class="'+r.join(" ")+'"><div class="fc-bg"><table class="'+n.getClass("tableGrid")+'">'+i.renderHtml({cells:this.props.cells[e],dateProfile:this.props.dateProfile,renderIntroHtml:this.renderProps.renderBgIntroHtml})+'</table></div><div class="fc-content-skeleton"><table>'+(this.getIsNumbersVisible()?"<thead>"+this.renderNumberTrHtml(e)+"</thead>":"")+"</table></div></div>"},n.prototype.getIsNumbersVisible=function(){return this.getIsDayNumbersVisible()||this.renderProps.cellWeekNumbersVisible||this.renderProps.colWeekNumbersVisible},n.prototype.getIsDayNumbersVisible=function(){return this.rowCnt>1},n.prototype.renderNumberTrHtml=function(e){var t=this.renderProps.renderNumberIntroHtml(e,this);return"<tr>"+(this.isRtl?"":t)+this.renderNumberCellsHtml(e)+(this.isRtl?t:"")+"</tr>"},n.prototype.renderNumberCellsHtml=function(e){var t,n,r=[];for(t=0;t<this.colCnt;t++)n=this.props.cells[e][t].date,r.push(this.renderNumberCellHtml(n));return this.isRtl&&r.reverse(),r.join("")},n.prototype.renderNumberCellHtml=function(e){var n,r,i=this.view,o=this.dateEnv,a="",s=t.rangeContainsMarker(this.props.dateProfile.activeRange,e),l=this.getIsDayNumbersVisible()&&s;return l||this.renderProps.cellWeekNumbersVisible?((n=t.getDayClasses(e,this.props.dateProfile,this.context)).unshift("fc-day-top"),this.renderProps.cellWeekNumbersVisible&&(r=o.weekDow),a+='<td class="'+n.join(" ")+'"'+(s?' data-date="'+o.formatIso(e,{omitTime:!0})+'"':"")+">",this.renderProps.cellWeekNumbersVisible&&e.getUTCDay()===r&&(a+=t.buildGotoAnchorHtml(i,{date:e,type:"week"},{class:"fc-week-number"},o.format(e,y))),l&&(a+=t.buildGotoAnchorHtml(i,e,{class:"fc-day-number"},o.format(e,m))),a+="</td>"):"<td></td>"},n.prototype.updateSize=function(e){var t=this.fillRenderer,n=this.eventRenderer,r=this.mirrorRenderer;(e||this.isCellSizesDirty||this.view.calendar.isEventsUpdated)&&(this.buildPositionCaches(),this.isCellSizesDirty=!1),t.computeSizes(e),n.computeSizes(e),r.computeSizes(e),t.assignSizes(e),n.assignSizes(e),r.assignSizes(e)},n.prototype.buildPositionCaches=function(){this.buildColPositions(),this.buildRowPositions()},n.prototype.buildColPositions=function(){this.colPositions.build()},n.prototype.buildRowPositions=function(){this.rowPositions.build(),this.rowPositions.bottoms[this.rowCnt-1]+=this.bottomCoordPadding},n.prototype.positionToHit=function(e,t){var n=this.colPositions,r=this.rowPositions,i=n.leftToIndex(e),o=r.topToIndex(t);if(null!=o&&null!=i)return{row:o,col:i,dateSpan:{range:this.getCellRange(o,i),allDay:!0},dayEl:this.getCellEl(o,i),relativeRect:{left:n.lefts[i],right:n.rights[i],top:r.tops[o],bottom:r.bottoms[o]}}},n.prototype.getCellEl=function(e,t){return this.cellEls[e*this.colCnt+t]},n.prototype._renderEventDrag=function(e){e&&(this.eventRenderer.hideByHash(e.affectedInstances),this.fillRenderer.renderSegs("highlight",e.segs))},n.prototype._unrenderEventDrag=function(e){e&&(this.eventRenderer.showByHash(e.affectedInstances),this.fillRenderer.unrender("highlight"))},n.prototype._renderEventResize=function(e){e&&(this.eventRenderer.hideByHash(e.affectedInstances),this.fillRenderer.renderSegs("highlight",e.segs),this.mirrorRenderer.renderSegs(e.segs,{isResizing:!0,sourceSeg:e.sourceSeg}))},n.prototype._unrenderEventResize=function(e){e&&(this.eventRenderer.showByHash(e.affectedInstances),this.fillRenderer.unrender("highlight"),this.mirrorRenderer.unrender(e.segs,{isResizing:!0,sourceSeg:e.sourceSeg}))},n.prototype.removeSegPopover=function(){this.segPopover&&this.segPopover.hide()},n.prototype.limitRows=function(e){var t,n,r=this.eventRenderer.rowStructs||[];for(t=0;t<r.length;t++)this.unlimitRow(t),!1!==(n=!!e&&("number"==typeof e?e:this.computeRowLevelLimit(t)))&&this.limitRow(t,n)},n.prototype.computeRowLevelLimit=function(e){var n,r,i=this.rowEls[e].getBoundingClientRect().bottom,o=t.findChildren(this.eventRenderer.rowStructs[e].tbodyEl);for(n=0;n<o.length;n++)if((r=o[n]).classList.remove("fc-limited"),r.getBoundingClientRect().bottom>i)return n;return!1},n.prototype.limitRow=function(e,n){var r,i,o,a,s,l,d,c,u,p,h,f,g,v,m,y=this,E=this.colCnt,S=this.isRtl,b=this.eventRenderer.rowStructs[e],D=[],T=0,w=function(r){for(;T<r;)(l=y.getCellSegs(e,T,n)).length&&(u=i[n-1][T],m=y.renderMoreLink(e,T,l),v=t.createElement("div",null,m),u.appendChild(v),D.push(v)),T++};if(n&&n<b.segLevels.length){for(r=b.segLevels[n-1],i=b.cellMatrix,(o=t.findChildren(b.tbodyEl).slice(n)).forEach(function(e){e.classList.add("fc-limited")}),a=0;a<r.length;a++){s=r[a];var C=S?E-1-s.lastCol:s.firstCol,R=S?E-1-s.firstCol:s.lastCol;for(w(C),c=[],d=0;T<=R;)l=this.getCellSegs(e,T,n),c.push(l),d+=l.length,T++;if(d){for(p=(u=i[n-1][C]).rowSpan||1,h=[],f=0;f<c.length;f++)g=t.createElement("td",{className:"fc-more-cell",rowSpan:p}),l=c[f],m=this.renderMoreLink(e,C+f,[s].concat(l)),v=t.createElement("div",null,m),g.appendChild(v),h.push(g),D.push(g);u.classList.add("fc-limited"),t.insertAfterElement(u,h),o.push(u)}}w(this.colCnt),b.moreEls=D,b.limitedEls=o}},n.prototype.unlimitRow=function(e){var n=this.eventRenderer.rowStructs[e];n.moreEls&&(n.moreEls.forEach(t.removeElement),n.moreEls=null),n.limitedEls&&(n.limitedEls.forEach(function(e){e.classList.remove("fc-limited")}),n.limitedEls=null)},n.prototype.renderMoreLink=function(e,n,r){var i=this,o=this.view,a=this.dateEnv,s=t.createElement("a",{className:"fc-more"});return s.innerText=this.getMoreLinkText(r.length),s.addEventListener("click",function(t){var s=i.opt("eventLimitClick"),l=i.isRtl?i.colCnt-n-1:n,d=i.props.cells[e][l].date,c=t.currentTarget,u=i.getCellEl(e,n),p=i.getCellSegs(e,n),h=i.resliceDaySegs(p,d),f=i.resliceDaySegs(r,d);"function"==typeof s&&(s=i.publiclyTrigger("eventLimitClick",[{date:a.toDate(d),allDay:!0,dayEl:u,moreEl:c,segs:h,hiddenSegs:f,jsEvent:t,view:o}])),"popover"===s?i.showSegPopover(e,n,c,h):"string"==typeof s&&o.calendar.zoomTo(d,s)}),s},n.prototype.showSegPopover=function(e,n,r,i){var o,s,l=this,d=this.calendar,c=this.view,u=this.theme,p=this.isRtl?this.colCnt-n-1:n,f=r.parentNode;o=1===this.rowCnt?c.el:this.rowEls[e],s={className:"fc-more-popover "+u.getClass("popover"),parentEl:c.el,top:t.computeRect(o).top,autoHide:!0,content:function(t){l.segPopoverTile=new h(l.context,t),l.updateSegPopoverTile(l.props.cells[e][p].date,i)},hide:function(){l.segPopoverTile.destroy(),l.segPopoverTile=null,l.segPopover.destroy(),l.segPopover=null}},this.isRtl?s.right=t.computeRect(f).right+1:s.left=t.computeRect(f).left-1,this.segPopover=new a(s),this.segPopover.show(),d.releaseAfterSizingTriggers()},n.prototype.resliceDaySegs=function(e,n){for(var r=n,o={start:r,end:t.addDays(r,1)},a=[],s=0,l=e;s<l.length;s++){var d=l[s],c=d.eventRange,u=c.range,p=t.intersectRanges(u,o);p&&a.push(i({},d,{eventRange:{def:c.def,ui:i({},c.ui,{durationEditable:!1}),instance:c.instance,range:p},isStart:d.isStart&&p.start.valueOf()===u.start.valueOf(),isEnd:d.isEnd&&p.end.valueOf()===u.end.valueOf()}))}return a},n.prototype.getMoreLinkText=function(e){var t=this.opt("eventLimitText");return"function"==typeof t?t(e):"+"+e+" "+t},n.prototype.getCellSegs=function(e,t,n){for(var r,i=this.eventRenderer.rowStructs[e].segMatrix,o=n||0,a=[];o<i.length;)(r=i[o][t])&&a.push(r),o++;return a},n}(t.DateComponent),S=t.createFormatter({week:"numeric"}),b=function(e){function n(n,r,i,o){var a=e.call(this,n,r,i,o)||this;a.renderHeadIntroHtml=function(){var e=a.theme;return a.colWeekNumbersVisible?'<th class="fc-week-number '+e.getClass("widgetHeader")+'" '+a.weekNumberStyleAttr()+"><span>"+t.htmlEscape(a.opt("weekLabel"))+"</span></th>":""},a.renderDayGridNumberIntroHtml=function(e,n){var r=a.dateEnv,i=n.props.cells[e][0].date;return a.colWeekNumbersVisible?'<td class="fc-week-number" '+a.weekNumberStyleAttr()+">"+t.buildGotoAnchorHtml(a,{date:i,type:"week",forceOff:1===n.colCnt},r.format(i,S))+"</td>":""},a.renderDayGridBgIntroHtml=function(){var e=a.theme;return a.colWeekNumbersVisible?'<td class="fc-week-number '+e.getClass("widgetContent")+'" '+a.weekNumberStyleAttr()+"></td>":""},a.renderDayGridIntroHtml=function(){return a.colWeekNumbersVisible?'<td class="fc-week-number" '+a.weekNumberStyleAttr()+"></td>":""},a.el.classList.add("fc-dayGrid-view"),a.el.innerHTML=a.renderSkeletonHtml(),a.scroller=new t.ScrollComponent("hidden","auto");var s=a.scroller.el;a.el.querySelector(".fc-body > tr > td").appendChild(s),s.classList.add("fc-day-grid-container");var l,d=t.createElement("div",{className:"fc-day-grid"});return s.appendChild(d),a.opt("weekNumbers")?a.opt("weekNumbersWithinDays")?(l=!0,a.colWeekNumbersVisible=!1):(l=!1,a.colWeekNumbersVisible=!0):(a.colWeekNumbersVisible=!1,l=!1),a.dayGrid=new E(a.context,d,{renderNumberIntroHtml:a.renderDayGridNumberIntroHtml,renderBgIntroHtml:a.renderDayGridBgIntroHtml,renderIntroHtml:a.renderDayGridIntroHtml,colWeekNumbersVisible:a.colWeekNumbersVisible,cellWeekNumbersVisible:l}),a}return r(n,e),n.prototype.destroy=function(){e.prototype.destroy.call(this),this.dayGrid.destroy(),this.scroller.destroy()},n.prototype.renderSkeletonHtml=function(){var e=this.theme;return'<table class="'+e.getClass("tableGrid")+'">'+(this.opt("columnHeader")?'<thead class="fc-head"><tr><td class="fc-head-container '+e.getClass("widgetHeader")+'">&nbsp;</td></tr></thead>':"")+'<tbody class="fc-body"><tr><td class="'+e.getClass("widgetContent")+'"></td></tr></tbody></table>'},n.prototype.weekNumberStyleAttr=function(){return null!=this.weekNumberWidth?'style="width:'+this.weekNumberWidth+'px"':""},n.prototype.hasRigidRows=function(){var e=this.opt("eventLimit");return e&&"number"!=typeof e},n.prototype.updateSize=function(t,n,r){e.prototype.updateSize.call(this,t,n,r),this.dayGrid.updateSize(t)},n.prototype.updateBaseSize=function(e,n,r){var i,o,a=this.dayGrid,s=this.opt("eventLimit"),l=this.header?this.header.el:null;a.rowEls?(this.colWeekNumbersVisible&&(this.weekNumberWidth=t.matchCellWidths(t.findElements(this.el,".fc-week-number"))),this.scroller.clear(),l&&t.uncompensateScroll(l),a.removeSegPopover(),s&&"number"==typeof s&&a.limitRows(s),i=this.computeScrollerHeight(n),this.setGridHeight(i,r),s&&"number"!=typeof s&&a.limitRows(s),r||(this.scroller.setHeight(i),((o=this.scroller.getScrollbarWidths()).left||o.right)&&(l&&t.compensateScroll(l,o),i=this.computeScrollerHeight(n),this.scroller.setHeight(i)),this.scroller.lockOverflow(o))):r||(i=this.computeScrollerHeight(n),this.scroller.setHeight(i))},n.prototype.computeScrollerHeight=function(e){return e-t.subtractInnerElHeight(this.el,this.scroller.el)},n.prototype.setGridHeight=function(e,n){this.opt("monthMode")?(n&&(e*=this.dayGrid.rowCnt/6),t.distributeHeight(this.dayGrid.rowEls,e,!n)):n?t.undistributeHeight(this.dayGrid.rowEls):t.distributeHeight(this.dayGrid.rowEls,e,!0)},n.prototype.computeDateScroll=function(e){return{top:0}},n.prototype.queryDateScroll=function(){return{top:this.scroller.getScrollTop()}},n.prototype.applyDateScroll=function(e){void 0!==e.top&&this.scroller.setScrollTop(e.top)},n}(t.View);b.prototype.dateProfileGeneratorClass=o;var D=function(e){function t(t,n){var r=e.call(this,t,n.el)||this;return r.slicer=new T,r.dayGrid=n,t.calendar.registerInteractiveComponent(r,{el:r.dayGrid.el}),r}return r(t,e),t.prototype.destroy=function(){e.prototype.destroy.call(this),this.calendar.unregisterInteractiveComponent(this)},t.prototype.render=function(e){var t=this.dayGrid,n=e.dateProfile,r=e.dayTable;t.receiveProps(i({},this.slicer.sliceProps(e,n,e.nextDayThreshold,t,r),{dateProfile:n,cells:r.cells,isRigid:e.isRigid}))},t.prototype.buildPositionCaches=function(){this.dayGrid.buildPositionCaches()},t.prototype.queryHit=function(e,t){var n=this.dayGrid.positionToHit(e,t);if(n)return{component:this.dayGrid,dateSpan:n.dateSpan,dayEl:n.dayEl,rect:{left:n.relativeRect.left,right:n.relativeRect.right,top:n.relativeRect.top,bottom:n.relativeRect.bottom},layer:0}},t}(t.DateComponent),T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.sliceRange=function(e,t){return t.sliceRange(e)},t}(t.Slicer),w=function(e){function n(n,r,i,o){var a=e.call(this,n,r,i,o)||this;return a.buildDayTable=t.memoize(C),a.opt("columnHeader")&&(a.header=new t.DayHeader(a.context,a.el.querySelector(".fc-head-container"))),a.simpleDayGrid=new D(a.context,a.dayGrid),a}return r(n,e),n.prototype.destroy=function(){e.prototype.destroy.call(this),this.header&&this.header.destroy(),this.simpleDayGrid.destroy()},n.prototype.render=function(t){e.prototype.render.call(this,t);var n=this.props.dateProfile,r=this.dayTable=this.buildDayTable(n,this.dateProfileGenerator);this.header&&this.header.receiveProps({dateProfile:n,dates:r.headerDates,datesRepDistinctDays:1===r.rowCnt,renderIntroHtml:this.renderHeadIntroHtml}),this.simpleDayGrid.receiveProps({dateProfile:n,dayTable:r,businessHours:t.businessHours,dateSelection:t.dateSelection,eventStore:t.eventStore,eventUiBases:t.eventUiBases,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,isRigid:this.hasRigidRows(),nextDayThreshold:this.nextDayThreshold})},n}(b);function C(e,n){var r=new t.DaySeries(e.renderRange,n);return new t.DayTable(r,/year|month|week/.test(e.currentRangeUnit))}var R=t.createPlugin({defaultView:"dayGridMonth",views:{dayGrid:w,dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},monthMode:!0,fixedWeekCount:!0}}});e.AbstractDayGridView=b,e.DayBgRow=g,e.DayGrid=E,e.DayGridSlicer=T,e.DayGridView=w,e.SimpleDayGrid=D,e.buildBasicDayTable=C,e.default=R,Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","@fullcalendar/core"],t):t((e=e||self).FullCalendarInteraction={},e.FullCalendar)}(this,function(e,t){"use strict";var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function r(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};t.config.touchMouseIgnoreWait=500;var o=0,a=0,s=!1,l=function(){function e(e){var n=this;this.subjectEl=null,this.downEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=function(e){if(!n.shouldIgnoreMouse()&&function(e){return 0===e.button&&!e.ctrlKey}(e)&&n.tryStart(e)){var t=n.createEventFromMouse(e,!0);n.emitter.trigger("pointerdown",t),n.initScrollWatch(t),n.shouldIgnoreMove||document.addEventListener("mousemove",n.handleMouseMove),document.addEventListener("mouseup",n.handleMouseUp)}},this.handleMouseMove=function(e){var t=n.createEventFromMouse(e);n.recordCoords(t),n.emitter.trigger("pointermove",t)},this.handleMouseUp=function(e){document.removeEventListener("mousemove",n.handleMouseMove),document.removeEventListener("mouseup",n.handleMouseUp),n.emitter.trigger("pointerup",n.createEventFromMouse(e)),n.cleanup()},this.handleTouchStart=function(e){if(n.tryStart(e)){n.isTouchDragging=!0;var t=n.createEventFromTouch(e,!0);n.emitter.trigger("pointerdown",t),n.initScrollWatch(t);var r=e.target;n.shouldIgnoreMove||r.addEventListener("touchmove",n.handleTouchMove),r.addEventListener("touchend",n.handleTouchEnd),r.addEventListener("touchcancel",n.handleTouchEnd),window.addEventListener("scroll",n.handleTouchScroll,!0)}},this.handleTouchMove=function(e){var t=n.createEventFromTouch(e);n.recordCoords(t),n.emitter.trigger("pointermove",t)},this.handleTouchEnd=function(e){if(n.isDragging){var r=e.target;r.removeEventListener("touchmove",n.handleTouchMove),r.removeEventListener("touchend",n.handleTouchEnd),r.removeEventListener("touchcancel",n.handleTouchEnd),window.removeEventListener("scroll",n.handleTouchScroll,!0),n.emitter.trigger("pointerup",n.createEventFromTouch(e)),n.cleanup(),n.isTouchDragging=!1,o++,setTimeout(function(){o--},t.config.touchMouseIgnoreWait)}},this.handleTouchScroll=function(){n.wasTouchScroll=!0},this.handleScroll=function(e){if(!n.shouldIgnoreMove){var t=window.pageXOffset-n.prevScrollX+n.prevPageX,r=window.pageYOffset-n.prevScrollY+n.prevPageY;n.emitter.trigger("pointermove",{origEvent:e,isTouch:n.isTouchDragging,subjectEl:n.subjectEl,pageX:t,pageY:r,deltaX:t-n.origPageX,deltaY:r-n.origPageY})}},this.containerEl=e,this.emitter=new t.EmitterMixin,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),a++||window.addEventListener("touchmove",d,{passive:!1})}return e.prototype.destroy=function(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),--a||window.removeEventListener("touchmove",d,{passive:!1})},e.prototype.tryStart=function(e){var n=this.querySubjectEl(e),r=e.target;return!(!n||this.handleSelector&&!t.elementClosest(r,this.handleSelector))&&(this.subjectEl=n,this.downEl=r,this.isDragging=!0,this.wasTouchScroll=!1,!0)},e.prototype.cleanup=function(){s=!1,this.isDragging=!1,this.subjectEl=null,this.downEl=null,this.destroyScrollWatch()},e.prototype.querySubjectEl=function(e){return this.selector?t.elementClosest(e.target,this.selector):this.containerEl},e.prototype.shouldIgnoreMouse=function(){return o||this.isTouchDragging},e.prototype.cancelTouchScroll=function(){this.isDragging&&(s=!0)},e.prototype.initScrollWatch=function(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))},e.prototype.recordCoords=function(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.pageXOffset,this.prevScrollY=window.pageYOffset)},e.prototype.destroyScrollWatch=function(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)},e.prototype.createEventFromMouse=function(e,t){var n=0,r=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,r=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:r}},e.prototype.createEventFromTouch=function(e,t){var n,r,i=e.touches,o=0,a=0;return i&&i.length?(n=i[0].pageX,r=i[0].pageY):(n=e.pageX,r=e.pageY),t?(this.origPageX=n,this.origPageY=r):(o=n-this.origPageX,a=r-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:r,deltaX:o,deltaY:a}},e}();function d(e){s&&e.preventDefault()}var c=function(){function e(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}return e.prototype.start=function(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.pageXOffset,this.origScreenY=n-window.pageYOffset,this.deltaX=0,this.deltaY=0,this.updateElPosition()},e.prototype.handleMove=function(e,t){this.deltaX=e-window.pageXOffset-this.origScreenX,this.deltaY=t-window.pageYOffset-this.origScreenY,this.updateElPosition()},e.prototype.setIsVisible=function(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)},e.prototype.stop=function(e,t){var n=this,r=function(){n.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(r,this.revertDuration):setTimeout(r,0)},e.prototype.doRevertAnimation=function(e,n){var r=this.mirrorEl,i=this.sourceEl.getBoundingClientRect();r.style.transition="top "+n+"ms,left "+n+"ms",t.applyStyle(r,{left:i.left,top:i.top}),t.whenTransitionDone(r,function(){r.style.transition="",e()})},e.prototype.cleanup=function(){this.mirrorEl&&(t.removeElement(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null},e.prototype.updateElPosition=function(){this.sourceEl&&this.isVisible&&t.applyStyle(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})},e.prototype.getMirrorEl=function(){var e=this.sourceElRect,n=this.mirrorEl;return n||((n=this.mirrorEl=this.sourceEl.cloneNode(!0)).classList.add("fc-unselectable"),n.classList.add("fc-dragging"),t.applyStyle(n,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(n)),n},e}(),u=function(e){function t(t,n){var r=e.call(this)||this;return r.handleScroll=function(){r.scrollTop=r.scrollController.getScrollTop(),r.scrollLeft=r.scrollController.getScrollLeft(),r.handleScrollChange()},r.scrollController=t,r.doesListening=n,r.scrollTop=r.origScrollTop=t.getScrollTop(),r.scrollLeft=r.origScrollLeft=t.getScrollLeft(),r.scrollWidth=t.getScrollWidth(),r.scrollHeight=t.getScrollHeight(),r.clientWidth=t.getClientWidth(),r.clientHeight=t.getClientHeight(),r.clientRect=r.computeClientRect(),r.doesListening&&r.getEventTarget().addEventListener("scroll",r.handleScroll),r}return r(t,e),t.prototype.destroy=function(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)},t.prototype.getScrollTop=function(){return this.scrollTop},t.prototype.getScrollLeft=function(){return this.scrollLeft},t.prototype.setScrollTop=function(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())},t.prototype.setScrollLeft=function(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())},t.prototype.getClientWidth=function(){return this.clientWidth},t.prototype.getClientHeight=function(){return this.clientHeight},t.prototype.getScrollWidth=function(){return this.scrollWidth},t.prototype.getScrollHeight=function(){return this.scrollHeight},t.prototype.handleScrollChange=function(){},t}(t.ScrollController),p=function(e){function n(n,r){return e.call(this,new t.ElementScrollController(n),r)||this}return r(n,e),n.prototype.getEventTarget=function(){return this.scrollController.el},n.prototype.computeClientRect=function(){return t.computeInnerRect(this.scrollController.el)},n}(u),h=function(e){function n(n){return e.call(this,new t.WindowScrollController,n)||this}return r(n,e),n.prototype.getEventTarget=function(){return window},n.prototype.computeClientRect=function(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}},n.prototype.handleScrollChange=function(){this.clientRect=this.computeClientRect()},n}(u),f="function"==typeof performance?performance.now:Date.now,g=function(){function e(){var e=this;this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=function(){if(e.isAnimating){var t=e.computeBestEdge(e.pointerScreenX+window.pageXOffset,e.pointerScreenY+window.pageYOffset);if(t){var n=f();e.handleSide(t,(n-e.msSinceRequest)/1e3),e.requestAnimation(n)}else e.isAnimating=!1}}}return e.prototype.start=function(e,t){this.isEnabled&&(this.scrollCaches=this.buildCaches(),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))},e.prototype.handleMove=function(e,t){if(this.isEnabled){var n=e-window.pageXOffset,r=t-window.pageYOffset,i=null===this.pointerScreenY?0:r-this.pointerScreenY,o=null===this.pointerScreenX?0:n-this.pointerScreenX;i<0?this.everMovedUp=!0:i>0&&(this.everMovedDown=!0),o<0?this.everMovedLeft=!0:o>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=r,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(f()))}},e.prototype.stop=function(){if(this.isEnabled){this.isAnimating=!1;for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}this.scrollCaches=null}},e.prototype.requestAnimation=function(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)},e.prototype.handleSide=function(e,t){var n=e.scrollCache,r=this.edgeThreshold,i=r-e.distance,o=i*i/(r*r)*this.maxVelocity*t,a=1;switch(e.name){case"left":a=-1;case"right":n.setScrollLeft(n.getScrollLeft()+o*a);break;case"top":a=-1;case"bottom":n.setScrollTop(n.getScrollTop()+o*a)}},e.prototype.computeBestEdge=function(e,t){for(var n=this.edgeThreshold,r=null,i=0,o=this.scrollCaches;i<o.length;i++){var a=o[i],s=a.clientRect,l=e-s.left,d=s.right-e,c=t-s.top,u=s.bottom-t;l>=0&&d>=0&&c>=0&&u>=0&&(c<=n&&this.everMovedUp&&a.canScrollUp()&&(!r||r.distance>c)&&(r={scrollCache:a,name:"top",distance:c}),u<=n&&this.everMovedDown&&a.canScrollDown()&&(!r||r.distance>u)&&(r={scrollCache:a,name:"bottom",distance:u}),l<=n&&this.everMovedLeft&&a.canScrollLeft()&&(!r||r.distance>l)&&(r={scrollCache:a,name:"left",distance:l}),d<=n&&this.everMovedRight&&a.canScrollRight()&&(!r||r.distance>d)&&(r={scrollCache:a,name:"right",distance:d}))}return r},e.prototype.buildCaches=function(){return this.queryScrollEls().map(function(e){return e===window?new h(!1):new p(e,!1)})},e.prototype.queryScrollEls=function(){for(var e=[],t=0,n=this.scrollQuery;t<n.length;t++){var r=n[t];"object"==typeof r?e.push(r):e.push.apply(e,Array.prototype.slice.call(document.querySelectorAll(r)))}return e},e}(),v=function(e){function n(n){var r=e.call(this,n)||this;r.delay=null,r.minDistance=0,r.touchScrollAllowed=!0,r.mirrorNeedsRevert=!1,r.isInteracting=!1,r.isDragging=!1,r.isDelayEnded=!1,r.isDistanceSurpassed=!1,r.delayTimeoutId=null,r.onPointerDown=function(e){r.isDragging||(r.isInteracting=!0,r.isDelayEnded=!1,r.isDistanceSurpassed=!1,t.preventSelection(document.body),t.preventContextMenu(document.body),e.isTouch||e.origEvent.preventDefault(),r.emitter.trigger("pointerdown",e),r.pointer.shouldIgnoreMove||(r.mirror.setIsVisible(!1),r.mirror.start(e.subjectEl,e.pageX,e.pageY),r.startDelay(e),r.minDistance||r.handleDistanceSurpassed(e)))},r.onPointerMove=function(e){if(r.isInteracting){if(r.emitter.trigger("pointermove",e),!r.isDistanceSurpassed){var t=r.minDistance,n=e.deltaX,i=e.deltaY;n*n+i*i>=t*t&&r.handleDistanceSurpassed(e)}r.isDragging&&("scroll"!==e.origEvent.type&&(r.mirror.handleMove(e.pageX,e.pageY),r.autoScroller.handleMove(e.pageX,e.pageY)),r.emitter.trigger("dragmove",e))}},r.onPointerUp=function(e){r.isInteracting&&(r.isInteracting=!1,t.allowSelection(document.body),t.allowContextMenu(document.body),r.emitter.trigger("pointerup",e),r.isDragging&&(r.autoScroller.stop(),r.tryStopDrag(e)),r.delayTimeoutId&&(clearTimeout(r.delayTimeoutId),r.delayTimeoutId=null))};var i=r.pointer=new l(n);return i.emitter.on("pointerdown",r.onPointerDown),i.emitter.on("pointermove",r.onPointerMove),i.emitter.on("pointerup",r.onPointerUp),r.mirror=new c,r.autoScroller=new g,r}return r(n,e),n.prototype.destroy=function(){this.pointer.destroy()},n.prototype.startDelay=function(e){var t=this;"number"==typeof this.delay?this.delayTimeoutId=setTimeout(function(){t.delayTimeoutId=null,t.handleDelayEnd(e)},this.delay):this.handleDelayEnd(e)},n.prototype.handleDelayEnd=function(e){this.isDelayEnded=!0,this.tryStartDrag(e)},n.prototype.handleDistanceSurpassed=function(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)},n.prototype.tryStartDrag=function(e){this.isDelayEnded&&this.isDistanceSurpassed&&(this.pointer.wasTouchScroll&&!this.touchScrollAllowed||(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll()))},n.prototype.tryStopDrag=function(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))},n.prototype.stopDrag=function(e){this.isDragging=!1,this.emitter.trigger("dragend",e)},n.prototype.setIgnoreMove=function(e){this.pointer.shouldIgnoreMove=e},n.prototype.setMirrorIsVisible=function(e){this.mirror.setIsVisible(e)},n.prototype.setMirrorNeedsRevert=function(e){this.mirrorNeedsRevert=e},n.prototype.setAutoScrollEnabled=function(e){this.autoScroller.isEnabled=e},n}(t.ElementDragging),m=function(){function e(e){this.origRect=t.computeRect(e),this.scrollCaches=t.getClippingParents(e).map(function(e){return new p(e,!0)})}return e.prototype.destroy=function(){for(var e=0,t=this.scrollCaches;e<t.length;e++){t[e].destroy()}},e.prototype.computeLeft=function(){for(var e=this.origRect.left,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollLeft-r.getScrollLeft()}return e},e.prototype.computeTop=function(){for(var e=this.origRect.top,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollTop-r.getScrollTop()}return e},e.prototype.isWithinClipping=function(e,n){for(var r,i,o={left:e,top:n},a=0,s=this.scrollCaches;a<s.length;a++){var l=s[a];if(r=l.getEventTarget(),i=void 0,"HTML"!==(i=r.tagName)&&"BODY"!==i&&!t.pointInsideRect(o,l.clientRect))return!1}return!0},e}();var y=function(){function e(e,n){var r=this;this.useSubjectCenter=!1,this.requireInitial=!0,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=function(e){var t=r.dragging;r.initialHit=null,r.movingHit=null,r.finalHit=null,r.prepareHits(),r.processFirstCoord(e),r.initialHit||!r.requireInitial?(t.setIgnoreMove(!1),r.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=function(e){r.emitter.trigger("dragstart",e),r.handleMove(e,!0)},this.handleDragMove=function(e){r.emitter.trigger("dragmove",e),r.handleMove(e)},this.handlePointerUp=function(e){r.releaseHits(),r.emitter.trigger("pointerup",e)},this.handleDragEnd=function(e){r.movingHit&&r.emitter.trigger("hitupdate",null,!0,e),r.finalHit=r.movingHit,r.movingHit=null,r.emitter.trigger("dragend",e)},this.droppableStore=n,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new t.EmitterMixin}return e.prototype.processFirstCoord=function(e){var n,r={left:e.pageX,top:e.pageY},i=r,o=e.subjectEl;o!==document&&(n=t.computeRect(o),i=t.constrainPoint(i,n));var a=this.initialHit=this.queryHitForOffset(i.left,i.top);if(a){if(this.useSubjectCenter&&n){var s=t.intersectRects(n,a.rect);s&&(i=t.getRectCenter(s))}this.coordAdjust=t.diffPoints(i,r)}else this.coordAdjust={left:0,top:0}},e.prototype.handleMove=function(e,t){var n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);!t&&E(this.movingHit,n)||(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))},e.prototype.prepareHits=function(){this.offsetTrackers=t.mapHash(this.droppableStore,function(e){return e.component.buildPositionCaches(),new m(e.el)})},e.prototype.releaseHits=function(){var e=this.offsetTrackers;for(var t in e)e[t].destroy();this.offsetTrackers={}},e.prototype.queryHitForOffset=function(e,n){var r=this.droppableStore,i=this.offsetTrackers,o=null;for(var a in r){var s=r[a].component,l=i[a];if(l.isWithinClipping(e,n)){var d=l.computeLeft(),c=l.computeTop(),u=e-d,p=n-c,h=l.origRect,f=h.right-h.left,g=h.bottom-h.top;if(u>=0&&u<f&&p>=0&&p<g){var v=s.queryHit(u,p,f,g);!v||s.props.dateProfile&&!t.rangeContainsRange(s.props.dateProfile.activeRange,v.dateSpan.range)||o&&!(v.layer>o.layer)||(v.rect.left+=d,v.rect.right+=d,v.rect.top+=c,v.rect.bottom+=c,o=v)}}}return o},e}();function E(e,n){return!e&&!n||Boolean(e)===Boolean(n)&&t.isDateSpansEqual(e.dateSpan,n.dateSpan)}var S=function(e){function n(n){var r=e.call(this,n)||this;r.handlePointerDown=function(e){var t=r.dragging;t.setIgnoreMove(!r.component.isValidDateDownEl(t.pointer.downEl))},r.handleDragEnd=function(e){var t=r.component;if(!r.dragging.pointer.wasTouchScroll){var n=r.hitDragging,i=n.initialHit,o=n.finalHit;i&&o&&E(i,o)&&t.calendar.triggerDateClick(i.dateSpan,i.dayEl,t.view,e.origEvent)}};var i=n.component;r.dragging=new v(i.el),r.dragging.autoScroller.isEnabled=!1;var o=r.hitDragging=new y(r.dragging,t.interactionSettingsToStore(n));return o.emitter.on("pointerdown",r.handlePointerDown),o.emitter.on("dragend",r.handleDragEnd),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n}(t.Interaction),b=function(e){function n(n){var r=e.call(this,n)||this;r.dragSelection=null,r.handlePointerDown=function(e){var t=r,n=t.component,i=t.dragging,o=n.opt("selectable")&&n.isValidDateDownEl(e.origEvent.target);i.setIgnoreMove(!o),i.delay=e.isTouch?function(e){var t=e.opt("selectLongPressDelay");null==t&&(t=e.opt("longPressDelay"));return t}(n):null},r.handleDragStart=function(e){r.component.calendar.unselect(e)},r.handleHitUpdate=function(e,n){var o=r.component.calendar,a=null,s=!1;e&&((a=function(e,n,r){var o=e.dateSpan,a=n.dateSpan,s=[o.range.start,o.range.end,a.range.start,a.range.end];s.sort(t.compareNumbers);for(var l={},d=0,c=r;d<c.length;d++){var u=c[d],p=u(e,n);if(!1===p)return null;p&&i(l,p)}return l.range={start:s[0],end:s[3]},l.allDay=o.allDay,l}(r.hitDragging.initialHit,e,o.pluginSystem.hooks.dateSelectionTransformers))&&r.component.isDateSelectionValid(a)||(s=!0,a=null)),a?o.dispatch({type:"SELECT_DATES",selection:a}):n||o.dispatch({type:"UNSELECT_DATES"}),s?t.disableCursor():t.enableCursor(),n||(r.dragSelection=a)},r.handlePointerUp=function(e){r.dragSelection&&(r.component.calendar.triggerDateSelect(r.dragSelection,e),r.dragSelection=null)};var o=n.component,a=r.dragging=new v(o.el);a.touchScrollAllowed=!1,a.minDistance=o.opt("selectMinDistance")||0,a.autoScroller.isEnabled=o.opt("dragScroll");var s=r.hitDragging=new y(r.dragging,t.interactionSettingsToStore(n));return s.emitter.on("pointerdown",r.handlePointerDown),s.emitter.on("dragstart",r.handleDragStart),s.emitter.on("hitupdate",r.handleHitUpdate),s.emitter.on("pointerup",r.handlePointerUp),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n}(t.Interaction);var D=function(e){function n(r){var o=e.call(this,r)||this;o.subjectSeg=null,o.isDragging=!1,o.eventRange=null,o.relevantEvents=null,o.receivingCalendar=null,o.validMutation=null,o.mutatedRelevantEvents=null,o.handlePointerDown=function(e){var n=e.origEvent.target,r=o,i=r.component,a=r.dragging,s=a.mirror,l=i.calendar,d=o.subjectSeg=t.getElSeg(e.subjectEl),c=(o.eventRange=d.eventRange).instance.instanceId;o.relevantEvents=t.getRelevantEvents(l.state.eventStore,c),a.minDistance=e.isTouch?0:i.opt("eventDragMinDistance"),a.delay=e.isTouch&&c!==i.props.eventSelection?function(e){var t=e.opt("eventLongPressDelay");null==t&&(t=e.opt("longPressDelay"));return t}(i):null,s.parentNode=l.el,s.revertDuration=i.opt("dragRevertDuration");var u=i.isValidSegDownEl(n)&&!t.elementClosest(n,".fc-resizer");a.setIgnoreMove(!u),o.isDragging=u&&e.subjectEl.classList.contains("fc-draggable")},o.handleDragStart=function(e){var n=o.component.calendar,r=o.eventRange,i=r.instance.instanceId;e.isTouch?i!==o.component.props.eventSelection&&n.dispatch({type:"SELECT_EVENT",eventInstanceId:i}):n.dispatch({type:"UNSELECT_EVENT"}),o.isDragging&&(n.unselect(e),n.publiclyTrigger("eventDragStart",[{el:o.subjectSeg.el,event:new t.EventApi(n,r.def,r.instance),jsEvent:e.origEvent,view:o.component.view}]))},o.handleHitUpdate=function(e,n){if(o.isDragging){var r=o.relevantEvents,i=o.hitDragging.initialHit,a=o.component.calendar,s=null,l=null,d=null,c=!1,u={affectedEvents:r,mutatedEvents:t.createEmptyEventStore(),isEvent:!0,origSeg:o.subjectSeg};if(e){var p=e.component;a===(s=p.calendar)||p.opt("editable")&&p.opt("droppable")?(l=function(e,n,r){var i=e.dateSpan,o=n.dateSpan,a=i.range.start,s=o.range.start,l={};i.allDay!==o.allDay&&(l.allDay=o.allDay,l.hasEnd=n.component.opt("allDayMaintainDuration"),o.allDay&&(a=t.startOfDay(a)));var d=t.diffDates(a,s,e.component.dateEnv,e.component===n.component?e.component.largeUnit:null);d.milliseconds&&(l.allDay=!1);for(var c={datesDelta:d,standardProps:l},u=0,p=r;u<p.length;u++){var h=p[u];h(c,e,n)}return c}(i,e,s.pluginSystem.hooks.eventDragMutationMassagers))&&(d=t.applyMutationToEventStore(r,s.eventUiBases,l,s),u.mutatedEvents=d,p.isInteractionValid(u)||(c=!0,l=null,d=null,u.mutatedEvents=t.createEmptyEventStore())):s=null}o.displayDrag(s,u),c?t.disableCursor():t.enableCursor(),n||(a===s&&E(i,e)&&(l=null),o.dragging.setMirrorNeedsRevert(!l),o.dragging.setMirrorIsVisible(!e||!document.querySelector(".fc-mirror")),o.receivingCalendar=s,o.validMutation=l,o.mutatedRelevantEvents=d)}},o.handlePointerUp=function(){o.isDragging||o.cleanup()},o.handleDragEnd=function(e){if(o.isDragging){var n=o.component.calendar,r=o.component.view,a=o,s=a.receivingCalendar,l=a.validMutation,d=o.eventRange.def,c=o.eventRange.instance,u=new t.EventApi(n,d,c),p=o.relevantEvents,h=o.mutatedRelevantEvents,f=o.hitDragging.finalHit;if(o.clearDrag(),n.publiclyTrigger("eventDragStop",[{el:o.subjectSeg.el,event:u,jsEvent:e.origEvent,view:r}]),l){if(s===n){n.dispatch({type:"MERGE_EVENTS",eventStore:h});for(var g={},v=0,m=n.pluginSystem.hooks.eventDropTransformers;v<m.length;v++){var y=m[v];i(g,y(l,n))}var E=i({},g,{el:e.subjectEl,delta:l.datesDelta,oldEvent:u,event:new t.EventApi(n,h.defs[d.defId],c?h.instances[c.instanceId]:null),revert:function(){n.dispatch({type:"MERGE_EVENTS",eventStore:p})},jsEvent:e.origEvent,view:r});n.publiclyTrigger("eventDrop",[E])}else if(s){n.publiclyTrigger("eventLeave",[{draggedEl:e.subjectEl,event:u,view:r}]),n.dispatch({type:"REMOVE_EVENT_INSTANCES",instances:o.mutatedRelevantEvents.instances}),s.dispatch({type:"MERGE_EVENTS",eventStore:o.mutatedRelevantEvents}),e.isTouch&&s.dispatch({type:"SELECT_EVENT",eventInstanceId:c.instanceId});var S=i({},s.buildDatePointApi(f.dateSpan),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:f.component});s.publiclyTrigger("drop",[S]),s.publiclyTrigger("eventReceive",[{draggedEl:e.subjectEl,event:new t.EventApi(s,h.defs[d.defId],h.instances[c.instanceId]),view:f.component}])}}else n.publiclyTrigger("_noEventDrop")}o.cleanup()};var a=o.component,s=o.dragging=new v(a.el);s.pointer.selector=n.SELECTOR,s.touchScrollAllowed=!1,s.autoScroller.isEnabled=a.opt("dragScroll");var l=o.hitDragging=new y(o.dragging,t.interactionSettingsStore);return l.useSubjectCenter=r.useEventCenter,l.emitter.on("pointerdown",o.handlePointerDown),l.emitter.on("dragstart",o.handleDragStart),l.emitter.on("hitupdate",o.handleHitUpdate),l.emitter.on("pointerup",o.handlePointerUp),l.emitter.on("dragend",o.handleDragEnd),o}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n.prototype.displayDrag=function(e,n){var r=this.component.calendar,i=this.receivingCalendar;i&&i!==e&&(i===r?i.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:n.affectedEvents,mutatedEvents:t.createEmptyEventStore(),isEvent:!0,origSeg:n.origSeg}}):i.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:n})},n.prototype.clearDrag=function(){var e=this.component.calendar,t=this.receivingCalendar;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})},n.prototype.cleanup=function(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingCalendar=null,this.validMutation=null,this.mutatedRelevantEvents=null},n.SELECTOR=".fc-draggable, .fc-resizable",n}(t.Interaction);var T=function(e){function n(n){var r=e.call(this,n)||this;r.draggingSeg=null,r.eventRange=null,r.relevantEvents=null,r.validMutation=null,r.mutatedRelevantEvents=null,r.handlePointerDown=function(e){var t=r.component,n=r.querySeg(e),i=r.eventRange=n.eventRange;r.dragging.minDistance=t.opt("eventDragMinDistance"),r.dragging.setIgnoreMove(!r.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&r.component.props.eventSelection!==i.instance.instanceId)},r.handleDragStart=function(e){var n=r.component.calendar,i=r.eventRange;r.relevantEvents=t.getRelevantEvents(n.state.eventStore,r.eventRange.instance.instanceId),r.draggingSeg=r.querySeg(e),n.unselect(),n.publiclyTrigger("eventResizeStart",[{el:r.draggingSeg.el,event:new t.EventApi(n,i.def,i.instance),jsEvent:e.origEvent,view:r.component.view}])},r.handleHitUpdate=function(e,n,o){var a=r.component.calendar,s=r.relevantEvents,l=r.hitDragging.initialHit,d=r.eventRange.instance,c=null,u=null,p=!1,h={affectedEvents:s,mutatedEvents:t.createEmptyEventStore(),isEvent:!0,origSeg:r.draggingSeg};e&&(c=function(e,n,r,o,a){for(var s=e.component.dateEnv,l=e.dateSpan.range.start,d=n.dateSpan.range.start,c=t.diffDates(l,d,s,e.component.largeUnit),u={},p=0,h=a;p<h.length;p++){var f=h[p],g=f(e,n);if(!1===g)return null;g&&i(u,g)}if(r){if(s.add(o.start,c)<o.end)return u.startDelta=c,u}else if(s.add(o.end,c)>o.start)return u.endDelta=c,u;return null}(l,e,o.subjectEl.classList.contains("fc-start-resizer"),d.range,a.pluginSystem.hooks.eventResizeJoinTransforms)),c&&(u=t.applyMutationToEventStore(s,a.eventUiBases,c,a),h.mutatedEvents=u,r.component.isInteractionValid(h)||(p=!0,c=null,u=null,h.mutatedEvents=null)),u?a.dispatch({type:"SET_EVENT_RESIZE",state:h}):a.dispatch({type:"UNSET_EVENT_RESIZE"}),p?t.disableCursor():t.enableCursor(),n||(c&&E(l,e)&&(c=null),r.validMutation=c,r.mutatedRelevantEvents=u)},r.handleDragEnd=function(e){var n=r.component.calendar,i=r.component.view,o=r.eventRange.def,a=r.eventRange.instance,s=new t.EventApi(n,o,a),l=r.relevantEvents,d=r.mutatedRelevantEvents;n.publiclyTrigger("eventResizeStop",[{el:r.draggingSeg.el,event:s,jsEvent:e.origEvent,view:i}]),r.validMutation?(n.dispatch({type:"MERGE_EVENTS",eventStore:d}),n.publiclyTrigger("eventResize",[{el:r.draggingSeg.el,startDelta:r.validMutation.startDelta||t.createDuration(0),endDelta:r.validMutation.endDelta||t.createDuration(0),prevEvent:s,event:new t.EventApi(n,d.defs[o.defId],a?d.instances[a.instanceId]:null),revert:function(){n.dispatch({type:"MERGE_EVENTS",eventStore:l})},jsEvent:e.origEvent,view:i}])):n.publiclyTrigger("_noEventResize"),r.draggingSeg=null,r.relevantEvents=null,r.validMutation=null};var o=n.component,a=r.dragging=new v(o.el);a.pointer.selector=".fc-resizer",a.touchScrollAllowed=!1,a.autoScroller.isEnabled=o.opt("dragScroll");var s=r.hitDragging=new y(r.dragging,t.interactionSettingsToStore(n));return s.emitter.on("pointerdown",r.handlePointerDown),s.emitter.on("dragstart",r.handleDragStart),s.emitter.on("hitupdate",r.handleHitUpdate),s.emitter.on("dragend",r.handleDragEnd),r}return r(n,e),n.prototype.destroy=function(){this.dragging.destroy()},n.prototype.querySeg=function(e){return t.getElSeg(t.elementClosest(e.subjectEl,this.component.fgSegSelector))},n}(t.Interaction);var w=function(){function e(e){var n=this;this.isRecentPointerDateSelect=!1,this.onSelect=function(e){e.jsEvent&&(n.isRecentPointerDateSelect=!0)},this.onDocumentPointerUp=function(e){var r=n,i=r.calendar,o=r.documentPointer,a=i.state;if(!o.wasTouchScroll){if(a.dateSelection&&!n.isRecentPointerDateSelect){var s=i.viewOpt("unselectAuto"),l=i.viewOpt("unselectCancel");!s||s&&t.elementClosest(o.downEl,l)||i.unselect(e)}a.eventSelection&&!t.elementClosest(o.downEl,D.SELECTOR)&&i.dispatch({type:"UNSELECT_EVENT"})}n.isRecentPointerDateSelect=!1},this.calendar=e;var r=this.documentPointer=new l(document);r.shouldIgnoreMove=!0,r.shouldWatchScroll=!1,r.emitter.on("pointerup",this.onDocumentPointerUp),e.on("select",this.onSelect)}return e.prototype.destroy=function(){this.calendar.off("select",this.onSelect),this.documentPointer.destroy()},e}(),C=function(){function e(e,n){var r=this;this.receivingCalendar=null,this.droppableEvent=null,this.suppliedDragMeta=null,this.dragMeta=null,this.handleDragStart=function(e){r.dragMeta=r.buildDragMeta(e.subjectEl)},this.handleHitUpdate=function(e,n,o){var a=r.hitDragging.dragging,s=null,l=null,d=!1,c={affectedEvents:t.createEmptyEventStore(),mutatedEvents:t.createEmptyEventStore(),isEvent:r.dragMeta.create,origSeg:null};e&&(s=e.component.calendar,r.canDropElOnCalendar(o.subjectEl,s)&&(l=function(e,n,r){for(var o=i({},n.leftoverProps),a=0,s=r.pluginSystem.hooks.externalDefTransforms;a<s.length;a++){var l=s[a];i(o,l(e,n))}var d=t.parseEventDef(o,n.sourceId,e.allDay,r.opt("forceEventDuration")||Boolean(n.duration),r),c=e.range.start;e.allDay&&n.startTime&&(c=r.dateEnv.add(c,n.startTime));var u=n.duration?r.dateEnv.add(c,n.duration):r.getDefaultEventEnd(e.allDay,c),p=t.createEventInstance(d.defId,{start:c,end:u});return{def:d,instance:p}}(e.dateSpan,r.dragMeta,s),c.mutatedEvents=t.eventTupleToStore(l),(d=!t.isInteractionValid(c,s))&&(c.mutatedEvents=t.createEmptyEventStore(),l=null))),r.displayDrag(s,c),a.setMirrorIsVisible(n||!l||!document.querySelector(".fc-mirror")),d?t.disableCursor():t.enableCursor(),n||(a.setMirrorNeedsRevert(!l),r.receivingCalendar=s,r.droppableEvent=l)},this.handleDragEnd=function(e){var n=r,o=n.receivingCalendar,a=n.droppableEvent;if(r.clearDrag(),o&&a){var s=r.hitDragging.finalHit,l=s.component.view,d=r.dragMeta,c=i({},o.buildDatePointApi(s.dateSpan),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:l});o.publiclyTrigger("drop",[c]),d.create&&(o.dispatch({type:"MERGE_EVENTS",eventStore:t.eventTupleToStore(a)}),e.isTouch&&o.dispatch({type:"SELECT_EVENT",eventInstanceId:a.instance.instanceId}),o.publiclyTrigger("eventReceive",[{draggedEl:e.subjectEl,event:new t.EventApi(o,a.def,a.instance),view:l}]))}r.receivingCalendar=null,r.droppableEvent=null};var o=this.hitDragging=new y(e,t.interactionSettingsStore);o.requireInitial=!1,o.emitter.on("dragstart",this.handleDragStart),o.emitter.on("hitupdate",this.handleHitUpdate),o.emitter.on("dragend",this.handleDragEnd),this.suppliedDragMeta=n}return e.prototype.buildDragMeta=function(e){return"object"==typeof this.suppliedDragMeta?t.parseDragMeta(this.suppliedDragMeta):"function"==typeof this.suppliedDragMeta?t.parseDragMeta(this.suppliedDragMeta(e)):(n=function(e,n){var r=t.config.dataAttrPrefix,i=(r?r+"-":"")+n;return e.getAttribute("data-"+i)||""}(e,"event"),r=n?JSON.parse(n):{create:!1},t.parseDragMeta(r));var n,r},e.prototype.displayDrag=function(e,t){var n=this.receivingCalendar;n&&n!==e&&n.dispatch({type:"UNSET_EVENT_DRAG"}),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},e.prototype.clearDrag=function(){this.receivingCalendar&&this.receivingCalendar.dispatch({type:"UNSET_EVENT_DRAG"})},e.prototype.canDropElOnCalendar=function(e,n){var r=n.opt("dropAccept");return"function"==typeof r?r(e):"string"!=typeof r||!r||Boolean(t.elementMatches(e,r))},e}();t.config.dataAttrPrefix="";var R=function(){function e(e,n){var r=this;void 0===n&&(n={}),this.handlePointerDown=function(e){var n=r.dragging,i=r.settings,o=i.minDistance,a=i.longPressDelay;n.minDistance=null!=o?o:e.isTouch?0:t.globalDefaults.eventDragMinDistance,n.delay=e.isTouch?null!=a?a:t.globalDefaults.longPressDelay:0},this.handleDragStart=function(e){e.isTouch&&r.dragging.delay&&e.subjectEl.classList.contains("fc-event")&&r.dragging.mirror.getMirrorEl().classList.add("fc-selected")},this.settings=n;var i=this.dragging=new v(e);i.touchScrollAllowed=!1,null!=n.itemSelector&&(i.pointer.selector=n.itemSelector),null!=n.appendTo&&(i.mirror.parentNode=n.appendTo),i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),new C(i,n.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),M=function(e){function t(t){var n=e.call(this,t)||this;n.shouldIgnoreMove=!1,n.mirrorSelector="",n.currentMirrorEl=null,n.handlePointerDown=function(e){n.emitter.trigger("pointerdown",e),n.shouldIgnoreMove||n.emitter.trigger("dragstart",e)},n.handlePointerMove=function(e){n.shouldIgnoreMove||n.emitter.trigger("dragmove",e)},n.handlePointerUp=function(e){n.emitter.trigger("pointerup",e),n.shouldIgnoreMove||n.emitter.trigger("dragend",e)};var r=n.pointer=new l(t);return r.emitter.on("pointerdown",n.handlePointerDown),r.emitter.on("pointermove",n.handlePointerMove),r.emitter.on("pointerup",n.handlePointerUp),n}return r(t,e),t.prototype.destroy=function(){this.pointer.destroy()},t.prototype.setIgnoreMove=function(e){this.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){if(e)this.currentMirrorEl&&(this.currentMirrorEl.style.visibility="",this.currentMirrorEl=null);else{var t=this.mirrorSelector?document.querySelector(this.mirrorSelector):null;t&&(this.currentMirrorEl=t,t.style.visibility="hidden")}},t}(t.ElementDragging),I=function(){function e(e,t){var n=document;e===document||e instanceof Element?(n=e,t=t||{}):t=e||{};var r=this.dragging=new M(n);"string"==typeof t.itemSelector?r.pointer.selector=t.itemSelector:n===document&&(r.pointer.selector="[data-event]"),"string"==typeof t.mirrorSelector&&(r.mirrorSelector=t.mirrorSelector),new C(r,t.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),k=t.createPlugin({componentInteractions:[S,b,D,T],calendarInteractions:[w],elementDraggingImpl:v});e.Draggable=R,e.FeaturefulElementDragging=v,e.PointerDragging=l,e.ThirdPartyDraggable=I,e.default=k,Object.defineProperty(e,"__esModule",{value:!0})});var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1;$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerHolidayConfigurationMenu(),menuLoader.registerConfigureTemplateMenu(),menuLoader.registerAssignTemplateMenu(),menuLoader.registerEventManagementMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerHolidayConfigurationMenu:function(){$("#configureHolidayNav").on("click",function(){holidayConfig.registerHolidayConfigurationMenu()})},registerConfigureTemplateMenu:function(){$("#configureTemplateNav").on("click",function(){configureTemplate.registerConfigureTemplateMenu()})},registerAssignTemplateMenu:function(){$("#assignHolidaysNav").on("click",function(){assignHolidayTemplate.loadHomePage()})},registerEventManagementMenu:function(){$("#eventManagementNav").on("click",function(){eventManagement.loadEventManagementMainPage()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/holiday-calendar/home",function(e){$("#main-content").html(e),homePage.initHomePage()})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/holiday-calendar/session-home/"+e,function(e){})},refreshHomePage:function(){homePage.loadHomePageForSession()}},holidayConfig={registerHolidayConfigurationMenu:function(){ajaxClient.get("/holiday-calendar/manage-holiday/home",function(e){$("#main-content").html(e),holidayConfig.bindHolidayTypeSelection(),holidayConfig.bindHolidayFrequencySelection(),initSelect2(),initDateWithYearRange("-5:+5")})},loadSessionHolidays:function(){var e=$(".holiday-academic-session option:selected").val();ajaxClient.get("/holiday-calendar/manage-holiday/"+e+"/holidays",function(e){$("#session-holidays-container").html(e)})},bindHolidayTypeSelection:function(){$("#holiday-types").change(function(){"STATIC"==$(this).find(":selected").val().trim()?$("#recurring-holiday-input").attr("style","display:none"):$("#recurring-holiday-input").attr("style","display:block")})},bindHolidayFrequencySelection:function(){$("#holiday-frequency-types").change(function(){"WEEK"==$(this).find(":selected").val().trim()?$("#weeks-of-month-container").attr("style","display:none"):$("#weeks-of-month-container").attr("style","display:block")})},resetHolidayModal:function(){$("#add-new-holiday-modal").find(".modal-title").text("Add New Holiday"),$("#add-new-holiday-modal").find(".add-holiday").attr("style","display:block"),$("#add-new-holiday-modal").find(".update-holiday").attr("style","display:none"),$("#holiday-name").val(""),$("#holiday-description").val(""),$("#holiday-types").prop("selectedIndex",0),$("#holiday-start-date").val(""),$("#holiday-end-date").val(""),$("#holiday-frequency-types").prop("selectedIndex",0),$("#holiday-weekdays").prop("selectedIndex",0),$("#weeks-of-month").val([]).change(),$("#weeks-of-month-container").attr("style","display:none"),$("#recurring-holiday-input").attr("style","display:none")},populateUpdateHolidayModal:function(e){holidayConfig.resetHolidayModal(),$("#add-new-holiday-modal").find(".modal-title").text("Update Holiday"),$("#add-new-holiday-modal").find(".add-holiday").attr("style","display:none"),$("#add-new-holiday-modal").find(".update-holiday").attr("style","display:block");var t=$(e).parent().parent().find(".session-holiday-entry").text().trim(),n=JSON.parse(t);$("#holiday-id").val(n.holidayId),$("#holiday-name").val(n.name),$("#holiday-description").val(n.description),$("#holiday-types").val(n.holidayType),$("#holiday-start-date").val(getFormattedDate(n.start)),$("#holiday-end-date").val(getFormattedDate(n.end)),"RECURRING"==n.holidayType&&($("#recurring-holiday-input").attr("style","display:block"),$("#holiday-frequency-types").val(n.recurringPattern.recurringFrequency),$("#holiday-weekdays").val(n.recurringPattern.dayOfWeek),"MONTH"==n.recurringPattern.recurringFrequency&&($("#weeks-of-month-container").attr("style","display:block"),$("#weeks-of-month").val(n.recurringPattern.weekOfMonth).change())),$("#add-new-holiday-modal").modal({backdrop:"static",keyboard:!1})},addNewHoliday:function(){if(validateMandatoryFields($("#add-new-holiday-modal")))showErrorDialogBox("Please fill mandatory fields.");else{var e=$("#holiday-name").val(),t=$("#holiday-description").val(),n=$("#holiday-types option:selected").val(),r=getDate($("#holiday-start-date").val()).getTime()/1e3,i=getDate($("#holiday-end-date").val()).getTime()/1e3,o=null;if("RECURRING"==n){var a=$("#holiday-frequency-types option:selected").val(),s=$("#holiday-weekdays option:selected").val(),l=[];"MONTH"==a&&(l=$("#weeks-of-month").val()),o={recurringFrequency:a,dayOfWeek:s,weekOfMonth:l}}$("#add-new-holiday-modal").modal("toggle");var d={holidayType:n,start:r,end:i,name:e,description:t,recurringPattern:o};ajaxClient.post("/holiday-calendar/manage-holiday/new",{payload:JSON.stringify(d)},function(e){$("#holiday-default-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),holidayConfig.loadSessionHolidays()})}},updateHoliday:function(){if(validateMandatoryFields($("#add-new-holiday-modal")))showErrorDialogBox("Please fill mandatory fields.");else{var e=$("#holiday-id").val(),t=$("#holiday-name").val(),n=$("#holiday-description").val(),r=$("#holiday-types option:selected").val(),i=getDate($("#holiday-start-date").val()).getTime()/1e3,o=getDate($("#holiday-end-date").val()).getTime()/1e3,a=null;if("RECURRING"==r){var s=$("#holiday-frequency-types option:selected").val(),l=$("#holiday-weekdays option:selected").val(),d=[];"MONTH"==s&&(d=$("#weeks-of-month").val()),a={recurringFrequency:s,dayOfWeek:l,weekOfMonth:d}}$("#add-new-holiday-modal").modal("toggle");var c={holidayType:r,start:i,end:o,name:t,description:n,recurringPattern:a};ajaxClient.post("/holiday-calendar/manage-holiday/update/"+e,{payload:JSON.stringify(c)},function(e){$("#holiday-default-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),holidayConfig.loadSessionHolidays()})}},confirmDeleteHoliday:function(e){$("#delete-holiday-modal").modal("toggle");var t=$(e).parent().parent().find(".session-holiday-entry").text().trim(),n=JSON.parse(t),r=$(".holiday-academic-session").val();$("#delete-holiday-id").val(n.holidayId),$("#delete-academic-session-id").val(r)},deleteHoliday:function(e){$("#delete-holiday-modal").modal("toggle");e=$("#delete-holiday-id").val();var t=$("#delete-academic-session-id").val();ajaxClient.post("/holiday-calendar/manage-holiday/delete/"+t+"/"+e,{},function(e){$("#holiday-default-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),holidayConfig.loadSessionHolidays()})}},configureTemplate={dataCache:{},registerConfigureTemplateMenu:function(){ajaxClient.get("/holiday-calendar/configure-template/home",function(e){$("#main-content").html(e),configureTemplate.initDataCache(),academicSessionHandler.bindSessionChangeEvent(configureTemplate.loadHomePageForSession)})},initDataCache:function(){configureTemplate.dataCache={}},addNewTemplateForm:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/holiday-calendar/configure-template/form/"+e,function(e){$("#holiday-template-add-modal-container").html(e),$("#add-holiday-template-modal").modal("toggle")})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/holiday-calendar/configure-template/home/<USER>/"+e,function(e){$("#session-holiday-template-list").html(e),configureTemplate.initDataCache()})},submitNewHolidayTemplate:function(){var e=$("#new-holiday-template-name").val().trim(),t=$("#new-holiday-template-type").find(":selected").val().trim(),n=$("#new-holiday-template-description").val().trim(),r=configureTemplate.getTemplateSelectedHolidays("add-holiday-template-modal");if(0!=r.length){var i=academicSessionHandler.getSelectedSessionId();holidayTemplatePayload={academicSessionId:i,templateName:e,templateType:t,description:n,holidays:r},$("#add-holiday-template-modal").modal("toggle"),ajaxClient.post("/holiday-calendar/configure-template/add",{holidayTemplatePayload:JSON.stringify(holidayTemplatePayload)},function(e){$("#holiday-template-status-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),configureTemplate.loadHomePageForSession()})}else alert("Please select atleast one holiday")},getTemplateSelectedHolidays:function(e){var t=[];return $("#"+e).find("input.template-holiday-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).attr("id").split(":")[1];t.push(e)}}),t},viewHolidayTemplate:function(e){var t=$(e).parent().parent().attr("id");ajaxClient.get("/holiday-calendar/configure-template/view/"+t,function(e){$("#holiday-template-view-modal-container").html(e),$("#view-holiday-template-modal").modal("toggle")})},showUpdateHolidayTemplate:function(e){var t=$(e).parent().parent().attr("id");ajaxClient.get("/holiday-calendar/configure-template/update-form/"+t,function(e){$("#holiday-template-update-modal-container").html(e),$("#update-holiday-template-modal").modal("toggle")})},submitUpdateHolidayTemplate:function(e){var t=$("#update-holiday-template-name").val().trim(),n=$("#update-holiday-template-type").find(":selected").val().trim(),r=$("#update-holiday-template-description").val().trim(),i=configureTemplate.getTemplateSelectedHolidays("update-holiday-template-modal");if(0!=i.length){var o=academicSessionHandler.getSelectedSessionId();holidayTemplatePayload={academicSessionId:o,templateName:t,templateType:n,description:r,holidays:i},$("#update-holiday-template-modal").modal("toggle"),ajaxClient.post("/holiday-calendar/configure-template/update/"+e,{holidayTemplatePayload:JSON.stringify(holidayTemplatePayload)},function(e){$("#holiday-template-status-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),configureTemplate.loadHomePageForSession()})}else alert("Please select atleast one holiday")},confirmDeleteHolidayTemplate:function(e){var t=$(e).parent().parent().attr("id");$("#delete-holiday-template-id").text(t),$("#delete-holiday-template-modal").modal("toggle")},submitDeleteHolidayTemplate:function(){var e=$("#delete-holiday-template-id").text();$("#delete-holiday-template-modal").modal("toggle"),ajaxClient.get("/holiday-calendar/configure-template/delete/"+e,function(e){$("#holiday-template-status-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),configureTemplate.loadHomePageForSession()})}},assignHolidayTemplate={dataCache:{},studentUserType:"STUDENT",staffUserType:"STAFF",defaultUserType:"STUDENT",loadHomePage:function(){ajaxClient.get("/holiday-calendar/assign-template/home/"+assignHolidayTemplate.defaultUserType,function(e){$("#main-content").html(e),assignHolidayTemplate.initDataCache(),initSelect2(),academicSessionHandler.bindSessionChangeEvent(assignHolidayTemplate.loadHomePageForSession),assignHolidayTemplate.registerUserTypeOptionsTabs()})},initDataCache:function(){assignHolidayTemplate.dataCache={userType:assignHolidayTemplate.defaultUserType}},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId(),t=assignHolidayTemplate.dataCache.userType;ajaxClient.get("/holiday-calendar/assign-template/home/<USER>/"+e+"/"+t,function(e){$("#user-list-screen").html(e),initSelect2()})},registerUserTypeOptionsTabs:function(){$("#select-student-user-type").off("click").on("click",function(){assignHolidayTemplate.dataCache.userType=assignHolidayTemplate.studentUserType,assignHolidayTemplate.loadHomePageForSession()}),$("#select-staff-user-type").off("click").on("click",function(){assignHolidayTemplate.dataCache.userType=assignHolidayTemplate.staffUserType,assignHolidayTemplate.loadHomePageForSession()})},searchStudents:function(){var e=academicSessionHandler.getSelectedSessionId(),t=assignHolidayTemplate.dataCache.userType,n=$(".select-template-class").val(),r=!1;$("#template-not-assigned-checkbox").is(":checked")&&(r=!0),n=null!=n&&null!=n?n.join(","):"",ajaxClient.get("/holiday-calendar/assign-template/user-search/"+e+"/"+t+"?requiredStandards="+n+"&templateNotAssignedUsersOnly="+r,function(e){$("#student-template-list").html(e)})},selectBulkStudentList:function(e){if(e.checked){$(".bulk-student-select-checkbox").prop("checked",!0);var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),assignHolidayTemplate.dataCache.selectedStudentCount=t}else $(".bulk-student-select-checkbox").prop("checked",!1),$("#bulk-students-selected-count").html(0),assignHolidayTemplate.dataCache.selectedStudentCount=0},bulkStudentSelectCheckbox:function(e){var t=$("input.bulk-student-select-checkbox:checkbox:checked").length;$("#bulk-students-selected-count").html(t),assignHolidayTemplate.dataCache.selectedStudentCount=t},getSelectedBulkStudentIds:function(){var e=[];return $("input.bulk-student-select-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-template-assign-student-id").first().text().trim();e.push(t)}}),e},assignStudentTemplateConfirmModal:function(){var e=assignHolidayTemplate.dataCache.selectedStudentCount;null!=e&&null!=e&&0!=e?($("#bulk-student-template-assignment-confirmation-modal-text").html("Do you want to set the below calendar to "+e+" students?"),$("#bulk-student-template-assignment-confirmation-modal").modal("toggle")):alert("Please select atleast one student to assign template")},assignStudentTemplate:function(){$("#bulk-student-template-assignment-confirmation-modal").modal("toggle");var e=$("#assignment-holiday-template").find(":selected").val().trim();""==e&&(e=null);var t=assignHolidayTemplate.getSelectedBulkStudentIds(),n=assignHolidayTemplate.dataCache.userType,r=academicSessionHandler.getSelectedSessionId();holidayTemplateAssignPayload={academicSessionId:r,userType:n,templateId:e,userList:t},$("#add-holiday-template-modal").modal("toggle"),ajaxClient.post("/holiday-calendar/assign-template/update",{holidayTemplateAssignPayload:JSON.stringify(holidayTemplateAssignPayload)},function(e){$("#holiday-template-assignment-status-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),assignHolidayTemplate.searchStudents()})},searchStaff:function(){var e=academicSessionHandler.getSelectedSessionId(),t=assignHolidayTemplate.dataCache.userType,n=!1;$("#template-not-assigned-checkbox").is(":checked")&&(n=!0),ajaxClient.get("/holiday-calendar/assign-template/user-search/"+e+"/"+t+"?templateNotAssignedUsersOnly="+n,function(e){$("#staff-template-list").html(e)})},selectBulkStaffList:function(e){if(e.checked){$(".bulk-staff-select-checkbox").prop("checked",!0);var t=$("input.bulk-staff-select-checkbox:checkbox:checked").length;$("#bulk-staff-selected-count").html(t),assignHolidayTemplate.dataCache.selectedStaffCount=t}else $(".bulk-staff-select-checkbox").prop("checked",!1),$("#bulk-staff-selected-count").html(0),assignHolidayTemplate.dataCache.selectedStaffCount=0},bulkStaffSelectCheckbox:function(e){var t=$("input.bulk-staff-select-checkbox:checkbox:checked").length;$("#bulk-staff-selected-count").html(t),assignHolidayTemplate.dataCache.selectedStaffCount=t},getSelectedBulkStaffIds:function(){var e=[];return $("input.bulk-staff-select-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().find("p.bulk-template-assign-staff-id").first().text().trim();e.push(t)}}),e},assignStaffTemplateConfirmModal:function(){var e=assignHolidayTemplate.dataCache.selectedStaffCount;null!=e&&null!=e&&0!=e?($("#bulk-staff-template-assignment-confirmation-modal-text").html("Do you want to set the below calendar to "+e+" staffs?"),$("#bulk-staff-template-assignment-confirmation-modal").modal("toggle")):alert("Please select atleast one staff to assign template")},assignStaffTemplate:function(){$("#bulk-staff-template-assignment-confirmation-modal").modal("toggle");var e=$("#assignment-holiday-template").find(":selected").val().trim();""==e&&(e=null);var t=assignHolidayTemplate.getSelectedBulkStaffIds(),n=assignHolidayTemplate.dataCache.userType,r=academicSessionHandler.getSelectedSessionId();holidayTemplateAssignPayload={academicSessionId:r,userType:n,templateId:e,userList:t},ajaxClient.post("/holiday-calendar/assign-template/update",{holidayTemplateAssignPayload:JSON.stringify(holidayTemplateAssignPayload)},function(e){$("#holiday-template-assignment-status-modal-container").html(e),$("#holiday-default-status-modal").modal("toggle"),assignHolidayTemplate.searchStaff()})}},eventManagement={dataCache:{},loadEventManagementMainPage:function(){ajaxClient.get("/holiday-calendar/event-management-main-screen",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(eventManagement.changeSession),initDateWithYearRange("-5:+5")})},changeSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/holiday-calendar/event-session-change-screen/"+e,function(e){$("#event-list").html(e)})},addNewEvent:function(){if(validateMandatoryFields($("#add-event-modal")))return;const e={academicSessionId:academicSessionHandler.getSelectedSessionId(),eventId:null,title:$("#event-name").val().trim(),description:$("#description").val().trim(),eventDate:getDate($("#event-date").val()).getTime()/1e3,category:$("#event-category").val(),metaData:"",documents:"",createdBy:null};$("#add-event-modal").modal("toggle"),ajaxClient.post("/holiday-calendar/add-event",{event_details:JSON.stringify(e)},function(e){$("#event-status-modal-container").html(e),$("#holiday-default-status-modal").modal({backdrop:"static",keyboard:!1}),eventManagement.changeSession()})},updateEventDetails:function(e){const t=$(e).closest("tr").find(".event-info").text().trim(),n=JSON.parse(t);$("#update-event-name").val(n.title||""),$("#update-description").val(n.description||""),$("#update-event-category").val(n.category||""),$("#update-event-date").val(getFormattedDate(n.eventDate)),$("#update-event-id").text(n.eventId||""),$("#update-event-modal").modal("show")},updateEvent:function(){if(validateMandatoryFields($("#update-event-modal")))return;const e={academicSessionId:academicSessionHandler.getSelectedSessionId(),eventId:$("#update-event-id").text().trim(),title:$("#update-event-name").val().trim(),description:$("#update-description").val().trim(),eventDate:getDate($("#update-event-date").val()).getTime()/1e3,category:$("#update-event-category").val(),metaData:"",documents:"",createdBy:null};$("#update-event-modal").modal("toggle"),ajaxClient.post("/holiday-calendar/update-event",{event_details:JSON.stringify(e)},function(e){$("#event-status-modal-container").html(e),$("#holiday-default-status-modal").modal({backdrop:"static",keyboard:!1}),eventManagement.changeSession()})},deleteEventDetails:function(e){const t=$(e).attr("id");$("#delete-event-id").text(t),$("#delete-event-confirm-modal").modal("show")},deleteEvent:function(){academicSessionId=academicSessionHandler.getSelectedSessionId(),$("#delete-event-confirm-modal").modal("toggle");var e=$("#delete-event-id").text().trim();ajaxClient.post("/holiday-calendar/delete-event/"+academicSessionId+"/"+e,{},function(e){$("#event-status-modal-container").html(e),$("#holiday-default-status-modal").modal({backdrop:"static",keyboard:!1}),eventManagement.changeSession()})},resetFields:function(){$("#add-event-modal").find("input").val(""),$("#add-event-modal").find("select").val(""),$("#add-event-modal").find("textarea").val("")}};