{% load json %}
{% if exchange == True%}
{% else %}
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="#">Orders</a></li>
      {%if transaction_type == "RETURN" %}
        <li class="breadcrumb-item active" aria-current="page">Purchase Return</li>
      {%elif transaction_type == "SALES_RETURN" %}
        <li class="breadcrumb-item active" aria-current="page">Sale Return</li>
      {%else%}
          <li class="breadcrumb-item active" aria-current="page">New Sale</li>
      {%endif%}
    </ol>
  </nav>
 
{% endif %}
{% if transaction_type != "RETURN" %}
  <div class="d-flex justify-content-end mb-2">
    <div class="form-inline">
      <label for="sale-global-date" class="mr-2"><strong>Transaction Date*</strong></label>
      <input type="text" class="form-control mandatory-field select-date" id="sale-global-date" placeholder="Enter date ..." value="{{current_date}}">
    </div>
  </div>
{% endif %}

<div class="card" id="sales-order-body">
  {%if transaction_type == "RETURN" %}
    {% include 'inventory/orders/purchase_return_order_form.html' %}
  {%else%}
     {% include 'inventory/orders/sale_order_form.html' %}
  {%endif%}
</div>

<div id="new-sale-confirm-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog modal-md modal-dialog-centered" role="document">
   <div class="modal-content">
     <div class="modal-header">
       {%if transaction_type == "RETURN" %}
          <h5 class="modal-title">Purchase Return</h5>
       {%elif transaction_type == "SALES_RETURN" %}
          <h5 class="modal-title">Sale Return</h5>
       {%else%}
            {% if exchange == True %}
                <h5 class="modal-title">Exchange Order</h5>
            {% else %}
                <h5 class="modal-title">New Sale</h5>
            {% endif %}
          
       {%endif%}
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
        {%if transaction_type == "RETURN" %}
            <p>Are you sure to return these purchased products?</p>
        {%elif transaction_type == "SALES_RETURN" %}
            <p>Are you sure to accept return of these sold products?</p>
        {%else%}
          <div class="row">
            <div class="col-md-6">
              <span> <strong> Total Payble Amount : </strong></span> <span id="confirm-modal-total-sale-payble-amount"></span>
            </div>
            <div class="col-md-6">
              <span>  <strong>Available Wallet Amount : </strong></span> <span id="confirm-modal-wallet-amount"> </span>
            </div>
          </div>
          <br />
          <div class="sale-confirm-modal-content" id="sale-confirm-modal-student-wallet-usage-section" style="display:none;">
            <div id="with-wallet-return-amount-container">
              <div class="form-group col-md-6" id="with-wallet-return-payment-mode-selection">
                <label>Return Payment Mode*</label>
                <select class="form-control mandatory-field sale-payment-mode">
                  <option value="">select</option>
                  <option value="WALLET">Embrate Wallet</option>
                  <option value="CASH">Cash</option>
                  <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
                  <option value="CHEQUE"> Cheque</option>
                  <option value="CARD">Card</option>
                  <option value="QR_PAYMENT">QR Payment</option>
                  <option value="GOOGLE_PAY_UPI">GPay UPI</option>
                  <option value="PHONE_PE_UPI">PhonePe UPI</option>
                  <option value="PAYTM">Paytm</option>
                  <option value="BHIM_UPI">BHIM UPI</option>
                  <option value="OTHER_UPI">Other UPI</option>
                </select>
              </div>
            </div>
            <div id="with-wallet-collect-amount-container">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="confirm-modal-use-wallet-amount-check">
                    <label class="form-check-label" for="confirm-modal-use-wallet-amount-check">Use wallet amount</label>
                  </div>
                </div>
              </div>
              <br />
              <div class="row">
                <div class="col-md-4" style="display:none;">
                    <label>Used Wallet Amount*</label>
                    <input type="number" class="form-control" id="confirm-modal-used-wallet-amount" placeholder="Wallet amount" readonly>
                </div>
                <div class="col-md-4">
                    <label>Paid Amount*</label>
                    <input type="number" class="form-control" id="confirm-modal-paid-amount" placeholder="Paid Amount" readonly>
                </div>
                <div class="col-md-4" style="display:none;">
                    <label id="credit-amount-label">Credit Amount</label>
                    <input type="number" class="form-control" id="confirm-modal-credit-amount" placeholder="Credit Amount" value="0">
                </div>
              </div>
              <br/>
              <div class="row">
                <div class="form-group col-md-6" id="with-wallet-collect-payment-mode-selection">
                  <label>Payment Mode</label>
                  <select class="form-control mandatory-field sale-payment-mode">
                    <option value="">select</option>
                    <option value="CASH">Cash</option>
                    <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
                    <option value="CHEQUE"> Cheque</option>
                    <option value="CARD">Card</option>
                    <option value="QR_PAYMENT">QR Payment</option>
                    <option value="GOOGLE_PAY_UPI">GPay UPI</option>
                    <option value="PHONE_PE_UPI">PhonePe UPI</option>
                    <option value="PAYTM">Paytm</option>
                    <option value="BHIM_UPI">BHIM UPI</option>
                    <option value="OTHER_UPI">Other UPI</option>
                  </select>
                </div>
              </div>
              
            </div>
            <br />
            {% if exchange == True %}
              <p>Are you sure to exchange selected products?</p>
            {% else %}
                <p>Are you sure to sell selected products?</p>
            {% endif %}
          </div>

          <div class="sale-confirm-modal-content" id="sale-confirm-modal-without-wallet-usage">
            <div class="form-group col-md-6" id="without-wallet-return-payment-mode-selection">
              <label>Return Payment Mode*</label>
              <select  class="form-control mandatory-field sale-payment-mode">
                <option value="">select</option>
                <option value="WALLET">Embrate Wallet</option>
                <option value="CASH">Cash</option>
                <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
                <option value="CHEQUE"> Cheque</option>
                <option value="CARD">Card</option>
                <option value="QR_PAYMENT">QR Payment</option>
                <option value="GOOGLE_PAY_UPI">GPay UPI</option>
                <option value="PHONE_PE_UPI">PhonePe UPI</option>
                <option value="PAYTM">Paytm</option>
                <option value="BHIM_UPI">BHIM UPI</option>
                <option value="OTHER_UPI">Other UPI</option>
              </select>
            </div>
            <div class="form-group col-md-6" id="without-wallet-collect-payment-mode-selection">
              <label>Payment Mode*</label>
              <select class="form-control mandatory-field sale-payment-mode">
                <option value="">select</option>
                <option value="CASH">Cash</option>
                <option value="ACCOUNT_TRANSFER"> Account Transfer</option>
                <option value="CHEQUE"> Cheque</option>
                <option value="CARD">Card</option>
                <option value="QR_PAYMENT">QR Payment</option>
                <option value="GOOGLE_PAY_UPI">GPay UPI</option>
                <option value="PHONE_PE_UPI">PhonePe UPI</option>
                <option value="PAYTM">Paytm</option>
                <option value="BHIM_UPI">BHIM UPI</option>
                <option value="OTHER_UPI">Other UPI</option>
              </select>
            </div>
            {% if exchange == True %}
                <p>Are you sure to exchange selected products?</p>
            {% else %}
                <p>Are you sure to sell selected products?</p>
            {% endif %}
          </div>
       {% endif %}
     </div>
     <div class="modal-footer">
        {% if exchange == True %}
            <button type="button" class="btn btn-primary" onclick="inventoryExchangeProduct.addExchangeTransaction()">Confirm Exchange</button>
        {% else %}
            <button type="button" class="btn btn-primary" onclick="inventorySale.addNewSale()">Confirm</button>
        {% endif %}
       
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>

<div id="inventory-student-search-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
      <h5 class="modal-title">Search Student</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <div class="form-row">
         <div class="form-group col-md-12">
           <div class="input-group" style="width:100%">
             <input id="inventory-student-search-text" type="text" class="form-control" placeholder="Type to search students...">
           </div>
           <div id="inventory-student-search-result"></div>
         </div>
       </div>
     </div>
     <div class="modal-footer">
       <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
     </div>
   </div>
 </div>
</div>

<div id="selected-student-data" style="display:none;"></div>
<div id="inventory-preferences-json" style="display:none;">{{inventory_preferences|jsonstr}}</div>
<div id="student-wallet-preferences-json" style="display:none;">{{student_wallet_preferences|jsonstr}}</div>

<div id="sale-status-modal-container"></div>
{% include 'inventory/product_groups/product_list_model.html' %}
