{% load json %}
<div class="row mb-2 mb-xl-3" id="lesson-plan-academic-session-div">
    {% include 'core/utils/v2/academic_session_display.html'%}
    <div class="col-auto ml-auto text-right mt-n1">
      <div id="standard-with-section-options-wrapper" style="display:inline-block;">
        {% include 'lesson_plan/course_details_lesson_plan/standard_with_section_options.html' %}
      </div>
        {% include 'core/utils/v2/academic_session_dropdown.html'%}
    </div>
</div>
<div id="lesson-plan-form-navbar" style="display : none;">
  <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
          <i class="bi bi-chevron-left" onclick="lessonPlanPage.back()" style="cursor:pointer; margin-right:0.2rem;"></i>
          <li class="breadcrumb-item" id="lesson-plan-course-name"></li>
          <li class="breadcrumb-item active" aria-current="page" id="lesson-plan-standard-name"></li>
      </ol>
  </nav>
</div>
<div class="card">
<div id="lesson-plan-main-container" class="card-body">
  <p id="admin-access" style="display: none;">{{lesson_plan_admin_access}}</p>
  <p id="staff-access" style="display: none;">{{lesson_plan_staff_access}}</p>
  <p id="staff-response-access" style="display: none;">{{lesson_plan_staff_response}}</p>
  <br><br><br><br><br><br><br><br><br><br>
  <h5 style="text-align:center;color:#223b5c;"> Select class from top menu to see course and lesson Plan !</h5>
  <br><br><br><br><br><br><br><br><br><br>
</div>
<div id="lesson-plan-staff-response-detail-container" class="card-body" style="display: none;">
</div>
</div>
<div class="modal fade" id="lesson-plan-delete-warning-popup" tabindex="-1" role="dialog" aria-labelledby="exampleCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleLongTitle">Delete Lesson Plan Infomation </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <span style="color:red" id="lesson-plan-delete-warning-popup-text">Do you want to delete lesson plan details?</span>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-success" data-dismiss="modal">No</button>
          <button type="button" class="btn btn-danger" id="lesson-plan-delete-confirm-button">Yes, delete</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="clone-conformation-data-popup" tabindex="-1" role="dialog" aria-labelledby="exampleCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleLongTitle">Clone Lesson Plan Details</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="returnDate">
          <span style="color:#212121" id="clone-confirm-popup-text"></span>
          <br><br>    
          <span style="color:red" id="clone-confirm-popup-warning-text">Warning: If the selected section already contains a lesson plan, the existing plan and any associated staff responses will be permanently deleted.</span>
          <br><br>
          <div class="form-row">
            <div class="form-group col-md-6">
              <label>Select Section *</label>
              <select class="form-control mr-sm-4 clone-lesson-section select2 mandatory-field" id="add-clone-lesson-plan-popup-class-section" multiple data-style="bg-white border" title="Select Section">
              </select>
            </div>
        </div>
      </div>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">No</button>
          <button type="button" class="btn btn-primary" id="lesson-plan-confirm-button">Yes, Clone</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="clone-conformation-popup" tabindex="-1" role="dialog" aria-labelledby="exampleCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleLongTitle">Clone Lesson Plan Infomation </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <span style="color:red" id="clone-conformation-Warning-popup-text">Warning: If the selected section already contains a lesson plan, the existing plan and any associated staff responses will be permanently deleted.</span>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">No</button>
          <button type="button" class="btn btn-danger" id="clone-conformation-popup-confirm-button">Yes, Clone</button>
      </div>
    </div>
  </div>
</div>
<div id="lesson-plan-status-modal-container"></div>