var regexEmail = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
var regexPhoneNumber = /^\d{10}$/;
var regexAadharNumber = /^\d{12}$/;
var regexAge = /^(0?[1-9]|[1-9][0-9]|[1][0-1][0-9]|120)$/;
// var STUDENT_IMAGE_SIZE_LIMIT = 500;

function loadAdmitStudentMenu() {
    ajaxClient.get("/admission/add-student-view", function(data) {
      $("#main-content").html(data);
      admission.bindSessionChangeEvent();
      bindAdmissionClassSectionEvent();
      bindRemoveErrorDisplayEvent();
      initDate(365*30);
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      permanentAddressUpdate("permanent-same-as-present",
      "student-permanent-address", "student-city", "student-state", "student-post-office", "student-police-station", "student-zipcode",
      "student-present-address", "student-present-city", "student-present-state", "student-present-post-office", "student-present-police-station", "student-present-zipcode");
      $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
      });
    });
}

var admission = {

  dataCache : {},

  bindSessionChangeEvent : function () {
    $("#add-student-academic-session").change(function() {
        var admissionAcademicSessionId = $(this).find(':selected').val().trim();
        ajaxClient.get("/admission/standards/"+admissionAcademicSessionId, function(data) {
          $(".student-standard-json-wrapper").html(data);
          var standardId = $("#add-student-class").find(':selected').val().trim();
          fillStudentSections(standardId);
          admission.updateInstituteHouses();
        });
    });
  },
  updateInstituteHouses : function () {
    var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
    ajaxClient.get("/admission/institute-houses/"+admissionAcademicSessionId, function(data) {
      $("#institute-houses-info-button").html(data);
      $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
      });
    });
  },
}

function bindAdmissionClassSectionEvent() {
  $(".student-class").change(function() {
      var classId = $(this).find(':selected').val().trim();
      fillStudentSections(classId);
  });
}

function loadCreateStudentConfirmModal() {
  var invalid = validateMandatoryFields($("#add\\.basic-info-content"));
  if(invalid){
    showErrorDialogBox("Invalid student information. Please try again.")
    return;
  }
  var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
  var latestSessionId = $(".latest-session-id").text().trim();
  var standardId = $("#add-student-class").find(':selected').val().trim();
  ajaxClient.get("/admission/default-fee-assignment-structure/"+admissionAcademicSessionId+"/"+standardId, function(data) {
    $("#create-student-confirm-modal-container").html(data);
    if(latestSessionId != admissionAcademicSessionId){
      showWaringDialogBoxWithExistingModalDetails("Warning: A new academic session has already been created. You are about to take admission in a previous session. Are you sure you want to proceed?", "#create-student-confirm-modal");
    }
    else{
      $("#create-student-confirm-modal").modal({backdrop: 'static', keyboard: false});
    }
    // var myModal = new bootstrap.Modal(document.getElementById('create-student-confirm-modal'), {
    //   keyboard: false,
    //   backdrop: 'static'
    // })
    // // var modal = bootstrap.Modal.getInstance($("#create-student-confirm-modal"));
    // myModal.show();
    // $("#create-student-confirm-modal").show();
  });
}

function createStudent() {
    $("#create-student-confirm-modal").modal('toggle');
    var invalid = validateMandatoryFields($("#add\\.basic-info-content"));
    if(invalid){
      showErrorDialogBox("Invalid student information. Please try again.")
      return;
    }

    var documentName = "";
    var file = "";
    // var ONE_KB = 1024;
    // if (($("#student-photo"))[0].files.length > 0) {
    //   file = ($("#student-photo"))[0].files[0];
    //     if((file.size / ONE_KB) > STUDENT_IMAGE_SIZE_LIMIT){
    //       showErrorDialogBox("Size Of document cannot be greater than "+STUDENT_IMAGE_SIZE_LIMIT+" kb");
    //       return;
    //     }
    // }

    var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
    var standardId = $("#add-student-class").find(':selected').val().trim();
    var sectionId = $("#add-student-section").find(':selected').val().trim();
    if(sectionId == ""){
      sectionId = null;
    }
    var studentGuardianInfoList = [];
    var studentBasicInfo = getStudentBasicInfo();
    if(studentBasicInfo === undefined) {
      return;
    }
    console.log(studentBasicInfo);
    var studentFamilyInfo = null;
    if(studentFamilyInfo === undefined) {
      return;
    }
    var studentGuardianInfo = null;
    if(studentGuardianInfo === undefined) {
      return;
    }
    var studentPreviousSchoolInfo = null;
    if(studentPreviousSchoolInfo === undefined) {
      return;
    }
    var studentMedicalInfo = null;

    var applicableFeeStructures = [];
    $("#applicable-registration-fees").find("input.applicable-fee-structure-assign-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var feeStructureId = $(this).attr("id");
        applicableFeeStructures.push(feeStructureId);
    });
    // var studentFamilyInfo = getStudentFamilyInfo();
    // var studentGuardianInfo = getStudentGuardianInfo();
    // var studentPreviousSchoolInfo = getStudentPreviousSchoolInfo();
    // var studentMedicalInfo = getStudentMedicalInfo();
    // studentGuardianInfoList.push(studentGuardianInfo);
    var newAdmission = $("#student-new-admission").is(":checked");
    var addStudentPayload = {'admissionAcademicSession' : admissionAcademicSessionId, 'standardId':standardId, 'sectionId':sectionId,
     'studentBasicInfo': studentBasicInfo, 'studentFamilyInfo': studentFamilyInfo, 'studentGuardianInfoList': studentGuardianInfoList,
     'studentPreviousSchoolInfo':studentPreviousSchoolInfo, 'studentMedicalInfo':studentMedicalInfo, "newAdmission" : newAdmission};

     var registerStudentPayload = {'studentPayload':addStudentPayload, 'feeStructureIds' : applicableFeeStructures}

     var formData = new FormData();
     if(file != "") {
       formData.append('document', file);
       formData.append('documentName', documentName);
     }

     formData.append('registerStudentPayload', JSON.stringify(registerStudentPayload));
     ajaxClient.uploadFile("/admission/add-new-student", formData, function(data) {
         $("#admission-status-modal-container").html(data);
         var response = JSON.parse($("#student-data-response").text().trim());
         if(response.success){
           fillStudentInformation(response.student);
           // bindAdmissionClassSectionEvent();
           $("#update-student-family-info").trigger("click");
           $('.add-student-screen').attr('style','display:none');
           $('#add\\.update-student-screen').attr('style','display:block');
           $('#screen-name').text('ADMIT-STUDENT');
         }
         enforceConstraints();
         $("#admission-status-modal").modal('toggle');
     });
}

function getStudentBasicInfo(){
  var registrationNumber = $("#student-registration-number").val().trim();
  var admissionDate = getDate($("#student-admission-date").val());
  var registrationDate = getDate($("#student-registration-date").val());
  var name = $("#student-name").val().trim();
  var gender = $("#student-gender").val().trim();
  if(gender == ""){
    gender = null;
  }
  var dob = getDate($("#student-date-of-birth").val());
  var birthPlace = $("#student-birth-place").val().trim();
  var userCategory = $("#student-category").val().trim();
  if(userCategory == ""){
    userCategory = null;
  }
  var religion = $("#student-religion").val().trim();
  var caste = $("#student-caste").val().trim();
  var isRTE = $("#student-rte").is(":checked");
  var motherTongue = $("#student-mother-tongue").val().trim();
  var areaType = $("#student-area-type").val().trim();
  if(areaType == ""){
    areaType = null;
  }
  var speciallyAbled = $("#student-specially-abled").is(":checked");
  var bpl = $("#student-bpl").is(":checked");

  var presentAddress = $("#student-present-address").val().trim();
  var presentCity = $("#student-present-city").val().trim();
  var presentState = $("#student-present-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#student-present-state").find(".country-states").find(':selected').val().trim();
  var presentPostOffice = $("#student-present-post-office").val().trim();
  var presentPoliceStation = $("#student-present-police-station").val().trim();
  var presentZipcode = $("#student-present-zipcode").val().trim();


  var permanentAddress = $("#student-permanent-address").val().trim();
  var city = $("#student-city").val().trim();
  var state = $("#student-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#student-state").find(".country-states").find(':selected').val().trim();
  var postOffice = $("#student-post-office").val().trim();
  var policeStation = $("#student-police-station").val().trim();
  var zipcode = $("#student-zipcode").val().trim();


  var registrationDateInt = null;
  if(registrationDate != null){
    registrationDateInt = registrationDate.getTime()/1000;
  }

  var admissionDateInt = null;
  if(admissionDate != null){
    admissionDateInt = admissionDate.getTime()/1000;
  }
  var dobInt = null;
  if(dob != null){
    dobInt = dob.getTime()/1000;
  }


  var isError = false;
  var impactedElement = null;
  var aadharNumber = $("#student-aadhar-number").val().trim();
  if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
    isError = true;
    impactedElement = "student-aadhar-number";
    string = "Invalid Aadhar Number";
  }

  var nationality = $("#student-nationality").val().trim();

  var primaryContactNumber = $("#student-primary-contact-number").val().trim();
  if(primaryContactNumber != "" && !regexPhoneNumber.test(primaryContactNumber)) {
    isError = true;
    impactedElement = "student-primary-contact-number";
    string = "Primary Contact Number should be of 10 digits.";
  }

  var primaryEmail = $("#student-primary-email").val().trim();
  if(primaryEmail != "" && !regexEmail.test(primaryEmail)) {
    isError = true;
    impactedElement = "student-primary-email";
    string = "Type correct format of Primary Email";
  }

  var whatsappNumber = $("#student-whatsapp-number").val().trim();
  if(whatsappNumber != "" && !regexPhoneNumber.test(whatsappNumber)) {
    isError = true;
    impactedElement = "student-whatsapp-number";
    string = "Whatsapp Number should be of 10 digits.";
  }

  var isSponsored = $("#student-is-sponsored").is(":checked");
  var hosteller = $("#student-hosteller").is(":checked");

  if(isError) {
    if(impactedElement != null) {
      $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }

  // var instituteHouseId = $("#add-student-house").val();
  // if(instituteHouseId == undefined || instituteHouseId === "") {
  //   instituteHouseId = null;
  // }
  var instituteHouseId = null;
  var admissionInClass = $("#add-student-admission-in-class").val().trim();

  var studentNameAsPerAadhar = $("#add-student-name-as-per-aadhar").val().trim();

  var childCategoryCriteria = $("#add-child-category-criteria").val().trim();
  if(childCategoryCriteria == ""){
    childCategoryCriteria = null;
  }

  var speciallyAbledType = $("#add-specially-abled-type").val().trim();

  var penNumber = $("#add-pen-number").val().trim();

  var apaarIdNo = $("#add-apaar-id-number").val().trim();

  var studentBasicInfo = {'registrationRequestNumber' : registrationNumber + "-" + Math.random().toString(36),
     'registrationNumber' : registrationNumber, 'registrationDate' : registrationDateInt, 'admissionDate' : admissionDateInt,
     'name': name, 'gender' :gender, 'dateOfBirth' : dobInt, 'birthPlace' :  birthPlace, 'aadharNumber' : aadharNumber,
     'userCategory' : userCategory, 'religion' : religion, 'caste' : caste, 'rte' : isRTE, 'motherTongue' : motherTongue,
     'areaType' : areaType, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl, 'presentAddress' : presentAddress,
     'presentCity' : presentCity, 'presentState' : presentState, 'presentPostOffice' : presentPostOffice,
     'presentPoliceStation' : presentPoliceStation, 'presentZipcode' : presentZipcode, 'permanentAddress' : permanentAddress,
     'permanentCity' : city, 'permanentState' : state, 'permanentPostOffice' : postOffice,
     'permanentPoliceStation' : policeStation, 'permanentZipcode' : zipcode, 'nationality' : nationality,
     'primaryContactNumber' : primaryContactNumber, 'primaryEmail' : primaryEmail, 'isSponsored' : isSponsored, 'hosteller' : hosteller,
     'whatsappNumber' : whatsappNumber, 'instituteHouseId' : instituteHouseId, 'admissionInClass' : admissionInClass,
     'speciallyAbledType' : speciallyAbledType, 'studentNameAsPerAadhar' : studentNameAsPerAadhar, 'childCategoryCriteria' : childCategoryCriteria,
     'penNumber' : penNumber, 'apaarIdNo' : apaarIdNo};
  return studentBasicInfo;
}

function getStudentBasicInfoUpdate(){
  var registrationNumber = $("#update\\.student-registration-number").val().trim();
  var admissionNumber = $("#update\\.student-admission-number").val().trim();
  var penNumber = $("#update\\.pen-number").val().trim();
  var apaarIdNo = $("#update\\.apaar-id-number").val().trim();
  var admissionDate = getDate($("#update\\.student-admission-date").val());
  var registrationDate = getDate($("#update\\.student-registration-date").val());
  var name = $("#update\\.student-name").val().trim();
  var gender = $("#update\\.student-gender").val().trim();
  if(gender == ""){
    gender = null;
  }
  var dob = getDate($("#update\\.student-date-of-birth").val());
  var birthPlace = $("#update\\.student-birth-place").val().trim();
  var userCategory = $("#update\\.student-category").val().trim();
  if(userCategory == ""){
    userCategory = null;
  }
  var religion = $("#update\\.student-religion").val().trim();
  var caste = $("#update\\.student-caste").val().trim();
  var isRTE = $("#update\\.student-rte").is(":checked");
  var motherTongue = $("#update\\.student-mother-tongue").val().trim();
  var areaType = $("#update\\.student-area-type").val().trim();
  if(areaType == ""){
    areaType = null;
  }
  var speciallyAbled = $("#update\\.student-specially-abled").is(":checked");
  var bpl = $("#update\\.student-bpl").is(":checked");

  var presentAddress = $("#update\\.student-present-address").val().trim();
  var presentCity = $("#update\\.student-present-city").val().trim();
  var presentState = $("#update\\.student-present-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#update\\.student-present-state").find(".country-states").find(':selected').val().trim();
  var presentPostOffice = $("#update\\.student-present-post-office").val().trim();
  var presentPoliceStation = $("#update\\.student-present-police-station").val().trim();
  var presentZipcode = $("#update\\.student-present-zipcode").val().trim();


  var permanentAddress = $("#update\\.student-permanent-address").val().trim();
  var city = $("#update\\.student-city").val().trim();
  var state = $("#update\\.student-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#update\\.student-state").find(".country-states").find(':selected').val().trim();
  var postOffice = $("#update\\.student-post-office").val().trim();
  var policeStation = $("#update\\.student-police-station").val().trim();
  var zipcode = $("#update\\.student-zipcode").val().trim();

  var registrationDateInt = null;
  if(registrationDate != null){
    registrationDateInt = registrationDate.getTime()/1000;
  }
  var admissionDateInt = null;
  if(admissionDate != null){
    admissionDateInt = admissionDate.getTime()/1000;
  }
  var dobInt = null;
  if(dob != null){
    dobInt = dob.getTime()/1000;
  }

  var isError = false;
  var impactedElement = null;
  var aadharNumber = $("#update\\.student-aadhar-number").val().trim();
  if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
    isError = true;
    impactedElement = "student-aadhar-number";
    string = "Invalid Aadhar Number";
  }

  var nationality = $("#update\\.student-nationality").val().trim();

  var primaryContactNumber = $("#update\\.student-primary-contact-number").val().trim();
  if(primaryContactNumber != "" && !regexPhoneNumber.test(primaryContactNumber)) {
    isError = true;
    impactedElement = "student-primary-contact-number";
    string = "Primary Contact Number should be of 10 digits.";
  }

  var primaryEmail = $("#update\\.student-primary-email").val().trim();
  if(primaryEmail != "" && !regexEmail.test(primaryEmail)) {
    isError = true;
    impactedElement = "student-primary-email";
    string = "Type correct format of Primary Email";
  }

  var whatsappNumber = $("#update\\.student-whatsapp-number").val().trim();
  if(whatsappNumber != "" && !regexPhoneNumber.test(whatsappNumber)) {
    isError = true;
    impactedElement = "student-whatsapp-number";
    string = "Whatsapp Number should be of 10 digits.";
  }

  var isSponsored = $("#update\\.student-is-sponsored").is(":checked");
  var hosteller = $("#update\\.student-hosteller").is(":checked");

  if(isError) {
    if(impactedElement != null) {
      $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }


  var instituteHouseId = $("#update\\.student-house").val();
  if(instituteHouseId == undefined || instituteHouseId === "") {
    instituteHouseId = null;
  }
  var admissionInClass = $("#update\\.student-admission-in-class").val().trim();

  var studentNameAsPerAadhar = $("#update\\.student-name-as-per-aadhar").val().trim();

  var childCategoryCriteria = $("#update\\.child-category-criteria").val();
  if(childCategoryCriteria == ""){
    childCategoryCriteria = null;
  } else {
    childCategoryCriteria = childCategoryCriteria.trim();
  }

  var speciallyAbledType = $("#update\\.specially-abled-type").val().trim();

  var studentBasicInfo = {'registrationNumber' : registrationNumber, 'admissionNumber' : admissionNumber, 'penNumber' : penNumber, 'apaarIdNo' : apaarIdNo, 'registrationDate' : registrationDateInt, 'admissionDate' : admissionDateInt, 'name': name,
   'gender' :gender, 'dateOfBirth' : dobInt, 'birthPlace' :  birthPlace,
   'aadharNumber' : aadharNumber, 'userCategory' : userCategory, 'religion' : religion, 'caste' : caste, 'rte' : isRTE,
   'motherTongue' : motherTongue, 'areaType' : areaType, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl,
   'presentAddress' : presentAddress, 'presentCity' : presentCity, 'presentState' : presentState,
   'presentPostOffice' : presentPostOffice, 'presentPoliceStation' : presentPoliceStation, 'presentZipcode' : presentZipcode,
   'permanentAddress' : permanentAddress, 'permanentCity' : city, 'permanentState' : state, 'permanentPostOffice' : postOffice,
   'permanentPoliceStation' : policeStation, 'permanentZipcode' : zipcode, 'nationality' : nationality,
   'primaryContactNumber' : primaryContactNumber, 'primaryEmail' : primaryEmail, 'isSponsored' : isSponsored,'hosteller' : hosteller,
   'whatsappNumber' : whatsappNumber, 'instituteHouseId' : instituteHouseId, 'admissionInClass' : admissionInClass,
   'speciallyAbledType' : speciallyAbledType, 'studentNameAsPerAadhar' : studentNameAsPerAadhar,
   'childCategoryCriteria' : childCategoryCriteria};
  return studentBasicInfo;
}

function getStudentFamilyInfo(){

  var motherName = $("#student-mother-name").val().trim();
  var fatherName = $("#student-father-name").val().trim();

  var mothersQualification = $("#student-mother-qualification").val().trim();
  var fathersQualification = $("#student-father-qualification").val().trim();


  var isError = false;
  var impactedElement = null;

  var motherContactNumber = $("#mother-contact-number").val().trim();
  if(motherContactNumber != "" && !regexPhoneNumber.test(motherContactNumber)) {
    isError = true;
    impactedElement = "mother-contact-number";
    string = "Mother Contact Number should be of 10 digits.";
  }

  var fatherContactNumber = $("#father-contact-number").val().trim();
  if(fatherContactNumber != "" && !regexPhoneNumber.test(fatherContactNumber)) {
    isError = true;
    impactedElement = "father-contact-number";
    string = "Father Contact Number should be of 10 digits.";
  }

  var motherOccupation = $("#mother-occupation").val().trim();
  var fatherOccupation = $("#father-occupation").val().trim();
  var motherAadharNumber = $("#mother-aadhar-number").val().trim();
  if(motherAadharNumber != "" && !regexAadharNumber.test(motherAadharNumber)) {
    isError = true;
    impactedElement = "mother-aadhar-number";
    string = "Invalid Mother Aadhar Number";
  }
  var fatherAadharNumber = $("#father-aadhar-number").val().trim();
  if(fatherAadharNumber != "" && !regexAadharNumber.test(fatherAadharNumber)) {
    isError = true;
    impactedElement = "father-aadhar-number";
    string = "Invalid Father Aadhar Number";
  }

  var mothersPanCardDetails = $("#mother-pan-card-details").val().trim();

  var fathersPanCardDetails = $("#father-pan-card-details").val().trim();

  var approxFamilyIncome = $("#approx-family-income").val().trim();
  if(approxFamilyIncome < 0) {
    isError = true;
    impactedElement = "approx-family-income";
    string = "Please put the proper value for the income";
  }

  var motherAnnualIncome = $("#mother-annual-income").val().trim();
  if(motherAnnualIncome < 0) {
      isError = true;
      impactedElement = "mother-annual-income";
      string = "Please put the proper value for the income";
  }

  var fatherAnnualIncome = $("#father-annual-income").val().trim();
  if(fatherAnnualIncome < 0) {
       isError = true;
       impactedElement = "father-annual-income";
       string = "Please put the proper value for the income";
  }

  if(isError) {
  if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
  }
    showErrorDialogBox(string);
    return;
  }

  var studentFamilyInfo = {'mothersName' : motherName, 'fathersName' : fatherName,
  'mothersQualification' : mothersQualification, 'fathersQualification' : fathersQualification,
  'mothersContactNumber' : motherContactNumber, 'fathersContactNumber' : fatherContactNumber,
  'mothersOccupation' : motherOccupation, 'mothersAnnualIncome' : motherAnnualIncome, 'fathersOccupation' : fatherOccupation,
  'fathersAnnualIncome' : fatherAnnualIncome, 'mothersAadharNumber' : motherAadharNumber, 'fathersAadharNumber' : fatherAadharNumber,
  'mothersPanCardDetails':mothersPanCardDetails,'fathersPanCardDetails':fathersPanCardDetails,
  'approxFamilyIncome' : approxFamilyIncome};

  return studentFamilyInfo;
}

function getStudentGuardianInfo(){
  var isError = false;
  var impactedElement = null;

  var guardianName = $("#guardian-name").val().trim();
  var guardianRelation = $("#guardian-relation").val().trim();
  var guardianAge = $("#guardian-age").val().length == 0 ? null : $("#guardian-age").val().trim();
  if(guardianAge != null && !regexAge.test(guardianAge)) {
    isError = true;
    impactedElement = "guardian-age";
    string = "Invalid Age";
  }
  var guardianGender = $("#guardian-gender").val().trim();
  var guardianOccupation = $("#guardian-occupation").val().trim();
  var guardianEmail = $("#guardian-email").val().trim();
  if(guardianEmail != "" && !regexEmail.test(guardianEmail)) {
    isError = true;
    impactedElement = "guardian-email";
    string = "Type correct format of guardian Email";
  }
  var guardianContactNumber = $("#guardian-contact-number").val().trim();
  if(guardianContactNumber != "" && !regexPhoneNumber.test(guardianContactNumber)) {
    isError = true;
    impactedElement = "guardian-contact-number";
    string = "Guardian contact Number should be of 10 digits.";
  }
  var guardianAddress = $("#guardian-address").val().trim();
  var guardianCity = $("#guardian-city").val().trim();
  var guardianState = $("#guardian-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#guardian-state").find(".country-states").find(':selected').val().trim();
  var guardianZipcode = $("#guardian-zipcode").val().trim();



  var studentGuardianInfo = {};

  if(isError) {
    if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }

  if(guardianGender != ''){
    studentGuardianInfo = {'guardianName' : guardianName, 'relation' : guardianRelation,
    'age' : guardianAge, 'gender' : guardianGender, 'occupation' : guardianOccupation,
    'email' : guardianEmail, 'contactNumber' : guardianContactNumber, 'occupation' : guardianOccupation,
    'address' : guardianAddress, 'city' : guardianCity, 'state' : guardianState, 'zipcode' : guardianZipcode};
  } else{
    studentGuardianInfo = {'guardianName' : guardianName, 'relation' : guardianRelation,
    'age' : guardianAge, 'occupation' : guardianOccupation,
    'email' : guardianEmail, 'contactNumber' : guardianContactNumber, 'occupation' : guardianOccupation,
    'address' : guardianAddress, 'city' : guardianCity, 'state' : guardianState, 'zipcode' : guardianZipcode};
  }
  return studentGuardianInfo;
}

function getStudentPreviousSchoolInfo(){
  var isError = false;
  var impactedElement = null;
  var isAdmissionTcBased = $("#student-is-admission-tc-based").is(":checked");
  var tcNumber = $("#previous-school-tc-number").val().trim();
  var previousSchoolName = $("#previous-school-name").val().trim();
  var medium = $("#previous-school-medium").val().trim();
  var classPassed = $("#class-passed").val().trim();
  var yearOfPassing = $("#year-of-passing").val().length == 0 ? null : $("#year-of-passing").val().trim();
  if(yearOfPassing < 0) {
    isError = true;
    impactedElement = "year-of-passing";
    string = "Invalid Year of Passing";
  }
  var result = $("#result").val().trim();
  var percentage = $("#percentage").val().trim();
  if(isError) {
  if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
  }
    showErrorDialogBox(string);
    return;
  }
  var studentPreviousSchoolInfo = {'isAdmissionTcBased' : isAdmissionTcBased, 'tcNumber' : tcNumber,
    'schoolName' : previousSchoolName, 'medium' : medium, 'classPassed' : classPassed,
    'yearOfPassing' : yearOfPassing, 'result' : result, 'percentage' : percentage};
  return studentPreviousSchoolInfo;
}

function getStudentMedicalInfo(){
  var bloodGroup = $("#blood-group").val().trim();
  var bloodPressure = $("#blood-pressure").val().trim();
  var pulse = $("#pulse").val().trim();
  var height = $("#height").val().trim();
  var weight = $("#weight").val().length == 0 ? null : $("#weight").val().trim();
  var dateOfPhysicalExamination = getDate($("#date-of-physical-examination").val());
  var dateOfPhysicalExaminationInt = null;
  if(dateOfPhysicalExamination != null){
    dateOfPhysicalExaminationInt = dateOfPhysicalExamination.getTime()/1000;
  }

  var studentMedicalInfo = {};
  if(bloodGroup == ''){
    studentMedicalInfo = {'bloodPressure' : bloodPressure,
     'pulse' : pulse, 'height' : height, 'weight' : weight, 'dateOfPhysicalExamination' : dateOfPhysicalExamination};
  } else{
    studentMedicalInfo = {'bloodGroup' : bloodGroup, 'bloodPressure' : bloodPressure,
     'pulse' : pulse, 'height' : height, 'weight' : weight, 'dateOfPhysicalExamination' : dateOfPhysicalExaminationInt};
  }
  return studentMedicalInfo;
}

function updateStudent(finish) {
    updateStudentWithScreen(finish, 'STUDENT-LIST');
}

function updateStudentWithScreen(finish, screenName) {
  var invalid = validateMandatoryFields($("#update-basic-info-content"));
  invalid |= validateMandatoryFields($("#update-family-info-content"));
  invalid |= validateMandatoryFields($("#update-guardian-info-content"));
  invalid |= validateMandatoryFields($("#update-previous-school-info-content"));
  invalid |= validateMandatoryFields($("#update-medical-info-content"));
  if(invalid){
    showErrorDialogBox("Please fill mandatory fields.")
    return;
  }

  var documentName = "";
  var file = "";
  // var ONE_KB = 1024;
  // if (($("#update-student-photo"))[0].files.length > 0) {
  //   file = ($("#update-student-photo"))[0].files[0];
  //     if((file.size / ONE_KB) > STUDENT_IMAGE_SIZE_LIMIT){
  //       showErrorDialogBox("Size Of document cannot be greater than " + STUDENT_IMAGE_SIZE_LIMIT + " kb");
  //       return;
  //     }
  // }

  var studentId = $("#update-admission-student-id").text().trim();
  var standardId = $("#update-student-class").find(':selected').val().trim();
  var sectionId = $("#update-student-section").find(':selected').val().trim();
  if(sectionId == ""){
    sectionId = null;
  }
  var admissionAcademicSessionId = $("#update-student-academic-session").find(':selected').val().trim();
  var studentGuardianInfoList = [];
  var studentBasicInfo = getStudentBasicInfoUpdate();
  if(studentBasicInfo === undefined) {
    return;
  }
  console.log(studentBasicInfo);
  var studentFamilyInfo = getStudentFamilyInfo();
  if(studentFamilyInfo === undefined) {
    return;
  }
  var studentGuardianInfo = getStudentGuardianInfo();
  if(studentGuardianInfo === undefined) {
    return;
  }
  var studentPreviousSchoolInfo = getStudentPreviousSchoolInfo();
  if(studentPreviousSchoolInfo === undefined) {
    return;
  }
  var studentMedicalInfo = getStudentMedicalInfo();
  if(studentMedicalInfo === undefined) {
    return;
  }
  studentGuardianInfoList.push(studentGuardianInfo);

  var collectedStudentDocumentType = getStudentDocumentInformation();

  var newAdmission = $("#update\\.student-new-admission").is(":checked");
  var updateStudentPayload = {'studentId' :studentId, 'standardId': standardId,
  'sectionId': sectionId, 'studentBasicInfo': studentBasicInfo, 'studentFamilyInfo': studentFamilyInfo,
  'studentGuardianInfoList': studentGuardianInfoList, 'studentPreviousSchoolInfo':studentPreviousSchoolInfo,
  'studentMedicalInfo':studentMedicalInfo, 'newAdmission' : newAdmission, 'collectedStudentDocumentType' : collectedStudentDocumentType};

  var formData = new FormData();
  if(file != "") {
    formData.append('document', file);
    formData.append('documentName', documentName);
  }

  formData.append('updateStudentPayload', JSON.stringify(updateStudentPayload));

  console.log(screenName);
  ajaxClient.uploadFile("/admission/update-student/"+admissionAcademicSessionId, formData, function(data) {
          $("#admission-status-modal-container").html(data);
          var response = JSON.parse($("#student-data-response").text());
          // $('#screen-name').text(screenName);
          if(response.success){
            if(finish){
              returnToMainScreen();
              $("#student-document-warning-popup").modal("toggle");
            }else{
                switchToNextTab();
            }
         } else {
           $("#admission-status-modal").modal('toggle');
         }

  });
}

function getStudentDocumentInformation() {
  var collectedStudentDocumentType = [];
  $("input.student-document-collected-checkbox").each(function() {
      if($(this).is(":checked")) {
        var documentType = $(this).parent().parent().parent().attr("id");
        collectedStudentDocumentType.push(documentType);
      }
  });
  return collectedStudentDocumentType;
}
