from math import *
from core.controller.user.institute import *

MAX_PAGE_COUNT_DISPLAY = 5;
FREQUENT_MODULES = ["FEES", "ADMISSION", "EXAMINATION", "STORE"]

def get_user_login_view(request):
    return request.session.get('user_login_view')

def get_user_theme(request):
    return request.session.get('dashboard_theme')

def set_user_theme_type(request, theme_type):
    request.session['dashboard_theme'] = {"theme_type" : theme_type}


def is_authorised_for_action(user_login_view, institute_unique_code, module, action):
    metadata_preferences = get_institute_metadata_preferences(user_login_view, institute_unique_code)
    if not metadata_preferences['enablePermission']:
        return True

    user_permissions = user_login_view['userPermissions']['modulePermissions']
    if module not in user_permissions:
        return False

    if action not in user_permissions[module]:
        return False
    else:
        if not user_permissions[module][action]['authorised']:
            return False

    return True

def get_grid(items, grid_row_count):
    rows = []
    count = 0
    row_count  = 0
    for item in items:
        if(count % grid_row_count == 0):
            rows.append([])
            row_count+=1
        row = rows[row_count-1]
        row.append(item)
        count += 1
    return rows

def get_pagination_details(pagination_info):
    total_items = pagination_info['totalItems']
    items_per_page = pagination_info['itemsPerPage']
    offset = pagination_info['offset']
    total_pages = 0
    total_pages = ceil(total_items/items_per_page)
    current_page = ceil(offset/items_per_page) + 1
    prev_element_count =  ceil(MAX_PAGE_COUNT_DISPLAY/2) - 1
    next_element_count =  floor(MAX_PAGE_COUNT_DISPLAY/2)

    first_page = 1
    last_page = total_pages
    if total_pages - (current_page + next_element_count) >= 0 and current_page - prev_element_count > 0 :
        first_page = current_page - prev_element_count
        last_page = current_page + next_element_count
    elif total_pages - (current_page + next_element_count) >= 0 and current_page - prev_element_count <= 0 :
        first_page = 1
        last_page = min(total_pages, next_element_count + prev_element_count + 1)
    elif total_pages - (current_page + next_element_count) < 0 and current_page - prev_element_count > 0 :
        first_page = max(1, total_pages - (next_element_count + prev_element_count ))
        last_page = total_pages
    else:
        first_page = 1
        last_page = total_pages

    more_pages = False
    if(last_page < total_pages):
        more_pages = True

    page_number_range_list = []
    for page_number in range(first_page, last_page + 1):
        page_number_range_list.append(page_number)

    return {"pagination_info" : pagination_info, 'page_number_range_list' : page_number_range_list, 'more_pages':more_pages,'total_pages':total_pages}


def get_tutorial_video_details(modulesType, institute_unique_code, user_type):
    institute = get_institute_by_unique_code(institute_unique_code)
    institute_id = institute['instituteId']
    metadata_preferences = get_metadata_preferences(institute_id)
    if(not metadata_preferences['tutorialVideosEnabled']):
        return None

    module_video_details = metadata_preferences['tutorialVideoDetails']
    
    module_video_details = filter_videos_by_user_type(module_video_details, user_type)

    return module_video_details.get(modulesType, None)

def filter_videos_by_user_type(module_video_details, user_type):
    filtered_module_video_details = {}

    if not module_video_details:
        return filtered_module_video_details
    
    for module, videos in module_video_details.items():
        filtered_videos = [
            video for video in videos if user_type in video.get("userTypeList", [])
        ]
        if filtered_videos:
            filtered_module_video_details[module] = filtered_videos

    return filtered_module_video_details

def get_display_modules(modules, user_login_view, institute_unique_code):
    freq_modules = []
    rest_modules = []
    all_modules = []
    institute_authorized_module = get_institute_modules(user_login_view, institute_unique_code)
    modules = [value for value in institute_authorized_module if value in modules]
    for module in modules:
        if module is None:
            continue
        properties = get_module_display_properties(module, user_login_view['user']['instituteId'], institute_unique_code)
        if properties is None:
            continue

        display_module = {"moduleId" : module["moduleId"], "properties" : properties}

        if module["moduleId"] in FREQUENT_MODULES:
            freq_modules.append(display_module)
        else:
            rest_modules.append(display_module)

        all_modules.append(display_module)
    return {"freq_modules" : sort_freq_modules(freq_modules), "rest_modules" : sort_modules(rest_modules), "all_modules" : sort_modules(all_modules)}

def sort_modules(modules):
    return sorted(modules, key = lambda i: i['properties']['name'])

def sort_freq_modules(modules):
    sorted_list = []
    for freq_module in FREQUENT_MODULES:
        for module in modules:
            if freq_module == module["moduleId"]:
                sorted_list.append(module)
                break
    return sorted_list

def get_module_display_properties(module, institute_id, institute_unique_code):
    name = None
    url_path_name = None
    icon = None
    if module["moduleId"] == "ADMISSION":
        name = "Admission"
        url_path_name = "admission"
        icon = "bi bi-person-plus"
    elif module["moduleId"] == "FEES":
        name = "Fees"
        url_path_name = "fees"
        icon = "bi bi-bag-check"
    elif module["moduleId"] == "TRANSPORT":
        name = "Transport"
        url_path_name = "transport"
        icon = "bi bi-truck"
    elif module["moduleId"] == "EXAMINATION":
        name = "Examination"
        url_path_name = "examination"
        icon = "bi bi-clipboard"
    elif module["moduleId"] == "ATTENDANCE":
        name = "Attendance"
        url_path_name = "attendance"
        icon = "bi bi-check-square"
    elif module["moduleId"] == "STORE":
        name = "Inventory"
        url_path_name = "inventory"
        #
        # if institute_id == 10135 or institute_id == 10190 or institute_id == 700 or institute_id == 110 or institute_id == 702 or institute_id == 101:
        #     url_path_name = "inventory"
        # else:
        #     url_path_name = "store"
        icon = "bi bi-cart"
    elif module["moduleId"] == "COURSES":
        name = "Courses"
        url_path_name = "courses"
        icon = "bi bi-book"
    elif module["moduleId"] == "STAFF_MANAGEMENT":
        name = "Staff Management"
        url_path_name = "staff-management"
        icon = "bi bi-people"
    elif module["moduleId"] == "INCOME_EXPENSE":
        name = "Income & Expense"
        url_path_name = "income-expense"
        icon = "bi bi-clipboard-data"
    elif module["moduleId"] == "USER_MANAGEMENT":
        name = "User Management"
        url_path_name = "user-management"
        icon = "bi bi-person"
    elif module["moduleId"] == "SALARY_MANAGEMENT":
        name = "Salary Management"
        url_path_name = "salary-management"
        icon = "bi bi-wallet2"
    elif module["moduleId"] == "LECTURE_MANAGEMENT":
        name = "Lecture Manager"
        url_path_name = "lecture-management"
        icon = "bi bi-camera-reels"
    elif module["moduleId"] == "HOMEWORK_MANAGEMENT":
        name = "HW / CW Manager"
        url_path_name = "homework-management"
        icon = "bi bi-file-earmark-text"
    elif module["moduleId"] == "NOTICE_BOARD_MANAGEMENT":
        name = "Notice Board"
        url_path_name = "noticeboard"
        icon = "bi bi-file-text"
    elif module["moduleId"] == "STUDENT_MANAGEMENT":
        name = "Student Management"
        url_path_name = "student-management"
        icon = "bi bi-clipboard-check"
    elif module["moduleId"] == "AUDIT_LOGS":
        name = "Audit Logs"
        url_path_name = "audit-logs"
        icon = "bi bi-search"
    elif module["moduleId"] == "COMMUNICATION":
        name = "Communication"
        url_path_name = "communication"
        icon = "bi bi-chat"
    elif module["moduleId"] == "STAFF_ATTENDANCE":
        name = "Staff Attendance"
        url_path_name = "staff-attendance"
        icon = "bi bi-check-square"
    elif module["moduleId"] == "HOLIDAY_CALENDAR":
        name = "Calendar"
        url_path_name = "holiday-calendar"
        icon = "bi bi-check-square"
    elif module["moduleId"] == "TIMETABLE_MANAGEMENT":
        name = "Timetable Manager"
        url_path_name = "timetable-management"
        icon = "bi bi-calendar4-event"
    elif module["moduleId"] == "FRONT_DESK":
        name = "Front Desk"
        url_path_name = "frontdesk"
        icon = "bi bi-pip"
    elif module["moduleId"] == "MOBILE_APPLICATION_MANAGEMENT":
        name = "Mobile App Management"
        url_path_name = "mobileapp-management"
        icon = "bi bi-phone"
    elif module["moduleId"] == "STUDENT_DIARY":
        name = "Student Diary"
        url_path_name = "student-diary"
        icon = "bi bi-journal-album"
    elif module["moduleId"] == "INSTITUTE_MANAGEMENT":
        name = "Institute Management"
        url_path_name = "institute-management"
        icon = "bi bi-collection"
    elif module["moduleId"] == "LIBRARY_MANAGEMENT":
        name = "Library Management"
        url_path_name = "library-management"
        icon = "bi bi-journal-richtext"
    elif module["moduleId"] == "ONLINE_ASSESSMENT":
        name = "Assessment"
        url_path_name = "assessment"
        icon = "bi bi-journal-richtext"
    elif module["moduleId"] == "LESSON_PLAN":
        name = "Lesson Plan"
        url_path_name = "lesson-plan"
        icon = "bi bi-journal-richtext"
    elif module["moduleId"] == "STUDY_TRACKER":
        name = "Study Tracker"
        url_path_name = "study-tracker"
        icon = "bi bi-journal-richtext"
    elif module["moduleId"] == "LEAVE_MANAGEMENT":
        name = "Leave Management"
        url_path_name = "leave-management"
        icon = "bi bi-check-square"
    elif module["moduleId"] == "COMPLAINT_BOX":
        name = "Complaint Box"
        url_path_name = "complaint-box"
        icon = "bi bi-check-square"
    elif module["moduleId"] == "STAFF_DIARY":
        name = "Staff Diary"
        url_path_name = "staff-diary"
        icon = "bi bi-journal-check"
    elif module["moduleId"] == "PARENTS_APPOINTMENT":
        name = "Parents Appointment"
        url_path_name = "parents-appointment"
        icon = "bi bi-person-check"
    elif module["moduleId"] == "VISITORS_DESK":
        name = "Visitors Desk"
        url_path_name = "visitors-desk"
        icon = "bi bi-briefcase"
    elif module["moduleId"] == "HOSTEL_MANAGEMENT":
        name = "Hostel Management"
        url_path_name = "hostel-management"
        icon = "bi bi-building"
    elif module["moduleId"] == "STUDENT_FINANCE":
        name = "Student Finance"
        url_path_name = "student-finance"
        icon = "bi bi-wallet"

    if name is None or url_path_name is None:
        return None

    link = "/"+institute_unique_code+"/"+url_path_name+"/dashboard"
    return {"name" : name, "link" : link, "icon" : icon}
