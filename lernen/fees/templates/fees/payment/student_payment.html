{% if student %}
{% load currency_utils %}
{% load json %}
  <p id="student-fee-payment-details-json" style="display:none;"> {{fee_payment_details|jsonstr}}</p>
  <p id="restrict-session-fees-payment-json" style="display:none;"> {{restrict_session_fees_payment|jsonstr}}</p>
  <div class="container-fluid border rounded fee-payment-tab active" id='fee-payment-view-tab' style="width:100%; height:100%; background-color:#fff; overflow: hidden;">
        <br>
        <div style="float:left">
          {% if student.studentStatus == 'ENROLLED' %}
            <p style="color:black">Status : <span class="badge badge-success">ENROLLED</span> </p>
          {% elif student.studentStatus == 'ENROLMENT_PENDING' %}
            <p style="color:black">Status : <span class="badge badge-warning">ENROLMENT PENDING</span> </p>
          {% elif student.studentStatus == 'RELIEVED' %}
              <p style="color:black">Status : <span class="badge badge-danger">RELIEVED</span> </p>
          {% elif student.studentStatus == 'NSO' %}
              <p style="color:black">Status : <span class="badge badge-info">STRIKED OFF</span> </p>
          {% endif %}
        </div>
        <p style="display:none" id="challan-student-id"></p>
        <a style="text-decoration:underline;margin-left: 12px;" type="button" data-toggle="modal" data-keyboard="false" data-backdrop="static" onclick="generateFeeChallan('{{ student.studentId }}')">Download Challan</a>
          
        <div style="float:right">
          <div>
            {% if student.studentStatus == 'ENROLLED' %}
              <button type="button" class="btn btn-sm btn-outline-danger" data-toggle="modal" data-keyboard="false" data-backdrop="static" data-target="#student-wallet-recharge-modal">+ Recharge Student Wallet</button>
            {% endif %}
            {% if student.studentStatus != 'ENROLMENT_PENDING' %}
              <button type="button" class="btn btn-sm btn-outline-primary" onclick="studentWalletHandler.loadWalletTransactions()">Wallet Transactions</button>
            {% endif %}
          </div>
        </div>
        <div>
              <table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
                <thead>
                </thead>
                <tbody>
                  <tr>
                    {% if student.thumbnail %}<td scope="col" rowspan="2"><img src="data:image/jpg;base64, {{student.thumbnail}}"></td>{% endif %}
                    <th scope="col">Name</th>
                    <th scope="col" >Adm No.</th>
                    <th scope="col" >Father Name</th>
                    <th scope="col" >Class</th>
                    <th scope="col">Wallet(Rs)</th>
                    <th scope="col" colspan="2"></th>
                  </tr>
                    <tr>
                      <td style="display:none;">
                        <p id="fee-assign-view-student-id" style="display:none;"> {{student.studentId}} </p>
                      </td>
                      <td id="search-student-name"> {{student.studentBasicInfo.name}} {% if is_fee_payment_restricted %}
                          <div style="color: red; font-size: smaller;">
                              Fee Payment Restricted
                          </div>
                          {% endif %}</td>
                      <td id="search-admission-number">{{student.studentBasicInfo.admissionNumber}}</td>
                      <td >{{student.studentFamilyInfo.fathersName}}</td>
                      <td id="search-student-class">{{student.studentAcademicSessionInfoResponse.standard.displayNameWithSection}}</td>
                      {% if student.walletAmount < 0 %}
                        <td id="search-student-wallet-amount"><span style="color:red"><strong>{{student.walletAmount}}</strong></span></td>
                      {% else %}
                        <td id="search-student-wallet-amount">{{student.walletAmount}}</td>
                      {% endif %}
                      {% with student.studentAcademicSessionInfoResponse.academicSession.academicSessionId|stringformat:"s" as sessionId %}
                        <td>  <button style="width:100%;" type="button" class="btn btn-primary" id="collect-fee-button" onclick={{"feePayment.collectFeesDisplay('"|add:sessionId|add:"')"}} {% if is_fee_payment_restricted %} disabled {% endif %}>Collect {{student.studentAcademicSessionInfoResponse.academicSession.shortYearDisplayName}} Fees</button></td>
                      {% endwith %}
                      <td>  <button style="width:100%;" type="button" class="btn btn-primary" id="past-payment-button">Past Payments</button></td>
                    </tr>

                </tbody>
              </table>
          </div>
          {% if due_in_other_sessions  %}
          <span><p>*The due amounts below includes pending fees from both the <span style="color: red;">current and other sessions. </span></p></span>
          {% endif %}
        <div class=" p-3 mb-3 bg-white rounded " style="border: 1px solid #D3D3D3;">
          <div class="row">

            <div class="col-md-2">
              <div class="row">
                  <div class="col-md-3" >
                      <img src="https://assetsv5.embrate.com/static/core/images/banknotes-32.png">
                  </div>
                  <div class="col-md-9" >
                    <h5 >Total Fees</h5>
                    <h4> {{fee_payment_details.selected_session.total_assigned_amount|format_inr_currency_with_round}} </h4>
                  </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="row">
                  <div class="col-md-2" >
                      <img src="https://assetsv5.embrate.com/static/core/images/cash-receiving-32.png">
                  </div>
                  <div class="col-md-10" >
                    <h5 ><span style="color:green">Amount Collected</span></h5>
                    <h4 id="total-student-count"> <span style="color:green">{{fee_payment_details.selected_session.total_paid_amount|format_inr_currency_with_round}}</span> </h4>
                  </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="row">
                  <div class="col-md-2" >
                      <img src="https://assetsv5.embrate.com/static/core/images/procent-badge-32.png">
                      <!-- <i class="bi bi-percent" style="color: #3187c5;font-size: 2em;"></i> -->
                  </div>
                  <div class="col-md-1" >
                  </div>
                  <div class="col-md-8">
                    <h5 >Discount</h5>
                     <h4>{{fee_payment_details.selected_session.total_given_discount|format_inr_currency_with_round}}</h4>
                  </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="row">
                  <div class="col-md-2" >
                      <!-- <img src="https://assetsv5.embrate.com/static/core/images/error-4-32.png"> -->
                      <i class="bi bi-exclamation-octagon" style="color: red;font-size: 2em;"></i>
                  </div>
                  <div class="col-md-1" >
                  </div>
                  <div class="col-md-8">
                    {% if student.studentStatus == 'RELIEVED' %}
                      <h5 > <span style="color:red">Session Dues (Relaxed)</span></h5>
                    {% else %}
                        <h5 > <span style="color:red">Session Dues</span></h5>
                    {% endif %}

                      <h4 >  <span style="color:red">{{total_due_amount|format_inr_currency_with_round}}</span> </h4>
                  </div>
              </div>

            </div>

            <div class="col-md-3">
              <div class="row">
                  <div class="col-md-2" >
                      <!-- <img src="https://assetsv5.embrate.com/static/core/images/error-4-32.png"> -->
                      <i class="bi bi-clock-history" style="color: red;font-size: 2em;"></i>
                  </div>
                  <div class="col-md-1" >
                  </div>
                  <div class="col-md-8">
                    {% if student.studentStatus == 'RELIEVED' %}
                      <h5 > <span style="color:red">Due Fees Till Today (Relaxed)</span></h5>
                    {% else %}
                        <h5 > <span style="color:red">Due Fees Till Today</span></h5>
                    {% endif %}

                      <h4 >  <span style="color:red">{{total_due_amount_till_today|format_inr_currency_with_round}}</span> </h4>
                  </div>
              </div>

            </div>

          </div>
          <div class="collapse" id="feehead-payment-details-wrapper">
            <br />
            <div>
                <table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
                  <thead>
                    <tr>
                      <th scope="col" class="w-25">Fee Head</th>
                      <th scope="col" >Total Fees</th>
                      <th scope="col" >Amount Collected</th>
                      <th scope="col">Discount</th>
                      <th scope="col">Session Dues</th>
                      <th scope="col">Due Fees Till Today</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for total_fee_head_payment_details in fee_payment_details.selected_session.total_fee_head_payment_details_list %}
                      <tr>
                        <td > {{total_fee_head_payment_details.feeHeadConfiguration.feeHead}}</td>
                        <td > {{total_fee_head_payment_details.assignedAmount}}</td>
                        <td > {{total_fee_head_payment_details.paidAmount}}</td>
                        <td > {{total_fee_head_payment_details.totalDiscount}}</td>
                        <td > {{total_fee_head_payment_details.balanceAmount}}</td>
                        <td > {{total_fee_head_payment_details.feeHeadDueAmountTillToday}}</td>
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>
            </div>
            {% if student_assigned_discounts %}
              <br/>
              <div>
                <p>Following Discount Structures are assignment to student:</p>
                {% for student_assigned_discount in student_assigned_discounts %}
                  {% if student_assigned_discount.feeDiscountMetadata.discountStructureType == "CUSTOM" %}
                    <p>{{ forloop.counter }}. {{student_assigned_discount.feeDiscountMetadata.name}}</p>
                  {% else %}
                    <p>{{ forloop.counter }}. {{student_assigned_discount.feeDiscountMetadata.metadata.title}}</p>
                  {% endif %}
                {% endfor %}
              </div>
            {% endif %}
          </div>
          <div class="closed" style="display: flex;  align-items: center;  justify-content: center; cursor: pointer;" onclick="feePayment.showFeeHeadPaymentDetails(this)" data-toggle="collapse" href="#feehead-payment-details-wrapper">
              <i class="align-middle mr-2 fas fa-fw fa-angle-down arrow down"></i>
              <i class="align-middle mr-2 fas fa-fw fa-angle-up arrow up" style="display:none;"></i>
              <!-- <img class="arrow down"  src="https://assetsv5.embrate.com/static/core/images/arrow-204-24.png"> -->
              <!-- <img class="arrow up" style="display:none;" src="https://assetsv5.embrate.com/static/core/images/arrow-142-24.png"> -->
          </div>
        </div>
<br/>

        <div id="fee-payment-status-list">
          {% include 'fees/payment/fee_payment_status_list.html' %}
        </div>
      </div>

      <div class="fee-payment-tab" id='collect-fee-tab' style="display:none;">
        {% include 'fees/payment/take_fees_screen.html' %}
      </div>

      <div class="fee-payment-tab" id='sibling-collect-fee-tab' style="display:none;">
      </div>

      <div class="fee-payment-tab" id='fee-transactions-tab' style="display:none;">

        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="">
              <a href="#" onclick="returnToPaymentMainScreenDisplay()">
                <img class="sidebar_icon" src="https://assetsv5.embrate.com/static/core/images/back-button-20.png"></a>
            </li>
            <li class="breadcrumb-item"><a href="#">Fee Payment Transactions</a></li>
            <li class="breadcrumb-item " aria-current="page">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</li>
            <li class="breadcrumb-item " aria-current="page">{{student.studentAcademicSessionInfoResponse.standard.standardSectionList.0.sectionName}}</li>
            <li class="breadcrumb-item " aria-current="page">{{student.studentBasicInfo.admissionNumber}}</li>
            <li class="breadcrumb-item active" aria-current="page">{{student.studentBasicInfo.name}}</li>
          </ol>
        </nav>
        {% include 'fees/transactions/fee_payment_transaction_list.html' %}
        {% include 'fees/payment/cancel_transaction_modal.html' %}
      </div>

  <div class="modal fade" id="student-wallet-recharge-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalCenterTitle">Recharge student wallet</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <p style="display:none" id="wallet-recharge-student-id">{{student.studentId}}</p>
          <h5> <strong> Student : </strong> <span id="wallet-recharge-student-name-identifier"> {{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</span> </h5>
          <br />
          <div class="row">
            <div class="col-md-6">
                <label>Recharge Amount*</label>
                <input type="number" class="form-control mandatory-field" id="student-recharge-wallet-amount" placeholder="Wallet recharge amount">
            </div>
            <div class="col-md-6">
              <label>Payment Mode*</label>
              <select id="student-recharge-payment-mode" class="form-control mandatory-field">
                <option selected=""></option>
                <option value="CASH">Cash</option>
                <option value="ACCOUNT_TRANSFER">Account Transfer</option>
                <option value="CHEQUE">Cheque</option>
                <option value="DEMAND_DRAFT">Demand Draft</option>
                <option value="NETBANKING">Net Banking</option>
                <option value="CARD">Card</option>
                <option value="QR_PAYMENT">QR Payment</option>
                <option value="GOOGLE_PAY_UPI">GPay UPI</option>
                <option value="PHONE_PE_UPI">PhonePe UPI</option>
                <option value="PAYTM">Paytm</option>
                <option value="BHIM_UPI">BHIM UPI</option>
                <option value="OTHER_UPI">Other UPI</option>
                <option value="OTHER">Other</option>
              </select>
            </div>
          </div>
          <br />
          <div class="row">
            <div class="col-md-6">
              <label for="inputPassword4">Recharge Date*</label>
              <input type="text" class="form-control mandatory-field select-date" id="student-rechage-wallet-date" placeholder="Enter date ..." {% if institute_metadata_preferences.hideCurrentDateOnFeePayment %} {% else %} value="{{current_date}}" {% endif %}>
            </div>
          </div>
          <br />
          <div class="row">
            <div class="col-md-12">
                <label>Description</label>
                <textarea class="form-control" id="student-recharge-wallet-description" rows="2" placeholder="Description..."></textarea>
            </div>
          </div>
        </div>
        <br />

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary" onclick="studentWalletHandler.confirmWalletRecharge()">Recharge Student Wallet</button>
        </div>
      </div>
    </div>
  </div>

  <div id="student-wallet-transactions-modal-container"></div>
{% endif %}
