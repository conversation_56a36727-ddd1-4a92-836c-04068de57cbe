var graphLoadTimer,sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1,graphLoadInterval=100,STAFF_IMAGE_SIZE_LIMIT=500,regexEmail=/^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,regexPhoneNumber=/^\d{10}$/,regexAadharNumber=/^\d{12}$/,regexPANNumber=/[a-zA-z]{5}\d{4}[a-zA-Z]{1}/,OTHER_DOCUMENT_TYPE="OTHER",IN_TIME="In Time",OUT_TIME="Out Time",HALF_DAY_DURATION="Half Day Duration(in hrs)",FULL_DAY_DURATION="Full Day Duration(in hrs)",daysOfWeekMap=new Map([["MONDAY",0],["TUESDAY",1],["WEDNESDAY",2],["THURSDAY",3],["FRIDAY",4],["SATURDAY",5],["SUNDAY",6]]);$(document).ready(function(){menuLoader.registerSidebarMenu();var t=new URLSearchParams(window.location.search);if(actionType=t.get(ACTION_TYPE),""===actionType||null===actionType)homePage.initHomePage();else if("student-dossier"===actionType){$(".sidebar-nav").find(".sidebar-item").each(function(){$(this).removeClass("active")}),homePage.initHomePage();var e=window.location.href;url=removeURLParameter(e,ACTION_TYPE);let t={id:"100"};window.history.replaceState(t,e,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerConfigurationMenu(),menuLoader.registerAddStaffMenu(),menuLoader.registerJoinerMenu(),menuLoader.registerOnboardMenu(),menuLoader.registerBulkUpdateMenu(),menuLoader.registerRelieveMenu(),menuLoader.registerReportsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerConfigurationMenu:function(){$("#configurationNav").on("click",function(){configuration.loadConfigurationMainPage()})},registerAddStaffMenu:function(){$("#addStaffNav").on("click",function(){admitStaff.loadAdmitStaffMenu()})},registerJoinerMenu:function(){$("#joinerNav").on("click",function(){joinerStaff.loadJoinerStaffMainPage()})},registerOnboardMenu:function(){$("#onboardNav").on("click",function(){onboardStaff.loadOnboardStaffMainPage()})},registerBulkUpdateMenu:function(){$("#bulkUpdateNav").on("click",function(){bulkUpdateStaff.loadUpdateStaffMainPage()})},registerRelieveMenu:function(){$("#relieveNav").on("click",function(){relieveStaff.loadRelieveStaffMainPage()})},registerReportsMenu:function(){$("#reportsNav").on("click",function(){staffReports.loadReportsMenu()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession),homePage.displayDashboardContent(),homePage.onClickFeeStatsSwitchButton()},loadHomePage:function(){ajaxClient.get("/staff-management/home",function(t){$("#main-content").html(t),homePage.initHomePage()})},loadHomePageForSession:function(){ajaxClient.get("/staff-management/session-home",function(t){$("#staffmanagement-dashboard-session-content").html(t),homePage.displayDashboardContent(),homePage.onClickFeeStatsSwitchButton()})},onClickFeeStatsSwitchButton:function(){$("#staff-stats-switch-button").on("click",function(){$("#current-card-view-type").text()===TABULAR?($("#current-card-view-type").text(GRAPHICAL),$("#catogorywise-graph-header-text").text("Staff Distribution Category Wise (Graphical View)"),$(this).html("Tabular View"),homePage.displayDashboardContent(),$("#tabularview-catogorywise-staff-distribution").attr("style","display:none;"),$("#chartjs-staff-distribution").attr("style","display:block;")):($("#current-card-view-type").text(TABULAR),$("#catogorywise-graph-header-text").text("Staff Distribution Category Wise (Tabular View)"),$(this).html("Graphical View"),homePage.renderBarGraphTabularView(),$("#chartjs-staff-distribution").attr("style","display:none;"),$("#tabularview-catogorywise-staff-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;"))})},loadStaffList:function(t,e){var a=readJson("#home-page-stats");if(null!=a){var i,n='<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Sr No.</th><th scope="col">Staff ID</th><th scope="col">Staff Name</th><th scope="col">Gender</th><th scope="col">Primary Contact Number</th><th scope="col">Primary Email</th><th scope="col">Category</th><th scope="col">Department (Designation)</th></tr></thead><tbody>';null!==t?a.categoryWiseStaffDetailsMap.hasOwnProperty(t)?i=a.categoryWiseStaffDetailsMap[t]:a.genderWiseStaffDetailsMap.hasOwnProperty(t)&&(i=a.genderWiseStaffDetailsMap[t]):"PENDING_STAFF"==e?i=a.pendingStaffDetailsList:"TRANSPORT_STAFF"==e?i=a.transportStaffDetailsList:"TOTAL_ONBOARD_STAFF"==e&&(i=a.totalOnboardStaffDetailsList);for(var s=1,f=0;f<i.length;f++){var o=i[f].staffInstituteId,r=i[f].name,l=i[f].gender,d=i[f].primaryContactNumber,c=i[f].primaryEmail,u=i[f].staffCategory.staffCategoryName,p="",m="";for(let t=0;t<i[f].departmentDesignationNameList.length;t++)p+=m+i[f].departmentDesignationNameList[t],m=", ";n+='<tr><th scope="row">'+s+++"</th><td>"+o+"</td><td>"+r+"</td><td>"+l+"</td> <td>"+d+"</td> <td>"+c+"</td> <td>"+u+"</td> <td>"+p+"</td></tr>"}n+="</tbody></table>",$("#staff-data-display").html(n),$("#display-staff-data-modal").modal({backdrop:"static",keyboard:!1})}},renderBarGraphTabularView:function(){var t=$("#home-page-stats").text().trim(),e=JSON.parse(t);if(null!=e&&e.categoryWiseStaffDetailsMap){var a=0,i=0,n=e.totalOnboardStaff,s='<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Category</th><th scope="col">Count</th><th scope="col">Percentage %</th></tr></thead><tbody>';for(const[t,d]of Object.entries(e.categoryWiseStaffDetailsMap)){var f=t,o=d[0].staffCategory.staffCategoryName,r=d.length,l=d.length/n*100;s+='<tr><td scope="row" style="cursor: pointer" onclick=\'homePage.loadStaffList("'+f+"\", null)'>"+o+'</td><td style="cursor: pointer" onclick=\'homePage.loadStaffList("'+f+"\", null)'>"+r+'</td><td style="cursor: pointer" onclick=\'homePage.loadStaffList("'+f+"\", null)'>"+l.toFixed(2)+"</td></tr>",a+=r,i+=l}s+='<tr><th scope="row" style="cursor: pointer"onclick=\'homePage.loadStaffList(null,"TOTAL_ONBOARD_STAFF")\'>Grand Total </th><td style="cursor: pointer"onclick=\'homePage.loadStaffList(null,"TOTAL_ONBOARD_STAFF")\'>'+a+'</td><td style="cursor: pointer"onclick=\'homePage.loadStaffList(null,"TOTAL_ONBOARD_STAFF")\'>'+i.toFixed(2)+"</td></tr>",s+="</tbody></table>",$("#tabularview-catogorywise-staff-distribution").html(s)}},refreshHomePage:function(){homePage.loadHomePageForSession()},displayDashboardContent:function(){for(var t=$("#home-page-stats").text().trim(),e=JSON.parse(t),a=e.staffCategory,i={},n=0;n<a.length;n++)i[a[n].staffCategoryId]=a[n];var s=[],f=[],o=e.totalOnboardStaff,r=e.totalPendingStaff,l=e.totalTransportStaff,d=(n=0,e.staffCategoryOnboardStaffCount);$.each(d,function(t,e){s.push(i[t].staffCategoryName),f.push(e)});var c=[],u=[],p=e.staffGenderWiseCount;$.each(p,function(t,e){c.push(t),u.push(e)}),$("#total-staff-count").text(o),$("#pending-staff").text(r),$("#transport-staff").text(l),homePage.renderStaffCountChart(s,f),homePage.renderFeeCollectionPieChart(c,u)},renderStaffCountChart:function(t,e){new Chart($("#chartjs-staff-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Count",backgroundColor:window.theme.primary,borderColor:window.theme.primary,hoverBackgroundColor:window.theme.primary,hoverBorderColor:window.theme.primary,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{gridLines:{display:!1},stacked:!1,ticks:{stepSize:20},stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeeCollectionPieChart:function(t,e){new Chart($("#chartjs-fee-collection-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,window.theme.primary,"#108F2B","#C08143 ","#DCDF69","#719E90","#A8B9DF ","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"],borderWidth:5,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cutoutPercentage:70,legend:{display:!1}}})}};function previousTabSwtichingEvent(){$(".previous-switch-tab").on("click",function(){var t=$(".switch-tab.active").prev();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")})}function nextTabSwtichingEvent(){$(".next-switch-tab").on("click",function(){switchToNextTab()})}function switchToNextTab(){var t=$(".switch-tab.active").next();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")}function swtichingTostaffUpdateFormFirstTab(){var t=$(".switch-tab").first();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")}function closeModal(){clearMandatoryFieldsErrorDisplay()}function returnToMainScreen(t){"ADMIT-STAFF"==$("#screen-name").text().trim()?admitStaff.loadAdmitStaffMenu():(null==t&&(t=$("#staff-status-screen").text().trim()),$(".staff-details-screen").attr("style","display:none"),$("#staff-details").attr("style","display:block"),swtichingTostaffUpdateFormFirstTab(),"RELIEVED"===t?relieveStaff.loadRelieveStaffMainPage():"ONBOARD"===t?onboardStaff.loadOnboardStaffMainPage():joinerStaff.loadJoinerStaffMainPage())}function fillStaffInformation(t){$("#update-admission-staff-id").text(t.staffId),$("#update-staff-number").val(t.staffBasicDetailsWithCategoryDepartDesignation.staffInstituteId),$("#update-staff-name").val(t.staffBasicDetailsWithCategoryDepartDesignation.name),$("#update-staff-initials").val(t.staffBasicDetailsWithCategoryDepartDesignation.initials),$("#update-staff-gender").val(t.staffBasicDetailsWithCategoryDepartDesignation.gender),$("#update-staff-date-of-birth").val(getFormattedDate(t.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth)),$("#update-user-staff-category").val(t.staffBasicDetailsWithCategoryDepartDesignation.category),$("#update-staff-maritial-status").val(t.staffBasicDetailsWithCategoryDepartDesignation.maritalStatus),$("#update-staff-father-name").val(t.staffBasicDetailsWithCategoryDepartDesignation.fatherName),$("#update-staff-mother-name").val(t.staffBasicDetailsWithCategoryDepartDesignation.motherName),$("#update-staff-birth-place").val(t.staffBasicDetailsWithCategoryDepartDesignation.birthPlace),$("#update-staff-mother-tongue").val(t.staffBasicDetailsWithCategoryDepartDesignation.motherTongue),$("#update-staff-religion").val(t.staffBasicDetailsWithCategoryDepartDesignation.religion),$("#update-staff-specially-abled").prop("checked",t.staffBasicDetailsWithCategoryDepartDesignation.speciallyAbled),$("#update-staff-bpl").prop("checked",t.staffBasicDetailsWithCategoryDepartDesignation.bpl),$("#update-transport-staff").prop("checked",t.transportStaff),$("#update-staff-primary-email").val(t.staffBasicDetailsWithCategoryDepartDesignation.primaryEmail),$("#update-staff-secondary-email").val(t.staffBasicDetailsWithCategoryDepartDesignation.secondaryEmail),$("#update-staff-primary-contact-number").val(t.staffBasicDetailsWithCategoryDepartDesignation.primaryContactNumber),$("#update-staff-alternate-contact-number").val(t.staffBasicDetailsWithCategoryDepartDesignation.alternateContactNumber),$("#update-staff-aadhar-number").val(t.staffBasicDetailsWithCategoryDepartDesignation.aadharNumber),$("#update-staff-pan-number").val(t.staffBasicDetailsWithCategoryDepartDesignation.panNumber),$("#update-staff-category").val(t.staffBasicDetailsWithCategoryDepartDesignation.staffCategory.staffCategoryId),$("#update-staff-oasis-id").val(t.staffBasicDetailsWithCategoryDepartDesignation.oasisId),$("#update-staff-national-code").val(t.staffBasicDetailsWithCategoryDepartDesignation.nationalCode),$("#update-staff-appointment-type").val(t.staffBasicDetailsWithCategoryDepartDesignation.staffNatureOfAppointment);var e=JSON.parse($("#staff-department-json").text()),a=JSON.parse($("#staff-designation-json").text()),i=Object.keys(t.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID).length;if(i>0){var n="",s=1;$.each(t.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID,function(t,f){var o=getDepartmentHTML(e,t),r=getDesignationHTML(a,f);n+=1===i?'<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control update-staff-department">'+o+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control update-staff-designation">'+r+'</select></div><div class="form-group" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.addNewDepartmentDesignationUpdateRow(this);"><span aria-hidden="true">+</span></button></div></div>':s<i?'<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control update-staff-department">'+o+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control update-staff-designation">'+r+'</select></div><div class="form-group" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.addNewDepartmentDesignationUpdateRow(this);"><span aria-hidden="true">+</span></button></div></div></div>':'<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control update-staff-department">'+o+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control update-staff-designation">'+r+'</select></div><div class="form-group remove-class" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.removeDepartmentDesignationRow(this);"><span aria-hidden="true">x</span></button></div></div>',s+=1}),$("#basic-close-btn").before(n)}else{n='<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control update-staff-department">'+admitStaff.getDepartmentHTML(e)+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control update-staff-designation">'+admitStaff.getDesignationHTML(a)+'</select></div><div class="form-group" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.addNewDepartmentDesignationUpdateRow(this);"><span aria-hidden="true">+</span></button></div><div class="form-group remove-class" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.removeDepartmentDesignationRow(this);"><span aria-hidden="true">x</span></button></div></div>';$("#basic-close-btn").before(n)}$("#staff-present-address-1").val(t.staffAddressContactInfo.presentAddress1),$("#staff-present-address-2").val(t.staffAddressContactInfo.presentAddress2),$("#staff-present-city").val(t.staffAddressContactInfo.presentCity),$("#staff-present-state").find(".country-states").val(t.staffAddressContactInfo.presentState),$("#staff-present-country").find(".country").val(t.staffAddressContactInfo.presentCountry),$("#staff-present-zipcode").val(t.staffAddressContactInfo.presentZipcode),$("#staff-permanent-address-1").val(t.staffAddressContactInfo.permanentAddress1),$("#staff-permanent-address-2").val(t.staffAddressContactInfo.permanentAddress2),$("#staff-permanent-city").val(t.staffAddressContactInfo.permanentCity),$("#staff-permanent-state").find(".country-states").val(t.staffAddressContactInfo.permanentState),$("#staff-permanent-country").find(".country").val(t.staffAddressContactInfo.permanentCountry),$("#staff-permanent-zipcode").val(t.staffAddressContactInfo.permanentZipcode),$("#update-staff-emergancy-contact-number").val(t.staffAddressContactInfo.emergancyContactNumber),$("#staff-highest-qualification").val(t.staffQualificationExprienceInfo.highestQualification),$("#staff-experience").val(t.staffQualificationExprienceInfo.experience),$("#staff-last-organization-name").val(t.staffQualificationExprienceInfo.lastOrganizationName),$("#staff-last-organization-address").val(t.staffQualificationExprienceInfo.lastOrganizationAddress),$("#staff-last-designation").val(t.staffQualificationExprienceInfo.lastDesignation),$("#staff-last-job-duration").val(t.staffQualificationExprienceInfo.lastJobDuration),$("#staff-offer-acceptance-date").val(getFormattedDate(t.staffJoiningInfo.offerAcceptanceDate)),$("#staff-tentative-date-of-joining").val(getFormattedDate(t.staffJoiningInfo.tentativeDateOfJoining)),$("#staff-background-verification").prop("checked",t.staffJoiningInfo.backgroundVerification),$("#staff-service-start-date").val(getFormattedDate(t.startDate)),$("#staff-service-end-date").val(getFormattedDate(t.endDate)),$("#staff-rehire").prop("checked",t.staffJoiningInfo.rehire),$("#staff-absconding").prop("checked",t.staffJoiningInfo.absconding),$("#staff-account-type").val(t.staffBankInfo.accountType),$("#staff-bank-name").val(t.staffBankInfo.bankName),$("#staff-account-holder-name").val(t.staffBankInfo.accountHolderName),$("#staff-account-number").val(t.staffBankInfo.accountNumber),$("#staff-ifsc-code").val(t.staffBankInfo.ifscCode),$("#update-staff-pan-number").val(t.staffBankInfo.panNumber),$("#epf-consent-type").val(t.staffBankInfo.epfConsentType),$("#esic-consent-type").val(t.staffBankInfo.esicConsentType),$("#staff-blood-group").val(t.staffMedicalInfo.bloodGroup),$("#staff-blood-pressure").val(t.staffMedicalInfo.bloodPressure),$("#staff-pulse").val(t.staffMedicalInfo.pulse),$("#staff-height").val(t.staffMedicalInfo.height),$("#staff-weight").val(t.staffMedicalInfo.weight),$("#update-date-of-physical-examination").val(getFormattedDate(t.staffMedicalInfo.dateOfPhysicalExamination)),validateStaffTimingDetails(t.staffTimingDetails)&&(validateStaffTimingDetails(t.staffTimingDetails.inTime)&&$("#in-time").val(("0"+t.staffTimingDetails.inTime.hour).slice(-2)+":"+("0"+t.staffTimingDetails.inTime.minute).slice(-2)),validateStaffTimingDetails(t.staffTimingDetails.outTime)&&$("#out-time").val(("0"+t.staffTimingDetails.outTime.hour).slice(-2)+":"+("0"+t.staffTimingDetails.outTime.minute).slice(-2)),$("#staff-half-day-duration").val(t.staffTimingDetails.halfDayDuration),$("#staff-full-day-duration").val(t.staffTimingDetails.fullDayDuration),t.staffVisitingDaysList&&t.staffVisitingDaysList.forEach(function(t){var e=0,a="";t.visitingHoursList.forEach(function(t){var i=onboardStaff.generateStartAndEndTimeHTML(t.startDisplayTime.substring(0,5),t.endDisplayTime.substring(0,5),!1);a+="<tr>"+i+'<td  style=" vertical-align:  top; ">',e>0&&(a+='<button type="button" class="close" aria-label="Close" onclick="onboardStaff.deleteVisitingHours(this);" style="margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;"><span aria-hidden="true">×</span></button>'),a+="</td></tr>",e++});var i=onboardStaff.generateWeekDaysUpperHTML(t.visitingDay,"400px","150px"),n=onboardStaff.generateWeekDaysLowerHTML(t.visitingDay,"370px"),s=i+a+n;$("#staff-visiting-hours-div").append(s),$('#weekdays option[value="'+t.visitingDay+'"]').remove(),$("#weekdays").trigger("change"),$(".clockpicker").clockpicker({autoclose:!0,default:"now"})}))}function validateStaffTimingDetails(t){return null!=t&&null!=t&&""!=t}function fillStaffViewInformation(t){$("#detail-view-staff-number").text(t.staffBasicDetailsWithCategoryDepartDesignation.staffInstituteId),$("#detail-view-staff-name").text(t.staffBasicDetailsWithCategoryDepartDesignation.name),$("#detail-view-staff-initials").text(t.staffBasicDetailsWithCategoryDepartDesignation.initials),$("#detail-view-gender").text(t.staffBasicDetailsWithCategoryDepartDesignation.gender),null!=t.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth&&t.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth>0?$("#detail-view-dob").text(getFormattedDate(t.staffBasicDetailsWithCategoryDepartDesignation.dateOfBirth)):$("#detail-view-dob").text(""),$("#detail-view-category").text(t.staffBasicDetailsWithCategoryDepartDesignation.category),$("#detail-view-maritial-status").text(t.staffBasicDetailsWithCategoryDepartDesignation.maritalStatus),$("#detail-view-father-name").text(t.staffBasicDetailsWithCategoryDepartDesignation.fatherName),$("#detail-view-mother-name").text(t.staffBasicDetailsWithCategoryDepartDesignation.motherName),$("#detail-view-birth-place").text(t.staffBasicDetailsWithCategoryDepartDesignation.birthPlace),$("#detail-view-mother-tongue").text(t.staffBasicDetailsWithCategoryDepartDesignation.motherTongue),$("#detail-view-religion").text(t.staffBasicDetailsWithCategoryDepartDesignation.religion),$("#detail-view-specially-abled").text(t.staffBasicDetailsWithCategoryDepartDesignation.speciallyAbled?"Yes":"No"),$("#detail-view-bpl").text(t.staffBasicDetailsWithCategoryDepartDesignation.bpl?"Yes":"No"),$("#detail-view-transport-staff").text(t.transportStaff?"Yes":"No"),$("#detail-view-primary-email").text(t.staffBasicDetailsWithCategoryDepartDesignation.primaryEmail),$("#detail-view-secondary-email").text(t.staffBasicDetailsWithCategoryDepartDesignation.secondaryEmail),$("#detail-view-primary-contact-number").text(t.staffBasicDetailsWithCategoryDepartDesignation.primaryContactNumber),$("#detail-view-alternate-contact-number").text(t.staffBasicDetailsWithCategoryDepartDesignation.alternateContactNumber),$("#detail-view-aadhar-number").text(t.staffBasicDetailsWithCategoryDepartDesignation.aadharNumber),$("#detail-staff-oasis-id").text(t.staffBasicDetailsWithCategoryDepartDesignation.oasisId),$("#detail-staff-national-code").text(t.staffBasicDetailsWithCategoryDepartDesignation.nationalCode),$("#detail-staff-appointment-type").text(t.staffBasicDetailsWithCategoryDepartDesignation.staffNatureOfAppointmentDisplayName);var e=JSON.parse($("#staff-category-json").text()),a="";$.each(e,function(e,i){i.staffCategoryId===t.staffBasicDetailsWithCategoryDepartDesignation.staffCategory.staffCategoryId&&(a=i.staffCategoryName)}),$("#detail-view-staff-category").text(a);JSON.parse($("#staff-department-json").text()),JSON.parse($("#staff-designation-json").text());for(var i="",n=t.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationNameList,s=0;s<n.length;s++)i+=s>0?", "+n[s]:n[s];$("#detail-view-staff-department-designation").text(i),null!=t.staffImage?$("#detail-view-staff-image").text("Uploaded"):$("#detail-view-staff-image").text("Not present"),$("#detail-view-present-address-1").text(t.staffAddressContactInfo.presentAddress1),$("#detail-view-present-address-2").text(t.staffAddressContactInfo.presentAddress2),$("#detail-view-present-city").text(t.staffAddressContactInfo.presentCity),$("#detail-view-present-state").text(t.staffAddressContactInfo.presentState),$("#detail-view-present-country").text(t.staffAddressContactInfo.presentCountry),$("#detail-view-present-zipcode").text(t.staffAddressContactInfo.presentZipcode),$("#detail-view-permanent-address-1").text(t.staffAddressContactInfo.permanentAddress1),$("#detail-view-permanent-address-2").text(t.staffAddressContactInfo.permanentAddress2),$("#detail-view-permanent-city").text(t.staffAddressContactInfo.permanentCity),$("#detail-view-permanent-state").text(t.staffAddressContactInfo.permanentState),$("#detail-view-permanent-country").text(t.staffAddressContactInfo.permanentCountry),$("#detail-view-permanent-zipcode").text(t.staffAddressContactInfo.permanentZipcode),$("#detail-view-emergancy-contact-number").text(t.staffAddressContactInfo.emergancyContactNumber),$("#detail-view-highest-qualification").text(t.staffQualificationExprienceInfo.highestQualification),$("#detail-view-experience").text(t.staffQualificationExprienceInfo.experience),$("#detail-view-last-organization-name").text(t.staffQualificationExprienceInfo.lastOrganizationName),$("#detail-view-last-organization-address").text(t.staffQualificationExprienceInfo.lastOrganizationAddress),$("#detail-view-last-designation").text(t.staffQualificationExprienceInfo.lastDesignation),$("#detail-view-last-job-duration").text(t.staffQualificationExprienceInfo.lastJobDuration),null!=t.staffJoiningInfo.offerAcceptanceDate&&t.staffJoiningInfo.offerAcceptanceDate>0?$("#detail-view-offer-acceptance-date").text(getFormattedDate(t.staffJoiningInfo.offerAcceptanceDate)):$("#detail-view-offer-acceptance-date").text(""),null!=t.staffJoiningInfo.tentativeDateOfJoining&&t.staffJoiningInfo.tentativeDateOfJoining>0?$("#detail-view-tentative-date-of-joining").text(getFormattedDate(t.staffJoiningInfo.tentativeDateOfJoining)):$("#detail-view-tentative-date-of-joining").text(""),null!=t.staffJoiningInfo.relieveDate&&t.staffJoiningInfo.relieveDate>0?$("#detail-view-relieve-date").text(getFormattedDate(t.staffJoiningInfo.relieveDate)):$("#detail-view-relieve-date").text(""),$("#detail-view-background-verification").text(t.staffJoiningInfo.backgroundVerification?"Yes":"No"),null!=t.startDate&&t.startDate>0?$("#detail-view-service-start-date").text(getFormattedDate(t.startDate)):$("#detail-view-service-start-date").text(""),$("#detail-view-rehire").text(t.staffJoiningInfo.rehire?"Yes":"No"),$("#detail-view-absconding").text(t.staffJoiningInfo.absconding?"Yes":"No"),$("#detail-view-account-type").text(t.staffBankInfo.accountType),$("#detail-view-bank-name").text(t.staffBankInfo.bankName),$("#detail-view-account-holder-name").text(t.staffBankInfo.accountHolderName),$("#detail-view-account-number").text(t.staffBankInfo.accountNumber),$("#detail-view-ifsc-code").text(t.staffBankInfo.ifscCode),$("#detail-view-epf-consent-type").text(t.staffBankInfo.epfConsentType),$("#detail-view-esic-consent-type").text(t.staffBankInfo.esicConsentType),$("#detail-view-pan-number").text(t.staffBankInfo.panNumber);var f="";validateStaffTimingDetails(t.staffTimingDetails.inTime)&&(f=("0"+t.staffTimingDetails.inTime.hour).slice(-2)+":"+("0"+t.staffTimingDetails.inTime.minute).slice(-2)),$("#detail-in-time").text(f);var o="";validateStaffTimingDetails(t.staffTimingDetails.outTime)&&(o=("0"+t.staffTimingDetails.outTime.hour).slice(-2)+":"+("0"+t.staffTimingDetails.outTime.minute).slice(-2)),$("#detail-out-time").text(o),$("#detail-half-day-duration").text(t.staffTimingDetails.halfDayDuration),$("#detail-full-day-duration").text(t.staffTimingDetails.fullDayDuration),$("#view-collapse-staff-visiting-hours-info").empty(),t.staffVisitingDaysList&&t.staffVisitingDaysList.forEach(function(t){var e="";t.visitingHoursList.forEach(function(t){var a=onboardStaff.generateStartAndEndTimeHTML(t.startDisplayTime.substring(0,5),t.endDisplayTime.substring(0,5),!0);e+="<div><tr>"+a+"</tr></div>"});var a='<div class="col-md-12 mb-3 view-staff-visiting-hours-list"><div class="row view_visiting_hours"><div class="mb-3 col-md-2"><label>'+t.visitingDay.charAt(0).toUpperCase()+t.visitingDay.slice(1).toLowerCase()+'</label></div><div class="col-md-9"><div style="display: flex;"><table id="visiting-hours-'+t.visitingDay.toLowerCase()+'">'+e+"</table></div></div></div>";$("#view-collapse-staff-visiting-hours-info").append(a)})}function getDepartmentHTML(t,e){var a="<option selected></option>";return $.each(t,function(t,i){var n="";i.staffDepartmentId===e&&(n="selected"),a+="<option value="+i.staffDepartmentId+" "+n+">"+i.staffDepartmentName+"</option>"}),a}function getDesignationHTML(t,e){var a="<option selected></option>";return $.each(t,function(t,i){var n="";i.staffDesignationId===e&&(n="selected"),a+="<option value="+i.staffDesignationId+" "+n+">"+i.staffDesignationName+"</option>"}),a}function getDepartmentName(t,e){var a=null;return $.each(t,function(t,i){i.staffDepartmentId===e&&(a=i.staffDepartmentName)}),a}function getDesignationName(t,e){var a=null;return $.each(t,function(t,i){i.staffDesignationId===e&&(a=i.staffDesignationName)}),a}var configuration={loadConfigurationMainPage:function(){ajaxClient.get("/staff-management/configuration-details",function(t){$("#main-content").html(t),configuration.addCategory(),configuration.addDepartment(),configuration.addDesignation()})},loadCategoryDetails:function(){ajaxClient.get("/staff-management/category-details",function(t){$("#nav-staff-category").html(t),configuration.addCategory()})},addCategory:function(){$("#add-category-button").on("click",function(){var t=$("#category-name-textarea").val();if(""===t||null==t)return $("#category-name-textarea").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add category name!");var e={instituteId:null,staffCategoryName:t};ajaxClient.post("/staff-management/add-category",{staffCategory:JSON.stringify(e)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadCategoryDetails()})})},fillUpdateCategoryPopup:function(t){var e=JSON.parse($(t).parent().find(".category-info").text());$("#update-staff-category-id").text(e.staffCategoryId),$("#staff-category-name-text").val(e.staffCategoryName)},updateCategory:function(){var t=$("#update-staff-category-id").text(),e=$("#staff-category-name-text").val();if(""===e||null==e)return $("#staff-category-name-text").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add category name!");var a={instituteId:null,staffCategoryId:t,staffCategoryName:e};$("#update-category-popup").modal("toggle"),$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/update-category",{staffCategory:JSON.stringify(a)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadCategoryDetails()})},deleteCategoryPopup:function(t){var e=JSON.parse($(t).parent().find(".category-info").text());$("#delete-category-id").text(e.staffCategoryId)},deleteCategory:function(){var t=$("#delete-category-id").text();$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/delete-category/"+t,{},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadCategoryDetails()})},loadDepartmentDetails:function(){ajaxClient.get("/staff-management/department-details",function(t){$("#nav-staff-department").html(t),configuration.addDepartment()})},addDepartment:function(){$("#add-department-button").on("click",function(){var t=$("#department-name-textarea").val();if(""===t||null==t)return $("#department-name-textarea").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add department name!");var e={instituteId:null,staffDepartmentName:t};ajaxClient.post("/staff-management/add-department",{staffDepartment:JSON.stringify(e)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDepartmentDetails()})})},fillUpdateDepartmentPopup:function(t){var e=JSON.parse($(t).parent().find(".department-info").text());$("#update-staff-department-id").text(e.staffDepartmentId),$("#staff-department-name-text").val(e.staffDepartmentName)},updateDepartment:function(){var t=$("#update-staff-department-id").text(),e=$("#staff-department-name-text").val();if(""===e||null==e)return $("#staff-department-name-text").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add department name!");var a={instituteId:null,staffDepartmentId:t,staffDepartmentName:e};$("#update-department-popup").modal("toggle"),$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/update-department",{staffDepartment:JSON.stringify(a)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDepartmentDetails()})},deleteDepartmentPopup:function(t){var e=JSON.parse($(t).parent().find(".department-info").text());$("#delete-department-id").text(e.staffDepartmentId)},deleteDepartment:function(){var t=$("#delete-department-id").text();$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/delete-department/"+t,{},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDepartmentDetails()})},loadDesignationDetails:function(){ajaxClient.get("/staff-management/designation-details",function(t){$("#nav-staff-designation").html(t),configuration.addDesignation()})},addDesignation:function(){$("#add-designation-button").on("click",function(){var t=$("#designation-name-textarea").val();if(""===t||null==t)return $("#designation-name-textarea").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add designation name!");var e={instituteId:null,staffDesignationName:t};ajaxClient.post("/staff-management/add-designation",{staffDesignation:JSON.stringify(e)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDesignationDetails()})})},fillUpdateDesignationPopup:function(t){var e=JSON.parse($(t).parent().find(".designation-info").text());$("#update-staff-designation-id").text(e.staffDesignationId),$("#staff-designation-name-text").val(e.staffDesignationName)},updateDesignation:function(){var t=$("#update-staff-designation-id").text(),e=$("#staff-designation-name-text").val();if(""===e||null==e)return $("#staff-designation-name-text").css("border","1px solid #ff8795"),void showErrorDialogBox("Please add designation name!");var a={instituteId:null,staffDesignationId:t,staffDesignationName:e};$("#update-designation-popup").modal("toggle"),$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/update-designation",{staffDesignation:JSON.stringify(a)},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDesignationDetails()})},deleteDesignationPopup:function(t){var e=JSON.parse($(t).parent().find(".designation-info").text());$("#delete-designation-id").text(e.staffDesignationId)},deleteDesignation:function(){var t=$("#delete-designation-id").text();$(".modal-backdrop").remove(),ajaxClient.post("/staff-management/delete-designation/"+t,{},function(t){$("#configurations-status-modal-container").html(t),$("#configuration-status-modal").modal({backdrop:"static",keyboard:!1}),configuration.loadDesignationDetails()})}},admitStaff={loadAdmitStaffMenu:function(){joinerStaff.staffStatus="",onboardStaff.staffStatus="",relieveStaff.staffStatus="",ajaxClient.get("/staff-management/add-staff-view",function(t){$("#main-content").html(t),previousTabSwtichingEvent(),nextTabSwtichingEvent(),initSelect2("None"),onboardStaff.sameForAll(),enforceConstraints(),initDate(36500),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode")})},registerStaff:function(){var t=!1,e=null,a="";validateMandatoryFields($("#add\\.basic-info-content"))&&(t=!0,e=null,a="Please fill all Mandatory fields.");if(t)return null!=e&&$("#"+e).css("border","1px solid #ff8795"),void showErrorDialogBox(a);var i=admitStaff.getStaffBasicInfo();if(void 0!==i){var n={staffStatus:"JOINER",staffBasicInfo:i,staffAddressContactInfo:null,staffQualificationExprienceInfo:null,staffJoiningInfo:null,staffBankInfo:null,transportStaff:$("#transport-staff").is(":checked")},s=new FormData;0,s.append("registerStaffPayload",JSON.stringify(n)),ajaxClient.uploadFile("/staff-management/register-staff",s,function(t){$("#create-staff-confirm-modal-container").html(t);var e=JSON.parse($("#staff-data-response").text());e.success&&(fillStaffInformation(e.staff),$("#update-staff-previous-school-info").trigger("click"),$(".add-staff-screen").attr("style","display:none"),$(".update-staff-screen").attr("style","display:block"),$("#screen-name").text("ADMIT-STAFF")),enforceConstraints(),$("#admission-status-modal").modal("toggle")})}},getStaffBasicInfo:function(){var t=$("#staff-number").val(),e=$("#staff-name").val(),a=$("#staff-initials").val(),i=$("#staff-gender").val();""==i&&(i=null);var n=getDate($("#staff-date-of-birth").val()),s=null;null!=n&&(s=n.getTime()/1e3);var f=$("#user-staff-category").val();""==f&&(f=null);var o=$("#staff-maritial-status").val();""==o&&(o=null);var r=$("#staff-father-name").val(),l=$("#staff-mother-name").val(),d=$("#staff-birth-place").val(),c=$("#staff-mother-tongue").val(),u=$("#staff-religion").val(),p=$("#staff-specially-abled").is(":checked"),m=$("#staff-bpl").is(":checked"),g=!1,v=null,h=$("#staff-aadhar-number").val().trim();""==h||regexAadharNumber.test(h)||(g=!0,v="staff-aadhar-number",string="Invalid Aadhar Number");var D=$("#staff-alternate-contact-number").val().trim();""==D||regexPhoneNumber.test(D)||(g=!0,v="staff-alternate-contact-number",string="Alternate Contact Number should be of 10 digits.");var y=$("#staff-primary-contact-number").val().trim();regexPhoneNumber.test(y)||(g=!0,v="staff-primary-contact-number",string="Primary Contact Number should be of 10 digits."),y===D&&(g=!0,v=null,string="Primary Contact Number cannot be same as Alternate Contact Number");var b=$("#staff-secondary-email").val().trim();""==b||regexEmail.test(b)||(g=!0,v="staff-secondary-email",string="Type correct format of Secondary Email");var S=$("#staff-primary-email").val().trim();regexEmail.test(S)||(g=!0,v="staff-primary-email",string="Type correct format of Primary Email"),S===b&&(g=!0,v=null,string="Primary Email cannot be same as secondary Email");var x=$("#staff-oasis-id").val(),k=$("#staff-national-code").val(),C=$("#staff-appointment-type").val();if(g)return null!=v&&$("#"+v).css("border","1px solid #ff8795"),void showErrorDialogBox(string);var w=$("#staff-category").val();return departmentDesignationMapping={},$(".department-designation-mapping").each(function(){var t=$(this).find(".staff-department").val(),e=$(this).find(".staff-designation").val();departmentDesignationMapping[t]=e}),{staffInstituteId:t,name:e,initials:a,gender:i,dateOfBirth:s,staffCategoryId:w,departmentDesignationMapping:departmentDesignationMapping,category:f,maritalStatus:o,religion:u,fatherName:r,motherName:l,birthPlace:d,motherTongue:c,speciallyAbled:p,bpl:m,aadharNumber:h,primaryEmail:S,secondaryEmail:b,primaryContactNumber:y,alternateContactNumber:D,oasisId:x,nationalCode:k,staffNatureOfAppointment:C}},updateStaff:function(t){var e=!1,a=null,i="",n=validateMandatoryFields($("#update-basic-info-content"));n|=validateMandatoryFields($("#update-staff-address-contact-info-content")),n|=validateMandatoryFields($("#update-qualification-experience-info-content")),n|=validateMandatoryFields($("#update-joining-documents-info-content")),(n|=validateMandatoryFields($("#update-bank-info-content")))&&(e=!0,a=null,i="Please fill all Mandatory fields.");if(e)return null!=a&&$("#"+a).css("border","1px solid #ff8795"),void showErrorDialogBox(i);var s=$("#update-admission-staff-id").text().trim(),f=admitStaff.getStaffBasicInfoUpdate();if(void 0!==f){var o=admitStaff.getStaffAddressContactInfo(),r=admitStaff.getStaffQualificationExprienceInfo(),l=admitStaff.getStaffJoiningInfo(),d=admitStaff.getStaffBankInfo(),c=admitStaff.getStaffMedicalInfo(),u=admitStaff.getStaffTimingDetails(),p=admitStaff.getStaffVisitingDetails();if(void 0!==p&&void 0!==d){var m=getDate($("#staff-service-start-date").val()),g=null;null!=m&&(g=m.getTime()/1e3);var v=getDate($("#staff-service-end-date").val()),h=null;null!=v&&(h=v.getTime()/1e3);var D=""===relieveStaff.staffStatus?""===onboardStaff.staffStatus?joinerStaff.staffStatus:onboardStaff.staffStatus:relieveStaff.staffStatus;""===joinerStaff.staffStatus&&""===onboardStaff.staffStatus&&""===relieveStaff.staffStatus&&(D="JOINER");var y=$("#update-transport-staff").is(":checked"),b={instituteId:null,staffId:s,serviceStartDate:g,serviceEndDate:h,staffStatus:D,staffBasicInfo:f,staffAddressContactInfo:o,staffQualificationExprienceInfo:r,staffJoiningInfo:l,staffBankInfo:d,staffMedicalInfo:c,staffTimingDetails:u,transportStaff:y,staffVisitingDaysList:p},S=new FormData;0,S.append("updateStaffPayload",JSON.stringify(b)),ajaxClient.uploadFile("/staff-management/update-staff",S,function(e){$("#admission-status-modal-container").html(e),JSON.parse($("#staff-data-response").text()).success?t?(joinerStaff.staffStatus="",onboardStaff.staffStatus="",relieveStaff.staffStatus="",returnToMainScreen(D)):switchToNextTab():$("#admission-status-modal").modal("toggle")})}}},getStaffBasicInfoUpdate:function(){var t=$("#update-staff-number").val(),e=$("#update-staff-name").val(),a=$("#update-staff-initials").val(),i=$("#update-staff-gender").val();""==i&&(i=null);var n=getDate($("#update-staff-date-of-birth").val()),s=null;null!=n&&(s=n.getTime()/1e3);var f=$("#update-user-staff-category").val();""==f&&(f=null);var o=$("#update-staff-maritial-status").val();""==o&&(o=null);var r=$("#update-staff-father-name").val(),l=$("#update-staff-mother-name").val(),d=$("#update-staff-birth-place").val(),c=$("#update-staff-mother-tongue").val(),u=$("#update-staff-religion").val(),p=$("#update-staff-specially-abled").is(":checked"),m=$("#update-staff-bpl").is(":checked"),g=!1,v=null,h=$("#update-staff-aadhar-number").val().trim();""==h||regexAadharNumber.test(h)||(g=!0,v="update-staff-aadhar-number",string="Invalid Aadhar Number");var D=$("#update-staff-alternate-contact-number").val().trim();""==D||regexPhoneNumber.test(D)||(g=!0,v="update-staff-alternate-contact-number",string="Alternate Contact Number should be of 10 digits.");var y=$("#update-staff-primary-contact-number").val().trim();regexPhoneNumber.test(y)||(g=!0,v="update-staff-primary-contact-number",string="Primary Contact Number should be of 10 digits."),y===D&&(g=!0,v=null,string="Primary Contact Number cannot be same as Alternate Contact Number");var b=$("#update-staff-secondary-email").val().trim();""==b||regexEmail.test(b)||(g=!0,v="update-staff-secondary-email",string="Type correct format of Secondary Email");var S=$("#update-staff-primary-email").val().trim();if(regexEmail.test(S)||(g=!0,v="update-staff-primary-email",string="Type correct format of Primary Email"),S===b&&(g=!0,v=null,string="Primary Email cannot be same as secondary Email"),g)return null!=v&&$("#"+v).css("border","1px solid #ff8795"),void showErrorDialogBox(string);var x=$("#update-staff-category").val();departmentDesignationMapping={},$(".department-designation-mapping").each(function(){var t=$(this).find(".update-staff-department").val(),e=$(this).find(".update-staff-designation").val();departmentDesignationMapping[t]=e});var k=$("#update-staff-oasis-id").val(),C=$("#update-staff-national-code").val(),w=$("#update-staff-appointment-type").val();return{staffInstituteId:t,name:e,initials:a,gender:i,dateOfBirth:s,staffCategoryId:x,departmentDesignationMapping:departmentDesignationMapping,category:f,maritalStatus:o,religion:u,fatherName:r,motherName:l,birthPlace:d,motherTongue:c,speciallyAbled:p,bpl:m,aadharNumber:h,primaryEmail:S,secondaryEmail:b,primaryContactNumber:y,alternateContactNumber:D,oasisId:k,nationalCode:C,staffNatureOfAppointment:w}},getStaffAddressContactInfo:function(){var t=$("#staff-permanent-address-1").val(),e=$("#staff-permanent-address-2").val(),a=$("#staff-permanent-city").val(),i=$("#staff-permanent-state").find(".country-states").val();""==i&&(i=null);var n=$("#staff-permanent-country").find(".country").val();""==n&&(n=null);var s=$("#staff-permanent-zipcode").val(),f=$("#staff-present-address-1").val(),o=$("#staff-present-address-2").val(),r=$("#staff-present-city").val(),l=$("#staff-present-state").find(".country-states").val();""==l&&(l=null);var d=$("#staff-present-country").find(".country").val();return""==d&&(d=null),{permanentAddress1:t,permanentAddress2:e,permanentCity:a,permanentState:i,permanentCountry:n,permanentZipcode:s,presentAddress1:f,presentAddress2:o,presentCity:r,presentState:l,presentCountry:d,presentZipcode:$("#staff-present-zipcode").val(),emergancyContactNumber:$("#update-staff-emergancy-contact-number").val()}},getStaffQualificationExprienceInfo:function(){return{highestQualification:$("#staff-highest-qualification").val(),experience:$("#staff-experience").val(),lastOrganizationName:$("#staff-last-organization-name").val(),lastOrganizationAddress:$("#staff-last-organization-address").val(),lastDesignation:$("#staff-last-designation").val(),lastJobDuration:$("#staff-last-job-duration").val()}},getStaffJoiningInfo:function(){var t=getDate($("#staff-offer-acceptance-date").val()),e=null;null!=t&&(e=t.getTime()/1e3);var a=getDate($("#staff-tentative-date-of-joining").val()),i=null;null!=a&&(i=a.getTime()/1e3);var n=$("#staff-background-verification").is(":checked"),s=getDate($("#staff-relieve-date").val()),f=null;return null!=s&&(f=s.getTime()/1e3),{offerAcceptanceDate:e,tentativeDateOfJoining:i,backgroundVerification:n,relieveDate:f,rehire:$("#staff-rehire").is(":checked"),absconding:$("#staff-absconding").is(":checked")}},getStaffBankInfo:function(){var t=$("#staff-account-type").val(),e=$("#staff-bank-name").val(),a=$("#staff-account-holder-name").val(),i=$("#staff-account-number").val(),n=$("#staff-ifsc-code").val(),s=$("#epf-consent-type").val(),f=$("#esic-consent-type").val(),o=!1,r=null,l=$("#update-staff-pan-number").val().trim();return""==l||regexPANNumber.test(l)||(o=!0,r="update-staff-pan-number",string="Invalid PAN Number"),o?(null!=r&&$("#"+r).css("border","1px solid #ff8795"),void showErrorDialogBox(string)):{accountType:t,bankName:e,accountHolderName:a,accountNumber:i,ifscCode:n,panNumber:l,epfConsentType:s,esicConsentType:f}},getStaffMedicalInfo:function(){var t=$("#staff-blood-group").val(),e=$("#staff-blood-pressure").val(),a=$("#staff-pulse").val(),i=$("#staff-height").val(),n=$("#staff-weight").val(),s=getDate($("#update-date-of-physical-examination").val()),f=null;return null!=s&&(f=s.getTime()/1e3),{bloodGroup:t,bloodPressure:e,pulse:a,height:i,weight:n,dateOfPhysicalExamination:f}},getStaffTimingDetails:function(){var t=$("#in-time").val(),e=null;if(""!=t&&null!=t&&null!=t){e={hour:(n=t.split(":"))[0],minute:n[1],second:"00"}}var a=$("#out-time").val(),i=null;if(""!=a&&null!=a&&null!=a){var n;i={hour:(n=a.split(":"))[0],minute:n[1],second:"00"}}var s=$("#staff-half-day-duration").val();s=""!=s&&null!=s&&null!=s?parseFloat(s):null;var f=$("#staff-full-day-duration").val();return f=""!=f&&null!=f&&null!=f?parseFloat(f):null,staffTimingDetails={inTime:e,outTime:i,halfDayDuration:s,fullDayDuration:f},staffTimingDetails},getStaffVisitingDetails:function(){let t=[];var e=!1;if($(".staff-visiting-hours-list").each(function(){let a=$(this).find("h5").text().trim(),i=[];$(this).find("table tr").each(function(){let t=$(this).find(".start-time").val(),a=$(this).find(".end-time").val();var n=admitStaff.validateVisitingTimeDetails(t),s=admitStaff.validateVisitingTimeDetails(a);!0===n||!0===s?e=!0:i.push({startTime:n,endTime:s})}),t.push({visitingDay:a.toUpperCase(),visitingHoursList:i,recurringPattern:null})}),!e)return t;showErrorDialogBox("Please enter visiting hours correctly")},validateVisitingTimeDetails:function(t){if(""==t||null==t||null==t)return!0;var e=t.split(":"),a=e[0],i=e[1];return!!(isNaN(a)||isNaN(i)||a<0||a>=24||i<0||i>=60)||{hour:a,minute:i,second:"00"}},addNewDepartmentDesignationRow:function(t){var e=JSON.parse($("#staff-department-json").text()),a=JSON.parse($("#staff-designation-json").text()),i='<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control staff-department">'+admitStaff.getDepartmentHTML(e)+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control staff-designation">'+admitStaff.getDesignationHTML(a)+'</select></div><div class="form-group remove-class" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.removeDepartmentDesignationRow(this);"><span aria-hidden="true">x</span></button></div></div>';$("#register-staff-div").before(i)},addNewDepartmentDesignationUpdateRow:function(t){var e=JSON.parse($("#staff-department-json").text()),a=JSON.parse($("#staff-designation-json").text()),i='<div class="form-row department-designation-mapping"><div class="form-group col-md-4"><label>Staff Department</label><select class="form-control update-staff-department">'+admitStaff.getDepartmentHTML(e)+'</select></div><div class="form-group col-md-4"><label>Staff Designation</label><select class="form-control update-staff-designation">'+admitStaff.getDesignationHTML(a)+'</select></div><div class="form-group remove-class" style="float:left;padding-top:3.2%;padding-left:2%;"><button type="button" class="close" onclick="admitStaff.removeDepartmentDesignationRow(this);"><span aria-hidden="true">x</span></button></div></div>';$("#basic-close-btn").before(i)},getDepartmentHTML:function(t){var e="<option selected></option>";return $.each(t,function(t,a){e+="<option value="+a.staffDepartmentId+">"+a.staffDepartmentName+"</option>"}),e},getDesignationHTML:function(t){var e="<option selected></option>";return $.each(t,function(t,a){e+="<option value="+a.staffDesignationId+">"+a.staffDesignationName+"</option>"}),e},removeDepartmentDesignationRow:function(t){var e=0;$(t).parent().parent().parent().find(".department-designation-mapping").each(function(){e+=1}),e<=2?$(".remove-class").each(function(){$(t).parent().parent().remove(),$(this).remove()}):$(t).parent().parent().remove()}},joinerStaff={staffStatus:"",loadJoinerStaffMainPage:function(){ajaxClient.get("/staff-management/staff-details/JOINER",function(t){$("#main-content").html(t),joinerStaff.bindSearchStaffJoinerDetails(),joinerStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),joinerStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0),$(".selectpicker").selectpicker(),joinerStaff.onAuthorizedModuleChange(),$(".clockpicker").clockpicker({default:"now"}),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{targets:"no-sort",orderable:!1}],order:[[2,"asc"]]})})},loadJoinerStaffPageReload:function(){ajaxClient.get("/staff-management/staff-details-page-reload/JOINER",function(t){$("#search-joiner-staff-results").html(t),joinerStaff.refreshSearchFields(),joinerStaff.bindSearchStaffJoinerDetails(),joinerStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),joinerStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),initDateWithYearRange("-70:+10",!0)})},refreshSearchFields:function(){$("#joiner-staff-search-text").val(""),$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&$(this).prop("disabled",!0)})},bindSearchStaffJoinerDetails:function(){$("#joiner-staff-search").on("click",function(){joinerStaff.bindSearchStaffJoinerDetailsView()}),$("#joiner-staff-search-text").on("keyup",function(t){13==t.keyCode&&joinerStaff.bindSearchStaffJoinerDetailsView()}),$("#apply-staff-type-search-filter").on("click",function(){joinerStaff.bindSearchStaffJoinerDetailsView()})},bindSearchStaffJoinerDetailsView:function(){var t=$("#joiner-staff-search-text").val(),e=0;$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&(0==e?$(this).find(".staff-type:checked").attr("id").toString():","+$(this).find(".staff-type:checked").attr("id").toString(),e++)});var a="",i="",n="";if("none"!=$("#advance-search-option-div").css("display"))a=$("select.filter-category").val(),i=$("select.filter-department").val(),n=$("select.filter-designation").val();ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/JOINER?searchText="+t+"&category="+a+"&department="+i+"&designation="+n,function(t){$("#joiner-staff-list").html(t),joinerStaff.bindSearchStaffJoinerDetails(),joinerStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),joinerStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0)})},registerViewStaffDetailsCallBack:function(){$(".view-staff-info").on("click",function(){$("#staff-list-view-staff-screen").attr("style","display:block"),$("#view-staff-details-modal").modal("toggle");var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffViewInformation(JSON.parse(t))})},registerUpdateStaffDetailsCallBack:function(){$(".update-staff-info").on("click",function(){joinerStaff.staffStatus="JOINER";var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffInformation(JSON.parse(t)),enforceConstraints(),$(".staff-details-screen").attr("style","display:none"),$("#staff-list-update-staff-screen").attr("style","display:block"),$("#screen-name").text("STAFF-LIST"),$("#staff-status-screen").text("JOINER"),$("#update-staff-timing-content").attr("style","display:none"),$("#update-staff-timing").attr("style","display:none"),$("#staff-medical-info-finish-div").attr("style","display:block;float:right"),$("#staff-medical-info-save-next-div").attr("style","display:none;float:right"),$("#update-visiting-hours-content").attr("style","display:none"),$("#update-visiting-hours").attr("style","display:none")})},markStaffOnboard:function(){$("#staff-list-onboard-staff-screen").modal("toggle");var t=$("#joiner-staff-id").text(),e=$("#staff-create-user").is(":checked"),a=$("#organisation-details-json").text().trim(),i=$("#current-institute-id").text(),n=null;if("None"!=a){JSON.parse(a);if(n=$("select.filter-institute-scope").val(),jQuery.inArray(i,n)<=-1)return void showErrorDialogBox("Please select current institute in institute scope!")}var s=$("select.filter-authorized-module").val();if(e&&Array.isArray(s)&&!s.length)showErrorDialogBox("To create a user of staff, it should have access to atleast one module. Please select that from authorized modules drop down.");else{var f=$("select.filter-roles").val();if(e&&Array.isArray(f)&&!f.length)showErrorDialogBox("To create a user of staff, it should have access to atleast one role. Please select that from roles drop down.");else{user={instituteId:null,uuid:t,userType:"STAFF",authorizedModules:s,roles:f,instituteScope:n};var o=$("#in-time").val(),r=null;if(""!=o&&null!=o&&null!=o){r={hour:(c=o.split(":"))[0],minute:c[1],second:"00"}}var l=$("#out-time").val(),d=null;if(""!=l&&null!=l&&null!=l){var c;d={hour:(c=l.split(":"))[0],minute:c[1],second:"00"}}var u=$("#staff-half-day-duration").val();u=""!=u&&null!=u&&null!=u?parseFloat(u):null;var p=$("#staff-full-day-duration").val();p=""!=p&&null!=p&&null!=p?parseFloat(p):null,staffTimingDetails={inTime:r,outTime:d,halfDayDuration:u,fullDayDuration:p};var m={staffTimingDetails:staffTimingDetails,userPayload:user};ajaxClient.post("/staff-management/onboard-staff/"+t+"/"+e,{staffOnboardPayload:JSON.stringify(m)},function(t){$("#joiner-staff-confirmation-modal-container").html(t),$("#staff-joiner-status-modal").modal({backdrop:"static",keyboard:!1}),joinerStaff.loadJoinerStaffPageReload()})}}},populateMarkStaffOnboardStaffModal:function(t){$("#joiner-staff-id").text(t),$("#joiner-staff-details-modal-text").html("Are you sure to onboard staff? Please check below checkbox if you want to create staff's user.")},deleteStaff:function(){$("#staff-list-joiner-delete-staff-screen").modal("toggle");var t=$("#delete-joiner-staff-id").text();ajaxClient.get("/staff-management/delete-staff/"+t+"/JOINER",function(t){$("#joiner-staff-confirmation-modal-container").html(t),$("#staff-joiner-status-modal").modal({backdrop:"static",keyboard:!1}),joinerStaff.loadJoinerStaffPageReload()})},populateDeleteStaffModal:function(t){$("#delete-joiner-staff-id").text(t),$("#delete-joiner-staff-details-modal-text").html("Are you sure to delete staff details?")},onAuthorizedModuleChange:function(){$("select.filter-authorized-module").on("change",function(){authorizedModules=$(this).val(),rolesValue=$(this).parent().parent().parent().find("select.filter-roles").val();for(var t="",e=[],a=JSON.parse($("#role-module-mapping").text().trim()),i=0;i<authorizedModules.length;i++)for(var n=0;n<a.length;n++)authorizedModules[i]===a[n].module.moduleId&&(e.indexOf(a[n].roleId)>=0||(e.push(a[n].roleId),t+="<option value="+a[n].roleId+">"+a[n].roleName+"</option>"));""===t&&(t="<option> Please select any one of the Authorized Module!</option>"),t='<select class="form-control filter-roles selectpicker" multiple data-style="bg-white border" title="None">'+t+"</select>",$("div.filter-roles").remove(),$(".roles-label").after(t),$("select.filter-roles").val(rolesValue),$(".selectpicker").selectpicker()})}},onboardStaff={staffStatus:"",loadOnboardStaffMainPage:function(){ajaxClient.get("/staff-management/staff-details/ONBOARD",function(t){$("#main-content").html(t),onboardStaff.bindSearchStaffOnboardDetails(),onboardStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),onboardStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0),$(".selectpicker").selectpicker(),$(".clockpicker").clockpicker({default:"now"}),initSelect2("None"),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{targets:"no-sort",orderable:!1}],order:[[2,"asc"]]})})},loadOnboardStaffPageReload:function(){ajaxClient.get("/staff-management/staff-details-page-reload/ONBOARD",function(t){$("#search-onboard-staff-results").html(t),onboardStaff.refreshSearchFields(),onboardStaff.bindSearchStaffOnboardDetails(),onboardStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),onboardStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),initDateWithYearRange("-70:+10",!0)})},refreshSearchFields:function(){$("#onboard-staff-search-text").val(""),$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&$(this).prop("disabled",!0)})},bindSearchStaffOnboardDetails:function(){$("#onboard-staff-search").on("click",function(){onboardStaff.bindSearchStaffOnboardDetailsView()}),$("#onboard-staff-search-text").on("keyup",function(t){13==t.keyCode&&onboardStaff.bindSearchStaffOnboardDetailsView()}),$("#apply-staff-type-search-filter").on("click",function(){onboardStaff.bindSearchStaffOnboardDetailsView()})},bindSearchStaffOnboardDetailsView:function(){var t=$("#onboard-staff-search-text").val(),e=0;$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&(0==e?$(this).find(".staff-type:checked").attr("id").toString():","+$(this).find(".staff-type:checked").attr("id").toString(),e++)});var a="",i="",n="";if("none"!=$("#advance-search-option-div").css("display"))a=$("select.filter-category").val(),i=$("select.filter-department").val(),n=$("select.filter-designation").val();ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/ONBOARD?searchText="+t+"&category="+a+"&department="+i+"&designation="+n,function(t){$("#onboard-staff-list").html(t),onboardStaff.bindSearchStaffOnboardDetails(),onboardStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),onboardStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0)})},registerUpdateStaffDetailsCallBack:function(){$(".update-staff-info").on("click",function(){onboardStaff.staffStatus="ONBOARD";var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffInformation(JSON.parse(t)),onboardStaff.sameForAll(),enforceConstraints(),$(".staff-details-screen").attr("style","display:none"),$("#staff-list-update-staff-screen").attr("style","display:block"),$("#screen-name").text("STAFF-LIST"),$("#staff-status-screen").text("ONBOARD")})},registerViewStaffDetailsCallBack:function(){$(".view-staff-info").on("click",function(){$("#staff-list-view-staff-screen").attr("style","display:block"),$("#view-staff-details-modal").modal("toggle");var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffViewInformation(JSON.parse(t))})},markStaffRelieve:function(){$("#staff-list-relieve-staff-screen").modal("toggle");var t=$("#onboard-staff-id").text();ajaxClient.get("/staff-management/relieve-staff/"+t,function(t){$("#onboard-staff-confirmation-modal-container").html(t),$("#staff-onboard-status-modal").modal({backdrop:"static",keyboard:!1}),onboardStaff.loadOnboardStaffPageReload()})},populateMarkStaffRelieveStaffModal:function(t){$("#onboard-staff-id").text(t),$("#onboard-staff-details-modal-text").html("Are you sure to relieve staff?")},deleteStaff:function(){$("#staff-list-onboard-delete-staff-screen").modal("toggle");var t=$("#delete-onboard-staff-id").text();ajaxClient.get("/staff-management/delete-staff/"+t+"/ONBOARD",function(t){$("#onboard-staff-confirmation-modal-container").html(t),$("#staff-onboard-status-modal").modal({backdrop:"static",keyboard:!1}),onboardStaff.loadOnboardStaffPageReload()})},populateDeleteStaffModal:function(t){$("#delete-onboard-staff-id").text(t),$("#delete-onboard-staff-details-modal-text").html("Are you sure to delete staff details?")},loadWeekDays:function(){var t=$("#weekdays").val(),e=$(".onScreen").text(),a="VISITING_HOURS"==e?"270px":"400px",i="VISITING_HOURS"==e?"130px":"150px",n="VISITING_HOURS"==e?"220px":"370px";if(0==t.length)return"VISITING_HOURS"==e&&$("#bulk-update-staff-visiting-hours-details-modal").modal("toggle"),void showErrorDialogBox("Please select a day to load details.");for(var s=0;s<t.length;s++){var f=onboardStaff.generateStartAndEndTimeHTML("","",!1),o=onboardStaff.generateWeekDaysUpperHTML(t[s],a,i)+"<tr>"+f+"<td></td></tr>"+onboardStaff.generateWeekDaysLowerHTML(t[s],n);$("#staff-visiting-hours-div").append(o),$(".clockpicker").clockpicker({autoclose:!0,default:"now"}),$("#weekdays").val([]).trigger("change"),$('#weekdays option[value="'+t[s]+'"]').remove(),$("#weekdays").trigger("change")}},validateVisitingTime:function(t){var e=$(t).closest("tr"),a=e.find(".start-time"),i=$(t).val(),n=a.val(),s=(n.hour,e.closest("div").find("table").parent()),f=s.next(".error-message");if(n>=i)return e.find(".end-time").css("border","1px solid #ff8795"),void(0===f.length&&s.after('<div class="error-message" style="color: red; margin-top: 10px; text-align: center;">Start time should not be greater than or equal to end time.</div>'));e.find(".end-time").css("border",""),f.remove()},sameForAll:function(){$(document).on("click",".same-for-all",function(){var t=$(this).data("weekday"),e=$("#visiting-hours-"+t),a=[];e.find("tr").each(function(){var t=$(this).find(".start-time").val(),e=$(this).find(".end-time").val();t&&e&&a.push({startTime:t,endTime:e})}),0!==a.length?($(".staff-visiting-hours-list").each(function(){var e=$(this).find("h5").text().trim().toLowerCase();if(e!==t){var i=$("#visiting-hours-"+e);i.empty();var n=0;a.forEach(function(t){var e="<tr>"+onboardStaff.generateStartAndEndTimeHTML(t.startTime,t.endTime,!1)+'<td style="vertical-align: top;">';n>0&&(e+='<button type="button" class="close delete_stoppage" aria-label="Close" onclick="onboardStaff.deleteVisitingHours(this)" style="margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;"><span aria-hidden="true">×</span></button>'),e+="</td></tr>",n++,i.append(e)})}}),$(".clockpicker").clockpicker({autoclose:!0,default:"now"})):showErrorDialogBox("Please fill the visiting hours")})},addVisitingHours:function(t){var e="<tr>"+onboardStaff.generateStartAndEndTimeHTML("","",!1)+'<td  style=" vertical-align:  top; "><button type="button" class="close" aria-label="Close" onclick="onboardStaff.deleteVisitingHours(this);" style="margin-left: 10px; margin-top: 5px; vertical-align: middle; display: flex; align-items: center;"><span aria-hidden="true">×</span></button></td></tr>';$("#"+t).append(e),$(".clockpicker").clockpicker({autoclose:!0,default:"now"})},deleteVisitingHours:function(t){$(t).closest("tr").remove()},generateStartAndEndTimeHTML:function(t,e,a){const i=a?"readonly":"";return'<td><div class="input-group input-group-sm mb-3 "><input type="text" class="form-control form-control-sm time-hour hour-range clockpicker start-time" value="'+t+'" placeholder="" '+i+'></div></td><td><div style="margin : 10px;">to</div></td><td><div class="input-group input-group-sm mb-3"><input type="text" class="form-control form-control-sm time-hour hour-range clockpicker end-time"  value="'+e+'" placeholder="" onchange="onboardStaff.validateVisitingTime(this)" '+i+"></div></td>"},generateWeekDaysUpperHTML:function(t,e,a){return'<div class="card card-border text-center staff-visiting-hours-list" style="margin: auto; margin-bottom: 20px; width: 100%;"><div class="card-header-color" style="height: 50px;"><div class="col-md-12 mb-3 card-body"><div class="row add_visiting_hours"><div class="col-md-12 d-flex align-items-center"><div class="flex-grow-1 text-center"><h5 class="mb-0 card-title"><strong>'+t+'</strong></h5></div><button type="button" class="close delete_stoppage" style="position: absolute; right: 5px; margin-top: 0px;" onclick="onboardStaff.deleteVisitingDetails(this, \''+t+'\')" aria-label="Close"><span aria-hidden="true">&times;</span></button></div></div></div></div><div class="col-md-12 d-flex align-items-center" style="margin-top: 10px;"><div class="mb-2" style="margin-left:'+e+';"><label for="start-time">Start Time</label></div><div class="mb-2" style="margin-left:'+a+';"><label for="end-time">End Time</label></div></div><div class="col-md-12 d-flex justify-content-center align-items-center"><table id="visiting-hours-'+t.toLowerCase()+'" style="display: block;">'},generateWeekDaysLowerHTML:function(t,e){return'</table></div><div class="col-md-12 d-flex" style="margin: 15px;"><div class="d-flex align-items-center"><button type="button" class="btn btn-outline-primary btn-sm same-for-all" data-weekday="'+t.toLowerCase()+'">Same for All</button></div><button type="button" class="btn btn-outline-secondary btn-sm add_visiting_hours" style="margin-left:'+e+';"  onclick="onboardStaff.addVisitingHours(\'visiting-hours-'+t.toLowerCase()+"');\"> + Add More Visiting Hour</button></div></div>"},deleteVisitingDetails:function(t,e){$(t).parent().parent().parent().parent().parent().remove();e.charAt(0).toUpperCase(),e.slice(1).toLowerCase();var a=$("#weekdays option").map(function(){return $(this).val()}).get();a.push(e);var i=new Map;a.forEach(t=>{daysOfWeekMap.has(t)&&i.set(t,daysOfWeekMap.get(t))});const n=new Map([...i.entries()].sort((t,e)=>t[1]-e[1]));$("#weekdays").empty(),n.forEach((t,e)=>{var a='<option value="'+e+'">'+e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()+"</option>";$("#weekdays").append(a)}),$("#weekdays").trigger("change")}},bulkUpdateStaff={dataCache:{},loadUpdateStaffMainPage:function(){ajaxClient.get("/staff-management/bulk-update-staff-details/ONBOARD",function(t){$("#main-content").html(t),bulkUpdateStaff.bindBulkStaffDetailsOptions(),$(".selectpicker").selectpicker(),initSelect2("none"),initDateWithYearRange("-70:+10",!0),$(".clockpicker").clockpicker({default:"now"}),bulkUpdateStaff.bindUpdatingFieldTypeChangeEvent(),advanceSearch()})},bindBulkStaffDetailsOptions:function(){$("#bulk-staff-timing-details-option").on("click",function(){$("#bulk-staff-visiting-hours").attr("style","display:none;"),$("#bulk-staff-timing-details").attr("style","display:block;"),$(".onScreen").html("TIMING_DETAILS")}),$("#bulk-staff-visiting-hours-option").on("click",function(){$(".identity-card-screen").attr("style","display:none;"),$("#bulk-staff-visiting-hours").attr("style","display:block;"),$(".onScreen").html("VISITING_HOURS"),bulkUpdateStaff.bindBulkUpdateOnboardStaffView()})},bindBulkUpdatefOnboardStaffView:function(){var t="",e="",a="",i=$("#updating-field-type").find(":selected").val(),n="";if(null!=i){if("UPDATE_TIMING_DETAILS"==i&&(n="In Time,Out Time,Half Day Duration(in hrs),Full Day Duration(in hrs)"),"none"!=$("#advance-search-option-div").css("display"))t=$("select.filter-category").val(),e=$("select.filter-department").val(),a=$("select.filter-designation").val();ajaxClient.get("/staff-management/get-bulk-staff-list-by-advance-search-text/ONBOARD/"+n+"?searchText=&category="+t+"&department="+e+"&designation="+a,function(t){$("#bulk-onboard-staff-list").html(t),$("#button").attr("style","display:flex;"),$("#datatables-reponsive").attr("style","display:block"),$(".clockpicker").clockpicker({default:"now"}),bulkUpdateStaff.feedStaffDetails()})}else showErrorDialogBox("Please select a field to update.")},bindUpdatingFieldTypeChangeEvent:function(){$("#updating-field-type").change(function(){$(this).find(":selected").val().trim();$("select#attendance-types").html('<option value="IN_TIME">In Time</option><option value="OUT_TIME">Out Time</option><option value="HALF_DAY_DURATION">Half Day Duration</option><option value="FULL_DAY_DURATION">Full Day Duration</option>'),$("select#attendance-types").selectpicker("refresh")})},bindBulkUpdateOnboardStaffView:function(){var t=$("select.staff-category").val(),e=$("select.staff-department").val(),a=$("select.staff-designation").val();ajaxClient.get("/staff-management/get-bulk-staff-list/ONBOARD?category="+t+"&department="+e+"&designation="+a,function(t){$("#bulk-onboard-staff-visit").html(t),$("#button").attr("style","display:flex;"),$("#datatables-reponsive").attr("style","display:block"),$(".clockpicker").clockpicker({default:"now"}),bulkUpdateStaff.updateBulkStaffVisitingHoursDetails()})},bindUpdatingFieldTypeChangeEvent:function(){$("#updating-field-type").change(function(){$(this).find(":selected").val().trim();$("select#attendance-types").html('<option value="IN_TIME">In Time</option><option value="OUT_TIME">Out Time</option><option value="HALF_DAY_DURATION">Half Day Duration</option><option value="FULL_DAY_DURATION">Full Day Duration</option>'),$("select#attendance-types").selectpicker("refresh")})},selectStaffList:function(t){if(t.checked){$(".staff-select-checkbox").prop("checked",!0);var e=$("input.staff-select-checkbox:checkbox:checked").length;return $("#staff-selected-count").html(e),void(bulkUpdateStaff.dataCache.selectedStaffCount=e)}$(".staff-select-checkbox").prop("checked",!1),$("#staff-selected-count").html(0),bulkUpdateStaff.dataCache.selectedStaffCount=0},updateBulkStaffVisitingHoursDetails:function(){$("#updateVisitingDetails").on("click",function(){$("#weekdays").empty(),daysOfWeekMap.forEach(function(t,e){$("#weekdays").append("<option value='"+e+"'>"+e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()+"</option>")}),$("#staff-visiting-hours-div").empty(),parseInt($("#staff-selected-count").text())<=0?showErrorDialogBox("Please select a staff to update details"):($("#bulk-update-staff-visiting-hours-details-modal").modal({backdrop:"static",keyboard:!1}),onboardStaff.sameForAll())})},staffSelectCheckbox:function(t){var e=$("input.staff-select-checkbox:checkbox:checked").length;$("#staff-selected-count").html(e),bulkUpdateStaff.dataCache.selectedStaffCount=e},feedStaffDetails:function(){$("tr.staff-details-row").each(function(){var t=JSON.parse($(this).find(".staff").text()),e="",a="",i="",n="";null!=t.staffTimingDetails&&""!=t.staffTimingDetails&&null!=t.staffTimingDetails&&null!=t.staffTimingDetails.inTime&&null!=t.staffTimingDetails.outTime&&null!=t.staffTimingDetails.halfDayDuration&&null!=t.staffTimingDetails.fullDayDuration&&((e=t.staffTimingDetails.inTime).hour>=0&&e.hour<=9&&(e.hour="0"+e.hour),e.minute>=0&&e.minute<=9&&(e.minute="0"+e.minute),(a=t.staffTimingDetails.outTime).hour>=0&&a.hour<=9&&(a.hour="0"+a.hour),a.minute>=0&&a.minute<=9&&(a.minute="0"+a.minute),i=t.staffTimingDetails.halfDayDuration,n=t.staffTimingDetails.fullDayDuration),$(this).find(".staff-attendance-status").each(function(){var t=JSON.parse($(this).find(".filter_type").text().trim());if(t==IN_TIME){var s="";""!=e&&(s=e.hour+":"+e.minute),$(this).append('<input type="text" class="form-control clockpicker" id="in-time" placeholder="Enter In Time ...">'),$(this).find("#in-time").val(s)}else if(t==OUT_TIME){var f="";""!=a&&(f=a.hour+":"+a.minute),$(this).append('<input type="text" class="form-control clockpicker" id="out-time" placeholder="Enter Out Time ...">'),$(this).find("#out-time").val(f)}else if(t==HALF_DAY_DURATION){$(this).append('<input type="number" class="form-control" id="half-duration" placeholder="Enter Half Day Duration ...">'),$(this).find("#half-duration").val(i)}else if(t==FULL_DAY_DURATION){$(this).append('<input type="number" class="form-control" id="full-duration" placeholder="Enter Full Day Duration ...">'),$(this).find("#full-duration").val(n)}$(".clockpicker").clockpicker({default:"now"})})})},autofillData:function(){var t=$("#inTimeFill").val(),e=$("#outTimeFill").val(),a=$("#halfDayFill").val(),i=$("#fullDayFill").val();$("tr.staff-details-row").each(function(){""!=t&&$(this).find("#in-time").val(t),""!=e&&$(this).find("#out-time").val(e),""!=a&&$(this).find("#half-duration").val(a),""!=i&&$(this).find("#full-duration").val(i)})},validateTime:function(){var t=$("#inTimeFill").val(),e=$("#outTimeFill").val(),a=$("#halfDayFill").val(),i=$("#fullDayFill").val();return""!=t&&t>=e?(showErrorDialogBox("In Time should not be greater than or equal to Out Time"),void $("#outTimeFill").val("")):a<0?(showErrorDialogBox("Please Enter any positive value for the Half Time"),void $("#halfDayFill").val("")):i<0?(showErrorDialogBox("Please Enter any positive value for the Full Time"),void $("#fullDayFill").val("")):void 0},saveStaffDetails:function(){var t=[],e=null,a=[];a.push(null);var i=$(".onScreen").text();if("TIMING_DETAILS"==i)$("tr.staff-details-row").each(function(){var e=JSON.parse($(this).find(".staff").text()),a=e.staffId,i=(e.staffCategoryId,""),n="",s="",f="";if(null==e.staffTimingDetails||""==e.staffTimingDetails)i={hour:"00",minute:"00"},n={hour:"00",minute:"00"},s=0,f=0;else{var o=e.staffTimingDetails.inTime,r=e.staffTimingDetails.outTime;s=e.staffTimingDetails.halfDayDuration,f=e.staffTimingDetails.fullDayDuration;i=null!=o?o.hour+":"+o.minute:"00:00",n=null!=r?r.hour+":"+r.minute:"00:00",s=null!=s?e.staffTimingDetails.halfDayDuration:0,f=null!=f?e.staffTimingDetails.fullDayDuration:0}e.staffBasicDetailsWithCategoryDepartDesignation.departmentDesignationMappingUUID;var l=void 0===$(this).find("#in-time").val()?i:$(this).find("#in-time").val(),d=void 0===$(this).find("#out-time").val()?n:$(this).find("#out-time").val(),c=void 0===$(this).find("#half-duration").val()?s:$(this).find("#half-duration").val(),u=void 0===$(this).find("#full-duration").val()?f:$(this).find("#full-duration").val(),p=bulkUpdateStaff.getStaffTimingDetails(l,d,c,u);t.push({staffId:a,staffTimingDetails:p})}),a=["TIMING_DETAILS"];else{var n=bulkUpdateStaff.getSelectedBulkStaffIds(),s=admitStaff.getStaffVisitingDetails();if(void 0===s||0===s.length)return $("#bulk-update-staff-visiting-hours-details-modal").modal("toggle"),void showErrorDialogBox("Please fill data correctly");$("#bulk-update-staff-visiting-hours-details-modal").modal("toggle"),e={staffIdList:n,staffVisitingDaysList:s},a=["VISITING_HOURS"],t=null}var f={instituteId:null,staffDetailsParametersList:a,staffTimingDetails:t,staffVisitingDaysPayload:e};ajaxClient.post("/staff-management/bulk-update-staff",{bulkUpdateStaffPayload:JSON.stringify(f)},function(t){$("#onboard-staff-confirmation-modal-container").html(t),"TIMING_DETAILS"==i?($("#admission-status-modal").modal("toggle"),$(".clockpicker").clockpicker({default:"now"}),bulkUpdateStaff.bindBulkUpdatefOnboardStaffView()):bulkUpdateStaff.bindBulkUpdateOnboardStaffView()})},getSelectedBulkStaffIds:function(){var t=[];return $("input.staff-select-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().find("p.bulk-notification-staff-id").first().text().trim();t.push(e)}}),t},getStaffTimingDetails:function(t,e,a,i){var n=t;t=null;if(""!=n&&null!=n&&null!=n){t={hour:(f=n.split(":"))[0],minute:f[1],second:"00"}}var s=e;e=null;if(""!=s&&null!=s&&null!=s){var f;e={hour:(f=s.split(":"))[0],minute:f[1],second:"00"}}var o=a;o=""!=o&&null!=o&&null!=o?parseFloat(o):null;var r=i;return r=""!=r&&null!=r&&null!=r?parseFloat(r):null,staffTimingDetails={inTime:t,outTime:e,halfDayDuration:o,fullDayDuration:r},staffTimingDetails}},relieveStaff={staffStatus:"",loadRelieveStaffMainPage:function(){ajaxClient.get("/staff-management/staff-details/RELIEVED",function(t){$("#main-content").html(t),relieveStaff.bindSearchStaffRelieveDetails(),relieveStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),relieveStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0),$(".selectpicker").selectpicker(),$(".clockpicker").clockpicker({default:"now"}),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{targets:"no-sort",orderable:!1}],order:[[2,"asc"]]})})},loadRelieveStaffPageReload:function(){ajaxClient.get("/staff-management/staff-details-page-reload/RELIEVED",function(t){$("#search-relieve-staff-results").html(t),relieveStaff.refreshSearchFields(),relieveStaff.bindSearchStaffRelieveDetails(),relieveStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),relieveStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),initDateWithYearRange("-70:+10",!0)})},refreshSearchFields:function(){$("#relieve-staff-search-text").val(""),$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&$(this).prop("disabled",!0)})},bindSearchStaffRelieveDetails:function(){$("#relieve-staff-search").on("click",function(){relieveStaff.bindSearchStaffRelieveDetailsView()}),$("#relieve-staff-search-text").on("keyup",function(t){13==t.keyCode&&relieveStaff.bindSearchStaffRelieveDetailsView()}),$("#apply-staff-type-search-filter").on("click",function(){relieveStaff.bindSearchStaffRelieveDetailsView()})},bindSearchStaffRelieveDetailsView:function(){var t=$("#relieve-staff-search-text").val(),e=0;$(".staff-type-dropdown-div").each(function(){$(this).find(".staff-type:checked").length>0&&(0==e?$(this).find(".staff-type:checked").attr("id").toString():","+$(this).find(".staff-type:checked").attr("id").toString(),e++)});var a="",i="",n="";if("none"!=$("#advance-search-option-div").css("display"))a=$("select.filter-category").val(),i=$("select.filter-department").val(),n=$("select.filter-designation").val();ajaxClient.get("/staff-management/get-staff-list-by-advance-search-text/RELIEVED?searchText="+t+"&category="+a+"&department="+i+"&designation="+n,function(t){$("#relieve-staff-list").html(t),relieveStaff.bindSearchStaffRelieveDetails(),relieveStaff.registerUpdateStaffDetailsCallBack(),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),relieveStaff.registerViewStaffDetailsCallBack(),staffDocumentsDetails.registerUploadStaffDocumentCallBack(),staffPermanentAddressUpdate("permanent-same-as-present","staff-permanent-address-1","staff-permanent-address-2","staff-permanent-city","staff-permanent-state","staff-permanent-country","staff-permanent-zipcode","staff-present-address-1","staff-present-address-2","staff-present-city","staff-present-state","staff-present-country","staff-present-zipcode"),initDateWithYearRange("-70:+10",!0)})},registerUpdateStaffDetailsCallBack:function(){$(".update-staff-info").on("click",function(){relieveStaff.staffStatus="RELIEVED";var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffInformation(JSON.parse(t)),enforceConstraints(),$(".staff-details-screen").attr("style","display:none"),$("#staff-list-update-staff-screen").attr("style","display:block"),$("#screen-name").text("STAFF-LIST"),$("#staff-status-screen").text("RELIEVED")})},registerViewStaffDetailsCallBack:function(){$(".view-staff-info").on("click",function(){$("#staff-list-view-staff-screen").attr("style","display:block"),$("#view-staff-details-modal").modal("toggle");var t=$(this).parent().parent().find(".staff-info-json").text().trim();fillStaffViewInformation(JSON.parse(t))})},deleteStaff:function(){$("#staff-list-relieve-delete-staff-screen").modal("toggle");var t=$("#delete-relieve-staff-id").text();ajaxClient.get("/staff-management/delete-staff/"+t+"/RELIEVED",function(t){$("#relieve-staff-confirmation-modal-container").html(t),$("#staff-relieve-status-modal").modal({backdrop:"static",keyboard:!1}),relieveStaff.loadRelieveStaffPageReload()})},populateDeleteStaffModal:function(t){$("#delete-relieve-staff-id").text(t),$("#delete-relieve-staff-details-modal-text").html("Are you sure to delete staff details?")},markStaffOnboard:function(){$("#staff-list-onboard-staff-screen").modal("toggle");var t=$("#relieve-staff-id").text(),e=$("select.filter-institute-scope").val(),a={staffTimingDetails:null,userPayload:{instituteId:null,uuid:t,userType:"STAFF",authorizedModules:$("select.filter-authorized-module").val(),roles:$("select.filter-roles").val(),instituteScope:e}};ajaxClient.post("/staff-management/onboard-staff/"+t+"/"+!0,{staffOnboardPayload:JSON.stringify(a)},function(t){$("#relieve-staff-confirmation-modal-container").html(t),$("#staff-relieve-status-modal").modal({backdrop:"static",keyboard:!1}),relieveStaff.loadRelieveStaffPageReload()})},populateMarkStaffOnboardStaffModal:function(t){$("#relieve-staff-id").text(t),$("#relieve-staff-details-modal-text").html("Are you sure to onboard staff? If staff have user it will automatically get enable.")}},staffReports={dataCache:{},loadReportsMenu:function(){ajaxClient.get("/staff-management/reports",function(t){$("#main-content").html(t),initSelect2("All"),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),staffReports.bindGenerateReportEvent(),staffReports.loadOnChangeEventsFilter(),staffReports.checkboxEvents(),staffReports.dataCache.filterationCriteria=new Object})},loadOnChangeEventsFilter:function(){$("select.filter-gender").on("change",function(){staffReports.dataCache.filterationCriteria.gender=$(this).val()}),$("select.filter-religion").on("change",function(){staffReports.dataCache.filterationCriteria.religion=$(this).val()}),$("select.filter-category").on("change",function(){staffReports.dataCache.filterationCriteria.category=$(this).val()}),$("select.filter-states").on("change",function(){staffReports.dataCache.filterationCriteria.states=$(this).val()})},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var t="";null!=staffReports.dataCache.filterationCriteria.gender&&null!=staffReports.dataCache.filterationCriteria.gender&&(t=staffReports.dataCache.filterationCriteria.gender.join(","));var e="";null!=staffReports.dataCache.filterationCriteria.religion&&null!=staffReports.dataCache.filterationCriteria.religion&&(e=staffReports.dataCache.filterationCriteria.religion.join(","));var a="";null!=staffReports.dataCache.filterationCriteria.category&&null!=staffReports.dataCache.filterationCriteria.category&&(a=staffReports.dataCache.filterationCriteria.category.join(","));var i="";null!=staffReports.dataCache.filterationCriteria.states&&null!=staffReports.dataCache.filterationCriteria.states&&(i=staffReports.dataCache.filterationCriteria.states.join(","));var n=$(this).closest("div.report-field-container");if(!validateMandatoryFields($(n))){var s="";$(n).find(".staff-status").length>0&&(s=$(n).find(".staff-status").val().join());var f=$(n).find("p.report-type").text().trim(),o="";$(n).find("select.filter-staff-category").length>0&&(o=$(n).find("select.filter-staff-category").val().join());var r="";$(n).find("select.filter-department").length>0&&(r=$(n).find("select.filter-department").val().join());var l="";$(n).find("select.filter-designation").length>0&&(l=$(n).find("select.filter-designation").val().join()),$(this).closest("div.modal").modal("toggle");var d=reportUtils.getReportHeadersCSV(n);window.open(baseURL+"/staff-management/generate-report/"+f+"?staffStatus="+s+"&staffCategory="+o+"&staffDepartment="+r+"&staffDesignation="+l+"&requiredHeaders="+d+"&userCategory="+a+"&gender="+t+"&religion="+e+"&state="+i,"_blank")}})},resetPopup:function(){staffReports.resetCheckBoxes()},resetCheckBoxes:function(){$(".parent").prop("checked",!1),$(".child").prop("checked",!1),$(".basic").prop("checked",!0),$(".basic-report-column").prop("checked",!0)},checkboxEvents:function(){$(".child").on("change",function(){$(this).parent().parent().parent().find($(".parent")).prop("checked",!1)}),$(".parent").on("change",function(){$(this).prop("checked")?$(this).parent().parent().find($(".child")).prop("checked",!0):$(this).prop("checked")||$(this).parent().parent().find($(".child")).each(function(){$(this).is(":disabled")||$(this).prop("checked",!1)})})}},staffDocumentsDetails={registerUploadStaffDocumentCallBack:function(){$(".upload-staff-document").on("click",function(){var t=$(this).parent().parent().find(".staff-info-json").text().trim(),e=JSON.parse(t);$("#upload-document-staff-id").text(e.staffId),$(".staff-details-screen").attr("style","display:none"),$("#staff-list\\.upload-document-screen").attr("style","display:block"),$("#upload-document-staff-name").text(e.staffBasicDetailsWithCategoryDepartDesignation.name),staffDocumentsDetails.populateUploadedDocuments(e.staffDocuments),$("#screen-name").text("STAFF-LIST"),$("#staff-status-screen").text(e.staffStatus)}),$("#upload-staff-document-type").change(function(){$(this).find(":selected").val().trim()==OTHER_DOCUMENT_TYPE?$("#upload-staff-document-name").parent().attr("style","display:block"):$("#upload-staff-document-name").parent().attr("style","display:none")}),registerUploadFileCallback()},bindStaffDocumentActions:function(){$(".download-staff-document").on("click",function(){var t=$("#upload-document-staff-id").text().trim(),e=$(this).parent().find("p.view-document-id").text().trim();window.open(baseURL+"/staff-management/document-download/"+t+"/"+e,"_blank")}),$(".delete-staff-document").on("click",function(){var t=$(this).parent().find("p.view-document-id").text().trim();$("#staff-document-delete-confirm-button").attr("onclick","staffDocumentsDetails.deleteStaffDocument('"+t+"')"),$("#staff-document-delete-confirm-modal").modal({backdrop:"static",keyboard:!1})})},deleteStaffDocument:function(t){var e=$("#upload-document-staff-id").text().trim();ajaxClient.post("/staff-management/document-delete/"+e+"/"+t,{},function(t){$("#staff-document-status-modal-container").html(t),$("#staff-document-delete-status-modal").modal({backdrop:"static",keyboard:!1});var e=$("#success-document-delete-response").text().trim(),a=JSON.parse(e);staffDocumentsDetails.populateUploadedDocuments(a)})},populateUploadedDocuments:function(t){if(null!=t&&0!=t.length){for(var e="<br>",a=0,i=0;i<t.length;i++){a%3==0&&(0!=a&&(e+="</div>"),e+='<div class="row">');var n=t[i];if("STAFF_PROFILE_IMAGE_THUMBNAIL"!==n.documentType){a++;var s="Uploaded on : "+getFormattedDate(n.uploadTime);e+='<div class="col-sm-4"> <div class="card bg-light text-center"> <div class="card-header"> <h5> <strong> '+n.documentName+' </strong></h5> </div> <div class="card-body"> <p style="display:none;" class="view-document-id"> '+n.documentId+' </p> <p class="card-text"> Category : '+n.documentTypeDisplayName+' </p> <a href="#" class="btn btn-outline-info download-staff-document">Download </a> <a href="#" class="btn btn-outline-danger delete-staff-document">Delete </a> </div> <div class="card-footer text-muted"> '+s+" </div> </div> </div>"}}e+="</div> <br>",$("#staff-uploaded-documents").html(e),staffDocumentsDetails.bindStaffDocumentActions()}else $("#staff-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")},resetNewDocumentUploadPopup:function(){$("#upload-staff-document-type").val(""),$("#upload-document-file").val(""),$("#upload-document-file-label").text(""),$("#upload-staff-document-name").val(""),$("#upload-staff-document-name").parent().attr("style","display:none")},uploadStaffDocument:async function(){var t=$("#upload-document-staff-id").text(),e=$("#upload-staff-document-type option:selected").val().trim();if(""!=e){var a;if($("#upload-document-file")[0].files.length>0){var i=$("#upload-document-file")[0].files[0];if(console.log(i),a=await compressFileUtils.compress(i),console.log(a),console.log("final file size : "+a.size),a.size/1024>STAFF_IMAGE_SIZE_LIMIT)showErrorDialogBoxWithExistingModalDetails("File size exceeds "+STAFF_IMAGE_SIZE_LIMIT+" KB after compression. Please reduce the file size and try uploading again.","#upload-new-document-modal");else{var n="";if(e!=OTHER_DOCUMENT_TYPE||""!=(n=$("#upload-staff-document-name").val())){var s=new FormData;s.append("document",a),s.append("documentType",e),s.append("documentName",n),s.append("staffId",t),$("#upload-new-document-modal").modal("toggle"),ajaxClient.uploadFile("/staff-management/document-upload",s,function(t){$("#staff-document-status-modal-container").html(t),$("#staff-document-upload-status-modal").modal({backdrop:"static",keyboard:!1});var e=$("#success-document-upload-response").text().trim(),a=JSON.parse(e);staffDocumentsDetails.populateUploadedDocuments(a)})}else showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed","#upload-new-document-modal")}}else showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload","#upload-new-document-modal")}else showErrorDialogBox("Document type field is mandatory please fill it then proceed.")}},generateDocuments={dataCache:{},generateDocument:function(){var t=$("#generate-staff-document-type").find(":selected").val().trim(),e=$("#upload-document-staff-id").text();window.open(baseURL+"/staff-management/generate-document/"+e+"/"+t,"_blank")}};function advanceSearch(){$("#advance-search-option-div").toggle()}