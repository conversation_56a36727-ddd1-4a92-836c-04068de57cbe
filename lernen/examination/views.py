from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from staffmanagement.controller.staff_manager import *
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from studentmanagement.controller.studentmanagement import *
from core.controller.utils.reports_manager import *
from courses.controller.course_manager import *
from courses.controller.course_assignment_manager import *
from .controller.examination_manager import *
from .controller.hpc_manager import *
from core.controller.utils.tracking_events import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.utility import *
from core.controller.user.notification_manager import *
from core.controller.utils.authorised_actions import *
from core.controller.utils.template_manager import *
from math import *
import time
import base64

MODULE_NAME = 'EXAMINATION'

def authorized_user_session(request):
	return authorized_module(request, MODULE_NAME)

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		user_type = user_login_view['user']['userType']
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "EXAMINATION_MODULE_LOADED"})
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		examination_preferences = get_examination_preferences(user_login_view, institute_unique_code)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# institute_stats = get_institute_stats(user_login_view, institute_unique_code, current_session['academicSessionId'])
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		tutorial_videos = get_tutorial_video_details(MODULE_NAME, institute_unique_code, user_type)
		return render(request, 'examination/layouts/default-examination-layout.html',{'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' : payment_status_data, 'examination_preferences' : examination_preferences, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access, 'tutorial_videos': tutorial_videos })

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		# institute_stats = get_institute_stats(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		# institute_stats = get_institute_stats(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/layouts/dashboard-session-content.html',{'user': user_login_view['user']})

def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		if(status == 'undefined'):
			students = lite_v2_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""))
		else:
			students = lite_v2_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), request.GET.get("status",""))
		return render(request, 'core/student/live_search_student_list.html',{"students":students, "search_version" : "v2"})

def configure_exam_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/exam_config/exam_config_details.html',{'academic_years':academic_years, 'current_session':current_session, 'standards':standards})

def get_exam_forest_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		class_exam_forest = get_exam_forest(user_login_view, institute_unique_code, standard_id, academic_session_id)
		return render(request, 'examination/exam_config/exam_config_folder_view.html',{'class_exam_forest':class_exam_forest})

def get_exam_forest_graph_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		class_exam_forest = get_exam_forest(user_login_view, institute_unique_code, standard_id, academic_session_id)
		return render(request, 'examination/exam_config/class_exam_forest_data.html',{'class_exam_forest':class_exam_forest})

def get_exam_details_view(request, institute_unique_code, exam_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		exam_details = get_exam_details(user_login_view, institute_unique_code, exam_id )
		dimensions = get_dimensions(user_login_view, institute_unique_code)
		class_courses = get_class_courses(user_login_view, institute_unique_code, exam_details['standard']['standardId'], exam_details['academicSession']['academicSessionId'], "", "")
		return render(request, 'examination/exam_config/exam_details.html',{'exam_details':exam_details,'dimensions':dimensions, 'class_courses':class_courses})

def class_students_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = request.GET.get("standards","")
		sectionIds = request.GET.get("sectionId","")
		if(standards == ""):
			return render(request, 'examination/report_card/student_list.html',{'students': None})
		students = get_class_student_list(user_login_view, institute_unique_code, academic_session_id, standards, sectionIds)
		return render(request, 'examination/report_card/student_list.html',{'students':students})

def load_bulk_page_view(request,institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		return render(request, 'examination/report_card/main_screen.html')


@csrf_exempt
def create_exam_view(request, institute_unique_code, isLite):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		exam_creation_payload = json.loads(request.POST['examCreationPayload'])
		response_data = create_exam(user_login_view, institute_unique_code, exam_creation_payload)
		return render(request, 'examination/exam_config/exam_creation_status_modal.html',{"data":response_data, 'isLite' : isLite == "true" })

def get_dimension_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		dimensions = get_dimensions(user_login_view, institute_unique_code)
		return render(request, 'examination/exam_config/dimension_details.html',{'dimensions':dimensions})


def get_dimension_list_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		dimensions = get_dimensions(user_login_view, institute_unique_code)
		return render(request, 'examination/exam_config/dimension_list.html',{'dimensions':dimensions})

@csrf_exempt
def add_dimension_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		dimension_data = json.loads(request.POST['dimensionData'])
		response_data = add_dimension(user_login_view, institute_unique_code, dimension_data)
		return render(request, 'examination/exam_config/exam_config_status_modal.html',{"data":response_data})

@csrf_exempt
def update_dimension_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_dimension_data = json.loads(request.POST['updateDimensionData'])
		response_data = update_dimension(user_login_view, institute_unique_code, update_dimension_data)
		return render(request, 'examination/exam_config/exam_config_status_modal.html',{"data":response_data})

def get_datesheet_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/datesheet/datesheet.html', {'academic_years':academic_years, 'current_session':current_session, 'standards' : standards})

def get_datesheet_list_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		exam_courses = get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, False, True)
		datesheet_metadata_list = get_datesheet(user_login_view, institute_unique_code, academic_session_id, standard_id)
		return render(request, 'examination/datesheet/datesheet_list.html', {'datesheet_metadata_list':datesheet_metadata_list, 'academic_years':academic_years, 'current_session':current_session, 'standards' : standards, 'exam_courses' : exam_courses} )

def get_datesheet_detail_view(request, institute_unique_code, standard_id, academic_session_id, datesheet_id):
		if authorized_user_session(request) and request.is_ajax():
			user_login_view = get_user_login_view(request)
			datesheet_details = get_datesheet_detail(user_login_view, institute_unique_code, academic_session_id, standard_id, datesheet_id)
			return render(request, 'examination/datesheet/datesheet_detail.html', {'datesheet_details':datesheet_details})

@csrf_exempt
def add_datesheet_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		datesheet_and_syllabus_payload = json.loads(request.POST['datesheetAndSyllabusPayload'])
		# exam_and courses
		response_data = add_datesheet(user_login_view, institute_unique_code, datesheet_and_syllabus_payload)
		return render(request, 'examination/datesheet/datesheet_status_modal.html', {'data' : response_data})

@csrf_exempt
def update_datesheet_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		datesheet_and_syllabus_payload = json.loads(request.POST['datesheetAndSyllabusPayload'])
		response_data = update_datesheet(user_login_view, institute_unique_code, datesheet_and_syllabus_payload)
		return render(request, 'examination/datesheet/datesheet_status_modal.html', {'data' : response_data})

@csrf_exempt
def delete_datesheet_view(request, institute_unique_code, datesheet_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_datesheet(user_login_view, institute_unique_code, datesheet_id)
		return render(request, 'examination/datesheet/datesheet_status_modal.html', {'data' : response_data})



@csrf_exempt
def update_exam_metadata_view(request, institute_unique_code, exam_id, isLite):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_exam_metadata_payload = json.loads(request.POST['updateExamMetadataPayload'])
		response_data = update_exam_metadata(user_login_view, institute_unique_code, exam_id, update_exam_metadata_payload)
		return render(request, 'examination/exam_config/exam_update_status_modal.html',{"data":response_data, 'examId':exam_id, 'isLite' : isLite == "true" })


@csrf_exempt
def delete_exam_view(request, institute_unique_code, exam_id, standard_id, isLite):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_exam(user_login_view, institute_unique_code, exam_id)
		return render(request, 'examination/exam_config/exam_deletion_status_model.html',{"data":response_data, "standardId" : standard_id, 'isLite' : isLite == "true" })


@csrf_exempt
def delete_dimension_view(request, institute_unique_code, dimension_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_dimension(user_login_view, institute_unique_code, dimension_id)
		return render(request, 'examination/exam_config/exam_config_status_modal.html',{"data":response_data})

def marks_feed_main_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/marks_feed/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards})

def marks_feed_structure_page_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		class_marks_structures = get_class_marks_feed_structure_filter(user_login_view, institute_unique_code, standard_id, academic_session_id)
		return render(request, 'examination/marks_feed/class_marks_structure.html',{"class_marks_structures":class_marks_structures})

def marks_feed_sheet_view(request, institute_unique_code, academic_session_id, standard_id, exam_id, course_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		if not is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_MARKS):
			return render(request, 'examination/marks_feed/marks_feed_sheet.html',{"exam_marks_details":None, 'course':None, 'authorised_to_feed_marks' : False})

		section_id = request.GET.get("sectionId", None)
		add_relieved_students = request.GET.get("add_relieved_students", False)
		dimension_id_str = request.GET.get("dimension_id_str",None)
		course_type = request.GET.get("course_type",None)

		exam_marks_details = get_class_marks_by_course(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students, dimension_id_str, course_type)
		if exam_marks_details is None or len(exam_marks_details) == 0:
			return render(request, 'examination/marks_feed/marks_feed_sheet.html',{'authorised_to_feed_marks' : True, "exam_marks_details":None, 'course':None})

		courseExamDimensionValue = None
		dimension_feed_status=[]
		dimension_id_list=[]
		for exam_feed_status in exam_marks_details['studentMarksDetailsList'][0]['examDimensionObtainedValues']:
			dimension_id = int(exam_feed_status['examDimension']['dimensionId'])
			dimension_id_list.append(dimension_id)
			dimension_feed_status.append({'dimension_id': dimension_id, 'feed_status': exam_feed_status['marksFeedStatus'] == "SUBMITTED"})

		submitted = marks_submitted_filter(dimension_feed_status,dimension_id_list)
		dimension_feed_status = json.loads(json.dumps(dimension_feed_status))
		authorised_to_feed_submitted_marks = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_SUBMITTED_MARKS)
		edit_allowed = True
		if submitted :
			edit_allowed = authorised_to_feed_submitted_marks

		courseExamDimensionValue= exam_marks_details['examDimensionValues']
		exam_grades = exam_marks_details['examGrade']
		return render(request, 'examination/marks_feed/marks_feed_sheet.html',{'edit_allowed' : edit_allowed,'authorised_to_feed_marks' : True,'submitted' : submitted ,'dimension_feed_status' : dimension_feed_status ,'authorised_to_feed_submitted_marks' : authorised_to_feed_submitted_marks, 'exam_marks_details' : exam_marks_details, 'courseExamDimensionValue':courseExamDimensionValue, 'exam_grades' : exam_grades})


def advance_marks_feed_sheet_view(request, institute_unique_code, exam_id, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		if not is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_MARKS):
			return render(request, 'examination/advance_marks_feed/marks_feed_sheet.html',{"exam_marks_details":None, 'course':None, 'authorised_to_feed_marks' : False})

		course_id = request.GET.get("courseId", "")
		courseIdFinal = course_id;
		section_id = request.GET.get("sectionId", None)
		add_relieved_students = request.GET.get("add_relieved_students", False)

		exam_marks_details = get_class_marks_by_course_list(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students)
		if exam_marks_details is None or len(exam_marks_details) == 0:
			return render(request, 'examination/advance_marks_feed/marks_feed_sheet.html',{'authorised_to_feed_marks' : True, "exam_marks_details":None, 'course':None})

		course = None
		submitted = marks_submitted(exam_marks_details)
		authorised_to_feed_submitted_marks = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_SUBMITTED_MARKS)
		edit_allowed = True
		if submitted :
			edit_allowed = authorised_to_feed_submitted_marks

		course_details = {}
		course_ids = courseIdFinal.split(",");
		for exam_course_marks in exam_marks_details[0]['examCoursesAllDimensionsMarks']:
			courId = exam_course_marks['course']['courseId']
			if courId in course_ids:
				course_details[courId] = exam_course_marks

		# course_details = sortCourseDetailsByName(course_details)
		class_courses = get_class_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, "", "")
		course_student_map = {}

		for class_course in class_courses:
			if not class_course['mandatory']:
				courId = class_course['courseId']
				course_student_map[courId] = []

		class_students_details = get_student_courses(user_login_view, institute_unique_code, academic_session_id, standard_id, course_id)
		for class_students_detail in class_students_details:
			courId = class_students_detail['course']['courseId']
			studentCourseAssignmentDetailsList = []
			for studentCourseAssignmentDetails in class_students_detail['studentCourseAssignmentDetailsList']:
				if studentCourseAssignmentDetails['courseAssign']:
					studentCourseAssignmentDetailsList.append(studentCourseAssignmentDetails['studentDetailedRow']['studentId'])

			if studentCourseAssignmentDetailsList:
				course_student_map[courId] = studentCourseAssignmentDetailsList


		course_type = 'SCHOLASTIC'
		scholastic_exam_grades = get_exam_grades(user_login_view, institute_unique_code, exam_id, course_type)
		course_type = 'COSCHOLASTIC'
		coscholastic_exam_grades = get_exam_grades(user_login_view, institute_unique_code, exam_id, course_type)
		examGrade = {}
		examGrade['SCHOLASTIC'] = scholastic_exam_grades
		examGrade['COSCHOLASTIC'] = coscholastic_exam_grades
		return render(request, 'examination/advance_marks_feed/marks_feed_sheet.html',{'edit_allowed' : edit_allowed, 'authorised_to_feed_marks' : True, 'submitted' : submitted , 'authorised_to_feed_submitted_marks' : authorised_to_feed_submitted_marks, 'exam_marks_details' : exam_marks_details, 'course_type' : course_type, 'course':course, 'examGrade': examGrade, 'course_ids' : course_ids, 'course_details' : course_details, 'course_student_map' : course_student_map })

# def sortCourseDetailsByName(course_details):
# 	courNameMap = {}
# 	for key, value in course_details.items():
# 		courNameMap[key] = value['course']['courseName'].lower()
#
# 	sorted_tuples = sorted(courNameMap.items(), key=lambda item: item[1])
# 	sorted_dict = {k: v for k, v in sorted_tuples}
#
# 	courseDetails = {}
# 	for key, value in sorted_dict.items():
# 		courseDetails[key] = course_details[key]
# 	return courseDetails


def marks_submitted(exam_marks_details):
	for student_exam_course_marks in exam_marks_details:
		for exam_course_marks in student_exam_course_marks['examCoursesAllDimensionsMarks']:
			for exam_dimension_marks in exam_course_marks['examDimensionObtainedValues']:
				if exam_dimension_marks['marksFeedStatus'] == 'SUBMITTED':
					return True
	return False

def marks_submitted_filter(dimension_feed_status,dimension_id_list):
	status_dict = {item['dimension_id']: item['feed_status'] for item in dimension_feed_status}
	for dimension_id in dimension_id_list:
		if status_dict.get(dimension_id, False):
			return True
	return False

@csrf_exempt
def submit_marks_view(request, institute_unique_code, status, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		marks_feed_data = json.loads(request.POST['studentsMarksPayload'])
		response_data = submit_marks(user_login_view, institute_unique_code, marks_feed_data, status, section_id, academic_session_id, standard_id)
		return render(request, 'examination/exam_config/exam_config_status_modal.html',{"data":response_data})

@csrf_exempt
def update_exam_dimension_view(request, institute_unique_code, exam_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_exam_dimension_payload = json.loads(request.POST['updateExamDimensionPayload'])
		response_data = update_exam_dimension(user_login_view, institute_unique_code, exam_id, update_exam_dimension_payload)
		return render(request, 'examination/exam_config/exam_update_status_modal.html',{"data":response_data,'examId':exam_id})

@csrf_exempt
def update_exam_courses_view(request, institute_unique_code, exam_id,course_type):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		update_exam_courses_payload = json.loads(request.POST['examCoursesPayloads'])
		response_data = update_exam_courses(user_login_view, institute_unique_code, exam_id, course_type, update_exam_courses_payload)
		return render(request, 'examination/exam_config/exam_update_status_modal.html',{"data":response_data, 'examId':exam_id})


def marks_view_main_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/marks_view/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards})

def get_exam_course_structure_page_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		only_leaf_exams = request.GET.get("onlyLeaveNode", False)
		exam_courses = get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, only_leaf_exams)
		if not only_leaf_exams:
			return render(request, 'examination/marks_view/class_exam_courses.html',{"exam_courses":exam_courses})
		return render(request, 'examination/advance_marks_feed/class_marks_structure.html',{"exam_courses":exam_courses})

def get_reports_only_leaf_exams_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		exam_courses = get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, True)
		return render(request, 'examination/reports/leaf_exam_details.html',{"exam_courses":exam_courses})

def marks_view_sheet_view(request, institute_unique_code, academic_session_id, standard_id, exam_id, course_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		add_relieved_students = request.GET.get("add_relieved_students", False)
		exam_marks_details = get_resolved_class_marks_by_course(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students, True)
		if exam_marks_details is None or len(exam_marks_details) == 0:
			return render(request, 'examination/marks_view/marks_view_sheet.html',{"exam_marks_details":None, 'course':None})

		course = None
		for exam_course_marks in exam_marks_details[0]['examCoursesAllDimensionsMarks']:
			if course_id == exam_course_marks['course']['courseId']:
				course = exam_course_marks
				break

		course_type = course['course']['courseType']
		header_dimensions = exam_marks_details[0]['examCoursesAllDimensionsMarks'][0]['examDimensionObtainedValues']
		return render(request, 'examination/marks_view/marks_view_sheet.html',{"exam_marks_details": exam_marks_details, 'header_dimensions': header_dimensions, 'course_type' : course_type, 'course':course})

def report_card_generation_homepage_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/report_card/main_screen.html',{'academic_years':academic_years,'current_session':current_session, 'standards': standards, 'student' : None})

def report_card_student_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id)
		if student is None:
			return render(request, 'examination/report_card/student_report_card_view.html',{'student' : None})
		student_id = student['studentId']
		report_card_types = get_report_card_types(user_login_view, institute_unique_code, session_id, student['studentAcademicSessionInfoResponse']['standard']['standardId'])
		return render(request, 'examination/report_card/student_report_card_view.html',{'student' : student, 'report_card_types' : report_card_types})

def get_report_card_types_view(request, institute_unique_code, session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		report_card_types = get_report_card_types(user_login_view, institute_unique_code, session_id, standard_id)
		return render(request, 'examination/report_card/report_card_types_dropdown.html',{'report_card_types' : report_card_types})

def get_all_report_card_types_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		report_card_types = get_all_report_card_types(user_login_view, institute_unique_code, session_id)
		return render(request, 'examination/report_card/report_card_types_dropdown.html',{'report_card_types' : report_card_types})

def student_report_card_view(request, institute_unique_code, session_id, student_id, report_type):
    if authorized_user_session(request):
        user_login_view = get_user_login_view(request)
        report_card = get_student_report_card(user_login_view, institute_unique_code, session_id, student_id, report_type)
        if report_card['success']:
            response = HttpResponse(report_card['content'], content_type="application/pdf")
            response['Content-Disposition'] = 'filename=' + report_card['file_name']
            return response
        return HttpResponse("<strong>" + report_card['error_reason'] + "</strong>")

def report_card_variables_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/report_card_variables/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards})

def view_report_card_variables_view(request, institute_unique_code, standard_id, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_type = request.GET.get("reportType", None)
		report_card_variable_proprities = get_report_card_variable_proprities(user_login_view, institute_unique_code)
		report_card_variables = get_report_card_variables(user_login_view, institute_unique_code, standard_id, session_id, section_id, report_type)
		return render(request, 'examination/report_card_variables/view_report_card_variables.html',{"report_card_variables":report_card_variables, "report_card_variable_proprities" : report_card_variable_proprities})

def get_report_card_variable_proprities(user_login_view, institute_unique_code):
	examination_preferences = get_examination_preferences(user_login_view, institute_unique_code)
	report_card_variable_proprities = {}
	if not examination_preferences or not examination_preferences['reportCardVariableFieldPropertiesList']:
		return report_card_variable_proprities
	for reportCardVariableFieldProperty in examination_preferences['reportCardVariableFieldPropertiesList']:
		reportCardVariableParameters = reportCardVariableFieldProperty['reportCardVariableParameters']
		if reportCardVariableParameters:
			report_card_variable_proprities[reportCardVariableParameters] = reportCardVariableFieldProperty
	return report_card_variable_proprities


@csrf_exempt
def submit_report_card_variables_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		report_card_variable_details = json.loads(request.POST['reportCardVariableDetails'])
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		session_id = current_session['academicSessionId']
		response_data = submit_report_card_variables(user_login_view, institute_unique_code, report_card_variable_details, session_id)
		return render(request, 'examination/report_card_variables/report_card_variable_update_status_modal.html',{"data":response_data})


def class_report_card_view(request, institute_unique_code, session_id, standard_id, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		student_per_page = request.GET.get("studentPerPage", None)
		report_card = get_class_report_card(user_login_view, institute_unique_code, session_id, standard_id, section_id, report_type, student_per_page)
		if(report_card['success']):
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+report_card['file_name']
			return response
		return HttpResponse("<strong>"+str(report_card['error_reason'])+"</strong>")

def bulk_students_report_card_view(request, institute_unique_code, session_id, standard_id, report_type,student_ids):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_card = get_bulk_student_report_card(user_login_view, institute_unique_code, session_id, standard_id, section_id, report_type,student_ids)
		if(report_card['success']):
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+report_card['file_name']
			return response
		return HttpResponse("<strong>"+str(report_card['error_reason'])+"</strong>")

def green_sheet_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/green_sheet/main_screen.html',{'academic_years':academic_years,'current_session':current_session, 'standards': standards})

def generate_green_sheet_view(request, institute_unique_code, session_id, standard_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		report = get_class_green_sheet(user_login_view, institute_unique_code, session_id, standard_id)
		if(report is not None):
			response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
			response['Content-Disposition'] = 'inline; filename='+report["file_name"]
			return response

def get_report_card_variable_types_view(request, institute_unique_code, session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		report_card_types = get_report_card_types(user_login_view, institute_unique_code, session_id, standard_id)
		return render(request, 'examination/report_card_variables/report_card_type_field.html',{'report_card_types' : report_card_types})

def report_card_variables_with_session_details_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, session_id)
		return render(request, 'examination/report_card_variables/report_variables_class_report_type_page.html',{'standards':standards})

def reports_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		headers = get_report_headers(user_login_view, institute_unique_code, MODULE_NAME)
		report_header_columns = get_report_headers_for_display(headers)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
		class_courses = get_institute_courses(user_login_view, institute_unique_code, current_session['academicSessionId'])
		report_card_types = get_all_report_card_types(user_login_view, institute_unique_code, current_session['academicSessionId'])
		staff_details = get_general_staff_detail_by_staff_status(user_login_view, institute_unique_code, "ONBOARD")
		return render(request, 'examination/reports/main_screen.html',{'academic_years': academic_years, 'currect_session': current_session, 'report_header_columns' : report_header_columns, 'standards' : standards, 'session_class_exams' : session_class_exams, 'class_courses' : class_courses, 'report_card_types' : report_card_types, 'staff_details' : staff_details})


def get_exam_course_details_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		class_marks_structures = get_class_marks_feed_structure(user_login_view, institute_unique_code, standard_id, academic_session_id)
		return render(request, 'examination/reports/course_details.html',{"class_marks_structures":class_marks_structures})

def get_section_details_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/reports/section_details.html',{"standards":standards})

def generate_feed_marks_sheet_view(request, institute_unique_code, academic_session_id, standard_id, exam_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("section_id","")
		student_count_per_page = request.GET.get("student_count", 40)
		add_relieved_students = request.GET.get("add_relieved_students", False)
		course_id = request.GET.get("course_id", "")
		student_sorting_parameters = request.GET.get("student_sorting_parameters", False)
		class_feed_marks_sheet = get_class_feed_marks_sheet(user_login_view, institute_unique_code, academic_session_id, exam_id, standard_id, section_id, student_count_per_page, add_relieved_students, course_id, student_sorting_parameters)
		if(class_feed_marks_sheet is not None):
			response = HttpResponse(class_feed_marks_sheet['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+class_feed_marks_sheet['file_name']
			return response
		return HttpResponse("<strong>Invalid Information.</strong>")

def admitcard_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
		return render(request, 'examination/admitcard/main_screen.html',{'academic_years': academic_years, 'standards' : standards, 'session_class_exams' : session_class_exams, 'current_session' : current_session})

def admitcard_session_change_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/admitcard/admit_card_page_details.html',{'standards' : standards})

def get_student_list_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		standards = request.GET.get("standards","")
		sectionIds = request.GET.get("sectionId","")
		if(standards == ""):
			return render(request, 'examination/admitcard/student_list.html',{'students': None})
		students = get_class_student_list(user_login_view, institute_unique_code, academic_session_id, standards, sectionIds)
		return render(request, 'examination/admitcard/student_list.html',{'students' : students})

def generate_admitcard_view(request, institute_unique_code, exam_id, admit_card_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		student_ids = request.GET.get("studentIds", None)
		count_per_page = request.GET.get("countPerPage", None)
		admitcards = get_class_exam_admitcards(user_login_view, institute_unique_code, exam_id, admit_card_type, student_ids, count_per_page)
		if(admitcards is not None):
			response = HttpResponse(admitcards['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+admitcards['file_name']
			return response
		return HttpResponse("<strong>Invalid Information.</strong>")

def get_session_standards_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/standard_with_section_options.html',{'standards':standards})


def get_session_standards_json_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		report_card_types = get_all_report_card_types(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/wrapper.html',{'standards':standards, 'report_card_types' : report_card_types})


def result_declaration_view(request, institute_unique_code):
	institute = get_institute_by_unique_code(institute_unique_code)
	institute_id = institute['instituteId']
	academic_years = get_academic_years_by_institute_from_cache(institute['instituteId'])
	current_session = get_current_academic_year(request, academic_years)
	return render(request, 'examination/result_declaration/main_screen.html', {'institute': institute, 'current_session': current_session, 'academic_years' : academic_years})


def student_exam_report_card_view(request, institute_unique_code):
	admission_number = request.GET.get('admissionNumber', '')
	student_name = request.GET.get('studentName', '')
	father_name = request.GET.get('fatherName', '')
	google_auth_key = request.GET.get('googleAuthKey', '')
	academic_session_id = request.GET.get('academicSessionId', '')
	student_report_card = get_student_exam_report_card(institute_unique_code, admission_number, student_name, father_name, "ANNUAL", academic_session_id, google_auth_key)
	if student_report_card is not None and student_report_card['success']:
		response = HttpResponse(student_report_card['content'], content_type="application/pdf")
		response['Content-Disposition'] = 'filename=' + student_report_card['file_name']
		return response
	return HttpResponse("<strong> " + student_report_card["error_reason"] +" </strong>")

def get_channel_credits_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		channel_credits_list = get_counters(user_login_view, institute_unique_code, "SMS_COUNTER,AUDIO_VOICE_CALL_COUNTER")
		return render(request, 'core/utils/channel_credits_header_row_view.html',{'channel_credits_list' : channel_credits_list})

def notification_main_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/notification/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards})

def get_exam_notification_detail_view(request, institute_unique_code, standard_id, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		exam_courses = get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id)
		return render(request, 'examination/notification/class_exam_courses.html',{"exam_courses":exam_courses})

def student_details_sheet_view(request, institute_unique_code, academic_session_id, standard_id, exam_id, course_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		exam_marks_details = get_resolved_class_marks_by_course(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, False, False)
		if exam_marks_details is None or len(exam_marks_details) == 0:
			return render(request, 'examination/notification/marks_view_sheet.html',{"exam_marks_details":None, 'course':None})

		course = None
		for exam_course_marks in exam_marks_details[0]['examCoursesAllDimensionsMarks']:
			if course_id == exam_course_marks['course']['courseId']:
				course = exam_course_marks
				break

		course_type = course['course']['courseType']
		header_dimensions = exam_marks_details[0]['examDimensionValues'][course_type]
		return render(request, 'examination/notification/marks_view_sheet.html',{"exam_marks_details": exam_marks_details, 'header_dimensions': header_dimensions, 'course_type' : course_type, 'course':course})

def bulk_notification_template_selection_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		notification_templates = get_notification_templates(user_login_view, institute_unique_code, True, "CUSTOM")
		audio_templates = []
		sms_templates = []
		if notification_templates is not None:
			for template in notification_templates:
				if template['deliveryMode'] == "SMS":
					sms_templates.append(template)
				elif template['deliveryMode'] == "CALL":
					audio_templates.append(template)
		return render(request, 'examination/notification/bulk_notifications_template_selection_modal.html',{'audio_templates':audio_templates, 'sms_templates' : sms_templates})

def bulk_reminder_template_selection_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		sms_templates = get_sms_templates(user_login_view, institute_unique_code, False, "EXAM_OBTAINED_MARKS")
		return render(request, 'examination/notification/bulk_reminder_template_selection_modal.html',{'sms_templates' : sms_templates})

@csrf_exempt
def send_notifications_view(request, institute_unique_code, send_to_absentees, exam_id, course_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		send_notifications_payload = json.loads(request.POST['sendNotificationsPayload'])
		section_id = request.GET.get("sectionId", None)
		response_data = send_exam_notifications(user_login_view, institute_unique_code, send_to_absentees, exam_id, course_id, section_id, send_notifications_payload)
		return render(request, 'communication/notifications/status_modal.html',{"data":response_data})

def notification_history_homepage_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)

		return render(request, 'examination/notification_history/notification_history_homepage.html',{'academic_years':academic_years, 'current_session':current_session})

def notification_history_view(request, institute_unique_code, academic_session_id, delivery_mode, notificationHistoryTab, offset, limit):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)

		batch_notifications = []
		individual_notifications = []
		pagination_details = None
		if notificationHistoryTab == "BATCH":
			batch_notification_history = get_batch_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit)
			pagination_details = get_pagination_details(batch_notification_history['paginationInfo'])
			batch_notifications = batch_notification_history['result']
		else:
			individual_notification_history = get_individual_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit)
			pagination_details = get_pagination_details(individual_notification_history['paginationInfo'])
			individual_notifications = individual_notification_history['result']

		if(delivery_mode == 'SMS'):
			return render(request, 'examination/notification_history/sms_notification_history.html',{'batch_notifications': batch_notifications, 'individual_notifications' : individual_notifications, 'pagination_details' : pagination_details})
		elif (delivery_mode == 'CALL'):
			return render(request, 'examination/notification_history/voice_notification_history.html',{'batch_notifications': batch_notifications, 'individual_notifications' : individual_notifications, 'pagination_details' : pagination_details})


def batch_notification_details_view(request, institute_unique_code, batch_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		batch_notification_details = get_batch_notification_details(user_login_view, institute_unique_code, "STUDENT", batch_id)
		return render(request, 'examination/notification_history/batch_notifications_view_modal.html',{'batch_notification_details':batch_notification_details})

def advance_marks_feed_main_page_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/advance_marks_feed/main_screen.html',{"academic_years":academic_years,'current_session':current_session,'standards':standards})

def datesheet_pdf_view(request, institute_unique_code, datesheet_id, standard_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_session_id = request.GET.get("academic_session_id", 0)
		pdf_invoice = get_datesheet_pdf(user_login_view, institute_unique_code, datesheet_id, standard_id, academic_session_id)
		if(pdf_invoice is not None):
			response = HttpResponse(pdf_invoice['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+pdf_invoice['file_name']
			return response
		return HttpResponse("<strong>Invalid Datesheet Information.</strong>")

def get_publish_exam_config_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/publish_exam/publish_exam.html', {'academic_years':academic_years, 'current_session':current_session, 'standards' : standards})

def get_publish_exam_list_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
		exam_courses = sort_exam_courses(get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, False))
		return render(request, 'examination/publish_exam/publish_exam_list.html', {'academic_years':academic_years, 'current_session':current_session, 'standards' : standards, 'exam_courses' : exam_courses})

def sort_exam_courses(exam_courses):
	if not exam_courses:
		return []
	unpublished_exams = []
	published_exams = [];
	for exam_course in exam_courses:
		if exam_course['examCoursePublishedStatus'] == "PUBLISHED":
			published_exams.append(exam_course)
		elif exam_course['examCoursePublishedStatus'] == "UNPUBLISHED":
			unpublished_exams.append(exam_course)
	final_exams = unpublished_exams + published_exams
	return final_exams

@csrf_exempt
def update_exam_status_view(request, institute_unique_code, exam_id, status):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = update_exam_status(user_login_view, institute_unique_code, exam_id, status)
		return render(request, 'examination/publish_exam/publish_exam_status_modal.html',{"data":response_data})

def courses_marks_status_details_view(request, institute_unique_code, academic_session_id, exam_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		courses_marks_status_detail = courses_marks_status_details(user_login_view, institute_unique_code, academic_session_id, exam_id)
		return render(request, 'examination/publish_exam/exam_courses_marks_status_detail.html', {'courses_marks_status_detail' : courses_marks_status_detail, 'exam_id' : exam_id})

@csrf_exempt
def update_course_exam_status_view(request, institute_unique_code, dimension_id, course_id, exam_id, status, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		response_data = update_course_exam_status(user_login_view, institute_unique_code, dimension_id, course_id, exam_id, status, academic_session_id, standard_id, section_id)
		return render(request, 'examination/publish_exam/publish_exam_status_modal.html',{"data":response_data})


def generate_report_view(request, institute_unique_code, exam_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		report_request = {"exam_id" : exam_id,"report_type":request.GET.get("reportType",""), "section_id_str":request.GET.get("section_id_str",""),"required_headers":request.GET.get("requiredHeaders",""), "exclude_coscholastic_subjects" : request.GET.get("exclude_coscholastic_subjects",""), "show_dimensions" : request.GET.get("show_dimensions",""), 'show_total_column_dimension' : request.GET.get("show_total_column_dimension",""), 'display_rank' : request.GET.get("display_rank",""), 'show_coscholastic_grade' : request.GET.get("show_coscholastic_grade",""), 'display_attendance' : request.GET.get("display_attendance",""), 'show_scholastic_grade' : request.GET.get("show_scholastic_grade",""),'parent_remark':request.GET.get("parent_remark",""), "download_format" : downloadFormat}
		if download_report :
			report = get_class_exam_report(user_login_view, institute_unique_code, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_class_exam_report_data(user_login_view, institute_unique_code, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})

def generate_course_report_view(request, institute_unique_code, standard_id, exam_id):
	if authorized_user_session(request):
		download_report = request.GET.get("downloadReport", True)
		downloadFormat = request.GET.get("downloadFormat", "EXCEL")
		user_login_view = get_user_login_view(request)
		report_request = {"exam_id" : exam_id, "section_id":request.GET.get("section_id",""), "exclude_coscholastic_subjects" : request.GET.get("exclude_coscholastic_subjects",""), "show_dimensions" : request.GET.get("show_dimensions",""), 'show_total_column_dimension' : request.GET.get("show_total_column_dimension",""), 'academicSessionId' : request.GET.get("academicSessionId",0), "download_format" : downloadFormat}
		if download_report :
			report = get_class_course_report(user_login_view, institute_unique_code, standard_id, exam_id, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_class_course_report_data(user_login_view, institute_unique_code, standard_id, exam_id, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";

			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})

def generate_reports_view(request, institute_unique_code, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		exam_report_filteration_criteria = getExamReportFilterationCriteria(request, downloadFormat)
		if download_report :
			report = get_report(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_report_data(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria)
			url_path = request.build_absolute_uri()
			url_suffix_path = "/" + institute_unique_code + url_path.split(institute_unique_code)[1]
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";				
			return render(request, 'core/reports/report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})

def generate_graphical_reports_view(request, institute_unique_code, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		download_report = request.GET.get("downloadReport", True)
		downloadFormat = request.GET.get("downloadFormat")
		exam_report_filteration_criteria = getExamReportFilterationCriteria(request, downloadFormat)
		response = get_graphical_report(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria)
		return render(request, 'core/reports/graphical_report_data.html',{'response':response})

def getExamReportFilterationCriteria(request, downloadFormat):
	
	academic_session_id = request.GET.get("academic_session_id", 0)
	
	standard_id = request.GET.get("standard_id", None)
	if standard_id == "":
		standard_id = None
	
	section_id = request.GET.get("section_id", 0)
	if section_id == "":
		section_id = None

	exam_id = request.GET.get("exam_id", None)
	if exam_id == "":
		exam_id = None
	
	course_id = request.GET.get("course_id", None)
	if course_id == "":
		course_id = None
	
	course_type = request.GET.get("course_type", None)
	if course_type == "":
		course_type = None

	download_format = downloadFormat
	staff_id = request.GET.get("staffId", None)
	report_type_name = request.GET.get("reportTypeName", None)
	
	section_id_str = request.GET.get("sectionIdsStr", None)
	section_id_set = None
	if section_id_str.strip():
		section_id_set = section_id_str.split(',')

	exam_id_str = request.GET.get("examIdsStr", None)
	exam_id_set = None
	if exam_id_str.strip():
		exam_id_set = exam_id_str.split(',')

	course_id_str = request.GET.get("courseIdsStr", None)
	course_id_set = None
	if course_id_str and course_id_str.strip():
		course_id_set = course_id_str.split(',')

	compare_cumulative_exam_id_str = request.GET.get("compareCumulativeExamIdsStr",None)
	compare_cumulative_exam_id_set = None
	if compare_cumulative_exam_id_str and compare_cumulative_exam_id_str.strip():
		compare_cumulative_exam_id_set = compare_cumulative_exam_id_str.split(',')

	rank_till = request.GET.get("rankTill", None)
	exclude_coscholastic_subjects = request.GET.get("exclude_coscholastic_subjects", False)
	required_headers = request.GET.get("requiredHeaders","")
	sort_student_on_rank = request.GET.get("sort_student_on_rank", False)
	
	additional_course_id_str = request.GET.get("additionalCourseIdsStr", None)
	additional_course_id_set = None
	if additional_course_id_str and additional_course_id_str.strip():
		additional_course_id_set = additional_course_id_str.split(',')

	scholastic_marks_display_type_str = request.GET.get("scholasticMarksDisplayTypeStr", None)
	scholastic_marks_display_type_set = None
	if scholastic_marks_display_type_str and scholastic_marks_display_type_str.strip():
		scholastic_marks_display_type_set = scholastic_marks_display_type_str.split(',')

	co_scholastic_marks_display_type_str = request.GET.get("coScholasticMarksDisplayTypeStr", None)
	co_scholastic_marks_display_type_set = None
	if co_scholastic_marks_display_type_str and co_scholastic_marks_display_type_str.strip():
		co_scholastic_marks_display_type_set = co_scholastic_marks_display_type_str.split(',')

	show_class_average_details = request.GET.get("show_class_average_details", False)
	show_staff_details = request.GET.get("show_staff_details", False)

	filterationCriteria	= {
		"academicSessionId" : academic_session_id, "standardId" : standard_id, "sectionId" : section_id, "examId" : exam_id, "courseId" : course_id, 
		"courseType" : course_type, "downloadFormat" : download_format, "reportCardType" : report_type_name, 
		"staffId" : staff_id, "sectionIdSet" : section_id_set, "examIdSet" : exam_id_set, "courseIdSet" : course_id_set,
		"compareCummulativeWithExamIdSet" : compare_cumulative_exam_id_set, "rankTill" : rank_till, 
		"excludeCoScholasticSubjects" : exclude_coscholastic_subjects, "requiredHeaders" : required_headers, 
		"sortStudentOnRank" : sort_student_on_rank, "additionalCoursesSet" : additional_course_id_set, 
		"scholasticMarksDisplayTypeSet" : scholastic_marks_display_type_set, "coScholasticMarksDisplayTypeSet" : co_scholastic_marks_display_type_set, 
		"showClassAverageDetails" : show_class_average_details, "showStaffDetails" : show_staff_details
	 }
	return filterationCriteria

def student_report_variable_pdf_view(request, institute_unique_code, session_id, student_id, report_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		report_card = get_student_report_card(user_login_view, institute_unique_code, session_id, student_id, report_type)
		if(report_card is not None):
			byte_array_json = list(report_card['content'])
			return render(request, 'examination/report_card_variables/report_card_variable_pdf.html',{'pdf_content':byte_array_json})

def student_report_variable_page_view(request, institute_unique_code, session_id, student_id, report_type, standard_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_type = request.GET.get("reportType", None)
		report_card_variable_proprities = get_report_card_variable_proprities(user_login_view, institute_unique_code)
		report_card_variables = get_report_card_variables(user_login_view, institute_unique_code, standard_id, session_id, section_id, report_type, student_id)
		studentReportCardVariableDetail = filter_current_student_report_card_variables(report_card_variables, student_id)
		return render(request, 'examination/report_card_variables/report_card_variable_page.html',{'report_card_variable_proprities' : report_card_variable_proprities, 'studentReportCardVariableDetail' : studentReportCardVariableDetail, 'student_id' : student_id, 'session_id' : session_id, 'report_type' : report_type, 'report_card_variables' : report_card_variables})

def filter_current_student_report_card_variables(report_card_variables, student_id):
	for studentReportCardVariableDetail in report_card_variables['studentReportCardVariableDetails']:
		studentId = studentReportCardVariableDetail['student']['studentId']
		if studentId == student_id:
			return studentReportCardVariableDetail
			break
	return None

def personality_traits_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		academic_session_id = current_session['academicSessionId']
		standard_personality_trait_details_list = get_personality_trait_details(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/personality_traits/personality_trait_details.html',{'standard_personality_trait_details_list':standard_personality_trait_details_list,'academic_years':academic_years, 'current_session':current_session})

def	personality_traits_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standard_personality_trait_details_list = get_personality_trait_details(user_login_view, institute_unique_code, academic_session_id)
		return render(request, 'examination/personality_traits/personality_trait_list.html',{'standard_personality_trait_details_list':standard_personality_trait_details_list})

@csrf_exempt
def bulk_add_personality_trait_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		personality_traits_detail_list = json.loads(request.POST['personalityTraitsDetailList'])
		response_data = bulk_add_personality_trait(user_login_view, institute_unique_code, personality_traits_detail_list, academic_session_id)
		return render(request, 'examination/personality_traits/personality_trait_status_modal.html',{"data":response_data})

@csrf_exempt
def bulk_update_personality_trait_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		personality_traits_detail_list = json.loads(request.POST['personalityTraitsDetailList'])
		response_data = bulk_update_personality_trait(user_login_view, institute_unique_code, personality_traits_detail_list,academic_session_id)
		return render(request, 'examination/personality_traits/personality_trait_status_modal.html',{"data":response_data})

@csrf_exempt
def bulk_delete_personality_trait_view(request, institute_unique_code, academic_session_id, standard_id, personality_traits_str):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = bulk_delete_personality_trait(user_login_view, institute_unique_code, academic_session_id, standard_id, personality_traits_str)
		return render(request, 'examination/personality_traits/personality_trait_status_modal.html',{"data":response_data})

def view_personality_trait_view(request, institute_unique_code, standard_id, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_type = request.GET.get("reportType", None)
		standard_personality_trait_details_list = get_standard_personality_trait_details(user_login_view, institute_unique_code, session_id, standard_id)
		student_personality_trait_details_list = get_student_personality_trait_details(user_login_view, institute_unique_code, standard_id, session_id, section_id, report_type)
		return render(request, 'examination/report_card_variables/view_personality_traits.html',{"student_personality_trait_details_list" : student_personality_trait_details_list, "standard_personality_trait_details_list" : standard_personality_trait_details_list})

@csrf_exempt
def submit_personality_traits_view(request, institute_unique_code, session_id, report_type):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		studentPersonalityTraitsPayloadList = json.loads(request.POST['studentPersonalityTraitsPayloadList'])
		response_data = submit_personality_traits(user_login_view, institute_unique_code, studentPersonalityTraitsPayloadList, session_id, report_type)
		return render(request, 'examination/report_card_variables/report_card_variable_update_status_modal.html',{"data":response_data})

## Exam Update Student Info
def student_details_home_v2_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
		return render(request, 'examination/update_student_info/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'standards' : standards, 'session_class_exams' : session_class_exams})

def update_page_session_change_v2_view(request, institute_unique_code, academic_session_id):
	user_login_view = get_user_login_view(request)
	standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
	session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
	return render(request, 'examination/update_student_info/main_screen_content.html',{'standards' : standards, 'session_class_exams' : session_class_exams})


def update_student_list_v2_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId",None)
		exam_id_str = request.GET.get("examIdStr",None)
		additional_courseIds_str = request.GET.get("additionalCourseIdsStr",None)
		student_field_details = get_update_student_field_exam_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, exam_id_str, additional_courseIds_str)
		return render(request, 'examination/update_student_info/student_list.html',{'student_field_details' : student_field_details, 'standard_id' : standard_id, 'section_id' : section_id})


@csrf_exempt
def update_student_details_v2_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		sectionId = request.GET.get("sectionId",None)
		update_field_payload = json.loads(request.POST['updateFieldPayload'])
		response_data = update_student_details_v2(user_login_view, institute_unique_code, academic_session_id, standard_id, sectionId, update_field_payload)
		return render(request, 'examination/update_student_info/status_modal.html', {"data":response_data, 'dataJson' : json.dumps(response_data)})
###


## Publish Report Cards
def publish_report_card_home_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
		return render(request, 'examination/publish_report_cards/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'standards' : standards, 'session_class_exams' : session_class_exams})

def publish_report_card_session_home_view(request, institute_unique_code, academic_session_id):
	user_login_view = get_user_login_view(request)
	standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
	session_class_exams = get_session_exams_class_wise(user_login_view, institute_unique_code)
	return render(request, 'examination/publish_report_cards/main_screen_content.html',{'standards' : standards, 'session_class_exams' : session_class_exams})


def get_report_card_variable_types_with_id_view(request, institute_unique_code, session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		report_card_types = get_report_card_types(user_login_view, institute_unique_code, session_id, standard_id)
		return render(request, 'examination/publish_report_cards/report_card_type_field_with_id.html',{'report_card_types' : report_card_types})

def student_report_card_status_view(request, institute_unique_code, academic_session_id, standard_id, report_card_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId",None)
		student_report_card_status_details_list = get_student_report_card_status_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, report_card_id)
		return render(request, 'examination/publish_report_cards/student_list.html',{'student_report_card_status_details_list' : student_report_card_status_details_list, 'report_card_id' : report_card_id})

@csrf_exempt
def update_student_report_card_status_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		student_report_card_status_payload = json.loads(request.POST['studentReportCardStatusPayload'])
		response_data = update_student_report_card_status(user_login_view, institute_unique_code, student_report_card_status_payload)
		return render(request, 'examination/publish_report_cards/status_modal.html', {"data":response_data, 'dataJson' : json.dumps(response_data)})

### Grade Scheme

def exam_grade_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		grade_details_list = get_grade_details(user_login_view, institute_unique_code, current_session['academicSessionId'])
		for grade_details in grade_details_list:
			scholastic_empty = not grade_details['standardExaminationGrades'].get('SCHOLASTIC', [])
			coscholastic_empty = not grade_details['standardExaminationGrades'].get('COSCHOLASTIC', [])
			grade_details['both_empty'] = scholastic_empty and coscholastic_empty
		return render(request, 'examination/grade_scheme/main_screen.html',{'academic_years':academic_years,'current_session':current_session, 'grade_details_list': grade_details_list})

def exam_grade_details_with_session_view(request, institute_unique_code, session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		grade_details_list = get_grade_details(user_login_view, institute_unique_code, session_id)
		return render(request, 'examination/grade_scheme/grading_list.html',{'grade_details_list': grade_details_list})

@csrf_exempt
def rename_grades_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		gradeRenamePayload = json.loads(request.POST['gradeRenamePayload'])

		response_data = rename_grades(user_login_view, institute_unique_code, academic_session_id, gradeRenamePayload)
		return render(request, 'examination/grade_scheme/status_modal.html',{"data":response_data})

@csrf_exempt
def add_update_grades_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		gradeAddUpdatePayload = json.loads(request.POST['gradeAddUpdatePayload'])

		response_data = add_or_update_grades(user_login_view, institute_unique_code, academic_session_id, gradeAddUpdatePayload)
		return render(request, 'examination/grade_scheme/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_grades_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		standard_id_str = request.GET.get("standardIdList","")
		response_data = delete_grades(user_login_view, institute_unique_code, academic_session_id, standard_id_str)
		return render(request, 'examination/grade_scheme/status_modal.html',{"data":response_data})


# HPC

def hpc_form_homepage_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/hpc/input/student_form_homepage.html',{'academic_years':academic_years,'current_session':current_session, 'standards' : standards})


def student_hpc_form_view(request, institute_unique_code, academic_session_id, student_id, exam_type):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student_hpc_form = get_student_hpc_form(user_login_view, institute_unique_code, student_id, academic_session_id, exam_type)
		authorised_to_update_submitted_data = is_authorised_for_action(user_login_view, institute_unique_code, MODULE_NAME, UPDATE_SUBMITTED_HPC_DATA)
		return render(request, 'examination/hpc/input/student_hpc_form_mainpage.html',{'student_hpc_form' : student_hpc_form, 'required_exam_type' : exam_type, 'authorised_to_update_submitted_data' : authorised_to_update_submitted_data})


@csrf_exempt
def hpc_form_data_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		document = None
		if 'document' in request.FILES:
			document = request.FILES.getlist('document')
		form_data = json.loads(request.POST['payload'])
		response_data = update_form_data(user_login_view, institute_unique_code, form_data, document)
		return render(request, 'examination/hpc/input/hpc_form_status_modal.html',{"data":response_data})

def class_students_list_view(request, institute_unique_code, academic_session_id):
    if authorized_user_session(request) and request.is_ajax():
        user_login_view = get_user_login_view(request)
        students = search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("text",""), request.GET.get("status", ""), request.GET.get("offset", None), request.GET.get("limit", None), request.GET.get("standardSection",""))
        return render(request, 'examination/hpc/input/student_list.html',{"students": students['result']})


def download_hpc_document_view(request, institute_unique_code, academic_session_id, exam_type, hpc_user_type, student_id, id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = download_hpc_document(user_login_view, institute_unique_code, academic_session_id, exam_type, hpc_user_type, student_id, id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/octet-stream")
			response['Content-Disposition'] = 'inline; filename='+document['file_name']
			return response

@csrf_exempt
def delete_hpc_document_view(request, institute_unique_code, academic_session_id, exam_type, hpc_user_type, student_id, id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_hpc_document(user_login_view, institute_unique_code, academic_session_id, exam_type, hpc_user_type, student_id, id)
		return render(request, 'examination/hpc/input/hpc_form_status_modal.html',{"data":response_data})

def hpc_report_card_generation_homepage_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/hpc_report_card/main_screen.html',{'academic_years':academic_years,'current_session':current_session, 'standards': standards, 'student' : None})

def hpc_report_card_student_details_view(request, institute_unique_code, session_id, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		student = get_student(user_login_view, institute_unique_code, session_id, student_id)
		if student is None:
			return render(request, 'examination/hpc_report_card/student_report_card_view.html',{'student' : None})
		return render(request, 'examination/hpc_report_card/student_report_card_view.html',{'student' : student})

def student_hpc_report_card_view(request, institute_unique_code, session_id, student_id, exam_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		report_card = get_student_hpc_report_card(user_login_view, institute_unique_code, session_id, student_id, exam_type)
		if report_card is not None and report_card['success']:
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename=' + report_card['file_name']
			return response
		return HttpResponse("<strong> " + report_card["error_reason"] +" </strong>")

def class_hpc_report_card_view(request, institute_unique_code, session_id, standard_id, exam_type):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_card = get_class_hpc_report_card(user_login_view, institute_unique_code, session_id, standard_id, section_id, exam_type)
		if report_card is not None and report_card['success']:
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename=' + report_card['file_name']
			return response
		return HttpResponse("<strong> " + report_card["error_reason"] +" </strong>")

def bulk_students_hpc_report_card_view(request, institute_unique_code, session_id, standard_id, exam_type,student_ids):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId", None)
		report_card = get_bulk_student_hpc_report_card(user_login_view, institute_unique_code, session_id, standard_id, section_id, exam_type,student_ids)
		if report_card is not None and report_card['success']:
			response = HttpResponse(report_card['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename=' + report_card['file_name']
			return response
		return HttpResponse("<strong> " + report_card["error_reason"] +" </strong>")

def hpc_class_students_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards = request.GET.get("standards","")
		sectionIds = request.GET.get("sectionId","")
		if(standards == ""):
			return render(request, 'examination/report_card/student_list.html',{'students': None})
		students = get_class_student_list(user_login_view, institute_unique_code, academic_session_id, standards, sectionIds)
		return render(request, 'examination/hpc_report_card/student_list.html',{'students':students})

## Publish Report Cards
def publish_hpc_report_card_home_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'examination/hpc_publish_report_cards/main_screen.html',{'academic_years':academic_years, 'current_session':current_session, 'standards' : standards})

def publish_hpc_report_card_session_home_view(request, institute_unique_code, academic_session_id):
	user_login_view = get_user_login_view(request)
	standards = get_standards(user_login_view, institute_unique_code, academic_session_id)
	return render(request, 'examination/hpc_publish_report_cards/main_screen_content.html',{'standards' : standards})

def student_hpc_report_card_status_view(request, institute_unique_code, academic_session_id, standard_id, exam_card_type):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("sectionId",None)
		student_report_card_status_details_list = get_student_hpc_report_card_status_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, exam_card_type)
		return render(request, 'examination/hpc_publish_report_cards/student_list.html',{'student_report_card_status_details_list' : student_report_card_status_details_list, 'exam_card_type' : exam_card_type})

@csrf_exempt
def update_hpc_student_report_card_status_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		student_hpc_report_card_status_payload = json.loads(request.POST['studentHPCReportCardStatusPayload'])
		response_data = update_hpc_student_report_card_status(user_login_view, institute_unique_code, current_session['academicSessionId'], student_hpc_report_card_status_payload)
		return render(request, 'examination/hpc_publish_report_cards/status_modal.html', {"data":response_data, 'dataJson' : json.dumps(response_data)})
