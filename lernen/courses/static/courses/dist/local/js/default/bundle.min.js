var STUDENT_ASSIGNED_COURSES_LIST="student-assigned-courses-list",STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-student-assignment",BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID="select-bulk-student-assignment";$(document).ready(function(){menuLoader.registerSidebarMenu(),courses.loadCourseConfigurationPage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerCourseConfigurationMenu(),menuLoader.registerCourseAssignmentMenu(),menuLoader.registerReportsNav()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerCourseConfigurationMenu:function(){$("#configureCourseNav").on("click",function(){courses.loadCourseConfigurationPage()})},registerCourseAssignmentMenu:function(){$("#assignCourseNav").on("click",function(){courseAssignment.loadCourseAssignmentPage()})},registerReportsNav:function(){$("#reportCourseNav").on("click",function(){courseReports.loadReportsPage()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession),homePage.displayDashboardContent()},loadHomePage:function(){ajaxClient.get("/courses/home",function(e){$("#main-content").html(e),homePage.initHomePage()})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/courses/session-home/"+e,function(e){$("#courses-dashboard-session-content").html(e),homePage.displayDashboardContent()})},refreshHomePage:function(){homePage.loadHomePageForSession()},displayDashboardContent:function(){}},courses={dataCache:{},initDataCache:function(){var e=readJson("#add-courses-standards");courses.dataCache={},courses.dataCache.standards=e,courses.updateDataCacheOnNewConfiguredClass(),courses.dataCache.count=0},updateDataCacheOnNewConfiguredClass:function(){courses.dataCache.configuredStandards=[],courses.dataCache.configuredClassCoursesMap={};for(var e=readJson("#configured-class-courses-info"),s=[],t={},o=0;o<e.length;o++)s.push(e[o].standard),t[e[o].standard.standardId]=e[o];courses.dataCache.configuredStandards=s,courses.dataCache.configuredClassCoursesMap=t},loadCourseConfigurationPage:function(){ajaxClient.get("/courses/class-course-config",function(e){$("#main-content").html(e),courses.initDataCache(),courses.bindStandardChangeEvent(),academicSessionHandler.bindSessionChangeEvent(courses.changeSession)})},loadCourseConfiguredClasses:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/courses/configured-class-courses/"+e,function(e){$("#configured-class-courses-list").html(e),courses.updateDataCacheOnNewConfiguredClass()})},changeSession:function(){courses.loadCourseConfiguredClasses()},populateAddClassCoursesModal:function(){$("#error-reason").attr("style","color:red; display:none;"),$("#configure-courses-class-list").attr("style","");var e=academicSessionHandler.getSelectedSession();$("#add-class-courses-session-display").val(e.displayName);for(var s=courses.dataCache.standards,t=courses.dataCache.configuredStandards,o={},a=0;a<t.length;a++)o[t[a].standardId]=t[a];var c=[];for(a=0;a<s.length;a++)s[a].standardId in o||c.push(s[a]);var n='<option value=""> select class</option>';for(a=0;a<c.length;a++)n+='<option value="'+c[a].standardId+'">'+c[a].displayName+"</option>";$("#configure-courses-class-list").html(n);var r='<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th scope="col">Mandatory</th> <th scope="col">Course Type</th> <th scope="col">Course Name</th><th scope="col">Sequence</th> </tr> </thead> <tbody> '+courses.getBlankCourseRow()+' <tr id="new-course-add-row-bottom" style="display:none;"></tr> </tbody> </table> </div> <div class="col-md-1"> </div> </div>';$("#add-class-courses-input-div").html(r)},getBlankCourseRow:function(e){courses.dataCache.count++;var s="customSwitch"+courses.dataCache.count;return'<tr class="new-course-row"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+s+'" checked> <label class="custom-control-label" for="'+s+'"></label> </div> </td> <td> <select id="" class="form-control form-control-sm mandatory-field course-type"> <option value="SCHOLASTIC">Scholastic</option> <option value="COSCHOLASTIC">Co-Scholastic</option> </select> </td> <td><input type="text" class="form-control form-control-sm add-course-name" placeholder="Course Name..."></td><td><input type="number" class="form-control form-control-sm add-course-sequence" placeholder="Course Sequence..." style="width: 75px;" onChange="courses.validateCourseSequence(this,false)"></td> <td> <button type="button" onclick="courses.deleteCourseRow(this)" class="close delete-course-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'},validateCourseSequence:function(e,s){var t=[];s?$("tr.update-course-row").each(function(){var e=$(this).find(".update-course-sequence").val().trim();t.push(e)}):$("input.add-course-sequence").each(function(){var e=$(this).val().trim();t.push(e)}),coursesFilteredList=[],t.forEach(function(e){""!=e&&coursesFilteredList.push(e)});var o=Array.from(new Set(t));if(coursesFilteredSet=[],o.forEach(function(e){""!=e&&coursesFilteredSet.push(e)}),coursesFilteredList.length>1&&coursesFilteredList.length!=coursesFilteredSet.length)return alert("Duplicate Sequencing is not allowed"),void $(e).val("")},validateSequence:function(e,s){var t=[];s?$("tr.update-course-row").each(function(){$(this).find(".course-sequence").val().trim()}):$("input.add-course-sequence").each(function(){$(this).val().trim()}),coursesFilteredList=[],t.forEach(function(e){""!=e&&coursesFilteredList.push(e)});var o=Array.from(new Set(t));if(coursesFilteredSet=[],o.forEach(function(e){""!=e&&coursesFilteredSet.push(e)}),coursesFilteredList.length>1&&coursesFilteredList.length!=coursesFilteredSet.length)return s?$("#update-class-courses-modal").modal("toggle"):$("#add-class-courses-modal").modal("toggle"),alert("Duplicate Sequencing is not allowed"),void $(e).val("")},addNewCourseRow:function(){$("#add-course-add-row-bottom").before(courses.getBlankCourseRow())},deleteCourseRow(e){$(e).parent().parent().remove()},bindStandardChangeEvent:function(){$("#configure-courses-class-list").on("change",function(){var e=$(this).find(":selected").val().trim();void 0!==e&&""!==e&&$("#error-reason").attr("style","color:red; display:none;")})},submitNewClassCourses:function(){if(validateMandatoryFields($("#add-class-courses-modal")))$("#error-reason").attr("style","color:red; display:block;");else{$("#add-class-courses-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),s=$("#configure-courses-class-list").find(":selected").val().trim(),t=[];$("tr.new-course-row").each(function(){var e=$(this).find(".custom-control-input:checkbox:checked").length>0,s=$(this).find(".course-type").find(":selected").val().trim(),o=$(this).find(".course-name").val().trim(),a=$(this).find(".course-sequence").val().trim();""==a&&(a=null),t.push({courseType:s,courseName:o,mandatory:e,sequence:a})});var o={academicSessionId:e,standardId:s,courses:t};ajaxClient.post("/courses/add-class-courses",{classCoursesPayload:JSON.stringify(o)},function(e){$("#course-page-status-modal-container").html(e),$("#course-page-status-modal").modal({backdrop:"static",keyboard:!1}),courses.loadCourseConfiguredClasses(),courses.dataCache.count=0})}},deleteStandardCoursesWithError:function(e){var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/courses/courses-with-error/"+e+"/"+s,function(s){$("#courses-with-error-message-div").html(s),courses.populateDeleteClassCoursesModal(e)})},populateDeleteClassCoursesModal:function(e){var s=academicSessionHandler.getSelectedSession(),t=JSON.parse($("#courses-with-error-message").text());$("#delete-class-courses-modal").modal("toggle"),$("#delete-class-courses-session-display").val(s.displayName);var o=courses.dataCache.configuredClassCoursesMap[e];$("#delete-class-courses-class-display").val(o.standard.displayName),$("#delete-class-courses-standrad-id").val(o.standard.standardId);for(var a='<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th style="vertical-align: middle; text-align: center; padding-left: 48px; padding-bottom: 30px;"> <input type="checkbox" class="form-check-input course-select-all-checkbox" onclick="courses.selectAllCourses(this)"></th> <th scope="col">Course Type</th> <th scope="col">Course Name</th> <th scope="col">Sequence</th>  <th scope="col"></th></tr> </thead> <tbody> ',c=0;c<o.courses.length;c++){courses.dataCache.count+=1;var n=o.courses[c],r=n.courseId;n.mandatory&&"checked";var l=t[r],i=(courses.dataCache.count,'<tr class="delete-course-row"><td><div class="custom-control courses-remove"><input type="checkbox" class="course-id" name="" value="" '+(0==l.length?"":"disabled")+'></div></td><td><select id="" class="form-control form-control-sm mandatory-field course-type" disabled><option value="SCHOLASTIC" ~selectScholastic~ disabled>Scholastic</option><option value="COSCHOLASTIC" ~selectCoscholastic~ disabled>Co-Scholastic</option></select></td><td><p class="bulk-course-id" style="display:none;">'+r+'</p><input type="text" class="form-control form-control-sm delete-rename-manage-course" placeholder="Course Name..." value="'+n.courseName+'" disabled></td><td><input type="number" class="form-control form-control-sm course-sequence" placeholder="Course Sequence..." style="width: 75px;" value="'+n.sequence+'" disabled></td><td style="vertical-align: center; padding-top : 20px;"><a onclick="courses.toggleErrorDetails(this,\''+r+'\')" style="'+(0==l.length?"display: none;":"color: red;")+'"><u><p class="pb-0" style="font-size: 10px; ">Can\'t Delete<i class="bi bi-chevron-down toggle-arrow" style="margin-left: 5px;"></i></p></u></a></td></tr>');i=n.courseTypeDisplay.includes("Co-Scholastic")?(i=i.replace("~selectScholastic~","")).replace("~selectCoscholastic~","selected"):(i=i.replace("~selectScholastic~","selected")).replace("~selectCoscholastic~",""),l&&(i+='<tr class="error-row" id="'+r+'" style="display:none;"><td colspan="5" style="text-align: center; padding: 10px; padding-left: 35px"><div class="card-border" style="display: inline-block; width: 85%; padding: 10px; "><div style="font-size: 12px; margin-bottom: 10px; text-align: left;">Please resolve the below issues before deleting this course.</div><ul style="margin: 0; padding-left: 15px; list-style-type: disc; text-align: left;">',l.forEach(function(e){const s=e.split(":");let t="",o="";s.length>0&&void 0!==s[0]&&(t=s[0].trim()),s.length>1&&void 0!==s[1]&&(o=s[1].trim()),i+="<li style=\"font-size: 12px; margin: 5px 0;\"><strong style='font-weight: 800;'>"+t+"</strong>: "+o+"</li>"}),i+="</ul></div></td></tr>"),a+=i}a+='<tr id="update-course-add-row-bottom" style="display:none;"></tr></tbody></table></div><div class="col-md-1"></div></div>',$("#delete-class-courses-input-div").html(a)},toggleErrorDetails:function(e,s){$("#"+s).toggle();var t=$(e).find(".toggle-arrow");t.hasClass("bi-chevron-down")?t.removeClass("bi-chevron-down").addClass("bi-chevron-up"):t.removeClass("bi-chevron-up").addClass("bi-chevron-down")},deleteClassCourses:function(){$("#delete-class-courses-modal").modal("toggle");var e=courses.getDictionaryCourseInfos();academicSessionHandler.getSelectedSessionId(),$("#delete-class-id").val();if(options="",checkedCourse=e[0],unCheckedCourse=e[1],0==checkedCourse.length)return $("#delete-manage-course-modal").modal("toggle"),void showErrorDialogBox("Please Select atleast one course to delete");if(0!=unCheckedCourse.length){html1="";for(var s=0;s<unCheckedCourse.length;s++)html1="<option value="+unCheckedCourse[s].courseId+">"+unCheckedCourse[s].courseName+"</option>",options+=html1}$("#delete-manage-course-modal").modal("toggle");var t=[],o=[];for(s=0;s<checkedCourse.length;s++)courseId=checkedCourse[s].courseId,courseName=checkedCourse[s].courseName,t.push(courseId),o.push(courseName);t=t.join(","),courses.dataCache.courseIds=t,html="Do you wish to delete the selected courses?",$("#delete-standard-course-manage-id").html(html),$("#delete-id-manage-course-modal").modal("toggle")},deleteStudentCourseDetails:function(){$("#delete-id-manage-course-modal").modal("toggle"),course_ids=courses.dataCache.courseIds;var e=academicSessionHandler.getSelectedSessionId(),s=$("#delete-class-courses-standrad-id").val().trim();ajaxClient.post("/courses/delete-class-courses/"+s+"/"+e+"/"+course_ids,{},function(e){$("#course-page-status-modal-container").html(e),$("#course-page-status-modal").modal("toggle"),courses.loadCourseConfiguredClasses()})},selectAllCourses:function(e){e.checked?$(".course-id:not(:disabled)").prop("checked",!0):$(".course-id:not(:disabled)").prop("checked",!1)},getDictionaryCourseInfos:function(){var e=[],s=[],t=[];return $("tr.delete-course-row").each(function(){$(this).find("input.course-id").is(":checked")?(courseName=$(this).find("input.delete-rename-manage-course").val(),courseId=$(this).find("p.bulk-course-id").first().text().trim(),course_dict={courseId:courseId,courseName:courseName},t.push(course_dict)):(courseName=$(this).find("input.delete-rename-manage-course").val(),courseId=$(this).find("p.bulk-course-id").first().text().trim(),course_dict={courseId:courseId,courseName:courseName},s.push(course_dict))}),e.push(t),e.push(s),e},populateUpdateClassCoursesModal:function(e){var s=academicSessionHandler.getSelectedSession();$("#update-class-courses-session-display").val(s.displayName);var t=courses.dataCache.configuredClassCoursesMap[e];$("#update-class-courses-class-display").val(t.standard.displayName),$("#update-class-courses-standrad-id").val(t.standard.standardId);for(var o='<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th scope="col">Mandatory</th> <th scope="col">Course Type</th> <th scope="col">Course Name</th> <th scope="col">Sequence</th>  </tr> </thead> <tbody> ',a=0;a<t.courses.length;a++){courses.dataCache.count+=1;var c=t.courses[a],n="",r=c.courseId;c.mandatory&&(n="checked");var l="customSwitch"+courses.dataCache.count,i='<tr class="update-course-row"> <td> <div class="update-custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+l+'" '+n+' ><label class="custom-control-label" for="'+l+'"></label> </div>  </td> <td><select id="" class="form-control form-control-sm mandatory-field update-course-type"><option value="SCHOLASTIC" ~selectScholastic~>Scholastic</option> <option value="COSCHOLASTIC" ~selectCoscholastic~>Co-Scholastic</option> </select> </td> <td><p class="course-id" style="display:none;">'+r+'</p><input type="text" class="form-control form-control-sm update-course-name" placeholder="Course Name..." value = "'+c.courseName+'" </td>  <td><input type="number" class="form-control form-control-sm update-course-sequence" placeholder="Sequence..." style="width: 105px;" value = "'+c.sequence+'" onChange="courses.validateCourseSequence(this,true)" ></td> <td> <button type="button" onclick="courses.deleteCourseRow(this)" class="close delete-course-row" aria-label="Close"> </button> </td> </tr>';o+=i=c.courseTypeDisplay.includes("Co-Scholastic")?(i=i.replace("~selectScholastic~","")).replace("~selectCoscholastic~","selected"):(i=i.replace("~selectScholastic~","selected")).replace("~selectCoscholastic~","")}o+=' <tr id="update-course-add-row-bottom" style="display:none;"></tr> </tbody> </table> </div> <div class="col-md-1"> </div> </div>',$("#update-class-courses-input-div").html(o)},updateClassCourses:function(){var e,s=academicSessionHandler.getSelectedSessionId(),t=$("#update-class-courses-standrad-id").val().trim(),o=0,a=courses.dataCache.configuredClassCoursesMap[t],c=[];e=courses.getUpdateCourse();var n=Array.from(new Set(e));if(e.length==n.length){$("tr.update-course-row").each(function(){var e=$(this).find(".custom-control-input:checkbox:checked").length>0,s=$(this).find(".update-course-type").find(":selected").val().trim(),t=$(this).find(".update-course-name").val().trim(),n=$(this).find(".update-course-sequence").val().trim();""==n&&(n=null),c.push({courseType:s,courseName:t,courseId:a.courses[o].courseId,mandatory:e,sequence:n}),o++}),$("#update-class-courses-modal").modal("toggle");var r={academicSessionId:s,standardId:t,courses:c};ajaxClient.post("/courses/update-class-courses/"+s,{classCoursesPayload:JSON.stringify(r)},function(e){$("#course-page-status-modal-container").html(e),$("#course-page-status-modal").modal({backdrop:"static",keyboard:!1}),courses.loadCourseConfiguredClasses(),courses.dataCache.count=0})}else alert("Duplicate Class Courses are not allowed")},getUpdateCourse:function(){var e=[];return $("tr.update-course-row").each(function(){var s=$(this).find(".update-course-name").val().trim();e.push(s.toLowerCase())}),e},populateAddClassCoursesModal:function(e){var s=academicSessionHandler.getSelectedSession();$("#add-class-courses-session-display").val(s.displayName);var t=courses.dataCache.configuredClassCoursesMap[e];$("#add-class-courses-class-display").val(t.standard.displayName),$("#add-class-courses-standrad-id").val(t.standard.standardId);for(var o='<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th scope="col">Mandatory</th> <th scope="col">Course Type</th> <th scope="col">Course Name</th> <th scope="col">Sequence</th>  </tr> </thead> <tbody> ',a=0;a<t.courses.length;a++){courses.dataCache.count+=1;var c=t.courses[a],n="";c.mandatory&&(n="checked");var r="customSwitch"+courses.dataCache.count,l='<tr class="existing-course-row"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+r+'" '+n+' disabled > <label class="custom-control-label" for="'+r+'" disabled></label> </div>  </td> <td><select id="" class="form-control form-control-sm mandatory-field course-type" disabled><option value="SCHOLASTIC" ~selectScholastic~ disabled>Scholastic</option> <option value="COSCHOLASTIC" ~selectCoscholastic~ disabled>Co-Scholastic </option> </select> </td> <td><input type="text" class="form-control form-control-sm add-course-name" placeholder="Course Name..." value = "'+c.courseName+'" disabled></td>  <td><input type="number" class="form-control form-control-sm add-course-sequence" placeholder="Course Sequence..." style="width: 75px;" value = "'+c.sequence+'" onChange="courses.validateCourseSequence(this,false)" disabled></td>  </tr>';o+=l=c.courseTypeDisplay.includes("Co-Scholastic")?(l=l.replace("~selectScholastic~","")).replace("~selectCoscholastic~","selected"):(l=l.replace("~selectScholastic~","selected")).replace("~selectCoscholastic~","")}o+=' <tr id="add-course-add-row-bottom" style="display:none;"></tr> </tbody> </table> </div> <div class="col-md-1"> </div> </div>',$("#add-class-courses-input-div").html(o)},addClassCourses:function(){var e=academicSessionHandler.getSelectedSessionId(),s=$("#add-class-courses-standrad-id").val().trim(),t=courses.getNewCourseName(),o=Array.from(new Set(t));if(t.length==o.length){var a=[];if($("tr.new-course-row").each(function(){var e=$(this).find(".custom-control-input:checkbox:checked").length>0,s=$(this).find(".course-type").find(":selected").val().trim(),t=$(this).find(".add-course-sequence").val().trim(),o=$(this).find(".add-course-name").val();""==t&&(t=null),a.push({courseType:s,courseName:o,mandatory:e,sequence:t})}),0!=a.length){$("#add-class-courses-modal").modal("toggle");var c={academicSessionId:e,standardId:s,courses:a};ajaxClient.post("/courses/add-class-courses",{classCoursesPayload:JSON.stringify(c)},function(e){$("#course-page-status-modal-container").html(e),$("#course-page-status-modal").modal("toggle"),courses.loadCourseConfiguredClasses(),courses.dataCache.count=0})}else alert("Please Add atleast one Course")}else alert("Duplicate Class Courses are not allowed")},getNewCourseName:function(){var e=new Array;return $("input.add-course-name").each(function(){var s=$(this).val().trim();e.push(s.toLowerCase())}),e}},courseAssignment={dataCache:{},initDataCache:function(){courseAssignment.dataCache={};var e=readJson("#assigned-optional-scholastic-courses"),s=readJson("#assigned-optional-co-scholastic-courses"),t=readJson("#unassigned-optional-scholastic-courses"),o=readJson("#unassigned-optional-co-scholastic-courses");courseAssignment.dataCache.optionalScholasticCourses=e,courseAssignment.dataCache.optionalCoScholasticCourses=s,courseAssignment.dataCache.unassignedOptionalScholasticCourses=t,courseAssignment.dataCache.unassignedOptionalCoScholasticCourses=o},loadCourseAssignmentPage:function(){ajaxClient.get("/courses/course-assignment",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(courseAssignment.changeSession),courseAssignment.registerCourseAssignmentTabs(),courseAssignment.loadStudentScreen()})},changeSession:function(){var e=$(".course-assign-options.active").attr("id");e==STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID?courseAssignment.loadStudentScreen():e==BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID&&courseAssignment.loadBulkStudentScreen()},loadStudentScreen:function(){academicSessionHandler.getSelectedSessionId();ajaxClient.get("/courses/student-course-assignment",function(e){$("#course-assignment-screen").html(e),courseAssignment.bindSearchStudentCourseAssignmentEvent()})},loadBulkStudentScreen:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/courses/bulk-course-assignment/"+e,function(e){$("#course-assignment-screen").html(e),courseAssignment.bindSearchStudentCourseAssignmentEvent(),courseAssignment.bulkStandardChange()})},modifyStudentCourseAssignmentCallBack:function(){$("#student-modify-course-assignment-button").on("click",function(){courseAssignment.fillCurrentStudentCourseAssignment(),$("#modify-student-course-assignment-modal").modal("toggle")})},fillCurrentStudentCourseAssignment:function(){var e=1,s=courseAssignment.dataCache.optionalScholasticCourses,t=courseAssignment.dataCache.optionalCoScholasticCourses,o=courseAssignment.dataCache.unassignedOptionalScholasticCourses,a=courseAssignment.dataCache.unassignedOptionalCoScholasticCourses,c="";if(s.length>0)for(i=0;i<s.length;i++){var n="customSwitch"+e;e+=1,c=c+'<tr class="course-row" id="'+s[i].courseId+'"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+n+'" checked> <label class="custom-control-label" for="'+n+'"></label> </div> </td> <td><input type="text" class="form-control form-control-sm course-name" value="'+s[i].courseName+'" readonly></td> </tr>'}if(o.length>0)for(i=0;i<o.length;i++){n="customSwitch"+e;e+=1,c=c+'<tr class="course-row" id="'+o[i].courseId+'"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+n+'"> <label class="custom-control-label" for="'+n+'"></label> </div> </td> <td><input type="text" class="form-control form-control-sm course-name" value="'+o[i].courseName+'" readonly></td> </tr>'}var r="";r=""!=c?'<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th scope="col">#Select</th> <th scope="col">Course Name</th> </tr> </thead> <tbody> '+c+"  </tbody> </table> </div>  </div>":"<p> No optional scholastic courses available</p>",$("#update\\.optional-scholastic-course").html(r);var l="";if(t.length>0)for(i=0;i<t.length;i++){n="customSwitch"+e;e+=1,l=l+'<tr class="course-row" id="'+t[i].courseId+'"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+n+'" checked> <label class="custom-control-label" for="'+n+'"></label> </div> </td> <td><input type="text" class="form-control form-control-sm course-name" value="'+t[i].courseName+'" readonly></td> </tr>'}if(a.length>0)for(i=0;i<a.length;i++){n="customSwitch"+e;e+=1,l=l+'<tr class="course-row" id="'+a[i].courseId+'"> <td> <div class="custom-control custom-switch"> <input type="checkbox" class="custom-control-input" id="'+n+'"> <label class="custom-control-label" for="'+n+'"></label> </div> </td> <td><input type="text" class="form-control form-control-sm course-name" value="'+a[i].courseName+'" readonly></td> </tr>'}var d="";d=""!=l?'<div class="row"> <div class="col-md-1"> </div> <div class="col-md-10"> <table class="table borderless"> <thead> <tr> <th scope="col">#Select</th> <th scope="col">Course Name</th> </tr> </thead> <tbody> '+l+"  </tbody> </table> </div>  </div>":"<p> No optional co-scholastic courses available</p>",$("#update\\.optional-co-scholastic-course").html(d)},updateStudentOptionalCourseAssignment:function(){$("#modify-student-course-assignment-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),s=$("#course-assign-view-student-id").text().trim(),t=[];$("tr.course-row").each(function(){if(!(!$(this).find(".custom-control-input:checkbox:checked").length>0)){var e=$(this).attr("id");t.push(e)}});var o={studentId:s,courseIds:t};ajaxClient.post("/courses/update-student-course-assignment/"+e,{studentCourseAssignment:JSON.stringify(o)},function(e){$("#course-page-status-modal-container").html(e),$("#course-page-status-modal").modal({backdrop:"static",keyboard:!1}),courseAssignment.loadStudentCourseAssignment(s)})},doneStudentSearchTyping:function(e){var s=$("#course-assign-search-text").val().trim(),t=academicSessionHandler.getSelectedSessionId();coursesCommomMethods.studentLiveSearchEvent(t,s,e,courseAssignment.loadStudentCourseAssignment)},loadStudentCourseAssignment:function(e){var s=academicSessionHandler.getSelectedSessionId();courseAssignment.loadStudentCoursesAssignment(s,e,STUDENT_ASSIGNED_COURSES_LIST)},loadStudentCoursesAssignment:function(e,s,t){s=s.trim(),""!=e?ajaxClient.get("/courses/assigned-courses/"+e+"/"+s,function(e){$("#"+t).html(e),courseAssignment.initDataCache(),courseAssignment.modifyStudentCourseAssignmentCallBack()}):showErrorDialogBox("Please select session to assign course.")},registerCourseAssignmentTabs:function(){$("#"+STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){courseAssignment.loadStudentScreen()}),$("#"+BULK_STUDENT_ASSIGNMENT_SCREEN_SELECTION_BUTTON_ID).on("click",function(){courseAssignment.loadBulkStudentScreen()})},bindSearchStudentCourseAssignmentEvent:function(){var e="#course-assign-student-search-result";$("#course-assign-search-student").on("click",function(){courseAssignment.doneStudentSearchTyping(e)}),$("#course-assign-search-text").on("keyup",function(s){13==s.keyCode&&courseAssignment.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#course-assign-search-text",e,courseAssignment.doneStudentSearchTyping)},resetToMainScreen:function(){feeAssignment.returnToStudentMainScreen(),feeAssignment.returnToBulkStudentMainScreen()},returnToStudentMainScreen:function(){$(".student-course-assign-tab").attr("style","display:none"),$("#student-course-assign-main-tab").attr("style","display:block")},bulkStandardChange:function(){$("#bulk-course-assign-class").change(function(){var e=academicSessionHandler.getSelectedSessionId(),s=$(this).val().split(":")[0].trim();void 0!==s&&""!==s?ajaxClient.get("/courses/class-students/"+e+"/"+s,function(e){$(".optional-course-id").html(e)}):showErrorDialogBox("Please select a valid class!")})},getStudentCoursesDetails:function(){var e=academicSessionHandler.getSelectedSessionId(),s=$("#bulk-course-assign-class").val().split(":"),t=s[0].trim(),o=0;if(s.length>1&&(o=s[1].trim()),void 0!==t&&""!==t){var a=$(".optional-course-id").val();void 0!==a&&""!==a?ajaxClient.get("/courses/students-course-details/"+e+"/"+t+"/"+a+"?sectionId="+o,function(e){$("#bulk-student-list").html(e),bulkAssignmentStudentSelectCheckbox(),$("#datatables-reponsive").DataTable({paging:!1,searching:!1,columnDefs:[{orderable:!1,targets:"no-sort"}]})}):showErrorDialogBox("Please select a valid optional course to assign!")}else showErrorDialogBox("Please select a valid class!")}},courseReports={dataCache:{},loadReportsPage:function(){ajaxClient.get("/courses/reports",function(e){$("#main-content").html(e),initSelect2("All"),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),courseReports.bindSessionDropDown(),courseReports.bindStandardDropDown(),courseReports.bindGenerateReportEvent(),courseReports.bindOptionalStandardDropDown()})},bindSessionDropDown:function(){$(".report-academic-session").on("change",function(){$(this).find(":selected").val().trim();var e=$(this).closest(".modal-body").find(".standard-json").html();$(this).closest(".modal-body").find(".different-value-wrapper").html(e),courseReports.bindStandardDropDown(),$(this).closest(".modal-body").find(".report-standard-section").html(""),$(this).closest(".modal-body").find(".report-courses").html(""),$(this).closest(".modal-body").find(".report-standards").val("")})},bindStandardDropDown:function(){$(".report-standards").on("change",function(){var e=$(this).closest(".modal-body").find(".report-academic-session").find(":selected").val().trim(),s=$(this).find(":selected").val().trim();$(this).closest(".modal-body").find(".report-standard-section").html(""),$(this).closest(".modal-body").find(".report-courses").html("");courseReports.fillStudentSections(this,e,s,"")})},bindOptionalStandardDropDown:function(){$(".report-standards-optional").on("change",function(){var e=$(this).closest(".modal-body").find(".report-academic-session").find(":selected").val().trim(),s=$(this).find(":selected").val().trim();$(this).closest(".modal-body").find(".report-standard-section").html(""),$(this).closest(".modal-body").find(".report-courses").html("");courseReports.fillStudentSections(this,e,s,!1)})},fillStudentSections:function(e,s,t,o){""!==t&&void 0!==t&&ajaxClient.get("/courses/standard-details/"+t+"/"+s,function(e){$(".section-json-wrapper").html(e);for(var a=readJson("#standard-details-p"),c={},n=0;n<a.length;n++){c[(l=a[n]).standardId]=l}var r="";if(t in c){var l=c[t];for(n=0;n<l.standardSectionList.length;n++)r+='<option value="'+l.standardSectionList[n].sectionId+'">'+l.standardSectionList[n].sectionName+"</option>";$(".report-standard-section").html(r),courseReports.fillCoursesDropDown(this,s,t,o)}else $("select.student-section").html(r)})},fillCoursesDropDown:function(e,s,t,o){ajaxClient.get("/courses/course-details/"+t+"/"+s+"?showCoursesWithMandatoryFlag="+o,function(e){$(".report-courses").html(e)})},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){$(this).closest("div.modal").modal("toggle");var e=$(this).closest("div.report-field-container"),s=$(e).find(".report-academic-session option:selected").val(),t=$(e).find(".report-standard-section").val(),o=$(e).find(".report-courses").val(),a=$(e).find(".report-type").html(),c="STUDENT_OPTIONAL_COURSE_REPORT"===a?$(e).find(".report-standards-optional option:selected").val():$(e).find(".report-standards option:selected").val();""!=s&&""!=c&&null!=c?(t=void 0===t||0==t.length?"":t.join(","),o=void 0===o||0==o.length?"":o.join(","),window.open(baseURL+"/courses/generate-report?standard_id="+c+"&course_id="+o+"&section_id="+t+"&academicSessionId="+s+"&report_type="+a,"_blank")):showErrorDialogBox("Please select all the required fields to generate report")})}},coursesCommomMethods={studentLiveSearchEvent:function(e,s,t,o){ajaxClient.get("/courses/student-live-search/"+e+"?searchText="+s,function(e){$(t).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(t,o)})}};function selectBulkAssignmentStudentList(e){if(e.checked){$(".bulk-student-assignment-checkbox").prop("checked",!0);var s=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(s)}else $(".bulk-student-assignment-checkbox").prop("checked",!1),$("#bulk-assign-selected-count").html(0)}function updateBulkAssignmentStudentConfirmModal(){var e=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assignment-confirmation-modal-text").text("You have selected "+e+" students to assign course at student level. Do you want to proceed?")}function bulkAssignmentStudentSelectCheckbox(e){var s=$("input.bulk-student-assignment-checkbox:checkbox:checked").length;$("#bulk-assign-selected-count").html(s)}function getBulkFeesAssignmentScreen(){$("#bulk-assignment-confirmation-modal").modal("toggle");var e=academicSessionHandler.getSelectedSessionId(),s=[],t=[];$("input.bulk-student-assignment-checkbox").each(function(){var e=$(this).parent().find("p.bulk-fee-assign-student-id").first().text().trim();$(this).is(":checked")?s.push(e):t.push(e)});var o=$("#bulk-course-assign-class").val().split(":")[0].trim();if(void 0!==o&&""!==o){var a=$(".optional-course-id").val();if(void 0!==a&&""!==a){var c={instituteId:null,academicSessionId:e,courseId:a,assignCourseStudentList:s,removeCourseStudentList:t};ajaxClient.post("/courses/bulk-student-course-assignment",{bulkStudentCoursesPayload:JSON.stringify(c)},function(e){$("#course-page-status-modal-container").html(e),$("#fee-assignment-status-modal").modal({backdrop:"static",keyboard:!1}),courseAssignment.getStudentCoursesDetails()})}else showErrorDialogBox("Please select a valid optional course to assign!")}else showErrorDialogBox("Please select a valid class!")}